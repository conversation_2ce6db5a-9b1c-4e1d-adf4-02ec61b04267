# 数据迁移使用指南

## 概述

本文档介绍如何使用数据迁移工具从老系统迁移商品数据到新的Laravel系统。迁移工具支持灵活的字段映射、数据验证、批量处理和错误恢复。

## 🚀 快速开始

### 1. 配置老系统数据库连接

将以下配置添加到 `.env` 文件：

```bash
# 老系统数据库连接
OLD_DB_HOST=your_old_db_host
OLD_DB_PORT=3306
OLD_DB_DATABASE=your_old_database
OLD_DB_USERNAME=your_username
OLD_DB_PASSWORD=your_password
```

### 2. 配置字段映射

编辑 `config/migration.php` 文件，根据你的老系统表结构配置字段映射：

```php
'basic_fields' => [
    'code' => 'product_code',           // 新系统字段 => 老系统字段
    'name' => 'product_name',
    'description' => 'product_desc',
    'price' => 'sell_price',
    // ... 更多字段映射
],
```

### 3. 试运行迁移

首先使用试运行模式验证配置：

```bash
php artisan migrate:products --dry-run --all
```

### 4. 执行正式迁移

确认无误后执行正式迁移：

```bash
php artisan migrate:products --all
```

## 📋 详细配置

### 字段映射配置

#### 基础字段映射
```php
'basic_fields' => [
    // 简单映射
    'code' => 'product_code',
    'name' => 'product_name',
    
    // 复杂映射（值转换）
    'status' => [
        'field' => 'is_active',
        'mapping' => [1 => 1, 0 => 0, 'Y' => 1, 'N' => 0],
    ],
],
```

#### 分类映射配置
```php
'category_mapping' => [
    'table' => 'old_categories',        // 老系统分类表名
    'id_field' => 'cat_id',            // 主键字段
    'name_field' => 'cat_name',        // 分类名称字段
    'parent_field' => 'parent_id',     // 父级分类字段
    'product_category_field' => 'category_id', // 商品中的分类字段
],
```

#### 库存映射配置
```php
'inventory_mapping' => [
    'table' => 'old_inventory',        // 库存表名（可选）
    'product_field' => 'product_id',   // 商品ID字段
    'stock_field' => 'quantity',       // 库存数量字段
    'warehouse_field' => 'warehouse_id', // 仓库字段
    'warehouse_default' => 1,          // 默认仓库ID
],
```

### 数据验证规则

```php
'validation_rules' => [
    'product' => [
        'code' => 'required|string|max:50',
        'name' => 'required|string|max:200',
        'price' => 'required|numeric|min:0',
        'status' => 'required|in:0,1',
    ],
    // ... 更多验证规则
],
```

### 数据清理规则

```php
'data_cleaning' => [
    'trim_strings' => true,            // 去除字符串首尾空格
    'empty_to_null' => true,           // 空字符串转为null
    'fix_encoding' => true,            // 修复编码问题
    'normalize_prices' => true,        // 标准化价格格式
    'generate_missing_codes' => true,  // 生成缺失的商品代码
],
```

## 🔧 命令行选项

### 基本使用

```bash
# 迁移所有数据
php artisan migrate:products --all

# 只迁移商品数据
php artisan migrate:products --products

# 迁移商品和库存
php artisan migrate:products --products --inventory

# 试运行模式
php artisan migrate:products --dry-run --all
```

### 高级选项

```bash
# 自定义批次大小
php artisan migrate:products --all --batch-size=50

# 遇到错误继续执行
php artisan migrate:products --all --continue-on-error

# 组合使用
php artisan migrate:products --products --inventory --dry-run --batch-size=200
```

### 完整命令参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--dry-run` | 试运行模式，不修改数据 | false |
| `--all` | 迁移所有类型的数据 | - |
| `--products` | 迁移商品数据 | - |
| `--categories` | 迁移分类数据 | - |
| `--inventory` | 迁移库存数据 | - |
| `--images` | 迁移图片数据 | - |
| `--batch-size` | 批处理大小 | 100 |
| `--continue-on-error` | 遇到错误时继续 | false |

## 📊 迁移流程

### 1. 预检查阶段
- 验证数据库连接
- 检查必需的表和字段
- 验证默认数据（仓库、单位等）

### 2. 数据备份（可选）
- 备份现有商品数据
- 保存到 `storage/migration_backups/`

### 3. 数据迁移
- **分类迁移**：创建分类并建立父子关系
- **商品迁移**：批量处理商品基础信息
- **库存迁移**：创建库存记录
- **图片迁移**：处理商品图片（需自定义实现）

### 4. 数据验证
- 验证数据完整性
- 检查外键关系
- 业务逻辑验证

### 5. 生成报告
- 迁移统计信息
- 错误和警告列表
- 优化建议

## 🔍 数据验证

### 独立验证命令

```bash
# 创建验证命令（需要实现）
php artisan migrate:validate
```

### 验证内容

1. **连接验证**：确保新老系统数据库连接正常
2. **计数验证**：对比迁移前后的数据数量
3. **完整性验证**：检查必需字段和外键关系
4. **一致性验证**：验证数据逻辑正确性
5. **业务验证**：检查业务规则符合性

## 📁 文件结构

```
app/Migration/
├── Services/
│   ├── ProductMigrationService.php    # 核心迁移服务
│   └── DataValidationService.php      # 数据验证服务
├── Commands/
│   └── MigrateProductData.php         # 迁移命令
config/migration.php                   # 迁移配置文件
storage/
├── migration_reports/                 # 迁移报告
├── migration_backups/                 # 数据备份
└── logs/                             # 迁移日志
```

## 🚨 注意事项

### 迁移前准备

1. **备份数据**：务必备份新系统现有数据
2. **测试环境**：先在测试环境验证迁移过程
3. **停止服务**：迁移期间建议停止相关服务
4. **检查空间**：确保磁盘空间充足

### 常见问题

#### Q: 老系统字段名不匹配怎么办？
A: 修改 `config/migration.php` 中的字段映射配置

#### Q: 迁移中断了怎么办？
A: 检查错误日志，修复问题后重新运行，系统会跳过已迁移的数据

#### Q: 如何处理特殊的数据转换？
A: 在 `ProductMigrationService` 中自定义 `cleanProductData()` 方法

#### Q: 图片迁移如何实现？
A: 需要根据具体的图片存储方式（本地/云存储）自定义 `migrateImages()` 方法

### 性能优化

1. **批次大小**：根据服务器性能调整 `--batch-size`
2. **内存限制**：大量数据迁移时适当增加PHP内存限制
3. **索引优化**：迁移前临时删除非必需索引，迁移后重建
4. **并发控制**：避免在迁移期间进行其他数据库操作

## 📈 迁移报告

每次迁移都会生成详细的报告，包含：

- **执行统计**：总时间、成功/失败数量
- **详细日志**：每个步骤的执行记录
- **错误分析**：失败原因和修复建议
- **数据对比**：迁移前后的样本数据对比
- **优化建议**：后续改进建议

报告保存位置：`storage/migration_reports/migration_YYYY-MM-DD_HH-mm-ss.json`

## 🔧 高级定制

### 自定义数据清理

```php
private function cleanProductData($data)
{
    // 自定义数据清理逻辑
    if (isset($data['description'])) {
        $data['description'] = strip_tags($data['description']);
    }
    
    return $data;
}
```

### 自定义验证规则

```php
private function validateData($type, $data)
{
    // 自定义验证逻辑
    if ($type === 'product' && $data['price'] > 100000) {
        $this->log('warning', '商品价格过高', $data);
        return false;
    }
    
    return parent::validateData($type, $data);
}
```

### 自定义字段映射

```php
private function mapCategory($oldProduct)
{
    // 自定义分类映射逻辑
    $categoryMapping = [
        '电子产品' => 1,
        '服装鞋帽' => 2,
        // ... 更多映射
    ];
    
    return $categoryMapping[$oldProduct->category_name] ?? null;
}
```

## 📞 技术支持

如遇到问题，请检查：

1. **日志文件**：`storage/logs/laravel.log`
2. **迁移报告**：`storage/migration_reports/`
3. **配置文件**：`config/migration.php`
4. **环境变量**：`.env` 文件中的数据库配置

需要技术支持时，请提供：
- 错误信息截图
- 迁移报告文件
- 老系统表结构信息
- 期望的字段映射关系 