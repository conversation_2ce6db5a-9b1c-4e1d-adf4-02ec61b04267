<?php

namespace App\Warehouse\Services;

use App\Inventory\Models\Inventory;
use App\Product\Models\Product;
use App\Warehouse\Models\Warehouse;
use App\Unit\Models\Unit;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InventoryService
{
    /**
     * 初始化或更新商品的库存
     *
     * @param int $productId 商品ID
     * @param int|null $warehouseId 仓库ID，如果为null则代表移除商品的仓库关联
     * @param float|null $initialStock 初始库存量，如果为null则不设置库存
     * @param int|null $unitId 单位ID，如果为null则使用商品的基本单位
     * @return array 操作结果
     */
    public function initializeOrUpdateStock($productId, $warehouseId, $initialStock = null, $unitId = null)
    {
        try {
            $product = Product::findOrFail($productId);
            
            // 如果没有指定单位，使用产品的基本单位
            if (!$unitId) {
                $unitId = $product->base_unit_id;
            }
            
            // 验证单位是否有效
            if ($unitId) {
                $unit = Unit::find($unitId);
                if (!$unit) {
                    return [
                        'success' => false,
                        'message' => '单位不存在',
                        'inventory' => null
                    ];
                }
            }
            
            // 如果仓库ID为null，移除商品的仓库关联
            if ($warehouseId === null) {
                Inventory::where('product_id', $productId)->delete();
                $product->updateTotalStock();
                
                return [
                    'success' => true,
                    'message' => '商品已从所有仓库中移除',
                    'inventory' => null
                ];
            }
            
            // 验证仓库是否存在
            $warehouse = Warehouse::find($warehouseId);
            if (!$warehouse) {
                return [
                    'success' => false,
                    'message' => '仓库不存在',
                    'inventory' => null
                ];
            }
            
            // 开始事务
            DB::beginTransaction();
            
            try {
                // 查找或创建库存记录
                $inventory = Inventory::firstOrCreate(
                    [
                        'warehouse_id' => $warehouseId,
                        'product_id' => $productId,
                    ],
                    [
                        'unit_id' => $product->base_unit_id,
                        'stock' => 0,
                    ]
                );
                
                // 如果有初始库存，设置库存
                if ($initialStock !== null && $initialStock > 0) {
                    $success = $inventory->setStockInUnit($initialStock, $unitId);
                    
                    if (!$success) {
                        DB::rollBack();
                        return [
                            'success' => false,
                            'message' => '设置初始库存失败，请检查单位转换关系',
                            'inventory' => null
                        ];
                    }
                    
                    // 刷新库存数据
                    $inventory->refresh();
                }
                
                // 提交事务
                DB::commit();
                
                // 刷新库存数据
                $inventory->refresh();
                
                // 构建响应数据
                $displayUnit = $product->getSaleDefaultUnit() ?: $product->baseUnit;
                $displayUnitId = $displayUnit ? $displayUnit->id : null;
                
                $responseData = [
                    'inventory' => $inventory,
                    'product' => [
                        'id' => $product->id,
                        'name' => $product->name,
                        'code' => $product->code,
                        'base_unit_id' => $product->base_unit_id,
                        'base_unit' => $product->baseUnit ? [
                            'id' => $product->baseUnit->id,
                            'name' => $product->baseUnit->name,
                            'symbol' => $product->baseUnit->symbol
                        ] : null
                    ],
                    'warehouse' => [
                        'id' => $warehouse->id,
                        'location' => $warehouse->location
                    ],
                    'stock_in_base_unit' => $inventory->stock,
                    'display_stock' => $inventory->getStockInUnit($displayUnitId),
                    'display_unit' => $displayUnit ? [
                        'id' => $displayUnit->id,
                        'name' => $displayUnit->name,
                        'symbol' => $displayUnit->symbol
                    ] : null
                ];
                
                return [
                    'success' => true,
                    'message' => $initialStock > 0 ? '商品库存已初始化' : '商品已分配到仓库',
                    'inventory' => $responseData
                ];
            } catch (\Exception $e) {
                // 回滚事务
                DB::rollBack();
                
                Log::error('初始化或更新库存失败', [
                    'product_id' => $productId,
                    'warehouse_id' => $warehouseId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                return [
                    'success' => false,
                    'message' => '操作失败: ' . $e->getMessage(),
                    'inventory' => null
                ];
            }
        } catch (\Exception $e) {
            Log::error('初始化或更新库存失败', [
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '操作失败: ' . $e->getMessage(),
                'inventory' => null
            ];
        }
    }
    
    /**
     * 检查并修复库存单位
     * 确保所有库存均使用基本单位存储
     *
     * @return array 操作结果
     */
    public function checkAndRepairInventoryUnits()
    {
        $results = [
            'success' => true,
            'total' => 0,
            'fixed' => 0,
            'errors' => 0,
            'details' => []
        ];
        
        try {
            // 获取所有库存记录
            $inventories = Inventory::with('product')->get();
            $results['total'] = $inventories->count();
            
            foreach ($inventories as $inventory) {
                try {
                    $product = $inventory->product;
                    
                    // 如果没有关联产品，跳过
                    if (!$product) {
                        $results['details'][] = [
                            'inventory_id' => $inventory->id,
                            'status' => 'error',
                            'message' => '找不到关联产品'
                        ];
                        $results['errors']++;
                        continue;
                    }
                    
                    // 检查单位是否正确
                    if ($inventory->unit_id !== $product->base_unit_id) {
                        // 保存原始值
                        $oldUnitId = $inventory->unit_id;
                        $oldStock = $inventory->stock;
                        
                        // 转换库存
                        $conversionRate = $product->getUnitConversionRate($oldUnitId, $product->base_unit_id);
                        
                        // 如果无法转换，记录错误
                        if ($conversionRate === null) {
                            $results['details'][] = [
                                'inventory_id' => $inventory->id,
                                'status' => 'error',
                                'message' => '无法获取单位转换率'
                            ];
                            $results['errors']++;
                            continue;
                        }
                        
                        // 计算基本单位下的库存量
                        $stockInBaseUnit = $oldStock * $conversionRate;
                        
                        // 更新库存
                        $success = $inventory->setStockInUnit($stockInBaseUnit, $product->base_unit_id);
                        
                        if (!$success) {
                            $results['details'][] = [
                                'inventory_id' => $inventory->id,
                                'status' => 'error',
                                'message' => '更新库存失败'
                            ];
                            $results['errors']++;
                            continue;
                        }
                        
                        $results['details'][] = [
                            'inventory_id' => $inventory->id,
                            'status' => 'fixed',
                            'message' => "单位已从 {$oldUnitId} 修改为 {$product->base_unit_id}，库存从 {$oldStock} 修改为 {$stockInBaseUnit}"
                        ];
                        $results['fixed']++;
                    }
                } catch (\Exception $e) {
                    $results['details'][] = [
                        'inventory_id' => $inventory->id,
                        'status' => 'error',
                        'message' => $e->getMessage()
                    ];
                    $results['errors']++;
                }
            }
            
            // 更新所有产品的总库存
            $products = Product::all();
            foreach ($products as $product) {
                $product->updateTotalStock();
            }
            
            return $results;
        } catch (\Exception $e) {
            Log::error('检查和修复库存单位失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '操作失败: ' . $e->getMessage(),
                'total' => 0,
                'fixed' => 0,
                'errors' => 1,
                'details' => [
                    [
                        'status' => 'error',
                        'message' => $e->getMessage()
                    ]
                ]
            ];
        }
    }
} 