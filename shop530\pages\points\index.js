// pages/points/index.js - 积分商城主页
const PointsAPI = require('../../utils/pointsApi');
const app = getApp();

Page({
  data: {
    // 用户积分信息
    userBalance: 0,
    userLevel: '',
    userStats: {},
    
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 商品列表
    products: [],
    categories: [],
    currentCategory: '',
    
    // 分页
    page: 1,
    hasMore: true,
    
    // 筛选条件
    filters: {
      category: '',
      exchange_type: '',
      sort: 'sort_order'
    },
    
    // 推荐商品
    recommendedProducts: [],
    popularProducts: [],
    
    // 签到状态
    signinStatus: {
      signed_today: false,
      consecutive_days: 0,
      total_days: 0
    },
    
    // 积分规则
    pointsRules: {},
    
    // 搜索
    searchKeyword: '',
    showSearch: false
  },

  onLoad(options) {
    this.initPage();
  },

  onShow() {
    // 每次显示页面时刷新用户积分信息
    this.loadUserInfo();
  },

  onPullDownRefresh() {
    this.refreshPage();
  },

  onReachBottom() {
    this.loadMoreProducts();
  },

  // ==================== 页面初始化 ====================

  async initPage() {
    wx.showLoading({ title: '加载中...' });
    
    try {
      // 并行加载多个数据
      await Promise.all([
        this.loadUserInfo(),
        this.loadCategories(),
        this.loadRecommendedProducts(),
        this.loadPopularProducts(),
        this.loadProducts(),
        this.loadSigninStatus(),
        this.loadPointsRules()
      ]);
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  async refreshPage() {
    this.setData({
      refreshing: true,
      page: 1,
      products: [],
      hasMore: true
    });

    try {
      await Promise.all([
        this.loadUserInfo(),
        this.loadProducts(true),
        this.loadRecommendedProducts(),
        this.loadPopularProducts(),
        this.loadSigninStatus()
      ]);
    } catch (error) {
      console.error('刷新失败:', error);
    } finally {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  // ==================== 数据加载 ====================

  async loadUserInfo() {
    try {
      const [balance, stats] = await Promise.all([
        PointsAPI.getUserBalance(),
        PointsAPI.getUserStats()
      ]);

      this.setData({
        userBalance: balance.data.balance || 0,
        userLevel: balance.data.level || 'bronze',
        userStats: stats.data || {}
      });
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  async loadCategories() {
    try {
      const result = await PointsAPI.getPointsCategories();
      this.setData({
        categories: result.data || []
      });
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  },

  async loadProducts(reset = false) {
    if (this.data.loading) return;
    if (!reset && !this.data.hasMore) return;

    this.setData({ loading: true });

    try {
      const params = {
        page: reset ? 1 : this.data.page,
        per_page: 10,
        ...this.data.filters
      };

      if (this.data.searchKeyword) {
        params.search = this.data.searchKeyword;
      }

      const result = await PointsAPI.getPointsProducts(params);
      const newProducts = result.data.data || [];
      
      this.setData({
        products: reset ? newProducts : [...this.data.products, ...newProducts],
        page: reset ? 2 : this.data.page + 1,
        hasMore: newProducts.length >= params.per_page,
        loading: false
      });
    } catch (error) {
      console.error('加载商品失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载商品失败',
        icon: 'error'
      });
    }
  },

  async loadMoreProducts() {
    await this.loadProducts();
  },

  async loadRecommendedProducts() {
    try {
      const result = await PointsAPI.getRecommendedProducts(6);
      this.setData({
        recommendedProducts: result.data || []
      });
    } catch (error) {
      console.error('加载推荐商品失败:', error);
    }
  },

  async loadPopularProducts() {
    try {
      const result = await PointsAPI.getPopularProducts(6);
      this.setData({
        popularProducts: result.data || []
      });
    } catch (error) {
      console.error('加载热门商品失败:', error);
    }
  },

  async loadSigninStatus() {
    try {
      const result = await PointsAPI.getSigninStatus();
      this.setData({
        signinStatus: result.data || {}
      });
    } catch (error) {
      console.error('加载签到状态失败:', error);
    }
  },

  async loadPointsRules() {
    try {
      const result = await PointsAPI.getPointsRules();
      this.setData({
        pointsRules: result.data || {}
      });
    } catch (error) {
      console.error('加载积分规则失败:', error);
    }
  },

  // ==================== 用户操作 ====================

  async onSignin() {
    if (this.data.signinStatus.signed_today) {
      wx.showToast({
        title: '今日已签到',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '签到中...' });

    try {
      const result = await PointsAPI.dailySignin();
      
      wx.hideLoading();
      wx.showToast({
        title: `签到成功，获得${result.data.points}积分`,
        icon: 'success'
      });

      // 更新签到状态和用户积分
      await Promise.all([
        this.loadSigninStatus(),
        this.loadUserInfo()
      ]);
    } catch (error) {
      wx.hideLoading();
      console.error('签到失败:', error);
      wx.showToast({
        title: error.message || '签到失败',
        icon: 'error'
      });
    }
  },

  // ==================== 筛选和搜索 ====================

  onCategoryChange(e) {
    const category = e.currentTarget.dataset.category || '';
    
    this.setData({
      'filters.category': category,
      currentCategory: category,
      page: 1,
      products: [],
      hasMore: true
    });

    this.loadProducts(true);
  },

  onSortChange(e) {
    const sort = e.detail.value;
    
    this.setData({
      'filters.sort': sort,
      page: 1,
      products: [],
      hasMore: true
    });

    this.loadProducts(true);
  },

  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  onSearchConfirm() {
    this.setData({
      page: 1,
      products: [],
      hasMore: true
    });
    
    this.loadProducts(true);
  },

  onSearchClear() {
    this.setData({
      searchKeyword: '',
      page: 1,
      products: [],
      hasMore: true
    });
    
    this.loadProducts(true);
  },

  toggleSearch() {
    this.setData({
      showSearch: !this.data.showSearch
    });
  },

  // ==================== 页面跳转 ====================

  goToProduct(e) {
    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/points/product/index?id=${productId}`
    });
  },

  goToOrders() {
    wx.navigateTo({
      url: '/pages/points/orders/index'
    });
  },

  goToTransactions() {
    wx.navigateTo({
      url: '/pages/points/transactions/index'
    });
  },

  goToRules() {
    wx.navigateTo({
      url: '/pages/points/rules/index'
    });
  },

  goToRanking() {
    wx.navigateTo({
      url: '/pages/points/ranking/index'
    });
  },

  // ==================== 工具方法 ====================

  formatPoints(points) {
    return PointsAPI.formatPoints(points);
  },

  formatMemberLevel(level) {
    return PointsAPI.formatMemberLevel(level);
  },

  formatProductCategory(category) {
    return PointsAPI.formatProductCategory(category);
  },

  // 检查商品是否可兑换
  async checkProductEligibility(productId, quantity = 1) {
    try {
      const result = await PointsAPI.checkExchangeEligibility(productId, quantity);
      return result.data.eligible;
    } catch (error) {
      console.error('检查兑换资格失败:', error);
      return false;
    }
  },

  // 预览兑换
  async previewExchange(items) {
    try {
      const result = await PointsAPI.previewPointsOrder(items);
      return result.data;
    } catch (error) {
      console.error('预览兑换失败:', error);
      throw error;
    }
  }
}); 