// pages/points/index.js - 积分商城主页
const PointsAPI = require('../../utils/pointsApi');
const { getLoginStatus, addLoginStateListener, removeLoginStateListener } = require('../../utils/login-state-manager');

Page({
  data: {
    // 用户积分信息（从API加载）
    userPoints: {
      availablePoints: 0,
      totalPoints: 0,
      memberLevel: 'bronze',
      memberName: '普通会员',
      nextLevelPoints: 0,
      pointsMultiple: 1.0,
      progressPercent: 0
    },
    
    // 快速入口
    quickActions: [
      { key: 'checkin', name: '每日签到', icon: 'calendar', color: '#FF9800' },
      { key: 'tasks', name: '任务中心', icon: 'bookmark', color: '#2196F3' },
      { key: 'records', name: '积分记录', icon: 'records', color: '#4CAF50' },
      { key: 'rules', name: '积分规则', icon: 'help', color: '#9C27B0' }
    ],
    
    // 商品分类（从API加载）
    categories: [],
    
    // 热门兑换商品（API加载）
    hotProducts: [],
    
    // 推荐商品（API加载）
    recommendProducts: [],
    
    // 限时特惠商品（API加载）
    limitedOffers: [],
    
    // 页面状态
    loading: false,
    currentCategory: 'all',
    
    // 登录状态
    isLoggedIn: false,
    
    // tabBar选中状态
    activeTabBar: 2
  },

  /**
   * 页面加载
   */
  onLoad() {
    this.initLoginState();
    this.loadPointsProducts();
  },

  /**
   * 页面显示
   */
  onShow() {
    // 设置tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: this.data.activeTabBar
      });
    }
    
    // 检查登录状态并刷新用户积分信息
    this.checkLoginStatusAndLoadData();
  },

  /**
   * 页面隐藏
   */
  onHide() {
    // 移除登录状态监听器
    this.removeLoginListener();
  },

  /**
   * 页面卸载
   */
  onUnload() {
    // 移除登录状态监听器
    this.removeLoginListener();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.checkLoginStatusAndLoadData();
    this.loadPointsProducts();
    wx.stopPullDownRefresh();
  },

  /**
   * 初始化登录状态
   */
  initLoginState() {
    const loginStatus = getLoginStatus();
    this.setData({
      isLoggedIn: loginStatus.isLoggedIn
    });
    
    // 加载用户积分信息
    this.loadUserPoints();
    
    // 添加登录状态监听器
    this.addLoginListener();
  },

  /**
   * 检查登录状态并加载数据
   */
  checkLoginStatusAndLoadData() {
    const loginStatus = getLoginStatus();
    const wasLoggedIn = this.data.isLoggedIn;
    const isNowLoggedIn = loginStatus.isLoggedIn;
    
    this.setData({
      isLoggedIn: isNowLoggedIn
    });
    
    // 如果登录状态发生变化，重新加载用户积分信息
    if (wasLoggedIn !== isNowLoggedIn) {
      this.loadUserPoints();
    } else if (isNowLoggedIn) {
      // 如果已登录，刷新积分信息
      this.loadUserPoints();
    }
  },

  /**
   * 添加登录状态监听器
   */
  addLoginListener() {
    addLoginStateListener('points-page', (event) => {
      console.log('🔔 积分页面收到登录状态变化:', event);
      
      const newLoginStatus = event.newState.isLoggedIn;
      const oldLoginStatus = this.data.isLoggedIn;
      
      this.setData({
        isLoggedIn: newLoginStatus
      });
      
      // 如果登录状态发生变化，重新加载用户数据
      if (oldLoginStatus !== newLoginStatus) {
        this.loadUserPoints();
      }
    });
  },

  /**
   * 移除登录状态监听器
   */
  removeLoginListener() {
    removeLoginStateListener('points-page');
  },

  /**
   * 加载用户积分信息
   */
  async loadUserPoints() {
    if (!this.data.isLoggedIn) {
      // 未登录时显示未登录状态
      const guestUserPoints = {
        availablePoints: 0,
        totalPoints: 0,
        memberLevel: 'guest',
        memberName: '未登录',
        nextLevelPoints: 0,
        pointsMultiple: 1.0,
        progressPercent: 0
      };
      this.setData({ userPoints: guestUserPoints });
      return;
    }

    try {
              // 已登录，调用API获取真实积分信息
        const result = await PointsAPI.getUserBalance();
        if (result && result.data) {
          // 根据后端返回的数据结构调整
          const balance = result.data.balance || 0;
          const membershipLevel = result.data.membership_level || '普通会员';
          
          // 模拟进度计算，后续可以根据会员等级规则完善
          let progressPercent = 0;
          if (balance > 0) {
            // 简单的进度计算：每1000积分为一个等级
            const currentLevel = Math.floor(balance / 1000);
            const levelProgress = balance % 1000;
            progressPercent = (levelProgress / 1000) * 100;
          }
          
          const userPoints = {
            availablePoints: balance,
            totalPoints: balance, // 暂时使用balance作为总积分
            memberLevel: 'member',
            memberName: membershipLevel,
            nextLevelPoints: 1000 - (balance % 1000), // 距离下一等级所需积分
            pointsMultiple: 1.0, // 默认倍率
            progressPercent: Math.round(progressPercent)
          };
          this.setData({ userPoints });
        }
    } catch (error) {
      console.error('加载用户积分失败:', error);
      // 如果API调用失败，但用户已登录，显示登录状态但积分为0
      const errorUserPoints = {
        availablePoints: 0,
        totalPoints: 0,
        memberLevel: 'bronze',
        memberName: '加载失败',
        nextLevelPoints: 0,
        pointsMultiple: 1.0,
        progressPercent: 0
      };
      this.setData({ userPoints: errorUserPoints });
    }
  },

  /**
   * 加载积分商品
   */
  async loadPointsProducts() {
    try {
      this.setData({ loading: true });
      
      const [categoriesResult, hotProductsResult, recommendedProductsResult, newProductsResult] = await Promise.allSettled([
        PointsAPI.getPointsCategories(),
        PointsAPI.getPopularProducts(8),
        PointsAPI.getRecommendedProducts(8),
        PointsAPI.getNewProducts(6)
      ]);
      
      // 处理分类数据
      if (categoriesResult.status === 'fulfilled' && categoriesResult.value?.data) {
        const apiCategories = categoriesResult.value.data;
        
        // 按sort_order排序
        apiCategories.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
        
        const categories = apiCategories.map((cat, index) => ({
          key: cat.key, // 现在可能是数字ID或字符串
          name: cat.name,
          displayName: cat.count > 0 ? `${cat.name}(${cat.count})` : cat.name,
          active: index === 0, // 第一个分类默认选中
          count: cat.count,
          sort_order: cat.sort_order
        }));
        
        this.setData({ 
          categories: categories,
          currentCategory: categories.length > 0 ? categories[0].key : 'all'
        });
      } else {
        // API获取分类失败时的默认分类
        console.warn('获取分类数据失败，使用默认分类');
        const defaultCategories = [
          { key: 'all', name: '全部', displayName: '全部', active: true, count: 0, sort_order: 0 }
        ];
        this.setData({ 
          categories: defaultCategories,
          currentCategory: 'all'
        });
      }
      
      // 处理热销商品
      if (hotProductsResult.status === 'fulfilled' && hotProductsResult.value?.data) {
        const hotProducts = hotProductsResult.value.data.map(product => ({
          id: product.id,
          name: product.name,
          image: this.getSafeImageUrl(product.image),
          pointsPrice: product.points_price,
          originalPrice: product.original_price,
          description: product.description,
          category: product.category,
          type: product.category,
          memberLevel: product.membership_level_id ? 'gold' : '',
          tags: ['热销']
        }));
        
        this.setData({ hotProducts });
      }
      
      // 处理推荐商品
      if (recommendedProductsResult.status === 'fulfilled' && recommendedProductsResult.value?.data) {        
        const recommendProducts = recommendedProductsResult.value.data.map(product => ({
          id: product.id,
          name: product.name,
          image: this.getSafeImageUrl(product.image),
          pointsPrice: product.points_price,
          originalPrice: product.original_price,
          description: product.description,
          category: product.category,
          type: product.category,
          memberLevel: product.membership_level_id ? 'gold' : '',
          tags: ['推荐']
        }));
        
        this.setData({ recommendProducts });
      }
      
      // 处理限时特惠商品（使用新品数据）
      if (newProductsResult.status === 'fulfilled' && newProductsResult.value?.data) {
        const limitedOffers = newProductsResult.value.data.map(product => ({
          id: product.id,
          name: product.name,
          image: this.getSafeImageUrl(product.image),
          pointsPrice: product.points_price,
          originalPointsPrice: product.original_price ? Math.floor(product.original_price * 10) : null,
          originalPrice: product.original_price,
          description: product.description,
          category: product.category,
          type: product.category,
          memberLevel: product.membership_level_id ? 'gold' : '',
          tags: ['限时', '新品'],
          endTime: product.end_time,
          timeRemaining: product.end_time ? this.calculateTimeRemaining(product.end_time) : null
        }));
        
        this.setData({ limitedOffers });
      }
      
    } catch (error) {
      console.error('加载积分商品失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 积分卡片点击
   */
  onPointsCardTap() {
    if (!this.data.isLoggedIn) {
      // 未登录时跳转到登录页
      wx.navigateTo({
        url: '/pages/login/index'
      }).catch(() => {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
      });
    }
  },

  /**
   * 快速操作点击
   */
  onQuickActionTap(e) {
    const { action } = e.currentTarget.dataset;
    
    // 检查是否需要登录
    const requireLoginActions = ['checkin', 'records'];
    if (requireLoginActions.includes(action) && !this.data.isLoggedIn) {
      wx.showModal({
        title: '需要登录',
        content: '此功能需要先登录，是否前往登录？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/index'
            });
          }
        }
      });
      return;
    }
    
    switch (action) {
      case 'checkin':
        wx.navigateTo({
          url: '/pages/check-in/index'
        });
        break;
      case 'tasks':
        wx.showToast({
          title: '任务中心暂未开放',
          icon: 'none'
        });
        break;
      case 'records':
        wx.navigateTo({
          url: '/pages/points-record/index'
        });
        break;
      case 'rules':
        this.showPointsRules();
        break;
    }
  },

  /**
   * 显示积分规则
   */
  async showPointsRules() {
    try {
      // 尝试从API获取积分规则
      const result = await PointsAPI.getPointsRules();
      let content = '';
      
      if (result && result.data && Array.isArray(result.data)) {
        content = result.data.map(rule => `${rule.name}：${rule.description}`).join('\n');
      } else {
        // API失败时的默认规则
        content = '购物返积分：每消费1元=1积分\n首次下单：奖励100积分\n每日签到：1-20积分递增\n邀请好友：每人50积分\n完成评价：每次10积分';
      }
      
      wx.showModal({
        title: '积分规则',
        content: content,
        showCancel: false,
        confirmText: '我知道了'
      });
    } catch (error) {
      console.error('获取积分规则失败:', error);
      // 显示默认规则
      wx.showModal({
        title: '积分规则',
        content: '购物返积分：每消费1元=1积分\n首次下单：奖励100积分\n每日签到：1-20积分递增\n邀请好友：每人50积分\n完成评价：每次10积分',
        showCancel: false,
        confirmText: '我知道了'
      });
    }
  },

  /**
   * 分类切换
   */
  onCategoryTap(e) {
    const { category } = e.currentTarget.dataset;
    
    if (category === this.data.currentCategory) return;
    
    // 更新分类状态
    const categories = this.data.categories.map(item => ({
      ...item,
      active: item.key === category
    }));
    
    this.setData({
      categories: categories,
      currentCategory: category
    });
    
    // 加载对应分类的商品
    this.loadProductsByCategory(category);
  },

  /**
   * 根据分类加载商品
   */
  async loadProductsByCategory(category) {
    try {
      this.setData({ loading: true });
      
      // 构建查询参数
      const params = {
        per_page: 20,
        page: 1
      };
      
      // 分类筛选：如果不是全部分类，添加分类筛选
      if (category !== 'all') {
        params.category = category;
      }
      
      // 调用API获取商品
      const result = await PointsAPI.getPointsProducts(params);
      
      if (result && result.data && result.data.data) {
        const products = result.data.data.map(product => ({
          id: product.id,
          name: product.name,
          image: this.getSafeImageUrl(product.image),
          pointsPrice: product.points_price,
          originalPrice: product.original_price,
          description: product.description,
          category: product.category,
          type: product.category,
          memberLevel: product.membership_level_id ? 'gold' : '',
          tags: this.getProductTags(product),
          stock: product.stock_quantity,
          soldCount: product.exchanged_count
        }));
        
        // 更新推荐商品列表（用于显示分类商品）
        this.setData({ recommendProducts: products });
      } else {
        // 没有商品时显示空列表
        this.setData({ recommendProducts: [] });
      }
      
    } catch (error) {
      console.error('加载分类商品失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 获取商品标签
   */
  getProductTags(product) {
    const tags = [];
    
    if (product.is_hot) tags.push('热销');
    if (product.is_new) tags.push('新品');
    if (product.is_featured) tags.push('推荐');
    if (product.exchange_type === 'mixed_payment') tags.push('积分+现金');
    
    return tags.length > 0 ? tags : ['兑换'];
  },

  /**
   * 获取安全的图片URL（过滤外部链接）
   */
  getSafeImageUrl(imageUrl) {
    // 如果没有图片URL，返回默认占位图
    if (!imageUrl) {
      return '/static/images/placeholder-product.png';
    }
    
    // 过滤掉外部链接（如unsplash.com等）
    if (imageUrl.startsWith('http') && !imageUrl.includes(wx.getStorageSync('baseUrl'))) {
      return '/static/images/placeholder-product.png';
    }
    
    // 返回安全的图片URL
    return imageUrl;
  },

  /**
   * 计算剩余时间
   * @param {string} endTime - 结束时间
   * @returns {string} 格式化的剩余时间
   */
  calculateTimeRemaining(endTime) {
    if (!endTime) return null;
    
    const now = new Date();
    const end = new Date(endTime);
    const diff = end.getTime() - now.getTime();
    
    if (diff <= 0) {
      return '已结束';
    }
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) {
      return `${days}天${hours}小时`;
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  },

  /**
   * 商品点击
   */
  onProductTap(e) {
    const { product } = e.currentTarget.dataset;
    
    // 商品详情页面对所有用户开放，无需登录检查
    wx.navigateTo({
      url: `/pages/points-detail/index?productId=${product.id}`
    }).catch(() => {
      wx.showToast({
        title: '商品详情页面跳转失败',
        icon: 'none'
      });
    });
  },

  /**
   * 立即兑换
   */
  onExchangeNow(e) {
    // 安全地阻止事件冒泡
    if (e && typeof e.stopPropagation === 'function') {
      e.stopPropagation();
    }
    
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      wx.showModal({
        title: '需要登录',
        content: '兑换商品需要先登录，是否前往登录？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/index'
            });
          }
        }
      });
      return;
    }
    
    const { product } = e.currentTarget.dataset;
    
    // 检查积分是否足够
    if (this.data.userPoints.availablePoints < product.pointsPrice) {
      wx.showToast({
        title: '积分不足',
        icon: 'none'
      });
      return;
    }
    
    // 检查会员等级
    if (product.memberLevel && !this.checkMemberLevel(product.memberLevel)) {
      wx.showToast({
        title: `需要${this.getMemberLevelName(product.memberLevel)}才能兑换`,
        icon: 'none'
      });
      return;
    }
    
    // 跳转到兑换页面
    wx.navigateTo({
      url: `/pages/points-exchange/index?productId=${product.id}`
    });
  },

  /**
   * 检查会员等级
   */
  checkMemberLevel(requiredLevel) {
    const levelOrder = ['bronze', 'silver', 'gold', 'diamond'];
    const userLevelIndex = levelOrder.indexOf(this.data.userPoints.memberLevel);
    const requiredLevelIndex = levelOrder.indexOf(requiredLevel);
    
    return userLevelIndex >= requiredLevelIndex;
  },

  /**
   * 获取会员等级名称
   */
  getMemberLevelName(level) {
    const levelNames = {
      bronze: '青铜会员',
      silver: '白银会员',
      gold: '黄金会员',
      diamond: '钻石会员'
    };
    return levelNames[level] || '普通会员';
  },

  /**
   * 查看更多
   */
  onViewMore(e) {
    const { type } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/points-list/index?category=${type}`
    }).catch(() => {
      wx.showToast({
        title: '页面暂未开发',
        icon: 'none'
      });
    });
  }
}); 