<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reorder_records', function (Blueprint $table) {
            $table->id();
            
            // 关联信息
            $table->foreignId('reorder_rule_id')->constrained('auto_reorder_rules')->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('warehouse_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('supplier_id')->nullable()->constrained()->onDelete('set null');
            
            // 补货记录基本信息
            $table->string('reorder_no')->unique()->comment('补货单号');
            $table->enum('status', [
                'pending',      // 待处理
                'approved',     // 已批准
                'rejected',     // 已拒绝
                'ordered',      // 已下单
                'received',     // 已收货
                'completed',    // 已完成
                'cancelled'     // 已取消
            ])->default('pending')->comment('补货状态');
            
            // 触发信息
            $table->enum('trigger_reason', [
                'low_stock',
                'out_of_stock',
                'sales_velocity',
                'scheduled',
                'manual'
            ])->comment('触发原因');
            
            $table->json('trigger_data')->nullable()->comment('触发时的数据快照');
            
            // 库存信息
            $table->decimal('stock_before', 10, 2)->comment('补货前库存');
            $table->decimal('reorder_point', 10, 2)->comment('补货点');
            $table->decimal('requested_quantity', 10, 2)->comment('请求补货数量');
            $table->decimal('approved_quantity', 10, 2)->nullable()->comment('批准补货数量');
            $table->decimal('actual_quantity', 10, 2)->nullable()->comment('实际补货数量');
            
            // 成本信息
            $table->decimal('unit_cost', 10, 2)->nullable()->comment('单位成本');
            $table->decimal('total_cost', 10, 2)->nullable()->comment('总成本');
            $table->decimal('estimated_cost', 10, 2)->nullable()->comment('预估成本');
            
            // 时间信息
            $table->timestamp('triggered_at')->comment('触发时间');
            $table->timestamp('approved_at')->nullable()->comment('批准时间');
            $table->timestamp('ordered_at')->nullable()->comment('下单时间');
            $table->timestamp('expected_delivery_at')->nullable()->comment('预期到货时间');
            $table->timestamp('received_at')->nullable()->comment('实际收货时间');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            
            // 处理人员
            $table->foreignId('triggered_by')->nullable()->constrained('employees')->comment('触发人');
            $table->foreignId('approved_by')->nullable()->constrained('employees')->comment('批准人');
            $table->foreignId('ordered_by')->nullable()->constrained('employees')->comment('下单人');
            $table->foreignId('received_by')->nullable()->constrained('employees')->comment('收货人');
            
            // 关联订单
            $table->string('purchase_order_no')->nullable()->comment('采购订单号');
            $table->foreignId('purchase_order_id')->nullable()->comment('采购订单ID');
            
            // 备注和附加信息
            $table->text('notes')->nullable()->comment('备注');
            $table->text('rejection_reason')->nullable()->comment('拒绝原因');
            $table->json('additional_data')->nullable()->comment('附加数据');
            
            // 性能指标
            $table->integer('lead_time_actual')->nullable()->comment('实际采购周期（天）');
            $table->decimal('cost_variance', 10, 2)->nullable()->comment('成本差异');
            $table->boolean('on_time_delivery')->nullable()->comment('是否按时交货');
            
            $table->timestamps();
            
            // 索引
            $table->index(['reorder_rule_id', 'status']);
            $table->index(['product_id', 'warehouse_id']);
            $table->index(['status', 'triggered_at']);
            $table->index(['supplier_id', 'status']);
            $table->index('purchase_order_no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reorder_records');
    }
}; 