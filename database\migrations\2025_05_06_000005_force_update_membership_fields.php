<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 检查并修复会员等级表的字段
        if (Schema::hasColumn('membership_levels', 'points_required') && 
            !Schema::hasColumn('membership_levels', 'upgrade_points')) {
            
            // 添加新字段
            Schema::table('membership_levels', function (Blueprint $table) {
                $table->integer('upgrade_points')->default(0)
                    ->comment('升级所需积分');
                $table->decimal('upgrade_amount', 10, 2)->default(0)
                    ->comment('升级所需累计消费金额');
                $table->decimal('quick_upgrade_amount', 10, 2)->default(0)
                    ->comment('快速升级所需单笔订单金额');
            });
            
            // 复制数据
            DB::statement('UPDATE membership_levels SET 
                upgrade_points = points_required,
                upgrade_amount = total_consumption_required,
                quick_upgrade_amount = single_order_required');
                
            // 删除旧字段
            Schema::table('membership_levels', function (Blueprint $table) {
                $table->dropColumn(['points_required', 'total_consumption_required', 'single_order_required']);
            });
        }
        
        // 检查并修复用户表的字段
        if (Schema::hasColumn('users', 'points') && 
            !Schema::hasColumn('users', 'member_points')) {
            
            // 添加新字段
            Schema::table('users', function (Blueprint $table) {
                $table->integer('member_points')->default(0)
                    ->comment('会员积分');
                $table->decimal('total_spend', 10, 2)->default(0)
                    ->comment('累计消费金额');
                $table->decimal('largest_order', 10, 2)->default(0)
                    ->comment('最大单笔订单金额');
            });
            
            // 复制数据
            DB::statement('UPDATE users SET 
                member_points = points,
                total_spend = total_consumption,
                largest_order = max_single_order');
                
            // 删除旧字段
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn(['points', 'total_consumption', 'max_single_order']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 检查并恢复会员等级表的字段
        if (Schema::hasColumn('membership_levels', 'upgrade_points') && 
            !Schema::hasColumn('membership_levels', 'points_required')) {
            
            // 添加旧字段
            Schema::table('membership_levels', function (Blueprint $table) {
                $table->integer('points_required')->default(0)
                    ->comment('达到此等级所需的积分');
                $table->decimal('total_consumption_required', 10, 2)->default(0)
                    ->comment('升级所需的累计消费金额');
                $table->decimal('single_order_required', 10, 2)->default(0)
                    ->comment('单笔订单金额达到此值可直接升级');
            });
            
            // 复制数据
            DB::statement('UPDATE membership_levels SET 
                points_required = upgrade_points,
                total_consumption_required = upgrade_amount,
                single_order_required = quick_upgrade_amount');
                
            // 删除新字段
            Schema::table('membership_levels', function (Blueprint $table) {
                $table->dropColumn(['upgrade_points', 'upgrade_amount', 'quick_upgrade_amount']);
            });
        }
        
        // 检查并恢复用户表的字段
        if (Schema::hasColumn('users', 'member_points') && 
            !Schema::hasColumn('users', 'points')) {
            
            // 添加旧字段
            Schema::table('users', function (Blueprint $table) {
                $table->integer('points')->default(0)
                    ->comment('用户积分');
                $table->decimal('total_consumption', 10, 2)->default(0)
                    ->comment('用户累计消费金额');
                $table->decimal('max_single_order', 10, 2)->default(0)
                    ->comment('用户历史最大单笔订单金额');
            });
            
            // 复制数据
            DB::statement('UPDATE users SET 
                points = member_points,
                total_consumption = total_spend,
                max_single_order = largest_order');
                
            // 删除新字段
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn(['member_points', 'total_spend', 'largest_order']);
            });
        }
    }
}; 