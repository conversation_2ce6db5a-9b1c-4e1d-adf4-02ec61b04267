<?php

namespace App\WechatPayment\Http\Controllers;

use App\Http\Controllers\Controller;
use App\WechatPayment\Models\WechatServiceProvider;
use App\WechatPayment\Models\WechatSubMerchant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class WechatServiceProviderController extends Controller
{
    /**
     * 显示服务商列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = WechatServiceProvider::query();
        
        // 搜索条件
        if ($request->has('name')) {
            $query->where('name', 'like', '%' . $request->input('name') . '%');
        }
        
        if ($request->has('mch_id')) {
            $query->where('mch_id', 'like', '%' . $request->input('mch_id') . '%');
        }
        
        if ($request->has('is_active') && $request->input('is_active') !== '') {
            $query->where('is_active', $request->input('is_active'));
        }
        
        $providers = $query->orderBy('created_at', 'desc')->paginate(10);
        
        return response()->json([
            'data' => $providers->items(),
            'total' => $providers->total(),
            'current_page' => $providers->currentPage(),
            'last_page' => $providers->lastPage(),
        ]);
    }

    /**
     * 存储新创建的服务商
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|max:100',
            'mch_id' => 'required|max:50',
            'appid' => 'required|max:50',
            'api_key' => 'required',
            'notify_url' => 'nullable|url',
            'refund_notify_url' => 'nullable|url',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => $validator->errors()->first(),
            ], 422);
        }
        
        $data = $request->all();
        
        // 处理文件上传
        if ($request->hasFile('cert_path')) {
            $certPath = $request->file('cert_path')->store('wechat/cert', 'local');
            $data['cert_path'] = $certPath;
        }
        
        if ($request->hasFile('key_path')) {
            $keyPath = $request->file('key_path')->store('wechat/cert', 'local');
            $data['key_path'] = $keyPath;
        }
        
        $provider = WechatServiceProvider::create($data);
        
        return response()->json([
            'code' => 0,
            'message' => '创建成功',
            'data' => $provider,
        ]);
    }

    /**
     * 显示指定服务商
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $provider = WechatServiceProvider::findOrFail($id);
        
        return response()->json([
            'code' => 0,
            'data' => $provider,
        ]);
    }

    /**
     * 更新指定服务商
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|max:100',
            'mch_id' => 'required|max:50',
            'appid' => 'required|max:50',
            'notify_url' => 'nullable|url',
            'refund_notify_url' => 'nullable|url',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => $validator->errors()->first(),
            ], 422);
        }
        
        $provider = WechatServiceProvider::findOrFail($id);
        $data = $request->all();
        
        // 如果没有提供新密钥，使用旧密钥
        if (empty($data['api_key'])) {
            unset($data['api_key']);
        }
        
        if (empty($data['api_v3_key'])) {
            unset($data['api_v3_key']);
        }
        
        // 处理文件上传
        if ($request->hasFile('cert_path')) {
            // 删除旧文件
            if ($provider->cert_path) {
                Storage::disk('local')->delete($provider->cert_path);
            }
            
            $certPath = $request->file('cert_path')->store('wechat/cert', 'local');
            $data['cert_path'] = $certPath;
        }
        
        if ($request->hasFile('key_path')) {
            // 删除旧文件
            if ($provider->key_path) {
                Storage::disk('local')->delete($provider->key_path);
            }
            
            $keyPath = $request->file('key_path')->store('wechat/cert', 'local');
            $data['key_path'] = $keyPath;
        }
        
        $provider->update($data);
        
        return response()->json([
            'code' => 0,
            'message' => '更新成功',
            'data' => $provider,
        ]);
    }

    /**
     * 删除指定服务商
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $provider = WechatServiceProvider::findOrFail($id);
        
        // 检查是否有关联的子商户
        $subMerchantCount = WechatSubMerchant::where('provider_id', $id)->count();
        
        if ($subMerchantCount > 0) {
            return response()->json([
                'code' => 400,
                'message' => '该服务商有' . $subMerchantCount . '个关联的子商户，无法删除',
            ], 400);
        }
        
        // 删除关联文件
        if ($provider->cert_path) {
            Storage::disk('local')->delete($provider->cert_path);
        }
        
        if ($provider->key_path) {
            Storage::disk('local')->delete($provider->key_path);
        }
        
        $provider->delete();
        
        return response()->json([
            'code' => 0,
            'message' => '删除成功',
        ]);
    }

    /**
     * 获取微信支付服务商选项列表，用于下拉框
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function options()
    {
        $providers = WechatServiceProvider::where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'mch_id']);
            
        return response()->json([
            'code' => 0,
            'data' => $providers,
        ]);
    }
} 