<!--pages/points/ranking/index.wxml - 积分排行榜页面模板-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="header-title">积分排行榜</view>
    <view class="header-subtitle">看看谁是积分达人</view>
  </view>

  <!-- 排行榜类型切换 -->
  <view class="type-tabs">
    <view 
      wx:for="{{rankingTypes}}" 
      wx:key="key"
      class="type-tab {{currentType === item.key ? 'active' : ''}}"
      bindtap="onTypeChange"
      data-type="{{item.key}}"
    >
      <text class="tab-icon">{{item.icon}}</text>
      <text class="tab-name">{{item.name}}</text>
    </view>
  </view>

  <!-- 我的排名 -->
  <view wx:if="{{myRanking.rank}}" class="my-ranking">
    <view class="my-rank-header">
      <text class="my-rank-title">我的排名</text>
      <button class="share-btn" open-type="share" bindtap="onShareRanking">
        <text class="share-icon">分享</text>
        <text>分享</text>
      </button>
    </view>
    <view class="my-rank-content">
      <view class="rank-info">
        <text class="rank-number">第{{myRanking.rank}}名</text>
        <text class="rank-points">{{formatPoints(myRanking.points)}}积分</text>
      </view>
      <view class="rank-avatar">
        <image class="avatar" src="{{myRanking.avatar || '/images/default-avatar.png'}}" />
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading && rankingList.length === 0}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 排行榜列表 -->
  <view wx:else class="ranking-list">
    <!-- 前三名特殊展示 -->
    <view wx:if="{{rankingList.length > 0}}" class="top-three">
      <view 
        wx:for="{{rankingList.slice(0, 3)}}" 
        wx:key="user_id"
        class="top-item rank-{{item.rank}}"
        bindtap="onUserTap"
        data-user-id="{{item.user_id}}"
      >
        <view class="top-rank">{{getRankIcon(item.rank)}}</view>
        <view class="top-avatar">
          <image class="avatar" src="{{item.avatar || '/images/default-avatar.png'}}" />
        </view>
        <view class="top-info">
          <view class="top-name">{{item.nickname || '匿名用户'}}</view>
          <view class="top-points">{{formatPoints(item.points)}}</view>
        </view>
      </view>
    </view>

    <!-- 其他排名 -->
    <view wx:if="{{rankingList.length > 3}}" class="other-ranks">
      <view class="rank-header">
        <text class="rank-title">其他排名</text>
      </view>
      
      <view 
        wx:for="{{rankingList.slice(3)}}" 
        wx:key="user_id"
        class="rank-item"
        bindtap="onUserTap"
        data-user-id="{{item.user_id}}"
      >
        <view class="rank-number">{{item.rank}}</view>
        <view class="rank-avatar">
          <image class="avatar" src="{{item.avatar || '/images/default-avatar.png'}}" />
        </view>
        <view class="rank-info">
          <view class="rank-name">{{item.nickname || '匿名用户'}}</view>
          <view class="rank-level">{{item.level_name || 'VIP会员'}}</view>
        </view>
        <view class="rank-points">{{formatPoints(item.points)}}</view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{rankingList.length === 0}}" class="empty-state">
      <view class="empty-icon">奖杯</view>
      <view class="empty-text">暂无排行榜数据</view>
      <view class="empty-tip">快去赚取积分吧！</view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{hasMore && rankingList.length > 0}}" class="load-more">
      <view wx:if="{{loading}}" class="loading-text">加载中...</view>
      <view wx:else class="load-more-text">上拉加载更多</view>
    </view>

    <!-- 没有更多 -->
    <view wx:if="{{!hasMore && rankingList.length > 0}}" class="no-more">
      <view class="no-more-text">没有更多数据了</view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <button class="action-btn primary" bindtap="goToPointsMall">
      <text class="btn-icon">商城</text>
      <text>积分商城</text>
    </button>
    <button class="action-btn secondary" bindtap="goToPointsDetail">
      <text class="btn-icon">明细</text>
      <text>积分明细</text>
    </button>
  </view>
</view> 