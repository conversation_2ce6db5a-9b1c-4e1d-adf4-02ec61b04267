<?php

namespace App\Delivery\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Employee\Models\Employee;
use App\Delivery\Models\Delivery;

class DeliveryRoute extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'coverage_area',
        'starting_point',
        'estimated_delivery_time',
        'max_orders',
        'description',
        'status',
    ];

    /**
     * 获取该路线的配送员
     */
    public function deliverers()
    {
        return $this->belongsToMany(Employee::class, 'delivery_route_employee', 'route_id', 'employee_id')
            ->withTimestamps();
    }
    
    /**
     * 获取该路线的配送任务
     */
    public function deliveries()
    {
        return $this->hasMany(Delivery::class, 'route_id');
    }
} 