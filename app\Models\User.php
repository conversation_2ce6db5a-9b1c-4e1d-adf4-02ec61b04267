<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Models\Delivery;
use App\Models\UserAddress;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'password',
        'membership_level_id',
        'default_employee_deliverer_id',
        'crm_agent_id',
        'openid',
        'unionid',
        'phone',
        'avatar',
        'nickname',
        'gender',
        'province',
        'city',
        'country',
        'district',  // 区/县字段
        'merchant_name',
        'balance',
        'member_points',
        'total_spend',
        'largest_order',
        'joined_at',
        'level_upgraded_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'openid',
        'unionid',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'password' => 'hashed',
        'gender' => 'integer',
        'balance' => 'decimal:2',
        'member_points' => 'integer',
        'total_spend' => 'decimal:2',
        'largest_order' => 'decimal:2',
        'joined_at' => 'datetime',
        'level_upgraded_at' => 'datetime',
    ];
    
    /**
     * 应该附加到模型数组的属性
     *
     * @var array
     */
    protected $appends = ['role'];
    
    /**
     * 获取用户角色（向后兼容）
     * 根据员工表的角色返回，如果没有关联员工则返回普通用户角色
     *
     * @return string
     */
    public function getRoleAttribute()
    {
        // 如果有关联的员工记录
        if ($this->employee) {
            // 根据员工角色返回用户角色
            switch ($this->employee->role) {
                case 'admin':
                    return 'admin';
                case 'manager':
                    return 'admin'; // 管理者也视为管理员权限
                case 'crm_agent':
                    return 'merchant'; // CRM专员视为商户权限级别
                case 'delivery':
                    return 'merchant'; // 配送员视为商户权限级别
                default:
                    return 'merchant';
            }
        }
        
        // 没有关联员工的情况下为普通用户
        return 'customer';
    }
    
    /**
     * 判断用户是否为管理员
     *
     * @return bool
     */
    public function isAdmin()
    {
        return $this->role === 'admin';
    }
    
    /**
     * 判断用户是否为商户级别
     *
     * @return bool
     */
    public function isMerchant()
    {
        return $this->role === 'merchant';
    }
    
    /**
     * 判断用户是否为普通用户
     *
     * @return bool
     */
    public function isCustomer()
    {
        return $this->role === 'customer';
    }
    
    /**
     * 获取用户关联的员工记录
     */
    public function employee()
    {
        return $this->hasOne(\App\Employee\Models\Employee::class);
    }
    
    /**
     * 获取用户的默认员工配送员
     */
    public function defaultEmployeeDeliverer()
    {
        return $this->belongsTo(\App\Employee\Models\Employee::class, 'default_employee_deliverer_id');
    }
    
    /**
     * 获取用户的配送员信息
     * 此关系仅用于员工配送员模式
     */
    public function deliverer()
    {
        return $this->hasOneThrough(
            \App\Delivery\Models\Deliverer::class,
            \App\Employee\Models\Employee::class,
            'user_id', // Employee表的外键
            'employee_id', // Deliverer表的外键
            'id', // User表的主键
            'id' // Employee表的主键
        )->where('deliverers.type', 'employee');
    }
    
    /**
     * 获取用户的CRM专员
     */
    public function crmAgent()
    {
        return $this->belongsTo(\App\Employee\Models\Employee::class, 'crm_agent_id');
    }
    
    /**
     * 获取用户的跟进记录
     */
    public function followUps()
    {
        return $this->hasMany(\App\Crm\Models\ClientFollowUp::class, 'user_id');
    }
    
    /**
     * 检查是否是配送员
     * 检查员工配送员身份
     */
    public function isDeliverer()
    {
        // 检查是否是员工配送员
        return $this->deliverer()->exists();
    }
    
    /**
     * 检查是否是CRM专员
     * 
     * @return bool
     */
    public function isCrmAgent()
    {
        // 修改为基于员工角色判断
        return $this->employee && $this->employee->role === 'crm_agent';
    }
    
    /**
     * 获取用户的默认配送员信息
     * 
     * @return Employee|null
     */
    public function getDefaultDeliverer()
    {
        // 使用默认员工配送员
        $employee = $this->defaultEmployeeDeliverer;
        if ($this->default_employee_deliverer_id && $employee) {
            return $employee;
        }
        
        return null;
    }
    
    /**
     * 全面检查用户是否为配送员
     * 
     * @return bool
     */
    public function isAnyTypeOfDeliverer()
    {
        return $this->isDeliverer();
    }
    
    /**
     * 获取此用户作为配送员可以接受的订单
     * 
     * @return \Illuminate\Database\Eloquent\Relations\HasMany|null
     */
    public function getAssignedDeliveries()
    {
        // 员工配送员
        $deliverer = $this->deliverer;
        if ($deliverer) {
            return $deliverer->deliveries();
        }
        
        return null;
    }
    
    /**
     * 获取用户的会员等级
     */
    public function membershipLevel()
    {
        return $this->belongsTo(\App\Crm\Models\MembershipLevel::class);
    }
    
    /**
     * 获取用户的会员等级名称
     * 
     * @return string
     */
    public function getLevelName()
    {
        return $this->membershipLevel ? $this->membershipLevel->name : '普通会员';
    }
    
    /**
     * 获取用户的固定金额减免
     * 
     * @return float
     */
    public function getDiscountAmount()
    {
        return $this->membershipLevel ? $this->membershipLevel->discount_rate : 0.00;
    }
    
    /**
     * 获取用户的折扣率（保持向后兼容）
     * 
     * @deprecated 请使用 getDiscountAmount() 方法
     * @return float
     */
    public function getDiscountRate()
    {
        return $this->getDiscountAmount();
    }
    
    /**
     * 更新用户会员等级
     * 根据用户积分、累计消费金额、单笔订单最高金额自动计算应该处于的会员等级
     * 
     * @return bool 是否升级
     */
    public function updateMembershipLevel()
    {
        $currentLevelId = $this->membership_level_id;
        
        // 根据积分、累计消费和单笔订单最高金额综合判断应该的会员等级
        $level = \App\Crm\Models\MembershipLevel::getAppropriateLevel(
            $this->member_points, 
            $this->total_spend, 
            $this->largest_order
        );
        
        if ($level && ($level->id != $currentLevelId)) {
            $this->membership_level_id = $level->id;
            $this->level_upgraded_at = now();
            $this->save();
            
            // 触发会员升级事件，可以在这里添加通知等功能
            // event(new UserLevelUpgraded($this, $level));
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 添加积分并更新会员等级
     * 
     * @param int $points 要添加的积分
     * @param string $remark 备注
     * @return bool 操作是否成功
     */
    public function addPoints($points, $remark = null)
    {
        if ($points <= 0) {
            return false;
        }
        
        $this->member_points += $points;
        $this->save();
        
        // 记录积分变动
        // PointsTransaction::create([
        //     'user_id' => $this->id,
        //     'points' => $points,
        //     'type' => 'add',
        //     'remark' => $remark
        // ]);
        
        // 更新会员等级
        $this->updateMembershipLevel();
        
        return true;
    }
    
    /**
     * 扣减积分
     * 
     * @param int $points 要扣减的积分
     * @param string $remark 备注
     * @return bool 操作是否成功
     */
    public function deductPoints($points, $remark = null)
    {
        if ($points <= 0 || $this->member_points < $points) {
            return false;
        }
        
        $this->member_points -= $points;
        $this->save();
        
        // 记录积分变动
        // PointsTransaction::create([
        //     'user_id' => $this->id,
        //     'points' => -$points,
        //     'type' => 'deduct',
        //     'remark' => $remark
        // ]);
        
        // 更新会员等级 (降级)
        $this->updateMembershipLevel();
        
        return true;
    }
    
    /**
     * 添加消费记录并更新会员等级
     * 
     * @param float $amount 消费金额
     * @param bool $updateLevel 是否立即更新会员等级
     * @return bool 操作是否成功
     */
    public function addConsumption($amount, $updateLevel = true)
    {
        if ($amount <= 0) {
            return false;
        }
        
        // 更新累计消费金额
        $this->total_spend += $amount;
        
        // 更新单笔订单最高金额
        if ($amount > $this->largest_order) {
            $this->largest_order = $amount;
        }
        
        $this->save();
        
        // 判断是否需要立即更新会员等级
        if ($updateLevel) {
            $this->updateMembershipLevel();
        }
        
        return true;
    }
    
    /**
     * 检查是否可以通过单笔订单直接升级
     * 
     * @param float $orderAmount 订单金额
     * @return MembershipLevel|null 如果可以升级，返回升级后的等级；否则返回null
     */
    public function checkDirectUpgradeByOrder($orderAmount)
    {
        // 获取当前会员等级
        $currentLevel = $this->membershipLevel;
        if (!$currentLevel) {
            return null;
        }
        
        // 检查是否有单笔订单可以直接升级到更高等级
        $upgradeLevel = \App\Crm\Models\MembershipLevel::where('status', true)
            ->where('quick_upgrade_amount', '>', 0) // 必须设置了单笔订单要求
            ->where('quick_upgrade_amount', '<=', $orderAmount) // 满足金额要求
            ->where('sort_order', '>', $currentLevel->sort_order) // 等级比当前高
            ->orderByDesc('sort_order') // 取符合条件的最高等级
            ->first();
            
        return $upgradeLevel;
    }
    
    /**
     * 直接通过订单金额升级会员等级
     * 
     * @param float $orderAmount 订单金额
     * @return bool 是否升级成功
     */
    public function directUpgradeByOrder($orderAmount)
    {
        $upgradeLevel = $this->checkDirectUpgradeByOrder($orderAmount);
        
        if ($upgradeLevel) {
            $this->membership_level_id = $upgradeLevel->id;
            $this->level_upgraded_at = now();
            
            // 同时更新最大单笔订单金额
            if ($orderAmount > $this->largest_order) {
                $this->largest_order = $orderAmount;
            }
            
            $this->save();
            
            // 触发会员升级事件
            // event(new UserLevelUpgraded($this, $upgradeLevel));
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取用户的员工角色
     * 如果用户有关联的员工记录，返回员工的角色；否则返回null
     * 
     * @return string|null
     */
    public function getEmployeeRole()
    {
        return $this->employee ? $this->employee->role : null;
    }
    
    /**
     * 检查用户是否具有特定的员工角色
     * 
     * @param string|array $roles 要检查的角色
     * @return bool
     */
    public function hasEmployeeRole($roles)
    {
        if (!$this->employee) {
            return false;
        }
        
        if (is_array($roles)) {
            return in_array($this->employee->role, $roles);
        }
        
        return $this->employee->role === $roles;
    }
    
    /**
     * 检查用户是否具有管理员权限
     * 
     * @return bool
     */
    public function hasAdminPermission()
    {
        return $this->hasEmployeeRole(['admin', 'manager']);
    }
    
    /**
     * 检查用户是否具有商户级别权限
     * 
     * @return bool
     */
    public function hasMerchantPermission()
    {
        if ($this->hasAdminPermission()) {
            return true;
        }
        
        return $this->hasEmployeeRole(['staff', 'crm_agent', 'delivery', 'warehouse_manager']);
    }

    /**
     * 获取用户的所有收货地址
     */
    public function addresses()
    {
        return $this->hasMany(UserAddress::class);
    }

    /**
     * 获取用户的默认收货地址
     */
    public function defaultAddress()
    {
        return $this->hasOne(UserAddress::class)->where('is_default', true);
    }

    /**
     * 获取用户的行为分析记录
     */
    public function behaviorAnalytics()
    {
        return $this->hasMany(\App\Crm\Models\CustomerBehaviorAnalytics::class);
    }

    /**
     * 获取用户的会话记录
     */
    public function sessions()
    {
        return $this->hasMany(\App\Crm\Models\UserSession::class);
    }

    /**
     * 获取用户的行为统计
     */
    public function behaviorStatistics()
    {
        return $this->hasMany(\App\Crm\Models\BehaviorStatistics::class);
    }

    /**
     * 获取用户的订单
     */
    public function orders()
    {
        return $this->hasMany(\App\Order\Models\Order::class);
    }
}
