<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('upload_configs', function (Blueprint $table) {
            $table->id();
            $table->string('key', 100)->unique()->comment('配置键名');
            $table->text('value')->nullable()->comment('配置值');
            $table->string('group', 50)->default('basic')->comment('配置组');
            $table->string('type', 30)->default('string')->comment('值类型：string, number, boolean, json, array');
            $table->string('description')->nullable()->comment('配置说明');
            $table->timestamps();
            
            // 创建索引
            $table->index('group');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('upload_configs');
    }
};
