<?php

use App\Order\Http\Controllers\OrderController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'orders', 'middleware' => ['web', 'auth']], function () {
    Route::get('/', [OrderController::class, 'index'])->name('orders.index');
    Route::post('/', [OrderController::class, 'create'])->name('orders.create');
    Route::get('/{id}', [OrderController::class, 'show'])->name('orders.show');
    Route::put('/{id}/status', [OrderController::class, 'updateStatus'])->name('orders.update-status');
    Route::post('/{id}/cancel', [OrderController::class, 'cancel'])->name('orders.cancel');
    Route::post('/proxy', [OrderController::class, 'createForClient'])->name('orders.create-for-client');
}); 