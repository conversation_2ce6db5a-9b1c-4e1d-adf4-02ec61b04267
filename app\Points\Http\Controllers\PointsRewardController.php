<?php

namespace App\Points\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Points\Services\PointsService;
use App\Product\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PointsRewardController extends Controller
{
    protected PointsService $pointsService;

    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * 获取商品积分奖励预览
     */
    public function productRewardPreview(Request $request, int $productId): JsonResponse
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
            'quantity' => 'sometimes|integer|min:1',
        ]);

        $amount = $request->get('amount');
        $quantity = $request->get('quantity', 1);

        $preview = $this->pointsService->getProductPointsRewardPreview($productId, $amount, $quantity);

        return response()->json([
            'success' => true,
            'data' => $preview
        ]);
    }

    /**
     * 获取购物车积分奖励预览
     */
    public function cartRewardPreview(Request $request): JsonResponse
    {
        $request->validate([
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|integer|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.price' => 'required|numeric|min:0',
            'items.*.product_name' => 'sometimes|string',
        ]);

        $cartItems = $request->get('items');
        $preview = $this->pointsService->getCartPointsRewardPreview($cartItems);

        return response()->json([
            'success' => true,
            'data' => $preview
        ]);
    }

    /**
     * 获取有积分奖励的商品列表
     */
    public function productsWithRewards(Request $request): JsonResponse
    {
        $request->validate([
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100',
            'reward_type' => 'sometimes|string|in:fixed,rate',
            'min_points' => 'sometimes|integer|min:0',
            'category_id' => 'sometimes|integer|exists:categories,id',
        ]);

        $query = Product::withPointsReward()->with(['category']);

        // 按奖励类型筛选
        if ($request->filled('reward_type')) {
            $query->byPointsRewardType($request->reward_type);
        }

        // 按最小积分筛选
        if ($request->filled('min_points')) {
            $minPoints = $request->min_points;
            $query->where(function ($q) use ($minPoints) {
                $q->where('points_reward_fixed', '>=', $minPoints)
                  ->orWhere(function ($subQ) use ($minPoints) {
                      $subQ->where('points_reward_type', Product::POINTS_REWARD_TYPE_RATE)
                           ->whereRaw('(price * points_reward_rate) >= ?', [$minPoints]);
                  });
            });
        }

        // 按分类筛选
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // 分页
        $perPage = min($request->get('per_page', 20), 100);
        $products = $query->paginate($perPage);

        // 添加积分奖励信息
        $products->getCollection()->transform(function ($product) {
            $productArray = $product->toArray();
            $productArray['points_reward'] = $product->getPointsRewardConfig();
            return $productArray;
        });

        return response()->json([
            'success' => true,
            'data' => $products->items(),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
            ]
        ]);
    }

    /**
     * 获取积分奖励统计
     */
    public function rewardStats(Request $request): JsonResponse
    {
        // 有积分奖励的商品总数
        $totalProductsWithRewards = Product::withPointsReward()->count();

        // 按奖励类型统计
        $rewardTypeStats = Product::withPointsReward()
            ->selectRaw('points_reward_type, COUNT(*) as count')
            ->groupBy('points_reward_type')
            ->get()
            ->map(function ($item) {
                return [
                    'type' => $item->points_reward_type,
                    'type_text' => match($item->points_reward_type) {
                        Product::POINTS_REWARD_TYPE_FIXED => '固定积分',
                        Product::POINTS_REWARD_TYPE_RATE => '按比例',
                        default => '其他'
                    },
                    'count' => $item->count
                ];
            });

        // 平均积分奖励
        $avgFixedReward = Product::where('points_reward_type', Product::POINTS_REWARD_TYPE_FIXED)
            ->where('points_reward_enabled', true)
            ->avg('points_reward_fixed');

        $avgRateReward = Product::where('points_reward_type', Product::POINTS_REWARD_TYPE_RATE)
            ->where('points_reward_enabled', true)
            ->avg('points_reward_rate');

        // 最高积分奖励商品
        $topFixedRewardProduct = Product::where('points_reward_type', Product::POINTS_REWARD_TYPE_FIXED)
            ->where('points_reward_enabled', true)
            ->orderBy('points_reward_fixed', 'desc')
            ->select('id', 'name', 'points_reward_fixed')
            ->first();

        $topRateRewardProduct = Product::where('points_reward_type', Product::POINTS_REWARD_TYPE_RATE)
            ->where('points_reward_enabled', true)
            ->orderBy('points_reward_rate', 'desc')
            ->select('id', 'name', 'points_reward_rate', 'price')
            ->first();

        return response()->json([
            'success' => true,
            'data' => [
                'total_products_with_rewards' => $totalProductsWithRewards,
                'reward_type_stats' => $rewardTypeStats,
                'average_rewards' => [
                    'fixed_points' => round($avgFixedReward ?? 0, 2),
                    'rate_percentage' => round(($avgRateReward ?? 0) * 100, 2),
                ],
                'top_rewards' => [
                    'fixed' => $topFixedRewardProduct,
                    'rate' => $topRateRewardProduct ? [
                        'id' => $topRateRewardProduct->id,
                        'name' => $topRateRewardProduct->name,
                        'rate_percentage' => round($topRateRewardProduct->points_reward_rate * 100, 2),
                        'estimated_points_per_100' => round($topRateRewardProduct->price * $topRateRewardProduct->points_reward_rate, 0),
                    ] : null,
                ],
            ]
        ]);
    }

    /**
     * 获取积分奖励配置选项
     */
    public function rewardOptions(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'reward_types' => [
                    [
                        'value' => Product::POINTS_REWARD_TYPE_FIXED,
                        'label' => '固定积分',
                        'description' => '每件商品给予固定数量的积分'
                    ],
                    [
                        'value' => Product::POINTS_REWARD_TYPE_RATE,
                        'label' => '按比例奖励',
                        'description' => '按订单金额的一定比例给予积分'
                    ],
                    [
                        'value' => Product::POINTS_REWARD_TYPE_NONE,
                        'label' => '无奖励',
                        'description' => '该商品不给予积分奖励'
                    ],
                ],
                'default_config' => [
                    'fixed_points_range' => [1, 1000],
                    'rate_range' => [0.01, 0.20], // 1% - 20%
                    'max_points_range' => [10, 10000],
                    'min_amount_range' => [0, 1000],
                ],
                'examples' => [
                    [
                        'type' => 'fixed',
                        'description' => '每购买1件商品获得10积分',
                        'config' => [
                            'points_reward_type' => Product::POINTS_REWARD_TYPE_FIXED,
                            'points_reward_fixed' => 10,
                        ]
                    ],
                    [
                        'type' => 'rate',
                        'description' => '按订单金额1%获得积分，最多100积分',
                        'config' => [
                            'points_reward_type' => Product::POINTS_REWARD_TYPE_RATE,
                            'points_reward_rate' => 0.01,
                            'points_reward_max' => 100,
                        ]
                    ],
                ],
            ]
        ]);
    }
} 