<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Product\Models\Product;
use App\Points\Models\PointsRule;
use Illuminate\Support\Facades\DB;

class PointsRewardSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // 为现有商品设置不同的积分奖励策略
        $this->seedProductPointsRewards();
        
        // 创建积分获取规则
        $this->seedPointsRules();
        
        // 创建一些积分商品示例
        $this->seedPointsProducts();
    }

    /**
     * 为商品设置积分奖励
     */
    private function seedProductPointsRewards()
    {
        $products = Product::limit(50)->get();
        
        foreach ($products as $index => $product) {
            // 根据索引设置不同的奖励策略
            switch ($index % 4) {
                case 0:
                    // 固定积分奖励
                    $product->setFixedPointsReward(
                        points: rand(5, 50),
                        maxPoints: rand(100, 500),
                        minAmount: 0
                    );
                    break;
                    
                case 1:
                    // 按比例奖励
                    $product->setRatePointsReward(
                        rate: rand(1, 5) / 100, // 1%-5%
                        maxPoints: rand(50, 200),
                        minAmount: rand(0, 50)
                    );
                    break;
                    
                case 2:
                    // 高比例但有限制
                    $product->setRatePointsReward(
                        rate: rand(5, 10) / 100, // 5%-10%
                        maxPoints: rand(20, 100),
                        minAmount: rand(100, 200)
                    );
                    break;
                    
                case 3:
                    // 无积分奖励
                    $product->disablePointsReward();
                    break;
            }
            
            // 设置自定义描述
            if ($product->hasPointsReward()) {
                $descriptions = [
                    '购买即赚积分，积分可兑换好礼',
                    '新品上市，双倍积分奖励',
                    '热销商品，积分返还',
                    '限时积分加倍活动',
                    '会员专享积分奖励'
                ];
                $product->points_reward_desc = $descriptions[array_rand($descriptions)];
            }
            
            $product->save();
        }
    }

    /**
     * 创建积分获取规则
     */
    private function seedPointsRules()
    {
        $rules = [
            [
                'name' => '每日签到',
                'rule_type' => 'signin',
                'points_amount' => 10,
                'max_times_per_day' => 1,
                'max_times_total' => null,
                'status' => true,
                'description' => '每日签到获得10积分',
                'conditions' => null,
            ],
            [
                'name' => '邀请好友注册',
                'rule_type' => 'invite',
                'points_amount' => 100,
                'max_times_per_day' => 5,
                'max_times_total' => 50,
                'status' => true,
                'description' => '成功邀请好友注册获得100积分',
                'conditions' => json_encode(['require_friend_first_order' => true]),
            ],
            [
                'name' => '商品评价',
                'rule_type' => 'review',
                'points_amount' => 5,
                'max_times_per_day' => 10,
                'max_times_total' => null,
                'status' => true,
                'description' => '发表商品评价获得5积分',
                'conditions' => json_encode(['min_content_length' => 10]),
            ],
            [
                'name' => '分享商品',
                'rule_type' => 'share',
                'points_amount' => 2,
                'max_times_per_day' => 20,
                'max_times_total' => null,
                'status' => true,
                'description' => '分享商品到社交媒体获得2积分',
                'conditions' => null,
            ],
            [
                'name' => '生日奖励',
                'rule_type' => 'birthday',
                'points_amount' => 50,
                'max_times_per_day' => 1,
                'max_times_total' => null,
                'status' => true,
                'description' => '生日当天获得50积分奖励',
                'conditions' => null,
            ],
            [
                'name' => '会员升级奖励',
                'rule_type' => 'upgrade',
                'points_amount' => 200,
                'max_times_per_day' => null,
                'max_times_total' => null,
                'status' => true,
                'description' => '会员等级升级获得200积分奖励',
                'conditions' => json_encode(['min_level' => 2]),
            ],
        ];

        foreach ($rules as $rule) {
            PointsRule::updateOrCreate(
                ['rule_type' => $rule['rule_type']],
                $rule
            );
        }
    }

    /**
     * 创建积分商品示例
     */
    private function seedPointsProducts()
    {
        $pointsProducts = [
            [
                'name' => '10元优惠券',
                'description' => '全场通用10元优惠券，满50元可用',
                'points_price' => 1000,
                'cash_price' => 0,
                'exchange_type' => 'pure_points',
                'category' => 'coupon',
                'status' => true,
                'sort_order' => 1,
                'daily_limit' => 5,
                'total_limit' => 1000,
                'stock_quantity' => 1000,
            ],
            [
                'name' => '精美保温杯',
                'description' => '304不锈钢保温杯，保温12小时',
                'points_price' => 5000,
                'cash_price' => 0,
                'exchange_type' => 'pure_points',
                'category' => 'physical',
                'status' => true,
                'sort_order' => 2,
                'daily_limit' => 2,
                'total_limit' => 100,
                'stock_quantity' => 50,
            ],
            [
                'name' => '会员专享礼包',
                'description' => '包含多种精美小礼品的会员礼包',
                'points_price' => 3000,
                'cash_price' => 30.00,
                'exchange_type' => 'mixed_payment',
                'category' => 'physical',
                'status' => true,
                'sort_order' => 3,
                'daily_limit' => 1,
                'total_limit' => 200,
                'stock_quantity' => 100,
            ],
            [
                'name' => '免运费券',
                'description' => '单笔订单免运费，无金额限制',
                'points_price' => 500,
                'cash_price' => 0,
                'exchange_type' => 'pure_points',
                'category' => 'coupon',
                'status' => true,
                'sort_order' => 4,
                'daily_limit' => 3,
                'total_limit' => null,
                'stock_quantity' => 999999,
            ],
            [
                'name' => 'VIP会员体验卡',
                'description' => '7天VIP会员体验，享受会员专属优惠',
                'points_price' => 2000,
                'cash_price' => 0,
                'exchange_type' => 'pure_points',
                'category' => 'virtual',
                'status' => true,
                'sort_order' => 5,
                'daily_limit' => 1,
                'total_limit' => 500,
                'stock_quantity' => 500,
            ],
        ];

        foreach ($pointsProducts as $productData) {
            \App\Points\Models\PointsProduct::updateOrCreate(
                ['name' => $productData['name']],
                $productData
            );
        }
    }
} 