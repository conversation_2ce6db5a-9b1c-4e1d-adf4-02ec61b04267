<?php

namespace App\Supplier\Providers;

use Illuminate\Support\ServiceProvider;

class SupplierServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     *
     * @return void
     */
    public function register()
    {
        // 注册服务
    }

    /**
     * 启动服务
     *
     * @return void
     */
    public function boot()
    {
        // 加载路由
        $this->loadRoutes();
    }

    /**
     * 加载路由文件
     *
     * @return void
     */
    protected function loadRoutes()
    {
        // API路由
        $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
        
        // Web路由
        $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');
    }
} 