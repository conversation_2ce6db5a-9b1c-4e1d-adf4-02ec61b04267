<?php

namespace App\Employee\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use App\Crm\Models\CrmAgent;
use App\Crm\Models\ClientFollowUp;
use App\Delivery\Models\Deliverer;
use App\Models\User;

/**
 * 员工模型类
 * 
 * 用于管理员工信息，包括基本资料、登录凭证和权限角色
 * 继承自Authenticatable以支持认证功能
 * 
 * @property int $id 员工ID
 * @property string $name 员工姓名
 * @property string $username 登录用户名
 * @property string $password 加密后的密码
 * @property string|null $phone 电话号码
 * @property string $position 职位
 * @property string $role 角色权限(admin, manager, staff, crm_agent, delivery, warehouse_manager)
 * @property string|null $remember_token 记住我令牌
 * @property \Illuminate\Support\Carbon|null $created_at 创建时间
 * @property \Illuminate\Support\Carbon|null $updated_at 更新时间
 */
class Employee extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * 角色常量定义
     */
    const ROLE_ADMIN = 'admin';
    const ROLE_MANAGER = 'manager';
    const ROLE_STAFF = 'staff';
    const ROLE_CRM_AGENT = 'crm_agent';
    const ROLE_DELIVERY = 'delivery';
    const ROLE_WAREHOUSE_MANAGER = 'warehouse_manager';

    /**
     * 允许批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'username',
        'password',
        'phone',
        'position',
        'role',
    ];

    /**
     * 应该为序列化隐藏的属性
     * 这些属性在模型转换为数组或JSON时会被隐藏
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * 模型的默认属性值
     * 在创建新记录时，如果没有明确设置这些属性，将使用这些默认值
     *
     * @var array
     */
    protected $attributes = [
        'role' => 'staff',
    ];

    /**
     * 密码加密修改器
     * 在设置password属性时自动进行bcrypt加密
     *
     * @param string $value 明文密码
     * @return void
     */
    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = bcrypt($value);
    }

    /**
     * 判断是否是管理员
     *
     * @return bool 如果是管理员返回true，否则返回false
     */
    public function isAdmin()
    {
        return $this->role === self::ROLE_ADMIN;
    }

    /**
     * 判断是否是经理
     *
     * @return bool 如果是经理返回true，否则返回false
     */
    public function isManager()
    {
        return $this->role === self::ROLE_MANAGER;
    }

    /**
     * 判断是否是普通员工
     *
     * @return bool 如果是普通员工返回true，否则返回false
     */
    public function isStaff()
    {
        return $this->role === self::ROLE_STAFF;
    }

    /**
     * 判断员工是否为CRM专员
     * 
     * @return bool 如果是CRM专员返回true，否则返回false
     */
    public function isCrmAgent()
    {
        return $this->role === self::ROLE_CRM_AGENT;
    }

    /**
     * 判断员工是否为配送人员
     * 
     * @return bool 如果是配送人员返回true，否则返回false
     */
    public function isDeliveryPerson()
    {
        return $this->role === self::ROLE_DELIVERY;
    }

    /**
     * 判断员工是否为仓库管理员
     * 
     * @return bool 如果是仓库管理员返回true，否则返回false
     */
    public function isWarehouseManager()
    {
        return $this->role === self::ROLE_WAREHOUSE_MANAGER;
    }

    /**
     * 获取此员工作为CRM专员时的详细信息
     */
    public function crmAgentInfo()
    {
        return $this->hasOne(CrmAgent::class, 'employee_id');
    }

    /**
     * 获取此员工作为CRM专员时管理的客户列表
     */
    public function crmClients()
    {
        return $this->hasMany(User::class, 'crm_agent_id');
    }

    /**
     * 获取此员工作为CRM专员的跟进记录
     */
    public function clientFollowUps()
    {
        return $this->hasMany(ClientFollowUp::class, 'employee_id');
    }

    /**
     * 获取此员工关联的用户账号
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 获取此员工关联的配送员信息
     */
    public function deliverer()
    {
        return $this->hasOne(Deliverer::class, 'employee_id');
    }

    /**
     * 获取此员工的用户地址 (通过关联的用户)
     */
    public function addresses()
    {
        return $this->user ? $this->user->addresses() : null;
    }

    /**
     * 检查员工是否有权限访问特定的后台
     *
     * @param string $backend 后台类型 (admin, crm, delivery, warehouse)
     * @return bool
     */
    public function canAccessBackend($backend)
    {
        // 管理员和经理可以访问所有后台
        if (in_array($this->role, [self::ROLE_ADMIN, self::ROLE_MANAGER])) {
            return true;
        }

        switch ($backend) {
            case 'admin':
                return $this->isAdmin() || $this->isManager() || $this->isStaff();
            case 'crm':
                return $this->isCrmAgent() || $this->isStaff();
            case 'delivery':
                return $this->isDeliveryPerson();
            case 'warehouse':
                return $this->isWarehouseManager();
            default:
                return false;
        }
    }
} 