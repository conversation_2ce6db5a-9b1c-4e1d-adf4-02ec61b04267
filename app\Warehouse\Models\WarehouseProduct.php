<?php

namespace App\Warehouse\Models;

use App\Product\Models\Product;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;

class WarehouseProduct extends Pivot
{
    use HasFactory;
    
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'inventory';
    
    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'warehouse_id',
        'product_id',
        'stock',
    ];
    
    /**
     * 获取关联的仓库
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }
    
    /**
     * 获取关联的产品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
} 