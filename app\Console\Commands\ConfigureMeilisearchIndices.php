<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;

class ConfigureMeilisearchIndices extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'meilisearch:configure-indices';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '配置 Meilisearch 索引设置';

    /**
     * Meilisearch 主机
     *
     * @var string
     */
    protected $host;

    /**
     * Meilisearch API 密钥
     *
     * @var string|null
     */
    protected $apiKey;

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        $this->host = Config::get('scout.meilisearch.host');
        $this->apiKey = Config::get('scout.meilisearch.key');
        
        $this->info('开始配置 Meilisearch 索引...');
        
        // 检查 Meilisearch 服务是否可用
        try {
            $response = Http::timeout(5)->get($this->host . '/health');
            if ($response->status() !== 200) {
                $this->error('Meilisearch 服务不可用，请确保服务已启动');
                return Command::FAILURE;
            }
        } catch (\Exception $e) {
            $this->error('无法连接到 Meilisearch 服务: ' . $e->getMessage());
            $this->info('请确保 Meilisearch 服务已启动，并且可以通过 ' . $this->host . ' 访问');
            return Command::FAILURE;
        }
        
        $this->info('Meilisearch 服务正常运行');
        
        // 获取索引配置
        $indexSettings = Config::get('scout.meilisearch.index-settings', []);
        
        // 配置产品索引
        if (isset($indexSettings['products'])) {
            $this->configureIndex('products', $indexSettings['products']);
        } else {
            $this->warn('未找到产品索引配置');
        }
        
        $this->info('Meilisearch 索引配置完成');
        
        return Command::SUCCESS;
    }
    
    /**
     * 配置指定索引
     *
     * @param string $indexName 索引名称
     * @param array $settings 索引设置
     * @return void
     */
    protected function configureIndex($indexName, array $settings)
    {
        $this->info("配置索引: {$indexName}");
        
        $prefix = Config::get('scout.prefix');
        $fullIndexName = $prefix . $indexName;
        
        $headers = ['Content-Type' => 'application/json'];
        if ($this->apiKey) {
            $headers['Authorization'] = 'Bearer ' . $this->apiKey;
        }
        
        // 配置可过滤属性
        if (isset($settings['filterableAttributes'])) {
            $this->info("设置可过滤属性...");
            $response = Http::withHeaders($headers)
                ->put(
                    $this->host . '/indexes/' . $fullIndexName . '/settings/filterable-attributes',
                    $settings['filterableAttributes']
                );
                
            if ($response->successful()) {
                $this->info("可过滤属性设置成功");
            } else {
                $this->error("设置可过滤属性失败: " . $response->body());
            }
        }
        
        // 配置可排序属性
        if (isset($settings['sortableAttributes'])) {
            $this->info("设置可排序属性...");
            $response = Http::withHeaders($headers)
                ->put(
                    $this->host . '/indexes/' . $fullIndexName . '/settings/sortable-attributes',
                    $settings['sortableAttributes']
                );
                
            if ($response->successful()) {
                $this->info("可排序属性设置成功");
            } else {
                $this->error("设置可排序属性失败: " . $response->body());
            }
        }
        
        // 配置可搜索属性
        if (isset($settings['searchableAttributes'])) {
            $this->info("设置可搜索属性...");
            $response = Http::withHeaders($headers)
                ->put(
                    $this->host . '/indexes/' . $fullIndexName . '/settings/searchable-attributes',
                    $settings['searchableAttributes']
                );
                
            if ($response->successful()) {
                $this->info("可搜索属性设置成功");
            } else {
                $this->error("设置可搜索属性失败: " . $response->body());
            }
        }
        
        // 配置显示属性
        if (isset($settings['displayedAttributes'])) {
            $this->info("设置显示属性...");
            $response = Http::withHeaders($headers)
                ->put(
                    $this->host . '/indexes/' . $fullIndexName . '/settings/displayed-attributes',
                    $settings['displayedAttributes']
                );
                
            if ($response->successful()) {
                $this->info("显示属性设置成功");
            } else {
                $this->error("设置显示属性失败: " . $response->body());
            }
        }
        
        // 配置排名规则
        if (isset($settings['rankingRules'])) {
            $this->info("设置排名规则...");
            $response = Http::withHeaders($headers)
                ->put(
                    $this->host . '/indexes/' . $fullIndexName . '/settings/ranking-rules',
                    $settings['rankingRules']
                );
                
            if ($response->successful()) {
                $this->info("排名规则设置成功");
            } else {
                $this->error("设置排名规则失败: " . $response->body());
            }
        }
    }
} 