<?php

namespace App\Employee\Services;

use App\Employee\Models\Employee;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * 员工服务类
 * 
 * 提供员工相关的业务逻辑处理
 */
class EmployeeService
{
    /**
     * 获取员工列表
     *
     * @param array $filters 过滤条件
     * @param int $perPage 每页记录数
     * @return LengthAwarePaginator
     */
    public function getEmployees(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Employee::query();
        
        // 应用过滤条件
        if (isset($filters['keyword']) && $filters['keyword']) {
            $keyword = $filters['keyword'];
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('username', 'like', "%{$keyword}%")
                  ->orWhere('phone', 'like', "%{$keyword}%")
                  ->orWhere('position', 'like', "%{$keyword}%");
            });
        }
        
        if (isset($filters['role']) && $filters['role']) {
            $query->where('role', $filters['role']);
        }
        
        // 默认排序
        $query->orderBy('created_at', 'desc');
        
        return $query->paginate($perPage);
    }
    
    /**
     * 根据ID获取员工
     *
     * @param int $id 员工ID
     * @return Employee|null
     */
    public function getEmployeeById(int $id): ?Employee
    {
        return Employee::find($id);
    }
    
    /**
     * 创建员工
     *
     * @param array $data 员工数据
     * @return Employee
     */
    public function createEmployee(array $data): Employee
    {
        // 创建事务
        return DB::transaction(function() use ($data) {
            // 创建员工
            $employee = new Employee();
            $employee->name = $data['name'];
            $employee->username = $data['username'];
            $employee->password = $data['password']; // 使用模型中的修改器自动加密
            $employee->phone = $data['phone'] ?? null;
            $employee->position = $data['position'] ?? '普通员工';
            $employee->role = $data['role'] ?? Employee::ROLE_STAFF;
            $employee->save();
            
            // 记录日志
            Log::info('创建员工成功', ['employee_id' => $employee->id, 'username' => $employee->username]);
            
            return $employee;
        });
    }
    
    /**
     * 更新员工信息
     *
     * @param int $id 员工ID
     * @param array $data 员工数据
     * @return Employee|null
     */
    public function updateEmployee(int $id, array $data): ?Employee
    {
        $employee = $this->getEmployeeById($id);
        
        if (!$employee) {
            return null;
        }
        
        return DB::transaction(function() use ($employee, $data) {
            // 更新基本信息
            if (isset($data['name'])) {
                $employee->name = $data['name'];
            }
            
            if (isset($data['username'])) {
                $employee->username = $data['username'];
            }
            
            if (isset($data['password']) && $data['password']) {
                $employee->password = $data['password']; // 使用模型中的修改器自动加密
            }
            
            if (isset($data['phone'])) {
                $employee->phone = $data['phone'];
            }
            
            if (isset($data['position'])) {
                $employee->position = $data['position'];
            }
            
            if (isset($data['role'])) {
                $employee->role = $data['role'];
            }
            
            $employee->save();
            
            // 记录日志
            Log::info('更新员工信息成功', ['employee_id' => $employee->id]);
            
            return $employee;
        });
    }
    
    /**
     * 删除员工
     *
     * @param int $id 员工ID
     * @return bool
     */
    public function deleteEmployee(int $id): bool
    {
        $employee = $this->getEmployeeById($id);
        
        if (!$employee) {
            return false;
        }
        
        return DB::transaction(function() use ($employee) {
            // 删除员工
            $employee->delete();
            
            // 记录日志
            Log::info('删除员工成功', ['employee_id' => $employee->id, 'username' => $employee->username]);
            
            return true;
        });
    }
    
    /**
     * 更新员工状态
     *
     * @param int $id 员工ID
     * @param bool $active 是否激活
     * @return Employee|null
     */
    public function updateEmployeeStatus(int $id, bool $active): ?Employee
    {
        $employee = $this->getEmployeeById($id);
        
        if (!$employee) {
            return null;
        }
        
        $employee->active = $active;
        $employee->save();
        
        Log::info('更新员工状态', [
            'employee_id' => $employee->id,
            'status' => $active ? '激活' : '禁用'
        ]);
        
        return $employee;
    }
    
    /**
     * 更新员工角色
     *
     * @param int $id 员工ID
     * @param string $role 角色
     * @return Employee|null
     */
    public function updateEmployeeRole(int $id, string $role): ?Employee
    {
        $employee = $this->getEmployeeById($id);
        
        if (!$employee) {
            return null;
        }
        
        // 检查角色是否有效
        $validRoles = [
            Employee::ROLE_ADMIN,
            Employee::ROLE_MANAGER,
            Employee::ROLE_STAFF,
            Employee::ROLE_CRM_AGENT,
            Employee::ROLE_DELIVERY,
            Employee::ROLE_WAREHOUSE_MANAGER
        ];
        
        if (!in_array($role, $validRoles)) {
            throw new \InvalidArgumentException('无效的角色类型');
        }
        
        $employee->role = $role;
        $employee->save();
        
        Log::info('更新员工角色', [
            'employee_id' => $employee->id,
            'role' => $role
        ]);
        
        return $employee;
    }
    
    /**
     * 获取所有可用角色
     *
     * @return array
     */
    public function getAvailableRoles(): array
    {
        return [
            [
                'value' => Employee::ROLE_ADMIN,
                'label' => '超级管理员',
                'description' => '拥有系统所有权限'
            ],
            [
                'value' => Employee::ROLE_MANAGER,
                'label' => '经理',
                'description' => '管理店铺运营的各个方面'
            ],
            [
                'value' => Employee::ROLE_STAFF,
                'label' => '普通员工',
                'description' => '执行日常工作'
            ],
            [
                'value' => Employee::ROLE_CRM_AGENT,
                'label' => 'CRM专员',
                'description' => '负责客户关系管理'
            ],
            [
                'value' => Employee::ROLE_DELIVERY,
                'label' => '配送员',
                'description' => '负责订单配送'
            ],
            [
                'value' => Employee::ROLE_WAREHOUSE_MANAGER,
                'label' => '仓库管理员',
                'description' => '负责库存和仓库管理'
            ]
        ];
    }
} 