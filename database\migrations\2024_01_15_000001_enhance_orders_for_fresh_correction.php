<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. 修改cod_status为ENUM类型
        DB::statement("ALTER TABLE `orders` MODIFY COLUMN `cod_status` ENUM(
            'unpaid',
            'link_generated', 
            'paid',
            'failed',
            'expired'
        ) NULL COMMENT '货到付款状态'");
        
        // 2. 扩展订单状态
        DB::statement("ALTER TABLE `orders` MODIFY COLUMN `status` ENUM(
            'pending',
            'paid', 
            'shipped',
            'delivered',
            'corrected',
            'confirmed',
            'completed',
            'cancelled'
        ) NOT NULL DEFAULT 'pending'");
        
        // 3. 添加订单更正相关字段
        Schema::table('orders', function (Blueprint $table) {
            $table->boolean('is_corrected')->default(false)->after('cod_received_by')->comment('是否已更正');
            $table->enum('correction_status', ['none', 'pending', 'confirmed'])->default('none')->after('is_corrected')->comment('更正状态');
            $table->decimal('original_payment_amount', 10, 2)->nullable()->after('correction_status')->comment('原始付款金额');
            $table->decimal('final_payment_amount', 10, 2)->nullable()->after('original_payment_amount')->comment('最终收款金额（货到付款）');
            
            // 付款链接相关字段
            $table->string('payment_link_id', 64)->nullable()->after('final_payment_amount')->comment('付款链接ID');
            $table->timestamp('payment_link_expires_at')->nullable()->after('payment_link_id')->comment('付款链接过期时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复原始状态
        DB::statement("ALTER TABLE `orders` MODIFY COLUMN `status` ENUM(
            'pending',
            'paid',
            'shipped', 
            'delivered',
            'cancelled'
        ) NOT NULL DEFAULT 'pending'");
        
        DB::statement("ALTER TABLE `orders` MODIFY COLUMN `cod_status` VARCHAR(255) NULL COMMENT '货到付款状态：unpaid(未支付), paid(已支付), failed(支付失败)'");
        
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn([
                'is_corrected',
                'correction_status', 
                'original_payment_amount',
                'final_payment_amount',
                'payment_link_id',
                'payment_link_expires_at'
            ]);
        });
    }
}; 