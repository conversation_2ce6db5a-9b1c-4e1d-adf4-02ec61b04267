<?php

namespace App\Crm\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use Carbon\Carbon;

class BehaviorStatistics extends Model
{
    protected $table = 'behavior_statistics';

    protected $fillable = [
        'user_id',
        'stat_date',
        'page_views',
        'product_views',
        'cart_operations',
        'search_count',
        'order_count',
        'session_count',
        'total_duration',
        'total_amount',
    ];

    protected $casts = [
        'stat_date' => 'date',
        'page_views' => 'integer',
        'product_views' => 'integer',
        'cart_operations' => 'integer',
        'search_count' => 'integer',
        'order_count' => 'integer',
        'session_count' => 'integer',
        'total_duration' => 'integer',
        'total_amount' => 'decimal:2',
    ];

    /**
     * 获取关联的用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 创建或更新统计数据
     */
    public static function createOrUpdateForUser(
        int $userId,
        Carbon $date,
        array $data = []
    ): self {
        return self::updateOrCreate(
            [
                'user_id' => $userId,
                'stat_date' => $date->format('Y-m-d'),
            ],
            $data
        );
    }

    /**
     * 增加页面浏览次数
     */
    public function incrementPageViews(int $count = 1): void
    {
        $this->increment('page_views', $count);
    }

    /**
     * 增加商品浏览次数
     */
    public function incrementProductViews(int $count = 1): void
    {
        $this->increment('product_views', $count);
    }

    /**
     * 增加购物车操作次数
     */
    public function incrementCartOperations(int $count = 1): void
    {
        $this->increment('cart_operations', $count);
    }

    /**
     * 增加搜索次数
     */
    public function incrementSearchCount(int $count = 1): void
    {
        $this->increment('search_count', $count);
    }

    /**
     * 增加订单数量
     */
    public function incrementOrderCount(int $count = 1): void
    {
        $this->increment('order_count', $count);
    }

    /**
     * 增加会话数量
     */
    public function incrementSessionCount(int $count = 1): void
    {
        $this->increment('session_count', $count);
    }

    /**
     * 增加总停留时间
     */
    public function addDuration(int $seconds): void
    {
        $this->increment('total_duration', $seconds);
    }

    /**
     * 增加消费金额
     */
    public function addAmount(float $amount): void
    {
        $this->increment('total_amount', $amount);
    }

    /**
     * 获取平均会话时长（分钟）
     */
    public function getAverageSessionDuration(): float
    {
        if ($this->session_count == 0) {
            return 0;
        }
        
        return round($this->total_duration / $this->session_count / 60, 2);
    }

    /**
     * 获取平均页面浏览数
     */
    public function getAveragePageViews(): float
    {
        if ($this->session_count == 0) {
            return 0;
        }
        
        return round($this->page_views / $this->session_count, 2);
    }

    /**
     * 按时间范围查询
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('stat_date', [$startDate, $endDate]);
    }

    /**
     * 按用户查询
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 按日期查询
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('stat_date', $date);
    }

    /**
     * 查询最近N天的数据
     */
    public function scopeRecentDays($query, int $days = 7)
    {
        return $query->where('stat_date', '>=', now()->subDays($days)->format('Y-m-d'));
    }
} 