<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 确保表存在
        if (Schema::hasTable('unit_conversions')) {
            Schema::table('unit_conversions', function (Blueprint $table) {
                // 添加双向字段
                if (!Schema::hasColumn('unit_conversions', 'is_bidirectional')) {
                    $table->boolean('is_bidirectional')->default(false)->comment('是否双向转换');
                }
                
                // 添加优先级字段
                if (!Schema::hasColumn('unit_conversions', 'priority')) {
                    $table->integer('priority')->default(10)->comment('转换优先级');
                }
                
                // 添加额外说明字段
                if (!Schema::hasColumn('unit_conversions', 'description')) {
                    $table->text('description')->nullable()->comment('转换说明');
                }
                
                // 修改列名
                if (Schema::hasColumn('unit_conversions', 'rate') && !Schema::hasColumn('unit_conversions', 'conversion_factor')) {
                    $table->renameColumn('rate', 'conversion_factor');
                }
                
                // 确保有正确的索引
                if (!Schema::hasIndex('unit_conversions', ['from_unit_id', 'to_unit_id'])) {
                    $table->unique(['from_unit_id', 'to_unit_id']);
                }
            });
            
            // 创建反向转换记录
            $this->createInverseConversions();
        }
    }

    /**
     * 为标记为双向的转换创建反向记录
     */
    private function createInverseConversions()
    {
        // 获取所有转换记录
        $conversions = DB::table('unit_conversions')->get();
        
        foreach ($conversions as $conversion) {
            // 检查是否存在反向记录
            $inverseExists = DB::table('unit_conversions')
                              ->where('from_unit_id', $conversion->to_unit_id)
                              ->where('to_unit_id', $conversion->from_unit_id)
                              ->exists();
            
            // 如果不存在反向记录且转换标记为双向或可推断为双向
            if (!$inverseExists) {
                $isBidirectional = false;
                
                // 检查是否有双向字段
                if (Schema::hasColumn('unit_conversions', 'is_bidirectional')) {
                    $isBidirectional = $conversion->is_bidirectional;
                } else {
                    // 没有双向字段，但可以从单位类型推断
                    // 获取单位信息
                    $fromUnit = DB::table('units')->find($conversion->from_unit_id);
                    $toUnit = DB::table('units')->find($conversion->to_unit_id);
                    
                    // 如果是同一类型的单位，可能是双向的
                    if ($fromUnit && $toUnit && $fromUnit->type === $toUnit->type) {
                        $isBidirectional = true;
                    }
                }
                
                // 如果推断为双向，创建反向记录
                if ($isBidirectional) {
                    // 计算反向转换系数（取倒数）
                    $inverseFactor = 1 / $conversion->conversion_factor;
                    
                    // 插入新记录
                    DB::table('unit_conversions')->insert([
                        'from_unit_id' => $conversion->to_unit_id,
                        'to_unit_id' => $conversion->from_unit_id,
                        'conversion_factor' => $inverseFactor,
                        'is_bidirectional' => true,
                        'priority' => $conversion->priority ?? 10,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                    
                    // 更新原始记录为双向
                    if (Schema::hasColumn('unit_conversions', 'is_bidirectional')) {
                        DB::table('unit_conversions')
                          ->where('id', $conversion->id)
                          ->update(['is_bidirectional' => true]);
                    }
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('unit_conversions')) {
            Schema::table('unit_conversions', function (Blueprint $table) {
                // 如果存在新添加的字段，则删除它们
                if (Schema::hasColumn('unit_conversions', 'is_bidirectional')) {
                    $table->dropColumn('is_bidirectional');
                }
                
                if (Schema::hasColumn('unit_conversions', 'priority')) {
                    $table->dropColumn('priority');
                }
                
                if (Schema::hasColumn('unit_conversions', 'description')) {
                    $table->dropColumn('description');
                }
                
                // 将字段名改回原来的名称
                if (Schema::hasColumn('unit_conversions', 'conversion_factor') && !Schema::hasColumn('unit_conversions', 'rate')) {
                    $table->renameColumn('conversion_factor', 'rate');
                }
            });
        }
    }
};
