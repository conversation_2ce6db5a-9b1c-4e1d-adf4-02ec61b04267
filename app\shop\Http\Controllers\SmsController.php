<?php

namespace App\shop\Http\Controllers;

use App\Http\Controllers\Controller;
use App\shop\Services\Sms\AliyunSmsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class SmsController extends Controller
{
    /**
     * @var AliyunSmsService
     */
    protected $smsService;

    /**
     * 构造函数
     *
     * @param AliyunSmsService $smsService
     */
    public function __construct(AliyunSmsService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * 发送短信验证码
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function send(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'phone' => 'required|regex:/^1[3-9]\d{9}$/',
                'type' => 'required|in:login,register,reset_password',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            // 检查服务状态
            if (!$this->smsService->isEnabled()) {
                return response()->json([
                    'code' => 403,
                    'message' => '短信验证服务未启用',
                ], 403);
            }

            // 检查冷却时间
            if (!$this->smsService->canSendSms($request->phone)) {
                $cooldown = $this->smsService->getCooldownTime();
                return response()->json([
                    'code' => 429,
                    'message' => "发送过于频繁，请{$cooldown}秒后重试",
                ], 429);
            }

            // 收集请求信息
            $requestInfo = [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ];

            // 发送验证码
            $result = $this->smsService->sendVerificationCode(
                $request->phone,
                $request->type,
                $requestInfo
            );

            if (!$result['success']) {
                return response()->json([
                    'code' => 500,
                    'message' => $result['message'],
                ], 500);
            }

            $response = [
                'code' => 200,
                'message' => $result['message'],
            ];

            // 仅在测试环境返回验证码
            if (isset($result['code']) && app()->environment('local', 'testing')) {
                $response['data'] = [
                    'code' => $result['code']
                ];
            }

            return response()->json($response);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '发送验证码失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 验证短信验证码
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function verify(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'phone' => 'required|regex:/^1[3-9]\d{9}$/',
                'code' => 'required|string',
                'type' => 'required|in:login,register,reset_password',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            $isValid = $this->smsService->verifyCode(
                $request->phone,
                $request->code,
                $request->type,
                $request->input('use_once', true)
            );

            if (!$isValid) {
                return response()->json([
                    'code' => 400,
                    'message' => '验证码无效或已过期',
                ], 400);
            }

            return response()->json([
                'code' => 200,
                'message' => '验证码验证成功',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '验证码验证失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取短信配置状态（是否启用等）
     *
     * @return JsonResponse
     */
    public function getStatus(): JsonResponse
    {
        return response()->json([
            'code' => 200,
            'data' => [
                'enabled' => $this->smsService->isEnabled(),
                'cooldown' => $this->smsService->getCooldownTime(),
                'expire' => $this->smsService->getExpireTime(),
            ],
        ]);
    }

    /**
     * 测试短信发送（仅管理后台使用）
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function testSend(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'phone' => 'required|regex:/^1[3-9]\d{9}$/',
                'type' => 'required|in:login,register,reset_password',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            // 发送验证码，忽略冷却时间限制
            $requestInfo = [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ];

            // 发送验证码
            $result = $this->smsService->sendVerificationCode(
                $request->phone,
                $request->type,
                $requestInfo
            );

            $response = [
                'code' => $result['success'] ? 200 : 500,
                'message' => $result['message'],
            ];

            // 无论是否测试环境，都返回验证码
            if (isset($result['code'])) {
                $response['data'] = [
                    'code' => $result['code']
                ];
            }

            return response()->json($response);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '发送测试短信失败: ' . $e->getMessage(),
            ], 500);
        }
    }
} 