<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 添加排序字段，默认为0，数字越大排序越靠前
            $table->integer('sort')->default(0)->after('status')->comment('排序权重，数字越大排序越靠前');
            // 添加索引以便更快地按排序字段查询
            $table->index('sort', 'idx_products_sort');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 删除索引
            $table->dropIndex('idx_products_sort');
            // 删除排序字段
            $table->dropColumn('sort');
        });
    }
};
