# 防重复付款机制设计文档

## 概述

为了确保支付系统的安全性和可靠性，我们实现了一套完整的防重复付款机制，从多个层面防止用户重复支付和系统异常导致的重复扣款。

## 🛡️ 防护机制

### 1. 数据库层面防护

#### 状态机制
```php
// 付款链接状态
enum PaymentLinkStatus {
    'active',      // 活跃状态，可以支付
    'processing',  // 支付处理中，防止并发
    'paid',        // 已支付，不可重复支付
    'expired',     // 已过期
    'cancelled'    // 已取消
}
```

#### 原子操作
```php
// 使用数据库锁防止并发支付
public function startPaymentProcessing(): bool
{
    return $this->where('id', $this->id)
        ->where('status', 'active')
        ->update(['status' => 'processing', 'updated_at' => now()]) > 0;
}
```

### 2. 幂等性机制

#### 幂等性键
- **生成规则**: `pay_{timestamp}_{random_string}`
- **作用范围**: 单次支付请求
- **验证逻辑**: 检查相同幂等性键是否已存在

```php
// 前端生成幂等性键
function generateIdempotencyKey() {
    return 'pay_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 后端验证幂等性
if ($idempotencyKey) {
    $existingPayment = PaymentLink::where('idempotency_key', $idempotencyKey)
        ->where('id', '!=', $linkId)
        ->first();
    
    if ($existingPayment) {
        return response()->json(['code' => 400, 'message' => '重复的支付请求']);
    }
}
```

### 3. 交易号防重复

#### 第三方交易号检查
```php
// 检查交易号是否已存在
if ($paymentData['transaction_id']) {
    $existingPayment = PaymentLink::where('transaction_id', $paymentData['transaction_id'])
        ->where('id', '!=', $linkId)
        ->where('status', 'paid')
        ->first();

    if ($existingPayment) {
        Log::warning('重复的交易号', [
            'payment_link_id' => $linkId,
            'transaction_id' => $paymentData['transaction_id'],
        ]);
        return response()->json(['code' => 400, 'message' => '重复的交易号']);
    }
}
```

### 4. 前端防护机制

#### 支付频率限制
```javascript
const MAX_PAYMENT_ATTEMPTS = 3;      // 最大支付尝试次数
const PAYMENT_COOLDOWN = 30000;      // 支付冷却时间（30秒）

function canInitiatePayment() {
    // 检查支付尝试次数
    if (paymentAttempts >= MAX_PAYMENT_ATTEMPTS) {
        showMessage('支付尝试次数过多，请稍后再试或联系客服', 'error');
        return false;
    }
    
    // 检查支付冷却时间
    if (now - lastPaymentTime < PAYMENT_COOLDOWN) {
        const remainingTime = Math.ceil((PAYMENT_COOLDOWN - (now - lastPaymentTime)) / 1000);
        showMessage(`请等待 ${remainingTime} 秒后再试`, 'warning');
        return false;
    }
    
    return true;
}
```

#### 按钮状态控制
```javascript
// 支付过程中禁用按钮
isPaymentProcessing = true;
payBtn.disabled = true;
payBtnText.style.display = 'none';
payBtnLoading.style.display = 'inline-block';
```

### 5. 超时处理机制

#### 支付处理超时
- **超时时间**: 10分钟
- **检查频率**: 每10分钟执行一次清理任务
- **处理方式**: 自动恢复为 `active` 状态

```php
// 检查支付处理是否超时
public function isPaymentTimeout(): bool
{
    if ($this->status !== 'processing') {
        return false;
    }
    
    // 支付处理超过10分钟视为超时
    return $this->updated_at->addMinutes(10)->isPast();
}
```

#### 定时清理任务
```php
// 每10分钟执行一次
$schedule->command('payment:cleanup-timeout')
         ->everyTenMinutes()
         ->withoutOverlapping()
         ->runInBackground();
```

## 🔄 支付流程

### 1. 支付发起流程
```
1. 前端检查 → 2. 生成幂等性键 → 3. 后端验证 → 4. 状态锁定 → 5. 生成支付参数
     ↓              ↓                ↓           ↓            ↓
  频率限制      防重复请求        状态检查    原子操作      记录日志
```

### 2. 支付回调流程
```
1. 接收回调 → 2. 状态检查 → 3. 金额验证 → 4. 交易号检查 → 5. 数据库事务 → 6. 完成支付
     ↓            ↓           ↓            ↓              ↓            ↓
  验证来源      防重复处理    防篡改       防重复交易      原子操作      后续处理
```

## 📊 监控和日志

### 1. 关键日志记录
```php
// 支付发起日志
Log::info('支付发起', [
    'payment_link_id' => $linkId,
    'payment_method' => $paymentMethod,
    'amount' => $paymentLink->amount,
    'idempotency_key' => $idempotencyKey,
    'user_ip' => $request->ip(),
    'user_agent' => $request->userAgent(),
]);

// 支付成功日志
Log::info('支付成功', [
    'payment_link_id' => $linkId,
    'transaction_id' => $paymentData['transaction_id'],
    'amount' => $paymentData['amount'],
    'payment_method' => $paymentData['payment_method'],
]);

// 异常日志
Log::warning('重复的交易号', [
    'payment_link_id' => $linkId,
    'transaction_id' => $paymentData['transaction_id'],
    'existing_payment_id' => $existingPayment->id,
]);
```

### 2. 监控指标
- **支付成功率**: 成功支付 / 总支付尝试
- **重复支付拦截率**: 拦截的重复支付 / 总支付尝试
- **支付处理超时率**: 超时的支付处理 / 总支付处理
- **平均支付处理时间**: 从发起到完成的平均时间

## 🚨 异常处理

### 1. 常见异常场景

#### 重复支付请求
```
原因：用户快速多次点击支付按钮
处理：前端频率限制 + 后端幂等性检查
结果：拒绝重复请求，提示用户
```

#### 并发支付
```
原因：多个设备同时发起支付
处理：数据库锁 + 状态机制
结果：只有一个请求成功，其他请求被拒绝
```

#### 支付处理超时
```
原因：第三方支付接口响应慢或网络问题
处理：定时任务清理 + 状态恢复
结果：自动恢复为可支付状态
```

#### 回调重复
```
原因：第三方支付多次发送回调
处理：交易号检查 + 状态验证
结果：只处理第一次有效回调
```

### 2. 错误码定义
```php
// 支付相关错误码
const PAYMENT_ERRORS = [
    'PAYMENT_ALREADY_PAID' => ['code' => 4001, 'message' => '订单已完成付款，请勿重复支付'],
    'PAYMENT_PROCESSING' => ['code' => 4002, 'message' => '支付正在处理中，请稍后再试'],
    'PAYMENT_EXPIRED' => ['code' => 4003, 'message' => '付款链接已过期'],
    'PAYMENT_CANCELLED' => ['code' => 4004, 'message' => '付款链接已取消'],
    'DUPLICATE_REQUEST' => ['code' => 4005, 'message' => '重复的支付请求'],
    'DUPLICATE_TRANSACTION' => ['code' => 4006, 'message' => '重复的交易号'],
    'AMOUNT_MISMATCH' => ['code' => 4007, 'message' => '支付金额不匹配'],
    'TOO_MANY_ATTEMPTS' => ['code' => 4008, 'message' => '支付尝试次数过多'],
];
```

## 🔧 运维命令

### 1. 清理超时支付
```bash
# 查看将要清理的记录（不实际执行）
php artisan payment:cleanup-timeout --dry-run

# 执行清理
php artisan payment:cleanup-timeout
```

### 2. 清理过期链接
```bash
# 清理过期的付款链接
php artisan payment-links:cleanup-expired
```

### 3. 查看支付统计
```bash
# 查看支付链接统计
curl -X GET "http://your-domain.com/api/payment-links/statistics"
```

## 📈 性能优化

### 1. 数据库优化
```sql
-- 添加索引提高查询性能
CREATE INDEX idx_payment_links_status ON payment_links(status);
CREATE INDEX idx_payment_links_transaction_id ON payment_links(transaction_id);
CREATE INDEX idx_payment_links_idempotency_key ON payment_links(idempotency_key);
CREATE INDEX idx_payment_links_updated_at ON payment_links(updated_at);
```

### 2. 缓存策略
```php
// 缓存支付状态（可选）
Cache::put("payment_status_{$linkId}", $status, 300); // 5分钟缓存
```

### 3. 异步处理
```php
// 支付成功后的业务逻辑可以异步处理
dispatch(new ProcessPaymentSuccessJob($paymentLink));
```

## 🧪 测试建议

### 1. 单元测试
- 测试状态机转换
- 测试幂等性检查
- 测试超时处理

### 2. 集成测试
- 测试完整支付流程
- 测试并发支付场景
- 测试异常恢复机制

### 3. 压力测试
- 模拟高并发支付
- 测试系统稳定性
- 验证防护机制有效性

## 📋 部署检查清单

- [ ] 数据库迁移已执行
- [ ] 定时任务已配置
- [ ] 日志目录有写权限
- [ ] 第三方支付配置正确
- [ ] 监控告警已设置
- [ ] 备份策略已制定

---

**注意**: 防重复付款机制是支付系统的核心安全功能，任何修改都应该经过充分测试和评估。 