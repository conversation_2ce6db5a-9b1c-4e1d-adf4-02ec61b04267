<!-- 横向商品卡片 - 修复事件冲突 -->
<view class="horizontal-product-card {{customClass}} {{cardClassNames}}" catchtap="onProductTap">
  
  <!-- 商品图片 -->
  <view class="product-image-wrapper">
    <lazy-image
      src="{{product.image || product.thumbnail || ''}}"
      mode="aspectFill"
      width="100%"
      height="100%"
      custom-class="product-image"
      showPlaceholder="{{true}}"
      showLoadingIndicator="{{false}}"
      placeholderText="加载中"
      errorText="加载失败"
      bind:load="onImageLoad"
      bind:error="onImageError"
      bind:tap="onImageTap"
    />
    
    <!-- 缺货遮罩 -->
    <view class="out-of-stock-overlay" wx:if="{{product.out_of_stock}}">
      <view class="out-of-stock-text">缺货</view>
    </view>
  </view>
  
  <!-- 商品信息 -->
  <view class="product-info">
    <!-- 商品标签 - 参考首页处理方式 -->
    <view class="product-tags-container" wx:if="{{product.tags && product.tags.length > 0}}">
      <van-tag 
        wx:for="{{product.tags}}" 
        wx:key="index"
        wx:for-item="tag"
        wx:for-index="tagIndex"
        type="warning"
        size="small"
        round
        custom-class="product-tag {{tag.cssClass || ''}}"
      >
        {{tag.name || tag}}
      </van-tag>
    </view>
    
    <view class="product-title">{{product.name || product.title || '未命名商品'}}</view>
    <view class="product-subtitle" wx:if="{{product.subtitle}}">{{product.subtitle}}</view>
    
    <!-- 价格和操作 -->
    <view class="product-footer">
      <view class="price-section">
        <price-display 
          productId="{{product.id || 0}}"
          quantity="{{1}}"
          price="{{product.price || '0.00'}}"
          unit="{{product.unit || ''}}"
          className="product-price-horizontal"
          loginText="登录查看"
          showLoginIcon="{{false}}"
          showPriceLabels="{{true}}"
          showDiscountInfo="{{true}}"
          forceShowPrice="{{false}}"
          bind:priceLoaded="onPriceLoaded"
          bind:priceError="onPriceError"
        />
        <!-- 移除自营显示 -->
      </view>
      
      <!-- 购物车控制区域 -->
      <view wx:if="{{showAddCart}}">
        <!-- 缺货按钮 -->
        <view class="out-of-stock-button" wx:if="{{product.out_of_stock}}" catchtap="onOutOfStockTap">
          <text class="out-of-stock-button-text">缺货</text>
        </view>
        <!-- 购物车数量控制 -->
        <view class="cart-controls" wx:else>
          <!-- 没有商品时显示加购按钮 -->
          <view class="add-cart-button" wx:if="{{cartQuantity === 0}}" catchtap="onAddToCart">
            <van-icon name="plus" size="28rpx" color="#fff" />
          </view>
          <!-- 有商品时显示数量控制 -->
          <view class="quantity-controls" wx:else>
            <!-- 减少按钮 -->
            <view class="quantity-btn decrease" catchtap="onDecreaseQuantity">
              <van-icon name="minus" size="24rpx" color="#666" />
            </view>
            <!-- 数量显示/输入 -->
            <view class="quantity-display" catchtap="onQuantityTap">
              <text class="quantity-text">{{cartQuantity > 99 ? '99+' : cartQuantity}}</text>
            </view>
            <!-- 增加按钮 -->
            <view class="quantity-btn increase" catchtap="onIncreaseQuantity">
              <van-icon name="plus" size="24rpx" color="#666" />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  
</view> 