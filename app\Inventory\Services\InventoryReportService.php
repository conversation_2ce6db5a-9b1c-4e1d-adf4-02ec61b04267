<?php

namespace App\Inventory\Services;

use App\Inventory\Models\InventoryReportConfig;
use App\Inventory\Models\InventoryReportInstance;
use App\Inventory\Models\InventoryAnalysisMetric;
use App\Inventory\Models\Inventory;
use App\Inventory\Models\InventoryBatch;
use App\Product\Models\Product;
use App\Warehouse\Models\Warehouse;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class InventoryReportService
{
    /**
     * 生成库存报表
     * 
     * @param int $configId 报表配置ID
     * @param array $parameters 生成参数
     * @param int|null $userId 用户ID
     * @return array
     */
    public function generateReport($configId, $parameters = [], $userId = null)
    {
        try {
            $config = InventoryReportConfig::findOrFail($configId);
            
            // 创建报表实例
            $instance = InventoryReportInstance::create([
                'config_id' => $configId,
                'title' => $this->generateReportTitle($config, $parameters),
                'parameters' => $parameters,
                'report_date' => $parameters['report_date'] ?? now()->toDateString(),
                'data_start_date' => $parameters['start_date'] ?? null,
                'data_end_date' => $parameters['end_date'] ?? null,
                'generated_by' => $userId ?? auth()->id(),
            ]);
            
            // 开始生成
            $instance->startGeneration();
            
            // 根据报表类型生成数据
            $reportData = $this->generateReportData($config, $parameters);
            
            // 生成图表数据
            $chartsData = $this->generateChartsData($config, $reportData);
            
            // 生成摘要
            $summary = $this->generateSummary($config, $reportData);
            
            // 标记完成
            $instance->markAsCompleted([
                'report_data' => $reportData,
                'charts_data' => $chartsData,
                'summary' => $summary,
            ]);
            
            return [
                'success' => true,
                'message' => '报表生成成功',
                'instance_id' => $instance->id,
                'data' => [
                    'report_data' => $reportData,
                    'charts_data' => $chartsData,
                    'summary' => $summary,
                ],
            ];
            
        } catch (\Exception $e) {
            if (isset($instance)) {
                $instance->markAsFailed($e->getMessage());
            }
            
            Log::error('报表生成失败', [
                'config_id' => $configId,
                'parameters' => $parameters,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '报表生成失败：' . $e->getMessage(),
                'instance_id' => $instance->id ?? null,
            ];
        }
    }

    /**
     * 生成报表数据
     * 
     * @param InventoryReportConfig $config
     * @param array $parameters
     * @return array
     */
    protected function generateReportData($config, $parameters)
    {
        switch ($config->type) {
            case 'status':
                return $this->generateStatusReport($parameters);
            
            case 'movement':
                return $this->generateMovementReport($parameters);
            
            case 'analysis':
                return $this->generateAnalysisReport($parameters);
            
            case 'forecast':
                return $this->generateForecastReport($parameters);
            
            default:
                throw new \Exception('未知的报表类型：' . $config->type);
        }
    }

    /**
     * 生成库存状态报表
     * 
     * @param array $parameters
     * @return array
     */
    protected function generateStatusReport($parameters)
    {
        $query = Inventory::with(['product', 'warehouse', 'unit', 'batches']);
        
        // 应用过滤条件
        if (isset($parameters['warehouse_id'])) {
            $query->where('warehouse_id', $parameters['warehouse_id']);
        }
        
        if (isset($parameters['product_id'])) {
            $query->where('product_id', $parameters['product_id']);
        }
        
        if (isset($parameters['category_id'])) {
            $query->whereHas('product', function($q) use ($parameters) {
                $q->where('category_id', $parameters['category_id']);
            });
        }
        
        $inventories = $query->get();
        
        $data = [];
        foreach ($inventories as $inventory) {
            $batchInfo = $this->getBatchSummary($inventory->batches);
            
            $data[] = [
                'warehouse_name' => $inventory->warehouse ? $inventory->warehouse->location : '未知仓库',
                'product_name' => $inventory->product ? $inventory->product->name : '未知商品',
                'product_code' => $inventory->product ? $inventory->product->code : '',
                'category_name' => ($inventory->product && $inventory->product->category) ? $inventory->product->category->name : '',
                'current_stock' => $inventory->stock,
                'unit_name' => $inventory->unit,
                'min_stock_level' => $inventory->min_stock_level,
                'stock_status' => $this->getStockStatus($inventory),
                'total_batches' => $batchInfo['total_batches'],
                'expired_batches' => $batchInfo['expired_batches'],
                'near_expiry_batches' => $batchInfo['near_expiry_batches'],
                'total_value' => $inventory->stock * ($inventory->product && $inventory->product->price ? $inventory->product->price : 0),
                'last_updated' => $inventory->updated_at->format('Y-m-d H:i:s'),
            ];
        }
        
        return [
            'items' => $data,
            'summary' => [
                'total_items' => count($data),
                'total_value' => array_sum(array_column($data, 'total_value')),
                'low_stock_items' => count(array_filter($data, fn($item) => $item['stock_status'] === 'low')),
                'out_of_stock_items' => count(array_filter($data, fn($item) => $item['stock_status'] === 'out')),
            ],
        ];
    }

    /**
     * 生成库存变动报表
     * 
     * @param array $parameters
     * @return array
     */
    protected function generateMovementReport($parameters)
    {
        $startDate = $parameters['start_date'] ?? now()->subMonth()->toDateString();
        $endDate = $parameters['end_date'] ?? now()->toDateString();
        
        // 获取批次操作记录 - 修复字段名称
        $operations = DB::table('batch_operations as bo')
            ->join('inventory_batches as ib', 'bo.batch_id', '=', 'ib.id')
            ->join('inventory as i', 'ib.inventory_id', '=', 'i.id')
            ->join('products as p', 'i.product_id', '=', 'p.id')
            ->join('warehouses as w', 'i.warehouse_id', '=', 'w.id')
            ->leftJoin('employees as e', 'bo.operated_by', '=', 'e.id')
            ->whereBetween('bo.operated_at', [$startDate, $endDate])
            ->select([
                'bo.operated_at',
                'bo.operation_type',
                'bo.quantity_change',
                'bo.reason',
                'ib.batch_code',
                'p.name as product_name',
                'p.code as product_code',
                'w.location as warehouse_name',
                DB::raw('COALESCE(e.name, "系统操作") as operator_name'),
                'bo.reference_type',
                'bo.reference_no',
            ])
            ->orderBy('bo.operated_at', 'desc')
            ->get()
            ->toArray();
        
        // 按日期汇总
        $dailySummary = [];
        foreach ($operations as $operation) {
            $date = Carbon::parse($operation->operated_at)->toDateString();
            
            if (!isset($dailySummary[$date])) {
                $dailySummary[$date] = [
                    'date' => $date,
                    'total_in' => 0,
                    'total_out' => 0,
                    'operations_count' => 0,
                ];
            }
            
            $dailySummary[$date]['operations_count']++;
            
            if ($operation->quantity_change > 0) {
                $dailySummary[$date]['total_in'] += $operation->quantity_change;
            } else {
                $dailySummary[$date]['total_out'] += abs($operation->quantity_change);
            }
        }
        
        return [
            'operations' => $operations,
            'daily_summary' => array_values($dailySummary),
            'summary' => [
                'total_operations' => count($operations),
                'total_in' => array_sum(array_column($dailySummary, 'total_in')),
                'total_out' => array_sum(array_column($dailySummary, 'total_out')),
                'date_range' => [$startDate, $endDate],
            ],
        ];
    }

    /**
     * 生成库存分析报表
     * 
     * @param array $parameters
     * @return array
     */
    protected function generateAnalysisReport($parameters)
    {
        $analysisDate = $parameters['analysis_date'] ?? now()->toDateString();
        
        // 计算基础指标
        $metrics = $this->calculateInventoryMetrics($analysisDate);
        
        // 分类分析
        $categoryAnalysis = $this->analyzeByCategoryAnalysis();
        
        // 仓库分析
        $warehouseAnalysis = $this->analyzeByWarehouse();
        
        // ABC分析
        $abcAnalysis = $this->performABCAnalysis();
        
        // 周转率分析
        $turnoverAnalysis = $this->analyzeTurnover();
        
        return [
            'metrics' => $metrics,
            'category_analysis' => $categoryAnalysis,
            'warehouse_analysis' => $warehouseAnalysis,
            'abc_analysis' => $abcAnalysis,
            'turnover_analysis' => $turnoverAnalysis,
            'analysis_date' => $analysisDate,
        ];
    }

    /**
     * 生成库存预测报表
     * 
     * @param array $parameters
     * @return array
     */
    protected function generateForecastReport($parameters)
    {
        $forecastDays = $parameters['forecast_days'] ?? 30;
        $baseDate = $parameters['base_date'] ?? now()->toDateString();
        
        // 获取历史销售数据（这里简化处理）
        $historicalData = $this->getHistoricalSalesData($baseDate, 90);
        
        // 简单的线性预测
        $forecasts = [];
        foreach ($historicalData as $productId => $data) {
            $forecast = $this->calculateLinearForecast($data, $forecastDays);
            $forecasts[$productId] = $forecast;
        }
        
        // 库存预警
        $stockAlerts = $this->generateStockAlerts($forecasts);
        
        // 补货建议
        $reorderSuggestions = $this->generateReorderSuggestions($forecasts);
        
        return [
            'forecasts' => $forecasts,
            'stock_alerts' => $stockAlerts,
            'reorder_suggestions' => $reorderSuggestions,
            'forecast_period' => $forecastDays,
            'base_date' => $baseDate,
        ];
    }

    /**
     * 计算库存指标
     * 
     * @param string $analysisDate
     * @return array
     */
    protected function calculateInventoryMetrics($analysisDate)
    {
        // 总库存价值和数量
        $totalValue = 0;
        $totalQuantity = 0;
        $totalSkuCount = 0;
        $activeSkuCount = 0;
        $zeroStockSkuCount = 0;
        
        $inventories = Inventory::with('product')->get();
        
        foreach ($inventories as $inventory) {
            $totalSkuCount++;
            $totalQuantity += $inventory->stock;
            $value = $inventory->stock * ($inventory->product->price ?? 0);
            $totalValue += $value;
            
            if ($inventory->stock > 0) {
                $activeSkuCount++;
            } else {
                $zeroStockSkuCount++;
            }
        }
        
        // 计算周转率（简化计算）
        $turnoverRatio = $this->calculateTurnoverRatio();
        
        return [
            'total_inventory_value' => $totalValue,
            'total_inventory_quantity' => $totalQuantity,
            'total_sku_count' => $totalSkuCount,
            'active_sku_count' => $activeSkuCount,
            'zero_stock_sku_count' => $zeroStockSkuCount,
            'inventory_turnover_ratio' => $turnoverRatio,
            'average_inventory_days' => $turnoverRatio > 0 ? 365 / $turnoverRatio : null,
            'stock_accuracy_rate' => 0.95, // 假设值
            'stockout_rate' => $zeroStockSkuCount / max($totalSkuCount, 1),
        ];
    }

    /**
     * 生成图表数据
     * 
     * @param InventoryReportConfig $config
     * @param array $reportData
     * @return array
     */
    protected function generateChartsData($config, $reportData)
    {
        $charts = [];
        
        switch ($config->type) {
            case 'status':
                $charts['stock_status_pie'] = $this->generateStockStatusPieChart($reportData);
                $charts['value_by_warehouse'] = $this->generateValueByWarehouseChart($reportData);
                break;
                
            case 'movement':
                $charts['daily_movement'] = $this->generateDailyMovementChart($reportData);
                $charts['operation_types'] = $this->generateOperationTypesChart($reportData);
                break;
                
            case 'analysis':
                $charts['category_distribution'] = $this->generateCategoryDistributionChart($reportData);
                $charts['turnover_analysis'] = $this->generateTurnoverAnalysisChart($reportData);
                break;
                
            case 'forecast':
                $charts['forecast_trend'] = $this->generateForecastTrendChart($reportData);
                $charts['reorder_alerts'] = $this->generateReorderAlertsChart($reportData);
                break;
        }
        
        return $charts;
    }

    /**
     * 生成报表摘要
     * 
     * @param InventoryReportConfig $config
     * @param array $reportData
     * @return array
     */
    protected function generateSummary($config, $reportData)
    {
        switch ($config->type) {
            case 'status':
                return [
                    'total_items' => $reportData['summary']['total_items'],
                    'total_value' => $reportData['summary']['total_value'],
                    'alerts' => [
                        'low_stock' => $reportData['summary']['low_stock_items'],
                        'out_of_stock' => $reportData['summary']['out_of_stock_items'],
                    ],
                ];
                
            case 'movement':
                return [
                    'total_operations' => $reportData['summary']['total_operations'],
                    'total_in' => $reportData['summary']['total_in'],
                    'total_out' => $reportData['summary']['total_out'],
                    'net_change' => $reportData['summary']['total_in'] - $reportData['summary']['total_out'],
                ];
                
            case 'analysis':
                return [
                    'inventory_health' => $this->calculateInventoryHealth($reportData['metrics']),
                    'key_insights' => $this->generateKeyInsights($reportData),
                ];
                
            case 'forecast':
                return [
                    'total_alerts' => count($reportData['stock_alerts']),
                    'reorder_suggestions' => count($reportData['reorder_suggestions']),
                    'forecast_accuracy' => 0.85, // 假设值
                ];
                
            default:
                return [];
        }
    }

    /**
     * 生成报表标题
     * 
     * @param InventoryReportConfig $config
     * @param array $parameters
     * @return string
     */
    protected function generateReportTitle($config, $parameters)
    {
        $date = $parameters['report_date'] ?? now()->toDateString();
        return $config->name . ' - ' . $date;
    }

    /**
     * 获取批次摘要
     * 
     * @param \Illuminate\Database\Eloquent\Collection $batches
     * @return array
     */
    protected function getBatchSummary($batches)
    {
        $total = $batches->count();
        $expired = $batches->filter(fn($batch) => $batch->isExpired())->count();
        $nearExpiry = $batches->filter(fn($batch) => $batch->isNearExpiry())->count();
        
        return [
            'total_batches' => $total,
            'expired_batches' => $expired,
            'near_expiry_batches' => $nearExpiry,
        ];
    }

    /**
     * 获取库存状态
     * 
     * @param Inventory $inventory
     * @return string
     */
    protected function getStockStatus($inventory)
    {
        if ($inventory->stock <= 0) {
            return 'out';
        } elseif ($inventory->min_stock_level && $inventory->stock <= $inventory->min_stock_level) {
            return 'low';
        } else {
            return 'normal';
        }
    }

    /**
     * 计算周转率
     * 
     * @return float
     */
    protected function calculateTurnoverRatio()
    {
        // 这里简化计算，实际应该基于销售数据
        return 6.0; // 假设年周转率为6次
    }

    /**
     * 分类分析
     * 
     * @return array
     */
    protected function analyzeByCategoryAnalysis()
    {
        return DB::table('inventory as i')
            ->join('products as p', 'i.product_id', '=', 'p.id')
            ->leftJoin('categories as c', 'p.category_id', '=', 'c.id')
            ->select([
                DB::raw('COALESCE(c.name, "未分类") as category_name'),
                DB::raw('SUM(i.stock) as total_quantity'),
                DB::raw('SUM(i.stock * COALESCE(p.price, 0)) as total_value'),
                DB::raw('COUNT(*) as sku_count'),
            ])
            ->groupBy('c.id', 'c.name')
            ->get()
            ->toArray();
    }

    /**
     * 仓库分析
     * 
     * @return array
     */
    protected function analyzeByWarehouse()
    {
        return DB::table('inventory as i')
            ->join('warehouses as w', 'i.warehouse_id', '=', 'w.id')
            ->join('products as p', 'i.product_id', '=', 'p.id')
            ->select([
                'w.location as warehouse_name',
                DB::raw('SUM(i.stock) as total_quantity'),
                DB::raw('SUM(i.stock * COALESCE(p.price, 0)) as total_value'),
                DB::raw('COUNT(*) as sku_count'),
            ])
            ->groupBy('w.id', 'w.location')
            ->get()
            ->toArray();
    }

    /**
     * ABC分析
     * 
     * @return array
     */
    protected function performABCAnalysis()
    {
        // 简化的ABC分析
        $products = DB::table('inventory as i')
            ->join('products as p', 'i.product_id', '=', 'p.id')
            ->select([
                'p.id',
                'p.name',
                'p.code',
                DB::raw('SUM(i.stock * COALESCE(p.price, 0)) as value'),
            ])
            ->groupBy('p.id', 'p.name', 'p.code')
            ->orderBy('value', 'desc')
            ->get();
        
        $totalValue = $products->sum('value');
        $cumulativeValue = 0;
        $result = [];
        
        foreach ($products as $index => $product) {
            $cumulativeValue += $product->value;
            $percentage = $totalValue > 0 ? ($cumulativeValue / $totalValue) * 100 : 0;
            
            if ($percentage <= 80) {
                $category = 'A';
            } elseif ($percentage <= 95) {
                $category = 'B';
            } else {
                $category = 'C';
            }
            
            $result[] = [
                'product_name' => $product->name,
                'product_code' => $product->code,
                'value' => $product->value,
                'percentage' => round($percentage, 2),
                'category' => $category,
            ];
        }
        
        return $result;
    }

    /**
     * 周转率分析
     * 
     * @return array
     */
    protected function analyzeTurnover()
    {
        // 简化的周转率分析
        return [
            'fast_moving' => ['count' => 10, 'percentage' => 20],
            'medium_moving' => ['count' => 25, 'percentage' => 50],
            'slow_moving' => ['count' => 15, 'percentage' => 30],
        ];
    }

    /**
     * 获取历史销售数据
     * 
     * @param string $baseDate
     * @param int $days
     * @return array
     */
    protected function getHistoricalSalesData($baseDate, $days)
    {
        // 这里应该从订单系统获取实际销售数据
        // 简化处理，返回模拟数据
        return [
            1 => [10, 12, 8, 15, 11, 9, 13], // 产品1的7天销售数据
            2 => [5, 7, 6, 8, 4, 6, 7],      // 产品2的7天销售数据
        ];
    }

    /**
     * 计算线性预测
     * 
     * @param array $data
     * @param int $forecastDays
     * @return array
     */
    protected function calculateLinearForecast($data, $forecastDays)
    {
        $average = array_sum($data) / count($data);
        
        // 简单的线性预测
        $forecast = [];
        for ($i = 1; $i <= $forecastDays; $i++) {
            $forecast[] = round($average, 2);
        }
        
        return $forecast;
    }

    /**
     * 生成库存预警
     * 
     * @param array $forecasts
     * @return array
     */
    protected function generateStockAlerts($forecasts)
    {
        $alerts = [];
        
        foreach ($forecasts as $productId => $forecast) {
            $totalDemand = array_sum($forecast);
            $currentStock = Inventory::where('product_id', $productId)->sum('stock');
            
            if ($currentStock < $totalDemand) {
                $alerts[] = [
                    'product_id' => $productId,
                    'current_stock' => $currentStock,
                    'predicted_demand' => $totalDemand,
                    'shortage' => $totalDemand - $currentStock,
                    'alert_level' => 'high',
                ];
            }
        }
        
        return $alerts;
    }

    /**
     * 生成补货建议
     * 
     * @param array $forecasts
     * @return array
     */
    protected function generateReorderSuggestions($forecasts)
    {
        $suggestions = [];
        
        foreach ($forecasts as $productId => $forecast) {
            $totalDemand = array_sum($forecast);
            $currentStock = Inventory::where('product_id', $productId)->sum('stock');
            
            if ($currentStock < $totalDemand * 1.2) { // 20%安全库存
                $suggestions[] = [
                    'product_id' => $productId,
                    'current_stock' => $currentStock,
                    'suggested_order_quantity' => ceil($totalDemand * 1.5 - $currentStock),
                    'reason' => '预测需求超过当前库存',
                ];
            }
        }
        
        return $suggestions;
    }

    /**
     * 生成库存状态饼图数据
     * 
     * @param array $reportData
     * @return array
     */
    protected function generateStockStatusPieChart($reportData)
    {
        $statusCounts = [
            'normal' => 0,
            'low' => 0,
            'out' => 0,
        ];
        
        foreach ($reportData['items'] as $item) {
            $statusCounts[$item['stock_status']]++;
        }
        
        return [
            'labels' => ['正常', '低库存', '缺货'],
            'data' => array_values($statusCounts),
            'colors' => ['#28a745', '#ffc107', '#dc3545'],
        ];
    }

    /**
     * 生成仓库价值分布图数据
     * 
     * @param array $reportData
     * @return array
     */
    protected function generateValueByWarehouseChart($reportData)
    {
        $warehouseValues = [];
        
        foreach ($reportData['items'] as $item) {
            $warehouse = $item['warehouse_name'];
            if (!isset($warehouseValues[$warehouse])) {
                $warehouseValues[$warehouse] = 0;
            }
            $warehouseValues[$warehouse] += $item['total_value'];
        }
        
        return [
            'labels' => array_keys($warehouseValues),
            'data' => array_values($warehouseValues),
        ];
    }

    /**
     * 生成日变动图数据
     * 
     * @param array $reportData
     * @return array
     */
    protected function generateDailyMovementChart($reportData)
    {
        $dailyData = $reportData['daily_summary'];
        
        return [
            'labels' => array_column($dailyData, 'date'),
            'datasets' => [
                [
                    'label' => '入库',
                    'data' => array_column($dailyData, 'total_in'),
                    'backgroundColor' => '#28a745',
                ],
                [
                    'label' => '出库',
                    'data' => array_column($dailyData, 'total_out'),
                    'backgroundColor' => '#dc3545',
                ],
            ],
        ];
    }

    /**
     * 生成操作类型分布图数据
     * 
     * @param array $reportData
     * @return array
     */
    protected function generateOperationTypesChart($reportData)
    {
        $typeCounts = [];
        
        foreach ($reportData['operations'] as $operation) {
            $type = $operation->operation_type;
            if (!isset($typeCounts[$type])) {
                $typeCounts[$type] = 0;
            }
            $typeCounts[$type]++;
        }
        
        return [
            'labels' => array_keys($typeCounts),
            'data' => array_values($typeCounts),
        ];
    }

    /**
     * 生成分类分布图数据
     * 
     * @param array $reportData
     * @return array
     */
    protected function generateCategoryDistributionChart($reportData)
    {
        $categoryData = $reportData['category_analysis'];
        
        return [
            'labels' => array_column($categoryData, 'category_name'),
            'data' => array_column($categoryData, 'total_value'),
        ];
    }

    /**
     * 生成周转率分析图数据
     * 
     * @param array $reportData
     * @return array
     */
    protected function generateTurnoverAnalysisChart($reportData)
    {
        $turnoverData = $reportData['turnover_analysis'];
        
        return [
            'labels' => ['快速流动', '中速流动', '慢速流动'],
            'data' => [
                $turnoverData['fast_moving']['count'],
                $turnoverData['medium_moving']['count'],
                $turnoverData['slow_moving']['count'],
            ],
            'colors' => ['#28a745', '#ffc107', '#dc3545'],
        ];
    }

    /**
     * 生成预测趋势图数据
     * 
     * @param array $reportData
     * @return array
     */
    protected function generateForecastTrendChart($reportData)
    {
        // 简化处理，返回第一个产品的预测数据
        $firstForecast = reset($reportData['forecasts']);
        
        return [
            'labels' => range(1, count($firstForecast)),
            'data' => $firstForecast,
        ];
    }

    /**
     * 生成补货预警图数据
     * 
     * @param array $reportData
     * @return array
     */
    protected function generateReorderAlertsChart($reportData)
    {
        $alerts = $reportData['stock_alerts'];
        
        return [
            'labels' => array_column($alerts, 'product_id'),
            'data' => array_column($alerts, 'shortage'),
        ];
    }

    /**
     * 计算库存健康度
     * 
     * @param array $metrics
     * @return array
     */
    protected function calculateInventoryHealth($metrics)
    {
        $score = 0;
        $maxScore = 100;
        
        // 周转率评分 (40分)
        if (isset($metrics['inventory_turnover_ratio'])) {
            $turnoverScore = min(40, $metrics['inventory_turnover_ratio'] * 6.67);
            $score += $turnoverScore;
        }
        
        // 准确率评分 (30分)
        if (isset($metrics['stock_accuracy_rate'])) {
            $accuracyScore = $metrics['stock_accuracy_rate'] * 30;
            $score += $accuracyScore;
        }
        
        // 缺货率评分 (30分) - 缺货率越低分数越高
        if (isset($metrics['stockout_rate'])) {
            $stockoutScore = max(0, 30 - ($metrics['stockout_rate'] * 150));
            $score += $stockoutScore;
        }
        
        return [
            'score' => round($score, 1),
            'rating' => $this->getHealthRating($score),
            'max_score' => $maxScore,
        ];
    }

    /**
     * 获取健康度等级
     * 
     * @param float $score
     * @return string
     */
    protected function getHealthRating($score)
    {
        if ($score >= 90) {
            return '优秀';
        } elseif ($score >= 80) {
            return '良好';
        } elseif ($score >= 70) {
            return '中等';
        } elseif ($score >= 60) {
            return '及格';
        } else {
            return '需改进';
        }
    }

    /**
     * 生成关键洞察
     * 
     * @param array $reportData
     * @return array
     */
    protected function generateKeyInsights($reportData)
    {
        $insights = [];
        
        $metrics = $reportData['metrics'];
        
        // 库存周转分析
        if (isset($metrics['inventory_turnover_ratio'])) {
            if ($metrics['inventory_turnover_ratio'] < 3) {
                $insights[] = '库存周转率偏低，建议优化库存结构';
            } elseif ($metrics['inventory_turnover_ratio'] > 12) {
                $insights[] = '库存周转率很高，注意避免缺货风险';
            }
        }
        
        // 缺货分析
        if (isset($metrics['stockout_rate']) && $metrics['stockout_rate'] > 0.1) {
            $insights[] = '缺货率较高，建议增加安全库存';
        }
        
        // SKU活跃度分析
        if (isset($metrics['zero_stock_sku_count']) && $metrics['zero_stock_sku_count'] > 0) {
            $percentage = ($metrics['zero_stock_sku_count'] / $metrics['total_sku_count']) * 100;
            if ($percentage > 20) {
                $insights[] = "有{$percentage}%的SKU缺货，建议及时补货";
            }
        }
        
        return $insights;
    }

    /**
     * 保存分析指标
     * 
     * @param array $metrics
     * @param string $type
     * @param string $date
     * @return InventoryAnalysisMetric
     */
    public function saveAnalysisMetrics($metrics, $type = 'daily', $date = null)
    {
        $analysisDate = $date ?? now()->toDateString();
        
        return InventoryAnalysisMetric::updateOrCreate(
            [
                'analysis_date' => $analysisDate,
                'metric_type' => $type,
            ],
            $metrics
        );
    }

    /**
     * 获取报表列表
     * 
     * @param User $user
     * @param array $filters
     * @return array
     */
    public function getReportsList(User $user, $filters = [])
    {
        $query = InventoryReportConfig::accessibleBy($user)->active();
        
        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }
        
        $configs = $query->with(['latestInstance'])->get();
        
        return $configs->map(function($config) {
            return [
                'id' => $config->id,
                'name' => $config->name,
                'type' => $config->type,
                'type_text' => $config->type_text,
                'description' => $config->description,
                'last_generated' => $config->latestInstance ? $config->latestInstance->created_at : null,
                'can_generate' => true,
            ];
        })->toArray();
    }
} 