<?php

/**
 * 调试商品查询问题
 */

require_once 'vendor/autoload.php';

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Product\Models\Product;
use App\Product\Services\ProductService;
use Illuminate\Support\Facades\DB;

echo "🔍 商品查询调试脚本\n";
echo "==================\n\n";

try {
    // 1. 检查数据库连接
    echo "1. 检查数据库连接...\n";
    DB::connection()->getPdo();
    echo "✅ 数据库连接正常\n\n";

    // 2. 检查商品总数
    echo "2. 检查商品总数...\n";
    $totalProducts = Product::count();
    echo "✅ 商品总数: {$totalProducts}\n";
    
    $activeProducts = Product::where('status', 1)->count();
    echo "✅ 启用商品数: {$activeProducts}\n";
    
    $inactiveProducts = Product::where('status', 0)->count();
    echo "✅ 禁用商品数: {$inactiveProducts}\n\n";

    // 3. 测试基本分页查询
    echo "3. 测试基本分页查询...\n";
    
    $page1 = Product::paginate(20);
    echo "第1页 (20条/页): {$page1->count()} 条数据, 总数: {$page1->total()}, 总页数: {$page1->lastPage()}\n";
    
    $page2 = Product::paginate(10);
    echo "第1页 (10条/页): {$page2->count()} 条数据, 总数: {$page2->total()}, 总页数: {$page2->lastPage()}\n\n";

    // 4. 测试ProductService查询
    echo "4. 测试ProductService查询...\n";

    // 使用Laravel容器来解析依赖
    $productService = app(ProductService::class);
    
    // 模拟前端请求参数
    $filters = [
        'page' => 1,
        'keyword' => '',
        'category_id' => null,
        'status' => null,
        'stock_status' => '',
        'supplier' => '',
        'min_price' => null,
        'max_price' => null,
        'date_range' => [],
        'sort_field' => 'id',
        'sort_order' => 'desc',
        'with_units' => true
    ];
    
    echo "使用过滤条件: " . json_encode($filters, JSON_PRETTY_PRINT) . "\n";
    
    $result = $productService->getProducts($filters, 20);
    
    echo "ProductService结果:\n";
    echo "  - 当前页: {$result->currentPage()}\n";
    echo "  - 每页数量: {$result->perPage()}\n";
    echo "  - 总数: {$result->total()}\n";
    echo "  - 总页数: {$result->lastPage()}\n";
    echo "  - 当前页商品数: {$result->count()}\n\n";

    // 5. 测试不同的过滤条件
    echo "5. 测试不同的过滤条件...\n";
    
    $testCases = [
        ['name' => '无过滤条件', 'filters' => []],
        ['name' => '只有分页', 'filters' => ['page' => 1]],
        ['name' => '启用商品', 'filters' => ['status' => 1]],
        ['name' => '禁用商品', 'filters' => ['status' => 0]],
    ];
    
    foreach ($testCases as $testCase) {
        echo "测试: {$testCase['name']}\n";
        $testResult = $productService->getProducts($testCase['filters'], 20);
        echo "  结果: {$testResult->count()} 条数据, 总数: {$testResult->total()}\n";
    }
    
    echo "\n";

    // 6. 检查SQL查询
    echo "6. 检查SQL查询...\n";
    
    // 启用查询日志
    DB::enableQueryLog();
    
    $result = $productService->getProducts(['page' => 1], 20);
    
    $queries = DB::getQueryLog();
    echo "执行的SQL查询数量: " . count($queries) . "\n";
    
    foreach ($queries as $index => $query) {
        echo "查询 " . ($index + 1) . ": " . $query['query'] . "\n";
        if (!empty($query['bindings'])) {
            echo "  参数: " . json_encode($query['bindings']) . "\n";
        }
        echo "  耗时: {$query['time']}ms\n\n";
    }

    // 7. 检查是否有作用域或中间件影响
    echo "7. 检查模型作用域...\n";
    
    $productModel = new Product();
    $globalScopes = $productModel->getGlobalScopes();
    
    if (empty($globalScopes)) {
        echo "✅ 没有全局作用域\n";
    } else {
        echo "⚠️ 发现全局作用域:\n";
        foreach ($globalScopes as $scope) {
            echo "  - " . get_class($scope) . "\n";
        }
    }

    echo "\n✅ 调试完成！\n";

} catch (Exception $e) {
    echo "❌ 调试失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
