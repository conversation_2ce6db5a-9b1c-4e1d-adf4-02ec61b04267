<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * 修复配送记录表中的deliverer_id外键关系
     * 将外键引用从employees表改为deliverers表
     */
    public function up(): void
    {
        Schema::table('deliveries', function (Blueprint $table) {
            // 首先删除原有的外键约束
            if (Schema::hasColumn('deliveries', 'deliverer_id')) {
                $table->dropForeign('deliveries_deliverer_id_foreign');
            }
            
            // 迁移已有配送员的数据
            $this->migrateDelivererData();
            
            // 添加新的外键约束，指向deliverers表
            $table->foreign('deliverer_id')
                  ->references('id')
                  ->on('deliverers')
                  ->onDelete('set null');
        });
    }
    
    /**
     * 迁移配送记录中的配送员数据
     * 将直接引用员工ID的记录更新为引用deliverers表的记录
     */
    private function migrateDelivererData()
    {
        // 获取所有有deliverer_id的配送记录
        $deliveries = DB::table('deliveries')
                        ->whereNotNull('deliverer_id')
                        ->get();
        
        foreach ($deliveries as $delivery) {
            // 查找对应的employee_id在deliverers表中的记录
            $employeeId = $delivery->deliverer_id;
            $deliverer = DB::table('deliverers')
                           ->where('employee_id', $employeeId)
                           ->where('type', 'employee')
                           ->first();
            
            if ($deliverer) {
                // 如果找到对应的deliverer记录，更新delivery中的deliverer_id
                DB::table('deliveries')
                    ->where('id', $delivery->id)
                    ->update([
                        'deliverer_id' => $deliverer->id,
                        'updated_at' => now()
                    ]);
            } else {
                // 如果没有找到对应的deliverer记录，为这个员工创建一个deliverer记录
                $employee = DB::table('employees')->find($employeeId);
                if ($employee) {
                    $delivererId = DB::table('deliverers')->insertGetId([
                        'employee_id' => $employee->id,
                        'type' => 'employee',
                        'status' => 'available',
                        'max_orders' => 10,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                    
                    // 更新配送记录
                    DB::table('deliveries')
                        ->where('id', $delivery->id)
                        ->update([
                            'deliverer_id' => $delivererId,
                            'updated_at' => now()
                        ]);
                }
            }
        }
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::table('deliveries', function (Blueprint $table) {
            // 删除指向deliverers表的外键约束
            $table->dropForeign('deliveries_deliverer_id_foreign');
            
            // 恢复原来的外键约束，指向employees表
            $table->foreign('deliverer_id')
                  ->references('id')
                  ->on('employees')
                  ->onDelete('set null');
        });
        
        // 注意：此回滚不会恢复数据，因为那样会很复杂且可能导致数据错误
        // 如果需要完全回滚，建议从备份恢复
    }
};
