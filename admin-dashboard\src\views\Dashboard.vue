<template>
  <div class="dashboard">
    <!-- 待办事项区域 -->
    <div class="section">
      <div class="section-header">
        <h3 class="section-title">
          <span class="title-indicator"></span>
          待办事项
        </h3>
        <div class="section-actions">
          <el-button type="text" size="small">
            <el-icon><Refresh /></el-icon>
          </el-button>
          <el-button type="text" size="small">
            <el-icon><Menu /></el-icon>
          </el-button>
          <el-button type="text" size="small">
            <el-icon><Setting /></el-icon>
          </el-button>
        </div>
      </div>
      
      <!-- 统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-number">0</div>
          <div class="stat-label">待分拣</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">0</div>
          <div class="stat-label">待发货</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">0</div>
          <div class="stat-label">待售后</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">0</div>
          <div class="stat-label">待采购</div>
        </div>
        <div class="stat-card highlight">
          <div class="stat-number">475</div>
          <div class="stat-label">待审核用户</div>
        </div>
        <div class="stat-card highlight">
          <div class="stat-number">338</div>
          <div class="stat-label">待入库</div>
        </div>
        <div class="stat-card highlight">
          <div class="stat-number">116</div>
          <div class="stat-label">待出库</div>
        </div>
      </div>
      
      <!-- 功能图标区域 -->
      <div class="function-grid">
        <div class="function-item">
          <div class="function-icon green">📦</div>
          <div class="function-label">代客下单</div>
        </div>
        <div class="function-item">
          <div class="function-icon green">🛒</div>
          <div class="function-label">计划采购</div>
        </div>
        <div class="function-item">
          <div class="function-icon green">📋</div>
          <div class="function-label">商品列表</div>
        </div>
        <div class="function-item">
          <div class="function-icon green">🏪</div>
          <div class="function-label">库房管理</div>
        </div>
        <div class="function-item">
          <div class="function-icon green">🚚</div>
          <div class="function-label">配送排线</div>
        </div>
        <div class="function-item">
          <div class="function-icon green">📧</div>
          <div class="function-label">发票申核</div>
        </div>
        <div class="function-item">
          <div class="function-icon green">💰</div>
          <div class="function-label">收银订单</div>
        </div>
      </div>
    </div>

    <!-- 今日数据区域 -->
    <div class="section">
      <div class="section-header">
        <h3 class="section-title">
          <span class="title-indicator"></span>
          今日数据
        </h3>
        <div class="section-actions">
          <el-button type="text" size="small">
            <el-icon><Menu /></el-icon>
          </el-button>
          <el-button type="text" size="small">
            <el-icon><Setting /></el-icon>
          </el-button>
        </div>
      </div>
      
      <!-- 今日数据卡片 -->
      <div class="today-stats">
        <div class="today-card green">
          <div class="today-icon">💰</div>
          <div class="today-content">
            <div class="today-title">新增订单(笔)</div>
            <div class="today-number">0</div>
            <div class="today-subtitle">昨日: 2</div>
          </div>
        </div>
        <div class="today-card blue">
          <div class="today-icon">💳</div>
          <div class="today-content">
            <div class="today-title">下单金额(元)</div>
            <div class="today-number">0.00</div>
            <div class="today-subtitle">昨日: 172.85</div>
          </div>
        </div>
        <div class="today-card yellow">
          <div class="today-icon">👥</div>
          <div class="today-content">
            <div class="today-title">客单价(元)</div>
            <div class="today-number">0.00</div>
            <div class="today-subtitle">昨日: 86.42</div>
          </div>
        </div>
        <div class="today-card pink">
          <div class="today-icon">📋</div>
          <div class="today-content">
            <div class="today-title">未支付订单(笔)</div>
            <div class="today-number">0</div>
            <div class="today-subtitle">昨日: 0</div>
          </div>
        </div>
        <div class="today-card cyan">
          <div class="today-icon">👤</div>
          <div class="today-content">
            <div class="today-title">新增客户(人)</div>
            <div class="today-number">0</div>
            <div class="today-subtitle">昨日: 3</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部区域 -->
    <div class="bottom-section">
      <!-- 支付统计 -->
      <div class="chart-section">
        <div class="section-header">
          <h3 class="section-title">
            <span class="title-indicator"></span>
            支付统计
          </h3>
        </div>
        <div class="chart-placeholder">
          <div class="chart-content">
            <div class="chart-info">
              <div class="chart-label">支付单数</div>
              <div class="chart-value green">0%</div>
              <div class="chart-number">0 单</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分类统计 -->
      <div class="chart-section">
        <div class="section-header">
          <h3 class="section-title">
            <span class="title-indicator"></span>
            分类统计
          </h3>
          <div class="chart-tabs">
            <span class="chart-tab active">近一月</span>
            <span class="chart-tab">全部</span>
            <span class="chart-tab">全部</span>
          </div>
        </div>
        <div class="chart-placeholder">
          <div class="pie-chart">
            <!-- 这里可以放置饼图组件 -->
            <div class="pie-placeholder">饼图区域</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧信息栏 -->
    <div class="info-sidebar">
      <!-- 消息通知 -->
      <div class="info-section">
        <div class="info-header">
          <h4>消息通知</h4>
          <span class="more-link">更多 ></span>
        </div>
        <div class="message-list">
          <div class="message-item">
            <div class="message-text">[商户注册]新商户"注册成功...</div>
            <div class="message-time">05-22</div>
          </div>
          <div class="message-item">
            <div class="message-text">[商户注册]新商户"胖哥雅江...</div>
            <div class="message-time">05-21</div>
          </div>
          <div class="message-item">
            <div class="message-text">[商户注册]新商户"众汇"注册...</div>
            <div class="message-time">05-20</div>
          </div>
          <div class="message-item">
            <div class="message-text">[商户注册]新商户"二姐豆腐坊...</div>
            <div class="message-time">04-18</div>
          </div>
        </div>
      </div>

      <!-- 帮助中心 -->
      <div class="info-section">
        <div class="info-header">
          <h4>帮助中心</h4>
        </div>
        <div class="help-content">
          <div class="help-avatar">?</div>
          <div class="help-text">
            <div class="help-title">视频中心</div>
            <div class="help-subtitle">全面了解产品功能</div>
          </div>
        </div>
        <div class="contact-info">
          <div class="contact-phone">************</div>
          <div class="contact-time">7×24小时服务电话</div>
        </div>
      </div>

      <!-- 预警信息 -->
      <div class="info-section">
        <div class="info-header">
          <h4>预警信息</h4>
        </div>
        <div class="warning-grid">
          <div class="warning-item">
            <div class="warning-number">6</div>
            <div class="warning-label">库存不足</div>
          </div>
          <div class="warning-item">
            <div class="warning-number">5</div>
            <div class="warning-label">异常地址</div>
          </div>
          <div class="warning-item">
            <div class="warning-number">48</div>
            <div class="warning-label">采购待结算</div>
          </div>
          <div class="warning-item">
            <div class="warning-number">8</div>
            <div class="warning-label">临期产品</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Refresh, Menu, Setting } from '@element-plus/icons-vue'
</script>

<style scoped>
.dashboard {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 20px;
  padding: 0;
}

/* 左侧主要内容区域 */
.section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.title-indicator {
  width: 4px;
  height: 16px;
  background: #28a745;
  border-radius: 2px;
  margin-right: 8px;
}

.section-actions {
  display: flex;
  gap: 8px;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 15px;
  margin-bottom: 30px;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px 15px;
  text-align: center;
  transition: all 0.3s;
  border: 1px solid #e9ecef;
}

.stat-card.highlight {
  background: #28a745;
  color: white;
  border: 1px solid #28a745;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

/* 功能图标网格 */
.function-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 20px;
}

.function-item {
  text-align: center;
}

.function-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin: 0 auto 8px;
  background: #28a745;
}

.function-label {
  font-size: 12px;
  color: #666;
}

/* 今日数据卡片 */
.today-stats {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
}

.today-card {
  border-radius: 12px;
  padding: 20px;
  color: white;
  display: flex;
  align-items: center;
  gap: 15px;
}

.today-card.green { 
  background: linear-gradient(135deg, #4CAF50, #45a049);
}
.today-card.blue { 
  background: linear-gradient(135deg, #2196F3, #1976D2);
}
.today-card.yellow { 
  background: linear-gradient(135deg, #FF9800, #F57C00);
}
.today-card.pink { 
  background: linear-gradient(135deg, #E91E63, #C2185B);
}
.today-card.cyan { 
  background: linear-gradient(135deg, #00BCD4, #0097A7);
}

.today-icon {
  font-size: 32px;
}

.today-content {
  flex: 1;
}

.today-title {
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.today-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.today-subtitle {
  font-size: 11px;
  opacity: 0.7;
}

/* 底部图表区域 */
.bottom-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-tabs {
  display: flex;
  gap: 15px;
}

.chart-tab {
  font-size: 12px;
  color: #999;
  cursor: pointer;
}

.chart-tab.active {
  color: #28a745;
  font-weight: 600;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 15px;
}

.chart-content {
  text-align: center;
}

.chart-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.chart-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.chart-value.green {
  color: #28a745;
}

.chart-number {
  font-size: 18px;
  color: #333;
}

/* 右侧信息栏 */
.info-sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.info-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.more-link {
  font-size: 12px;
  color: #28a745;
  cursor: pointer;
}

/* 消息列表 */
.message-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.message-text {
  font-size: 12px;
  color: #666;
  flex: 1;
}

.message-time {
  font-size: 11px;
  color: #999;
}

/* 帮助中心 */
.help-content {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.help-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #28a745;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.help-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.help-subtitle {
  font-size: 12px;
  color: #666;
}

.contact-info {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.contact-phone {
  font-size: 16px;
  font-weight: bold;
  color: #28a745;
  margin-bottom: 4px;
}

.contact-time {
  font-size: 11px;
  color: #666;
}

/* 预警信息网格 */
.warning-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.warning-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.warning-number {
  font-size: 20px;
  font-weight: bold;
  color: #28a745;
  margin-bottom: 5px;
}

.warning-label {
  font-size: 11px;
  color: #666;
}
</style> 