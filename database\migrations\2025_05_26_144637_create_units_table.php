<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('units')) {
            Schema::create('units', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('display_name')->nullable();
                $table->string('symbol', 10)->unique();
                $table->string('type')->default('general');
                $table->boolean('is_base_unit')->default(false);
                $table->boolean('is_visible')->default(true);
                $table->text('description')->nullable();
                $table->integer('sort')->default(0);
                $table->json('properties')->nullable();
                $table->foreignId('base_unit_id')->nullable()->constrained('units')->onDelete('set null');
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('units');
    }
}; 