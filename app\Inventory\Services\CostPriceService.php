<?php

namespace App\Inventory\Services;

use App\Inventory\Models\Inventory;
use App\Inventory\Models\InventoryTransaction;
use App\Product\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CostPriceService
{
    /**
     * 计算移动加权平均成本价（ERP标准方法）
     * 
     * @param int $inventoryId 库存ID
     * @param bool $useCache 是否使用缓存
     * @return float|null 移动加权平均成本价
     */
    public function calculateMovingAverageCostPrice(int $inventoryId, bool $useCache = true): ?float
    {
        $cacheKey = "inventory_moving_avg_cost_{$inventoryId}";
        
        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }
        
        $inventory = Inventory::find($inventoryId);
        if (!$inventory) {
            return null;
        }
        
        $costPrice = $this->calculateMovingWeightedAverage(
            $inventory->product_id,
            $inventory->warehouse_id
        );
        
        if ($useCache && $costPrice !== null) {
            // 缓存1小时
            Cache::put($cacheKey, $costPrice, 3600);
        }
        
        return $costPrice;
    }
    
    /**
     * 计算移动加权平均成本（核心算法）
     * 
     * 移动加权平均法：每次入库后重新计算平均成本
     * 公式：新平均成本 = (原库存金额 + 新入库金额) / (原库存数量 + 新入库数量)
     * 
     * @param int $productId 商品ID
     * @param int $warehouseId 仓库ID
     * @return float|null
     */
    public function calculateMovingWeightedAverage(int $productId, int $warehouseId): ?float
    {
        // 获取所有入库事务，按时间顺序排列
        $transactions = InventoryTransaction::where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->whereHas('transactionType', function($query) {
                // 只考虑入库事务
                $query->where('effect_direction', '>', 0);
            })
            ->whereNotNull('unit_price')
            ->where('unit_price', '>', 0)
            ->orderBy('created_at', 'asc')
            ->select('id', 'quantity', 'unit_price', 'unit_id', 'created_at')
            ->get();
            
        if ($transactions->isEmpty()) {
            return null;
        }
        
        $runningQuantity = 0;  // 累计库存数量
        $runningValue = 0;     // 累计库存金额
        $currentAvgCost = 0;   // 当前平均成本
        
        foreach ($transactions as $transaction) {
            // 将数量转换为基础单位
            $baseQuantity = $this->convertToBaseUnit(
                $transaction->quantity,
                $transaction->unit_id,
                $productId
            );
            
            if ($baseQuantity > 0) {
                // 计算本次入库的总金额
                $transactionValue = $baseQuantity * $transaction->unit_price;
                
                // 更新累计数量和金额
                $runningQuantity += $baseQuantity;
                $runningValue += $transactionValue;
                
                // 计算新的移动平均成本
                $currentAvgCost = $runningValue / $runningQuantity;
                
                Log::debug("移动平均成本计算", [
                    'transaction_id' => $transaction->id,
                    'quantity' => $baseQuantity,
                    'unit_price' => $transaction->unit_price,
                    'running_quantity' => $runningQuantity,
                    'running_value' => $runningValue,
                    'avg_cost' => $currentAvgCost
                ]);
            }
        }
        
        return $runningQuantity > 0 ? round($currentAvgCost, 4) : null;
    }
    
    /**
     * 计算标准成本价（基于最近采购价格）
     * 
     * @param int $productId 商品ID
     * @param int $warehouseId 仓库ID
     * @return float|null
     */
    public function calculateStandardCostPrice(int $productId, int $warehouseId): ?float
    {
        // 获取最近的采购入库价格
        $latestTransaction = InventoryTransaction::where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->whereHas('transactionType', function($query) {
                $query->where('code', 'purchase_in');
            })
            ->whereNotNull('unit_price')
            ->where('unit_price', '>', 0)
            ->orderBy('created_at', 'desc')
            ->first();
            
        if (!$latestTransaction) {
            return null;
        }
        
        return $latestTransaction->unit_price;
    }
    
    /**
     * 计算FIFO成本价（先进先出）
     * 
     * @param int $productId 商品ID
     * @param int $warehouseId 仓库ID
     * @return float|null
     */
    public function calculateFIFOCostPrice(int $productId, int $warehouseId): ?float
    {
        $inventory = Inventory::where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->first();
            
        if (!$inventory || $inventory->stock <= 0) {
            return null;
        }
        
        // 获取所有入库事务（按时间正序）
        $inTransactions = InventoryTransaction::where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->whereHas('transactionType', function($query) {
                $query->where('effect_direction', '>', 0);
            })
            ->whereNotNull('unit_price')
            ->where('unit_price', '>', 0)
            ->orderBy('created_at', 'asc')
            ->get();
        
        // 获取所有出库事务（按时间正序）
        $outTransactions = InventoryTransaction::where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->whereHas('transactionType', function($query) {
                $query->where('effect_direction', '<', 0);
            })
            ->orderBy('created_at', 'asc')
            ->get();
        
        // 计算当前库存的成本层
        $costLayers = [];
        $totalOutQuantity = 0;
        
        // 累计出库数量
        foreach ($outTransactions as $outTransaction) {
            $baseQuantity = $this->convertToBaseUnit(
                abs($outTransaction->quantity),
                $outTransaction->unit_id,
                $productId
            );
            $totalOutQuantity += $baseQuantity;
        }
        
        // 从最早的入库开始，减去已出库的数量
        $remainingOutQty = $totalOutQuantity;
        $totalCostValue = 0;
        $totalRemainingQty = 0;
        
        foreach ($inTransactions as $inTransaction) {
            $baseQuantity = $this->convertToBaseUnit(
                $inTransaction->quantity,
                $inTransaction->unit_id,
                $productId
            );
            
            if ($remainingOutQty >= $baseQuantity) {
                // 这批货已全部出库
                $remainingOutQty -= $baseQuantity;
            } else {
                // 这批货还有剩余
                $remainingQty = $baseQuantity - $remainingOutQty;
                $costLayers[] = [
                    'quantity' => $remainingQty,
                    'unit_price' => $inTransaction->unit_price
                ];
                $totalCostValue += $remainingQty * $inTransaction->unit_price;
                $totalRemainingQty += $remainingQty;
                $remainingOutQty = 0;
            }
        }
        
        return $totalRemainingQty > 0 ? round($totalCostValue / $totalRemainingQty, 4) : null;
    }
    
    /**
     * 获取商品的成本价信息（包含多种计算方法）
     * 
     * @param int $inventoryId 库存ID
     * @return array
     */
    public function getCostPriceInfo(int $inventoryId): array
    {
        $inventory = Inventory::find($inventoryId);
        if (!$inventory) {
            return [];
        }
        
        $movingAvgCost = $this->calculateMovingAverageCostPrice($inventoryId);
        $standardCost = $this->calculateStandardCostPrice($inventory->product_id, $inventory->warehouse_id);
        $fifoCost = $this->calculateFIFOCostPrice($inventory->product_id, $inventory->warehouse_id);
        
        // 获取最后入库和出库时间
        $lastInTime = InventoryTransaction::where('product_id', $inventory->product_id)
            ->where('warehouse_id', $inventory->warehouse_id)
            ->whereHas('transactionType', function($query) {
                $query->where('effect_direction', '>', 0);
            })
            ->orderBy('created_at', 'desc')
            ->value('created_at');
            
        $lastOutTime = InventoryTransaction::where('product_id', $inventory->product_id)
            ->where('warehouse_id', $inventory->warehouse_id)
            ->whereHas('transactionType', function($query) {
                $query->where('effect_direction', '<', 0);
            })
            ->orderBy('created_at', 'desc')
            ->value('created_at');
        
        return [
            'moving_average_cost' => $movingAvgCost,
            'standard_cost' => $standardCost,
            'fifo_cost' => $fifoCost,
            'last_in_time' => $lastInTime,
            'last_out_time' => $lastOutTime,
            'current_stock' => $inventory->stock,
            'calculation_time' => now(),
            'cost_method' => 'moving_average' // 默认使用移动加权平均
        ];
    }
    
    /**
     * 刷新库存成本价缓存
     * 
     * @param int $inventoryId 库存ID
     * @return array 刷新后的成本价信息
     */
    public function refreshCostPriceCache(int $inventoryId): array
    {
        $this->clearCostPriceCache($inventoryId);
        return $this->getCostPriceInfo($inventoryId);
    }
    
    /**
     * 清除指定库存的成本价缓存
     * 
     * @param int $inventoryId 库存ID
     * @return void
     */
    public function clearCostPriceCache(int $inventoryId): void
    {
        Cache::forget("inventory_moving_avg_cost_{$inventoryId}");
        Cache::forget("inventory_standard_cost_{$inventoryId}");
        Cache::forget("inventory_fifo_cost_{$inventoryId}");
    }
    
    /**
     * 批量清除成本价缓存
     * 
     * @param array $inventoryIds 库存ID数组
     * @return void
     */
    public function clearBatchCostPriceCache(array $inventoryIds): void
    {
        foreach ($inventoryIds as $inventoryId) {
            $this->clearCostPriceCache($inventoryId);
        }
    }
    
    /**
     * 当有新的库存事务时，自动清除相关缓存
     * 
     * @param int $productId 商品ID
     * @param int $warehouseId 仓库ID
     * @return void
     */
    public function clearCostPriceCacheForProduct(int $productId, int $warehouseId): void
    {
        $inventory = Inventory::where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->first();
            
        if ($inventory) {
            $this->clearCostPriceCache($inventory->id);
        }
    }
    
    /**
     * 将数量转换为基础单位
     * 
     * @param float $quantity 数量
     * @param int $unitId 单位ID
     * @param int $productId 商品ID
     * @return float 基础单位数量
     */
    private function convertToBaseUnit(float $quantity, int $unitId, int $productId): float
    {
        // 这里需要根据实际的单位转换逻辑来实现
        // 如果是基础单位，直接返回
        // 如果是其他单位，需要根据换算关系转换
        
        $product = Product::find($productId);
        if (!$product || !$product->base_unit_id) {
            return $quantity;
        }
        
        // 如果就是基础单位
        if ($unitId == $product->base_unit_id) {
            return $quantity;
        }
        
        // 查找单位换算关系
        $unitConversion = DB::table('product_unit_conversions')
            ->where('product_id', $productId)
            ->where('from_unit_id', $unitId)
            ->where('to_unit_id', $product->base_unit_id)
            ->first();
            
        if ($unitConversion) {
            return $quantity * $unitConversion->conversion_factor;
        }
        
        // 如果没有找到换算关系，返回原数量
        return $quantity;
    }
    
    /**
     * 当入库事务完成时，更新商品成本价
     * 
     * @param InventoryTransaction $transaction 库存事务
     * @return void
     */
    public function updateCostPriceOnInbound(InventoryTransaction $transaction): void
    {
        // 检查是否为入库事务且有价格信息
        if (!$this->shouldUpdateCostPrice($transaction)) {
            return;
        }

        try {
            DB::beginTransaction();
            
            $product = Product::lockForUpdate()->find($transaction->product_id);
            if (!$product) {
                Log::warning('商品不存在，无法更新成本价', [
                    'product_id' => $transaction->product_id,
                    'transaction_id' => $transaction->id
                ]);
                return;
            }

            // 计算新的移动加权平均成本价
            $newCostPrice = $this->calculateNewMovingAverageCost($product, $transaction);
            
            if ($newCostPrice !== null) {
                $oldCostPrice = $product->cost_price;
                $product->cost_price = $newCostPrice;
                $product->save();
                
                Log::info('商品成本价已更新', [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'old_cost_price' => $oldCostPrice,
                    'new_cost_price' => $newCostPrice,
                    'transaction_id' => $transaction->id,
                    'transaction_price' => $transaction->unit_price,
                    'transaction_quantity' => $transaction->quantity
                ]);
            }
            
            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('更新商品成本价失败', [
                'product_id' => $transaction->product_id,
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 检查是否应该更新成本价
     * 
     * @param InventoryTransaction $transaction
     * @return bool
     */
    private function shouldUpdateCostPrice(InventoryTransaction $transaction): bool
    {
        // 必须是已完成的事务
        if ($transaction->status !== 'completed') {
            return false;
        }

        // 必须有价格信息
        if (!$transaction->unit_price || $transaction->unit_price <= 0) {
            return false;
        }

        // 必须是入库事务（数量大于0）
        if ($transaction->quantity <= 0) {
            return false;
        }

                         // 检查事务类型
        $transaction->load('transactionType');
        if (!$transaction->transactionType) {
            return false;
        }

        // 允许更新成本价的事务类型：采购入库、库存调整（入库方向）
        $allowedTypes = ['purchase_in', 'inventory_adjustment'];
        return in_array($transaction->transactionType->code, $allowedTypes);
    }

    /**
     * 计算新的移动加权平均成本价
     * 
     * @param Product $product
     * @param InventoryTransaction $newTransaction
     * @return float|null
     */
    private function calculateNewMovingAverageCost(Product $product, InventoryTransaction $newTransaction): ?float
    {
        // 获取商品当前总库存（所有仓库）
        $currentTotalStock = $product->inventories()->sum('stock');
        
        // 当前库存价值 = 当前库存数量 × 当前成本价
        $currentStockValue = $currentTotalStock * ($product->cost_price ?? 0);
        
        // 新入库的价值
        $newInboundValue = $newTransaction->quantity * $newTransaction->unit_price;
        
        // 新的总库存数量
        $newTotalStock = $currentTotalStock + $newTransaction->quantity;
        
        // 如果新的总库存为0，无法计算成本价
        if ($newTotalStock <= 0) {
            return null;
        }
        
        // 计算新的移动加权平均成本价
        $newCostPrice = ($currentStockValue + $newInboundValue) / $newTotalStock;
        
        Log::debug('移动加权平均成本价计算', [
            'product_id' => $product->id,
            'current_stock' => $currentTotalStock,
            'current_cost_price' => $product->cost_price,
            'current_stock_value' => $currentStockValue,
            'new_quantity' => $newTransaction->quantity,
            'new_unit_price' => $newTransaction->unit_price,
            'new_inbound_value' => $newInboundValue,
            'new_total_stock' => $newTotalStock,
            'calculated_cost_price' => $newCostPrice
        ]);
        
        return round($newCostPrice, 4);
    }

    /**
     * 手动重新计算并更新商品成本价
     * 
     * @param int $productId
     * @return float|null
     */
    public function recalculateProductCostPrice(int $productId): ?float
    {
        try {
            DB::beginTransaction();
            
            $product = Product::lockForUpdate()->find($productId);
            if (!$product) {
                return null;
            }

                         // 获取所有已完成的采购入库事务（按时间顺序）
             $inboundTransactions = InventoryTransaction::where('product_id', $productId)
                 ->where('status', 'completed')
                 ->where('quantity', '>', 0)
                 ->whereNotNull('unit_price')
                 ->where('unit_price', '>', 0)
                 ->whereHas('transactionType', function($query) {
                     $query->where('code', 'purchase_in');
                 })
                 ->orderBy('created_at', 'asc')
                 ->get();

                         if ($inboundTransactions->isEmpty()) {
                 Log::info('商品没有有效的采购入库事务，无法计算成本价', [
                     'product_id' => $productId
                 ]);
                 return null;
             }

            // 模拟移动加权平均计算过程
            $runningStock = 0;
            $runningValue = 0;
            $finalCostPrice = 0;

            foreach ($inboundTransactions as $transaction) {
                $runningStock += $transaction->quantity;
                $runningValue += $transaction->quantity * $transaction->unit_price;
                $finalCostPrice = $runningValue / $runningStock;
            }

            // 更新商品成本价
            $oldCostPrice = $product->cost_price;
            $product->cost_price = round($finalCostPrice, 4);
            $product->save();

            Log::info('商品成本价重新计算完成', [
                'product_id' => $productId,
                'product_name' => $product->name,
                'old_cost_price' => $oldCostPrice,
                'new_cost_price' => $product->cost_price,
                'transaction_count' => $inboundTransactions->count(),
                'total_stock_processed' => $runningStock,
                'total_value_processed' => $runningValue
            ]);

            DB::commit();
            
            return $product->cost_price;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('重新计算商品成本价失败', [
                'product_id' => $productId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 批量重新计算所有商品的成本价
     * 
     * @return array 处理结果统计
     */
    public function recalculateAllProductsCostPrice(): array
    {
        $stats = [
            'total_products' => 0,
            'updated_products' => 0,
            'skipped_products' => 0,
            'failed_products' => 0,
            'errors' => []
        ];

        $products = Product::all();
        $stats['total_products'] = $products->count();

        foreach ($products as $product) {
            try {
                $newCostPrice = $this->recalculateProductCostPrice($product->id);
                
                if ($newCostPrice !== null) {
                    $stats['updated_products']++;
                } else {
                    $stats['skipped_products']++;
                }
                
            } catch (\Exception $e) {
                $stats['failed_products']++;
                $stats['errors'][] = [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'error' => $e->getMessage()
                ];
                
                Log::error('批量更新成本价时失败', [
                    'product_id' => $product->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('批量重新计算成本价完成', $stats);
        
        return $stats;
    }

    /**
     * 获取商品的成本价信息（用于API返回）
     * 
     * @param int $productId
     * @return array
     */
    public function getProductCostPriceInfo(int $productId): array
    {
        $product = Product::find($productId);
        if (!$product) {
            return [];
        }

                 // 获取最近的采购入库事务
         $latestInbound = InventoryTransaction::where('product_id', $productId)
             ->where('status', 'completed')
             ->where('quantity', '>', 0)
             ->whereNotNull('unit_price')
             ->where('unit_price', '>', 0)
             ->whereHas('transactionType', function($query) {
                 $query->where('code', 'purchase_in');
             })
             ->orderBy('created_at', 'desc')
             ->first();

         // 统计采购入库事务数量
         $inboundCount = InventoryTransaction::where('product_id', $productId)
             ->where('status', 'completed')
             ->where('quantity', '>', 0)
             ->whereNotNull('unit_price')
             ->where('unit_price', '>', 0)
             ->whereHas('transactionType', function($query) {
                 $query->where('code', 'purchase_in');
             })
             ->count();

        return [
            'product_id' => $productId,
            'product_name' => $product->name,
            'current_cost_price' => $product->cost_price,
            'calculation_method' => 'moving_average',
            'last_updated' => $product->updated_at,
            'latest_inbound_price' => $latestInbound ? $latestInbound->unit_price : null,
            'latest_inbound_time' => $latestInbound ? $latestInbound->created_at : null,
            'inbound_transaction_count' => $inboundCount,
            'total_stock' => $product->inventories()->sum('stock')
        ];
    }
} 