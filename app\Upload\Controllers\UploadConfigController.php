<?php

namespace App\Upload\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\UploadConfig;
use App\Upload\Services\UploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class UploadConfigController extends Controller
{
    /**
     * 获取上传配置
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getConfig()
    {
        try {
            // 记录请求信息
            Log::info('正在获取上传配置', [
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);
            
            // 获取基本配置
            $basicConfigs = UploadConfig::byGroup('upload_basic')->get();
            
            // 记录查询结果
            Log::info('上传基本配置查询结果', [
                'count' => $basicConfigs->count(),
                'items' => $basicConfigs->toArray()
            ]);
            
            $basicConfig = [
                'upload_driver' => 'local',
                'image_max_size' => 5120,
                'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif'],
            ];
            
            // 将数据库中的配置应用到默认配置上
            foreach ($basicConfigs as $config) {
                if ($config->key === 'allowed_file_types') {
                    $basicConfig[$config->key] = $config->value;
                } else {
                    $basicConfig[$config->key] = $config->value;
                }
            }
            
            // 获取COS配置
            $cosConfigs = UploadConfig::byGroup('upload_cos')->get();
            
            // 记录查询结果
            Log::info('COS配置查询结果', [
                'count' => $cosConfigs->count(),
                'items' => $cosConfigs->toArray()
            ]);
            
            $cosConfig = [
                'cos_app_id' => '',
                'cos_secret_id' => '',
                'cos_secret_key' => '',
                'cos_region' => '',
                'cos_bucket' => '',
                'cos_cdn' => '',
            ];
            
            // 将数据库中的配置应用到默认配置上
            foreach ($cosConfigs as $config) {
                $cosConfig[$config->key] = $config->value;
            }
            
            $responseData = [
                'basic' => $basicConfig,
                'cos' => $cosConfig,
            ];
            
            // 记录响应数据
            Log::info('上传配置响应数据', [
                'data' => $responseData
            ]);
            
            return response()->json(ApiResponse::success($responseData));
        } catch (\Exception $e) {
            Log::error('获取上传配置失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json(ApiResponse::error('获取上传配置失败'));
        }
    }
    
    /**
     * 保存上传配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveConfig(Request $request)
    {
        try {
            // 验证基本配置
            $validator = Validator::make($request->input('basic', []), [
                'upload_driver' => 'required|in:local,cos',
                'image_max_size' => 'required|integer|min:500|max:10240',
                'allowed_file_types' => 'required|array|min:1',
                'allowed_file_types.*' => 'in:jpg,jpeg,png,gif,webp',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first()), 422);
            }
            
            // 如果是COS驱动，验证COS配置
            if ($request->input('basic.upload_driver') === 'cos') {
                $cosValidator = Validator::make($request->input('cos', []), [
                    'cos_app_id' => 'required|string',
                    'cos_secret_id' => 'required|string',
                    'cos_secret_key' => 'required|string',
                    'cos_region' => 'required|string',
                    'cos_bucket' => 'required|string',
                    'cos_cdn' => 'nullable|string',
                ]);
                
                if ($cosValidator->fails()) {
                    return response()->json(ApiResponse::error($cosValidator->errors()->first()), 422);
                }
                
                // 确保存储桶名称格式正确
                $cosData = $request->input('cos');
                $appId = $cosData['cos_app_id'];
                $bucket = $cosData['cos_bucket'];
                
                // 不再自动添加APPID，使用用户指定的完整存储桶名称
                Log::info('使用用户指定的存储桶名称', [
                    'bucket' => $bucket, 
                    'app_id' => $appId
                ]);
            }
            
            // 保存基本配置到数据库
            $basicData = $request->input('basic');
            foreach ($basicData as $key => $value) {
                // 对于数组类型，使用json存储
                $type = is_array($value) ? 'json' : null;
                $description = match($key) {
                    'upload_driver' => '上传存储驱动',
                    'image_max_size' => '图片最大尺寸(KB)',
                    'allowed_file_types' => '允许的文件类型',
                    default => null
                };
                
                UploadConfig::setValue($key, $value, 'upload_basic', $type, $description);
            }
            
            // 如果是COS驱动，保存COS配置到数据库
            if ($basicData['upload_driver'] === 'cos') {
                $cosData = $request->input('cos');
                foreach ($cosData as $key => $value) {
                    $description = match($key) {
                        'cos_app_id' => 'COS APP ID',
                        'cos_secret_id' => 'COS Secret ID',
                        'cos_secret_key' => 'COS Secret Key',
                        'cos_region' => 'COS 区域',
                        'cos_bucket' => 'COS 存储桶名称',
                        'cos_cdn' => 'COS CDN加速域名',
                        default => null
                    };
                    
                    UploadConfig::setValue($key, $value, 'upload_cos', null, $description);
                }
            }
            
            // 同步更新.env文件中的配置(为了兼容性)
            $this->updateEnvFile([
                'UPLOAD_DRIVER' => $basicData['upload_driver'],
            ]);
            
            // 如果是COS，更新腾讯云存储配置(为了兼容性)
            if ($basicData['upload_driver'] === 'cos') {
                $cosData = $request->input('cos');
                $this->updateEnvFile([
                    'COS_APP_ID' => $cosData['cos_app_id'],
                    'COS_SECRET_ID' => $cosData['cos_secret_id'],
                    'COS_SECRET_KEY' => $cosData['cos_secret_key'],
                    'COS_REGION' => $cosData['cos_region'],
                    'COS_BUCKET' => $cosData['cos_bucket'],
                    'COS_CDN' => $cosData['cos_cdn'] ?? '',
                ]);
            }
            
            // 清除配置缓存
            $this->clearConfigCache();
            
            return response()->json(ApiResponse::success(null, '保存配置成功'));
        } catch (\Exception $e) {
            Log::error('保存上传配置失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);
            
            return response()->json(ApiResponse::error('保存配置失败: ' . $e->getMessage()));
        }
    }
    
    /**
     * 更新.env文件
     *
     * @param array $data
     * @return bool
     */
    protected function updateEnvFile(array $data)
    {
        $envFile = app()->environmentFilePath();
        $envContent = file_get_contents($envFile);
        
        foreach ($data as $key => $value) {
            // 如果值包含空格，用引号包裹
            if (strpos($value, ' ') !== false) {
                $value = '"' . $value . '"';
            }
            
            // 检查配置是否已存在
            if (strpos($envContent, $key . '=') !== false) {
                // 更新现有配置
                $envContent = preg_replace(
                    "/{$key}=.*/",
                    "{$key}={$value}",
                    $envContent
                );
            } else {
                // 添加新配置
                $envContent .= PHP_EOL . "{$key}={$value}";
            }
        }
        
        file_put_contents($envFile, $envContent);
        return true;
    }
    
    /**
     * 清除Laravel配置缓存
     */
    protected function clearConfigCache()
    {
        // 在生产环境中运行 php artisan config:clear
        if (app()->environment('production')) {
            Artisan::call('config:clear');
        }
        
        // 重新加载配置
        $configPath = app()->getCachedConfigPath();
        if (file_exists($configPath)) {
            unlink($configPath);
        }
    }
} 