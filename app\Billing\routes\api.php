<?php

use App\Billing\Http\Controllers\BillController;
use App\Billing\Http\Controllers\ConsolidatedBillController;
use App\Billing\Http\Controllers\Api\SettlementController;
use App\Billing\Http\Controllers\Api\PaymentRecordController;
use Illuminate\Support\Facades\Route;

// 将所有非前缀路由包装在一个组中，移除重复的api前缀
Route::group([], function () {
    // 账单管理路由
    Route::middleware('auth:sanctum')->group(function () {
        // 普通账单
        Route::apiResource('bills', BillController::class);
        
        // 累计账单
        Route::get('/bills/{bill}/consolidate', [ConsolidatedBillController::class, 'consolidate']);
        Route::post('/bills/{bill}/split', [ConsolidatedBillController::class, 'split']);
        Route::get('/consolidated-bills', [ConsolidatedBillController::class, 'index']);
        Route::get('/consolidated-bills/{id}', [ConsolidatedBillController::class, 'show']);
        Route::post('/consolidated-bills/{id}/payment', [ConsolidatedBillController::class, 'payment']);
        
        // 账单分页查询和统计
        Route::get('/bills-paginated', [BillController::class, 'getBillsPaginated']);
        Route::get('/bill-statistics', [BillController::class, 'getBillStatistics']);
        Route::get('/user/{user}/bills', [BillController::class, 'getUserBills']);
    });

    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/user/bills', [BillController::class, 'userBills'])->name('user.bills');
    });

    /*
    |--------------------------------------------------------------------------
    | Billing API Routes
    |--------------------------------------------------------------------------
    */

    Route::prefix('billing')->group(function () {
        
        // 账单管理
        Route::apiResource('bills', BillController::class);
        Route::post('bills/{bill}/pay', [BillController::class, 'pay']);
        Route::post('bills/{bill}/cancel', [BillController::class, 'cancel']);
        Route::get('bills/{bill}/items', [BillController::class, 'items']);
        Route::get('bills/statistics/summary', [BillController::class, 'statistics']);
        
        // 账单报告接口
        Route::get('reports/bills', [BillController::class, 'reports']);
        Route::get('reports/payments', [PaymentRecordController::class, 'reports']);
        
        // 合并账单统计接口
        Route::get('bills/consolidated/statistics', [BillController::class, 'consolidatedStats']);
        
        // 债务管理接口
        Route::get('bills/debt/overview', [BillController::class, 'debtOverview']);
        Route::get('bills/debt/ranking', [BillController::class, 'debtRanking']);
        
        // 支付记录管理
        Route::apiResource('payment-records', PaymentRecordController::class);
        Route::get('payment-records/statistics', [PaymentRecordController::class, 'statistics']);
        Route::post('payment-records/{record}/retry', [PaymentRecordController::class, 'retry']);
        Route::post('payment-records/{record}/cancel', [PaymentRecordController::class, 'cancel']);
        Route::get('payment-records/export', [PaymentRecordController::class, 'export']);
        
        // 结算管理
        Route::apiResource('settlements', SettlementController::class);
        Route::get('settlements/summary', [SettlementController::class, 'summary']);
        Route::get('settlements/analytics', [SettlementController::class, 'analytics']);
        Route::get('settlements/trends', [SettlementController::class, 'trends']);
        Route::get('settlements/module-distribution', [SettlementController::class, 'moduleDistribution']);
        Route::get('settlements/payment-analysis', [SettlementController::class, 'paymentAnalysis']);
        Route::post('settlements/{settlement}/calculate', [SettlementController::class, 'calculate']);
        Route::post('settlements/{settlement}/verify', [SettlementController::class, 'verify']);
        Route::post('settlements/{settlement}/publish', [SettlementController::class, 'publish']);
        Route::post('settlements/{settlement}/archive', [SettlementController::class, 'archive']);
        Route::get('settlements/{settlement}/export', [SettlementController::class, 'export']);
    });

    // 添加一些非billing前缀的路由（用于兼容前端API调用）
    Route::get('bills/statistics/summary', [BillController::class, 'statistics']);
    Route::get('consolidated-bills/statistics/summary', [BillController::class, 'consolidatedStats']);
    Route::get('bills/debt/overview', [BillController::class, 'debtOverview']);
    Route::get('bills/debt/ranking', [BillController::class, 'debtRanking']);
    Route::get('settlements/summary', [SettlementController::class, 'summary']);
    Route::get('settlements', [SettlementController::class, 'index']);
    Route::get('settlements/trends', [SettlementController::class, 'trends']);
    Route::get('settlements/analytics', [SettlementController::class, 'analytics']);
    Route::get('settlements/module-distribution', [SettlementController::class, 'moduleDistribution']);
    Route::get('settlements/payment-analysis', [SettlementController::class, 'paymentAnalysis']);

    // 支付记录兼容路由（移到这里避免重复）
    Route::get('payment-records/statistics', [PaymentRecordController::class, 'statistics']);
    Route::get('payment-records', [PaymentRecordController::class, 'index']);

    /*
    |--------------------------------------------------------------------------
    | Settlements API Routes
    |--------------------------------------------------------------------------
    */

    // 注意：这个路由组已经有api前缀，所以会变成 /api/api/settlements
    // 修改为去掉内部的api前缀，避免重复
    Route::middleware(['auth:sanctum'])->group(function () {

        // ==================== 结算管理 ====================
        Route::apiResource('settlements', SettlementController::class);
        
        // 结算操作
        Route::prefix('settlements')->group(function () {
            // 状态操作
            Route::post('{settlement}/calculate', [SettlementController::class, 'calculate']);
            Route::post('{settlement}/recalculate', [SettlementController::class, 'recalculate']);
            Route::post('{settlement}/verify', [SettlementController::class, 'verify']);
            Route::post('{settlement}/publish', [SettlementController::class, 'publish']);
            Route::post('{settlement}/archive', [SettlementController::class, 'archive']);
            
            // 数据获取
            Route::get('summary', [SettlementController::class, 'summary']);
            Route::get('analytics', [SettlementController::class, 'analytics']);
            Route::get('{settlement}/details', [SettlementController::class, 'details']);
            Route::get('{settlement}/logs', [SettlementController::class, 'logs']);
            
            // 导出功能
            Route::get('{settlement}/export', [SettlementController::class, 'export']);
            Route::post('batch-export', [SettlementController::class, 'batchExport']);
            
            // 快速创建
            Route::prefix('quick-create')->group(function () {
                Route::post('monthly-platform', [SettlementController::class, 'createMonthlyPlatform']);
                Route::post('quarterly-module', [SettlementController::class, 'createQuarterlyModule']);
                Route::post('yearly-total', [SettlementController::class, 'createYearlyTotal']);
            });
            
            // 数据维护
            Route::post('{settlement}/validate', [SettlementController::class, 'validateSettlement']);
            Route::post('{settlement}/repair', [SettlementController::class, 'repair']);
        });
    });
}); // 结束路由组

// 🔥 重要：统一支付回调路由（不需要认证）- 替代各模块的支付回调处理
use App\Billing\Http\Controllers\Api\PaymentCallbackController;

// 主要微信支付回调路由 - 由账单系统统一处理
Route::any('billing/callbacks/wechat', [PaymentCallbackController::class, 'wechatCallback'])->withoutMiddleware(['auth:sanctum']);
Route::any('billing/callbacks/wechat/notify', [PaymentCallbackController::class, 'wechatCallback'])->withoutMiddleware(['auth:sanctum']);

// 其他支付方式回调
Route::any('billing/callbacks/alipay', [PaymentCallbackController::class, 'alipayCallback'])->withoutMiddleware(['auth:sanctum']);
Route::post('billing/callbacks/payment-link', [PaymentCallbackController::class, 'paymentLinkCallback'])->withoutMiddleware(['auth:sanctum']);

// 手动确认收款（需要认证）
Route::post('billing/callbacks/mark-payment-success', [PaymentCallbackController::class, 'markPaymentSuccess']);

// 🔥 兼容性路由：保持旧的微信支付回调路由，但转发到新的统一处理器
Route::post('payment-records/wechat/notify', [PaymentCallbackController::class, 'wechatCallback'])->withoutMiddleware(['auth:sanctum']);

// 🔥 全局兼容性路由：处理可能的历史回调地址
Route::any('api/wechat/notify', [PaymentCallbackController::class, 'wechatCallback'])->withoutMiddleware(['auth:sanctum']);
Route::any('api/payments/wechat/callback', [PaymentCallbackController::class, 'wechatCallback'])->withoutMiddleware(['auth:sanctum']);
Route::any('wechat/payment/notify', [PaymentCallbackController::class, 'wechatCallback'])->withoutMiddleware(['auth:sanctum']); 