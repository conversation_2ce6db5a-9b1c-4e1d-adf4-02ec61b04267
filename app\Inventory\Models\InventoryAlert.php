<?php

namespace App\Inventory\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Product\Models\Product;
use App\Warehouse\Models\Warehouse;
use App\Employee\Models\Employee;
use Illuminate\Support\Facades\Log;

class InventoryAlert extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'warehouse_id',
        'alert_type',
        'severity',
        'status',
        'title',
        'message',
        'alert_data',
        'current_stock',
        'threshold_value',
        'previous_stock',
        'acknowledged_at',
        'acknowledged_by',
        'resolved_at',
        'resolved_by',
        'resolution_notes',
        'notification_channels',
        'notification_recipients',
        'last_notified_at',
        'notification_count',
        'auto_resolve',
        'expires_at',
    ];

    protected $casts = [
        'alert_data' => 'array',
        'notification_channels' => 'array',
        'notification_recipients' => 'array',
        'acknowledged_at' => 'datetime',
        'resolved_at' => 'datetime',
        'last_notified_at' => 'datetime',
        'expires_at' => 'datetime',
        'auto_resolve' => 'boolean',
        'current_stock' => 'decimal:2',
        'threshold_value' => 'decimal:2',
        'previous_stock' => 'decimal:2',
        'notification_count' => 'integer',
    ];

    /**
     * 关联商品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 关联仓库
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * 确认人员
     */
    public function acknowledgedBy()
    {
        return $this->belongsTo(Employee::class, 'acknowledged_by');
    }

    /**
     * 解决人员
     */
    public function resolvedBy()
    {
        return $this->belongsTo(Employee::class, 'resolved_by');
    }

    /**
     * 创建库存预警
     * 
     * @param array $data 预警数据
     * @return static
     */
    public static function createAlert(array $data)
    {
        // 检查是否已存在相同的活跃预警
        $existingAlert = static::where('product_id', $data['product_id'])
            ->where('warehouse_id', $data['warehouse_id'] ?? null)
            ->where('alert_type', $data['alert_type'])
            ->where('status', 'active')
            ->first();

        if ($existingAlert) {
            // 更新现有预警
            $existingAlert->update([
                'current_stock' => $data['current_stock'],
                'previous_stock' => $existingAlert->current_stock,
                'message' => $data['message'],
                'alert_data' => array_merge($existingAlert->alert_data ?? [], $data['alert_data'] ?? []),
                'updated_at' => now(),
            ]);
            
            Log::info('更新现有库存预警', [
                'alert_id' => $existingAlert->id,
                'product_id' => $data['product_id'],
                'alert_type' => $data['alert_type']
            ]);
            
            return $existingAlert;
        }

        // 创建新预警
        $alert = static::create($data);
        
        Log::info('创建新库存预警', [
            'alert_id' => $alert->id,
            'product_id' => $data['product_id'],
            'alert_type' => $data['alert_type'],
            'severity' => $data['severity'] ?? 'medium'
        ]);

        return $alert;
    }

    /**
     * 确认预警
     * 
     * @param int $employeeId 员工ID
     * @param string|null $notes 备注
     * @return bool
     */
    public function acknowledge($employeeId, $notes = null)
    {
        $this->update([
            'status' => 'acknowledged',
            'acknowledged_at' => now(),
            'acknowledged_by' => $employeeId,
            'resolution_notes' => $notes,
        ]);

        Log::info('库存预警已确认', [
            'alert_id' => $this->id,
            'acknowledged_by' => $employeeId,
            'notes' => $notes
        ]);

        return true;
    }

    /**
     * 解决预警
     * 
     * @param int $employeeId 员工ID
     * @param string|null $notes 解决备注
     * @return bool
     */
    public function resolve($employeeId, $notes = null)
    {
        $this->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'resolved_by' => $employeeId,
            'resolution_notes' => $notes,
        ]);

        Log::info('库存预警已解决', [
            'alert_id' => $this->id,
            'resolved_by' => $employeeId,
            'notes' => $notes
        ]);

        return true;
    }

    /**
     * 忽略预警
     * 
     * @param int $employeeId 员工ID
     * @param string|null $reason 忽略原因
     * @return bool
     */
    public function ignore($employeeId, $reason = null)
    {
        $this->update([
            'status' => 'ignored',
            'acknowledged_at' => now(),
            'acknowledged_by' => $employeeId,
            'resolution_notes' => $reason,
        ]);

        Log::info('库存预警已忽略', [
            'alert_id' => $this->id,
            'ignored_by' => $employeeId,
            'reason' => $reason
        ]);

        return true;
    }

    /**
     * 发送通知
     * 
     * @param array $channels 通知渠道
     * @param array $recipients 接收人
     * @return bool
     */
    public function sendNotification($channels = null, $recipients = null)
    {
        $channels = $channels ?? $this->notification_channels ?? ['system'];
        $recipients = $recipients ?? $this->notification_recipients ?? [];

        // 更新通知记录
        $this->update([
            'last_notified_at' => now(),
            'notification_count' => $this->notification_count + 1,
        ]);

        // TODO: 实现具体的通知发送逻辑
        // 这里可以集成邮件、短信、系统通知等
        Log::info('发送库存预警通知', [
            'alert_id' => $this->id,
            'channels' => $channels,
            'recipients' => $recipients,
            'notification_count' => $this->notification_count
        ]);

        return true;
    }

    /**
     * 检查是否应该自动解决
     * 
     * @return bool
     */
    public function shouldAutoResolve()
    {
        if (!$this->auto_resolve || $this->status !== 'active') {
            return false;
        }

        // 检查库存是否已恢复正常
        $product = $this->product;
        if (!$product) {
            return false;
        }

        switch ($this->alert_type) {
            case 'low_stock':
                return $product->stock > ($this->threshold_value ?? 0);
            
            case 'negative_stock':
                return $product->stock >= 0;
            
            case 'out_of_stock':
                return $product->stock > 0;
            
            default:
                return false;
        }
    }

    /**
     * 自动解决预警
     * 
     * @return bool
     */
    public function autoResolve()
    {
        if (!$this->shouldAutoResolve()) {
            return false;
        }

        $this->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'resolution_notes' => '系统自动解决：库存已恢复正常',
        ]);

        Log::info('库存预警自动解决', [
            'alert_id' => $this->id,
            'alert_type' => $this->alert_type,
            'current_stock' => $this->product->stock
        ]);

        return true;
    }

    /**
     * 获取预警级别颜色
     * 
     * @return string
     */
    public function getSeverityColorAttribute()
    {
        return match($this->severity) {
            'low' => 'blue',
            'medium' => 'yellow',
            'high' => 'orange',
            'critical' => 'red',
            default => 'gray'
        };
    }

    /**
     * 获取预警状态颜色
     * 
     * @return string
     */
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'active' => 'red',
            'acknowledged' => 'yellow',
            'resolved' => 'green',
            'ignored' => 'gray',
            default => 'gray'
        };
    }

    /**
     * 获取预警类型显示文本
     * 
     * @return string
     */
    public function getAlertTypeTextAttribute()
    {
        return match($this->alert_type) {
            'low_stock' => '低库存预警',
            'negative_stock' => '负库存预警',
            'out_of_stock' => '缺货预警',
            'overstock' => '超库存预警',
            'reorder_point' => '补货点预警',
            'expiry_warning' => '过期预警',
            'custom' => '自定义预警',
            default => '未知预警'
        };
    }

    /**
     * 获取预警级别显示文本
     * 
     * @return string
     */
    public function getSeverityTextAttribute()
    {
        return match($this->severity) {
            'low' => '低',
            'medium' => '中',
            'high' => '高',
            'critical' => '紧急',
            default => '未知'
        };
    }

    /**
     * 获取预警状态显示文本
     * 
     * @return string
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'active' => '活跃',
            'acknowledged' => '已确认',
            'resolved' => '已解决',
            'ignored' => '已忽略',
            default => '未知'
        };
    }

    /**
     * 作用域：活跃预警
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 作用域：按严重程度排序
     */
    public function scopeOrderBySeverity($query)
    {
        return $query->orderByRaw("
            CASE severity 
                WHEN 'critical' THEN 1 
                WHEN 'high' THEN 2 
                WHEN 'medium' THEN 3 
                WHEN 'low' THEN 4 
                ELSE 5 
            END
        ");
    }

    /**
     * 作用域：过期预警
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }
} 