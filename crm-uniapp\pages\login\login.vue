<template>
	<view class="login-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<text class="navbar-title">员工登录</text>
			</view>
		</view>
		
		<!-- 登录表单 -->
		<view class="login-form">
			<!-- Logo -->
			<view class="logo-section">
				<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
				<text class="app-name">CRM管理系统</text>
				<text class="app-desc">生鲜配送员工管理平台</text>
			</view>
			
			<!-- 表单 -->
			<view class="form-section">
				<view class="input-group">
					<view class="input-item">
						<view class="input-icon">
							<text class="iconfont icon-user"></text>
						</view>
						<input 
							class="input-field" 
							type="text" 
							placeholder="请输入用户名" 
							v-model="formData.username"
							:focus="usernameFocus"
						/>
					</view>
					
					<view class="input-item">
						<view class="input-icon">
							<text class="iconfont icon-lock"></text>
						</view>
						<input 
							class="input-field" 
							:type="showPassword ? 'text' : 'password'" 
							placeholder="请输入密码" 
							v-model="formData.password"
						/>
						<view class="input-action" @tap="togglePassword">
							<text class="iconfont" :class="showPassword ? 'icon-eye' : 'icon-eye-close'"></text>
						</view>
					</view>
				</view>
				
				<!-- 记住密码 -->
				<view class="form-options">
					<view class="checkbox-item" @tap="toggleRemember">
						<view class="checkbox" :class="{ checked: rememberPassword }">
							<text class="iconfont icon-check" v-if="rememberPassword"></text>
						</view>
						<text class="checkbox-text">记住密码</text>
					</view>
				</view>
				
				<!-- 登录按钮 -->
				<button 
					class="login-btn" 
					:class="{ disabled: !canLogin }" 
					:disabled="!canLogin || loading"
					@tap="handleLogin"
				>
					<text v-if="loading">登录中...</text>
					<text v-else>登录</text>
				</button>
			</view>
		</view>
		
		<!-- 底部信息 -->
		<view class="footer">
			<text class="footer-text">© 2024 生鲜配送CRM系统</text>
		</view>
	</view>
</template>

<script>
import authApi from '../../api/auth.js'
import config from '../../utils/config.js'
import authManager from '../../utils/auth.js'

export default {
	data() {
		return {
			statusBarHeight: 0,
			usernameFocus: false,
			showPassword: false,
			rememberPassword: false,
			loading: false,
			formData: {
				username: '',
				password: ''
			}
		}
	},
	
	computed: {
		canLogin() {
			return this.formData.username.trim() && this.formData.password.trim()
		}
	},
	
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync()
		this.statusBarHeight = systemInfo.statusBarHeight || 0
		
		// 检查是否已登录
		this.checkLoginStatus()
		
		// 加载记住的密码
		this.loadRememberedPassword()
	},
	
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			if (authManager.isLoggedIn() && !authManager.isTokenExpired()) {
				// 已登录且token未过期，跳转到首页
				uni.reLaunch({
					url: '/pages/index/index'
				})
			}
		},
		
		// 加载记住的密码
		loadRememberedPassword() {
			const remembered = uni.getStorageSync('remembered_password')
			if (remembered) {
				this.formData.username = remembered.username || ''
				this.formData.password = remembered.password || ''
				this.rememberPassword = true
			}
		},
		
		// 切换密码显示
		togglePassword() {
			this.showPassword = !this.showPassword
		},
		
		// 切换记住密码
		toggleRemember() {
			this.rememberPassword = !this.rememberPassword
		},
		
		// 处理登录
		async handleLogin() {
			if (!this.canLogin || this.loading) return
			
			this.loading = true
			
			try {
				console.log('开始登录，用户名:', this.formData.username)
				
				const response = await authApi.login({
					username: this.formData.username.trim(),
					password: this.formData.password.trim()
				})
				
				console.log('登录响应:', response)
				
				// 检查响应数据结构
				if (response && response.data) {
					// 使用认证管理器保存登录信息
					authManager.saveLoginInfo(response.data.token, response.data.employee)
					
					// 处理记住密码
					if (this.rememberPassword) {
						uni.setStorageSync('remembered_password', {
							username: this.formData.username,
							password: this.formData.password
						})
					} else {
						uni.removeStorageSync('remembered_password')
					}
					
					uni.showToast({
						title: '登录成功',
						icon: 'success',
						duration: 1500
					})
					
					// 跳转到首页
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/index/index'
						})
					}, 1500)
				} else {
					throw new Error('响应数据格式错误')
				}
				
			} catch (error) {
				console.error('登录失败:', error)
				
				// 显示具体的错误信息
				let errorMessage = '登录失败'
				if (error && error.message) {
					errorMessage = error.message
				} else if (typeof error === 'string') {
					errorMessage = error
				}
				
				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
			} finally {
				this.loading = false
			}
		}
	}
}
</script>

<style scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	flex-direction: column;
}

.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10px);
}

.navbar-content {
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 32rpx;
}

.navbar-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

.login-form {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	padding: 0 60rpx;
	margin-top: 100rpx;
}

.logo-section {
	text-align: center;
	margin-bottom: 100rpx;
}

.logo {
	width: 160rpx;
	height: 160rpx;
	border-radius: 20rpx;
	margin-bottom: 40rpx;
}

.app-name {
	display: block;
	font-size: 48rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 16rpx;
}

.app-desc {
	display: block;
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
}

.form-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 24rpx;
	padding: 60rpx 40rpx;
	backdrop-filter: blur(10px);
}

.input-group {
	margin-bottom: 40rpx;
}

.input-item {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 16rpx;
	margin-bottom: 32rpx;
	padding: 0 24rpx;
	height: 96rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.input-item:focus-within {
	border-color: #007AFF;
	background: #ffffff;
}

.input-icon {
	margin-right: 24rpx;
	color: #8e8e93;
	font-size: 36rpx;
}

.input-field {
	flex: 1;
	font-size: 32rpx;
	color: #333333;
}

.input-action {
	margin-left: 24rpx;
	color: #8e8e93;
	font-size: 36rpx;
	padding: 8rpx;
}

.form-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 60rpx;
}

.checkbox-item {
	display: flex;
	align-items: center;
}

.checkbox {
	width: 36rpx;
	height: 36rpx;
	border: 2rpx solid #d1d1d6;
	border-radius: 8rpx;
	margin-right: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.checkbox.checked {
	background: #007AFF;
	border-color: #007AFF;
}

.checkbox .icon-check {
	color: #ffffff;
	font-size: 24rpx;
}

.checkbox-text {
	font-size: 28rpx;
	color: #666666;
}

.login-btn {
	width: 100%;
	height: 96rpx;
	background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
	border-radius: 16rpx;
	border: none;
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.login-btn:not(.disabled):active {
	transform: scale(0.98);
}

.login-btn.disabled {
	background: #d1d1d6;
	color: #8e8e93;
}

.footer {
	text-align: center;
	padding: 40rpx;
}

.footer-text {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.6);
}

/* 图标字体 */
.iconfont {
	font-family: 'iconfont';
}

.icon-user::before { content: '👤'; }
.icon-lock::before { content: '🔒'; }
.icon-eye::before { content: '👁'; }
.icon-eye-close::before { content: '🙈'; }
.icon-check::before { content: '✓'; }
</style> 