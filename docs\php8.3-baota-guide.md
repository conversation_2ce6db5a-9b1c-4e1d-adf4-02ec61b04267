# PHP 8.3 宝塔面板安装指南

## 1. 安装PHP 8.3

### 方法一：通过宝塔面板软件商店
1. 登录宝塔面板
2. 点击 **软件商店**
3. 搜索 **PHP 8.3**
4. 点击 **安装**

### 方法二：命令行安装（如果软件商店没有）
```bash
# Ubuntu/Debian
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh

# CentOS
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh
```

## 2. 安装PHP 8.3扩展

### 在宝塔面板中安装扩展：
1. 软件商店 → PHP 8.3 → **设置**
2. 点击 **安装扩展** 标签
3. 安装以下扩展：

#### 必需扩展（Laravel 10）
- [x] **bcmath** - 任意精度数学
- [x] **curl** - HTTP客户端
- [x] **fileinfo** - 文件信息
- [x] **gd** - 图像处理
- [x] **mbstring** - 多字节字符串
- [x] **mysql** - MySQL数据库
- [x] **opcache** - 操作码缓存
- [x] **openssl** - 加密支持
- [x] **pdo** - 数据库抽象层
- [x] **xml** - XML处理
- [x] **zip** - 压缩文件

#### 推荐扩展
- [x] **redis** - Redis缓存
- [x] **imagick** - 高级图像处理
- [x] **intl** - 国际化支持
- [x] **soap** - SOAP协议支持

#### 项目特定扩展（根据您的composer.json）
- [x] **simplexml** - 简单XML处理（微信SDK需要）
- [x] **libxml** - XML库支持

## 3. PHP配置优化

### 在宝塔面板中配置PHP：
1. 软件商店 → PHP 8.3 → **设置**
2. 点击 **配置修改** 标签
3. 修改以下配置：

```ini
; 内存限制
memory_limit = 512M

; 执行时间限制
max_execution_time = 300

; 上传文件大小限制
upload_max_filesize = 100M
post_max_size = 100M

; 错误报告（生产环境设为Off）
display_errors = Off
log_errors = On

; 时区设置
date.timezone = Asia/Shanghai

; OPcache配置
opcache.enable = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
```

## 4. 设置网站PHP版本

1. 网站 → 找到您的网站 → **设置**
2. **PHP版本** → 选择 **PHP-83**
3. 点击 **确定**

## 5. 配置Nginx（针对Laravel）

在网站设置中配置Nginx：

```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ \.php$ {
    fastcgi_pass unix:/tmp/php-cgi-83.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
}

# 阻止访问敏感目录
location ~ ^/(app|bootstrap|config|database|resources|routes|storage|tests|vendor)/ {
    deny all;
    return 403;
}

# 静态文件缓存
location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
    expires 30d;
    add_header Cache-Control "public, immutable";
}
```

## 6. 验证安装

### 方法一：使用检查脚本
```bash
php check-php-extensions.php
```

### 方法二：创建phpinfo页面
1. 在网站根目录创建 `info.php`：
```php
<?php
phpinfo();
?>
```
2. 访问 `http://your-domain.com/info.php`
3. 检查PHP版本和扩展

### 方法三：命令行检查
```bash
# 检查PHP版本
php -v

# 检查已安装的扩展
php -m

# 检查特定扩展
php -m | grep -E "(bcmath|curl|gd|mbstring|mysql|redis)"
```

## 7. 常见问题解决

### 问题1：502 Bad Gateway
**原因**：PHP-FPM未启动或配置错误
**解决**：
```bash
# 重启PHP-FPM
systemctl restart php83-php-fpm

# 检查PHP-FPM状态
systemctl status php83-php-fpm
```

### 问题2：扩展安装失败
**解决**：
1. 更新系统包：`yum update` 或 `apt update`
2. 手动编译安装扩展
3. 检查系统依赖

### 问题3：内存不足
**解决**：
1. 增加 `memory_limit`
2. 启用 OPcache
3. 优化代码

## 8. 性能优化建议

1. **启用OPcache**：显著提升PHP性能
2. **使用Redis缓存**：缓存会话和应用数据
3. **配置合适的内存限制**：根据应用需求调整
4. **启用Gzip压缩**：减少传输数据量
5. **使用CDN**：加速静态资源加载

## 9. 安全建议

1. **隐藏PHP版本**：在nginx配置中添加 `server_tokens off;`
2. **禁用危险函数**：在php.ini中禁用 `exec`, `shell_exec` 等
3. **设置合适的文件权限**：网站目录755，文件644
4. **定期更新**：保持PHP和扩展为最新版本
5. **使用HTTPS**：启用SSL证书 