<?php

use App\User\Http\Controllers\UserController;
use App\Crm\Http\Controllers\UserAddressController;
use App\Crm\Http\Controllers\ClientFollowUpController;
use App\Admin\Http\Controllers\AddressController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| User API 路由
|--------------------------------------------------------------------------
|
| User模块的API路由定义
|
*/

// 用户管理路由
Route::prefix('users')->group(function () {
    Route::get('/', [UserController::class, 'index']);
    Route::get('/debug', [UserController::class, 'debugIndex']); // 临时调试路由
    Route::post('/', [UserController::class, 'store'])->middleware('employee.role:admin,manager');
    Route::get('/available-employees', [UserController::class, 'availableEmployees']);
    Route::get('/available-deliverers', [UserController::class, 'availableDeliverers']);
    Route::get('/available-crm-agents', [UserController::class, 'availableCrmAgents']);
    Route::post('/sync-wechat', [UserController::class, 'syncWechatUsers'])->middleware('employee.role:admin,manager');
    Route::get('/search', [UserController::class, 'search']);
    Route::get('/export', [UserController::class, 'exportUsers'])->middleware('employee.role:admin,manager');
    Route::get('/by-phone/{phone}', [UserController::class, 'findByPhone']);
    Route::get('/{id}', [UserController::class, 'show']);
    Route::put('/{id}', [UserController::class, 'update'])->middleware('employee.role:admin,manager');
    Route::delete('/{id}', [UserController::class, 'destroy'])->middleware('employee.role:admin');
    Route::put('/{id}/status', [UserController::class, 'updateStatus'])->middleware('employee.role:admin,manager');
    Route::put('/{id}/membership', [UserController::class, 'updateMembership'])->middleware('employee.role:admin,manager');
    Route::put('/{id}/balance', [UserController::class, 'updateBalance'])->middleware('employee.role:admin,manager');
    Route::put('/{id}/points', [UserController::class, 'updatePoints'])->middleware('employee.role:admin,manager');
    Route::put('/{id}/membership-level', [UserController::class, 'updateMembershipLevel'])->middleware('employee.role:admin,manager');
    Route::post('/{id}/refresh-level', [UserController::class, 'refreshMembershipLevel'])->middleware('employee.role:admin,manager');
    Route::get('/{id}/orders', [UserController::class, 'getUserOrders']);
    Route::get('/{id}/statistics', [UserController::class, 'getUserStatistics']);
    Route::post('/{id}/assign-agent', [UserController::class, 'assignAgent'])->middleware('employee.role:admin,manager');
    
    // 用户地址快捷路由 - 匹配前端调用方式
    Route::get('/{id}/addresses', [UserAddressController::class, 'getUserAddresses']);
    Route::post('/{id}/addresses', [AddressController::class, 'store']);
    
    // 用户跟进记录快捷路由 - 匹配前端调用方式  
    Route::get('/{id}/follow-ups', [ClientFollowUpController::class, 'getUserFollowUps']);
}); 