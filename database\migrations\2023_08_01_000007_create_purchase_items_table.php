<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('purchase_order_id')->constrained()->onDelete('cascade')->comment('采购订单ID');
            $table->foreignId('product_id')->constrained()->comment('商品ID');
            $table->decimal('quantity', 10, 2)->comment('数量');
            $table->foreignId('unit_id')->constrained()->comment('单位ID');
            $table->decimal('unit_price', 10, 2)->comment('单价');
            $table->decimal('received_quantity', 10, 2)->default(0)->comment('已收货数量');
            $table->decimal('total_price', 12, 2)->comment('总价');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_items');
    }
}; 