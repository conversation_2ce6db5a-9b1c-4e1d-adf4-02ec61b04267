<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Inventory\Models\Inventory;
use App\Product\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ConvertInventoryToBaseUnits extends Migration
{
    /**
     * 运行迁移
     *
     * @return void
     */
    public function up()
    {
        // 记录开始迁移
        Log::info('开始进行库存单位转换迁移');
        
        // 获取所有库存记录
        $inventories = Inventory::with(['product'])->get();
        $totalCount = $inventories->count();
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($inventories as $inventory) {
            try {
                $product = $inventory->product;
                
                // 如果产品不存在，记录错误并跳过
                if (!$product) {
                    Log::error('库存转换失败：找不到产品', [
                        'inventory_id' => $inventory->id,
                        'product_id' => $inventory->product_id
                    ]);
                    $errorCount++;
                    continue;
                }
                
                // 获取产品的基本单位ID
                $baseUnitId = $product->base_unit_id;
                
                // 如果库存已经是基本单位，不需要转换
                if ($inventory->unit_id === $baseUnitId) {
                    $successCount++;
                    continue;
                }
                
                // 获取从当前单位到基本单位的转换率
                $conversionRate = $product->getUnitConversionRate(
                    $inventory->unit_id, 
                    $baseUnitId
                );
                
                // 如果无法获取转换率，记录错误并跳过
                if ($conversionRate === null) {
                    Log::error('库存转换失败：无法获取转换率', [
                        'inventory_id' => $inventory->id,
                        'product_id' => $inventory->product_id,
                        'from_unit_id' => $inventory->unit_id,
                        'to_unit_id' => $baseUnitId
                    ]);
                    $errorCount++;
                    continue;
                }
                
                // 计算基本单位下的库存量
                $stockInBaseUnit = $inventory->stock * $conversionRate;
                $oldStock = $inventory->stock;
                
                // 更新库存单位和数量
                $inventory->stock = $stockInBaseUnit;
                $inventory->unit_id = $baseUnitId;
                $inventory->save();
                
                Log::info('库存单位转换成功', [
                    'inventory_id' => $inventory->id,
                    'product_id' => $inventory->product_id,
                    'from_unit_id' => $inventory->unit_id,
                    'to_unit_id' => $baseUnitId,
                    'old_stock' => $oldStock,
                    'new_stock' => $stockInBaseUnit
                ]);
                
                $successCount++;
            } catch (\Exception $e) {
                Log::error('库存转换异常', [
                    'inventory_id' => $inventory->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                $errorCount++;
            }
        }
        
        // 更新所有产品的总库存
        $products = Product::all();
        foreach ($products as $product) {
            $product->updateTotalStock();
        }
        
        // 记录迁移完成结果
        Log::info('库存单位转换迁移完成', [
            'total' => $totalCount,
            'success' => $successCount,
            'error' => $errorCount
        ]);
    }

    /**
     * 回滚迁移
     *
     * @return void
     */
    public function down()
    {
        // 不提供回滚功能，因为无法准确还原原始单位
        Log::warning('无法回滚库存单位转换');
    }
} 