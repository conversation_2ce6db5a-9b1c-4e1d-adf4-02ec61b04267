// 横向商品卡片组件 - 性能优化版
const { isLoggedIn } = require('../../utils/login-state-manager');

Component({
  /**
   * 组件属性
   */
  properties: {
    // 商品数据
    product: {
      type: Object,
      value: {},
      observer: 'onProductChange'
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    
    // 是否显示供应商信息
    showSupplier: {
      type: Boolean,
      value: true
    },
    
    // 是否显示标签
    showTags: {
      type: Boolean,
      value: true
    },
    
    // 是否显示加购按钮
    showAddCart: {
      type: Boolean,
      value: true
    },
    
    // 布局模式 normal/compact
    layoutMode: {
      type: String,
      value: 'normal'
    }
  },

  /**
   * 组件数据
   */
  data: {
    cardClassNames: '',
    cartQuantity: 0, // 商品在购物车中的数量
    adding: false,   // 防止重复点击
    minQuantity: 1   // 最小起购数量
  },

  // 防抖控制
  _updateTimer: null,
  _timers: {},

  /**
   * 组件生命周期
   */
  lifetimes: {
    /**
     * 组件挂载时
     */
    attached() {
      this.updateCardClass();
      this.checkCartQuantity();
    },

    /**
     * 组件卸载时
     */
    detached() {
      // 清理定时器
      if (this._updateTimer) {
        clearTimeout(this._updateTimer);
        this._updateTimer = null;
      }

      // 清理所有定时器
      Object.values(this._timers).forEach(timer => {
        if (timer) clearTimeout(timer);
      });
      this._timers = {};
    }
  },

  /**
   * 组件方法
   */
  methods: {
    /**
     * 商品数据变化监听 - 参考首页商品卡片处理方式
     */
    onProductChange(newProduct, oldProduct) {
      // 验证新商品数据的有效性
      if (!newProduct || typeof newProduct !== 'object' || !newProduct.id) {
        return;
      }
      
      if (newProduct !== oldProduct) {
        // 预处理标签数据，添加CSS类
        if (newProduct.tags && Array.isArray(newProduct.tags)) {
          newProduct.tags = newProduct.tags.map(tag => ({
            ...tag,
            cssClass: this._getTagClass(tag)
          }));
        }
        
        // 商品变化时更新样式类即可，图片由lazy-image组件自行处理
        
        // 更新样式类
        this.updateCardClass();
      }
    },

    /**
     * 安全的setData方法
     */
    safeSetData(data, callback) {
      if (this._updateTimer) {
        clearTimeout(this._updateTimer);
      }
      
      this._updateTimer = setTimeout(() => {
        this.setData(data, callback);
        this._updateTimer = null;
      }, 10);
    },

    /**
     * 更新卡片样式类 - 参考首页处理方式
     */
    updateCardClass() {
      const { layoutMode } = this.properties;
      let classNames = [];
      
      if (layoutMode && layoutMode !== 'normal') {
        classNames.push(layoutMode);
      }
      
      // 检查商品状态 - 参考首页逻辑
      const { product } = this.properties;
      if (product) {
        // 检查缺货状态 - 使用后端提供的out_of_stock字段
        if (product.out_of_stock) {
          classNames.push('out-of-stock');
        }
        
        // 检查促销标签 - 参考首页逻辑
        if (product.isPromotion || (product.tags && product.tags.some(tag => 
          (tag.name || tag).includes('促销') || 
          (tag.name || tag).includes('特价') || 
          (tag.name || tag).includes('折扣') || 
          (tag.name || tag).includes('优惠')))) {
          classNames.push('promotion');
        }
        
        // 检查新品标签
        if (product.tags && product.tags.some(tag => 
          (tag.name || tag).includes('新品') || 
          (tag.name || tag).includes('上新') || 
          (tag.name || tag).includes('NEW') || 
          (tag.name || tag).includes('new')
        )) {
          classNames.push('new-product');
        }
      }
      
      const newClassNames = classNames.join(' ');
      if (newClassNames !== this.data.cardClassNames) {
        this.safeSetData({ cardClassNames: newClassNames });
      }
    },

    /**
     * 商品点击
     */
    onProductTap(e) {
      // 安全地阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      this.triggerEvent('productTap', { product: this.properties.product });
    },

    /**
     * 图片加载成功
     */
    onImageLoad(e) {
      // 触发图片加载成功事件
      this.triggerEvent('imageLoad', {
        product: this.properties.product,
        detail: e.detail
      });
    },

    /**
     * 图片加载失败
     */
    onImageError(e) {
      // 触发图片加载失败事件
      this.triggerEvent('imageError', {
        product: this.properties.product,
        detail: e.detail
      });
    },

    /**
     * 图片点击事件
     */
    onImageTap(e) {
      // 安全地阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      
      // 触发图片点击事件
      this.triggerEvent('imageTap', {
        product: this.properties.product,
        event: e
      });
    },

    /**
     * 添加到购物车
     */
    onAddToCart(e) {
      // 安全地阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      
      // 检查商品数据是否完整
      const product = this.properties.product;
      if (!product || !product.id) {
        wx.showToast({
          title: '商品信息不完整',
          icon: 'none'
        });
        console.error('添加购物车失败: 商品信息不完整', product);
        return;
      }
      
      // 检查登录状态，未登录时跳转到登录页
      const userLoggedIn = isLoggedIn();
      if (!userLoggedIn) {
        wx.navigateTo({
          url: '/pages/login/index'
        });
        return;
      }
      
      // 触发添加到购物车事件
      this.triggerEvent('addToCart', { 
        product: this.properties.product,
        quantity: 1
      });
      
      // 更新购物车数量
      setTimeout(() => {
        this.checkCartQuantity();
      }, 500); // 延迟500ms，等待购物车更新完成
    },

    /**
     * 缺货按钮点击处理
     */
    onOutOfStockTap(e) {
      // 安全地阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      
      // 显示缺货提示
      const product = this.properties.product;
      const message = product.purchase_message || '商品已售罄';
      
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      });
      
      // 触发缺货点击事件，供父组件处理
      this.triggerEvent('outOfStockTap', { 
        product: product,
        message: message
      });
    },

    /**
     * 检查商品在购物车中的数量
     */
    async checkCartQuantity() {
      try {
        if (!this.properties.product || !this.properties.product.id) return;

        // 检查登录状态，未登录时直接设置为0
        if (!isLoggedIn()) {
          if (this.data.cartQuantity !== 0) {
            this.setData({ cartQuantity: 0 });
          }
          return;
        }

        const { getItemQuantity } = require('../../utils/cart-unified');
        const quantity = await getItemQuantity(this.properties.product.id);

        console.log('🔍 checkCartQuantity 结果:', {
          product_id: this.properties.product.id,
          product_name: this.properties.product.name,
          当前界面数量: this.data.cartQuantity,
          API返回数量: quantity,
          需要更新: quantity !== this.data.cartQuantity
        });

        if (quantity !== this.data.cartQuantity) {
          this.setData({ cartQuantity: quantity });
          console.log('✅ 已更新界面数量:', quantity);
        }
      } catch (error) {
        // 静默失败，未登录或其他错误时设置为0
        if (this.data.cartQuantity !== 0) {
          this.setData({ cartQuantity: 0 });
        }
      }
    },
    
    /**
     * 获取标签CSS类 - 根据标签内容返回对应的样式类
     */
    _getTagClass(tag) {
      const tagName = (tag.name || tag || '').toLowerCase();
      
      // 促销类标签
      if (tagName.includes('促销') || 
          tagName.includes('特价') || 
          tagName.includes('折扣') || 
          tagName.includes('优惠')) {
        return 'promotion-tag';
      }
      
      // 新品类标签
      if (tagName.includes('新品') || 
          tagName.includes('上新') || 
          tagName.includes('new')) {
        return 'new-tag';
      }
      
      // 默认标签样式
      return '';
    },
    
    /**
     * 价格加载成功事件处理
     */
    onPriceLoaded(e) {
      // 触发价格加载成功事件，传递给父组件
      this.triggerEvent('priceLoaded', {
        product: this.properties.product,
        priceInfo: e.detail
      });
    },
    
    /**
     * 价格加载失败事件处理
     */
    onPriceError(e) {
      // 触发价格加载失败事件，传递给父组件
      this.triggerEvent('priceError', {
        product: this.properties.product,
        error: e.detail
      });
    },

    /**
     * 增加商品数量
     */
    async onIncreaseQuantity() {
      const { product } = this.properties;
      const { cartQuantity } = this.data;

      // 参数验证
      if (!product || !product.id) {
        console.error('❌ 商品信息无效，无法增加数量:', product);
        wx.showToast({
          title: '商品信息错误',
          icon: 'none'
        });
        return;
      }

      // 检查登录状态
      if (!isLoggedIn()) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      // 防止重复点击
      if (this.data.adding) {
        return;
      }

      this.setData({ adding: true });

      // 计算新数量
      const newQuantity = cartQuantity + 1;
      const minQuantity = product.min_sale_quantity || 1;

      console.log('🔼 横向卡片增加数量:', {
        product_id: product.id,
        product_name: product.name || '未知商品',
        currentQuantity: cartQuantity,
        newQuantity,
        minQuantity
      });

      // 🔍 重新检查购物车中的实际数量
      console.log('🔍 重新检查购物车中的实际数量...');
      const { getItemQuantity } = require('../../utils/cart-unified');
      const actualQuantity = await getItemQuantity(product.id);

      console.log('🔍 数量对比:', {
        界面显示数量: cartQuantity,
        实际购物车数量: actualQuantity,
        是否一致: cartQuantity === actualQuantity
      });

      // 根据实际数量决定操作类型
      if (actualQuantity === 0) {
        // 商品不在购物车中，需要先添加
        console.log('➕ 商品不在购物车中，调用添加方法');
        this.addToCart(product, newQuantity);
      } else {
        // 商品已在购物车中，更新数量
        console.log('🔄 商品已在购物车中，调用更新方法');
        this.updateCartItemQuantity(product, actualQuantity + 1);
      }
    },

    /**
     * 减少商品数量
     */
    async onDecreaseQuantity() {
      const { product } = this.properties;
      const { cartQuantity } = this.data;

      // 参数验证
      if (!product || !product.id) {
        console.error('❌ 商品信息无效，无法减少数量:', product);
        wx.showToast({
          title: '商品信息错误',
          icon: 'none'
        });
        return;
      }

      // 检查登录状态
      if (!isLoggedIn()) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      // 防止重复点击
      if (this.data.adding) {
        return;
      }

      // 检查数量是否大于0
      if (cartQuantity <= 0) {
        return;
      }

      this.setData({ adding: true });

      const minQuantity = product.min_sale_quantity || 1;
      const newQuantity = cartQuantity - 1;

      console.log('🔽 横向卡片减少数量:', {
        product_id: product.id,
        product_name: product.name || '未知商品',
        currentQuantity: cartQuantity,
        newQuantity,
        minQuantity
      });

      // 🔍 重新检查购物车中的实际数量
      console.log('🔍 重新检查购物车中的实际数量...');
      const { getItemQuantity } = require('../../utils/cart-unified');
      const actualQuantity = await getItemQuantity(product.id);

      console.log('🔍 数量对比:', {
        界面显示数量: cartQuantity,
        实际购物车数量: actualQuantity,
        是否一致: cartQuantity === actualQuantity
      });

      // 使用实际数量计算新数量
      const actualNewQuantity = actualQuantity - 1;

      // 如果新数量小于最小起购数量，直接删除商品
      if (actualNewQuantity < minQuantity) {
        console.log('🗑️ 数量低于最小起购量，删除商品');
        this.removeFromCart(product);
      } else {
        console.log('🔄 更新商品数量');
        this.updateCartItemQuantity(product, actualNewQuantity);
      }
    },

    /**
     * 数量点击事件 - 暂时只显示当前数量
     */
    onQuantityTap() {
      const { cartQuantity } = this.data;

      wx.showToast({
        title: `当前数量: ${cartQuantity}`,
        icon: 'none',
        duration: 1000
      });
    },

    /**
     * 添加商品到购物车
     */
    async addToCart(product, quantity = 1) {
      try {
        // 参数验证
        if (!product || !product.id) {
          console.error('❌ 商品信息无效:', product);
          wx.showToast({
            title: '商品信息错误',
            icon: 'none'
          });
          return;
        }

        const { addToCart } = require('../../utils/cart-unified');

        console.log('➕ 横向卡片添加到购物车:', {
          product_id: product.id,
          product_name: product.name || '未知商品',
          quantity
        });

        // 构造正确的参数格式
        const params = {
          product_id: product.id,
          quantity: quantity
        };

        // 如果有销售单位信息，也一并传入
        if (product.sale_unit && product.sale_unit.id) {
          params.unit_id = product.sale_unit.id;
        }

        const result = await addToCart(params);

        if (result) {
          this.setData({
            cartQuantity: quantity,
            adding: false
          });

          console.log('✅ 横向卡片添加购物车成功');
        } else {
          throw new Error('添加失败');
        }
      } catch (error) {
        console.error('❌ 横向卡片添加购物车失败:', error);
        wx.showToast({
          title: '添加失败，请重试',
          icon: 'none'
        });
        this.setData({ adding: false });
      }
    },

    /**
     * 更新购物车商品数量
     */
    async updateCartItemQuantity(product, quantity) {
      try {
        // 参数验证
        if (!product || !product.id) {
          console.error('❌ 商品信息无效:', product);
          wx.showToast({
            title: '商品信息错误',
            icon: 'none'
          });
          return;
        }

        if (!quantity || quantity < 0) {
          console.error('❌ 数量无效:', quantity);
          wx.showToast({
            title: '数量无效',
            icon: 'none'
          });
          return;
        }

        const { updateCartItemByProductId } = require('../../utils/cart-unified');

        console.log('🔄 横向卡片更新购物车:', {
          product_id: product.id,
          product_name: product.name || '未知商品',
          quantity
        });

        // 确保 _timers 对象存在
        if (!this._timers) {
          this._timers = {};
        }

        // 使用防抖机制
        const timerId = `update_${product.id}`;
        if (this._timers[timerId]) {
          clearTimeout(this._timers[timerId]);
        }

        this._timers[timerId] = setTimeout(async () => {
          try {
            const result = await updateCartItemByProductId(product.id, quantity);

            if (result.success) {
              this.setData({
                cartQuantity: quantity,
                adding: false
              });

              console.log('✅ 横向卡片购物车更新成功');
            } else {
              throw new Error(result.message || '更新失败');
            }
          } catch (error) {
            console.error('❌ 横向卡片购物车更新失败:', error);
            wx.showToast({
              title: '更新失败，请重试',
              icon: 'none'
            });
            this.setData({ adding: false });
          }

          delete this._timers[timerId];
        }, 300);

      } catch (error) {
        console.error('❌ 横向卡片更新购物车异常:', error);
        this.setData({ adding: false });
      }
    },

    /**
     * 从购物车删除商品
     */
    async removeFromCart(product) {
      try {
        // 参数验证
        if (!product || !product.id) {
          console.error('❌ 商品信息无效，无法删除:', product);
          wx.showToast({
            title: '商品信息错误',
            icon: 'none'
          });
          return;
        }

        const { removeCartItemByProductId } = require('../../utils/cart-unified');

        console.log('🗑️ 横向卡片删除商品:', {
          product_id: product.id,
          product_name: product.name || '未知商品'
        });

        const result = await removeCartItemByProductId(product.id);

        if (result.success) {
          this.setData({
            cartQuantity: 0,
            adding: false
          });

          console.log('✅ 横向卡片商品删除成功');
        } else {
          throw new Error(result.message || '删除失败');
        }
      } catch (error) {
        console.error('❌ 横向卡片删除商品失败:', error);
        wx.showToast({
          title: '删除失败，请重试',
          icon: 'none'
        });
        this.setData({ adding: false });
      }
    }
  }
});