<?php

namespace App\Inventory\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class InventoryReportInstance extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'config_id',
        'title',
        'status',
        'parameters',
        'report_date',
        'data_start_date',
        'data_end_date',
        'report_data',
        'summary',
        'charts_data',
        'file_path',
        'file_format',
        'file_size',
        'started_at',
        'completed_at',
        'generation_time',
        'error_message',
        'view_count',
        'download_count',
        'last_accessed_at',
        'generated_by',
    ];

    /**
     * 应该被转换的属性
     */
    protected $casts = [
        'parameters' => 'array',
        'report_data' => 'array',
        'summary' => 'array',
        'charts_data' => 'array',
        'report_date' => 'date',
        'data_start_date' => 'date',
        'data_end_date' => 'date',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'view_count' => 'integer',
        'download_count' => 'integer',
        'generation_time' => 'integer',
        'file_size' => 'integer',
    ];

    /**
     * 获取报表配置
     */
    public function config()
    {
        return $this->belongsTo(InventoryReportConfig::class, 'config_id');
    }

    /**
     * 获取生成者
     */
    public function generator()
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    /**
     * 获取状态文本
     * 
     * @return string
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'generating' => '生成中',
            'completed' => '已完成',
            'failed' => '生成失败',
            'expired' => '已过期',
            default => '未知状态'
        };
    }

    /**
     * 获取文件大小的可读格式
     * 
     * @return string
     */
    public function getFileSizeHumanAttribute()
    {
        if (!$this->file_size) {
            return '-';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $this->file_size;
        $unit = 0;

        while ($size >= 1024 && $unit < count($units) - 1) {
            $size /= 1024;
            $unit++;
        }

        return round($size, 2) . ' ' . $units[$unit];
    }

    /**
     * 获取生成耗时的可读格式
     * 
     * @return string
     */
    public function getGenerationTimeHumanAttribute()
    {
        if (!$this->generation_time) {
            return '-';
        }

        if ($this->generation_time < 60) {
            return $this->generation_time . ' 秒';
        } elseif ($this->generation_time < 3600) {
            return round($this->generation_time / 60, 1) . ' 分钟';
        } else {
            return round($this->generation_time / 3600, 1) . ' 小时';
        }
    }

    /**
     * 检查文件是否存在
     * 
     * @return bool
     */
    public function fileExists()
    {
        return $this->file_path && Storage::exists($this->file_path);
    }

    /**
     * 获取文件下载URL
     * 
     * @return string|null
     */
    public function getDownloadUrl()
    {
        if (!$this->fileExists()) {
            return null;
        }

        return Storage::url($this->file_path);
    }

    /**
     * 增加查看次数
     * 
     * @return void
     */
    public function incrementViewCount()
    {
        $this->increment('view_count');
        $this->update(['last_accessed_at' => now()]);
    }

    /**
     * 增加下载次数
     * 
     * @return void
     */
    public function incrementDownloadCount()
    {
        $this->increment('download_count');
        $this->update(['last_accessed_at' => now()]);
    }

    /**
     * 标记为完成
     * 
     * @param array $data
     * @return void
     */
    public function markAsCompleted($data = [])
    {
        $updateData = array_merge([
            'status' => 'completed',
            'completed_at' => now(),
        ], $data);

        if ($this->started_at) {
            $updateData['generation_time'] = now()->diffInSeconds($this->started_at);
        }

        $this->update($updateData);
    }

    /**
     * 标记为失败
     * 
     * @param string $errorMessage
     * @return void
     */
    public function markAsFailed($errorMessage)
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'completed_at' => now(),
            'generation_time' => $this->started_at ? now()->diffInSeconds($this->started_at) : null,
        ]);
    }

    /**
     * 开始生成
     * 
     * @return void
     */
    public function startGeneration()
    {
        $this->update([
            'status' => 'generating',
            'started_at' => now(),
        ]);
    }

    /**
     * 作用域：已完成的报表
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * 作用域：失败的报表
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * 作用域：生成中的报表
     */
    public function scopeGenerating($query)
    {
        return $query->where('status', 'generating');
    }

    /**
     * 作用域：按日期范围过滤
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('report_date', [$startDate, $endDate]);
    }
} 