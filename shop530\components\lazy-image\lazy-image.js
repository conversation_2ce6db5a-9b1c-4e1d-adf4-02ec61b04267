// 图片懒加载组件
const imageOptimizer = require('../../utils/image');

Component({
  /**
   * 组件属性
   */
  properties: {
    // 图片源地址
    src: {
      type: String,
      value: '',
      observer: 'onSrcChange'
    },
    
    // 图片显示模式
    mode: {
      type: String,
      value: 'aspectFit'
    },
    
    // 是否开启懒加载
    lazyLoad: {
      type: Boolean,
      value: true
    },
    
    // 图片宽度
    width: {
      type: String,
      value: '100%'
    },
    
    // 图片高度
    height: {
      type: String,
      value: '100%'
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    
    // 是否圆形
    rounded: {
      type: Boolean,
      value: false
    },
    
    // 是否显示占位图
          showPlaceholder: {
        type: Boolean,
        value: true
      },
    
    // 占位文本
    placeholderText: {
      type: String,
      value: ''
    },
    
    // 错误文本
    errorText: {
      type: String,
      value: ''
    },
    
    // 是否显示加载指示器
    showLoadingIndicator: {
      type: Boolean,
      value: false
    },
    
    // 是否支持长按显示菜单
    showMenuByLongpress: {
      type: Boolean,
      value: false
    },
    
    // 图片优化选项
    optimize: {
      type: Boolean,
      value: false
    },
    
    // 图片尺寸优化
    optimizeSize: {
      type: String,
      value: 'auto' // small, medium, large, auto
    },
    
    // WebP优化
    enableWebp: {
      type: Boolean,
      value: false
    },
    
    // 占位图 - 不再需要外部图片文件，使用van-icon代替
    placeholder: {
      type: String,
      value: ''
    },
    
    // 错误图片 - 不再需要外部图片文件，使用van-icon代替
    errorImage: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件数据
   */
  data: {
    // 当前显示的图片源
    currentSrc: '',
    
    // 是否已加载
    loaded: false,
    
    // 是否加载中
    loading: false,
    
    // 是否加载错误
    error: false,
    
    // 容器样式
    containerStyle: '',
    
    // 占位图样式
    placeholderStyle: '',
    
    // 图片样式
    imageStyle: ''
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    /**
     * 组件挂载时
     */
    attached() {
      this.initComponent();
    },

    /**
     * 组件卸载时
     */
    detached() {
      // 清理资源
    }
  },

  /**
   * 组件方法
   */
  methods: {
    /**
     * 初始化组件
     */
    initComponent() {
      this.updateStyles();
      this.processImageSrc();
    },

    /**
     * 更新样式
     */
    updateStyles() {
      const { width, height } = this.properties;
      
      const containerStyle = `width: ${width}; height: ${height};`;
      const imageStyle = `width: ${width}; height: ${height};`;
      
      this.setData({
        containerStyle,
        imageStyle,
        placeholderStyle: containerStyle
      });
    },

    /**
     * 处理图片源地址
     */
    processImageSrc() {
      const { src } = this.properties;
      
      // 安全处理 null、undefined 或空字符串
      if (!src || src === null || src === undefined || src === '') {
        this.setData({ 
          currentSrc: '',
          loaded: false,
          error: false,
          loading: false
        });
        return;
      }

      // 避免重复处理相同的图片
      if (this.data.currentSrc === src && (this.data.loaded || this.data.loading)) {
        return;
      }
      
      // 开始加载图片时设置正确的状态
      this.setData({ 
        currentSrc: src,
        loading: true,
        error: false,
        loaded: false
      });
    },

    /**
     * 自动判断图片尺寸
     */
    getAutoSize() {
      const { width, height } = this.properties;
      
      // 根据设置的宽高判断合适的优化尺寸
      const numWidth = parseInt(width);
      const numHeight = parseInt(height);
      
      if (numWidth <= 100 || numHeight <= 100) {
        return 'small';
      } else if (numWidth <= 300 || numHeight <= 300) {
        return 'medium';
      } else {
        return 'large';
      }
    },

    /**
     * 图片源变化监听
     */
    onSrcChange(newSrc, oldSrc) {
      const safeNewSrc = newSrc || '';
      const safeOldSrc = oldSrc || '';
      
      if (safeNewSrc !== safeOldSrc) {
        this.processImageSrc();
      }
    },

    /**
     * 图片加载成功
     */
    onImageLoad(e) {
      this.setData({
        loaded: true,
        loading: false,
        error: false
      });

      // 触发加载成功事件
      this.triggerEvent('load', {
        detail: e.detail,
        src: this.properties.src
      });
    },

    /**
     * 图片加载失败
     */
    onImageError(e) {
      console.log('图片加载失败:', e.detail.errMsg);
      
      this.setData({
        loaded: false,
        loading: false,
        error: true,
        // 不再设置currentSrc为错误图片，因为我们现在使用图标显示错误状态
        // 使用空字符串避免继续加载失败的图片
        currentSrc: ''
      });

      // 触发加载失败事件
      this.triggerEvent('error', {
        detail: e.detail,
        src: this.properties.src
      });
    },

    /**
     * 图片点击事件
     */
    onImageTap(e) {
      // 触发点击事件
      this.triggerEvent('tap', {
        detail: e.detail,
        src: this.properties.src,
        loaded: this.data.loaded
      });
    },

    /**
     * 重新加载图片
     */
    reload() {
      this.processImageSrc();
    },

    /**
     * 预加载图片
     */
    preload() {
      const { src } = this.properties;
      if (src) {
        // 直接预加载原始图片，不进行优化
        imageOptimizer.preloadImages([src]);
      }
    }
  }
}); 