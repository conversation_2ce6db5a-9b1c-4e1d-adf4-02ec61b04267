<?php

namespace App\Upload\Services;

use App\Upload\Contracts\StorageDriverInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class LocalStorageDriver implements StorageDriverInterface
{
    /**
     * 存储磁盘
     *
     * @var string
     */
    protected $disk = 'public';
    
    /**
     * 存储文件
     *
     * @param UploadedFile $file 上传的文件
     * @param string $directory 目标目录
     * @param string $filename 文件名
     * @param array $options 附加选项
     * @return array|null 上传结果
     */
    public function store(UploadedFile $file, string $directory, string $filename, array $options = []): ?array
    {
        try {
            // 检查存储路径
            Log::info('准备上传文件', [
                'original_name' => $file->getClientOriginalName(),
                'directory' => $directory,
                'filename' => $filename,
                'disk' => $this->disk
            ]);
            
            // 确保目录存在
            $fullPath = storage_path('app/' . $this->disk . '/' . $directory);
            if (!file_exists($fullPath)) {
                Log::info('尝试创建目录', ['path' => $fullPath]);
                if (!mkdir($fullPath, 0755, true)) {
                    Log::error('无法创建存储目录', ['path' => $fullPath]);
                    throw new \Exception('无法创建存储目录: ' . $fullPath);
                }
                Log::info('成功创建目录', ['path' => $fullPath]);
            }
            
            // 存储文件
            $path = $file->storeAs($directory, $filename, ['disk' => $this->disk]);
            
            if (!$path) {
                Log::error('文件上传失败', [
                    'original_name' => $file->getClientOriginalName(),
                    'directory' => $directory,
                    'filename' => $filename,
                    'disk' => $this->disk
                ]);
                return null;
            }
            
            // 获取完整URL
            $url = url('storage/' . $path);
            
            Log::info('文件上传成功', [
                'original_name' => $file->getClientOriginalName(),
                'path' => $path,
                'url' => $url
            ]);
            
            return [
                'path' => $path,
                'url' => $url,
                'disk' => $this->disk,
                'directory' => $directory,
                'filename' => $filename,
            ];
        } catch (\Exception $e) {
            Log::error('存储文件异常', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'original_name' => $file->getClientOriginalName(),
                'directory' => $directory,
                'filename' => $filename
            ]);
            return null;
        }
    }
    
    /**
     * 获取文件URL
     *
     * @param string $path 文件路径
     * @return string
     */
    public function getUrl(string $path): string
    {
        try {
            return asset('storage/' . $path);
        } catch (\Exception $e) {
            Log::error('生成本地文件URL失败', [
                'path' => $path,
                'error' => $e->getMessage()
            ]);
            
            return $path;
        }
    }
    
    /**
     * 删除文件
     *
     * @param string $path 文件路径
     * @return bool
     */
    public function delete(string $path): bool
    {
        try {
            return Storage::disk($this->disk)->delete($path);
        } catch (\Exception $e) {
            Log::error('删除本地文件失败', [
                'path' => $path,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * 获取存储驱动名称
     *
     * @return string
     */
    public function getDriverName(): string
    {
        return 'local';
    }
} 