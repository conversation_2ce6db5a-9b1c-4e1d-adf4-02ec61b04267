<?php

namespace App\Region\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class RegionPriceResource extends JsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'product_id' => $this->product_id,
            'region_id' => $this->region_id,
            'price' => $this->price,
            'original_price' => $this->original_price,
            'stock' => $this->stock,
            'status' => $this->status,
            'start_date' => $this->start_date ? $this->start_date->format('Y-m-d H:i:s') : null,
            'end_date' => $this->end_date ? $this->end_date->format('Y-m-d H:i:s') : null,
            'special_conditions' => $this->special_conditions,
            'created_at' => $this->created_at ? $this->created_at->format('Y-m-d H:i:s') : null,
            'updated_at' => $this->updated_at ? $this->updated_at->format('Y-m-d H:i:s') : null,
            
            // 如果加载了关联数据，则包含它们
            'product' => $this->whenLoaded('product', function () {
                // 如果需要使用ProductResource，请确保导入相应的命名空间
                return [
                    'id' => $this->product->id,
                    'name' => $this->product->name,
                    'sku' => $this->product->sku,
                    // 其他需要的商品属性
                ];
            }),
            
            'region' => $this->whenLoaded('region', function () {
                return new RegionResource($this->region);
            }),
            
            // 计算的属性
            'is_valid' => $this->when(method_exists($this, 'isValid'), function () {
                return $this->isValid();
            }),
            
            'has_stock' => $this->when(method_exists($this, 'hasStock'), function () {
                return $this->hasStock(1); // 默认检查是否有1个库存
            }),
            
            'discount_percent' => $this->when($this->original_price > 0, function () {
                // 计算折扣百分比
                return round((1 - $this->price / $this->original_price) * 100);
            }),
        ];
    }
} 