<!-- 购物车页面 - 全新设计 -->
<view class="cart-container {{isEditMode ? 'edit-mode' : ''}}">
  <!-- 骨架屏 -->
  <view wx:if="{{showSkeleton}}" class="skeleton-container">
    <view class="skeleton-header">
      <view class="skeleton-title"></view>
      <view class="skeleton-action"></view>
    </view>
    <view class="skeleton-delivery"></view>
    <view class="skeleton-items">
      <view class="skeleton-item" wx:for="{{[1,2,3]}}" wx:key="*this">
        <view class="skeleton-checkbox"></view>
        <view class="skeleton-image"></view>
        <view class="skeleton-content">
          <view class="skeleton-name"></view>
          <view class="skeleton-spec"></view>
          <view class="skeleton-price-row">
            <view class="skeleton-price"></view>
            <view class="skeleton-stepper"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 主要内容 -->
  <block wx:else>
    <!-- 空购物车状态 -->
    <view wx:if="{{!isLoggedIn}}" class="empty-state login-required">
      <image class="empty-image" src="/images/empty-cart-login.png" mode="aspectFit"></image>
      <view class="empty-title">请先登录</view>
      <view class="empty-desc">登录后查看您的购物车</view>
      <button class="primary-button" bindtap="goToLogin">立即登录</button>
    </view>

    <view wx:elif="{{cartList.length === 0}}" class="empty-state">
      <image class="empty-image" src="/images/empty-cart.png" mode="aspectFit"></image>
      <view class="empty-title">购物车空空如也</view>
      <view class="empty-desc">快去添加心仪的商品吧</view>
      <button class="primary-button" bindtap="goToHome">去购物</button>
    </view>

    <!-- 购物车内容 -->
    <block wx:else>
      <!-- 顶部操作栏 -->
      <view class="cart-header">
        <view class="cart-title">
          <text class="title-text">购物车</text>
          <text class="title-count">{{cartList.length}}</text>
        </view>
        <view class="cart-actions">
          <view class="refresh-btn" bindtap="handleManualRefresh">
            <van-icon name="replay" size="18px" />
          </view>
          <view class="edit-btn" bindtap="toggleEditMode">
            {{isEditMode ? '完成' : '编辑'}}
          </view>
        </view>
      </view>

      <!-- 配送信息 -->
      <view class="delivery-card">
        <view class="delivery-icon">
          <van-icon name="logistics" color="#4CAF50" size="20px" />
        </view>
        <view class="delivery-info">
          <view class="delivery-badge">{{deliveryText}}</view>
          <view class="delivery-date">{{deliveryDate}}</view>
        </view>
      </view>

      <!-- 商品列表 -->
      <view class="cart-list">
        <view class="cart-item {{item.updating ? 'updating' : ''}}" 
              wx:for="{{cartList}}" 
              wx:key="id">
          <!-- 选择框 -->
          <view class="item-select" catch:tap="onItemSelect" data-index="{{index}}">
            <view class="custom-checkbox {{item.selected ? 'checked' : ''}}">
              <van-icon wx:if="{{item.selected}}" name="success" color="#fff" size="12px" />
            </view>
          </view>

          <!-- 商品图片 -->
          <view class="item-image"
                catch:tap="goToProductDetail"
                data-id="{{item.product_id}}">
            <image src="{{item.image || '/images/default-product.png'}}"
                   mode="aspectFill"
                   lazy-load="true"
                   binderror="onImageError"
                   data-index="{{index}}"></image>
          </view>

          <!-- 商品信息 -->
          <view class="item-content">
            <view class="item-header">
              <view class="item-name" catch:tap="goToProductDetail" data-id="{{item.product_id}}">
                {{item.name}}
              </view>
              <view class="item-spec" wx:if="{{item.spec}}">{{item.spec}}</view>
            </view>

            <view class="item-footer">
              <view class="item-price">
                ¥{{item.price}}<text class="price-unit" wx:if="{{item.unit}}">/{{item.unit}}</text>
              </view>

              <view class="item-controls">
                <view class="control-btn {{item.quantity <= 1 ? 'disabled' : ''}}"
                      catch:tap="onQuantityDecrease"
                      data-index="{{index}}">
                  <van-icon name="minus" size="16px" />
                </view>
                <input class="quantity-input"
                       type="number"
                       value="{{item.quantity}}"
                       bindblur="onQuantityInput"
                       bindfocus="onQuantityFocus"
                       data-index="{{index}}"
                       maxlength="3" />
                <view class="control-btn"
                      catch:tap="onQuantityIncrease"
                      data-index="{{index}}">
                  <van-icon name="plus" size="16px" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 推荐商品 -->
      <view class="recommend-section" wx:if="{{recommendList.length > 0}}">
        <view class="section-header">
          <text class="section-title">猜你喜欢</text>
        </view>
        
        <view class="recommend-grid">
          <view class="recommend-item" 
                wx:for="{{recommendList}}" 
                wx:key="id"
                bindtap="onProductTap"
                data-product="{{item}}">
            <image class="recommend-image" 
                   src="{{item.image || '/images/default-product.png'}}"
                   mode="aspectFill"
                   lazy-load="true"></image>
            <view class="recommend-name">{{item.name}}</view>
            <view class="recommend-price-row">
              <view class="recommend-price">
                ¥{{item.price}}
              </view>
              <view class="add-cart-btn" catch:tap="onAddToCart" data-product="{{item}}">
                <van-icon name="cart-o" size="16px" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </block>
  </block>

  <!-- 底部结算栏 -->
  <view class="cart-footer" wx:if="{{cartList.length > 0}}">
    <view class="footer-select">
      <view class="select-all" bindtap="onSelectAll">
        <view class="custom-checkbox {{allSelected ? 'checked' : ''}}">
          <van-icon wx:if="{{allSelected}}" name="success" color="#fff" size="12px" />
        </view>
        <text class="select-text">全选</text>
      </view>
    </view>

    <view class="footer-info" wx:if="{{!isEditMode}}">
      <view class="total-price">
        <text>合计:</text>
        <text class="price-symbol">¥</text>
        <text class="price-value">{{totalPrice}}</text>
      </view>
    </view>

    <view class="footer-action">
      <button class="action-button delete" 
              wx:if="{{isEditMode}}"
              disabled="{{selectedCount === 0}}"
              bindtap="deleteSelectedItems">
        删除 <text wx:if="{{selectedCount > 0}}">({{selectedCount}})</text>
      </button>
      
      <button class="action-button checkout" 
              wx:else
              disabled="{{selectedCount === 0}}"
              bindtap="goToCheckout">
        结算 <text wx:if="{{selectedCount > 0}}">({{selectedCount}})</text>
      </button>
    </view>
  </view>
</view> 