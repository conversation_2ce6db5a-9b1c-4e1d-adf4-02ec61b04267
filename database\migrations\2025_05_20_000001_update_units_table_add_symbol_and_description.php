<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('units', function (Blueprint $table) {
            // 检查字段是否存在，避免重复添加
            if (!Schema::hasColumn('units', 'symbol')) {
                $table->string('symbol')->nullable()->after('is_base')->comment('单位符号');
            }
            
            if (!Schema::hasColumn('units', 'description')) {
                $table->text('description')->nullable()->after('symbol')->comment('单位描述');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('units', function (Blueprint $table) {
            $table->dropColumn(['symbol', 'description']);
        });
    }
}; 