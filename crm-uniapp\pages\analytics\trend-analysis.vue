<template>
	<view class="trend-analysis-container">
		<!-- 页面头部 -->
		<view class="header-section">
			<view class="header-title">
				<text class="title-text">趋势分析</text>
				<text class="subtitle-text">数据趋势与预测分析</text>
			</view>
		</view>

		<!-- 时间范围选择 -->
		<view class="time-filter-section">
			<view class="filter-tabs">
				<view 
					class="filter-tab" 
					v-for="(period, index) in timePeriods" 
					:key="index"
					:class="{ active: selectedPeriod === period.value }"
					@tap="selectPeriod(period.value)"
				>
					<text class="tab-text">{{ period.label }}</text>
				</view>
			</view>
		</view>

		<!-- 核心指标趋势 -->
		<view class="metrics-trend-section">
			<view class="section-title">
				<text class="title-text">📈 核心指标趋势</text>
			</view>
			<view class="metrics-tabs">
				<view 
					class="metric-tab" 
					v-for="(metric, index) in metricTypes" 
					:key="index"
					:class="{ active: selectedMetric === metric.value }"
					@tap="selectMetric(metric.value)"
				>
					<text class="tab-text">{{ metric.label }}</text>
				</view>
			</view>
			<view class="trend-chart">
				<view class="chart-header">
					<text class="chart-title">{{ getCurrentMetricTitle() }}</text>
					<text class="chart-subtitle">{{ selectedPeriod === 'last_7_days' ? '最近7天' : selectedPeriod === 'last_30_days' ? '最近30天' : '最近90天' }}</text>
				</view>
				<view class="line-chart">
					<view class="chart-grid">
						<view class="grid-line" v-for="i in 5" :key="i"></view>
					</view>
					<view class="chart-data">
						<view class="data-point" v-for="(point, index) in currentTrendData" :key="index" 
							:style="{ left: (index / (currentTrendData.length - 1)) * 100 + '%', bottom: point.percentage + '%' }">
							<view class="point-dot"></view>
							<text class="point-value">{{ point.value }}</text>
						</view>
					</view>
					<view class="chart-labels">
						<text class="label-item" v-for="(point, index) in currentTrendData" :key="index">{{ point.label }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 增长率分析 -->
		<view class="growth-analysis-section">
			<view class="section-title">
				<text class="title-text">📊 增长率分析</text>
			</view>
			<view class="growth-metrics">
				<view class="growth-item" v-for="(item, index) in growthMetrics" :key="index">
					<view class="growth-icon" :class="item.trend">
						<text class="icon-text">{{ item.icon }}</text>
					</view>
					<view class="growth-info">
						<text class="growth-title">{{ item.title }}</text>
						<text class="growth-value" :class="item.trend">{{ item.value }}</text>
						<text class="growth-desc">{{ item.description }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 预测分析 -->
		<view class="forecast-section">
			<view class="section-title">
				<text class="title-text">🔮 预测分析</text>
			</view>
			<view class="forecast-chart">
				<view class="forecast-header">
					<text class="forecast-title">未来30天趋势预测</text>
					<text class="forecast-subtitle">基于历史数据和机器学习算法</text>
				</view>
				<view class="forecast-metrics">
					<view class="forecast-item" v-for="(item, index) in forecastData" :key="index">
						<view class="forecast-icon" :class="item.trend">
							<text class="icon-text">{{ item.icon }}</text>
						</view>
						<view class="forecast-info">
							<text class="forecast-name">{{ item.name }}</text>
							<text class="forecast-value">{{ item.predicted }}</text>
							<text class="forecast-confidence">置信度: {{ item.confidence }}%</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import analyticsApi from '../../api/analytics.js'

export default {
	data() {
		return {
			loading: false,
			selectedPeriod: 'last_30_days',
			selectedMetric: 'sales',
			selectedComparison: 'mom',
			timePeriods: [
				{ label: '最近7天', value: 'last_7_days' },
				{ label: '最近30天', value: 'last_30_days' },
				{ label: '最近90天', value: 'last_90_days' }
			],
			metricTypes: [
				{ label: '销售额', value: 'sales' },
				{ label: '订单数', value: 'orders' },
				{ label: '客户数', value: 'customers' },
				{ label: '客单价', value: 'avg_order' }
			],
			comparisonTypes: [
				{ label: '环比', value: 'mom' },
				{ label: '同比', value: 'yoy' }
			],
			trendData: {},
			growthMetrics: [],
			comparisonData: [],
			forecastData: [],
			anomalies: []
		}
	},
	
	computed: {
		currentTrendData() {
			return this.trendData[this.selectedMetric] || []
		}
	},
	
	onLoad() {
		this.loadTrendAnalysis()
	},
	
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 刷新数据
		async refreshData() {
			await this.loadTrendAnalysis()
			uni.showToast({
				title: '刷新成功',
				icon: 'success',
				duration: 1500
			})
		},
		
		// 选择时间周期
		selectPeriod(period) {
			this.selectedPeriod = period
			this.loadTrendAnalysis()
		},
		
		// 选择指标类型
		selectMetric(metric) {
			this.selectedMetric = metric
		},
		
		// 选择对比类型
		selectComparison(comparison) {
			this.selectedComparison = comparison
			this.loadComparisonData()
		},
		
		// 获取当前指标标题
		getCurrentMetricTitle() {
			const titles = {
				sales: '销售额趋势',
				orders: '订单数趋势',
				customers: '客户数趋势',
				avg_order: '客单价趋势'
			}
			return titles[this.selectedMetric] || '趋势分析'
		},
		
		// 加载趋势分析数据
		async loadTrendAnalysis() {
			this.loading = true
			try {
				const params = {
					period: this.selectedPeriod
				}
				const response = await analyticsApi.getTrendAnalysis(params)
				
				this.trendData = response.data.trends || {}
				this.growthMetrics = response.data.growth || []
				this.forecastData = response.data.forecast || []
				this.anomalies = response.data.anomalies || []
				
				this.loadComparisonData()
				
			} catch (error) {
				console.error('加载趋势分析数据失败:', error)
				uni.showToast({
					title: '加载趋势分析数据失败',
					icon: 'none',
					duration: 2000
				})
				// 清空数据
				this.trendData = {}
				this.growthMetrics = []
				this.forecastData = []
				this.anomalies = []
			} finally {
				this.loading = false
			}
		},
		
		// 加载对比数据
		async loadComparisonData() {
			try {
				const params = {
					period: this.selectedPeriod,
					type: this.selectedComparison
				}
				const response = await analyticsApi.getComparisonAnalysis(params)
				this.comparisonData = response.data.comparison || []
			} catch (error) {
				console.error('加载对比数据失败:', error)
				this.comparisonData = []
			}
		},
		
		// 处理异常
		handleAnomaly(anomaly) {
			uni.showModal({
				title: '异常处理',
				content: `异常类型：${anomaly.title}\n\n详细描述：${anomaly.description}\n\n建议处理方案：${anomaly.suggestion || '请联系技术支持'}`,
				showCancel: true,
				confirmText: '已处理',
				cancelText: '稍后处理',
				success: (res) => {
					if (res.confirm) {
						// 标记异常为已处理
						anomaly.handled = true
						uni.showToast({
							title: '已标记为处理',
							icon: 'success'
						})
					}
				}
			})
		}
	}
}
</script>

<style scoped>
.trend-analysis-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 导航栏 */
.navbar {
	background: #ffffff;
	border-bottom: 2rpx solid #f0f0f0;
	position: sticky;
	top: 0;
	z-index: 100;
}

.navbar-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 32rpx;
}

.nav-left, .nav-right {
	width: 120rpx;
}

.nav-icon {
	font-size: 36rpx;
	color: #007AFF;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
}

.refresh-btn {
	font-size: 28rpx;
	color: #007AFF;
}

/* 时间筛选 */
.time-filter-section {
	background: #ffffff;
	padding: 24rpx 32rpx;
	margin-bottom: 20rpx;
}

.filter-tabs {
	display: flex;
	justify-content: space-around;
}

.filter-tab {
	padding: 16rpx 32rpx;
	border-radius: 20rpx;
	background: #f8f9fa;
	transition: all 0.3s ease;
}

.filter-tab.active {
	background: #007AFF;
}

.filter-tab.active .tab-text {
	color: #ffffff;
}

.tab-text {
	font-size: 28rpx;
	color: #666666;
}

/* 通用样式 */
.section-title {
	padding: 32rpx;
	background: #ffffff;
	border-bottom: 2rpx solid #f0f0f0;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
}

/* 指标趋势 */
.metrics-trend-section {
	background: #ffffff;
	margin-bottom: 20rpx;
}

.metrics-tabs {
	display: flex;
	padding: 24rpx 32rpx 0;
}

.metric-tab {
	flex: 1;
	padding: 16rpx 0;
	text-align: center;
	border-bottom: 4rpx solid transparent;
	transition: all 0.3s ease;
}

.metric-tab.active {
	border-bottom-color: #007AFF;
}

.metric-tab.active .tab-text {
	color: #007AFF;
	font-weight: 600;
}

.trend-chart {
	padding: 32rpx;
}

.chart-header {
	text-align: center;
	margin-bottom: 32rpx;
}

.chart-title {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.chart-subtitle {
	font-size: 24rpx;
	color: #666666;
}

.line-chart {
	position: relative;
	height: 400rpx;
	margin-bottom: 32rpx;
}

.chart-grid {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 60rpx;
}

.grid-line {
	height: 2rpx;
	background: #f0f0f0;
	margin-bottom: 20%;
}

.chart-data {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 60rpx;
}

.data-point {
	position: absolute;
	transform: translateX(-50%);
}

.point-dot {
	width: 16rpx;
	height: 16rpx;
	background: #007AFF;
	border-radius: 50%;
	margin: 0 auto 8rpx;
}

.point-value {
	font-size: 20rpx;
	color: #333333;
	text-align: center;
	display: block;
}

.chart-labels {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 60rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.label-item {
	font-size: 24rpx;
	color: #666666;
}

/* 增长分析 */
.growth-analysis-section {
	background: #ffffff;
	margin-bottom: 20rpx;
}

.growth-metrics {
	padding: 32rpx;
}

.growth-item {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
}

.growth-item:last-child {
	border-bottom: none;
}

.growth-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.growth-icon.up {
	background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.growth-icon.down {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.icon-text {
	font-size: 36rpx;
}

.growth-info {
	flex: 1;
}

.growth-title {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.growth-value {
	display: block;
	font-size: 48rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
}

.growth-value.up {
	color: #43e97b;
}

.growth-value.down {
	color: #ff6b6b;
}

.growth-desc {
	font-size: 24rpx;
	color: #666666;
}

/* 预测分析 */
.forecast-section {
	background: #ffffff;
	margin-bottom: 20rpx;
}

.forecast-chart {
	padding: 32rpx;
}

.forecast-header {
	text-align: center;
	margin-bottom: 32rpx;
}

.forecast-title {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.forecast-subtitle {
	font-size: 24rpx;
	color: #666666;
}

.forecast-metrics {
	
}

.forecast-item {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
}

.forecast-item:last-child {
	border-bottom: none;
}

.forecast-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
	background: linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 100%);
}

.forecast-info {
	flex: 1;
}

.forecast-name {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.forecast-value {
	display: block;
	font-size: 48rpx;
	font-weight: 600;
	color: #007AFF;
	margin-bottom: 8rpx;
}

.forecast-confidence {
	font-size: 24rpx;
	color: #666666;
}
</style> 