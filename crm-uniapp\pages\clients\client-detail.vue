<template>
	<view class="client-detail-container">
		<!-- 客户基本信息 -->
		<view class="client-info-card">
			<view class="client-header">
				<view class="client-avatar">
					<text class="avatar-text">{{ getAvatarText(clientInfo) }}</text>
				</view>
				<view class="client-basic">
					<text class="client-name">{{ clientInfo.merchant_name || clientInfo.name || '未知商户' }}</text>
					<text class="client-contact" v-if="clientInfo.merchant_name">联系人：{{ clientInfo.name }}</text>
					<text class="client-phone">{{ clientInfo.phone }}</text>
					<text class="client-status" :class="getStatusClass(clientInfo.status)">{{ getStatusText(clientInfo.status) }}</text>
				</view>
				<view class="client-actions">
					<button class="action-btn primary" @tap="createOrder">代客下单</button>
					<button class="action-btn secondary" @tap="addFollowUp">添加跟进</button>
				</view>
			</view>
		</view>
		
		<!-- 客户统计 -->
		<view class="stats-card">
			<view class="card-title">客户统计</view>
			<view class="stats-grid">
				<view class="stat-item">
					<text class="stat-number">{{ clientStats.total_orders || 0 }}</text>
					<text class="stat-label">总订单数</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">¥{{ clientStats.total_amount || 0 }}</text>
					<text class="stat-label">总消费金额</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ clientStats.avg_order_amount || 0 }}</text>
					<text class="stat-label">平均订单金额</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ clientStats.follow_up_count || 0 }}</text>
					<text class="stat-label">跟进次数</text>
				</view>
			</view>
		</view>
		
		<!-- 标签页切换 -->
		<view class="tabs-container">
			<view class="tabs-header">
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'orders' }"
					@tap="switchTab('orders')"
				>
					<text class="tab-text">订单记录</text>
				</view>
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'behavior' }"
					@tap="switchTab('behavior')"
				>
					<text class="tab-text">行为分析</text>
				</view>
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'addresses' }"
					@tap="switchTab('addresses')"
				>
					<text class="tab-text">收货地址</text>
				</view>
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'followups' }"
					@tap="switchTab('followups')"
				>
					<text class="tab-text">跟进记录</text>
				</view>
			</view>
			
			<!-- 订单记录 -->
			<view class="tab-content" v-if="activeTab === 'orders'">
				<view class="order-list" v-if="orderList.length > 0">
					<view class="order-item" v-for="order in orderList" :key="order.id" @tap="goToOrderDetail(order.id)">
						<view class="order-header">
							<text class="order-no">{{ order.order_no }}</text>
							<text class="order-status" :class="getOrderStatusClass(order.status)">{{ getOrderStatusText(order.status) }}</text>
						</view>
						<view class="order-info">
							<text class="order-amount">金额：¥{{ order.total }}</text>
							<text class="order-time">{{ formatTime(order.created_at) }}</text>
						</view>
						<view class="order-items" v-if="order.items && order.items.length > 0">
							<text class="items-summary">{{ getOrderItemsSummary(order.items) }}</text>
						</view>
					</view>
				</view>
				<view class="empty-state" v-else>
					<text class="empty-text">暂无订单记录</text>
				</view>
			</view>

			<!-- 行为分析 -->
			<view class="tab-content" v-if="activeTab === 'behavior'">
				<view class="behavior-analysis" v-if="behaviorData">
					<!-- 行为概览 -->
					<view class="behavior-overview">
						<view class="overview-title">
							<text>📊 行为概览</text>
						</view>
						<view class="behavior-metrics">
							<view class="metric-item">
								<text class="metric-number">{{ behaviorData.total_sessions || 0 }}</text>
								<text class="metric-label">访问次数</text>
							</view>
							<view class="metric-item">
								<text class="metric-number">{{ formatDuration(behaviorData.avg_session_duration) }}</text>
								<text class="metric-label">平均停留</text>
							</view>
							<view class="metric-item">
								<text class="metric-number">{{ behaviorData.page_views || 0 }}</text>
								<text class="metric-label">页面浏览</text>
							</view>
							<view class="metric-item">
								<text class="metric-number">{{ behaviorData.product_views || 0 }}</text>
								<text class="metric-label">商品浏览</text>
							</view>
						</view>
					</view>

					<!-- 购买偏好 -->
					<view class="purchase-preference" v-if="behaviorData.category_preferences">
						<view class="preference-title">
							<text>🛒 购买偏好</text>
						</view>
						<view class="preference-list">
							<view class="preference-item" v-for="(count, category) in behaviorData.category_preferences" :key="category">
								<text class="category-name">{{ category }}</text>
								<view class="preference-bar">
									<view class="bar-fill" :style="{ width: getPreferenceWidth(count) }"></view>
								</view>
								<text class="preference-count">{{ count }}次</text>
							</view>
						</view>
					</view>

					<!-- 行为洞察 -->
					<view class="behavior-insights">
						<view class="insights-title">
							<text>💡 行为洞察</text>
						</view>
						<view class="insights-list">
							<view class="insight-card" v-for="insight in generateBehaviorInsights()" :key="insight.id">
								<view class="insight-icon">{{ insight.icon }}</view>
								<view class="insight-content">
									<text class="insight-title">{{ insight.title }}</text>
									<text class="insight-desc">{{ insight.description }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="empty-state" v-else>
					<text class="empty-text">暂无行为数据</text>
				</view>
			</view>
			
			<!-- 收货地址 -->
			<view class="tab-content" v-if="activeTab === 'addresses'">
				<view class="address-list" v-if="addressList.length > 0">
					<view class="address-item" v-for="address in addressList" :key="address.id">
						<view class="address-header">
							<text class="contact-name">{{ address.contact_name }}</text>
							<text class="contact-phone">{{ address.contact_phone }}</text>
							<view class="default-badge" v-if="address.is_default">默认</view>
						</view>
						<text class="address-detail">{{ getFullAddress(address) }}</text>
					</view>
				</view>
				<view class="empty-state" v-else>
					<text class="empty-text">暂无地址信息</text>
				</view>
			</view>
			
			<!-- 跟进记录 -->
			<view class="tab-content" v-if="activeTab === 'followups'">
				<view class="followup-list" v-if="followUpList.length > 0">
					<view class="followup-item" v-for="followup in followUpList" :key="followup.id">
						<view class="followup-header">
							<text class="followup-type">{{ getFollowUpTypeText(followup.type) }}</text>
							<text class="followup-time">{{ formatTime(followup.created_at) }}</text>
						</view>
						<text class="followup-content">{{ followup.content }}</text>
						<text class="followup-agent" v-if="followup.agent">跟进人：{{ followup.agent.name }}</text>
					</view>
				</view>
				<view class="empty-state" v-else>
					<text class="empty-text">暂无跟进记录</text>
				</view>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
import clientApi from '../../api/client.js'
import orderApi from '../../api/order.js'
import analyticsApi from '../../api/analytics.js'
import config from '../../utils/config.js'
import { formatDateTime, formatDate } from '../../utils/date-formatter.js'

export default {
	data() {
		return {
			clientId: null,
			clientInfo: {},
			clientStats: {},
			activeTab: 'orders',
			orderList: [],
			addressList: [],
			followUpList: [],
			behaviorData: null,
			loading: false
		}
	},
	
	onLoad(options) {
		if (options.id) {
			this.clientId = options.id
			this.loadClientDetail()
		}
	},
	
	methods: {
		// 加载客户详情
		async loadClientDetail() {
			if (!this.clientId) return
			
			this.loading = true
			try {
				// 加载客户基本信息
				const clientResponse = await clientApi.getClientDetail(this.clientId)
				this.clientInfo = clientResponse.data
				
				// 加载客户统计
				const statsResponse = await clientApi.getClientStats(this.clientId)
				this.clientStats = statsResponse.data
				
				// 根据当前标签页加载对应数据
				this.loadTabData()
				
			} catch (error) {
				console.error('加载客户详情失败:', error)
				uni.showToast({
					title: '加载客户详情失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 切换标签页
		switchTab(tab) {
			this.activeTab = tab
			this.loadTabData()
		},
		
		// 加载标签页数据
		async loadTabData() {
			if (!this.clientId) return
			
			try {
				switch (this.activeTab) {
					case 'orders':
						await this.loadOrderList()
						break
					case 'addresses':
						await this.loadAddressList()
						break
					case 'followups':
						await this.loadFollowUpList()
						break
					case 'behavior':
						await this.loadBehaviorData()
						break
				}
			} catch (error) {
				console.error('加载标签页数据失败:', error)
			}
		},
		
		// 加载订单列表
		async loadOrderList() {
			const response = await orderApi.getClientOrders(this.clientId)
			this.orderList = response.data.data || response.data || []
		},
		
		// 加载地址列表
		async loadAddressList() {
			const response = await clientApi.getClientAddresses(this.clientId)
			this.addressList = response.data || []
		},
		
		// 加载跟进记录
		async loadFollowUpList() {
			const response = await clientApi.getClientFollowUps(this.clientId)
			this.followUpList = response.data || []
		},
		
		// 加载行为分析数据
		async loadBehaviorData() {
			try {
				const response = await analyticsApi.getClientBehavior(this.clientId)
				this.behaviorData = response.data || {}
			} catch (error) {
				console.error('加载行为分析数据失败:', error)
				this.behaviorData = null
			}
		},
		
		// 获取头像文本
		getAvatarText(client) {
			if (client.merchant_name) {
				return client.merchant_name.charAt(0)
			}
			return (client.name || 'U').charAt(0)
		},
		
		// 获取状态样式类
		getStatusClass(status) {
			const statusMap = {
				'active': 'status-active',
				'inactive': 'status-inactive',
				'blocked': 'status-blocked'
			}
			return statusMap[status] || 'status-default'
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'active': '正常',
				'inactive': '未激活',
				'blocked': '已禁用'
			}
			return statusMap[status] || status
		},
		
		// 获取订单状态样式类
		getOrderStatusClass(status) {
			const statusMap = {
				'pending': 'order-pending',
				'paid': 'order-paid',
				'shipped': 'order-shipped',
				'delivered': 'order-delivered',
				'cancelled': 'order-cancelled'
			}
			return statusMap[status] || 'order-default'
		},
		
		// 获取订单状态文本
		getOrderStatusText(status) {
			return config.orderStatus[status] || status
		},
		
		// 获取跟进类型文本
		getFollowUpTypeText(type) {
			const typeMap = {
				'phone': '电话跟进',
				'visit': '上门拜访',
				'wechat': '微信沟通',
				'email': '邮件联系',
				'other': '其他'
			}
			return typeMap[type] || type
		},
		
		// 格式化时间
		formatTime(timeStr) {
			return formatDateTime(timeStr)
		},
		
		// 获取订单商品摘要
		getOrderItemsSummary(items) {
			if (!items || items.length === 0) return ''
			if (items.length === 1) {
				return items[0].product_name
			}
			return `${items[0].product_name} 等${items.length}件商品`
		},
		
		// 生成行为洞察
		generateBehaviorInsights() {
			if (!this.behaviorData) return []
			
			const insights = []
			
			// 基于访问频次生成洞察
			if (this.behaviorData.total_sessions > 10) {
				insights.push({
					id: 'high_activity',
					icon: '🔥',
					title: '高活跃用户',
					description: `近期访问${this.behaviorData.total_sessions}次，是活跃用户`
				})
			}
			
			// 基于停留时间生成洞察
			if (this.behaviorData.avg_session_duration > 300) {
				insights.push({
					id: 'engaged_user',
					icon: '⏰',
					title: '深度参与',
					description: `平均停留时间${this.formatDuration(this.behaviorData.avg_session_duration)}，参与度很高`
				})
			}
			
			// 基于商品浏览生成洞察
			if (this.behaviorData.product_views > 20) {
				insights.push({
					id: 'product_explorer',
					icon: '🔍',
					title: '商品探索者',
					description: `浏览了${this.behaviorData.product_views}个商品，购买意向强烈`
				})
			}
			
			return insights
		},
		
		// 获取偏好宽度百分比
		getPreferenceWidth(count) {
			if (!this.behaviorData || !this.behaviorData.category_preferences) return '0%'
			
			const maxCount = Math.max(...Object.values(this.behaviorData.category_preferences))
			const percentage = maxCount > 0 ? (count / maxCount) * 100 : 0
			return `${Math.max(percentage, 5)}%` // 最小5%确保可见
		},
		
		// 格式化时长
		formatDuration(seconds) {
			if (!seconds) return '0s'
			
			const hours = Math.floor(seconds / 3600)
			const minutes = Math.floor((seconds % 3600) / 60)
			const secs = Math.floor(seconds % 60)
			
			if (hours > 0) {
				return `${hours}h${minutes}m`
			} else if (minutes > 0) {
				return `${minutes}m${secs}s`
			} else {
				return `${secs}s`
			}
		},
		
		// 获取完整地址
		getFullAddress(address) {
			const parts = [
				address.province,
				address.city,
				address.district,
				address.detail
			].filter(part => part && part.trim())
			
			return parts.join(' ')
		},
		
		// 拨打电话
		callClient() {
			if (this.clientInfo.phone) {
				uni.makePhoneCall({
					phoneNumber: this.clientInfo.phone
				})
			}
		},
		
		// 创建订单
		createOrder() {
			uni.navigateTo({
				url: `/pages/proxy-order/proxy-order?clientId=${this.clientId}&clientName=${encodeURIComponent(this.clientInfo.merchant_name || this.clientInfo.name)}&clientPhone=${this.clientInfo.phone}`
			})
		},
		
		// 跳转到订单详情
		goToOrderDetail(orderId) {
			uni.navigateTo({
				url: `/pages/orders/order-detail?id=${orderId}`
			})
		},
		
		// 添加跟进
		addFollowUp() {
			// TODO: 实现添加跟进功能
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.client-detail-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.client-info-card {
	background: white;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.client-header {
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
}

.client-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.avatar-text {
	color: white;
	font-size: 36rpx;
	font-weight: bold;
}

.client-basic {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.client-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.client-contact {
	font-size: 28rpx;
	color: #666;
}

.client-phone {
	font-size: 28rpx;
	color: #666;
}

.client-status {
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	align-self: flex-start;
	
	&.status-active {
		background: #e8f5e8;
		color: #52c41a;
	}
	
	&.status-inactive {
		background: #fff7e6;
		color: #fa8c16;
	}
	
	&.status-blocked {
		background: #fff2f0;
		color: #ff4d4f;
	}
}

.client-actions {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.action-btn {
	padding: 16rpx 24rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	border: none;
	
	&.primary {
		background: #1890ff;
		color: white;
	}
	
	&.secondary {
		background: #f0f0f0;
		color: #333;
	}
	
	&.small {
		padding: 8rpx 16rpx;
		font-size: 24rpx;
	}
	
	&.danger {
		background: #ff4d4f;
		color: white;
	}
}

.stats-card {
	background: white;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.stats-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.stat-item {
	text-align: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.stat-number {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #1890ff;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}

.tabs-container {
	background: white;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.tabs-header {
	display: flex;
	border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
	flex: 1;
	padding: 30rpx 20rpx;
	text-align: center;
	background: #fafafa;
	
	&.active {
		background: white;
		border-bottom: 4rpx solid #1890ff;
	}
}

.tab-text {
	font-size: 28rpx;
	color: #666;
	
	.tab-item.active & {
		color: #1890ff;
		font-weight: bold;
	}
}

.tab-content {
	padding: 30rpx;
}

.order-list, .address-list, .followup-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.order-item, .address-item, .followup-item {
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: 1rpx solid #e8e8e8;
}

.order-header, .address-header, .followup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.order-no {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.order-status {
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	
	&.order-pending {
		background: #fff7e6;
		color: #fa8c16;
	}
	
	&.order-paid {
		background: #e6f7ff;
		color: #1890ff;
	}
	
	&.order-delivered {
		background: #e8f5e8;
		color: #52c41a;
	}
	
	&.order-cancelled {
		background: #fff2f0;
		color: #ff4d4f;
	}
}

.order-info {
	display: flex;
	justify-content: space-between;
	margin-bottom: 8rpx;
}

.order-amount, .order-time {
	font-size: 24rpx;
	color: #666;
}

.items-summary {
	font-size: 24rpx;
	color: #999;
}

.contact-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.contact-phone {
	font-size: 24rpx;
	color: #666;
}

.default-badge {
	font-size: 20rpx;
	padding: 2rpx 8rpx;
	background: #1890ff;
	color: white;
	border-radius: 8rpx;
}

.address-detail {
	font-size: 26rpx;
	color: #666;
	margin: 12rpx 0;
}

.followup-type {
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	background: #e6f7ff;
	color: #1890ff;
	border-radius: 12rpx;
}

.followup-time {
	font-size: 24rpx;
	color: #999;
}

.followup-content {
	font-size: 28rpx;
	color: #333;
	margin: 12rpx 0;
	line-height: 1.5;
}

.followup-agent {
	font-size: 24rpx;
	color: #666;
}

.empty-state {
	text-align: center;
	padding: 60rpx 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-text {
	font-size: 28rpx;
	color: #666;
}

/* 行为分析样式 */
.behavior-analysis {
	padding: 32rpx;
}

.behavior-overview {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
}

.overview-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
}

.behavior-metrics {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
}

.metric-item {
	text-align: center;
	padding: 16rpx;
	background: #ffffff;
	border-radius: 8rpx;
}

.metric-number {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #007AFF;
	margin-bottom: 8rpx;
}

.metric-label {
	font-size: 24rpx;
	color: #666666;
}

.purchase-preference {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
}

.preference-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
}

.preference-list {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.preference-item {
	display: flex;
	align-items: center;
	padding: 12rpx;
	background: #ffffff;
	border-radius: 8rpx;
}

.category-name {
	width: 120rpx;
	font-size: 24rpx;
	color: #333333;
}

.preference-bar {
	flex: 1;
	height: 16rpx;
	background: #e9ecef;
	border-radius: 8rpx;
	margin: 0 16rpx;
	overflow: hidden;
}

.bar-fill {
	height: 100%;
	background: linear-gradient(90deg, #007AFF 0%, #5856D6 100%);
	border-radius: 8rpx;
	transition: width 0.3s ease;
}

.preference-count {
	font-size: 24rpx;
	color: #666666;
	min-width: 60rpx;
	text-align: right;
}

.behavior-insights {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
}

.insights-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
}

.insights-list {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.insight-card {
	display: flex;
	align-items: center;
	padding: 16rpx;
	background: #ffffff;
	border-radius: 8rpx;
}

.insight-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
}

.insight-content {
	flex: 1;
}

.insight-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
}

.insight-desc {
	font-size: 24rpx;
	color: #666666;
	line-height: 1.4;
}
</style> 