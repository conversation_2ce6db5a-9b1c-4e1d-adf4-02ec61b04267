<?php

namespace App\WechatMp\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ConfigController extends Controller
{
    /**
     * 获取微信小程序配置
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMiniProgramConfig()
    {
        // 从配置文件或数据库中获取微信小程序配置
        $config = [
            'appId' => config('wechat.mini_program.app_id'),
            'showCartBadge' => true,
            'enableShare' => true,
            'shareTitle' => '天新商城 - 您身边的优质商城',
            'shareImage' => '/static/images/share-default.png',
            'customerServicePhone' => config('shop.customer_service_phone', '************'),
            'aboutUs' => config('shop.about_us', '天新商城是一家专注于提供优质商品和服务的线上平台'),
        ];

        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $config
        ]);
    }
} 