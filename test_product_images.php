<?php

/**
 * 商品图片测试脚本
 * 用于检查商品图片数据的获取和显示
 */

require_once 'vendor/autoload.php';

use App\Product\Models\Product;
use App\Product\Models\ProductImage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🖼️  商品图片测试脚本\n";
echo "==================\n\n";

try {
    // 1. 检查数据库连接
    echo "1. 检查数据库连接...\n";
    DB::connection()->getPdo();
    echo "✅ 数据库连接正常\n\n";

    // 2. 检查商品图片表
    echo "2. 检查商品图片表...\n";
    $imageCount = ProductImage::count();
    echo "✅ 商品图片表存在，共有 {$imageCount} 条图片记录\n\n";

    // 3. 获取前5个商品及其图片信息
    echo "3. 获取商品图片信息...\n";
    $products = Product::with(['images'])->take(5)->get();
    
    foreach ($products as $index => $product) {
        echo "商品 " . ($index + 1) . ": {$product->name} (ID: {$product->id})\n";
        
        // 显示原始图片数据
        if ($product->images->count() > 0) {
            echo "  📸 关联图片 ({$product->images->count()} 张):\n";
            foreach ($product->images as $image) {
                echo "    - ID: {$image->id}\n";
                echo "      URL: {$image->url}\n";
                echo "      Path: {$image->path}\n";
                echo "      主图: " . ($image->is_main ? '是' : '否') . "\n";
                echo "      状态: " . ($image->status ? '启用' : '禁用') . "\n";
                echo "      排序: {$image->sort}\n";
                echo "\n";
            }
        } else {
            echo "  ❌ 没有关联图片\n";
        }
        
        // 测试 getCoverUrlAttribute 方法
        $coverUrl = $product->cover_url;
        echo "  🎯 主图URL (cover_url): {$coverUrl}\n";
        
        // 检查图片文件是否存在
        if ($coverUrl) {
            if (filter_var($coverUrl, FILTER_VALIDATE_URL)) {
                echo "  🌐 图片URL格式正确\n";
            } else {
                echo "  ⚠️  图片URL格式可能有问题\n";
            }
        }
        
        echo "\n" . str_repeat("-", 50) . "\n\n";
    }

    // 4. 检查图片存储配置
    echo "4. 检查图片存储配置...\n";
    $defaultImage = config('app.default_product_image', '/images/default-product.png');
    echo "默认商品图片: {$defaultImage}\n";
    
    $storageUrl = config('app.url') . '/storage';
    echo "存储URL前缀: {$storageUrl}\n\n";

    // 5. 测试图片处理逻辑
    echo "5. 测试图片处理逻辑...\n";
    $testProduct = $products->first();
    if ($testProduct) {
        echo "测试商品: {$testProduct->name}\n";
        
        // 模拟前端图片处理逻辑
        $image = '';
        
        if ($testProduct->images->count() > 0) {
            $mainImage = $testProduct->images->where('is_main', true)->first();
            if ($mainImage) {
                $image = $mainImage->url ?: $mainImage->path;
            } else {
                $firstImage = $testProduct->images->first();
                $image = $firstImage->url ?: $firstImage->path;
            }
        }
        
        if (!$image) {
            $image = $testProduct->cover_url ?? '';
        }
        
        echo "处理后的图片URL: {$image}\n";
        
        // 检查图片是否需要URL前缀
        if ($image && !str_starts_with($image, 'http') && !str_starts_with($image, '/')) {
            $fullImage = "/storage/{$image}";
            echo "添加前缀后: {$fullImage}\n";
        }
    }

    echo "\n✅ 测试完成！\n";

} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
