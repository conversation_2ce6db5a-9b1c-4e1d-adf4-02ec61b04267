<?php

namespace App\WechatPayment\Models;

use App\Order\Models\Order;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WechatServicePayment extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'provider_id',
        'sub_merchant_id',
        'order_id',
        'out_trade_no',
        'transaction_id',
        'total_fee',
        'service_fee',
        'settlement_fee',
        'trade_type',
        'trade_state',
        'pay_time',
        'attach',
        'notify_data',
        'prepay_data',
        'error_message',
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'total_fee' => 'float',
        'service_fee' => 'float',
        'settlement_fee' => 'float',
        'pay_time' => 'datetime',
        'notify_data' => 'array',
        'prepay_data' => 'array',
    ];

    /**
     * 获取所属的订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * 获取所属的子商户
     */
    public function subMerchant()
    {
        return $this->belongsTo(WechatSubMerchant::class, 'sub_merchant_id');
    }

    /**
     * 获取所属的服务商
     */
    public function provider()
    {
        return $this->belongsTo(WechatServiceProvider::class, 'provider_id');
    }

    /**
     * 获取相关的退款记录
     */
    public function refunds()
    {
        return $this->hasMany(WechatServiceRefund::class, 'payment_id');
    }
}