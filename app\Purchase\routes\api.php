<?php

use App\Purchase\Http\Controllers\PurchaseOrderController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'api/purchase-orders', 'middleware' => ['api']], function () {
    Route::get('/', [PurchaseOrderController::class, 'index']);
    Route::post('/', [PurchaseOrderController::class, 'store']);
    Route::get('/stats', [PurchaseOrderController::class, 'stats']);
    Route::get('/{id}', [PurchaseOrderController::class, 'show']);
    Route::put('/{id}', [PurchaseOrderController::class, 'update']);
    Route::post('/{id}/submit', [PurchaseOrderController::class, 'submit']);
    Route::post('/{id}/approve', [PurchaseOrderController::class, 'approve']);
    Route::post('/{id}/cancel', [PurchaseOrderController::class, 'cancel']);
    Route::post('/{id}/receive', [PurchaseOrderController::class, 'receive']);
}); 