<?php

namespace App\Inventory\Models;

use App\Models\User;
use App\Product\Models\Category;
use App\Product\Models\Product;
use App\Warehouse\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BatchOutboundStrategy extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'name',
        'code',
        'description',
        'strategy_type',
        'strategy_rules',
        'priority_factors',
        'scope',
        'category_id',
        'product_id',
        'warehouse_id',
        'is_active',
        'priority',
        'is_default',
        'conditions',
        'min_quantity',
        'max_quantity',
        'created_by',
    ];

    /**
     * 应该被转换的属性
     */
    protected $casts = [
        'strategy_rules' => 'array',
        'priority_factors' => 'array',
        'conditions' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'min_quantity' => 'decimal:2',
        'max_quantity' => 'decimal:2',
        'priority' => 'integer',
    ];

    /**
     * 获取关联的分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * 获取关联的商品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 获取关联的仓库
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * 获取创建者
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取策略类型文本
     * 
     * @return string
     */
    public function getStrategyTypeTextAttribute()
    {
        return match($this->strategy_type) {
            'fifo' => '先进先出',
            'lifo' => '后进先出',
            'fefo' => '先过期先出',
            'fmfo' => '先生产先出',
            'manual' => '手动选择',
            'quality_first' => '质量优先',
            'cost_optimized' => '成本优化',
            'custom' => '自定义',
            default => '未知策略'
        };
    }

    /**
     * 获取适用范围文本
     * 
     * @return string
     */
    public function getScopeTextAttribute()
    {
        return match($this->scope) {
            'global' => '全局',
            'category' => '分类',
            'product' => '商品',
            'warehouse' => '仓库',
            default => '未知范围'
        };
    }

    /**
     * 检查策略是否适用于指定条件
     * 
     * @param array $context 上下文信息
     * @return bool
     */
    public function isApplicable($context = [])
    {
        if (!$this->is_active) {
            return false;
        }

        // 检查适用范围
        switch ($this->scope) {
            case 'category':
                if (!isset($context['category_id']) || $context['category_id'] !== $this->category_id) {
                    return false;
                }
                break;

            case 'product':
                if (!isset($context['product_id']) || $context['product_id'] !== $this->product_id) {
                    return false;
                }
                break;

            case 'warehouse':
                if (!isset($context['warehouse_id']) || $context['warehouse_id'] !== $this->warehouse_id) {
                    return false;
                }
                break;
        }

        // 检查数量限制
        if (isset($context['quantity'])) {
            $quantity = $context['quantity'];
            
            if ($this->min_quantity && $quantity < $this->min_quantity) {
                return false;
            }
            
            if ($this->max_quantity && $quantity > $this->max_quantity) {
                return false;
            }
        }

        // 检查其他条件
        if ($this->conditions) {
            foreach ($this->conditions as $condition => $value) {
                if (isset($context[$condition]) && $context[$condition] !== $value) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 应用策略对批次进行排序
     * 
     * @param \Illuminate\Database\Eloquent\Collection $batches 批次集合
     * @param array $context 上下文信息
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function applyStrategy($batches, $context = [])
    {
        switch ($this->strategy_type) {
            case 'fifo':
                return $batches->sortBy([
                    ['production_date', 'asc'],
                    ['created_at', 'asc']
                ]);

            case 'lifo':
                return $batches->sortBy([
                    ['production_date', 'desc'],
                    ['created_at', 'desc']
                ]);

            case 'fefo':
                return $batches->sortBy(function($batch) {
                    // 无过期日期的排在最后
                    if (!$batch->expiry_date) {
                        return '9999-12-31';
                    }
                    return $batch->expiry_date->format('Y-m-d');
                });

            case 'quality_first':
                return $batches->sortBy(function($batch) {
                    $qualityScore = match($batch->quality_status) {
                        'passed' => 1,
                        'partial' => 2,
                        'pending' => 3,
                        'failed' => 4,
                        default => 5
                    };
                    
                    $expiryScore = $batch->expiry_date ? 
                        $batch->expiry_date->timestamp : 
                        9999999999;
                    
                    return $qualityScore * 10000000000 + $expiryScore;
                });

            case 'cost_optimized':
                return $batches->sortBy(function($batch) {
                    return $batch->unit_cost ?? $batch->purchase_price ?? 0;
                });

            case 'custom':
                return $this->applyCustomStrategy($batches, $context);

            default:
                return $batches;
        }
    }

    /**
     * 应用自定义策略
     * 
     * @param \Illuminate\Database\Eloquent\Collection $batches 批次集合
     * @param array $context 上下文信息
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function applyCustomStrategy($batches, $context = [])
    {
        if (!$this->strategy_rules || !isset($this->strategy_rules['sort_fields'])) {
            return $batches;
        }

        $sortFields = $this->strategy_rules['sort_fields'];
        
        return $batches->sortBy(function($batch) use ($sortFields) {
            $score = 0;
            
            foreach ($sortFields as $field => $config) {
                $weight = $config['weight'] ?? 1;
                $direction = $config['direction'] ?? 'asc';
                
                $value = $this->getFieldValue($batch, $field);
                
                if ($direction === 'desc') {
                    $value = -$value;
                }
                
                $score += $value * $weight;
            }
            
            return $score;
        });
    }

    /**
     * 获取批次字段值
     * 
     * @param InventoryBatch $batch 批次
     * @param string $field 字段名
     * @return float
     */
    protected function getFieldValue($batch, $field)
    {
        switch ($field) {
            case 'production_date':
                return $batch->production_date ? $batch->production_date->timestamp : 0;
            
            case 'expiry_date':
                return $batch->expiry_date ? $batch->expiry_date->timestamp : 9999999999;
            
            case 'purchase_price':
                return $batch->purchase_price ?? 0;
            
            case 'unit_cost':
                return $batch->unit_cost ?? $batch->purchase_price ?? 0;
            
            case 'quantity':
                return $batch->quantity ?? 0;
            
            case 'quality_score':
                return match($batch->quality_status) {
                    'passed' => 100,
                    'partial' => 50,
                    'pending' => 10,
                    'failed' => 0,
                    default => 0
                };
            
            case 'days_until_expiry':
                return $batch->daysUntilExpiry() ?? 9999;
            
            default:
                return 0;
        }
    }

    /**
     * 作用域：活跃的策略
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按优先级排序
     */
    public function scopeOrderByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * 作用域：默认策略
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * 作用域：按适用范围过滤
     */
    public function scopeForScope($query, $scope, $scopeId = null)
    {
        $query = $query->where('scope', $scope);
        
        if ($scopeId) {
            switch ($scope) {
                case 'category':
                    $query->where('category_id', $scopeId);
                    break;
                case 'product':
                    $query->where('product_id', $scopeId);
                    break;
                case 'warehouse':
                    $query->where('warehouse_id', $scopeId);
                    break;
            }
        }
        
        return $query;
    }

    /**
     * 获取适用的策略
     * 
     * @param array $context 上下文信息
     * @return static|null
     */
    public static function getApplicableStrategy($context = [])
    {
        $strategies = static::active()
            ->orderByPriority()
            ->get();

        foreach ($strategies as $strategy) {
            if ($strategy->isApplicable($context)) {
                return $strategy;
            }
        }

        // 如果没有找到适用的策略，返回默认策略
        return static::default()->active()->first();
    }
} 