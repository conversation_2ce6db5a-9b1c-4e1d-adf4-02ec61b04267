<?php

namespace App\Delivery\Services;

use App\Delivery\Models\Deliverer;
use App\Models\User;
use App\Employee\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class DelivererService
{
    /**
     * 获取配送员列表
     *
     * @param Request $request
     * @return \Illuminate\Pagination\LengthAwarePaginator|\Illuminate\Database\Eloquent\Collection
     */
    public function getDeliverers(Request $request)
    {
        $query = Deliverer::query()->with(['employee', 'user']);
        
        // 过滤条件
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }
        
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->whereHas('employee', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }
        
        // 根据评分排序
        if ($request->has('sort_by') && $request->sort_by === 'rating') {
            $direction = $request->has('sort_direction') && $request->sort_direction === 'asc' ? 'asc' : 'desc';
            $query->orderBy('rating', $direction);
        } else {
            $query->latest();
        }
        
        // 分页或获取全部
        if ($request->has('per_page')) {
            return $query->paginate($request->per_page);
        }
        
        return $query->get();
    }
    
    /**
     * 获取配送员详情
     *
     * @param int $id
     * @return Deliverer
     */
    public function getDeliverer($id)
    {
        return Deliverer::with(['employee', 'user'])->findOrFail($id);
    }
    
    /**
     * 创建配送员
     *
     * @param array $data
     * @return Deliverer
     * @throws \Exception
     */
    public function createDeliverer(array $data)
    {
        DB::beginTransaction();
        
        try {
            // 检查员工是否存在
            $employee = Employee::findOrFail($data['employee_id']);
            
            // 检查员工是否已经是配送员
            $existingDeliverer = Deliverer::where('employee_id', $data['employee_id'])->first();
            if ($existingDeliverer) {
                throw new \Exception('该员工已经是配送员', 400);
            }
            
            // 创建配送员
            $deliverer = new Deliverer();
            $deliverer->employee_id = $data['employee_id'];
            $deliverer->user_id = $employee->user_id ?? null;
            $deliverer->type = Deliverer::TYPE_EMPLOYEE;
            $deliverer->delivery_area = $data['delivery_area'] ?? '';
            $deliverer->max_orders = $data['max_orders'] ?? 5;
            $deliverer->rating = $data['rating'] ?? 5.0;
            $deliverer->status = $data['status'] ?? Deliverer::STATUS_AVAILABLE;
            $deliverer->working_hours = $data['working_hours'] ?? '';
            $deliverer->transportation = $data['transportation'] ?? '';
            $deliverer->save();
            
            DB::commit();
            return $deliverer;
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Create deliverer error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 更新配送员
     *
     * @param int $id
     * @param array $data
     * @return Deliverer
     * @throws \Exception
     */
    public function updateDeliverer($id, array $data)
    {
        $deliverer = Deliverer::findOrFail($id);
        
        DB::beginTransaction();
        
        try {
            if (isset($data['delivery_area'])) {
                $deliverer->delivery_area = $data['delivery_area'];
            }
            
            if (isset($data['max_orders'])) {
                $deliverer->max_orders = $data['max_orders'];
            }
            
            if (isset($data['status'])) {
                $deliverer->status = $data['status'];
            }
            
            if (isset($data['working_hours'])) {
                $deliverer->working_hours = $data['working_hours'];
            }
            
            if (isset($data['transportation'])) {
                $deliverer->transportation = $data['transportation'];
            }
            
            if (isset($data['rating'])) {
                $deliverer->rating = $data['rating'];
            }
            
            $deliverer->save();
            
            DB::commit();
            return $deliverer;
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Update deliverer error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 删除配送员
     *
     * @param int $id
     * @return bool
     * @throws \Exception
     */
    public function deleteDeliverer($id)
    {
        $deliverer = Deliverer::findOrFail($id);
        
        // 检查是否有进行中的配送任务
        $activeDeliveries = $deliverer->activeDeliveries()->count();
        if ($activeDeliveries > 0) {
            throw new \Exception('该配送员有正在进行中的配送任务，无法删除', 400);
        }
        
        return $deliverer->delete();
    }
    
    /**
     * 更新配送员位置
     *
     * @param array $data
     * @return Deliverer
     */
    public function updateLocation(array $data)
    {
        $employee = Employee::findOrFail($data['employee_id']);
        $deliverer = Deliverer::where('employee_id', $employee->id)->firstOrFail();
        
        $deliverer->last_location_lat = $data['latitude'];
        $deliverer->last_location_lng = $data['longitude'];
        $deliverer->last_active_at = now();
        $deliverer->save();
        
        return $deliverer;
    }
    
    /**
     * 根据员工ID获取配送员
     *
     * @param int $employeeId
     * @return Deliverer|null
     */
    public function getDelivererByEmployeeId($employeeId)
    {
        return Deliverer::where('employee_id', $employeeId)->first();
    }
} 