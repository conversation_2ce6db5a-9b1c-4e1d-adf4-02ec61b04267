<?php

namespace App\Crm\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Feedback extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'subject',
        'content',
        'type',
        'status',
        'response',
        'response_by',
        'resolved_at',
    ];

    /**
     * 应该被转换为日期的属性
     *
     * @var array
     */
    protected $dates = [
        'resolved_at',
        'created_at',
        'updated_at',
    ];

    /**
     * 获取提交反馈的用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取回复反馈的员工/管理员
     */
    public function responder(): BelongsTo
    {
        return $this->belongsTo(User::class, 'response_by');
    }
    
    /**
     * 待处理的反馈范围查询
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }
    
    /**
     * 处理中的反馈范围查询
     */
    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }
    
    /**
     * 已解决的反馈范围查询
     */
    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }
    
    /**
     * 已关闭的反馈范围查询
     */
    public function scopeClosed($query)
    {
        return $query->where('status', 'closed');
    }
} 