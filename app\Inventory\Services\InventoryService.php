<?php

namespace App\Inventory\Services;

use App\Inventory\Models\Inventory;
use App\Inventory\Models\InventoryBatch;
use App\Inventory\Models\InventoryTransaction;
use App\Inventory\Models\InventoryTransactionType;
use App\Inventory\Services\CostPriceService;
use App\Product\Models\Product;
use App\Models\Warehouse;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InventoryService
{
    /**
     * 成本价服务
     *
     * @var CostPriceService
     */
    private $costPriceService;

    /**
     * 构造函数
     *
     * @param CostPriceService $costPriceService
     */
    public function __construct(CostPriceService $costPriceService)
    {
        $this->costPriceService = $costPriceService;
    }
    /**
     * 获取库存列表
     * 
     * @param array $params 查询参数
     * @return LengthAwarePaginator
     */
    public function getInventoryList(array $params): LengthAwarePaginator
    {
        $query = Inventory::with(['product', 'warehouse', 'unit', 'batches']);
        
        // 仓库筛选
        if (isset($params['warehouse_id'])) {
            $query->where('warehouse_id', $params['warehouse_id']);
        }
        
        // 商品筛选
        if (isset($params['product_id'])) {
            $query->where('product_id', $params['product_id']);
        }
        
        // 商品名称筛选
        if (isset($params['product_name'])) {
            $query->whereHas('product', function($q) use ($params) {
                $q->where('name', 'like', "%{$params['product_name']}%");
            });
        }
        
        // 库存量筛选
        if (isset($params['min_stock'])) {
            $query->where('stock', '>=', $params['min_stock']);
        }
        
        if (isset($params['max_stock'])) {
            $query->where('stock', '<=', $params['max_stock']);
        }
        
        // 排序
        $orderBy = $params['order_by'] ?? 'id';
        $direction = $params['direction'] ?? 'asc';
        $query->orderBy($orderBy, $direction);
        
        // 分页
        $perPage = $params['per_page'] ?? 15;
        return $query->paginate($perPage);
    }
    
    /**
     * 获取库存详情
     * 
     * @param int $id 库存ID
     * @return Inventory
     */
    public function getInventory(int $id): Inventory
    {
        return Inventory::with(['product', 'warehouse', 'unit', 'batches'])->findOrFail($id);
    }
    
    /**
     * 获取商品在特定仓库的库存
     * 
     * @param int $productId 商品ID
     * @param int $warehouseId 仓库ID
     * @return Inventory|null
     */
    public function getProductInventory(int $productId, int $warehouseId): ?Inventory
    {
        return Inventory::with(['product', 'warehouse', 'unit', 'batches'])
            ->where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->first();
    }
    
    /**
     * 更新库存
     * 
     * @param int $id 库存ID
     * @param float $stock 新库存量
     * @param int|null $unitId 单位ID
     * @return Inventory
     */
    public function updateInventory(int $id, float $stock, ?int $unitId = null): Inventory
    {
        $inventory = Inventory::findOrFail($id);
        $inventory->setStockInUnit($stock, $unitId);
        return $inventory->refresh();
    }
    
    /**
     * 创建库存事务
     * 
     * @param array $data 事务数据
     * @return InventoryTransaction
     */
    public function createTransaction(array $data): InventoryTransaction
    {
        DB::beginTransaction();
        
        try {
            // 检查用户认证
            $userId = auth()->id();
            if (!$userId) {
                throw new \Exception('用户未登录，无法创建库存事务');
            }
            
            $transaction = InventoryTransaction::create([
                'transaction_type_id' => $data['transaction_type_id'],
                'product_id' => $data['product_id'],
                'warehouse_id' => $data['warehouse_id'],
                'quantity' => $data['quantity'],
                'unit_id' => $data['unit_id'],
                'unit_price' => $data['unit_price'] ?? 0,
                'total_amount' => $data['total_amount'] ?? ($data['quantity'] * ($data['unit_price'] ?? 0)),
                'status' => $data['status'] ?? 'pending',
                'notes' => $data['notes'] ?? null,
                'created_by' => $data['created_by'] ?? $userId,
                'updated_by' => $data['updated_by'] ?? $userId,
            ]);
            
            // 如果状态为已完成，则应用库存更新
            if ($transaction->status === 'completed') {
                $transaction->applyToInventory();
            }
            
            DB::commit();
            return $transaction;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建库存事务失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);
            throw $e;
        }
    }
    
    /**
     * 获取库存事务列表
     * 
     * @param array $params 查询参数
     * @return LengthAwarePaginator
     */
    public function getTransactionList(array $params): LengthAwarePaginator
    {
        $query = InventoryTransaction::with(['product', 'warehouse', 'unit', 'transactionType', 'creator', 'updater']);
        
        // 事务类型筛选
        if (isset($params['transaction_type_id'])) {
            $query->where('transaction_type_id', $params['transaction_type_id']);
        }
        
        // 商品筛选
        if (isset($params['product_id'])) {
            $query->where('product_id', $params['product_id']);
        }
        
        // 仓库筛选
        if (isset($params['warehouse_id'])) {
            $query->where('warehouse_id', $params['warehouse_id']);
        }
        
        // 状态筛选
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }
        
        // 日期范围筛选
        if (isset($params['start_date'])) {
            $query->where('created_at', '>=', $params['start_date']);
        }
        
        if (isset($params['end_date'])) {
            $query->where('created_at', '<=', $params['end_date']);
        }
        
        // 排序
        $orderBy = $params['order_by'] ?? 'created_at';
        $direction = $params['direction'] ?? 'desc';
        $query->orderBy($orderBy, $direction);
        
        // 分页
        $perPage = $params['per_page'] ?? 15;
        return $query->paginate($perPage);
    }
    
    /**
     * 获取库存批次列表
     * 
     * @param array $params 查询参数
     * @return LengthAwarePaginator
     */
    public function getBatchList(array $params): LengthAwarePaginator
    {
        $query = InventoryBatch::with(['inventory.product', 'inventory.warehouse', 'unit', 'purchaseItem', 'creator']);
        
        // 库存ID筛选
        if (isset($params['inventory_id'])) {
            $query->where('inventory_id', $params['inventory_id']);
        }
        
        // 商品筛选（通过库存）
        if (isset($params['product_id'])) {
            $query->whereHas('inventory', function($q) use ($params) {
                $q->where('product_id', $params['product_id']);
            });
        }
        
        // 仓库筛选（通过库存）
        if (isset($params['warehouse_id'])) {
            $query->whereHas('inventory', function($q) use ($params) {
                $q->where('warehouse_id', $params['warehouse_id']);
            });
        }
        
        // 批次编码筛选
        if (isset($params['batch_code'])) {
            $query->where('batch_code', 'like', "%{$params['batch_code']}%");
        }
        
        // 过期日期范围筛选
        if (isset($params['expiry_start_date'])) {
            $query->where('expiry_date', '>=', $params['expiry_start_date']);
        }
        
        if (isset($params['expiry_end_date'])) {
            $query->where('expiry_date', '<=', $params['expiry_end_date']);
        }
        
        // 仅显示库存大于0的批次
        if (isset($params['only_available']) && $params['only_available']) {
            $query->where('quantity', '>', 0);
        }
        
        // 排序
        $orderBy = $params['order_by'] ?? 'created_at';
        $direction = $params['direction'] ?? 'desc';
        $query->orderBy($orderBy, $direction);
        
        // 分页
        $perPage = $params['per_page'] ?? 15;
        return $query->paginate($perPage);
    }
    
    /**
     * 创建库存批次
     * 
     * @param array $data 批次数据
     * @return InventoryBatch
     */
    public function createBatch(array $data): InventoryBatch
    {
        DB::beginTransaction();
        
        try {
            // 检查用户认证
            $userId = auth()->id();
            if (!$userId) {
                throw new \Exception('用户未登录，无法创建库存批次');
            }
            
            // 自动生成批次编码（如果未提供）
            if (!isset($data['batch_code']) || empty($data['batch_code'])) {
                $data['batch_code'] = InventoryBatch::generateBatchCode();
            }
            
            $batch = InventoryBatch::create([
                'inventory_id' => $data['inventory_id'],
                'purchase_item_id' => $data['purchase_item_id'] ?? null,
                'batch_code' => $data['batch_code'],
                'production_date' => $data['production_date'] ?? null,
                'expiry_date' => $data['expiry_date'] ?? null,
                'purchase_price' => $data['purchase_price'] ?? 0,
                'quantity' => $data['quantity'],
                'initial_quantity' => $data['quantity'], // 初始数量等于当前数量
                'unit_id' => $data['unit_id'],
                'notes' => $data['notes'] ?? null,
                'created_by' => $data['created_by'] ?? $userId,
            ]);
            
            // 更新库存总量
            $inventory = Inventory::findOrFail($data['inventory_id']);
            $inventory->stock += $data['quantity'];
            $inventory->save();
            
            DB::commit();
            return $batch;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建库存批次失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);
            throw $e;
        }
    }
    
    /**
     * 库存调整
     * 
     * @param array $data 调整数据
     * @return array 包含事务和库存的响应
     */
    public function adjustInventory(array $data): array
    {
        DB::beginTransaction();
        
        try {
            // 找到或创建库存记录
            $inventory = Inventory::firstOrCreate(
                [
                    'warehouse_id' => $data['warehouse_id'],
                    'product_id' => $data['product_id'],
                ],
                [
                    'unit_id' => $data['unit_id'],
                    'stock' => 0,
                ]
            );
            
            // 找到调整类型
            $transactionType = InventoryTransactionType::where('code', $data['adjustment_type'])->firstOrFail();
            
            // 检查用户认证
            $userId = auth()->id();
            if (!$userId) {
                throw new \Exception('用户未登录，无法调整库存');
            }
            
            // 创建库存事务
            $transaction = InventoryTransaction::create([
                'transaction_type_id' => $transactionType->id,
                'product_id' => $data['product_id'],
                'warehouse_id' => $data['warehouse_id'],
                'quantity' => abs($data['quantity']), // 事务数量始终为正
                'unit_id' => $data['unit_id'],
                'unit_price' => $data['unit_price'] ?? 0,
                'total_amount' => $data['unit_price'] ? abs($data['quantity']) * $data['unit_price'] : 0,
                'status' => 'completed',
                'notes' => $data['notes'] ?? null,
                'created_by' => $data['created_by'] ?? $userId,
                'updated_by' => $data['updated_by'] ?? $userId,
            ]);
            
            // 获取产品模型
            $product = Product::findOrFail($data['product_id']);
            
            // 计算调整数量并转换单位
            $adjustmentQuantity = abs($data['quantity']);
            
            // 如果事务单位与库存单位不同，需要进行转换
            if ($data['unit_id'] != $inventory->unit_id) {
                $adjustmentQuantity = $product->convertQuantity(
                    $adjustmentQuantity,
                    $data['unit_id'],
                    $inventory->unit_id
                );
                
                if ($adjustmentQuantity === null) {
                    throw new \Exception("无法转换单位：从 {$data['unit_id']} 到 {$inventory->unit_id}");
                }
            }
            
            // 根据调整类型方向调整符号
            if ($transactionType->effect_direction < 0) {
                $adjustmentQuantity = -$adjustmentQuantity;
            }
            
            // 更新库存
            $oldStock = $inventory->stock;
            $inventory->stock += $adjustmentQuantity;
            $inventory->save();
            
            // 记录操作日志
            Log::info('库存调整成功', [
                'product_id' => $data['product_id'],
                'warehouse_id' => $data['warehouse_id'],
                'old_stock' => $oldStock,
                'adjustment' => $adjustmentQuantity,
                'new_stock' => $inventory->stock,
                'transaction_id' => $transaction->id,
                'user_id' => $data['created_by'] ?? auth()->id(),
            ]);
            
            // 如果有价格信息，触发成本价更新
            if (isset($data['unit_price']) && $data['unit_price'] > 0) {
                $this->costPriceService->clearCostPriceCacheForProduct(
                    $data['product_id'],
                    $data['warehouse_id']
                );
            }
            
            DB::commit();
            
            return [
                'transaction' => $transaction,
                'inventory' => $inventory->refresh(),
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('库存调整失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);
            throw $e;
        }
    }
    
    /**
     * 库存转移（从一个仓库到另一个仓库）
     * 
     * @param array $data 转移数据
     * @return array 包含源库存、目标库存和事务的响应
     */
    public function transferInventory(array $data): array
    {
        DB::beginTransaction();
        
        try {
            // 检查用户认证
            $userId = auth()->id();
            if (!$userId) {
                throw new \Exception('用户未登录，无法转移库存');
            }
            // 源库存
            $sourceInventory = Inventory::where('product_id', $data['product_id'])
                ->where('warehouse_id', $data['source_warehouse_id'])
                ->first();
                
            if (!$sourceInventory) {
                throw new \Exception('源仓库没有该商品的库存记录');
            }
            
            // 获取产品模型
            $product = Product::findOrFail($data['product_id']);
            
            // 如果传入的单位与源库存单位不同，需要进行转换
            $transferQuantityInSourceUnit = $data['quantity'];
            if ($data['unit_id'] != $sourceInventory->unit_id) {
                $transferQuantityInSourceUnit = $product->convertQuantity(
                    $data['quantity'],
                    $data['unit_id'],
                    $sourceInventory->unit_id
                );
                
                if ($transferQuantityInSourceUnit === null) {
                    throw new \Exception("无法转换单位：从 {$data['unit_id']} 到 {$sourceInventory->unit_id}");
                }
            }
            
            // 检查库存是否足够
            if ($sourceInventory->stock < $transferQuantityInSourceUnit) {
                throw new \Exception("源仓库库存不足：当前 {$sourceInventory->stock} {$sourceInventory->unit->symbol}，需要 {$transferQuantityInSourceUnit} {$sourceInventory->unit->symbol}");
            }
            
            // 目标库存
            $targetInventory = Inventory::firstOrCreate(
                [
                    'product_id' => $data['product_id'],
                    'warehouse_id' => $data['target_warehouse_id'],
                ],
                [
                    'unit_id' => $sourceInventory->unit_id,
                    'stock' => 0,
                ]
            );
            
            // 创建出库事务
            $outTransactionType = InventoryTransactionType::where('code', 'transfer_out')->firstOrFail();
            $outTransaction = InventoryTransaction::create([
                'transaction_type_id' => $outTransactionType->id,
                'product_id' => $data['product_id'],
                'warehouse_id' => $data['source_warehouse_id'],
                'quantity' => $data['quantity'],
                'unit_id' => $data['unit_id'],
                'status' => 'completed',
                'notes' => $data['notes'] ?? '库存转移',
                'created_by' => $data['created_by'] ?? $userId,
                'updated_by' => $data['updated_by'] ?? $userId,
            ]);
            
            // 创建入库事务
            $inTransactionType = InventoryTransactionType::where('code', 'transfer_in')->firstOrFail();
            $inTransaction = InventoryTransaction::create([
                'transaction_type_id' => $inTransactionType->id,
                'product_id' => $data['product_id'],
                'warehouse_id' => $data['target_warehouse_id'],
                'quantity' => $data['quantity'],
                'unit_id' => $data['unit_id'],
                'status' => 'completed',
                'notes' => $data['notes'] ?? '库存转移',
                'created_by' => $data['created_by'] ?? $userId,
                'updated_by' => $data['updated_by'] ?? $userId,
            ]);
            
            // 如果目标库存单位与源库存单位不同，进行转换
            $transferQuantityInTargetUnit = $transferQuantityInSourceUnit;
            if ($targetInventory->unit_id != $sourceInventory->unit_id) {
                $transferQuantityInTargetUnit = $product->convertQuantity(
                    $transferQuantityInSourceUnit,
                    $sourceInventory->unit_id,
                    $targetInventory->unit_id
                );
                
                if ($transferQuantityInTargetUnit === null) {
                    throw new \Exception("无法转换单位：从 {$sourceInventory->unit_id} 到 {$targetInventory->unit_id}");
                }
            }
            
            // 更新库存
            $sourceInventory->stock -= $transferQuantityInSourceUnit;
            $sourceInventory->save();
            
            $targetInventory->stock += $transferQuantityInTargetUnit;
            $targetInventory->save();
            
            // 更新产品总库存
            $product->updateTotalStock();
            
            // 记录操作日志
            Log::info('库存转移成功', [
                'product_id' => $data['product_id'],
                'source_warehouse_id' => $data['source_warehouse_id'],
                'target_warehouse_id' => $data['target_warehouse_id'],
                'quantity' => $data['quantity'],
                'unit_id' => $data['unit_id'],
                'source_quantity' => $transferQuantityInSourceUnit,
                'target_quantity' => $transferQuantityInTargetUnit,
                'out_transaction_id' => $outTransaction->id,
                'in_transaction_id' => $inTransaction->id,
                'user_id' => $data['created_by'] ?? auth()->id(),
            ]);
            
            DB::commit();
            
            return [
                'source_inventory' => $sourceInventory->refresh(),
                'target_inventory' => $targetInventory->refresh(),
                'out_transaction' => $outTransaction,
                'in_transaction' => $inTransaction,
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('库存转移失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);
            throw $e;
        }
    }
} 