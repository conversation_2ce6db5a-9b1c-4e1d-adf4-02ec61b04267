// 图片加载状态管理测试页面
const ImageLoadingManager = require('../../utils/image-loading-manager');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('ImageLoadingTest');

Page({
  data: {
    testImages: [
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg', 
      'https://example.com/image3.jpg',
      'https://example.com/image4.jpg',
      'https://example.com/image5.jpg',
      'https://example.com/image6.jpg'
    ],
    loadingStats: null,
    pageKey: 'image-test-page'
  },

  onLoad() {
    logger.info('图片加载测试页面加载');
    this.startImageLoadingTest();
  },

  onUnload() {
    logger.info('图片加载测试页面卸载');
    ImageLoadingManager.stopPageImageLoading(this.data.pageKey);
  },

  /**
   * 开始图片加载测试
   */
  startImageLoadingTest() {
    const { testImages, pageKey } = this.data;
    
    // 启动页面级图片加载跟踪
    ImageLoadingManager.startPageImageLoading(pageKey, testImages.length, {
      title: '测试图片加载中',
      page: this,
      showProgress: true
    });

    // 为每张图片启动加载跟踪
    testImages.forEach((imageUrl, index) => {
      const imageKey = `test-image-${index}`;
      ImageLoadingManager.startImageLoading(imageKey, pageKey, {
        silent: true
      });
    });

    logger.info('图片加载测试开始', { 
      总图片数: testImages.length,
      pageKey 
    });

    // 定时更新统计信息
    this.updateStatsTimer = setInterval(() => {
      this.updateLoadingStats();
    }, 1000);
  },

  /**
   * 更新加载统计信息
   */
  updateLoadingStats() {
    const stats = ImageLoadingManager.getAllStats();
    this.setData({ loadingStats: stats });
  },

  /**
   * 图片加载成功
   */
  onImageLoad(e) {
    const { index } = e.currentTarget.dataset;
    const imageKey = `test-image-${index}`;
    
    ImageLoadingManager.onImageLoaded(imageKey, {
      index,
      timestamp: Date.now()
    });

    logger.info(`测试图片加载成功: ${imageKey}`);
  },

  /**
   * 图片加载失败
   */
  onImageError(e) {
    const { index } = e.currentTarget.dataset;
    const imageKey = `test-image-${index}`;
    
    ImageLoadingManager.onImageError(imageKey, {
      index,
      error: e.detail,
      timestamp: Date.now()
    });

    logger.warn(`测试图片加载失败: ${imageKey}`, e.detail);
  },

  /**
   * 手动触发加载成功（用于测试）
   */
  onManualSuccess(e) {
    const { index } = e.currentTarget.dataset;
    const imageKey = `test-image-${index}`;
    
    ImageLoadingManager.onImageLoaded(imageKey, {
      index,
      manual: true,
      timestamp: Date.now()
    });

    wx.showToast({
      title: `图片${index + 1}标记为成功`,
      icon: 'success'
    });
  },

  /**
   * 手动触发加载失败（用于测试）
   */
  onManualError(e) {
    const { index } = e.currentTarget.dataset;
    const imageKey = `test-image-${index}`;
    
    ImageLoadingManager.onImageError(imageKey, {
      index,
      manual: true,
      error: '手动触发的错误',
      timestamp: Date.now()
    });

    wx.showToast({
      title: `图片${index + 1}标记为失败`,
      icon: 'error'
    });
  },

  /**
   * 重置测试
   */
  onResetTest() {
    // 停止当前跟踪
    ImageLoadingManager.stopPageImageLoading(this.data.pageKey);
    
    // 清理定时器
    if (this.updateStatsTimer) {
      clearInterval(this.updateStatsTimer);
    }

    // 重新开始测试
    setTimeout(() => {
      this.startImageLoadingTest();
    }, 500);

    wx.showToast({
      title: '测试已重置',
      icon: 'success'
    });
  },

  /**
   * 查看详细统计
   */
  onViewStats() {
    const stats = ImageLoadingManager.getAllStats();
    
    wx.showModal({
      title: '加载统计',
      content: JSON.stringify(stats.summary, null, 2),
      showCancel: false
    });
  },

  /**
   * 紧急清理
   */
  onEmergencyCleanup() {
    ImageLoadingManager.emergencyCleanup();
    
    wx.showToast({
      title: '已紧急清理',
      icon: 'success'
    });
  }
}); 