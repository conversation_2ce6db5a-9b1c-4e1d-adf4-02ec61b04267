<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 重命名会员等级表的字段
        Schema::table('membership_levels', function (Blueprint $table) {
            // 先添加新字段
            $table->integer('upgrade_points')->default(0)->after('points_required')
                  ->comment('升级所需积分');
            $table->decimal('upgrade_amount', 10, 2)->default(0)->after('total_consumption_required')
                  ->comment('升级所需累计消费金额');
            $table->decimal('quick_upgrade_amount', 10, 2)->default(0)->after('single_order_required')
                  ->comment('快速升级所需单笔订单金额');
        });
        
        // 复制旧字段数据到新字段
        DB::statement('UPDATE membership_levels SET 
            upgrade_points = points_required,
            upgrade_amount = total_consumption_required,
            quick_upgrade_amount = single_order_required');
            
        // 删除旧字段
        Schema::table('membership_levels', function (Blueprint $table) {
            $table->dropColumn(['points_required', 'total_consumption_required', 'single_order_required']);
        });
        
        // 重命名用户表的字段
        Schema::table('users', function (Blueprint $table) {
            // 先添加新字段
            $table->integer('member_points')->default(0)->after('points')
                  ->comment('会员积分');
            $table->decimal('total_spend', 10, 2)->default(0)->after('total_consumption')
                  ->comment('累计消费金额');
            $table->decimal('largest_order', 10, 2)->default(0)->after('max_single_order')
                  ->comment('最大单笔订单金额');
        });
        
        // 复制旧字段数据到新字段
        DB::statement('UPDATE users SET 
            member_points = points,
            total_spend = total_consumption,
            largest_order = max_single_order');
            
        // 删除旧字段
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['points', 'total_consumption', 'max_single_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复会员等级表的字段
        Schema::table('membership_levels', function (Blueprint $table) {
            // 先添加旧字段
            $table->integer('points_required')->default(0)->after('description')
                  ->comment('达到此等级所需的积分');
            $table->decimal('total_consumption_required', 10, 2)->default(0)->after('points_required')
                  ->comment('升级所需的累计消费金额');
            $table->decimal('single_order_required', 10, 2)->default(0)->after('total_consumption_required')
                  ->comment('单笔订单金额达到此值可直接升级');
        });
        
        // 复制新字段数据到旧字段
        DB::statement('UPDATE membership_levels SET 
            points_required = upgrade_points,
            total_consumption_required = upgrade_amount,
            single_order_required = quick_upgrade_amount');
            
        // 删除新字段
        Schema::table('membership_levels', function (Blueprint $table) {
            $table->dropColumn(['upgrade_points', 'upgrade_amount', 'quick_upgrade_amount']);
        });
        
        // 恢复用户表的字段
        Schema::table('users', function (Blueprint $table) {
            // 先添加旧字段
            $table->integer('points')->default(0)->after('balance')
                  ->comment('用户积分');
            $table->decimal('total_consumption', 10, 2)->default(0)->after('points')
                  ->comment('用户累计消费金额');
            $table->decimal('max_single_order', 10, 2)->default(0)->after('total_consumption')
                  ->comment('用户历史最大单笔订单金额');
        });
        
        // 复制新字段数据到旧字段
        DB::statement('UPDATE users SET 
            points = member_points,
            total_consumption = total_spend,
            max_single_order = largest_order');
            
        // 删除新字段
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['member_points', 'total_spend', 'largest_order']);
        });
    }
}; 