<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 检查旧表是否存在
        if (Schema::hasTable('payment_discounts')) {
            // 1. 重命名表
            Schema::rename('payment_discounts', 'payment_offers');
            
            // 2. 重命名字段
            Schema::table('payment_offers', function (Blueprint $table) {
                // 检查字段是否存在再重命名
                if (Schema::hasColumn('payment_offers', 'discount_type')) {
                    $table->renameColumn('discount_type', 'offer_type');
                }
                
                if (Schema::hasColumn('payment_offers', 'discount_value')) {
                    $table->renameColumn('discount_value', 'offer_value');
                }
                
                if (Schema::hasColumn('payment_offers', 'max_discount')) {
                    $table->renameColumn('max_discount', 'max_offer');
                }
            });
            
            echo "✅ 成功将 payment_discounts 表重命名为 payment_offers\n";
            echo "✅ 成功重命名相关字段\n";
        } else {
            echo "ℹ️  payment_discounts 表不存在，跳过重命名操作\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 检查新表是否存在
        if (Schema::hasTable('payment_offers')) {
            // 1. 重命名字段回原来的名称
            Schema::table('payment_offers', function (Blueprint $table) {
                // 检查字段是否存在再重命名
                if (Schema::hasColumn('payment_offers', 'offer_type')) {
                    $table->renameColumn('offer_type', 'discount_type');
                }
                
                if (Schema::hasColumn('payment_offers', 'offer_value')) {
                    $table->renameColumn('offer_value', 'discount_value');
                }
                
                if (Schema::hasColumn('payment_offers', 'max_offer')) {
                    $table->renameColumn('max_offer', 'max_discount');
                }
            });
            
            // 2. 重命名表回原来的名称
            Schema::rename('payment_offers', 'payment_discounts');
            
            echo "✅ 成功回滚：将 payment_offers 表重命名为 payment_discounts\n";
            echo "✅ 成功回滚相关字段名称\n";
        } else {
            echo "ℹ️  payment_offers 表不存在，跳过回滚操作\n";
        }
    }
};
