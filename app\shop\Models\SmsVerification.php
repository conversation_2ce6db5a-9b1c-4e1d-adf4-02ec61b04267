<?php

namespace App\shop\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SmsVerification extends Model
{
    use HasFactory;

    protected $table = 'sms_verifications';

    protected $fillable = [
        'phone',
        'code',
        'type', // login, register, reset_password 等类型
        'expires_at',
        'used',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'used' => 'boolean'
    ];

    /**
     * 检查验证码是否已过期
     *
     * @return bool
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * 检查验证码是否有效
     *
     * @return bool
     */
    public function isValid(): bool
    {
        return !$this->used && !$this->isExpired();
    }

    /**
     * 标记验证码为已使用
     *
     * @return bool
     */
    public function markAsUsed(): bool
    {
        $this->used = true;
        return $this->save();
    }

    /**
     * 按类型查找最新的未使用验证码
     *
     * @param string $phone
     * @param string $type
     * @return self|null
     */
    public static function findLatestValidCode(string $phone, string $type)
    {
        return self::where('phone', $phone)
            ->where('type', $type)
            ->where('used', false)
            ->where('expires_at', '>', now())
            ->latest()
            ->first();
    }
} 