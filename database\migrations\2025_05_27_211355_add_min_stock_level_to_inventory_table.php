<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inventory', function (Blueprint $table) {
            // 添加最低库存预警字段（按库存单位计算）
            $table->decimal('min_stock_level', 10, 2)->nullable()->after('stock')
                ->comment('最低库存预警阈值（按当前库存单位计算，不同仓库可设置不同值）');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inventory', function (Blueprint $table) {
            $table->dropColumn('min_stock_level');
        });
    }
};
