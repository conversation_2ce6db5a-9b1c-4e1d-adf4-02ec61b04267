<!-- pages/points/product/index.wxml - 积分商品详情页 -->
<view class="product-container" wx:if="{{!loading}}">
  
  <!-- 商品图片轮播 -->
  <swiper class="product-swiper" indicator-dots="{{true}}" autoplay="{{false}}">
    <swiper-item wx:for="{{product.images || [product.image]}}" wx:key="index">
      <image 
        class="product-image" 
        src="{{item}}" 
        mode="aspectFill"
        bindtap="previewImage"
        data-url="{{item}}"
      />
    </swiper-item>
  </swiper>

  <!-- 商品基本信息 -->
  <view class="product-info">
    <view class="product-header">
      <text class="product-name">{{product.name}}</text>
      <view class="product-tags">
        <text class="tag category">{{formatProductCategory(product.category)}}</text>
        <text class="tag hot" wx:if="{{product.is_hot}}">热门</text>
        <text class="tag new" wx:if="{{product.is_new}}">新品</text>
      </view>
    </view>
    
    <view class="product-price">
      <view class="points-price">
        <text class="points-value">{{calculateTotalPoints()}}</text>
        <text class="points-unit">积分</text>
      </view>
      <view class="cash-price" wx:if="{{calculateTotalCash() > 0}}">
        <text class="cash-label">+</text>
        <text class="cash-value">¥{{calculateTotalCash().toFixed(2)}}</text>
      </view>
      <view class="original-price" wx:if="{{product.original_price}}">
        <text class="original-value">¥{{product.original_price}}</text>
      </view>
    </view>

    <view class="product-stats">
      <text class="stat-item">已兑换 {{product.exchanged_count || 0}} 件</text>
      <text class="stat-item" wx:if="{{product.stock > 0}}">库存 {{product.stock}} 件</text>
      <text class="stat-item stock-out" wx:else>库存不足</text>
    </view>
  </view>

  <!-- 用户积分信息 -->
  <view class="user-points-info">
    <view class="points-balance">
      <text class="balance-label">我的积分</text>
      <text class="balance-value">{{formatPoints(userBalance)}}</text>
    </view>
    <view class="points-sufficient {{isPointsSufficient() ? 'sufficient' : 'insufficient'}}">
      <text>{{isPointsSufficient() ? '积分充足' : '积分不足'}}</text>
    </view>
  </view>

  <!-- 商品规格选择 -->
  <view class="product-specs" wx:if="{{product.specs && product.specs.length > 0}}">
    <view class="specs-title">选择规格</view>
    <view class="specs-list">
      <view 
        class="spec-item {{selectedSpec.id === item.id ? 'selected' : ''}}"
        wx:for="{{product.specs}}"
        wx:key="id"
        data-index="{{index}}"
        bindtap="onSpecChange"
      >
        <view class="spec-name">{{item.name}}</view>
        <view class="spec-price">{{item.points_price}}积分</view>
      </view>
    </view>
  </view>

  <!-- 数量选择 -->
  <view class="quantity-selector">
    <view class="quantity-title">兑换数量</view>
    <view class="quantity-controls">
      <button 
        class="quantity-btn minus {{quantity <= 1 ? 'disabled' : ''}}"
        bindtap="onQuantityChange"
        data-action="minus"
      >-</button>
      <input 
        class="quantity-input"
        type="number"
        value="{{quantity}}"
        bindchange="onQuantityChange"
      />
      <button 
        class="quantity-btn plus"
        bindtap="onQuantityChange"
        data-action="plus"
      >+</button>
    </view>
  </view>

  <!-- 商品详情 -->
  <view class="product-detail">
    <view class="detail-title">商品详情</view>
    <view class="detail-content">
      <text class="detail-description">{{product.description}}</text>
      
      <!-- 详情图片 -->
      <view class="detail-images" wx:if="{{detailImages.length > 0}}">
        <image 
          class="detail-image"
          wx:for="{{detailImages}}"
          wx:key="index"
          src="{{item}}"
          mode="widthFix"
          bindtap="previewImage"
          data-url="{{item}}"
        />
      </view>
    </view>
  </view>

  <!-- 兑换须知 -->
  <view class="exchange-notice">
    <view class="notice-title">兑换须知</view>
    <view class="notice-content">
      <view class="notice-item">• 积分兑换后不可退换，请谨慎选择</view>
      <view class="notice-item">• 实物商品需要填写收货地址</view>
      <view class="notice-item">• 虚拟商品将在兑换成功后发放到账户</view>
      <view class="notice-item">• 如有疑问请联系客服</view>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="related-products" wx:if="{{relatedProducts.length > 0}}">
    <view class="related-title">相关推荐</view>
    <scroll-view class="related-scroll" scroll-x="{{true}}">
      <view 
        class="related-item"
        wx:for="{{relatedProducts}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="goToRelatedProduct"
      >
        <image class="related-image" src="{{item.image}}" mode="aspectFill" />
        <view class="related-info">
          <text class="related-name">{{item.name}}</text>
          <text class="related-points">{{item.points_price}}积分</text>
        </view>
      </view>
    </scroll-view>
  </view>

</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-spinner"></view>
  <text class="loading-text">加载中...</text>
</view>

<!-- 底部操作栏 -->
<view class="bottom-actions">
  <view class="action-info">
    <view class="total-points">
      <text class="total-label">需要积分</text>
      <text class="total-value">{{calculateTotalPoints()}}</text>
    </view>
    <view class="total-cash" wx:if="{{calculateTotalCash() > 0}}">
      <text class="total-label">需要现金</text>
      <text class="total-value">¥{{calculateTotalCash().toFixed(2)}}</text>
    </view>
  </view>
  
  <button 
    class="{{getExchangeButtonClass()}}"
    bindtap="onExchange"
  >
    {{getExchangeButtonText()}}
  </button>
</view> 