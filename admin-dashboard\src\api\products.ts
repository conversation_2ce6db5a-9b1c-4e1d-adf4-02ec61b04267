// 商品API服务
export interface Product {
  id: number
  name: string
  subtitle: string
  image: string
  category: string
  stock: string
  sales: string
  type: string
  price: string
  tags: string[]
  status: string
}

export interface ProductListParams {
  page: number
  pageSize: number
  category?: string
  standard?: string
  supplier?: string
  tagStatus?: string
  keyword?: string
  tab?: string
  status?: string
}

export interface ProductListResponse {
  data: Product[]
  total: number
  page: number
  pageSize: number
}

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟商品数据
const mockProducts: Product[] = [
  {
    id: 1902,
    name: '时令水果-苹果3',
    subtitle: '纯正美味好商品',
    image: '/api/placeholder/60/60',
    category: '养后大棚',
    stock: '0.00',
    sales: '0',
    type: '正常',
    price: '¥42.00',
    tags: ['编辑', '删除', '已上架', '标签', '新品', '特惠'],
    status: '正常'
  },
  {
    id: 1901,
    name: '小王理想',
    subtitle: '中等明科',
    image: '/api/placeholder/60/60',
    category: '海鲜水产-冷冻鱼类-秋刀鱼',
    stock: '60.00',
    sales: '3',
    type: '正常',
    price: '¥50.00',
    tags: ['编辑', '删除', '已上架', '标签', '新品'],
    status: '正常'
  },
  {
    id: 1900,
    name: '味让小青',
    subtitle: '',
    image: '/api/placeholder/60/60',
    category: '海鲜水产-鲜活水产',
    stock: '998.00',
    sales: '2',
    type: '正常',
    price: '¥18.00',
    tags: ['编辑', '删除', '已上架', '标签'],
    status: '正常'
  },
  {
    id: 1899,
    name: '[自营] 水牛-海虾小青',
    subtitle: '',
    image: '/api/placeholder/60/60',
    category: '海鲜水产-鲜活水产',
    stock: '20.00',
    sales: '2',
    type: '主上架',
    price: '¥1.00',
    tags: ['编辑', '删除', '已上架', '设置预售', '已上架', '标签'],
    status: '主上架'
  },
  {
    id: 1898,
    name: '[自营] 千岛豆',
    subtitle: '',
    image: '/api/placeholder/60/60',
    category: '日用百货-黄花日货',
    stock: '97.00',
    sales: '3',
    type: '正常',
    price: '¥2.00',
    tags: ['编辑', '删除', '已上架', '设置预售', '已上架', '标签'],
    status: '正常'
  }
]

// 获取商品列表
export const getProductList = async (params: ProductListParams): Promise<ProductListResponse> => {
  await delay(800) // 模拟网络延迟
  
  // 模拟筛选逻辑
  let filteredProducts = [...mockProducts]
  
  if (params.keyword) {
    filteredProducts = filteredProducts.filter(product => 
      product.name.includes(params.keyword!) || 
      product.id.toString().includes(params.keyword!)
    )
  }
  
  if (params.category) {
    filteredProducts = filteredProducts.filter(product => 
      product.category.includes(params.category!)
    )
  }
  
  // 模拟分页
  const start = (params.page - 1) * params.pageSize
  const end = start + params.pageSize
  const paginatedProducts = filteredProducts.slice(start, end)
  
  return {
    data: paginatedProducts,
    total: filteredProducts.length,
    page: params.page,
    pageSize: params.pageSize
  }
}

// 删除商品
export const deleteProduct = async (id: number): Promise<void> => {
  await delay(500)
  console.log(`删除商品 ${id}`)
}

// 批量操作
export const batchUpdateProducts = async (ids: number[], action: string): Promise<void> => {
  await delay(1000)
  console.log(`批量${action}商品:`, ids)
} 