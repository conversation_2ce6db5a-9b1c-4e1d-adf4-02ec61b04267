import { get, post, put, del } from '@/utils/request'

// 商品API服务
export interface Product {
  id: number
  name: string
  subtitle?: string
  description?: string
  code?: string
  image?: string
  cover_url?: string
  category?: string
  category_id?: number
  stock: number
  sales?: number
  type?: string
  price: number
  sale_price?: number
  cost_price?: number
  tags?: string[]
  status: number
  allow_sale?: boolean
  created_at?: string
  updated_at?: string
}

export interface ProductListParams {
  page: number
  per_page?: number
  pageSize?: number  // 兼容旧参数名
  category?: string
  category_id?: number
  standard?: string
  supplier?: string
  stock_status?: string
  tagStatus?: string
  keyword?: string
  search?: string
  tab?: string
  status?: string | number
  allow_sale?: boolean
  min_price?: number
  max_price?: number
  start_date?: string
  end_date?: string
}

export interface ProductStats {
  totalProducts: number
  activeProducts: number
  nonSellableProducts: number
  lowStockProducts: number
  outOfStockProducts: number
  totalValue: number
  averagePrice: number
}

export interface ProductListResponse {
  data: Product[]
  total: number
  current_page: number
  per_page: number
  last_page: number
  from: number | null
  to: number | null
}

// 构建查询参数
const buildQueryParams = (params: ProductListParams): Record<string, any> => {
  const queryParams: Record<string, any> = {}
  
  // 分页参数
  queryParams.page = params.page
  queryParams.per_page = params.per_page || params.pageSize || 20
  
  // 搜索参数 - 统一使用search字段
  if (params.keyword) {
    queryParams.search = params.keyword
  }
  
  // 筛选参数 - 只添加有值的参数
  if (params.category_id) queryParams.category_id = params.category_id
  if (params.category) queryParams.category = params.category
  if (params.supplier) queryParams.supplier = params.supplier
  if (params.stock_status) queryParams.stock_status = params.stock_status
  if (params.status !== undefined && params.status !== '') queryParams.status = params.status
  if (params.allow_sale !== undefined) queryParams.allow_sale = params.allow_sale
  if (params.min_price !== undefined) queryParams.min_price = params.min_price
  if (params.max_price !== undefined) queryParams.max_price = params.max_price
  if (params.start_date) queryParams.start_date = params.start_date
  if (params.end_date) queryParams.end_date = params.end_date
  
  return queryParams
}

// 获取商品列表
export const getProductList = async (params: ProductListParams): Promise<ProductListResponse> => {
  try {
    const queryParams = buildQueryParams(params)
    const urlParams = new URLSearchParams()
    
    // 构建URL参数
    Object.entries(queryParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        urlParams.append(key, String(value))
      }
    })
    
    const response = await get(`/products?${urlParams.toString()}`)
    const result = await response.json()
    
    if (result.code !== 200) {
      throw new Error(result.message || '获取商品列表失败')
    }
    
    // 处理后端响应格式
    const responseData = result.data
    
    return {
      data: responseData.data || [],
      total: responseData.total || 0,
      current_page: responseData.current_page || 1,
      per_page: responseData.per_page || 20,
      last_page: responseData.last_page || 1,
      from: responseData.from || null,
      to: responseData.to || null
    }
  } catch (error: any) {
    console.error('获取商品列表失败:', error)
    throw new Error(error.message || '获取商品列表失败')
  }
}

// 获取商品统计数据
export const getProductStats = async (): Promise<ProductStats> => {
  try {
    const response = await get('/products/stats')
    const result = await response.json()
    
    if (result.code !== 200) {
      throw new Error(result.message || '获取商品统计数据失败')
    }
    
    return result.data
  } catch (error: any) {
    console.error('获取商品统计数据失败:', error)
    throw new Error(error.message || '获取商品统计数据失败')
  }
}

// 删除商品
export const deleteProduct = async (id: number): Promise<void> => {
  try {
    const response = await del(`/products/${id}`)
    const result = await response.json()
    
    if (result.code !== 200) {
      throw new Error(result.message || '删除商品失败')
    }
  } catch (error: any) {
    console.error('删除商品失败:', error)
    throw new Error(error.message || '删除商品失败')
  }
}

// 更新商品状态
export const updateProductStatus = async (id: number, status: number): Promise<void> => {
  try {
    const response = await put(`/products/${id}/status`, { status })
    const result = await response.json()
    
    if (result.code !== 200) {
      throw new Error(result.message || '更新商品状态失败')
    }
  } catch (error: any) {
    console.error('更新商品状态失败:', error)
    throw new Error(error.message || '更新商品状态失败')
  }
}

// 更新商品可销售状态
export const updateProductAllowSale = async (id: number, allow_sale: boolean): Promise<any> => {
  try {
    const response = await put(`/products/${id}/allow-sale`, { allow_sale })
    const result = await response.json()
    
    if (result.code !== 200) {
      throw new Error(result.message || '更新商品可销售状态失败')
    }
    
    return result
  } catch (error: any) {
    console.error('更新商品可销售状态失败:', error)
    throw new Error(error.message || '更新商品可销售状态失败')
  }
}

// 批量操作
export const batchUpdateProducts = async (ids: number[], action: string): Promise<void> => {
  try {
    const response = await post(`/products/batch-${action}`, { ids })
    const result = await response.json()
    
    if (result.code !== 200) {
      throw new Error(result.message || `批量${action}失败`)
    }
  } catch (error: any) {
    console.error(`批量${action}失败:`, error)
    throw new Error(error.message || `批量${action}失败`)
  }
} 