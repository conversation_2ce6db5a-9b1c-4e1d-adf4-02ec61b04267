<?php

namespace App\Search\Models;

use Illuminate\Database\Eloquent\Model;

class HotKeyword extends Model
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'hot_keywords';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'keyword',
        'weight',
        'highlight',
        'is_manual',
        'is_active'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'weight' => 'integer',
        'highlight' => 'boolean',
        'is_manual' => 'boolean',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
} 