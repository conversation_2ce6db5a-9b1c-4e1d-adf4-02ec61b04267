<?php

namespace App\Product\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class Category extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'parent_id',
        'sort',
        'status',
        'image_url',
        'icon',
        'custom_icon_url',
    ];

    /**
     * 类型转换
     *
     * @var array
     */
    protected $casts = [
        'parent_id' => 'integer',
        'sort' => 'integer',
        'status' => 'integer',
    ];

    /**
     * 获取子分类
     */
    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id', 'id');
    }

    /**
     * 获取激活状态的子分类
     */
    public function activeChildren()
    {
        return $this->hasMany(Category::class, 'parent_id', 'id')->where('status', 1);
    }

    /**
     * 获取父分类
     */
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id', 'id');
    }

    /**
     * 获取该分类下的所有商品
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * 获取分类树（静态方法）
     * 
     * @param int $parentId 父分类ID
     * @param array $conditions 附加条件
     * @return \Illuminate\Support\Collection
     */
    public static function getTree($parentId = 0, $conditions = [])
    {
        $query = self::where('parent_id', $parentId);
        
        if (!empty($conditions)) {
            foreach ($conditions as $key => $value) {
                $query->where($key, $value);
            }
        }
        
        $categories = $query->orderBy('sort', 'asc')->orderBy('id', 'asc')->get();
        
        foreach ($categories as $category) {
            // 递归获取子分类
            $category->children_data = self::getTree($category->id, $conditions);
        }
        
        return $categories;
    }

    /**
     * 获取优化的分类树（减少数据库查询）
     * 
     * @param array $conditions 附加条件
     * @return \Illuminate\Support\Collection
     */
    public static function getOptimizedTree($conditions = [])
    {
        // 一次性获取所有符合条件的分类
        $query = self::query();
        
        if (!empty($conditions)) {
            foreach ($conditions as $key => $value) {
                $query->where($key, $value);
            }
        }
        
        $allCategories = $query->orderBy('sort', 'asc')->orderBy('id', 'asc')->get();
        
        // 按parent_id分组
        $groupedCategories = $allCategories->groupBy('parent_id');
        
        // 构建树形结构
        return self::buildTreeFromGrouped($groupedCategories, 0);
    }

    /**
     * 从分组数据构建树形结构
     * 
     * @param \Illuminate\Support\Collection $groupedCategories
     * @param int $parentId
     * @return \Illuminate\Support\Collection
     */
    private static function buildTreeFromGrouped($groupedCategories, $parentId = 0)
    {
        $result = collect();
        
        if (!$groupedCategories->has($parentId)) {
            return $result;
        }
        
        foreach ($groupedCategories[$parentId] as $category) {
            // 递归获取子分类
            $category->children_data = self::buildTreeFromGrouped($groupedCategories, $category->id);
            $result->push($category);
        }
        
        return $result;
    }

    /**
     * 获取扁平化的分类列表（包含层级信息）
     * 
     * @param array $conditions 附加条件
     * @return \Illuminate\Support\Collection
     */
    public static function getFlatList($conditions = [])
    {
        // 一次性获取所有符合条件的分类
        $query = self::query();
        
        if (!empty($conditions)) {
            foreach ($conditions as $key => $value) {
                $query->where($key, $value);
            }
        }
        
        $allCategories = $query->orderBy('sort', 'asc')->orderBy('id', 'asc')->get();
        
        // 按parent_id分组
        $groupedCategories = $allCategories->groupBy('parent_id');
        
        // 构建扁平化列表
        return self::buildFlatListFromGrouped($groupedCategories, 0, 0);
    }

    /**
     * 从分组数据构建扁平化列表
     * 
     * @param \Illuminate\Support\Collection $groupedCategories
     * @param int $parentId
     * @param int $level
     * @return \Illuminate\Support\Collection
     */
    private static function buildFlatListFromGrouped($groupedCategories, $parentId = 0, $level = 0)
    {
        $result = collect();
        
        if (!$groupedCategories->has($parentId)) {
            return $result;
        }
        
        foreach ($groupedCategories[$parentId] as $category) {
            // 添加层级信息
            $category->level = $level;
            $category->indent = str_repeat('　', $level); // 全角空格用于缩进显示
            $category->display_name = $category->indent . $category->name;
            
            $result->push($category);
            
            // 递归获取子分类
            $children = self::buildFlatListFromGrouped($groupedCategories, $category->id, $level + 1);
            $result = $result->merge($children);
        }
        
        return $result;
    }

    /**
     * 获取激活状态的分类树
     * 
     * @return \Illuminate\Support\Collection
     */
    public static function getActiveTree()
    {
        return self::getTree(0, ['status' => 1]);
    }
    
    /**
     * 获取分类的所有祖先（从根到当前分类的路径）
     * 
     * @return \Illuminate\Support\Collection
     */
    public function getAncestors()
    {
        if ($this->parent_id == 0) {
            return collect();
        }
        
        // 使用优化的方法，一次性获取所有可能的祖先
        $ancestors = collect();
        $currentParentId = $this->parent_id;
        $allCategories = self::all()->keyBy('id');
        
        while ($currentParentId > 0 && $allCategories->has($currentParentId)) {
            $parent = $allCategories[$currentParentId];
            $ancestors->prepend($parent);
            $currentParentId = $parent->parent_id;
        }
        
        return $ancestors;
    }
    
    /**
     * 获取分类路径（包含自己）
     * 
     * @return \Illuminate\Support\Collection
     */
    public function getBreadcrumb()
    {
        $breadcrumb = $this->getAncestors();
        $breadcrumb->push($this);
        return $breadcrumb;
    }
    
    /**
     * 获取分类的所有后代
     * 
     * @param bool $activeOnly 是否只获取激活的分类
     * @return \Illuminate\Support\Collection
     */
    public function getAllDescendants($activeOnly = false)
    {
        // 使用优化的方法获取所有后代
        $conditions = [];
        if ($activeOnly) {
            $conditions['status'] = 1;
        }
        
        // 一次性获取所有分类
        $query = self::query();
        if (!empty($conditions)) {
            foreach ($conditions as $key => $value) {
                $query->where($key, $value);
            }
        }
        $allCategories = $query->get();
        
        // 按parent_id分组
        $groupedCategories = $allCategories->groupBy('parent_id');
        
        // 递归获取所有后代
        return $this->getDescendantsFromGrouped($groupedCategories, $this->id);
    }
    
    /**
     * 从分组数据中获取后代
     * 
     * @param \Illuminate\Support\Collection $groupedCategories
     * @param int $parentId
     * @return \Illuminate\Support\Collection
     */
    private function getDescendantsFromGrouped($groupedCategories, $parentId)
    {
        $descendants = collect();
        
        if (!$groupedCategories->has($parentId)) {
            return $descendants;
        }
        
        foreach ($groupedCategories[$parentId] as $child) {
            $descendants->push($child);
            // 递归获取子分类的后代
            $childDescendants = $this->getDescendantsFromGrouped($groupedCategories, $child->id);
            $descendants = $descendants->merge($childDescendants);
        }
        
        return $descendants;
    }
    
    /**
     * 判断当前分类是否是指定分类的后代
     * 
     * @param int $ancestorId 祖先分类ID
     * @return bool
     */
    public function isDescendantOf($ancestorId)
    {
        if ($this->parent_id == $ancestorId) {
            return true;
        }
        
        $parent = $this->parent;
        while ($parent) {
            if ($parent->id == $ancestorId) {
                return true;
            }
            $parent = $parent->parent;
        }
        
        return false;
    }
    
    /**
     * 判断分类是否有子分类
     * 
     * @param bool $activeOnly 是否只检查激活的子分类
     * @return bool
     */
    public function hasChildren($activeOnly = false)
    {
        if ($activeOnly) {
            return $this->activeChildren()->count() > 0;
        }
        return $this->children()->count() > 0;
    }
    
    /**
     * 获取分类级别（根分类为0级）
     * 
     * @return int
     */
    public function getLevel()
    {
        return $this->getAncestors()->count();
    }
    
    /**
     * 获取分类的完整名称路径
     * 
     * @param string $separator 分隔符
     * @return string
     */
    public function getFullPath($separator = ' > ')
    {
        $names = $this->getAncestors()->map(function ($category) {
            return $category->name;
        })->push($this->name);
        
        return $names->implode($separator);
    }
} 