<?php

namespace App\Region\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RegionRequest extends FormRequest
{
    /**
     * 确定用户是否有权限执行此请求
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name' => 'required|string|max:50',
            'code' => 'nullable|string|max:50',
            'parent_id' => 'nullable|integer|exists:regions,id',
            'level' => 'nullable|integer|min:0|max:5',
            'sort' => 'nullable|integer|min:0',
            'status' => 'nullable|boolean',
            'metadata' => 'nullable|array',
        ];

        // 对于创建操作，code字段必须唯一
        if ($this->isMethod('post')) {
            $rules['code'] = 'nullable|string|max:50|unique:regions,code';
        }
        
        // 对于更新操作，code字段必须唯一，但排除当前记录
        if ($this->isMethod('put') || $this->isMethod('patch')) {
            $rules['code'] = 'nullable|string|max:50|unique:regions,code,' . $this->route('id');
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => '区域名称不能为空',
            'name.max' => '区域名称不能超过50个字符',
            'code.unique' => '区域代码已存在',
            'parent_id.exists' => '父级区域不存在',
            'level.min' => '级别不能小于0',
            'level.max' => '级别不能大于5',
            'sort.min' => '排序值不能小于0',
        ];
    }
} 