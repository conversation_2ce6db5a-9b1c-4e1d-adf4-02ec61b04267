/**
 * 缓存管理器
 * 提供数据缓存功能，减少重复请求，提升页面加载性能
 */
class CacheManager {
	constructor() {
		this.cache = new Map() // 内存缓存
		this.cacheConfig = {
			// 默认缓存配置
			default: {
				ttl: 5 * 60 * 1000, // 5分钟
				maxSize: 100 // 最大缓存条目数
			},
			// 不同数据类型的缓存配置
			clients: {
				ttl: 10 * 60 * 1000, // 客户数据缓存10分钟
				maxSize: 50
			},
			orders: {
				ttl: 3 * 60 * 1000, // 订单数据缓存3分钟
				maxSize: 100
			},
			products: {
				ttl: 30 * 60 * 1000, // 商品数据缓存30分钟
				maxSize: 200
			},
			stats: {
				ttl: 5 * 60 * 1000, // 统计数据缓存5分钟
				maxSize: 20
			}
		}
	}
	
	/**
	 * 生成缓存键
	 * @param {string} type 数据类型
	 * @param {string} key 键名
	 * @param {object} params 参数
	 */
	generateKey(type, key, params = {}) {
		const paramStr = Object.keys(params)
			.sort()
			.map(k => `${k}=${params[k]}`)
			.join('&')
		return `${type}:${key}${paramStr ? ':' + paramStr : ''}`
	}
	
	/**
	 * 设置缓存
	 * @param {string} type 数据类型
	 * @param {string} key 键名
	 * @param {any} data 数据
	 * @param {object} params 参数
	 * @param {number} customTtl 自定义过期时间
	 */
	set(type, key, data, params = {}, customTtl = null) {
		const cacheKey = this.generateKey(type, key, params)
		const config = this.cacheConfig[type] || this.cacheConfig.default
		const ttl = customTtl || config.ttl
		
		const cacheItem = {
			data,
			timestamp: Date.now(),
			ttl,
			type,
			key,
			params
		}
		
		this.cache.set(cacheKey, cacheItem)
		
		// 检查缓存大小，清理过期数据
		this.cleanup(type)
		
		console.log(`缓存设置: ${cacheKey}, TTL: ${ttl}ms`)
	}
	
	/**
	 * 获取缓存
	 * @param {string} type 数据类型
	 * @param {string} key 键名
	 * @param {object} params 参数
	 */
	get(type, key, params = {}) {
		const cacheKey = this.generateKey(type, key, params)
		const cacheItem = this.cache.get(cacheKey)
		
		if (!cacheItem) {
			console.log(`缓存未命中: ${cacheKey}`)
			return null
		}
		
		// 检查是否过期
		if (Date.now() - cacheItem.timestamp > cacheItem.ttl) {
			this.cache.delete(cacheKey)
			console.log(`缓存已过期: ${cacheKey}`)
			return null
		}
		
		console.log(`缓存命中: ${cacheKey}`)
		return cacheItem.data
	}
	
	/**
	 * 删除缓存
	 * @param {string} type 数据类型
	 * @param {string} key 键名
	 * @param {object} params 参数
	 */
	delete(type, key, params = {}) {
		const cacheKey = this.generateKey(type, key, params)
		const deleted = this.cache.delete(cacheKey)
		if (deleted) {
			console.log(`缓存删除: ${cacheKey}`)
		}
		return deleted
	}
	
	/**
	 * 清除指定类型的所有缓存
	 * @param {string} type 数据类型
	 */
	clearType(type) {
		let count = 0
		for (const [key, item] of this.cache.entries()) {
			if (item.type === type) {
				this.cache.delete(key)
				count++
			}
		}
		console.log(`清除 ${type} 类型缓存: ${count} 条`)
		return count
	}
	
	/**
	 * 清除所有缓存
	 */
	clearAll() {
		const count = this.cache.size
		this.cache.clear()
		console.log(`清除所有缓存: ${count} 条`)
		return count
	}
	
	/**
	 * 清理过期缓存
	 * @param {string} type 数据类型
	 */
	cleanup(type = null) {
		const now = Date.now()
		let cleanupCount = 0
		
		for (const [key, item] of this.cache.entries()) {
			// 如果指定了类型，只清理该类型
			if (type && item.type !== type) continue
			
			// 清理过期数据
			if (now - item.timestamp > item.ttl) {
				this.cache.delete(key)
				cleanupCount++
			}
		}
		
		// 如果指定类型，检查该类型缓存数量是否超限
		if (type) {
			const config = this.cacheConfig[type] || this.cacheConfig.default
			const typeItems = Array.from(this.cache.entries())
				.filter(([key, item]) => item.type === type)
				.sort(([, a], [, b]) => b.timestamp - a.timestamp) // 按时间倒序
			
			// 如果超过最大数量，删除最旧的
			if (typeItems.length > config.maxSize) {
				const toDelete = typeItems.slice(config.maxSize)
				toDelete.forEach(([key]) => {
					this.cache.delete(key)
					cleanupCount++
				})
			}
		}
		
		if (cleanupCount > 0) {
			console.log(`清理缓存: ${cleanupCount} 条`)
		}
	}
	
	/**
	 * 获取缓存统计信息
	 */
	getStats() {
		const stats = {
			total: this.cache.size,
			types: {}
		}
		
		for (const [key, item] of this.cache.entries()) {
			if (!stats.types[item.type]) {
				stats.types[item.type] = 0
			}
			stats.types[item.type]++
		}
		
		return stats
	}
	
	/**
	 * 检查缓存是否存在且未过期
	 * @param {string} type 数据类型
	 * @param {string} key 键名
	 * @param {object} params 参数
	 */
	has(type, key, params = {}) {
		return this.get(type, key, params) !== null
	}
	
	/**
	 * 获取或设置缓存（如果不存在则执行获取函数）
	 * @param {string} type 数据类型
	 * @param {string} key 键名
	 * @param {function} fetchFn 获取数据的函数
	 * @param {object} params 参数
	 * @param {number} customTtl 自定义过期时间
	 */
	async getOrSet(type, key, fetchFn, params = {}, customTtl = null) {
		// 先尝试从缓存获取
		const cached = this.get(type, key, params)
		if (cached !== null) {
			return cached
		}
		
		// 缓存未命中，执行获取函数
		try {
			const data = await fetchFn()
			this.set(type, key, data, params, customTtl)
			return data
		} catch (error) {
			console.error(`获取数据失败: ${type}:${key}`, error)
			throw error
		}
	}
}

// 创建全局实例
const cacheManager = new CacheManager()

// 定期清理过期缓存（每5分钟）
setInterval(() => {
	cacheManager.cleanup()
}, 5 * 60 * 1000)

export default cacheManager 