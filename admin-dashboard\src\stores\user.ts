import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login, getUserInfo, logout, type LoginParams, type UserInfo } from '@/api/auth'
import { Storage } from '@/utils/storage'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 用户信息
  const userInfo = ref<UserInfo | null>(null)
  
  // 登录状态
  const isLoggedIn = ref(false)
  
  // token
  const token = ref('')
  
  // 初始化用户状态
  const initUserState = () => {
    const savedToken = Storage.getToken()
    const savedUser = Storage.getUser()
    
    if (savedToken && savedUser) {
      token.value = savedToken
      userInfo.value = savedUser
      isLoggedIn.value = true
      
      // 检查token是否即将过期，提前刷新
      if (Storage.isTokenExpiringSoon()) {
        console.log('Token即将过期，建议刷新')
        // 这里可以自动刷新token
      }
    }
  }
  
  // 登录
  const loginUser = async (params: LoginParams) => {
    try {
      const response = await login(params)
      
      // 保存token和用户信息
      token.value = response.token
      userInfo.value = response.user
      isLoggedIn.value = true
      
      // 使用新的存储工具
      Storage.setToken(response.token, response.expires_in)
      Storage.setUser(response.user)
      
      ElMessage.success('登录成功')
      return response
      
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      throw error
    }
  }
  
  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const user = await getUserInfo()
      userInfo.value = user
      Storage.setUser(user)
      return user
    } catch (error: any) {
      ElMessage.error('获取用户信息失败')
      await logoutUser()
      throw error
    }
  }
  
  // 退出登录
  const logoutUser = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('退出登录失败:', error)
    } finally {
      // 清除状态
      token.value = ''
      userInfo.value = null
      isLoggedIn.value = false
      
      // 使用新的存储工具清除认证信息
      Storage.clearAuth()
      
      ElMessage.success('已退出登录')
    }
  }
  
  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (!userInfo.value) return false
    
    // 超级管理员拥有所有权限
    if (userInfo.value.permissions.includes('*')) return true
    
    return userInfo.value.permissions.includes(permission)
  }
  
  // 检查角色
  const hasRole = (role: string): boolean => {
    if (!userInfo.value) return false
    return userInfo.value.roles.includes(role)
  }
  
  // 获取token剩余时间
  const getTokenRemainingTime = (): number => {
    return Storage.getTokenRemainingTime()
  }
  
  return {
    userInfo,
    isLoggedIn,
    token,
    initUserState,
    loginUser,
    fetchUserInfo,
    logoutUser,
    hasPermission,
    hasRole,
    getTokenRemainingTime
  }
}) 