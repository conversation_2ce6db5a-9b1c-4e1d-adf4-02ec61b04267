<?php

namespace App\Crm\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Crm\Models\ClientFollowUp;
use App\Crm\Models\CrmAgent;
use App\Models\User;
use App\Employee\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class ClientFollowUpController extends Controller
{
    /**
     * 获取跟进记录列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $currentUser = Auth::user();
            
            // 查询参数
            $userId = $request->input('user_id');
            $employeeId = $request->input('employee_id'); // 改为employee_id
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $result = $request->input('result');
            $contactMethod = $request->input('contact_method');
            $keyword = $request->input('keyword'); // 添加关键词搜索
            $perPage = $request->input('per_page', 15);
            
            // 构建查询
            $query = ClientFollowUp::with(['client', 'agent']);
            
            // 权限控制：根据用户角色决定数据范围
            $currentEmployee = Employee::where('user_id', $currentUser->id)->first();
            
            if ($currentEmployee) {
                // 如果是管理员或经理，可以查看所有数据
                if (in_array($currentEmployee->role, ['admin', 'manager'])) {
                    // 管理员和经理可以查看所有跟进记录，不添加额外限制
                } else {
                    // 其他员工（如CRM专员）只能查看自己的跟进记录
                    $crmAgent = CrmAgent::where('employee_id', $currentEmployee->id)->first();
                    if ($crmAgent) {
                        $query->where('employee_id', $currentEmployee->id);
                    }
                }
            }
            // 如果不是员工，默认可以查看所有数据（可能是超级管理员等）
            
            // 筛选条件
            if ($userId) {
                $query->where('user_id', $userId);
            }
            
            if ($employeeId) {
                $query->where('employee_id', $employeeId);
            }
            
            if ($startDate) {
                $query->whereDate('follow_up_date', '>=', $startDate);
            }
            
            if ($endDate) {
                $query->whereDate('follow_up_date', '<=', $endDate);
            }
            
            if ($result) {
                $query->where('result', $result);
            }
            
            if ($contactMethod) {
                $query->where('contact_method', $contactMethod);
            }
            
            // 关键词搜索（搜索客户姓名、跟进内容）
            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->where('notes', 'like', "%{$keyword}%")
                      ->orWhereHas('client', function($clientQuery) use ($keyword) {
                          $clientQuery->where('name', 'like', "%{$keyword}%")
                                     ->orWhere('phone', 'like', "%{$keyword}%");
                      });
                });
            }
            
            // 排序
            $sortField = $request->input('sort', 'follow_up_date');
            $sortDirection = $request->input('direction', 'desc');
            $query->orderBy($sortField, $sortDirection);
            
            $followUps = $query->paginate($perPage);
            
            return response()->json(ApiResponse::success($followUps));
            
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('获取跟进记录失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 创建跟进记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $currentUser = Auth::user();
            
            $validator = Validator::make($request->all(), [
                'user_id' => 'required|exists:users,id',
                'employee_id' => 'required|exists:employees,id', // 改为employee_id
                'follow_up_date' => 'required|date',
                'contact_method' => 'required|in:phone,sms,email,visit,wechat,other',
                'notes' => 'required|string|max:2000',
                'result' => 'required|in:successful,follow_up,no_answer,rejected,other',
                'next_follow_up' => 'nullable|date|after:follow_up_date',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            // 权限检查
            $currentEmployee = Employee::where('user_id', $currentUser->id)->first();
            if (!$currentEmployee) {
                return response()->json(ApiResponse::error('只有员工才能创建跟进记录', 403), 403);
            }
            
            // 检查是否为自己创建跟进记录
            if ($currentEmployee->id != $request->employee_id) {
                return response()->json(ApiResponse::error('只能为自己创建跟进记录', 403), 403);
            }
            
            // 检查客户是否分配给该CRM专员
            $user = User::find($request->user_id);
            if (!$user || $user->crm_agent_id != $request->employee_id) {
                return response()->json(ApiResponse::error('该客户未分配给您', 400), 400);
            }
            
            $followUp = ClientFollowUp::create([
                'user_id' => $request->user_id,
                'employee_id' => $request->employee_id,
                'follow_up_date' => $request->follow_up_date,
                'contact_method' => $request->contact_method,
                'notes' => $request->notes,
                'result' => $request->result,
                'next_follow_up' => $request->next_follow_up,
            ]);
            
            // 加载关联数据
            $followUp->load(['client', 'agent']);
            
            return response()->json(ApiResponse::success($followUp, '跟进记录创建成功'), 201);
            
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('创建跟进记录失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取跟进记录详情
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        try {
            // 使用与其他CRM API相同的认证方式
            $currentUser = $request->user();
            
            if (!$currentUser) {
                return response()->json(ApiResponse::error('未认证或认证已过期', 401), 401);
            }
            
            $followUp = ClientFollowUp::with(['client', 'agent'])->findOrFail($id);
            
            $currentEmployee = null;
            
            // 尝试获取Employee记录（如果当前用户是员工）
            if ($currentUser instanceof \App\Employee\Models\Employee) {
                $currentEmployee = $currentUser;
            } else {
                // 如果是User模型，尝试通过user_id查找对应的Employee
                $currentEmployee = Employee::where('user_id', $currentUser->id)->first();
            }
            
            // 权限检查
            $isAdmin = false;
            if ($currentEmployee) {
                $isAdmin = in_array($currentEmployee->role, ['admin', 'manager']);
            } elseif ($currentUser instanceof \App\Models\User) {
                // 如果是User模型，检查是否为超级管理员
                $isAdmin = in_array($currentUser->id, [1]) || $currentUser->email === '<EMAIL>';
            }
            
            if (!$isAdmin && $currentEmployee) {
                // 非管理员员工只能查看自己的跟进记录
                if ($currentEmployee->id != $followUp->employee_id) {
                    return response()->json(ApiResponse::error('没有权限查看此跟进记录', 403), 403);
                }
            }
            
            return response()->json(ApiResponse::success($followUp));
            
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('获取跟进记录详情失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 更新跟进记录
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            // 使用与其他CRM API相同的认证方式
            $currentUser = $request->user();
            
            if (!$currentUser) {
                return response()->json(ApiResponse::error('未认证或认证已过期', 401), 401);
            }
            
            $followUp = ClientFollowUp::findOrFail($id);
            
            $currentEmployee = null;
            
            // 尝试获取Employee记录（如果当前用户是员工）
            if ($currentUser instanceof \App\Employee\Models\Employee) {
                $currentEmployee = $currentUser;
            } else {
                // 如果是User模型，尝试通过user_id查找对应的Employee
                $currentEmployee = Employee::where('user_id', $currentUser->id)->first();
            }
            
            // 权限检查
            $isAdmin = false;
            if ($currentEmployee) {
                $isAdmin = in_array($currentEmployee->role, ['admin', 'manager']);
            } elseif ($currentUser instanceof \App\Models\User) {
                // 如果是User模型，检查是否为超级管理员
                $isAdmin = in_array($currentUser->id, [1]) || $currentUser->email === '<EMAIL>';
            }
            
            if (!$isAdmin && $currentEmployee) {
                // 非管理员员工只能编辑自己的跟进记录
                if ($currentEmployee->id != $followUp->employee_id) {
                    return response()->json(ApiResponse::error('没有权限更新此跟进记录', 403), 403);
                }
            } elseif (!$isAdmin && !$currentEmployee) {
                return response()->json(ApiResponse::error('没有权限更新此跟进记录', 403), 403);
            }
            
            $validator = Validator::make($request->all(), [
                'follow_up_date' => 'sometimes|date',
                'contact_method' => 'sometimes|in:phone,sms,email,visit,wechat,other',
                'notes' => 'sometimes|string|max:2000',
                'result' => 'sometimes|in:successful,follow_up,no_answer,rejected,other',
                'next_follow_up' => 'nullable|date|after:follow_up_date',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $followUp->update($request->only([
                'follow_up_date',
                'contact_method',
                'notes',
                'result',
                'next_follow_up',
            ]));
            
            // 加载关联数据
            $followUp->load(['client', 'agent']);
            
            return response()->json(ApiResponse::success($followUp, '跟进记录更新成功'));
            
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('更新跟进记录失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 删除跟进记录
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        try {
            // 使用与其他CRM API相同的认证方式
            $currentUser = $request->user();
            
            if (!$currentUser) {
                return response()->json(ApiResponse::error('未认证或认证已过期', 401), 401);
            }
            
            $followUp = ClientFollowUp::findOrFail($id);
            
            $currentEmployee = null;
            
            // 尝试获取Employee记录（如果当前用户是员工）
            if ($currentUser instanceof \App\Employee\Models\Employee) {
                $currentEmployee = $currentUser;
            } else {
                // 如果是User模型，尝试通过user_id查找对应的Employee
                $currentEmployee = Employee::where('user_id', $currentUser->id)->first();
            }
            
            // 权限检查
            $isAdmin = false;
            if ($currentEmployee) {
                $isAdmin = in_array($currentEmployee->role, ['admin', 'manager']);
            } elseif ($currentUser instanceof \App\Models\User) {
                // 如果是User模型，检查是否为超级管理员
                $isAdmin = in_array($currentUser->id, [1]) || $currentUser->email === '<EMAIL>';
            }
            
            if (!$isAdmin && $currentEmployee) {
                // 非管理员员工只能删除自己的跟进记录
                if ($currentEmployee->id != $followUp->employee_id) {
                    return response()->json(ApiResponse::error('没有权限删除此跟进记录', 403), 403);
                }
            } elseif (!$isAdmin && !$currentEmployee) {
                return response()->json(ApiResponse::error('没有权限删除此跟进记录', 403), 403);
            }
            
            $followUp->delete();
            return response()->json(ApiResponse::success(null, '跟进记录删除成功'));
            
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('删除跟进记录失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取用户的跟进记录
     *
     * @param Request $request
     * @param int $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserFollowUps(Request $request, $userId)
    {
        try {
            // 使用与其他CRM API相同的认证方式
            $currentUser = $request->user();
            
            if (!$currentUser) {
                return response()->json(ApiResponse::error('未认证或认证已过期', 401), 401);
            }
            
            $currentEmployee = null;
            
            // 尝试获取Employee记录（如果当前用户是员工）
            if ($currentUser instanceof \App\Employee\Models\Employee) {
                $currentEmployee = $currentUser;
            } else {
                // 如果是User模型，尝试通过user_id查找对应的Employee
                $currentEmployee = Employee::where('user_id', $currentUser->id)->first();
            }
            
            // 权限检查
            $isAdmin = false;
            if ($currentEmployee) {
                $isAdmin = in_array($currentEmployee->role, ['admin', 'manager']);
            } elseif ($currentUser instanceof \App\Models\User) {
                // 如果是User模型，检查是否为超级管理员
                $isAdmin = in_array($currentUser->id, [1]) || $currentUser->email === '<EMAIL>';
            }
            
            if (!$isAdmin && $currentEmployee) {
                // 非管理员员工只能查看分配给自己的客户的跟进记录
                $user = User::find($userId);
                if (!$user || $user->crm_agent_id != $currentEmployee->id) {
                    return response()->json(ApiResponse::error('该用户不是您的客户', 403), 403);
                }
            }
            
            $perPage = $request->input('per_page', 15);
            $followUps = ClientFollowUp::with(['agent'])
                ->where('user_id', $userId)
                ->orderBy('follow_up_date', 'desc')
                ->paginate($perPage);
                
            return response()->json(ApiResponse::success($followUps));
            
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('获取用户跟进记录失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取CRM专员的跟进记录
     *
     * @param Request $request
     * @param int $employeeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAgentFollowUps(Request $request, $employeeId)
    {
        try {
            // 使用与其他CRM API相同的认证方式
            $currentUser = $request->user();
            
            if (!$currentUser) {
                return response()->json(ApiResponse::error('未认证或认证已过期', 401), 401);
            }
            
            $currentEmployee = null;
            
            // 尝试获取Employee记录（如果当前用户是员工）
            if ($currentUser instanceof \App\Employee\Models\Employee) {
                $currentEmployee = $currentUser;
            } else {
                // 如果是User模型，尝试通过user_id查找对应的Employee
                $currentEmployee = Employee::where('user_id', $currentUser->id)->first();
            }
            
            // 权限检查
            $isAdmin = false;
            if ($currentEmployee) {
                $isAdmin = in_array($currentEmployee->role, ['admin', 'manager']);
            } elseif ($currentUser instanceof \App\Models\User) {
                // 如果是User模型，检查是否为超级管理员
                $isAdmin = in_array($currentUser->id, [1]) || $currentUser->email === '<EMAIL>';
            }
            
            if (!$isAdmin && $currentEmployee) {
                // 非管理员员工只能查看自己的跟进记录
                if ($currentEmployee->id != $employeeId) {
                    return response()->json(ApiResponse::error('没有权限查看此CRM专员的跟进记录', 403), 403);
                }
            } elseif (!$isAdmin && !$currentEmployee) {
                return response()->json(ApiResponse::error('没有权限查看此CRM专员的跟进记录', 403), 403);
            }
            
            $perPage = $request->input('per_page', 15);
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $result = $request->input('result');
            
            $query = ClientFollowUp::with(['client'])
                ->where('employee_id', $employeeId);
                
            if ($startDate) {
                $query->whereDate('follow_up_date', '>=', $startDate);
            }
            
            if ($endDate) {
                $query->whereDate('follow_up_date', '<=', $endDate);
            }
            
            if ($result) {
                $query->where('result', $result);
            }
            
            $followUps = $query->orderBy('follow_up_date', 'desc')
                ->paginate($perPage);
                
            return response()->json(ApiResponse::success($followUps));
            
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('获取CRM专员跟进记录失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 创建下一次跟进记录
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function createNextFollowUp(Request $request, $id)
    {
        try {
            $currentUser = Auth::user();
            $previousFollowUp = ClientFollowUp::findOrFail($id);
            
            // 权限检查
            $currentEmployee = Employee::where('user_id', $currentUser->id)->first();
            if ($currentEmployee) {
                // 管理员和经理可以为任何跟进记录创建后续跟进
                if (!in_array($currentEmployee->role, ['admin', 'manager'])) {
                    // 非管理员/经理只能为自己的跟进记录创建后续跟进
                    if ($currentEmployee->id != $previousFollowUp->employee_id) {
                        return response()->json(ApiResponse::error('没有权限创建后续跟进记录', 403), 403);
                    }
                }
            } else {
                return response()->json(ApiResponse::error('没有权限创建后续跟进记录', 403), 403);
            }
            
            $validator = Validator::make($request->all(), [
                'follow_up_date' => 'required|date',
                'contact_method' => 'required|in:phone,sms,email,visit,wechat,other',
                'notes' => 'required|string|max:2000',
                'result' => 'sometimes|in:successful,follow_up,no_answer,rejected,other',
                'next_follow_up' => 'nullable|date|after:follow_up_date',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $followUp = ClientFollowUp::create([
                'user_id' => $previousFollowUp->user_id,
                'employee_id' => $previousFollowUp->employee_id,
                'follow_up_date' => $request->follow_up_date,
                'contact_method' => $request->contact_method,
                'notes' => $request->notes,
                'result' => $request->input('result', 'follow_up'),
                'next_follow_up' => $request->next_follow_up,
            ]);
            
            // 加载关联数据
            $followUp->load(['client', 'agent']);
            
            return response()->json(ApiResponse::success($followUp, '后续跟进记录创建成功'), 201);
            
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('创建后续跟进记录失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 调试统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function debugStats(Request $request)
    {
        try {
            // 使用与其他CRM API相同的认证方式
            $currentUser = $request->user();
            
            if (!$currentUser) {
                return response()->json([
                    'code' => 401,
                    'message' => '未认证',
                    'debug_info' => [
                        'auth_check' => false,
                        'total_follow_ups' => ClientFollowUp::count(),
                        'sample_follow_up' => ClientFollowUp::first(),
                    ]
                ], 401);
            }
            
            $currentEmployee = null;
            
            // 尝试获取Employee记录（如果当前用户是员工）
            if ($currentUser instanceof \App\Employee\Models\Employee) {
                $currentEmployee = $currentUser;
            } else {
                // 如果是User模型，尝试通过user_id查找对应的Employee
                $currentEmployee = Employee::where('user_id', $currentUser->id)->first();
            }
            
            $debug = [
                'auth_check' => true,
                'current_user_id' => $currentUser->id,
                'current_user_name' => $currentUser->name ?? 'N/A',
                'current_user_type' => get_class($currentUser),
                'current_employee' => $currentEmployee ? [
                    'id' => $currentEmployee->id,
                    'name' => $currentEmployee->name,
                    'role' => $currentEmployee->role,
                ] : null,
                'total_follow_ups' => ClientFollowUp::count(),
                'follow_ups_with_employee' => ClientFollowUp::whereNotNull('employee_id')->count(),
                'employee_ids_in_follow_ups' => ClientFollowUp::distinct()->pluck('employee_id')->toArray(),
                'all_employees' => Employee::select('id', 'name', 'role')->get()->toArray(),
                'sample_follow_up' => ClientFollowUp::first(),
            ];
            
            // 如果有当前员工，检查该员工的跟进记录
            if ($currentEmployee) {
                $debug['current_employee_follow_ups'] = ClientFollowUp::where('employee_id', $currentEmployee->id)->count();
                $debug['is_admin_or_manager'] = in_array($currentEmployee->role, ['admin', 'manager']);
                
                // 测试统计查询
                $query = ClientFollowUp::query();
                if (!in_array($currentEmployee->role, ['admin', 'manager'])) {
                    $query->where('employee_id', $currentEmployee->id);
                }
                $debug['test_query_count'] = $query->count();
                $debug['test_query_results'] = $query->limit(3)->get()->toArray();
            } else {
                // 如果不是员工，测试无权限限制的查询
                $debug['test_query_count_no_restriction'] = ClientFollowUp::count();
                $debug['test_query_results_no_restriction'] = ClientFollowUp::limit(3)->get()->toArray();
            }
            
            return response()->json([
                'code' => 200,
                'message' => '调试成功',
                'data' => $debug
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '调试失败: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * 获取跟进统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFollowUpStats(Request $request)
    {
        try {
            // 使用与其他CRM API相同的认证方式
            $currentUser = $request->user();
            
            if (!$currentUser) {
                return response()->json(ApiResponse::error('未认证或认证已过期', 401), 401);
            }
            
            $currentEmployee = null;
            
            // 尝试获取Employee记录（如果当前用户是员工）
            if ($currentUser instanceof \App\Employee\Models\Employee) {
                $currentEmployee = $currentUser;
            } else {
                // 如果是User模型，尝试通过user_id查找对应的Employee
                $currentEmployee = Employee::where('user_id', $currentUser->id)->first();
            }
            
            // 如果没有传入日期范围，则查询所有数据
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            
            // 构建基础查询
            $query = ClientFollowUp::query();
            
            // 添加日期范围筛选（如果提供了日期参数）
            if ($startDate && $endDate) {
                $query->whereBetween('follow_up_date', [$startDate, $endDate]);
            } elseif ($startDate) {
                $query->whereDate('follow_up_date', '>=', $startDate);
            } elseif ($endDate) {
                $query->whereDate('follow_up_date', '<=', $endDate);
            }
            
            // 权限控制：根据用户角色决定数据范围
            $isRestricted = false;
            
            // 检查是否为超级管理员或管理员角色
            $isAdmin = false;
            if ($currentEmployee) {
                $isAdmin = in_array($currentEmployee->role, ['admin', 'manager']);
            } elseif ($currentUser instanceof \App\Models\User) {
                // 如果是User模型，检查是否为超级管理员
                $isAdmin = in_array($currentUser->id, [1]) || $currentUser->email === '<EMAIL>';
            }
            
            if (!$isAdmin && $currentEmployee) {
                // 非管理员员工只能查看自己的统计数据
                $query->where('employee_id', $currentEmployee->id);
                $isRestricted = true;
            }
            // 如果是管理员或者没有Employee记录，可以查看所有数据
            
            // 获取统计数据
            $total = (clone $query)->count();
            
            $byResult = (clone $query)->groupBy('result')
                ->selectRaw('result, count(*) as count')
                ->pluck('count', 'result')
                ->toArray();
                
            $byMethod = (clone $query)->groupBy('contact_method')
                ->selectRaw('contact_method, count(*) as count')
                ->pluck('count', 'contact_method')
                ->toArray();
            
            // 待跟进客户数（7天内需要跟进的）
            $pendingQuery = ClientFollowUp::whereNotNull('next_follow_up')
                ->where('next_follow_up', '<=', now()->addDays(7));
                
            if ($isRestricted && $currentEmployee) {
                $pendingQuery->where('employee_id', $currentEmployee->id);
            }
            
            $pendingFollowUps = $pendingQuery->count();
            
            $stats = [
                'total' => $total,
                'by_result' => $byResult,
                'by_method' => $byMethod,
                'pending_follow_ups' => $pendingFollowUps,
                // 调试信息（生产环境可以移除）
                'debug' => [
                    'user_id' => $currentUser->id,
                    'user_type' => get_class($currentUser),
                    'employee_id' => $currentEmployee ? $currentEmployee->id : null,
                    'employee_role' => $currentEmployee ? $currentEmployee->role : null,
                    'is_admin' => $isAdmin,
                    'is_restricted' => $isRestricted,
                    'date_filter' => [
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                    ],
                    'total_records_in_db' => ClientFollowUp::count(),
                ]
            ];
            
            return response()->json(ApiResponse::success($stats));
            
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('获取统计数据失败: ' . $e->getMessage(), 500), 500);
        }
    }
}