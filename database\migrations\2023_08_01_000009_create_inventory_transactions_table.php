<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('transaction_type_id')->constrained('inventory_transaction_types')->comment('事务类型ID');
            $table->string('reference_type')->nullable()->comment('关联类型：订单、采购单等');
            $table->unsignedBigInteger('reference_id')->nullable()->comment('关联ID');
            $table->foreignId('product_id')->constrained()->comment('商品ID');
            $table->foreignId('warehouse_id')->constrained()->comment('仓库ID');
            $table->decimal('quantity', 10, 2)->comment('数量（正数为入库，负数为出库）');
            $table->foreignId('unit_id')->constrained()->comment('单位ID');
            $table->decimal('unit_price', 10, 2)->nullable()->comment('单价');
            $table->decimal('total_amount', 12, 2)->nullable()->comment('总金额');
            $table->enum('status', ['draft', 'pending', 'completed', 'canceled'])
                  ->default('draft')->comment('状态：草稿，待处理，已完成，已取消');
            $table->text('notes')->nullable()->comment('备注');
            $table->foreignId('created_by')->constrained('users')->comment('创建人');
            $table->foreignId('updated_by')->nullable()->constrained('users')->comment('更新人');
            $table->timestamps();
            
            // 添加索引
            $table->index(['reference_type', 'reference_id']);
            $table->index(['product_id', 'warehouse_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_transactions');
    }
}; 