<?php

namespace App\Order\Services;

use App\Order\Models\Order;
use Illuminate\Support\Collection;

class SimplePaymentMergeService
{
    /**
     * 验证支付方式合并规则（简化版）
     */
    public function validatePaymentMethodMerge(Collection $orders): array
    {
        $paymentMethods = $orders->pluck('payment_method')->unique();
        
        // 核心规则：只允许相同支付方式的订单合并
        if ($paymentMethods->count() > 1) {
            return [
                'allowed' => false,
                'reason' => '只能合并相同支付方式的订单',
                'found_methods' => $paymentMethods->map([$this, 'getPaymentMethodName'])->toArray(),
                'suggestion' => '请分别合并相同支付方式的订单',
            ];
        }
        
        $paymentMethod = $paymentMethods->first();
        
        // 特殊检查：货到付款订单的额外验证
        if ($paymentMethod === 'cod') {
            return $this->validateCodOrdersMerge($orders);
        }
        
        return [
            'allowed' => true,
            'payment_method' => $paymentMethod,
            'payment_method_name' => $this->getPaymentMethodName($paymentMethod),
            'order_count' => $orders->count(),
        ];
    }
    
    /**
     * 货到付款订单的特殊验证
     */
    private function validateCodOrdersMerge(Collection $orders): array
    {
        // 检查所有订单是否都是货到付款且未支付
        foreach ($orders as $order) {
            if (!$order->isCashOnDelivery()) {
                return [
                    'allowed' => false,
                    'reason' => '非货到付款订单无法与货到付款订单合并',
                ];
            }
            
            if ($order->cod_status !== 'unpaid') {
                return [
                    'allowed' => false,
                    'reason' => '只能合并未支付的货到付款订单',
                    'invalid_order' => $order->order_no,
                    'cod_status' => $order->cod_status,
                ];
            }
        }
        
        // 检查是否同一配送区域（货到付款需要考虑配送）
        $regions = $orders->pluck('region_id')->unique();
        if ($regions->count() > 1) {
            return [
                'allowed' => false,
                'reason' => '货到付款订单只能合并同一配送区域的订单',
                'found_regions' => $regions->toArray(),
            ];
        }
        
        return [
            'allowed' => true,
            'payment_method' => 'cod',
            'payment_method_name' => '货到付款',
            'order_count' => $orders->count(),
            'special_notes' => '货到付款订单合并，配送时统一收款',
        ];
    }
    
    /**
     * 获取支付方式名称
     */
    public function getPaymentMethodName(string $method): string
    {
        $names = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'bank' => '银行转账',
            'cash' => '现金支付',
            'cod' => '货到付款',
        ];
        
        return $names[$method] ?? $method;
    }
    
    /**
     * 获取合并后的支付方式信息
     */
    public function getMergedPaymentInfo(Collection $orders): array
    {
        $paymentMethod = $orders->first()->payment_method;
        
        $info = [
            'payment_method' => $paymentMethod,
            'payment_method_name' => $this->getPaymentMethodName($paymentMethod),
            'order_count' => $orders->count(),
            'total_amount' => $orders->sum('total'),
        ];
        
        // 货到付款的特殊信息
        if ($paymentMethod === 'cod') {
            $info['cod_info'] = [
                'all_unpaid' => $orders->every(fn($order) => $order->cod_status === 'unpaid'),
                'delivery_region' => $orders->first()->region_id,
                'delivery_notes' => '合并订单，配送时统一收款',
            ];
        }
        
        return $info;
    }
    
    /**
     * 按支付方式分组订单
     */
    public function groupOrdersByPaymentMethod(Collection $orders): array
    {
        $groups = [];
        
        foreach ($orders as $order) {
            $method = $order->payment_method;
            
            if (!isset($groups[$method])) {
                $groups[$method] = [
                    'payment_method' => $method,
                    'payment_method_name' => $this->getPaymentMethodName($method),
                    'orders' => [],
                    'total_amount' => 0,
                    'order_count' => 0,
                ];
            }
            
            $groups[$method]['orders'][] = $order;
            $groups[$method]['total_amount'] += $order->total;
            $groups[$method]['order_count']++;
        }
        
        return array_values($groups);
    }
} 