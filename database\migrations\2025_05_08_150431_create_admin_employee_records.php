<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Hash;

return new class extends Migration
{
    /**
     * 为管理员角色的用户创建员工记录并建立关联
     */
    public function up(): void
    {
        // 不创建新表，直接进行数据迁移处理
        DB::statement('SET FOREIGN_KEY_CHECKS=0');
        
        // 检查是否已经有员工表
        if (Schema::hasTable('employees')) {
            // 找出所有管理员角色的用户（如果仍存在role字段）
            $adminUsers = DB::table('users')
                ->whereIn('id', function($query) {
                    // 查找已关联有员工记录的用户
                    $query->select('user_id')
                        ->from('employees')
                        ->whereNotNull('user_id');
                }, 'or')
                ->get();
            
            // 为每一个管理员用户创建或更新员工记录
            foreach ($adminUsers as $user) {
                // 先检查用户是否已有关联的员工记录
                $employee = DB::table('employees')
                    ->where('user_id', $user->id)
                    ->first();
                
                if (!$employee) {
                    // 创建新的员工记录
                    DB::table('employees')->insert([
                        'name' => $user->name ?? '管理员',
                        'username' => $user->phone ?? ('admin' . $user->id),
                        'password' => Hash::make('password123'), // 默认密码，应该提示用户修改
                        'phone' => $user->phone ?? null,
                        'position' => '系统管理员',
                        'role' => 'admin',
                        'user_id' => $user->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    
                    // 输出日志
                    echo "为用户ID:{$user->id} 创建了员工管理员记录\n";
                } else {
                    // 如果已有员工记录，确保角色是admin
                    DB::table('employees')
                        ->where('id', $employee->id)
                        ->update([
                            'role' => 'admin',
                            'position' => '系统管理员',
                            'updated_at' => now(),
                        ]);
                    
                    // 输出日志
                    echo "更新用户ID:{$user->id} 的员工记录为管理员角色\n";
                }
            }
        }
        
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        // 不做任何回滚操作，因为没有创建新表，仅进行了数据迁移
    }
};
