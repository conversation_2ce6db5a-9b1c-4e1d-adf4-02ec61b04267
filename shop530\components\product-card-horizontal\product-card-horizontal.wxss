/* 横向商品卡片 - 修复懒加载 */

.horizontal-product-card {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  transition: transform 0.2s ease;
  border: 2rpx solid transparent; /* 默认透明边框 */
}

.horizontal-product-card:active {
  transform: scale(0.98);
}

/* ========== 特殊状态边框 ========== */

/* 促销状态 - 橙红色边框 */
.horizontal-product-card.promotion {
  border: 2rpx solid #ff6b35;
  box-shadow: 0 4rpx 20rpx rgba(255, 107, 53, 0.15);
}

.horizontal-product-card.promotion .product-title {
  color: #ff6b35;
}

/* 新品状态 - 绿色边框 */
.horizontal-product-card.new-product {
  border: 2rpx solid #4CAF50;
  box-shadow: 0 4rpx 20rpx rgba(76, 175, 80, 0.15);
}

.horizontal-product-card.new-product .product-title {
  color: #4CAF50;
}

/* 缺货状态 - 整体灰度效果 */
.horizontal-product-card.out-of-stock {
  filter: grayscale(0.8);
  opacity: 0.7;
  border-color: #e0e0e0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.horizontal-product-card.out-of-stock .product-title {
  color: #999;
}

.horizontal-product-card.out-of-stock .price-display.product-price-horizontal .price-symbol,
.horizontal-product-card.out-of-stock .price-display.product-price-horizontal .price-main,
.horizontal-product-card.out-of-stock .price-display.product-price-horizontal .price-unit {
  color: #999 !important;
}

/* 商品图片区域 */
.product-image-wrapper {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
  position: relative; /* 为遮罩定位 */
}

/* 缺货遮罩 */
.out-of-stock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  z-index: 2;
}

.out-of-stock-text {
  color: #fff;
  font-size: 24rpx;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 图片样式 */
.product-image {
  border-radius: 12rpx;
}

/* 商品信息区域 */
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
}

/* ========== 商品标签区域 ========== */
.product-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

/* 默认标签样式 - 橙色渐变 */
.product-tag {
  background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%) !important;
  color: #fff !important;
  font-size: 18rpx !important;
  padding: 4rpx 8rpx !important;
  border-radius: 12rpx !important;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3) !important;
  backdrop-filter: blur(8rpx);
  border: none !important;
}

/* 促销类标签 - 橙红色 */
.product-tag[data-type="promotion"],
.product-tag.promotion-tag {
  background: linear-gradient(135deg, #ff6b35 0%, #ff4444 100%) !important;
  box-shadow: 0 2rpx 8rpx rgba(255, 68, 68, 0.3) !important;
}

/* 新品类标签 - 绿色 */
.product-tag[data-type="new"],
.product-tag.new-tag {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%) !important;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3) !important;
}

.product-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  transition: color 0.3s ease; /* 标题颜色过渡动画 */
}

.product-subtitle {
  font-size: 26rpx;
  color: #888;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 价格和操作区域 */
.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.price-section {
  flex: 1;
  min-width: 0;
}

/* 价格显示组件样式 */
.price-section .price-display.product-price-horizontal {
  align-self: flex-start;
  margin-bottom: 8rpx;
}

.price-section .price-display.product-price-horizontal .price-symbol {
  font-size: 24rpx;
  color: #ff4444;
  font-weight: 600;
}

.price-section .price-display.product-price-horizontal .price-main {
  font-size: 36rpx;
  color: #ff4444;
  font-weight: 700;
}

.price-section .price-display.product-price-horizontal .price-unit {
  font-size: 24rpx;
  color: #ff4444;
  font-weight: 500;
}

.price-section .price-display.product-price-horizontal .login-prompt {
  font-size: 32rpx;
  color: #999;
  font-weight: 500;
}

/* ========== 购物车控制区域 ========== */
.cart-controls {
  display: flex;
  align-items: center;
  margin-left: 16rpx;
  flex-shrink: 0;
}

/* 加购按钮 */
.add-cart-button {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: #00C853;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 200, 83, 0.3);
  transition: all 0.2s ease;
}

.add-cart-button:active {
  transform: scale(0.9);
}

/* 数量控制区域 */
.quantity-controls {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 32rpx;
  padding: 4rpx;
  border: 2rpx solid #e0e0e0;
  min-width: 160rpx;
}

/* 数量按钮 */
.quantity-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border: 2rpx solid #e0e0e0;
  transition: all 0.2s ease;
}

.quantity-btn:active {
  transform: scale(0.9);
  background: #f0f0f0;
}

.quantity-btn.decrease {
  margin-right: 8rpx;
}

.quantity-btn.increase {
  margin-left: 8rpx;
  background: #00C853;
  border-color: #00C853;
}

.quantity-btn.increase:active {
  background: #00A047;
}

/* 数量显示区域 */
.quantity-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48rpx;
  height: 48rpx;
  background: #fff;
  border-radius: 24rpx;
  border: 2rpx solid #e0e0e0;
  transition: all 0.2s ease;
}

.quantity-display:active {
  background: #f8f8f8;
}

.quantity-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

/* 缺货按钮 */
.out-of-stock-button {
  width: 80rpx;
  height: 48rpx;
  background: #f5f5f5;
  border: 2rpx solid #e0e0e0;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  flex-shrink: 0;
}

.out-of-stock-button-text {
  font-size: 22rpx;
  color: #999;
  font-weight: 500;
}

.out-of-stock-button:active {
  background: #eeeeee;
}

/* 骨架屏动画 */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

/* 紧凑模式 */
.horizontal-product-card.compact {
  padding: 16rpx;
}

.horizontal-product-card.compact .product-image-wrapper {
  width: 140rpx;
  height: 140rpx;
  margin-right: 20rpx;
}

.horizontal-product-card.compact .product-title {
  font-size: 28rpx;
}

.horizontal-product-card.compact .price-display.product-price-horizontal .price-main {
  font-size: 32rpx;
}

/* ========== 深色模式适配 ========== */
@media (prefers-color-scheme: dark) {
  .horizontal-product-card {
    background: #2c2c2c;
    border-color: #444;
  }

  .horizontal-product-card.promotion {
    border-color: #ff6b35;
    box-shadow: 0 4rpx 20rpx rgba(255, 107, 53, 0.2);
  }

  .horizontal-product-card.new-product {
    border-color: #4CAF50;
    box-shadow: 0 4rpx 20rpx rgba(76, 175, 80, 0.2);
  }

  .product-title {
    color: #fff;
  }

  .horizontal-product-card.promotion .product-title {
    color: #ff6b35;
  }

  .horizontal-product-card.new-product .product-title {
    color: #4CAF50;
  }

  .product-description {
    color: #aaa;
  }

  /* 深色模式下的数量控制 */
  .quantity-controls {
    background: #3c3c3c;
    border-color: #555;
  }

  .quantity-btn {
    background: #444;
    border-color: #555;
  }

  .quantity-btn:active {
    background: #555;
  }

  .quantity-btn.increase {
    background: #00C853;
    border-color: #00C853;
  }

  .quantity-display {
    background: #444;
    border-color: #555;
  }

  .quantity-display:active {
    background: #555;
  }

  .quantity-text {
    color: #fff;
  }
}