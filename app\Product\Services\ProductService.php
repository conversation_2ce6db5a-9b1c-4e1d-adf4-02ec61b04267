<?php

namespace App\Product\Services;

use App\Product\Models\Product;
use App\Product\Models\ProductUnit;
use App\Product\Models\Category;
use App\Unit\Models\Unit;
use App\Unit\Services\UnitService;
use App\Unit\Services\ProductUnitService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ProductService
{
    /**
     * 单位服务
     * 
     * @var UnitService
     */
    protected $unitService;
    
    /**
     * 产品单位服务
     * 
     * @var ProductUnitService
     */
    protected $productUnitService;
    
    /**
     * 构造函数
     * 
     * @param UnitService $unitService
     * @param ProductUnitService $productUnitService
     */
    public function __construct(UnitService $unitService, ProductUnitService $productUnitService)
    {
        $this->unitService = $unitService;
        $this->productUnitService = $productUnitService;
    }
    
    /**
     * 创建商品
     *
     * @param array $data 商品数据
     * @return Product 创建的商品
     */
    public function createProduct(array $data)
    {
        try {
            DB::beginTransaction();
            
            // 自动生成商品编码
            if (!isset($data['code']) || empty($data['code'])) {
                $data['code'] = Product::generateProductCode();
            }
            
            // 创建商品记录
            $product = Product::create($data);
            
            // 如果提供了辅助单位信息，添加辅助单位
            if (isset($data['auxiliary_units']) && is_array($data['auxiliary_units'])) {
                foreach ($data['auxiliary_units'] as $unitData) {
                    if (!isset($unitData['unit_id']) || !isset($unitData['conversion_factor']) && !isset($unitData['conversion_rate'])) {
                        continue;
                    }
                    
                    // 兼容旧参数名
                    $conversionFactor = $unitData['conversion_factor'] ?? $unitData['conversion_rate'] ?? 1.0;
                    
                    // 准备角色数据
                    $roles = [];
                    $rolePriority = [];
                    
                    // 处理旧的单位角色字段
                    if (isset($unitData['is_sale_unit']) && $unitData['is_sale_unit']) {
                        $roles[] = 'sales';
                        $rolePriority['sales'] = 10;
                    }
                    if (isset($unitData['is_purchase_unit']) && $unitData['is_purchase_unit']) {
                        $roles[] = 'purchase';
                        $rolePriority['purchase'] = 10;
                    }
                    if (isset($unitData['is_inventory_unit']) && $unitData['is_inventory_unit']) {
                        $roles[] = 'inventory';
                        $rolePriority['inventory'] = 10;
                    }
                    
                    // 使用新的角色字段
                    if (isset($unitData['roles'])) {
                        $roles = array_merge($roles, $unitData['roles']);
                    }
                    if (isset($unitData['role_priority'])) {
                        $rolePriority = array_merge($rolePriority, $unitData['role_priority']);
                    }
                    
                    // 使用ProductUnitService添加单位
                    $unitObj = Unit::find($unitData['unit_id']);
                    if ($unitObj) {
                        $isDefault = $unitData['is_default'] ?? false;
                        $this->productUnitService->addProductUnit(
                            $product, 
                            $unitObj, 
                            $conversionFactor, 
                            $roles, 
                            $rolePriority, 
                            $isDefault
                        );
                    }
                }
            }
            
            DB::commit();
            return $product;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建商品失败', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }
    
    /**
     * 更新商品信息
     *
     * @param int $id 商品ID
     * @param array $data 商品数据
     * @return Product 更新后的商品
     */
    public function updateProduct($id, array $data)
    {
        try {
            DB::beginTransaction();
            
            $product = Product::findOrFail($id);
            $product->update($data);
            
            // 更新辅助单位
            if (isset($data['auxiliary_units']) && is_array($data['auxiliary_units'])) {
                // 收集单位数据
                $unitsData = [];
                foreach ($data['auxiliary_units'] as $unitData) {
                    if (!isset($unitData['unit_id'])) {
                        continue;
                    }
                    
                    // 兼容旧参数名
                    $conversionFactor = $unitData['conversion_factor'] ?? $unitData['conversion_rate'] ?? 1.0;
                    
                    // 准备角色数据
                    $roles = [];
                    $rolePriority = [];
                    
                    // 处理旧的单位角色字段
                    if (isset($unitData['is_sale_unit']) && $unitData['is_sale_unit']) {
                        $roles[] = 'sales';
                        $rolePriority['sales'] = 10;
                    }
                    if (isset($unitData['is_purchase_unit']) && $unitData['is_purchase_unit']) {
                        $roles[] = 'purchase';
                        $rolePriority['purchase'] = 10;
                    }
                    if (isset($unitData['is_inventory_unit']) && $unitData['is_inventory_unit']) {
                        $roles[] = 'inventory';
                        $rolePriority['inventory'] = 10;
                    }
                    
                    // 使用新的角色字段
                    if (isset($unitData['roles'])) {
                        $roles = array_merge($roles, $unitData['roles']);
                    }
                    if (isset($unitData['role_priority'])) {
                        $rolePriority = array_merge($rolePriority, $unitData['role_priority']);
                    }
                    
                    $unitsData[] = [
                        'unit_id' => $unitData['unit_id'],
                        'conversion_factor' => $conversionFactor,
                        'roles' => $roles,
                        'role_priority' => $rolePriority,
                        'is_default' => $unitData['is_default'] ?? false,
                        'is_active' => $unitData['is_active'] ?? true
                    ];
                }
                
                // 使用ProductUnitService设置单位
                $this->productUnitService->setProductUnits($product, $unitsData);
            }
            
            DB::commit();
            return $product;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('更新商品失败', [
                'product_id' => $id,
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }
    
    /**
     * 删除商品
     *
     * @param int $id 商品ID
     * @return bool 是否成功
     */
    public function deleteProduct($id)
    {
        try {
            $product = Product::findOrFail($id);
            
            // 检查商品是否有关联的库存记录或订单项
            $hasInventory = $product->inventories()->exists();
            
            if ($hasInventory) {
                throw new \Exception('商品存在库存记录，无法删除');
            }
            
            // 删除商品单位关联
            ProductUnit::where('product_id', $product->id)->delete();
            
            // 删除商品
            return $product->delete();
            
        } catch (\Exception $e) {
            Log::error('删除商品失败', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 更新商品状态
     *
     * @param int $id 商品ID
     * @param int $status 状态值
     * @return Product 更新后的商品
     */
    public function updateProductStatus($id, $status)
    {
        $product = Product::findOrFail($id);
        
        // 当尝试上架商品时，进行销售单位检查
        if ($status == 1) { // 1表示上架状态
            // 获取商品的销售单位
            $saleUnit = $this->hasSaleUnit($product);
            
            // 如果没有设置销售单位，则抛出异常
            if (!$saleUnit) {
                throw new \Exception('商品未设置销售单位，无法上架');
            }
        }
        
        $product->status = $status;
        $product->save();
        
        return $product;
    }
    
    /**
     * 检查商品是否设置了销售单位
     *
     * @param Product $product 商品实例
     * @return bool 是否设置了销售单位
     */
    protected function hasSaleUnit(Product $product)
    {
        // 尝试获取默认销售单位
        $saleUnit = $product->getSaleDefaultUnit();
        
        // 如果默认销售单位就是基本单位，并且没有显式指定其为销售单位，则认为未设置销售单位
        if ($saleUnit && $saleUnit->id == $product->base_unit_id) {
            // 检查基本单位是否被显式指定为销售单位
            $baseUnitHasSalesRole = false;
            
            // 检查商品单位关联中的基本单位角色
            $baseUnitRelation = ProductUnit::where('product_id', $product->id)
                                          ->where('unit_id', $product->base_unit_id)
                                          ->first();
                                          
            if ($baseUnitRelation) {
                $roles = json_decode($baseUnitRelation->roles, true) ?: [];
                $baseUnitHasSalesRole = in_array('sales', $roles);
            }
            
            // 如果基本单位没有被显式指定为销售单位，则认为未设置销售单位
            if (!$baseUnitHasSalesRole) {
                return false;
            }
        }
        
        // 查找是否有明确设置为销售单位的辅助单位
        $hasSaleUnit = ProductUnit::where('product_id', $product->id)
                                 ->whereJsonContains('roles', 'sales')
                                 ->exists();
                                 
        return $hasSaleUnit || ($saleUnit && $saleUnit->id != $product->base_unit_id);
    }
    
    /**
     * 查询商品列表
     *
     * @param array $filters 过滤条件
     * @param int $perPage 每页数量
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getProducts($filters = [], $perPage = 10)
    {
        $query = Product::query();

        // 设置当前页码（如果在过滤器中提供）
        $currentPage = $filters['page'] ?? 1;
        if ($currentPage > 0) {
            \Illuminate\Pagination\Paginator::currentPageResolver(function () use ($currentPage) {
                return $currentPage;
            });
        }

        Log::info('ProductService分页参数', [
            'page' => $currentPage,
            'perPage' => $perPage,
            'filters' => $filters
        ]);

        // 调试：检查基础查询
        $totalCount = Product::count();
        Log::info('数据库商品总数检查', [
            'total_products_in_db' => $totalCount
        ]);
        
        // 添加查询条件
        if (!empty($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }
        
        if (!empty($filters['keyword'])) {
            $keyword = $filters['keyword'];
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('code', 'like', "%{$keyword}%")
                  ->orWhere('description', 'like', "%{$keyword}%");
            });
        }
        
        if (isset($filters['status']) && $filters['status'] !== '' && $filters['status'] !== null) {
            $query->where('status', $filters['status']);
        }
        
        // 排序
        $sortField = $filters['sort_field'] ?? 'id';
        $sortOrder = $filters['sort_order'] ?? 'desc';
        
        // 优先按排序权重排序，然后再按其他字段排序
        if ($sortField !== 'sort') {
            // 先按排序字段排序
            $query->orderBy('sort', 'desc');
            // 然后按请求的字段排序
            $query->orderBy($sortField, $sortOrder);
        } else {
            // 如果直接按排序字段排序，就只用这一个排序
            $query->orderBy('sort', $sortOrder);
        }
        
        try {
            // 调试：检查应用过滤条件后的查询结果数量
            $filteredCount = $query->count();
            Log::info('应用过滤条件后的商品数量', [
                'filtered_count' => $filteredCount,
                'applied_filters' => array_filter($filters, function($value) {
                    return $value !== null && $value !== '';
                })
            ]);

            // 检查是否请求包含仓库信息
            $relations = ['category', 'baseUnit', 'units', 'images'];

            // 如果请求包含仓库信息或明确请求了库存信息，则添加仓库关联
            $includeWarehouses = !empty($filters['include_warehouses']) ||
                                !empty($filters['with_inventory']) ||
                                $sortField === 'stock';

            if ($includeWarehouses) {
                $relations[] = 'warehouses'; // 添加仓库关联
                $relations[] = 'inventories'; // 添加库存关联
            }

            // 尝试加载关联项目
            $result = $query->with($relations)->paginate($perPage);

            Log::info('ProductService分页查询结果', [
                'current_page' => $result->currentPage(),
                'per_page' => $result->perPage(),
                'total' => $result->total(),
                'last_page' => $result->lastPage(),
                'count' => $result->count()
            ]);

            return $result;
        } catch (\Exception $e) {
            // 如果加载关联失败，记录错误并仅加载基本关联
            Log::error('加载商品关联项目失败，使用简化查询', [
                'error' => $e->getMessage()
            ]);
            return $query->with(['category', 'images'])->paginate($perPage);
        }
    }
} 