<?php

namespace App\Region\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use App\Product\Models\Product;

class Region extends Model
{
    use HasFactory;
    
    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'code',
        'parent_id',
        'level',
        'latitude',
        'longitude',
        'boundary',
        'status',
        'sort',
        'full_name',
        'metadata',
        'description'
    ];
    
    /**
     * 类型转换
     *
     * @var array
     */
    protected $casts = [
        'status' => 'boolean',
        'sort' => 'integer',
        'level' => 'integer',
        'parent_id' => 'integer',
        'latitude' => 'float',
        'longitude' => 'float',
        'boundary' => 'array',
        'metadata' => 'array'
    ];
    
    /**
     * 关联子区域
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function children()
    {
        return $this->hasMany(Region::class, 'parent_id');
    }
    
    /**
     * 关联父区域
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function parent()
    {
        return $this->belongsTo(Region::class, 'parent_id');
    }
    
    /**
     * 关联区域价格
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function prices()
    {
        return $this->hasMany(RegionPrice::class);
    }
    
    /**
     * 获取树形结构
     *
     * @param int $parentId 父级ID
     * @param array $conditions 条件
     * @return \Illuminate\Support\Collection
     */
    public static function getTree($parentId = 0, array $conditions = [])
    {
        $query = self::query();
        
        // 添加查询条件
        foreach ($conditions as $field => $value) {
            $query->where($field, $value);
        }
        
        // 获取父级下的所有区域
        $regions = $query->where('parent_id', $parentId)
            ->orderBy('sort', 'asc')
            ->get();
        
        Log::info('Region getTree 查询结果', [
            'parent_id' => $parentId,
            'conditions' => $conditions,
            'count' => $regions->count(),
            'first_few' => $regions->take(3)->toArray()
        ]);
        
        // 递归获取子区域
        $regions->each(function ($region) use ($conditions) {
            $region->children = self::getTree($region->id, $conditions);
        });
        
        return $regions;
    }
    
    /**
     * 获取完整区域路径
     *
     * @param string $separator 分隔符
     * @return string
     */
    public function getFullPath($separator = ' > ')
    {
        $path = [$this->name];
        $parent = $this->parent;
        
        while ($parent) {
            array_unshift($path, $parent->name);
            $parent = $parent->parent;
        }
        
        return implode($separator, $path);
    }
    
    /**
     * 获取祖先链
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAncestors()
    {
        $ancestors = collect();
        $parent = $this->parent;
        
        while ($parent) {
            $ancestors->push($parent);
            $parent = $parent->parent;
        }
        
        return $ancestors->reverse();
    }
    
    /**
     * 获取所有后代
     *
     * @param bool $activeOnly 是否只获取激活的区域
     * @return \Illuminate\Support\Collection
     */
    public function getAllDescendants($activeOnly = false)
    {
        $descendants = collect();
        
        $query = self::query();
        if ($activeOnly) {
            $query->where('status', true);
        }
        
        // 先获取直接子区域
        $children = $query->where('parent_id', $this->id)->get();
        
        $descendants = $descendants->merge($children);
        
        // 递归获取每个子区域的后代
        foreach ($children as $child) {
            $descendants = $descendants->merge($child->getAllDescendants($activeOnly));
        }
        
        return $descendants;
    }
    
    /**
     * 获取面包屑
     *
     * @return \Illuminate\Support\Collection
     */
    public function getBreadcrumb()
    {
        $breadcrumb = collect([$this]);
        $parent = $this->parent;
        
        while ($parent) {
            $breadcrumb->prepend($parent);
            $parent = $parent->parent;
        }
        
        return $breadcrumb;
    }
    
    /**
     * 检查是否是指定区域的后代
     *
     * @param int $ancestorId 祖先ID
     * @return bool
     */
    public function isDescendantOf($ancestorId)
    {
        if ($this->parent_id == $ancestorId) {
            return true;
        }
        
        $parent = $this->parent;
        while ($parent) {
            if ($parent->id == $ancestorId) {
                return true;
            }
            $parent = $parent->parent;
        }
        
        return false;
    }
    
    /**
     * 检查是否有子区域
     *
     * @return bool
     */
    public function hasChildren()
    {
        return $this->children()->count() > 0;
    }
    
    /**
     * 检查区域是否可用
     *
     * @return bool
     */
    public function isAvailable()
    {
        return $this->status === true;
    }
} 