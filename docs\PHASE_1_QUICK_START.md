# 第一阶段：基础设施搭建 - 快速启动指南

## 🚀 立即开始

### 步骤1：验证环境配置

1. **打开微信开发者工具**
2. **导入项目** `shop530`
3. **检查基础配置**

在开发者工具控制台执行：
```javascript
// 验证配置
import { REQUEST_CONFIG } from './utils/request-config.js';
console.log('当前环境:', REQUEST_CONFIG.CURRENT_ENV);
console.log('API基础URL:', REQUEST_CONFIG.BASE_URL);
```

### 步骤2：访问API测试页面

1. **在模拟器中访问测试页面**
   - 方法1：在地址栏输入 `/pages/test/test`
   - 方法2：在任意页面控制台执行：
     ```javascript
     wx.navigateTo({ url: '/pages/test/test' });
     ```

2. **执行快速测试**
   - 点击"快速测试"按钮
   - 观察测试结果
   - 检查成功率

### 步骤3：分析测试结果

#### ✅ 预期成功的API：
- 轮播图API
- 商品分类API  
- 商品列表API

#### ⚠️ 可能需要调整的API：
- 热门商品API（路径可能需要调整）
- 系统配置API（可能需要后端添加）
- 地区数据API（需要确认路径）

### 步骤4：问题排查

#### 如果所有API都失败：
1. **检查网络连接**
2. **确认后端服务是否启动**
3. **检查API基础URL配置**

```javascript
// 在控制台检查网络
wx.request({
  url: 'https://your-api-domain.com/api/test',
  success: (res) => console.log('网络正常:', res),
  fail: (err) => console.error('网络异常:', err)
});
```

#### 如果部分API失败：
1. **查看具体错误信息**
2. **检查API路径是否正确**
3. **确认后端对应接口是否存在**

### 步骤5：完整测试

当快速测试通过后，执行完整测试：
1. 点击"完整测试"按钮
2. 等待所有API测试完成
3. 查看详细测试报告

## 📋 检查清单

### 基础配置检查：
- [ ] 项目能正常启动
- [ ] 配置文件加载正常
- [ ] 网络请求工具正常

### API连通性检查：
- [ ] 轮播图API ✅
- [ ] 商品分类API ✅  
- [ ] 商品列表API ✅
- [ ] 商品详情API ✅
- [ ] 搜索API ✅
- [ ] 地区数据API ⚠️
- [ ] 系统配置API ⚠️

### 错误处理检查：
- [ ] 网络错误处理正常
- [ ] 404错误处理正常
- [ ] 参数错误处理正常
- [ ] 超时处理正常

## 🔧 常见问题解决

### 问题1：API基础URL配置错误
**症状**：所有API请求都失败
**解决**：
```javascript
// 在 utils/request-config.js 中修改
const ENVIRONMENTS = {
  development: {
    BASE_URL: 'http://your-local-api.com', // 修改为正确的本地API地址
    // ...
  }
};
```

### 问题2：跨域问题
**症状**：浏览器控制台显示CORS错误
**解决**：
1. 确认后端已配置CORS
2. 检查请求头设置
3. 确认域名白名单配置

### 问题3：认证相关API失败
**症状**：需要认证的API返回401
**解决**：
```javascript
// 这是正常的，公共API不需要认证
// 认证相关功能将在第三阶段实现
console.log('认证API将在第三阶段实现');
```

### 问题4：数据格式不匹配
**症状**：API返回数据但格式异常
**解决**：
1. 检查后端返回的数据结构
2. 对比前端期望的数据格式
3. 调整API服务类中的数据处理逻辑

## 📊 成功标准

### 第一阶段完成标准：
- [ ] **快速测试成功率 ≥ 80%**
- [ ] **核心公共API全部可用**
- [ ] **错误处理机制正常**
- [ ] **配置管理正常**

### 准备进入第二阶段的条件：
- [ ] 轮播图API正常
- [ ] 商品分类API正常  
- [ ] 商品列表API正常
- [ ] 商品详情API正常
- [ ] 基础错误处理完善

## 🎯 下一步行动

### 当第一阶段完成后：
1. **记录测试结果**
2. **修复发现的问题**
3. **准备第二阶段：公共页面对接**
4. **开始首页数据对接**

### 立即行动项：
1. ✅ 打开微信开发者工具
2. ✅ 导入shop530项目
3. ✅ 访问 `/pages/test/test` 页面
4. ✅ 执行快速测试
5. ✅ 分析结果并记录问题

## 💡 提示

- **保持耐心**：第一次对接可能会遇到各种问题，这是正常的
- **详细记录**：记录每个问题和解决方案，便于后续参考
- **逐步推进**：不要急于求成，确保每个阶段都稳固
- **及时沟通**：遇到后端相关问题及时与后端开发者沟通

---

**准备好了吗？让我们开始第一阶段的API对接之旅！** 🚀 