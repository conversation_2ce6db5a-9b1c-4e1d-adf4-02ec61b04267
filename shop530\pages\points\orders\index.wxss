/* pages/points/orders/index.wxss - 积分订单列表页样式 */

/* 主容器 */
.orders-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* ===== 状态标签栏 ===== */
.status-tabs {
  background: #fff;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #E0E0E0;
}

.tabs-scroll {
  white-space: nowrap;
}

.tab-item {
  display: inline-block;
  padding: 30rpx 20rpx;
  margin-right: 40rpx;
  position: relative;
  vertical-align: top;
}

.tab-item.active {
  color: #4CAF50;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #4CAF50;
  border-radius: 2rpx;
}

.tab-name {
  font-size: 28rpx;
  color: #333;
}

.tab-item.active .tab-name {
  color: #4CAF50;
  font-weight: 600;
}

.tab-count {
  background: #FF5722;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  margin-left: 8rpx;
  min-width: 32rpx;
  text-align: center;
}

/* ===== 订单列表 ===== */
.orders-list {
  padding: 20rpx;
}

.order-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.order-no {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 26rpx;
  font-weight: 600;
}

/* 订单商品 */
.order-products {
  padding: 0 30rpx;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F8F8F8;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-spec {
  font-size: 22rpx;
  color: #999;
}

.product-price {
  display: flex;
  align-items: baseline;
  gap: 12rpx;
}

.points-price {
  font-size: 24rpx;
  color: #FF6B35;
  font-weight: 600;
}

.cash-price {
  font-size: 22rpx;
  color: #666;
}

.product-quantity {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #666;
}

/* 订单总计 */
.order-total {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #F0F0F0;
  background: #FAFAFA;
}

.total-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-label {
  font-size: 24rpx;
  color: #666;
}

.total-price {
  display: flex;
  align-items: baseline;
  gap: 12rpx;
}

.total-points {
  font-size: 28rpx;
  color: #FF6B35;
  font-weight: 600;
}

.total-cash {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 订单操作 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #F0F0F0;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  border: 1rpx solid #E0E0E0;
  background: #fff;
  color: #666;
  margin: 0;
}

.action-btn::after {
  border: none;
}

.action-btn.primary {
  background: #4CAF50;
  color: #fff;
  border-color: #4CAF50;
}

.action-btn.danger {
  background: #fff;
  color: #F44336;
  border-color: #F44336;
}

.action-btn.secondary {
  background: #fff;
  color: #2196F3;
  border-color: #2196F3;
}

/* ===== 空状态 ===== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  background: #4CAF50;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
}

.empty-btn::after {
  border: none;
}

/* ===== 加载状态 ===== */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

/* ===== 没有更多 ===== */
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
} 