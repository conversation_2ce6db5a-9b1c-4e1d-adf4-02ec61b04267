<?php

namespace Database\Seeders;

use App\Crm\Models\CrmAgent;
use App\Crm\Models\ClientFollowUp;
use App\Models\User;
use App\Employee\Models\Employee;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class CrmSeeder extends Seeder
{
    /**
     * 生成CRM系统的测试数据
     */
    public function run(): void
    {
        // 临时禁用外键约束
        DB::statement('SET FOREIGN_KEY_CHECKS=0');
        
        // 清空相关表
        DB::table('client_follow_ups')->truncate();
        DB::table('crm_agents')->truncate();
        // 更新users表，清空crm_agent_id
        DB::table('users')->update(['crm_agent_id' => null]);
        
        // 创建CRM专员
        $this->createCrmAgents();
        
        // 分配客户给CRM专员
        $this->assignClientsToAgents();
        
        // 创建客户跟进记录
        $this->createFollowUps();
        
        // 更新CRM专员的客户计数
        $this->updateAgentClientCounts();
        
        // 重新启用外键约束
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
        
        $this->command->info('CRM系统测试数据生成完成！');
    }
    
    /**
     * 创建CRM专员
     */
    private function createCrmAgents(): void
    {
        $agents = [
            [
                'name' => '李明',
                'username' => 'liming_crm',
                'password' => '123456',
                'phone' => '13800138001',
                'position' => 'CRM专员',
                'role' => 'crm_agent',
                'agent_info' => [
                    'service_area' => '华南区域',
                    'max_clients' => 50,
                    'performance_rating' => 4.8,
                    'status' => 'available',
                    'specialty' => '大客户关系维护',
                    'monthly_target' => 50000
                ]
            ],
            [
                'name' => '王芳',
                'username' => 'wangfang_crm',
                'password' => '123456',
                'phone' => '13800138002',
                'position' => 'CRM专员',
                'role' => 'crm_agent',
                'agent_info' => [
                    'service_area' => '华东区域',
                    'max_clients' => 30,
                    'performance_rating' => 4.5,
                    'status' => 'available',
                    'specialty' => '新客户开发',
                    'monthly_target' => 30000
                ]
            ],
            [
                'name' => '张强',
                'username' => 'zhangqiang_crm',
                'password' => '123456',
                'phone' => '13800138003',
                'position' => 'CRM专员',
                'role' => 'crm_agent',
                'agent_info' => [
                    'service_area' => '华北区域',
                    'max_clients' => 40,
                    'performance_rating' => 4.2,
                    'status' => 'available',
                    'specialty' => '客户挽留',
                    'monthly_target' => 40000
                ]
            ]
        ];
        
        foreach ($agents as $agentData) {
            $agent_info = $agentData['agent_info'];
            unset($agentData['agent_info']);
            
            // 检查是否已存在相同用户名的员工
            $existingEmployee = Employee::where('username', $agentData['username'])->first();
            
            if ($existingEmployee) {
                $this->command->info("员工 {$agentData['username']} 已存在，使用现有记录");
                
                // 检查该员工是否已有CRM专员信息
                $existingAgent = CrmAgent::where('employee_id', $existingEmployee->id)->first();
                
                if (!$existingAgent) {
                    // 为已存在的员工创建CRM专员信息
                    CrmAgent::create([
                        'employee_id' => $existingEmployee->id,
                        'service_area' => $agent_info['service_area'],
                        'max_clients' => $agent_info['max_clients'],
                        'performance_rating' => $agent_info['performance_rating'],
                        'status' => $agent_info['status'],
                        'specialty' => $agent_info['specialty'],
                        'monthly_target' => $agent_info['monthly_target'],
                        'clients_count' => 0
                    ]);
                    $this->command->info("为员工 {$agentData['username']} 创建CRM专员信息");
                } else {
                    $this->command->info("员工 {$agentData['username']} 已有CRM专员信息，跳过");
                }
            } else {
                // 创建新员工记录
                $employee = Employee::create([
                    'name' => $agentData['name'],
                    'username' => $agentData['username'],
                    'password' => Hash::make($agentData['password']),
                    'phone' => $agentData['phone'],
                    'position' => $agentData['position'],
                    'role' => $agentData['role'],
                ]);
                
                // 创建CRM专员信息
                CrmAgent::create([
                    'employee_id' => $employee->id,
                    'service_area' => $agent_info['service_area'],
                    'max_clients' => $agent_info['max_clients'],
                    'performance_rating' => $agent_info['performance_rating'],
                    'status' => $agent_info['status'],
                    'specialty' => $agent_info['specialty'],
                    'monthly_target' => $agent_info['monthly_target'],
                    'clients_count' => 0
                ]);
                $this->command->info("创建员工 {$agentData['username']} 及CRM专员信息");
            }
        }
        
        $this->command->info('CRM专员创建/更新完成');
    }
    
    /**
     * 分配客户给CRM专员
     */
    private function assignClientsToAgents(): void
    {
        // 获取所有CRM专员
        $agents = CrmAgent::with('employee')->get();
        if ($agents->isEmpty()) {
            $this->command->warn('没有找到CRM专员，跳过分配客户');
            return;
        }
        
        // 获取客户角色的用户
        $customers = User::whereNull('crm_agent_id')->take(20)->get();
        if ($customers->isEmpty()) {
            // 如果没有客户用户，先创建一些
            $this->createCustomers();
            $customers = User::whereNull('crm_agent_id')->take(20)->get();
        }
        
        $count = 0;
        
        foreach ($customers as $customer) {
            // 随机选择一个CRM专员
            $agent = $agents->random();
            
            // 更新客户的CRM专员ID
            $customer->crm_agent_id = $agent->employee_id;
            $customer->save();
            
            $count++;
        }
        
        $this->command->info("已将{$count}个客户分配给CRM专员");
    }
    
    /**
     * 创建客户跟进记录
     */
    private function createFollowUps(): void
    {
        // 获取所有已分配CRM专员的客户
        $customers = User::whereNotNull('crm_agent_id')->get();
            
        if ($customers->isEmpty()) {
            $this->command->warn('没有找到已分配CRM专员的客户，跳过创建跟进记录');
            return;
        }
        
        $followUpMethods = ['phone', 'sms', 'email', 'visit', 'wechat', 'other'];
        $followUpResults = ['successful', 'follow_up', 'no_answer', 'rejected', 'other'];
        $followUpContents = [
            '客户对我们的产品表示满意，计划增加采购量',
            '客户反馈使用过程中存在一些问题，已安排技术支持跟进',
            '讨论了产品新功能需求，客户非常期待下一版本更新',
            '解决了客户在使用过程中遇到的问题，提供了解决方案',
            '与客户讨论了合同续签事宜，客户表示会认真考虑',
            '推荐了新产品给客户，安排了产品演示',
            '未能联系到客户，将在下周再次尝试',
            '客户对价格有异议，需要进一步沟通',
            '客户已确认下个月的订单计划',
            '向客户介绍了最新的优惠活动，客户表示有兴趣'
        ];
        
        $now = Carbon::now();
        $followUps = [];
        $count = 0;
        
        foreach ($customers as $customer) {
            // 为每个客户创建1-5条跟进记录
            $recordCount = rand(1, 5);
            
            for ($i = 0; $i < $recordCount; $i++) {
                $followUpDate = Carbon::now()->subDays(rand(1, 60));
                $nextFollowUp = rand(0, 1) ? $followUpDate->copy()->addDays(rand(5, 30)) : null;
                
                $followUps[] = [
                    'user_id' => $customer->id,
                    'employee_id' => $customer->crm_agent_id,
                    'follow_up_date' => $followUpDate->format('Y-m-d'),
                    'contact_method' => $followUpMethods[array_rand($followUpMethods)],
                    'notes' => $followUpContents[array_rand($followUpContents)],
                    'result' => $followUpResults[array_rand($followUpResults)],
                    'next_follow_up' => $nextFollowUp ? $nextFollowUp->format('Y-m-d') : null,
                    'created_at' => $now,
                    'updated_at' => $now
                ];
                
                $count++;
            }
        }
        
        // 批量插入
        if (!empty($followUps)) {
            DB::table('client_follow_ups')->insert($followUps);
        }
        
        $this->command->info("已创建{$count}条客户跟进记录");
    }
    
    /**
     * 创建一些客户用户
     */
    private function createCustomers(): void
    {
        $customers = [];
        $now = Carbon::now();
        
        for ($i = 1; $i <= 20; $i++) {
            $customers[] = [
                'name' => '测试客户' . $i,
                'password' => bcrypt('123456'),
                'phone' => '139' . str_pad($i, 8, '0', STR_PAD_LEFT),
                'role' => 'customer',
                'joined_at' => $now->subDays(rand(1, 90)),
                'created_at' => $now,
                'updated_at' => $now
            ];
        }
        
        // 批量插入
        if (!empty($customers)) {
            DB::table('users')->insert($customers);
        }
        
        $this->command->info("已创建20个测试客户");
    }
    
    /**
     * 更新CRM专员的客户计数
     */
    private function updateAgentClientCounts(): void
    {
        $agents = CrmAgent::all();
        
        foreach ($agents as $agent) {
            $count = User::where('crm_agent_id', $agent->employee_id)->count();
                
            $agent->clients_count = $count;
            $agent->save();
        }
        
        $this->command->info("已更新所有CRM专员的客户计数");
    }
} 