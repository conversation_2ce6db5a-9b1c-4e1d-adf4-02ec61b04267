<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 第一步：添加临时字段来备份数据
        Schema::table('crm_agents', function (Blueprint $table) {
            $table->text('service_area_backup')->nullable();
        });
        
        // 第二步：备份现有数据
        $agents = DB::table('crm_agents')->whereNotNull('service_area')->get();
        foreach ($agents as $agent) {
            DB::table('crm_agents')
                ->where('id', $agent->id)
                ->update(['service_area_backup' => $agent->service_area]);
        }
        
        // 第三步：删除原字段
        Schema::table('crm_agents', function (Blueprint $table) {
            $table->dropColumn('service_area');
        });
        
        // 第四步：创建新的JSON字段
        Schema::table('crm_agents', function (Blueprint $table) {
            $table->json('service_area')->nullable()->comment('服务区域（支持多个）');
        });
        
        // 第五步：恢复数据并转换为数组格式
        foreach ($agents as $agent) {
            if (!empty($agent->service_area)) {
                // 将字符串转换为数组
                $serviceAreas = [$agent->service_area];
                
                DB::table('crm_agents')
                    ->where('id', $agent->id)
                    ->update(['service_area' => json_encode($serviceAreas)]);
            }
        }
        
        // 第六步：删除临时备份字段
        Schema::table('crm_agents', function (Blueprint $table) {
            $table->dropColumn('service_area_backup');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 第一步：添加临时字段来备份JSON数据
        Schema::table('crm_agents', function (Blueprint $table) {
            $table->text('service_area_backup')->nullable();
        });
        
        // 第二步：备份现有JSON数据
        $agents = DB::table('crm_agents')->whereNotNull('service_area')->get();
        foreach ($agents as $agent) {
            if (!empty($agent->service_area)) {
                $serviceAreas = json_decode($agent->service_area, true);
                $firstArea = is_array($serviceAreas) && !empty($serviceAreas) ? $serviceAreas[0] : '';
                
                DB::table('crm_agents')
                    ->where('id', $agent->id)
                    ->update(['service_area_backup' => $firstArea]);
            }
        }
        
        // 第三步：删除JSON字段
        Schema::table('crm_agents', function (Blueprint $table) {
            $table->dropColumn('service_area');
        });
        
        // 第四步：创建string字段
        Schema::table('crm_agents', function (Blueprint $table) {
            $table->string('service_area')->nullable()->comment('服务区域');
        });
        
        // 第五步：恢复数据
        foreach ($agents as $agent) {
            $backupData = DB::table('crm_agents')->where('id', $agent->id)->value('service_area_backup');
            if (!empty($backupData)) {
                DB::table('crm_agents')
                    ->where('id', $agent->id)
                    ->update(['service_area' => $backupData]);
            }
        }
        
        // 第六步：删除临时备份字段
        Schema::table('crm_agents', function (Blueprint $table) {
            $table->dropColumn('service_area_backup');
        });
    }
};
