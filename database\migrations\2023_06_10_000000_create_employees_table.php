<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->id();                                // 自增ID主键
            $table->string('name');                     // 员工姓名，用于显示
            $table->string('username')->unique();       // 登录用户名，唯一，用于员工登录系统
            $table->string('password');                 // 登录密码，加密存储
            $table->string('phone')->nullable();        // 员工电话号码，可为空
            $table->string('position');                 // 员工职位，如"经理"、"主管"、"员工"等
            $table->string('role')->default('staff');   // 系统角色权限: admin(管理员), manager(经理), staff(普通员工)
            $table->rememberToken();                    // 记住我令牌，用于"记住登录状态"功能
            $table->timestamps();                       // created_at和updated_at时间戳，记录创建和更新时间
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
}; 