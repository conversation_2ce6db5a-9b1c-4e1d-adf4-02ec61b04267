<?php

namespace App\Order\Services;

use App\Order\Models\Order;
use App\Order\Models\OrderMerge;
use App\Order\Models\OrderItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class OrderMergeDataService
{
    /**
     * 执行订单合并的数据库操作
     */
    public function executeMerge(array $orderIds, array $mergedData, array $options = []): Order
    {
        return DB::transaction(function () use ($orderIds, $mergedData, $options) {
            // 1. 获取原订单并锁定
            $originalOrders = $this->lockOriginalOrders($orderIds);
            
            // 2. 创建合并记录
            $mergeRecord = $this->createMergeRecord($originalOrders, $mergedData, $options);
            
            // 3. 创建合并后的新订单
            $mergedOrder = $this->createMergedOrder($originalOrders, $mergedData, $mergeRecord);
            
            // 4. 处理原订单状态
            $this->updateOriginalOrders($originalOrders, $mergedOrder, $mergeRecord);
            
            // 5. 处理库存调整
            $this->adjustInventory($originalOrders, $mergedOrder);
            
            // 6. 记录操作日志
            $this->logMergeOperation($mergeRecord, $originalOrders, $mergedOrder);
            
            return $mergedOrder->fresh(['items', 'user']);
        });
    }
    
    /**
     * 锁定原订单（防止并发修改）
     */
    private function lockOriginalOrders(array $orderIds): \Illuminate\Database\Eloquent\Collection
    {
        // 首先获取所有订单，检查状态
        $allOrders = Order::whereIn('id', $orderIds)
            ->with(['items.product', 'user'])
            ->get();
            
        // 检查订单是否存在
        if ($allOrders->count() !== count($orderIds)) {
            $existingIds = $allOrders->pluck('id')->toArray();
            $missingIds = array_diff($orderIds, $existingIds);
            throw new \Exception('订单不存在：' . implode(', ', $missingIds));
        }
        
        // 检查订单状态，提供详细的错误信息
        $invalidOrders = [];
        foreach ($allOrders as $order) {
            if ($order->status !== 'pending') {
                $statusName = $this->getStatusName($order->status);
                $invalidOrders[] = "订单 {$order->order_no} 状态为 {$statusName}";
            }
            if ($order->is_merged_from) {
                $invalidOrders[] = "订单 {$order->order_no} 已被合并过";
            }
        }
        
        if (!empty($invalidOrders)) {
            throw new \Exception('以下订单无法合并：' . implode('；', $invalidOrders) . '。只能合并待付款状态的订单。');
        }
        
        // 重新获取并锁定有效订单
        $orders = Order::whereIn('id', $orderIds)
            ->where('status', 'pending')
            ->where('is_merged_from', false)
            ->lockForUpdate()
            ->with(['items.product', 'user'])
            ->get();
        
        // 验证合并规则
        $this->validateMergeRules($orders);
        
        return $orders;
    }
    
    /**
     * 验证合并规则（简化版）
     */
    private function validateMergeRules(\Illuminate\Database\Eloquent\Collection $orders): void
    {
        // 1. 必须是同一用户的订单
        $userIds = $orders->pluck('user_id')->unique();
        if ($userIds->count() > 1) {
            throw new \Exception('只能合并同一用户的订单');
        }
        
        // 2. 必须是相同支付方式（简化规则）
        $paymentMethods = $orders->pluck('payment_method')->unique();
        if ($paymentMethods->count() > 1) {
            $methodNames = $paymentMethods->map(function($method) {
                return $this->getPaymentMethodName($method);
            })->implode('、');
            throw new \Exception("只能合并相同支付方式的订单，当前包含：{$methodNames}");
        }
        
        // 3. 货到付款订单的特殊验证
        $paymentMethod = $paymentMethods->first();
        if ($paymentMethod === 'cod') {
            $this->validateCodOrders($orders);
        }
        
        // 4. 检查库存充足性
        $this->validateMergedStock($orders);
    }
    
    /**
     * 货到付款订单特殊验证
     */
    private function validateCodOrders(\Illuminate\Database\Eloquent\Collection $orders): void
    {
        foreach ($orders as $order) {
            if (!$order->isCashOnDelivery()) {
                throw new \Exception("订单 {$order->order_no} 不是货到付款订单");
            }
            
            if ($order->cod_status !== 'unpaid') {
                throw new \Exception("货到付款订单 {$order->order_no} 状态为 {$order->cod_status}，只能合并未支付的订单");
            }
        }
        
        // 检查配送区域一致性（货到付款需要同一配送员）
        $regions = $orders->pluck('region_id')->unique();
        if ($regions->count() > 1) {
            throw new \Exception('货到付款订单只能合并同一配送区域的订单');
        }
        
        // 检查代客下单的特殊情况
        $this->validateProxyOrderMerge($orders);
    }
    
    /**
     * 代客下单订单合并验证（简化版 - 以用户为基准）
     */
    private function validateProxyOrderMerge(\Illuminate\Database\Eloquent\Collection $orders): void
    {
        // 只需要记录合并信息，不需要限制合并
        // 同一客户的订单，无论是自下单还是代下单，都可以合并
        
        $proxyOrders = $orders->filter(fn($order) => $order->isProxyOrder());
        
        if ($proxyOrders->count() > 0) {
            Log::info('包含代客下单的订单合并', [
                'customer_id' => $orders->first()->user_id,
                'total_orders' => $orders->count(),
                'proxy_orders' => $proxyOrders->count(),
                'user_orders' => $orders->count() - $proxyOrders->count(),
                'proxy_employees' => $proxyOrders->pluck('created_by_id')->unique()->toArray(),
            ]);
        }
    }
    
    /**
     * 获取支付方式名称
     */
    private function getPaymentMethodName(string $method): string
    {
        $names = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'bank' => '银行转账',
            'cash' => '现金支付',
            'cod' => '货到付款',
        ];
        
        return $names[$method] ?? $method;
    }
    
    /**
     * 获取订单状态名称
     */
    private function getStatusName(string $status): string
    {
        $names = [
            'pending' => '待付款',
            'paid' => '已付款',
            'shipped' => '配送中',
            'delivered' => '交易完成',
            'cancelled' => '已取消',
            'closed' => '交易关闭',
            'review' => '待评价',
        ];
        
        return $names[$status] ?? $status;
    }
    
    /**
     * 验证合并后的库存充足性
     * 注意：由于订单创建时已经锁定了库存，合并时不需要额外的库存检查
     * 只需要确保合并后的数量不超过原订单的总数量即可
     */
    private function validateMergedStock(\Illuminate\Database\Eloquent\Collection $orders): void
    {
        // 收集所有订单的商品项，按商品+单位分组合并数量
        $originalItems = [];
        
        foreach ($orders as $order) {
            foreach ($order->items as $item) {
                $key = $item->product_id . '_' . ($item->unit_id ?? 'default');
                
                if (!isset($originalItems[$key])) {
                    $originalItems[$key] = [
                        'product_id' => $item->product_id,
                        'product_name' => $item->product_name,
                        'unit_id' => $item->unit_id,
                        'quantity' => 0,
                    ];
                }
                
                $originalItems[$key]['quantity'] += $item->quantity;
            }
        }
        
        // 记录验证信息（用于调试）
        Log::info('订单合并库存验证', [
            'orders_count' => $orders->count(),
            'original_items' => $originalItems,
            'validation_note' => '由于订单创建时已锁定库存，合并时无需额外库存检查'
        ]);
        
        // 合并时不需要额外的库存检查，因为：
        // 1. 原订单创建时已经扣减了库存
        // 2. 合并只是重新组织这些已锁定的库存
        // 3. 合并后的总数量不会超过原订单的总数量
    }
    
    /**
     * 创建合并记录
     */
    private function createMergeRecord($originalOrders, array $mergedData, array $options): OrderMerge
    {
        // 收集原订单优惠信息
        $originalDiscountInfo = $originalOrders->map(function ($order) {
            return [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'total' => $order->total,
                'subtotal' => $order->subtotal,
                'discount' => $order->discount,
                'payment_discount' => $order->payment_discount,
                'pricing_info' => $order->pricing_info,
                'payment_method' => $order->payment_method,
            ];
        })->toArray();
        
        // 计算节省金额
        $originalTotalAmount = $originalOrders->sum('total');
        $totalSavings = $originalTotalAmount - $mergedData['final_total'];
        
        return OrderMerge::create([
            'user_id' => $originalOrders->first()->user_id,
            'original_order_ids' => $originalOrders->pluck('id')->toArray(),
            'merge_strategy' => $options['strategy'] ?? 'intelligent',
            'merge_date' => Carbon::today(),
            'original_total_amount' => $originalTotalAmount,
            'merged_total_amount' => $mergedData['final_total'],
            'total_savings' => $totalSavings,
            'original_discount_info' => $originalDiscountInfo,
            'merged_discount_info' => $mergedData['discount_info'],
            'discount_comparison' => $this->buildDiscountComparison($originalDiscountInfo, $mergedData),
            'merged_by' => auth()->id(),
            'merge_type' => $options['type'] ?? 'manual',
            'merge_reason' => $options['reason'] ?? '当日订单合并',
        ]);
    }
    
    /**
     * 创建合并后的新订单
     */
    private function createMergedOrder($originalOrders, array $mergedData, OrderMerge $mergeRecord): Order
    {
        $firstOrder = $originalOrders->first();
        
        // 创建合并订单
        $mergedOrder = Order::create([
            'user_id' => $firstOrder->user_id,
            'user_address_id' => $firstOrder->user_address_id,
            'order_no' => Order::generateOrderNo(),
            'total' => $mergedData['final_total'],
            'subtotal' => $mergedData['subtotal'],
            'discount' => $mergedData['total_discount'],
            'payment_discount' => $mergedData['payment_discount'],
            'payment_discount_info' => $mergedData['payment_offer_info'],
            'original_total' => $mergedData['original_total'],
            'pricing_info' => $mergedData['discount_info'],
            'status' => 'pending',
            'payment_method' => $mergedData['payment_method'] ?? $firstOrder->payment_method,
            'shipping_address' => $firstOrder->shipping_address,
            'contact_name' => $firstOrder->contact_name,
            'contact_phone' => $firstOrder->contact_phone,
            'region_id' => $firstOrder->region_id,
            
            // 合并标识
            'is_merged' => true,
            'order_merge_id' => $mergeRecord->id,
            'merge_discount_details' => $this->buildMergeDiscountDetails($mergedData),
            'merge_savings' => $mergeRecord->total_savings,
            
            'notes' => $this->buildMergeNotes($originalOrders),
        ]);
        
        // 创建合并后的订单项
        foreach ($mergedData['items'] as $itemData) {
            OrderItem::create([
                'order_id' => $mergedOrder->id,
                'product_id' => $itemData['product_id'],
                'product_name' => $itemData['product_name'],
                'product_sku' => $itemData['product_sku'] ?? '',
                'quantity' => $itemData['quantity'],
                'price' => $itemData['final_price'],
                'original_price' => $itemData['base_price'],
                'total' => $itemData['item_total'],
                'unit_id' => $itemData['unit_id'],
                'region_id' => $firstOrder->region_id,
            ]);
        }
        
        // 更新合并记录中的合并订单ID
        $mergeRecord->update(['merged_order_id' => $mergedOrder->id]);
        
        return $mergedOrder;
    }
    
    /**
     * 更新原订单状态
     */
    private function updateOriginalOrders($originalOrders, Order $mergedOrder, OrderMerge $mergeRecord): void
    {
        foreach ($originalOrders as $order) {
            // 保存合并前的数据快照
            $preMergeData = [
                'original_total' => $order->total,
                'original_subtotal' => $order->subtotal,
                'original_discount' => $order->discount,
                'original_payment_discount' => $order->payment_discount,
                'original_pricing_info' => $order->pricing_info,
                'original_items' => $order->items->map(function ($item) {
                    return [
                        'product_id' => $item->product_id,
                        'quantity' => $item->quantity,
                        'price' => $item->price,
                        'total' => $item->total,
                    ];
                })->toArray(),
                'merge_timestamp' => now(),
            ];
            
            $order->update([
                'status' => 'cancelled', // 使用现有的状态值，表示原订单已被合并取消
                'is_merged_from' => true,
                'merged_to_order_id' => $mergedOrder->id,
                'order_merge_id' => $mergeRecord->id,
                'pre_merge_data' => $preMergeData,
            ]);
        }
    }
    
    /**
     * 调整库存（合并可能改变商品数量）
     */
    private function adjustInventory($originalOrders, Order $mergedOrder): void
    {
        // 先恢复原订单的库存
        foreach ($originalOrders as $order) {
            foreach ($order->items as $item) {
                $product = $item->product;
                if ($product) {
                    $product->addStock($item->quantity);
                }
            }
        }
        
        // 再按合并后的数量扣减库存
        foreach ($mergedOrder->items as $item) {
            $product = $item->product;
            if ($product) {
                $result = $product->reduceStockWithPolicy($item->quantity, $item->unit_id);
                
                if (!$result['success']) {
                    throw new \Exception("库存调整失败：{$product->name}，{$result['message']}");
                }
            }
        }
    }
    
    /**
     * 构建优惠对比信息
     */
    private function buildDiscountComparison(array $originalInfo, array $mergedData): array
    {
        $originalTotal = array_sum(array_column($originalInfo, 'total'));
        $originalDiscount = array_sum(array_column($originalInfo, 'discount'));
        $originalPaymentDiscount = array_sum(array_column($originalInfo, 'payment_discount'));
        
        return [
            'original' => [
                'total_amount' => $originalTotal,
                'product_discount' => $originalDiscount,
                'payment_discount' => $originalPaymentDiscount,
                'final_amount' => $originalTotal,
                'order_count' => count($originalInfo),
            ],
            'merged' => [
                'total_amount' => $mergedData['final_total'],
                'product_discount' => $mergedData['total_discount'],
                'payment_discount' => $mergedData['payment_discount'],
                'final_amount' => $mergedData['final_total'],
                'order_count' => 1,
            ],
            'savings' => [
                'amount' => $originalTotal - $mergedData['final_total'],
                'percentage' => $originalTotal > 0 ? round((($originalTotal - $mergedData['final_total']) / $originalTotal) * 100, 2) : 0,
            ],
        ];
    }
    
    /**
     * 构建合并优惠明细
     */
    private function buildMergeDiscountDetails(array $mergedData): array
    {
        return [
            'strategy' => 'intelligent_recalculation',
            'product_discounts' => $mergedData['discount_info'],
            'payment_discount' => $mergedData['payment_offer_info'],
            'total_discount_amount' => $mergedData['total_discount'] + $mergedData['payment_discount'],
            'discount_breakdown' => $this->categorizeDiscounts($mergedData['discount_info']),
        ];
    }
    
    /**
     * 分类优惠信息
     */
    private function categorizeDiscounts(array $discountInfo): array
    {
        $categories = [
            'region' => ['region_price', 'category_region_discount'],
            'member' => ['product_member_discount', 'category_member_discount', 'global_member_discount'],
            'payment' => ['payment_discount'],
        ];
        
        $breakdown = [];
        
        foreach ($categories as $category => $types) {
            $categoryAmount = 0;
            $categoryItems = [];
            
            foreach ($discountInfo as $discount) {
                if (in_array($discount['type'], $types)) {
                    $categoryAmount += $discount['discount_amount'];
                    $categoryItems[] = $discount;
                }
            }
            
            if ($categoryAmount > 0) {
                $breakdown[$category] = [
                    'total_amount' => $categoryAmount,
                    'items' => $categoryItems,
                ];
            }
        }
        
        return $breakdown;
    }
    
    /**
     * 构建合并备注
     */
    private function buildMergeNotes($originalOrders): string
    {
        $orderNos = $originalOrders->pluck('order_no')->toArray();
        return "由订单合并而来：" . implode(', ', $orderNos) . "。合并时间：" . now()->format('Y-m-d H:i:s');
    }
    
    /**
     * 记录合并操作日志
     */
    private function logMergeOperation(OrderMerge $mergeRecord, $originalOrders, Order $mergedOrder): void
    {
        Log::info('订单合并完成', [
            'merge_id' => $mergeRecord->id,
            'user_id' => $mergeRecord->user_id,
            'original_order_ids' => $mergeRecord->original_order_ids,
            'merged_order_id' => $mergedOrder->id,
            'merged_order_no' => $mergedOrder->order_no,
            'original_amount' => $mergeRecord->original_total_amount,
            'merged_amount' => $mergeRecord->merged_total_amount,
            'savings' => $mergeRecord->total_savings,
            'strategy' => $mergeRecord->merge_strategy,
        ]);
    }
    
    /**
     * 撤销订单合并
     */
    public function revertMerge(int $mergeId): bool
    {
        return DB::transaction(function () use ($mergeId) {
            $mergeRecord = OrderMerge::findOrFail($mergeId);
            
            if ($mergeRecord->status === 'reverted') {
                throw new \Exception('该合并记录已被撤销');
            }
            
            $mergedOrder = Order::find($mergeRecord->merged_order_id);
            if (!$mergedOrder || $mergedOrder->status !== 'pending') {
                throw new \Exception('合并订单状态已变更，无法撤销');
            }
            
            // 恢复原订单
            $originalOrders = Order::whereIn('id', $mergeRecord->original_order_ids)->get();
            
            foreach ($originalOrders as $order) {
                $preMergeData = $order->pre_merge_data;
                
                $order->update([
                    'status' => 'pending',
                    'is_merged_from' => false,
                    'merged_to_order_id' => null,
                    'order_merge_id' => null,
                    'pre_merge_data' => null,
                ]);
                
                // 恢复库存
                foreach ($order->items as $item) {
                    $product = $item->product;
                    if ($product) {
                        $product->reduceStockWithPolicy($item->quantity, $item->unit_id);
                    }
                }
            }
            
            // 释放合并订单库存
            foreach ($mergedOrder->items as $item) {
                $product = $item->product;
                if ($product) {
                    $product->addStock($item->quantity);
                }
            }
            
            // 删除合并订单
            $mergedOrder->delete();
            
            // 更新合并记录状态
            $mergeRecord->update([
                'status' => 'reverted',
                'reverted_at' => now(),
                'reverted_by' => auth()->id(),
            ]);
            
            Log::info('订单合并已撤销', [
                'merge_id' => $mergeId,
                'reverted_by' => auth()->id(),
            ]);
            
            return [
                'success' => true,
                'message' => '订单合并已撤销',
                'reverted_orders' => $originalOrders->pluck('order_no')->toArray()
            ];
        });
    }

    /**
     * 预览订单合并效果
     */
    public function previewMerge(array $orderIds): array
    {
        $orders = Order::with(['user', 'items.product', 'userAddress'])
            ->whereIn('id', $orderIds)
            ->get();

        if ($orders->count() !== count($orderIds)) {
            throw new \Exception('部分订单不存在');
        }

        // 检查合并规则
        $mergeRules = $this->checkMergeRules($orders);
        
        // 计算合并后的优惠
        $mergedPreview = $this->calculateMergedOrderPreview($orders);
        
        return [
            'original_orders' => $orders->map(function ($order) {
                return [
                    'id' => $order->id,
                    'order_no' => $order->order_no,
                    'total' => $order->total,
                    'user' => $order->user ? [
                        'id' => $order->user->id,
                        'name' => $order->user->name
                    ] : null,
                    'contact_phone' => $order->contact_phone
                ];
            })->toArray(),
            'merged_preview' => $mergedPreview,
            'can_merge' => $mergeRules['can_merge'],
            'merge_rules' => $mergeRules['rules'],
            'warnings' => $mergeRules['warnings'] ?? [],
            'errors' => $mergeRules['errors'] ?? []
        ];
    }

    /**
     * 检查合并规则
     */
    private function checkMergeRules($orders): array
    {
        $rules = [
            'same_user' => false,
            'same_payment_method' => false,
            'same_region' => false,
            'all_pending' => false
        ];
        
        $warnings = [];
        $errors = [];
        
        // 检查用户一致性
        $userIds = $orders->pluck('user_id')->unique();
        $rules['same_user'] = $userIds->count() === 1;
        if (!$rules['same_user']) {
            $errors[] = '只能合并同一用户的订单';
        }
        
        // 检查支付方式一致性
        $paymentMethods = $orders->pluck('payment_method')->unique();
        $rules['same_payment_method'] = $paymentMethods->count() === 1;
        if (!$rules['same_payment_method']) {
            $errors[] = '只能合并相同支付方式的订单';
        }
        
        // 检查订单状态
        $statuses = $orders->pluck('status')->unique();
        $rules['all_pending'] = $statuses->count() === 1 && $statuses->first() === 'pending';
        if (!$rules['all_pending']) {
            $errors[] = '只能合并待付款状态的订单';
        }
        
        // 检查配送区域（货到付款）
        if ($paymentMethods->first() === 'cod') {
            $regions = $orders->pluck('region_id')->unique();
            $rules['same_region'] = $regions->count() === 1;
            if (!$rules['same_region']) {
                $errors[] = '货到付款订单需要同一配送区域';
            }
        } else {
            $rules['same_region'] = true;
        }
        
        return [
            'can_merge' => empty($errors),
            'rules' => $rules,
            'warnings' => $warnings,
            'errors' => $errors
        ];
    }

    /**
     * 计算合并后的订单预览
     */
    private function calculateMergedOrderPreview($orders): array
    {
        $firstOrder = $orders->first();
        $originalTotal = $orders->sum('total');
        $originalSubtotal = $orders->sum('subtotal');
        $originalDiscount = $orders->sum('discount');
        $originalPaymentDiscount = $orders->sum('payment_discount');
        
        // 合并商品项
        $mergedItems = [];
        foreach ($orders as $order) {
            foreach ($order->items as $item) {
                $key = $item->product_id;
                if (!isset($mergedItems[$key])) {
                    $mergedItems[$key] = [
                        'product_id' => $item->product_id,
                        'product_name' => $item->product->name ?? "商品#{$item->product_id}",
                        'product_sku' => $item->product_sku ?? '',
                        'quantity' => 0,
                        'final_price' => $item->price,
                        'base_price' => $item->price,
                        'item_total' => 0,
                        'unit_id' => $item->unit_id,
                    ];
                }
                $mergedItems[$key]['quantity'] += $item->quantity;
                $mergedItems[$key]['item_total'] += $item->total;
            }
        }
        
        // 使用真实的优惠计算
        $potentialSavings = $this->calculatePotentialSavings($orders);
        $newTotal = max(0, $originalTotal - $potentialSavings);
        
        // 计算新的支付优惠
        $newPaymentDiscount = $this->calculatePaymentDiscount($firstOrder->payment_method, $originalSubtotal);
        
        return [
            // 预览显示用的字段
            'total_amount' => round($newTotal, 2),
            'savings' => round($potentialSavings, 2),
            'payment_method' => $firstOrder->payment_method,
            'payment_method_name' => $this->getPaymentMethodName($firstOrder->payment_method),
            'items' => array_values($mergedItems),
            
            // executeMerge 需要的完整字段
            'final_total' => round($newTotal, 2),
            'subtotal' => $originalSubtotal,
            'total_discount' => $originalDiscount,
            'payment_discount' => $newPaymentDiscount,
            'original_total' => $originalSubtotal,
            'payment_offer_info' => [
                'method' => $firstOrder->payment_method,
                'discount_amount' => $newPaymentDiscount,
                'description' => "合并订单{$this->getPaymentMethodName($firstOrder->payment_method)}优惠"
            ],
            'discount_info' => [
                [
                    'type' => 'merge_optimization',
                    'name' => '订单合并优化',
                    'discount_amount' => $potentialSavings,
                    'description' => '通过合并订单获得的额外优惠'
                ]
            ]
        ];
    }

    /**
     * 获取当日合并候选订单
     */
    public function getDailyMergeCandidates(string $date = null): array
    {
        $date = $date ?: now()->toDateString();
        
        // 获取当日待付款订单，按用户分组
        $orders = Order::with(['user', 'items.product'])
            ->where('status', 'pending')
            ->whereDate('created_at', $date)
            ->where(function($query) {
                $query->where('is_merged_from', false)
                      ->orWhereNull('is_merged_from');
            })
            ->whereHas('user') // 确保有用户信息
            ->get()
            ->groupBy('user_id');

        $candidates = [];
        
        foreach ($orders as $userId => $userOrders) {
            if ($userOrders->count() < 2) {
                continue; // 少于2个订单无法合并
            }

            $firstOrder = $userOrders->first();
            $user = $firstOrder->user;
            
            if (!$user) {
                continue; // 跳过没有用户信息的订单
            }

            // 按支付方式分组
            $paymentGroups = $userOrders->groupBy('payment_method');
            
            foreach ($paymentGroups as $paymentMethod => $paymentOrders) {
                if ($paymentOrders->count() < 2) {
                    continue;
                }

                // 检查是否可以合并
                $canMerge = true;
                $mergeIssues = [];
                
                // 检查货到付款的区域限制
                if ($paymentMethod === 'cod') {
                    $regions = $paymentOrders->pluck('region_id')->unique();
                    if ($regions->count() > 1) {
                        $canMerge = false;
                        $mergeIssues[] = '货到付款订单需要同一配送区域';
                    }
                }

                $totalAmount = $paymentOrders->sum('total');
                $potentialSavings = $this->calculatePotentialSavings($paymentOrders);

                $candidates[] = [
                    'user_id' => $userId,
                    'user_name' => $user->name,
                    'user_phone' => $user->phone ?? '',
                    'payment_method' => $paymentMethod,
                    'payment_method_name' => $this->getPaymentMethodName($paymentMethod),
                    'orders' => $paymentOrders->map(function ($order) {
                        return [
                            'id' => $order->id,
                            'order_no' => $order->order_no,
                            'total' => $order->total
                        ];
                    })->toArray(),
                    'total_amount' => $totalAmount,
                    'potential_savings' => $potentialSavings,
                    'can_merge' => $canMerge,
                    'merge_issues' => $mergeIssues
                ];
            }
        }

        // 按潜在节省金额降序排列
        usort($candidates, function ($a, $b) {
            return $b['potential_savings'] <=> $a['potential_savings'];
        });

        return $candidates;
    }

    /**
     * 计算潜在节省金额 - 基于支付优惠差异
     */
    private function calculatePotentialSavings($orders): float
    {
        if ($orders->isEmpty()) {
            return 0;
        }

        // 获取第一个订单的支付方式
        $paymentMethod = $orders->first()->payment_method;
        
        // 计算原始订单的支付优惠总和
        $originalPaymentDiscounts = $orders->sum('payment_discount');
        
        // 计算合并后的订单金额（商品小计）
        $mergedSubtotal = $orders->sum('subtotal');
        
        // 计算合并后可以享受的支付优惠
        $mergedPaymentDiscount = $this->calculatePaymentDiscount($paymentMethod, $mergedSubtotal);
        
        // 节省金额 = 合并后支付优惠 - 原始支付优惠总和
        $savings = max(0, $mergedPaymentDiscount - $originalPaymentDiscounts);
        
        return round($savings, 2);
    }

    /**
     * 计算支付优惠
     */
    private function calculatePaymentDiscount(string $paymentMethod, float $amount): float
    {
        try {
            $paymentOfferService = app(\App\Payment\Services\PaymentOfferService::class);
            $paymentOfferInfo = $paymentOfferService->calculatePaymentOffer(
                $paymentMethod,
                $amount,
                true
            );
            
            return $paymentOfferInfo['offer_amount'] ?? 0;
        } catch (\Exception $e) {
            // 使用简单的支付优惠逻辑
            switch ($paymentMethod) {
                case 'wechat':
                    return $amount >= 50 ? min(5, $amount) : 0;
                case 'alipay':
                    return $amount >= 30 ? min(3, $amount) : 0;
                default:
                    return 0;
            }
        }
    }

    /**
     * 计算合并后订单的成本
     */
    private function calculateMergedOrderCost(array $mergedOrderItems, $user, ?int $regionId, string $paymentMethod): float
    {
        // 简化版本：直接使用订单项的原始价格计算
        // 因为我们主要关注的是合并带来的支付优惠差异，而不是重新计算商品优惠
        
        $subtotal = 0;
        
        // 使用订单项的实际价格（已经包含了商品优惠）
        foreach ($mergedOrderItems as $item) {
            $subtotal += $item['unit_price'] * $item['quantity'];
        }
        
        // 计算支付优惠
        try {
            $paymentOfferService = app(\App\Payment\Services\PaymentOfferService::class);
            $paymentOfferInfo = $paymentOfferService->calculatePaymentOffer(
                $paymentMethod,
                $subtotal,
                true // 启用叠加优惠
            );
            
            $paymentDiscount = $paymentOfferInfo['offer_amount'] ?? 0;
        } catch (\Exception $e) {
            // 如果支付优惠服务不可用，使用简单的优惠逻辑
            $paymentDiscount = 0;
            if ($paymentMethod === 'wechat' && $subtotal >= 50) {
                $paymentDiscount = min(5, $subtotal); // 微信支付满50减5
            } elseif ($paymentMethod === 'alipay' && $subtotal >= 30) {
                $paymentDiscount = min(3, $subtotal); // 支付宝满30减3
            }
        }
        
        // 最终成本 = 商品小计 - 支付优惠
        return max(0, $subtotal - $paymentDiscount);
    }

    /**
     * 获取合并历史
     */
    public function getMergeHistory(array $filters = [], int $page = 1, int $perPage = 20): array
    {
        $query = OrderMerge::with(['user', 'mergedOrder'])
            ->orderBy('created_at', 'desc');

        // 应用筛选条件
        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        $total = $query->count();
        $merges = $query->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        return [
            'data' => $merges->map(function ($merge) {
                return [
                    'id' => $merge->id,
                    'user_id' => $merge->user_id,
                    'merged_order_id' => $merge->merged_order_id,
                    'original_order_ids' => $merge->original_order_ids,
                    'merge_date' => $merge->created_at->toDateString(),
                    'original_total_amount' => $merge->original_total_amount,
                    'merged_total_amount' => $merge->merged_total_amount,
                    'total_savings' => $merge->total_savings,
                    'merge_type' => $merge->merge_type,
                    'merge_reason' => $merge->merge_reason,
                    'status' => $merge->status,
                    'created_at' => $merge->created_at->toISOString(),
                    'user' => $merge->user ? [
                        'id' => $merge->user->id,
                        'name' => $merge->user->name,
                        'phone' => $merge->user->phone
                    ] : null,
                    'merged_order' => $merge->mergedOrder ? [
                        'id' => $merge->mergedOrder->id,
                        'order_no' => $merge->mergedOrder->order_no
                    ] : null
                ];
            })->toArray(),
            'total' => $total,
            'current_page' => $page,
            'per_page' => $perPage
        ];
    }

    /**
     * 自动合并当日订单
     */
    public function autoMergeTodayOrders(): array
    {
        $candidates = $this->getDailyMergeCandidates();
        $mergedCount = 0;
        $totalSavings = 0;
        $results = [];

        foreach ($candidates as $candidate) {
            // 检查是否可以合并
            if (!$candidate['can_merge']) {
                continue;
            }
            
            // 对于非货到付款订单，检查节省金额
            if ($candidate['payment_method'] !== 'cod' && $candidate['potential_savings'] < 5) {
                continue; // 跳过节省金额太少的非货到付款订单
            }
            
            // 货到付款订单无需考虑节省金额，主要目的是方便配送

            try {
                $orderIds = array_column($candidate['orders'], 'id');
                
                // 计算合并数据
                $orders = Order::whereIn('id', $orderIds)->get();
                $mergedData = $this->calculateMergedOrderData($orders);
                
                $mergedOrder = $this->executeMerge($orderIds, $mergedData, [
                    'merge_type' => 'auto',
                    'merge_reason' => '系统自动合并'
                ]);
                
                $mergedCount++;
                $totalSavings += $candidate['potential_savings'];
                $results[] = [
                    'success' => true,
                    'merged_order' => $mergedOrder,
                    'original_order_ids' => $orderIds,
                    'savings' => $candidate['potential_savings']
                ];
            } catch (\Exception $e) {
                // 记录错误但继续处理其他订单
                Log::error('自动合并订单失败', [
                    'user_id' => $candidate['user_id'],
                    'order_ids' => array_column($candidate['orders'], 'id'),
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'merged_count' => $mergedCount,
            'total_savings' => $totalSavings,
            'results' => $results
        ];
    }

    /**
     * 计算合并订单数据
     */
    private function calculateMergedOrderData($orders): array
    {
        $firstOrder = $orders->first();
        $totalAmount = $orders->sum('subtotal');
        
        // 简化的优惠计算
        $newTotal = $totalAmount;
        if ($totalAmount >= 200) {
            $newTotal = $totalAmount * 0.95; // 5%优惠
        } elseif ($totalAmount >= 100) {
            $newTotal = $totalAmount * 0.97; // 3%优惠
        }
        
        return [
            'user_id' => $firstOrder->user_id,
            'payment_method' => $firstOrder->payment_method,
            'total' => $newTotal,
            'subtotal' => $totalAmount,
            'discount' => $totalAmount - $newTotal,
            'contact_name' => $firstOrder->contact_name,
            'contact_phone' => $firstOrder->contact_phone,
            'shipping_address' => $firstOrder->shipping_address,
            'region_id' => $firstOrder->region_id,
            'items' => $this->mergeOrderItems($orders)
        ];
    }

    /**
     * 合并订单项
     */
    private function mergeOrderItems($orders): array
    {
        $mergedItems = [];
        
        foreach ($orders as $order) {
            foreach ($order->items as $item) {
                $key = $item->product_id . '_' . ($item->unit_id ?? 'default');
                
                if (!isset($mergedItems[$key])) {
                    $mergedItems[$key] = [
                        'product_id' => $item->product_id,
                        'quantity' => 0,
                        'price' => $item->price,
                        'unit_id' => $item->unit_id,
                        'total' => 0
                    ];
                }
                
                $mergedItems[$key]['quantity'] += $item->quantity;
                $mergedItems[$key]['total'] += $item->total;
            }
        }
        
        return array_values($mergedItems);
    }


} 