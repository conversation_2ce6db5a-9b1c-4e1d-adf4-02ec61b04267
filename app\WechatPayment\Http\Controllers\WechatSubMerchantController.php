<?php

namespace App\WechatPayment\Http\Controllers;

use App\Http\Controllers\Controller;
use App\WechatPayment\Models\WechatServiceProvider;
use App\WechatPayment\Models\WechatSubMerchant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class WechatSubMerchantController extends Controller
{
    /**
     * 显示子商户列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = WechatSubMerchant::query()->with('provider');
        
        // 搜索条件
        if ($request->has('name')) {
            $query->where('name', 'like', '%' . $request->input('name') . '%');
        }
        
        if ($request->has('sub_mch_id')) {
            $query->where('sub_mch_id', 'like', '%' . $request->input('sub_mch_id') . '%');
        }
        
        if ($request->has('provider_id') && $request->input('provider_id') > 0) {
            $query->where('provider_id', $request->input('provider_id'));
        }
        
        if ($request->has('is_active') && $request->input('is_active') !== '') {
            $query->where('is_active', $request->input('is_active'));
        }
        
        $subMerchants = $query->orderBy('created_at', 'desc')->paginate(10);
        
        return response()->json([
            'data' => $subMerchants->items(),
            'total' => $subMerchants->total(),
            'current_page' => $subMerchants->currentPage(),
            'last_page' => $subMerchants->lastPage(),
        ]);
    }

    /**
     * 存储新创建的子商户
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider_id' => 'required|exists:wechat_service_providers,id',
            'name' => 'required|max:100',
            'sub_mch_id' => 'required|max:50',
            'sub_appid' => 'nullable|max:50',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => $validator->errors()->first(),
            ], 422);
        }
        
        $data = $request->all();
        $subMerchant = WechatSubMerchant::create($data);
        
        return response()->json([
            'code' => 0,
            'message' => '创建成功',
            'data' => $subMerchant,
        ]);
    }

    /**
     * 显示指定子商户
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $subMerchant = WechatSubMerchant::with('provider')->findOrFail($id);
        
        return response()->json([
            'code' => 0,
            'data' => $subMerchant,
        ]);
    }

    /**
     * 更新指定子商户
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'provider_id' => 'required|exists:wechat_service_providers,id',
            'name' => 'required|max:100',
            'sub_mch_id' => 'required|max:50',
            'sub_appid' => 'nullable|max:50',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => $validator->errors()->first(),
            ], 422);
        }
        
        $subMerchant = WechatSubMerchant::findOrFail($id);
        $data = $request->all();
        
        $subMerchant->update($data);
        
        return response()->json([
            'code' => 0,
            'message' => '更新成功',
            'data' => $subMerchant,
        ]);
    }

    /**
     * 删除指定子商户
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $subMerchant = WechatSubMerchant::findOrFail($id);
        
        // 检查是否有关联的支付记录
        if ($subMerchant->payments()->count() > 0) {
            return response()->json([
                'code' => 400,
                'message' => '该子商户有关联的支付记录，无法删除',
            ], 400);
        }
        
        $subMerchant->delete();
        
        return response()->json([
            'code' => 0,
            'message' => '删除成功',
        ]);
    }
} 