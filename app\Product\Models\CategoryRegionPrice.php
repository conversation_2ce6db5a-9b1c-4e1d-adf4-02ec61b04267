<?php

namespace App\Product\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Product\Models\Category;
use App\Region\Models\Region;

class CategoryRegionPrice extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'region_id',
        'discount_type',
        'discount_value',
        'max_discount',
        'status',
        'start_time',
        'end_time',
        'description',
    ];

    protected $casts = [
        'discount_value' => 'decimal:2',
        'max_discount' => 'decimal:2',
        'status' => 'boolean',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * 关联区域
     */
    public function region()
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * 检查价格规则是否有效
     */
    public function isValid()
    {
        if (!$this->status) {
            return false;
        }

        $now = now();

        if ($this->start_time && $this->start_time->gt($now)) {
            return false;
        }

        if ($this->end_time && $this->end_time->lt($now)) {
            return false;
        }

        return true;
    }
} 