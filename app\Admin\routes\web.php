<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin模块 Web 路由
|--------------------------------------------------------------------------
|
| 这里定义所有Admin模块的Web路由
|
*/

// 管理员后台Web路由
Route::prefix('admin')->middleware(['web'])->group(function () {
    // 登录页面
    Route::get('/login', [App\Admin\Http\Controllers\AuthController::class, 'showLoginForm'])->name('employee.login');
    Route::post('/login', [App\Admin\Http\Controllers\AuthController::class, 'login'])->name('employee.login.post');
    
    // 需要认证的路由
    Route::middleware(['auth:web', 'backend.access:admin'])->group(function () {
        // 仪表盘
        Route::get('/', [App\Admin\Http\Controllers\DashboardController::class, 'index'])->name('admin.dashboard');
        
        // 员工管理
        Route::resource('employees', App\Admin\Http\Controllers\EmployeeController::class)
            ->names('admin.employees');
        
        // 系统设置
        Route::get('/settings', [App\Admin\Http\Controllers\SettingController::class, 'index'])->name('admin.settings');
        Route::post('/settings', [App\Admin\Http\Controllers\SettingController::class, 'update'])->name('admin.settings.update');
        
        // 登出
        Route::post('/logout', [App\Admin\Http\Controllers\AuthController::class, 'logout'])->name('employee.logout');
    });
}); 