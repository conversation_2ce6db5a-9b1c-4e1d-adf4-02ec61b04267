<?php

namespace App\Unit\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Unit\Models\Unit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class UnitExtendController extends Controller
{
    /**
     * 获取单位使用统计情况
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnitUsage($id)
    {
        $unit = Unit::findOrFail($id);
        $stats = $unit->getUsageStats();
        
        return response()->json(ApiResponse::success($stats));
    }
    
    /**
     * 获取单位类别列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategories()
    {
        $categories = [
            ['value' => 'weight', 'label' => '重量', 'description' => '用于计量物体重量的单位，如千克、克、吨等'],
            ['value' => 'volume', 'label' => '体积', 'description' => '用于计量物体体积的单位，如升、毫升、立方米等'],
            ['value' => 'length', 'label' => '长度', 'description' => '用于计量物体长度的单位，如米、厘米、毫米等'],
            ['value' => 'quantity', 'label' => '数量', 'description' => '用于计量物体数量的单位，如个、件、台等'],
            ['value' => 'package', 'label' => '包装', 'description' => '用于计量物体包装的单位，如箱、盒、包等'],
            ['value' => 'time', 'label' => '时间', 'description' => '用于计量时间的单位，如小时、天、月等'],
        ];
        
        return response()->json(ApiResponse::success($categories));
    }
    
    /**
     * 根据单位名称猜测单位类别
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function guessUnitCategory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $unitName = $request->name;
        
        // 定义单位类别映射
        $categoryMappings = [
            'weight' => ['kg', 'g', 't', 'lb', 'oz', '千克', '克', '吨', '公斤', '斤', '两'],
            'volume' => ['l', 'ml', 'kl', 'cc', 'm³', 'dm³', 'cm³', '升', '毫升', '立方米', '立方厘米', '桶', '瓶'],
            'length' => ['m', 'cm', 'mm', 'km', 'in', 'ft', 'yd', '米', '厘米', '毫米', '公里', '英寸', '英尺'],
            'package' => ['box', 'carton', 'case', 'pack', 'pallet', '箱', '盒', '包', '袋', '罐'],
            'quantity' => ['pcs', 'pc', 'set', 'unit', '个', '件', '台', '套', '支', '张', '块', '只', '根'],
            'time' => ['h', 'hr', 'min', 'sec', 'd', 'mo', 'yr', '小时', '分钟', '秒', '天', '月', '年'],
        ];
        
        // 尝试匹配单位名称
        $guessedCategory = 'quantity'; // 默认类别
        
        foreach ($categoryMappings as $category => $units) {
            // 检查单位名称是否在列表中，或者是否以列表中的单位为前缀/后缀
            if (in_array(strtolower($unitName), array_map('strtolower', $units))) {
                $guessedCategory = $category;
                break;
            }
            
            // 检查单位名称是否包含类别关键字
            foreach ($units as $unit) {
                if (stripos($unitName, $unit) !== false) {
                    $guessedCategory = $category;
                    break 2;
                }
            }
        }
        
        // 获取类别的中文名称
        $categoryLabels = [
            'weight' => '重量',
            'volume' => '体积',
            'length' => '长度',
            'package' => '包装',
            'quantity' => '数量',
            'time' => '时间',
        ];
        
        return response()->json(ApiResponse::success([
            'category' => $guessedCategory,
            'category_label' => $categoryLabels[$guessedCategory] ?? $guessedCategory,
            'confidence' => 'medium', // 可以根据匹配度设置高中低信心级别
        ]));
    }
    
    /**
     * 获取常用单位列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCommonUnits(Request $request)
    {
        // 获取请求的类别
        $category = $request->get('category');
        
        $query = Unit::query();
        
        if ($category) {
            $query->where('category', $category);
        }
        
        // 获取使用最频繁的单位
        $commonUnits = $query->withCount(['productsAsBase', 'productsAsAuxiliary'])
            ->orderBy('products_as_base_count', 'desc')
            ->orderBy('products_as_auxiliary_count', 'desc')
            ->take(10)
            ->get();
        
        return response()->json(ApiResponse::success($commonUnits));
    }
    
    /**
     * 批量检查单位关联状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkUnitsRelations(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'unit_ids' => 'required|array',
            'unit_ids.*' => 'exists:units,id',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $unitIds = $request->unit_ids;
        $result = [];
        
        foreach ($unitIds as $unitId) {
            $unit = Unit::find($unitId);
            if ($unit) {
                $stats = $unit->getUsageStats();
                $result[$unitId] = [
                    'id' => $unitId,
                    'name' => $unit->name,
                    'display_name' => $unit->display_name,
                    'symbol' => $unit->symbol,
                    'category' => $unit->category,
                    'is_base' => $unit->is_base,
                    'usage_stats' => $stats,
                    'can_delete' => $stats['total_usage'] === 0,
                ];
            }
        }
        
        return response()->json(ApiResponse::success($result));
    }
    
    /**
     * 获取单位详细字段信息
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnitFields($id)
    {
        $unit = Unit::findOrFail($id);
        
        $fields = [
            'id' => $unit->id,
            'name' => $unit->name,
            'display_name' => $unit->display_name,
            'category' => $unit->category,
            'is_base' => $unit->is_base,
            'symbol' => $unit->symbol,
            'description' => $unit->description,
            'created_at' => $unit->created_at,
            'updated_at' => $unit->updated_at,
        ];
        
        return response()->json(ApiResponse::success($fields));
    }
    
    /**
     * 更新单位信息
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $unit = Unit::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:20|unique:units,name,' . $id,
            'display_name' => 'sometimes|required|string|max:50',
            'category' => 'sometimes|required|string|in:weight,volume,length,quantity,time,package',
            'is_base' => 'sometimes|boolean',
            'symbol' => 'nullable|string|max:10',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        // 检查当前单位是否被使用，如果是则限制某些字段的修改
        $stats = $unit->getUsageStats();
        if ($stats['total_usage'] > 0) {
            // 如果单位已被使用，限制修改核心字段
            if ($request->has('category') && $request->category !== $unit->category) {
                return response()->json(ApiResponse::error('单位已被使用，不能修改类别', 422), 422);
            }
            
            if ($request->has('is_base') && $request->is_base !== $unit->is_base) {
                return response()->json(ApiResponse::error('单位已被使用，不能修改基本单位标志', 422), 422);
            }
        }
        
        $unit->update($request->only([
            'name',
            'display_name',
            'category',
            'is_base',
            'symbol',
            'description',
        ]));
        
        return response()->json(ApiResponse::success($unit, '单位更新成功'));
    }
    
    /**
     * 删除单位
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            // 记录删除请求
            Log::info('删除单位请求', [
                'unit_id' => $id
            ]);
            
            // 使用单位服务删除单位
            $unitService = app(\App\Unit\Services\UnitService::class);
            $result = $unitService->deleteUnit($id);
            
            Log::info('单位删除成功', [
                'unit_id' => $id,
                'result' => $result
            ]);
            
            return response()->json(ApiResponse::success(null, '单位删除成功'));
        } catch (\Exception $e) {
            Log::error('单位删除失败', [
                'unit_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 使用统一的错误响应格式
            $statusCode = strpos($e->getMessage(), '单位已被使用') !== false ? 422 : 500;
            
            return response()->json(
                ApiResponse::error('删除单位失败: ' . $e->getMessage(), $statusCode),
                $statusCode
            );
        }
    }
} 