<?php

namespace App\Payment\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentOffer extends Model
{
    use HasFactory;

    protected $table = 'payment_offers';

    protected $fillable = [
        'payment_method',
        'offer_type',
        'offer_value',
        'min_amount',
        'max_offer',
        'status',
        'start_time',
        'end_time',
        'description',
        'sort_order'
    ];

    protected $casts = [
        'offer_value' => 'decimal:2',
        'min_amount' => 'decimal:2',
        'max_offer' => 'decimal:2',
        'status' => 'boolean',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'sort_order' => 'integer',
    ];

    /**
     * 支付方式枚举
     */
    public const PAYMENT_METHODS = [
        'wechat' => '微信支付',
        'alipay' => '支付宝',
        'cod' => '货到付款',
        'bank' => '银行卡',
        'cash' => '现金'
    ];

    /**
     * 优惠类型枚举
     */
    public const OFFER_TYPES = [
        'fixed_amount' => '固定金额',
        'percentage' => '百分比',
        'none' => '无优惠'
    ];

    /**
     * 检查优惠是否有效
     */
    public function isValid(): bool
    {
        if (!$this->status) {
            return false;
        }

        $now = now();

        if ($this->start_time && $this->start_time->gt($now)) {
            return false;
        }

        if ($this->end_time && $this->end_time->lt($now)) {
            return false;
        }

        return true;
    }

    /**
     * 获取支付方式名称
     */
    public function getPaymentMethodNameAttribute(): string
    {
        return self::PAYMENT_METHODS[$this->payment_method] ?? $this->payment_method;
    }

    /**
     * 获取优惠类型名称
     */
    public function getOfferTypeNameAttribute(): string
    {
        return self::OFFER_TYPES[$this->offer_type] ?? $this->offer_type;
    }

    /**
     * 计算优惠金额
     */
    public function calculateOffer(float $amount): float
    {
        if (!$this->isValid() || $amount < $this->min_amount) {
            return 0;
        }

        $offer = 0;

        switch ($this->offer_type) {
            case 'fixed_amount':
                $offer = min($this->offer_value, $amount);
                break;
            case 'percentage':
                $offer = $amount * ($this->offer_value / 100);
                if ($this->max_offer) {
                    $offer = min($offer, $this->max_offer);
                }
                break;
        }

        return round($offer, 2);
    }

    /**
     * 获取有效的支付优惠
     */
    public static function getValidOfferForPayment(string $paymentMethod): ?self
    {
        return self::where('payment_method', $paymentMethod)
            ->where('status', true)
            ->where(function($query) {
                $query->whereNull('start_time')
                      ->orWhere('start_time', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_time')
                      ->orWhere('end_time', '>=', now());
            })
            ->orderBy('sort_order')
            ->first();
    }

    /**
     * 获取所有有效的支付优惠（支持叠加）
     */
    public static function getValidOffersForPayment(string $paymentMethod)
    {
        return self::where('payment_method', $paymentMethod)
            ->where('status', true)
            ->where(function($query) {
                $query->whereNull('start_time')
                      ->orWhere('start_time', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_time')
                      ->orWhere('end_time', '>=', now());
            })
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * 作用域：按支付方式筛选
     */
    public function scopeByPaymentMethod($query, string $paymentMethod)
    {
        return $query->where('payment_method', $paymentMethod);
    }

    /**
     * 作用域：按优惠类型筛选
     */
    public function scopeByOfferType($query, string $offerType)
    {
        return $query->where('offer_type', $offerType);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, bool $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：有效的优惠
     */
    public function scopeValid($query)
    {
        return $query->where('status', true)
            ->where(function($query) {
                $query->whereNull('start_time')
                      ->orWhere('start_time', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_time')
                      ->orWhere('end_time', '>=', now());
            });
    }

    /**
     * 作用域：按关键词搜索
     */
    public function scopeSearch($query, string $keyword)
    {
        return $query->where('description', 'like', "%{$keyword}%");
    }
} 