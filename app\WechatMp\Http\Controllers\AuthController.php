<?php

namespace App\WechatMp\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Crm\Models\MembershipLevel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\shop\Services\ConfigService;
use Illuminate\Support\Facades\DB;

// 使用WechatMpService服务
use App\WechatMp\Services\WechatMpService;

class AuthController extends Controller
{
    /**
     * 微信小程序登录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function wxLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            // 获取微信小程序的session_key和openid
            $code = $request->input('code');
            
            // 使用服务获取微信用户信息
            $wechatService = app(WechatMpService::class);
            
            // 记录日志便于调试
            $config = $wechatService->getMiniProgramConfig();
            Log::info('微信小程序登录参数', [
                'code' => $code,
                'app_id' => $config['app_id'] ? substr($config['app_id'], 0, 4) . '...' . substr($config['app_id'], -4) : 'null',
                'secret_exists' => !empty($config['app_secret']),
                'enabled' => $config['enabled']
            ]);
            
            // 获取用户信息
            $result = $wechatService->getUserInfoByCode($code);
            
            if (!$result['success']) {
                Log::error('微信小程序登录失败', ['message' => $result['message']]);
                return response()->json(ApiResponse::error('微信登录失败: ' . $result['message'], 401), 401);
            }
            
            $openid = $result['openid'] ?? null;
            $sessionKey = $result['session_key'] ?? null;
            $unionid = $result['unionid'] ?? null;
            
            if (!$openid) {
                return response()->json(ApiResponse::error('微信登录失败: 获取openid失败', 401), 401);
            }
            
            // 获取默认会员等级
            $defaultLevel = MembershipLevel::getDefault();
            
            // 查找用户
            $user = User::where('openid', $openid)->first();
            $isNewUser = false;
            $needCompleteInfo = false;
            
            // 用户存在逻辑判断
            if ($user) {
                Log::info('找到openid匹配的用户', ['user_id' => $user->id]);
                
                // 判断用户是否需要完善信息 (手机号、商户名称、所属区域)
                $needCompleteInfo = !$user->phone || !$user->merchant_name || !$user->district;
                
                // 如果用户没有设置会员等级，则设置默认会员等级
                if (is_null($user->membership_level_id) && $defaultLevel) {
                    $user->membership_level_id = $defaultLevel->id;
                    $user->level_upgraded_at = now();
                    $user->save();
                }
            } else {
                // 用户不存在，创建新用户
                Log::info('根据openid找不到用户，创建新用户', ['openid' => substr($openid, 0, 6) . '...']);
                
                $user = User::create([
                    'name' => '微信用户' . Str::random(6),
                    'nickname' => '微信用户',
                    'password' => Hash::make(Str::random(16)),
                    'openid' => $openid,
                    'unionid' => $unionid,
                    'membership_level_id' => $defaultLevel ? $defaultLevel->id : null,
                    'level_upgraded_at' => now(),
                ]);
                
                $isNewUser = true;
                $needCompleteInfo = true; // 新用户肯定需要完善信息
            }
            
            // 创建token
            $token = $user->createToken('wechat_mini_program')->plainTextToken;
            
            // 加载用户信息和会员等级
            $userData = $user->load('membershipLevel')->toArray();
            
            // 删除role字段，不再需要
            if (isset($userData['role'])) {
                unset($userData['role']);
            }
            
            return response()->json(ApiResponse::success([
                'token' => $token,
                'token_type' => 'Bearer',
                'user' => $userData,
                'is_new' => $isNewUser,
                'need_complete_info' => $needCompleteInfo, // 是否需要完善信息
                'session_key' => $sessionKey, // 前端需要用于解密手机号等信息
            ], '登录成功'));
            
        } catch (\Exception $e) {
            Log::error('微信小程序登录异常', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('登录失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 验证码登录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function smsLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'code' => 'required|string|max:6',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            $phone = $request->input('phone');
            $code = $request->input('code');
            
            // 使用短信服务验证验证码
            $smsService = app(\App\shop\Services\Sms\AliyunSmsService::class);
            
            // 检查短信服务是否启用
            if (!$smsService->isEnabled()) {
                return response()->json(ApiResponse::error('短信验证服务未启用', 403), 403);
            }
            
            // 验证验证码
            $isValid = $smsService->verifyCode($phone, $code, 'login');
            
            if (!$isValid) {
                return response()->json(ApiResponse::error('验证码无效或已过期', 400), 400);
            }
            
            // 查找用户表中是否有匹配的手机号
            $user = User::where('phone', $phone)->first();
            
            if (!$user) {
                // 用户不存在，提示注册
                return response()->json(ApiResponse::error('该手机号尚未注册，请先注册', 404), 404);
            }
            
            // 检查用户是否有openid（是否已绑定微信）
            if (!$user->openid) {
                return response()->json(ApiResponse::error('该账号尚未绑定微信，请使用微信登录后绑定手机号', 400), 400);
            }
            
            // 获取默认会员等级
            $defaultLevel = MembershipLevel::getDefault();
            
            // 如果用户没有设置会员等级，则设置默认会员等级
            if (is_null($user->membership_level_id) && $defaultLevel) {
                $user->membership_level_id = $defaultLevel->id;
                $user->level_upgraded_at = now();
                $user->save();
            }
            
            // 创建token
            $token = $user->createToken('sms_login')->plainTextToken;
            
            // 加载用户信息和会员等级
            $userData = $user->load('membershipLevel')->toArray();
            
            // 删除role字段，不再需要
            if (isset($userData['role'])) {
                unset($userData['role']);
            }
            
            // 判断用户是否需要完善信息 (商户名称、所属区域)
            $needCompleteInfo = !$user->merchant_name || !$user->district;
            
            Log::info('验证码登录成功', [
                'user_id' => $user->id,
                'phone' => $phone,
                'need_complete_info' => $needCompleteInfo
            ]);
            
            return response()->json(ApiResponse::success([
                'token' => $token,
                'token_type' => 'Bearer',
                'user' => $userData,
                'is_new' => false,
                'need_complete_info' => $needCompleteInfo,
                'login_type' => 'sms'
            ], '登录成功'));
            
        } catch (\Exception $e) {
            Log::error('验证码登录异常', ['error' => $e->getMessage(), 'phone' => $request->input('phone')]);
            return response()->json(ApiResponse::error('登录失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 更新用户信息（微信昵称、头像等）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateUserInfo(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nickname' => 'sometimes|string|max:50',
            'avatar' => 'sometimes|string|max:500',
            'gender' => 'sometimes|integer|in:0,1,2',
            'province' => 'sometimes|string|max:50',
            'city' => 'sometimes|string|max:50',
            'country' => 'sometimes|string|max:50',
            'merchant_name' => 'sometimes|string|max:100', // 允许更新商户名称
            'district' => 'sometimes|string|max:50', // 区/县字段
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $user = $request->user();
        
        try {
            $userData = $request->only([
                'nickname', 'avatar', 'gender', 'province', 'city', 
                'country', 'merchant_name', 'district'
            ]);
            
            // 如果提供了姓名，也更新name字段
            if (isset($userData['nickname']) && !empty($userData['nickname'])) {
                $userData['name'] = $userData['nickname'];
            }
            
            // 处理手机号和验证码的逻辑（如果提供）
            if ($request->has('phone') && $request->has('code')) {
                // 验证手机号格式
                $phoneValidator = Validator::make($request->all(), [
                    'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
                    'code' => 'required|string|max:6',
                ]);
                
                if ($phoneValidator->fails()) {
                    return response()->json(ApiResponse::error($phoneValidator->errors()->first(), 422), 422);
                }
                
                // 使用短信服务验证验证码
                $smsService = app(\App\shop\Services\Sms\AliyunSmsService::class);
                
                // 检查短信服务是否启用
                if (!$smsService->isEnabled()) {
                    return response()->json(ApiResponse::error('短信验证服务未启用', 403), 403);
                }
                
                // 验证验证码
                $isValid = $smsService->verifyCode(
                    $request->phone,
                    $request->code,
                    $request->input('type', 'register')
                );
                
                if (!$isValid) {
                    return response()->json(ApiResponse::error('验证码无效或已过期', 400), 400);
                }
                
                // 验证通过，更新手机号
                $userData['phone'] = $request->phone;
            }
            
            $user->update($userData);
            
            // 重新加载用户
            $user->refresh();
            $userResponse = $user->toArray();
            
            // 删除role字段，不再需要
            if (isset($userResponse['role'])) {
                unset($userResponse['role']);
            }
            
            // 记录更新后的用户数据
            Log::info('用户信息更新成功', [
                'user_id' => $user->id,
                'updated_fields' => $userData,
                'current_user_data' => [
                    'phone' => $user->phone,
                    'merchant_name' => $user->merchant_name,
                    'district' => $user->district,
                    'city' => $user->city
                ]
            ]);
            
            return response()->json(ApiResponse::success([
                'user' => $userResponse,
                'updated_fields' => array_keys($userData)
            ], '用户信息更新成功'));
            
        } catch (\Exception $e) {
            Log::error('更新用户信息异常', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('更新失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 绑定手机号
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bindPhone(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'encryptedData' => 'required|string',
            'iv' => 'required|string',
            'session_key' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        $user = $request->user();
        $encryptedData = $request->input('encryptedData');
        $iv = $request->input('iv');
        $sessionKey = $request->input('session_key');
        
        try {
            // 使用服务解密手机号
            $wechatService = app(WechatMpService::class);
            $result = $wechatService->decryptData($sessionKey, $iv, $encryptedData);
            
            if (!$result['success']) {
                Log::error('手机号解密失败', ['message' => $result['message']]);
                return response()->json(ApiResponse::error('手机号解密失败: ' . $result['message'], 422), 422);
            }
            
            // 获取手机号并更新用户信息
            $phoneNumber = $result['data']['phoneNumber'] ?? null;
            
            if (empty($phoneNumber)) {
                return response()->json(ApiResponse::error('获取手机号失败', 422), 422);
            }
            
            // 更新用户手机号
            $user->phone = $phoneNumber;
            $user->save();
            
            return response()->json(ApiResponse::success(['phone' => $user->phone], '手机号绑定成功'));
            
        } catch (\Exception $e) {
            Log::error('手机号解密失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('手机号绑定失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 手动绑定手机号（验证码方式）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bindPhoneWithCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'code' => 'required|string|max:6',
            'type' => 'nullable|string|in:login,register,reset_password',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        $user = $request->user();
        $phone = $request->input('phone');
        $code = $request->input('code');
        $type = $request->input('type', 'register'); // 默认为注册类型
        
        try {
            // 使用短信服务验证验证码
            $smsService = app(\App\shop\Services\Sms\AliyunSmsService::class);
            
            // 检查短信服务是否启用
            if (!$smsService->isEnabled()) {
                return response()->json(ApiResponse::error('短信验证服务未启用', 403), 403);
            }
            
            // 验证验证码
            $isValid = $smsService->verifyCode(
                $phone,
                $code,
                $type
            );
            
            if (!$isValid) {
                return response()->json(ApiResponse::error('验证码无效或已过期', 400), 400);
            }
            
            // 验证通过，绑定手机号
            $user->phone = $phone;
            $user->save();
            
            // 准备返回的用户数据（不包含角色字段）
            $userData = [
                'phone' => $user->phone,
                'id' => $user->id
            ];
            
            return response()->json(ApiResponse::success($userData, '手机号绑定成功'));
            
        } catch (\Exception $e) {
            Log::error('手机号绑定失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('手机号绑定失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 发送短信验证码
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendSmsCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'type' => 'nullable|string|in:login,register,reset_password,bind',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        $phone = $request->input('phone');
        $type = $request->input('type', 'register'); // 默认为注册验证码
        
        try {
            // 使用短信服务发送验证码
            $smsService = app(\App\shop\Services\Sms\AliyunSmsService::class);
            
            // 检查短信服务是否启用
            if (!$smsService->isEnabled()) {
                return response()->json(ApiResponse::error('短信验证服务未启用', 403), 403);
            }
            
            // 检查冷却时间
            if (!$smsService->canSendSms($phone)) {
                $cooldown = $smsService->getCooldownTime();
                return response()->json(ApiResponse::error("发送过于频繁，请{$cooldown}秒后重试", 429), 429);
            }
            
            // 收集请求信息
            $requestInfo = [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ];
            
            // 发送验证码
            $result = $smsService->sendVerificationCode(
                $phone,
                $type,
                $requestInfo
            );
            
            if (!$result['success']) {
                return response()->json(ApiResponse::error($result['message'], 500), 500);
            }
            
            $response = ApiResponse::success(null, $result['message']);
            
            // 仅在测试环境返回验证码
            if (isset($result['code']) && app()->environment('local', 'testing')) {
                $response['data'] = ['code' => $result['code']];
            }
            
            return response()->json($response);
            
        } catch (\Exception $e) {
            Log::error('发送短信验证码失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('发送验证码失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取当前用户信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCurrentUser(Request $request)
    {
        $user = $request->user();
        $userData = $user->toArray();
        
        // 删除role字段，不再需要
        if (isset($userData['role'])) {
            unset($userData['role']);
        }
        
        return response()->json(ApiResponse::success($userData, '获取用户信息成功'));
    }

    /**
     * 检查微信小程序配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkConfig(Request $request)
    {
        try {
            // 添加调试信息 - 获取ShopConfig模型使用的表名和连接
            $shopConfigModel = new \App\Models\ShopConfig();
            $tableName = $shopConfigModel->getTable();
            $connection = $shopConfigModel->getConnectionName() ?: config('database.default');
            
            // 获取数据库的所有表
            $tables = \Illuminate\Support\Facades\DB::connection($connection)->getDoctrineSchemaManager()->listTableNames();
            
            // 直接从数据库查询配置记录
            $appIdConfig = \App\Models\ShopConfig::where('key', 'wx_mini_program_app_id')->first();
            $secretConfig = \App\Models\ShopConfig::where('key', 'wx_mini_program_secret')->first();
            $tokenConfig = \App\Models\ShopConfig::where('key', 'wx_mini_program_token')->first();
            $aesKeyConfig = \App\Models\ShopConfig::where('key', 'wx_mini_program_aes_key')->first();
            
            // 获取所有配置记录
            $allConfigs = \App\Models\ShopConfig::all();
            
            // 记录数据库中的记录
            Log::info('数据库配置记录', [
                'model_table' => $tableName,
                'connection' => $connection,
                'tables_in_db' => $tables,
                'table_exists' => in_array($tableName, $tables),
                'total_count' => $allConfigs->count(),
                'appId_exists' => $appIdConfig ? true : false,
                'secret_exists' => $secretConfig ? true : false,
                'wechat_group_count' => \App\Models\ShopConfig::where('group', 'wechat_mini_program')->count(),
                'all_groups' => \App\Models\ShopConfig::select('group')->distinct()->pluck('group')
            ]);
            
            // 检查配置是否完整
            $configStatus = [
                'app_id' => !empty($appIdConfig),
                'secret' => !empty($secretConfig),
                'token' => !empty($tokenConfig),
                'aes_key' => !empty($aesKeyConfig),
                'app_id_value' => $appIdConfig ? substr($appIdConfig->value, 0, 4) . '****' . substr($appIdConfig->value, -4) : null,
                'secret_length' => $secretConfig ? strlen($secretConfig->value) : 0,
                'config_source' => '直接数据库查询',
                'db_config' => [
                    'total_configs' => $allConfigs->count(),
                    'wechat_group' => \App\Models\ShopConfig::where('group', 'wechat_mini_program')->count(),
                    'all_keys' => $allConfigs->pluck('key')->take(10)->toArray(),
                    'all_groups' => \App\Models\ShopConfig::select('group')->distinct()->pluck('group'),
                    'table_name' => $tableName,
                    'connection' => $connection,
                    'tables_in_db' => $tables,
                    'table_exists' => in_array($tableName, $tables)
                ]
            ];
            
            return response()->json(ApiResponse::success($configStatus, '配置检查完成'));
            
        } catch (\Exception $e) {
            Log::error('微信小程序配置检查异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(ApiResponse::error('配置检查失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 初始化微信小程序配置（仅开发环境使用）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function initWechatConfig(Request $request)
    {
        try {
            // 只在非生产环境使用
            if (app()->environment('production')) {
                return response()->json(ApiResponse::error('禁止在生产环境使用此功能', 403), 403);
            }
            
            // 初始化配置
            $configs = [
                [
                    'key' => 'wx_mini_program_app_id',
                    'value' => 'wxd4c8d4a724eff46d',
                    'group' => 'wechat_mini_program',
                    'title' => '小程序AppID',
                    'description' => '微信小程序的AppID',
                    'type' => 'text',
                    'is_system' => true,
                    'sort_order' => 10
                ],
                [
                    'key' => 'wx_mini_program_secret',
                    'value' => $request->input('secret', '填写您的小程序Secret'),
                    'group' => 'wechat_mini_program',
                    'title' => '小程序Secret',
                    'description' => '微信小程序的Secret',
                    'type' => 'password',
                    'is_system' => true,
                    'sort_order' => 20
                ],
                [
                    'key' => 'wx_mini_program_token',
                    'value' => Str::random(32),
                    'group' => 'wechat_mini_program',
                    'title' => '小程序Token',
                    'description' => '用于微信服务器验证',
                    'type' => 'text',
                    'is_system' => true,
                    'sort_order' => 30
                ],
                [
                    'key' => 'wx_mini_program_aes_key',
                    'value' => Str::random(43),
                    'group' => 'wechat_mini_program',
                    'title' => '小程序AES Key',
                    'description' => '用于消息加解密',
                    'type' => 'text',
                    'is_system' => true,
                    'sort_order' => 40
                ]
            ];
            
            $results = [];
            
            foreach ($configs as $config) {
                $model = \App\Models\ShopConfig::updateOrCreate(
                    ['key' => $config['key']],
                    $config
                );
                
                $results[] = [
                    'key' => $model->key,
                    'status' => 'success',
                    'id' => $model->id
                ];
            }
            
            // 清除缓存
            $configService = app(\App\shop\Services\ConfigService::class);
            $configService->clearCache();
            
            return response()->json(ApiResponse::success([
                'configs' => $results,
                'message' => '微信小程序配置初始化成功'
            ]));
            
        } catch (\Exception $e) {
            Log::error('初始化微信小程序配置失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('初始化配置失败: ' . $e->getMessage(), 500), 500);
        }
    }
} 