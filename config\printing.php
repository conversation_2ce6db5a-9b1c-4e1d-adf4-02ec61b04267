<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 默认打印驱动
    |--------------------------------------------------------------------------
    |
    | 这里指定默认使用的打印驱动。支持的驱动有：
    | - browser: 浏览器打印
    | - clodop: CLodop打印控件
    | - lodop: Lodop打印控件
    | - flycloud: 飞蛾云打印
    |
    */
    'default' => env('PRINTING_DRIVER', 'browser'),

    /*
    |--------------------------------------------------------------------------
    | 打印驱动配置
    |--------------------------------------------------------------------------
    |
    | 这里配置各种打印驱动的具体参数
    |
    */
    'drivers' => [
        'browser' => [
            'driver' => 'browser',
            'auto_close' => env('BROWSER_PRINT_AUTO_CLOSE', true),
            'page_size' => env('BROWSER_PRINT_PAGE_SIZE', 'A4'),
            'orientation' => env('BROWSER_PRINT_ORIENTATION', 'portrait'),
            'timeout' => 30,
        ],

        'lodop' => [
            'driver' => 'lodop',
            'service_url' => env('CLODOP_SERVICE_URL', 'http://localhost:8000/CLodopfuncs.js'),
            'license' => env('LODOP_LICENSE', ''),
            'default_printer' => env('CLODOP_DEFAULT_PRINTER', ''),
            'debug' => env('PRINTING_DEBUG', false),
            'timeout' => 30,
        ],

        'flycloud' => [
            'driver' => 'flycloud',
            'api_url' => env('FLYCLOUD_API_URL', 'http://api.feieyun.cn/Api/Open/'),
            'user' => env('FLYCLOUD_USER', ''), // 飞蛾云后台注册账号
            'ukey' => env('FLYCLOUD_UKEY', ''), // 飞蛾云后台生成的UKEY
            'default_printer' => env('FLYCLOUD_DEFAULT_PRINTER', ''), // 默认打印机编号
            'timeout' => 30,
            'debug' => env('PRINTING_DEBUG', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 打印模板配置
    |--------------------------------------------------------------------------
    |
    | 配置各种打印模板的默认参数
    |
    */
    'templates' => [
        'order_receipt' => [
            'width' => 80, // 小票宽度（毫米）
            'font_size' => 12,
            'line_height' => 1.2,
            'margins' => [
                'top' => 5,
                'bottom' => 5,
                'left' => 2,
                'right' => 2,
            ],
        ],

        'delivery_list' => [
            'page_size' => 'A4',
            'orientation' => 'portrait',
            'font_size' => 14,
            'line_height' => 1.4,
            'margins' => [
                'top' => 20,
                'bottom' => 20,
                'left' => 15,
                'right' => 15,
            ],
        ],

        'invoice' => [
            'page_size' => 'A4',
            'orientation' => 'portrait',
            'font_size' => 12,
            'line_height' => 1.3,
            'margins' => [
                'top' => 25,
                'bottom' => 25,
                'left' => 20,
                'right' => 20,
            ],
        ],

        'receipt' => [
            'width' => 58, // 小票宽度(mm)
            'font_size' => 12,
            'line_height' => 1.2,
        ],

        'label' => [
            'width' => 100,
            'height' => 150,
            'font_size' => 10,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 调试模式
    |--------------------------------------------------------------------------
    |
    | 开启调试模式时，会记录详细的打印日志
    |
    */
    'debug' => env('PRINTING_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | 打印队列
    |--------------------------------------------------------------------------
    |
    | 是否启用打印队列，大批量打印时建议启用
    |
    */
    'queue' => [
        'enabled' => env('PRINTING_QUEUE_ENABLED', false),
        'connection' => env('PRINTING_QUEUE_CONNECTION', 'default'),
        'queue' => env('PRINTING_QUEUE_NAME', 'printing'),
    ],

    /*
    |--------------------------------------------------------------------------
    | 飞蛾云打印配置说明
    |--------------------------------------------------------------------------
    |
    | 1. 注册飞蛾云账户并实名认证: https://www.feieyun.com
    | 2. 获取UKEY: 登录后台 -> 用户中心 -> 开发者信息
    | 3. 购买打印机并获取打印机编号(SN)
    | 4. 在后台添加打印机或通过API添加
    | 5. 配置环境变量:
    |    FLYCLOUD_USER=你的飞蛾云账号
    |    FLYCLOUD_UKEY=你的UKEY
    |    FLYCLOUD_DEFAULT_PRINTER=打印机编号
    |
    | 支持的小票格式控制标签:
    | <BR> - 换行符
    | <CUT> - 切刀指令(主动切纸)
    | <LOGO> - 打印LOGO指令
    | <C>文本</C> - 居中对齐
    | <L>文本</L> - 左对齐
    | <R>文本</R> - 右对齐
    | <B>文本</B> - 粗体
    | <QR>二维码内容</QR> - 二维码
    |
    */
]; 