<template>
	<view class="browse-analysis-container">
		<!-- 页面头部 -->
		<view class="header-section">
			<view class="header-title">
				<text class="title-text">浏览分析</text>
				<text class="subtitle-text">用户页面访问行为分析</text>
			</view>
		</view>

		<!-- 概览指标 -->
		<view class="overview-section">
			<view class="overview-grid">
				<view class="overview-item">
					<text class="overview-number">{{ formatNumber(browseData.total_page_views) }}</text>
					<text class="overview-label">总页面浏览</text>
				</view>
				<view class="overview-item">
					<text class="overview-number">{{ formatNumber(browseData.unique_visitors) }}</text>
					<text class="overview-label">独立访客</text>
				</view>
				<view class="overview-item">
					<text class="overview-number">{{ browseData.avg_pages_per_session }}</text>
					<text class="overview-label">平均页面/会话</text>
				</view>
				<view class="overview-item">
					<text class="overview-number">{{ browseData.bounce_rate }}%</text>
					<text class="overview-label">跳出率</text>
				</view>
			</view>
		</view>

		<!-- 热门页面 -->
		<view class="popular-pages">
			<view class="section-title">
				<text>🔥 热门页面</text>
			</view>
			<view class="page-list">
				<view class="page-item" v-for="(page, index) in browseData.popular_pages" :key="index">
					<view class="page-rank">{{ index + 1 }}</view>
					<view class="page-info">
						<text class="page-title">{{ page.title }}</text>
						<text class="page-url">{{ page.url }}</text>
					</view>
					<view class="page-stats">
						<text class="page-views">{{ formatNumber(page.views) }}次</text>
						<text class="page-time">{{ formatDuration(page.avg_time) }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 访问路径 -->
		<view class="user-flow">
			<view class="section-title">
				<text>🔄 用户访问路径</text>
			</view>
			<view class="flow-chart">
				<view class="flow-item" v-for="(step, index) in browseData.user_flow" :key="index">
					<view class="step-number">{{ index + 1 }}</view>
					<view class="step-content">
						<text class="step-page">{{ step.page }}</text>
						<text class="step-users">{{ step.users }}人访问</text>
					</view>
					<view class="flow-arrow" v-if="index < browseData.user_flow.length - 1">→</view>
				</view>
			</view>
		</view>

		<!-- 设备分析 -->
		<view class="device-analysis">
			<view class="section-title">
				<text>📱 设备分析</text>
			</view>
			<view class="device-stats">
				<view class="device-item" v-for="device in browseData.device_stats" :key="device.type">
					<text class="device-type">{{ device.type }}</text>
					<view class="device-bar">
						<view class="bar-fill" :style="{ width: device.percentage + '%' }"></view>
					</view>
					<text class="device-percentage">{{ device.percentage }}%</text>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
import analyticsApi from '../../api/analytics.js'

export default {
	data() {
		return {
			browseData: {
				total_page_views: 0,
				unique_visitors: 0,
				avg_pages_per_session: 0,
				bounce_rate: 0,
				popular_pages: [],
				user_flow: [],
				device_stats: []
			},
			loading: false
		}
	},
	
	onLoad() {
		this.loadBrowseAnalysis()
	},
	
	onPullDownRefresh() {
		this.refreshData()
	},
	
	methods: {
		// 加载浏览分析数据
		async loadBrowseAnalysis() {
			this.loading = true
			try {
				const response = await analyticsApi.getBrowseAnalysis()
				this.browseData = response.data || {}
			} catch (error) {
				console.error('加载浏览分析数据失败:', error)
				uni.showToast({
					title: '加载数据失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 刷新数据
		async refreshData() {
			await this.loadBrowseAnalysis()
			uni.stopPullDownRefresh()
		},
		
		// 格式化数字
		formatNumber(num) {
			if (!num) return '0'
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + 'w'
			}
			if (num >= 1000) {
				return (num / 1000).toFixed(1) + 'k'
			}
			return num.toString()
		},
		
		// 格式化时长
		formatDuration(seconds) {
			if (!seconds) return '0s'
			if (seconds >= 60) {
				return Math.round(seconds / 60) + 'm'
			}
			return Math.round(seconds) + 's'
		}
	}
}
</script>

<style scoped>
.browse-analysis-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 页面头部 */
.header-section {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx 32rpx 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	color: #ffffff;
}

.header-title {
	flex: 1;
}

.title-text {
	font-size: 40rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
}

.subtitle-text {
	font-size: 28rpx;
	opacity: 0.8;
}

/* 概览指标 */
.overview-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.overview-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 24rpx;
}

.overview-item {
	text-align: center;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.overview-number {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #007AFF;
	margin-bottom: 8rpx;
}

.overview-label {
	font-size: 24rpx;
	color: #666666;
}

/* 热门页面 */
.popular-pages {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 24rpx;
}

.page-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.page-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.page-rank {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #007AFF;
	color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: 600;
	margin-right: 20rpx;
}

.page-info {
	flex: 1;
}

.page-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.page-url {
	font-size: 24rpx;
	color: #666666;
}

.page-stats {
	text-align: right;
}

.page-views {
	font-size: 28rpx;
	font-weight: 600;
	color: #007AFF;
	margin-bottom: 4rpx;
}

.page-time {
	font-size: 24rpx;
	color: #666666;
}

/* 访问路径 */
.user-flow {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.flow-chart {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.flow-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.step-number {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	background: #28a745;
	color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: 600;
	margin-right: 20rpx;
}

.step-content {
	flex: 1;
}

.step-page {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.step-users {
	font-size: 24rpx;
	color: #666666;
}

.flow-arrow {
	font-size: 32rpx;
	color: #cccccc;
	margin-left: 20rpx;
}

/* 设备分析 */
.device-analysis {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.device-stats {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.device-item {
	display: flex;
	align-items: center;
	padding: 16rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.device-type {
	width: 120rpx;
	font-size: 28rpx;
	color: #333333;
}

.device-bar {
	flex: 1;
	height: 16rpx;
	background: #e9ecef;
	border-radius: 8rpx;
	margin: 0 16rpx;
	overflow: hidden;
}

.bar-fill {
	height: 100%;
	background: linear-gradient(90deg, #007AFF 0%, #5856D6 100%);
	border-radius: 8rpx;
}

.device-percentage {
	font-size: 24rpx;
	color: #666666;
	min-width: 60rpx;
	text-align: right;
}

/* 加载状态 */
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}
</style> 