<?php

namespace App\Points\Listeners;

use App\Points\Services\PointsService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class OrderCompletedListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected PointsService $pointsService;

    /**
     * Create the event listener.
     */
    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * Handle the event.
     */
    public function handle($event): void
    {
        try {
            // 获取订单信息
            $order = $event->order;
            
            // 准备订单项数据
            $orderItems = $order->items->map(function ($item) {
                return [
                    'product_id' => $item->product_id,
                    'quantity' => $item->quantity,
                    'price' => $item->unit_price,
                ];
            })->toArray();

            // 给予积分奖励
            $totalPoints = $this->pointsService->awardPointsForOrder(
                $order->user_id,
                $order->id,
                $orderItems
            );

            if ($totalPoints > 0) {
                Log::info('订单完成积分奖励成功', [
                    'order_id' => $order->id,
                    'user_id' => $order->user_id,
                    'total_points' => $totalPoints
                ]);
            }

        } catch (\Exception $e) {
            Log::error('订单完成积分奖励失败', [
                'order_id' => $order->id ?? null,
                'user_id' => $order->user_id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed($event, $exception): void
    {
        Log::error('订单完成积分奖励监听器失败', [
            'order_id' => $event->order->id ?? null,
            'exception' => $exception->getMessage()
        ]);
    }
} 