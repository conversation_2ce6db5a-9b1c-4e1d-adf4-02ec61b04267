<?php

namespace App\Points\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PointsRule extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'rule_type',
        'points_amount',
        'conditions',
        'max_times_per_day',
        'max_times_total',
        'status',
        'valid_from',
        'valid_to',
        'description',
    ];

    protected $casts = [
        'points_amount' => 'integer',
        'conditions' => 'array',
        'max_times_per_day' => 'integer',
        'max_times_total' => 'integer',
        'status' => 'boolean',
        'valid_from' => 'datetime',
        'valid_to' => 'datetime',
    ];

    /**
     * 积分规则类型常量
     */
    const RULE_TYPE_ORDER_COMPLETE = 'order_complete';      // 订单完成
    const RULE_TYPE_SIGNIN = 'signin';                      // 每日签到
    const RULE_TYPE_INVITE = 'invite';                      // 邀请好友
    const RULE_TYPE_REVIEW = 'review';                      // 商品评价
    const RULE_TYPE_SHARE = 'share';                        // 分享商品
    const RULE_TYPE_BIRTHDAY = 'birthday';                  // 生日奖励
    const RULE_TYPE_REGISTER = 'register';                  // 注册奖励
    const RULE_TYPE_FIRST_ORDER = 'first_order';            // 首次下单
    const RULE_TYPE_LEVEL_UPGRADE = 'level_upgrade';        // 会员升级

    /**
     * 检查规则是否有效
     */
    public function isValid(): bool
    {
        if (!$this->status) {
            return false;
        }

        $now = now();
        
        if ($this->valid_from && $now->lt($this->valid_from)) {
            return false;
        }
        
        if ($this->valid_to && $now->gt($this->valid_to)) {
            return false;
        }

        return true;
    }

    /**
     * 获取规则类型文本
     */
    public function getRuleTypeTextAttribute(): string
    {
        return match($this->rule_type) {
            self::RULE_TYPE_ORDER_COMPLETE => '订单完成',
            self::RULE_TYPE_SIGNIN => '每日签到',
            self::RULE_TYPE_INVITE => '邀请好友',
            self::RULE_TYPE_REVIEW => '商品评价',
            self::RULE_TYPE_SHARE => '分享商品',
            self::RULE_TYPE_BIRTHDAY => '生日奖励',
            self::RULE_TYPE_REGISTER => '注册奖励',
            self::RULE_TYPE_FIRST_ORDER => '首次下单',
            self::RULE_TYPE_LEVEL_UPGRADE => '会员升级',
            default => '未知类型'
        };
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return $this->status ? '启用' : '禁用';
    }

    /**
     * 检查用户是否满足条件
     */
    public function checkConditions(array $data): bool
    {
        if (empty($this->conditions)) {
            return true;
        }

        foreach ($this->conditions as $key => $condition) {
            if (!$this->checkSingleCondition($key, $condition, $data)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查单个条件
     */
    private function checkSingleCondition(string $key, $condition, array $data): bool
    {
        $value = data_get($data, $key);

        if (is_array($condition)) {
            $operator = $condition['operator'] ?? '=';
            $expectedValue = $condition['value'] ?? null;

            return match($operator) {
                '=' => $value == $expectedValue,
                '!=' => $value != $expectedValue,
                '>' => $value > $expectedValue,
                '>=' => $value >= $expectedValue,
                '<' => $value < $expectedValue,
                '<=' => $value <= $expectedValue,
                'in' => in_array($value, (array)$expectedValue),
                'not_in' => !in_array($value, (array)$expectedValue),
                default => false
            };
        }

        return $value == $condition;
    }

    /**
     * 计算积分奖励
     */
    public function calculatePoints(array $data): int
    {
        $points = $this->points_amount;

        // 如果是订单完成，可能需要根据订单金额计算积分
        if ($this->rule_type === self::RULE_TYPE_ORDER_COMPLETE) {
            $orderAmount = data_get($data, 'order_amount', 0);
            $pointsRate = data_get($this->conditions, 'points_rate', 0);
            
            if ($pointsRate > 0) {
                $points = intval($orderAmount * $pointsRate);
            }
        }

        return max(0, $points);
    }

    /**
     * 检查用户今日是否已达到限制
     */
    public function checkDailyLimit(int $userId): bool
    {
        if (!$this->max_times_per_day) {
            return true;
        }

        $todayCount = PointsTransaction::where('user_id', $userId)
            ->where('source', $this->getTransactionSource())
            ->whereDate('created_at', today())
            ->count();

        return $todayCount < $this->max_times_per_day;
    }

    /**
     * 检查用户总次数是否已达到限制
     */
    public function checkTotalLimit(int $userId): bool
    {
        if (!$this->max_times_total) {
            return true;
        }

        $totalCount = PointsTransaction::where('user_id', $userId)
            ->where('source', $this->getTransactionSource())
            ->count();

        return $totalCount < $this->max_times_total;
    }

    /**
     * 获取对应的积分流水来源
     */
    private function getTransactionSource(): string
    {
        return match($this->rule_type) {
            self::RULE_TYPE_ORDER_COMPLETE => PointsTransaction::SOURCE_ORDER,
            self::RULE_TYPE_SIGNIN => PointsTransaction::SOURCE_SIGNIN,
            self::RULE_TYPE_INVITE => PointsTransaction::SOURCE_INVITE,
            self::RULE_TYPE_REVIEW => PointsTransaction::SOURCE_REVIEW,
            self::RULE_TYPE_SHARE => PointsTransaction::SOURCE_SHARE,
            self::RULE_TYPE_BIRTHDAY => PointsTransaction::SOURCE_BIRTHDAY,
            self::RULE_TYPE_LEVEL_UPGRADE => PointsTransaction::SOURCE_UPGRADE,
            default => PointsTransaction::SOURCE_ADMIN
        };
    }

    /**
     * 作用域：有效规则
     */
    public function scopeValid($query)
    {
        $now = now();
        
        return $query->where('status', true)
            ->where(function ($q) use ($now) {
                $q->whereNull('valid_from')->orWhere('valid_from', '<=', $now);
            })
            ->where(function ($q) use ($now) {
                $q->whereNull('valid_to')->orWhere('valid_to', '>=', $now);
            });
    }

    /**
     * 作用域：按类型筛选
     */
    public function scopeByType($query, string $ruleType)
    {
        return $query->where('rule_type', $ruleType);
    }

    /**
     * 获取所有规则类型选项
     */
    public static function getRuleTypeOptions(): array
    {
        return [
            self::RULE_TYPE_ORDER_COMPLETE => '订单完成',
            self::RULE_TYPE_SIGNIN => '每日签到',
            self::RULE_TYPE_INVITE => '邀请好友',
            self::RULE_TYPE_REVIEW => '商品评价',
            self::RULE_TYPE_SHARE => '分享商品',
            self::RULE_TYPE_BIRTHDAY => '生日奖励',
            self::RULE_TYPE_REGISTER => '注册奖励',
            self::RULE_TYPE_FIRST_ORDER => '首次下单',
            self::RULE_TYPE_LEVEL_UPGRADE => '会员升级',
        ];
    }
} 