<?php

use App\Warehouse\Http\Controllers\WarehouseController;
use App\Warehouse\Http\Controllers\InventoryController;
use App\Warehouse\Http\Controllers\InventorySystemController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 仓库模块 API 路由
|--------------------------------------------------------------------------
|
| 这里定义仓库管理模块的所有API路由
|
*/

// 注意：为避免路由冲突，这里使用warehouse前缀，而不是warehouses
// inventory/warehouses 路由已在 app/Inventory/routes/api.php 中定义

// 公共API路由，不需要认证
Route::prefix('inventory-system')->group(function () {
    Route::get('/transaction-types', [InventorySystemController::class, 'getTransactionTypes']);
});

// 需要认证的API路由
Route::group(['middleware' => ['auth:sanctum']], function () {
    // 仓库管理相关
    Route::prefix('warehouse')->group(function () {
        Route::get('/', [WarehouseController::class, 'index']);
        Route::post('/', [WarehouseController::class, 'create']);
        Route::get('/{id}', [WarehouseController::class, 'show']);
        Route::put('/{id}', [WarehouseController::class, 'update']);
        Route::delete('/{id}', [WarehouseController::class, 'destroy']);
        Route::put('/{warehouseId}/product/{productId}/stock', [WarehouseController::class, 'updateStock']);
        
        // 库存管理相关
        Route::get('/{warehouseId}/inventory', [InventoryController::class, 'index']);
        Route::get('/{warehouseId}/inventory/{productId}', [InventoryController::class, 'show']);
        Route::put('/{warehouseId}/inventory/{productId}', [InventoryController::class, 'updateStock']);
        Route::post('/{warehouseId}/inventory', [InventoryController::class, 'addProduct']);
        Route::delete('/{warehouseId}/inventory/{productId}', [InventoryController::class, 'removeProduct']);
    });
    
    // 库存系统管理
    Route::prefix('inventory-system')->group(function () {
        Route::get('/transactions', [InventorySystemController::class, 'getTransactions']);
        Route::post('/transactions', [InventorySystemController::class, 'createTransaction']);
        Route::get('/transactions/{id}', [InventorySystemController::class, 'getTransaction']);
        Route::post('/adjust-stock', [InventorySystemController::class, 'adjustStock']);
        Route::post('/transfer-stock', [InventorySystemController::class, 'transferStock']);
    });
}); 