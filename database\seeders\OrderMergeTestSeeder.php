<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Order\Models\Order;
use App\Order\Models\OrderItem;
use App\Product\Models\Product;
use App\Crm\Models\UserAddress;
use App\Unit\Models\Unit;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class OrderMergeTestSeeder extends Seeder
{
    /**
     * 运行数据填充
     */
    public function run()
    {
        $this->command->info('开始创建订单合并测试数据...');
        
        DB::transaction(function () {
            // 1. 创建测试用户
            $users = $this->createTestUsers();
            
            // 2. 创建测试商品
            $products = $this->createTestProducts();
            
            // 3. 创建测试地址
            $addresses = $this->createTestAddresses($users);
            
            // 4. 创建可合并的测试订单
            $this->createMergeableOrders($users, $products, $addresses);
            
            // 5. 创建一些不可合并的订单（不同状态、不同支付方式等）
            $this->createNonMergeableOrders($users, $products, $addresses);
        });
        
        $this->command->info('订单合并测试数据创建完成！');
        $this->command->info('');
        $this->command->info('测试场景说明：');
        $this->command->info('1. 张三有3个待付款的微信支付订单，可以合并');
        $this->command->info('2. 李四有2个待付款的货到付款订单，同一区域，可以合并');
        $this->command->info('3. 王五有2个待付款订单，但支付方式不同，不可合并');
        $this->command->info('4. 赵六有1个已付款订单和1个待付款订单，不可合并');
    }
    
    /**
     * 创建测试用户
     */
    private function createTestUsers()
    {
        $users = [];
        
        $testUsers = [
            ['name' => '张三', 'phone' => '13800138001', 'merchant_name' => '张三的店铺'],
            ['name' => '李四', 'phone' => '13800138002', 'merchant_name' => '李四商贸'],
            ['name' => '王五', 'phone' => '13800138003', 'merchant_name' => '王五超市'],
            ['name' => '赵六', 'phone' => '13800138004', 'merchant_name' => '赵六便利店'],
        ];
        
        foreach ($testUsers as $userData) {
            $user = User::firstOrCreate(
                ['phone' => $userData['phone']],
                [
                    'name' => $userData['name'],
                    'merchant_name' => $userData['merchant_name'],
                    'password' => bcrypt('123456'),
                    'email' => strtolower($userData['name']) . '@test.com',
                    'region_id' => 89, // 使用实际存在的区域ID
                ]
            );
            $users[] = $user;
        }
        
        return $users;
    }
    
    /**
     * 创建测试商品
     */
    private function createTestProducts()
    {
        $products = [];
        
        // 获取或创建基础单位
        $unit = Unit::firstOrCreate(
            ['name' => '个'],
            [
                'symbol' => '个', 
                'description' => '基础计量单位',
                'display_name' => '个'
            ]
        );
        
        $testProducts = [
            ['name' => '苹果', 'price' => 5.00, 'stock' => 1000],
            ['name' => '香蕉', 'price' => 3.50, 'stock' => 800],
            ['name' => '橙子', 'price' => 4.20, 'stock' => 600],
            ['name' => '牛奶', 'price' => 12.80, 'stock' => 500],
            ['name' => '面包', 'price' => 8.50, 'stock' => 300],
        ];
        
        foreach ($testProducts as $productData) {
            $product = Product::firstOrCreate(
                ['name' => $productData['name']],
                [
                    'price' => $productData['price'],
                    'stock' => $productData['stock'],
                    'status' => 1, // 1 = 启用状态
                    'base_unit_id' => $unit->id,
                    'description' => '测试商品 - ' . $productData['name'],
                ]
            );
            $products[] = $product;
        }
        
        return $products;
    }
    
    /**
     * 创建测试地址
     */
    private function createTestAddresses($users)
    {
        $addresses = [];
        
        foreach ($users as $user) {
            $address = UserAddress::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'contact_name' => $user->name,
                ],
                [
                    'contact_phone' => $user->phone,
                    'province' => '广东省',
                    'city' => '深圳市',
                    'district' => '南山区',
                    'address' => '科技园' . $user->name . '大厦',
                    'is_default' => true,
                ]
            );
            $addresses[$user->id] = $address;
        }
        
        return $addresses;
    }
    
    /**
     * 创建可合并的测试订单
     */
    private function createMergeableOrders($users, $products, $addresses)
    {
        $today = Carbon::today();
        
        // 场景1：张三的3个微信支付订单（可合并）
        $user1 = $users[0]; // 张三
        for ($i = 1; $i <= 3; $i++) {
            $order = Order::create([
                'order_no' => 'TEST' . $today->format('Ymd') . sprintf('%03d', $i),
                'user_id' => $user1->id,
                'user_address_id' => $addresses[$user1->id]->id,
                'status' => 'pending',
                'payment_method' => 'wechat',
                'total' => 0, // 稍后计算
                'subtotal' => 0,
                'contact_name' => $user1->name,
                'contact_phone' => $user1->phone,
                'shipping_address' => $addresses[$user1->id]->address,
                'notes' => "张三的第{$i}个测试订单",
                'created_at' => $today->addMinutes($i * 10),
                'region_id' => 89,
            ]);
            
            // 添加订单商品
            $this->addOrderItems($order, $products, $i);
        }
        
        // 场景2：李四的2个货到付款订单（可合并）
        $user2 = $users[1]; // 李四
        for ($i = 1; $i <= 2; $i++) {
            $order = Order::create([
                'order_no' => 'TEST' . $today->format('Ymd') . sprintf('%03d', $i + 10),
                'user_id' => $user2->id,
                'user_address_id' => $addresses[$user2->id]->id,
                'status' => 'pending',
                'payment_method' => 'cod',
                'is_cod' => true,
                'cod_status' => 'unpaid',
                'total' => 0, // 稍后计算
                'subtotal' => 0,
                'contact_name' => $user2->name,
                'contact_phone' => $user2->phone,
                'shipping_address' => $addresses[$user2->id]->address,
                'notes' => "李四的第{$i}个货到付款订单",
                'created_at' => $today->addMinutes(($i + 10) * 10),
                'region_id' => 89, // 同一区域
            ]);
            
            // 添加订单商品
            $this->addOrderItems($order, $products, $i + 2);
        }
    }
    
    /**
     * 创建不可合并的测试订单
     */
    private function createNonMergeableOrders($users, $products, $addresses)
    {
        $today = Carbon::today();
        
        // 场景3：王五的2个订单，支付方式不同（不可合并）
        $user3 = $users[2]; // 王五
        
        // 微信支付订单
        $order1 = Order::create([
            'order_no' => 'TEST' . $today->format('Ymd') . '020',
            'user_id' => $user3->id,
            'user_address_id' => $addresses[$user3->id]->id,
            'status' => 'pending',
            'payment_method' => 'wechat',
            'total' => 0,
            'subtotal' => 0,
            'contact_name' => $user3->name,
            'contact_phone' => $user3->phone,
            'shipping_address' => $addresses[$user3->id]->address,
            'notes' => '王五的微信支付订单',
            'created_at' => $today->addMinutes(200),
            'region_id' => 89,
        ]);
        $this->addOrderItems($order1, $products, 1);
        
        // 支付宝订单
        $order2 = Order::create([
            'order_no' => 'TEST' . $today->format('Ymd') . '021',
            'user_id' => $user3->id,
            'user_address_id' => $addresses[$user3->id]->id,
            'status' => 'pending',
            'payment_method' => 'alipay',
            'total' => 0,
            'subtotal' => 0,
            'contact_name' => $user3->name,
            'contact_phone' => $user3->phone,
            'shipping_address' => $addresses[$user3->id]->address,
            'notes' => '王五的支付宝订单',
            'created_at' => $today->addMinutes(210),
            'region_id' => 89,
        ]);
        $this->addOrderItems($order2, $products, 2);
        
        // 场景4：赵六的订单，状态不同（不可合并）
        $user4 = $users[3]; // 赵六
        
        // 已付款订单
        $order3 = Order::create([
            'order_no' => 'TEST' . $today->format('Ymd') . '030',
            'user_id' => $user4->id,
            'user_address_id' => $addresses[$user4->id]->id,
            'status' => 'paid',
            'payment_method' => 'wechat',
            'total' => 0,
            'subtotal' => 0,
            'contact_name' => $user4->name,
            'contact_phone' => $user4->phone,
            'shipping_address' => $addresses[$user4->id]->address,
            'notes' => '赵六的已付款订单',
            'paid_at' => $today->addMinutes(300),
            'created_at' => $today->addMinutes(300),
            'region_id' => 89,
        ]);
        $this->addOrderItems($order3, $products, 1);
        
        // 待付款订单
        $order4 = Order::create([
            'order_no' => 'TEST' . $today->format('Ymd') . '031',
            'user_id' => $user4->id,
            'user_address_id' => $addresses[$user4->id]->id,
            'status' => 'pending',
            'payment_method' => 'wechat',
            'total' => 0,
            'subtotal' => 0,
            'contact_name' => $user4->name,
            'contact_phone' => $user4->phone,
            'shipping_address' => $addresses[$user4->id]->address,
            'notes' => '赵六的待付款订单',
            'created_at' => $today->addMinutes(310),
            'region_id' => 89,
        ]);
        $this->addOrderItems($order4, $products, 2);
    }
    
    /**
     * 为订单添加商品
     */
    private function addOrderItems($order, $products, $seed)
    {
        $total = 0;
        $subtotal = 0;
        
        // 根据seed选择不同的商品组合
        $itemsToAdd = [
            1 => [['product' => $products[0], 'quantity' => 10], ['product' => $products[1], 'quantity' => 5]],
            2 => [['product' => $products[1], 'quantity' => 8], ['product' => $products[2], 'quantity' => 6]],
            3 => [['product' => $products[2], 'quantity' => 12], ['product' => $products[3], 'quantity' => 3]],
            4 => [['product' => $products[3], 'quantity' => 4], ['product' => $products[4], 'quantity' => 7]],
            5 => [['product' => $products[0], 'quantity' => 15], ['product' => $products[4], 'quantity' => 2]],
        ];
        
        $items = $itemsToAdd[$seed] ?? $itemsToAdd[1];
        
        foreach ($items as $itemData) {
            $product = $itemData['product'];
            $quantity = $itemData['quantity'];
            $price = $product->price;
            $itemTotal = $price * $quantity;
            
            OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $product->id,
                'product_name' => $product->name,
                'quantity' => $quantity,
                'price' => $price,
                'total' => $itemTotal,
                'unit_id' => $product->base_unit_id,
            ]);
            
            $total += $itemTotal;
            $subtotal += $itemTotal;
        }
        
        // 更新订单总金额
        $order->update([
            'total' => $total,
            'subtotal' => $subtotal,
        ]);
    }
} 