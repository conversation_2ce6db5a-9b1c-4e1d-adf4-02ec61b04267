<?php

namespace App\Product\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Product\Models\Category;
use App\Product\Services\CategoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PublicCategoryController extends Controller
{
    /**
     * 分类服务
     */
    protected $categoryService;
    
    /**
     * 构造函数
     */
    public function __construct(CategoryService $categoryService = null)
    {
        $this->categoryService = $categoryService ?: app(CategoryService::class);
    }

    /**
     * 获取商品分类 - 无需认证
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function categories()
    {
        Log::info('公开API - 获取分类列表');
        
        $categories = Category::where('status', 1)
                            ->orderBy('sort', 'asc')
                            ->get();
        
        return response()->json(ApiResponse::success($categories));
    }
    
    /**
     * 获取分类树 - 无需认证
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function categoryTree(Request $request)
    {
        Log::info('公开API - 获取分类树');
        
        $parentId = $request->input('parent_id', 0);
        
        // 使用新的Category模型的getTree方法
        $tree = Category::getTree($parentId, ['status' => 1]);
        
        return response()->json(ApiResponse::success($tree));
    }
    
    /**
     * 获取分类面包屑 - 无需认证
     *
     * @param int $id 分类ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function categoryBreadcrumb($id)
    {
        Log::info('公开API - 获取分类面包屑', ['category_id' => $id]);
        
        $category = Category::find($id);
        
        if (!$category) {
            return response()->json(ApiResponse::error('分类不存在', 404), 404);
        }
        
        $breadcrumb = $category->getBreadcrumb();
        
        return response()->json(ApiResponse::success($breadcrumb));
    }
    
    /**
     * 获取分类子分类 - 无需认证
     *
     * @param int $id 分类ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function categoryChildren($id)
    {
        Log::info('公开API - 获取分类子分类', ['category_id' => $id]);
        
        $category = Category::find($id);
        
        if (!$category) {
            return response()->json(ApiResponse::error('分类不存在', 404), 404);
        }
        
        $children = Category::where('parent_id', $id)
                           ->where('status', 1)
                           ->orderBy('sort', 'asc')
                           ->get();
        
        return response()->json(ApiResponse::success($children));
    }
} 