<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TabBar图标快速生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 2px solid #eee;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon-preview {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .icon-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .download-btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .instructions ol {
            margin: 10px 0;
        }
        .instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 TabBar图标快速生成器</h1>
        
        <div class="icon-grid" id="iconGrid">
            <!-- 图标将通过JavaScript生成 -->
        </div>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>点击下方的下载按钮保存图标文件</li>
                <li>将下载的PNG文件放到 <code>static/tabbar/</code> 目录下</li>
                <li>重新启动UniApp项目</li>
                <li>即可看到底部导航栏显示新图标</li>
            </ol>
            <p><strong>注意：</strong>每个图标都有普通状态和选中状态两个版本</p>
        </div>
    </div>

    <script>
        // 图标配置
        const icons = [
            { name: 'home', emoji: '🏠', label: '首页' },
            { name: 'analytics', emoji: '📊', label: '行为分析' },
            { name: 'client', emoji: '👥', label: '客户' },
            { name: 'orders', emoji: '📋', label: '订单' },
            { name: 'profile', emoji: '👤', label: '我的' }
        ];

        // 创建图标
        function createIcon(emoji, bgColor, size = 81) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // 绘制背景
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, size, size);
            
            // 绘制emoji
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = 'white';
            ctx.fillText(emoji, size / 2, size / 2);
            
            return canvas;
        }

        // 下载图标
        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }

        // 生成所有图标
        function generateIcons() {
            const iconGrid = document.getElementById('iconGrid');
            
            icons.forEach(icon => {
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                
                // 创建普通状态和选中状态的图标
                const normalCanvas = createIcon(icon.emoji, '#999999');
                const activeCanvas = createIcon(icon.emoji, '#007AFF');
                
                iconItem.innerHTML = `
                    <div class="icon-label">${icon.label}</div>
                    <div class="icon-preview">
                        <div>
                            <div>普通状态</div>
                            <canvas width="81" height="81"></canvas>
                        </div>
                        <div>
                            <div>选中状态</div>
                            <canvas width="81" height="81"></canvas>
                        </div>
                    </div>
                    <div>
                        <button class="download-btn" onclick="downloadIcon(this.parentElement.parentElement.querySelector('canvas:nth-of-type(1)'), '${icon.name}.png')">
                            下载普通状态
                        </button>
                        <button class="download-btn" onclick="downloadIcon(this.parentElement.parentElement.querySelector('canvas:nth-of-type(2)'), '${icon.name}-active.png')">
                            下载选中状态
                        </button>
                    </div>
                `;
                
                iconGrid.appendChild(iconItem);
                
                // 将生成的图标绘制到页面上的canvas
                const canvases = iconItem.querySelectorAll('canvas');
                const normalCtx = canvases[0].getContext('2d');
                const activeCtx = canvases[1].getContext('2d');
                
                normalCtx.drawImage(normalCanvas, 0, 0);
                activeCtx.drawImage(activeCanvas, 0, 0);
                
                // 保存canvas引用以便下载
                canvases[0].sourceCanvas = normalCanvas;
                canvases[1].sourceCanvas = activeCanvas;
            });
        }

        // 修改下载函数
        function downloadIcon(displayCanvas, filename) {
            const sourceCanvas = displayCanvas.sourceCanvas;
            const link = document.createElement('a');
            link.download = filename;
            link.href = sourceCanvas.toDataURL();
            link.click();
        }

        // 页面加载完成后生成图标
        document.addEventListener('DOMContentLoaded', generateIcons);
    </script>
</body>
</html> 