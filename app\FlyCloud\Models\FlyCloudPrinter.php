<?php

namespace App\FlyCloud\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;

class FlyCloudPrinter extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'flycloud_printers';

    protected $fillable = [
        'sn',
        'name',
        'key',
        'location',
        'description',
        'is_active',
        'is_default',
        'status',
        'last_status_check',
        'settings',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'last_status_check' => 'datetime',
        'settings' => 'array'
    ];

    protected $dates = [
        'last_status_check',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * 获取创建用户
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取更新用户
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * 查询活跃的打印机
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 查询默认打印机
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * 查询在线打印机
     */
    public function scopeOnline($query)
    {
        return $query->where('status', 'online');
    }

    /**
     * 获取默认打印机
     */
    public static function getDefault()
    {
        return static::active()->default()->first();
    }

    /**
     * 设置为默认打印机
     */
    public function setAsDefault(): bool
    {
        // 清除其他打印机的默认状态
        static::where('is_default', true)->update(['is_default' => false]);
        
        // 设置当前打印机为默认
        return $this->update(['is_default' => true]);
    }

    /**
     * 检查打印机是否在线
     */
    public function isOnline(): bool
    {
        return $this->status === 'online';
    }

    /**
     * 更新打印机状态
     */
    public function updateStatus(string $status, string $message = ''): bool
    {
        return $this->update([
            'status' => $status,
            'last_status_check' => now(),
            'settings' => array_merge($this->settings ?? [], [
                'last_status_message' => $message
            ])
        ]);
    }

    /**
     * 获取打印机配置
     */
    public function getConfig(): array
    {
        return [
            'sn' => $this->sn,
            'key' => $this->key,
            'name' => $this->name,
            'location' => $this->location,
            'settings' => $this->settings ?? []
        ];
    }

    /**
     * 验证打印机密钥格式
     */
    public static function validateKey(string $key): bool
    {
        // 飞蛾云密钥通常是32位字符串
        return preg_match('/^[a-zA-Z0-9]{32}$/', $key);
    }

    /**
     * 验证打印机编号格式
     */
    public static function validateSn(string $sn): bool
    {
        // 飞蛾云打印机编号格式验证
        return preg_match('/^[A-Z0-9]{9,15}$/', $sn);
    }
} 