# 最小起购数量功能实现说明

## 功能概述

根据商品的最小起购数量（`min_sale_quantity`）字段，实现首次加购时自动触发最小起购数量的功能。确保用户购买的商品数量满足商家设定的最小销售要求。

## 数据库字段

```sql
`min_sale_quantity` decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '最小销售量（按销售单位计算）'
```

## 实现范围

### 1. 后端实现

#### 1.1 商品模型 (Product.php)
- 已包含 `min_sale_quantity` 字段
- 字段类型：`float`
- 默认值：1.00

#### 1.2 购物车服务 (CartService.php)
- **位置**: `app/Cart/Services/CartService.php`
- **功能**: 
  - 新增购物车项时检查最小起购数量
  - 如果数量不足，自动调整到最小起购数量
  - 更新购物车数量时，如果低于最小起购数量则删除商品
  - 记录调整和删除日志

```php
// 核心逻辑
$minSaleQuantity = $product->min_sale_quantity ?? 1;

if ($quantity < $minSaleQuantity) {
    $quantity = $minSaleQuantity;
    // 记录调整日志
}
```

#### 1.3 购物车控制器 (CartController.php)
- **位置**: `app/Cart/Http/Controllers/CartController.php`
- **功能**:
  - 返回调整后的数量信息
  - 处理商品删除的响应
  - 提供友好的调整和删除提示消息

### 2. 前端实现

#### 2.1 购物车统一管理器 (cart-unified.js)
- **位置**: `shop530/utils/cart-unified.js`
- **功能**:
  - 前端预检查最小起购数量
  - 处理后端返回的调整信息
  - 更新数量时检查最小起购数量，低于则删除
  - 显示调整和删除提示弹窗

```javascript
// 核心逻辑
const minSaleQuantity = product.min_sale_quantity || 1;

if (existingQuantity === 0 && quantity < minSaleQuantity) {
    quantity = minSaleQuantity;
    // 显示调整提示
}
```

#### 2.2 商品卡片组件 (product-card.js)
- **位置**: `shop530/components/product-card/product-card.js`
- **功能**:
  - 根据商品数据动态设置最小起购数量
  - 传递给数字键盘组件
  - 减少数量时检查最小起购数量，低于则删除商品

#### 2.3 数字键盘组件 (number-keyboard.js)
- **位置**: `shop530/components/number-keyboard/number-keyboard.js`
- **功能**:
  - 验证输入数量是否满足最小起购要求
  - 显示相应的错误提示

#### 2.4 商品详情页 (product-detail.js)
- **位置**: `shop530/pages/product-detail/product-detail.js`
- **功能**:
  - 设置默认数量为最小起购数量
  - 数量减少时检查最小起购限制

## 用户体验流程

### 场景1：首次加购（购物车中无该商品）

1. 用户点击加购按钮（默认数量1）
2. 系统检查商品的最小起购数量
3. 如果1 < 最小起购数量，自动调整数量
4. 显示调整提示："该商品最小起购量为X件，已为您调整数量"
5. 成功加入购物车

### 场景2：使用数字键盘加购

1. 用户长按加购按钮或点击数量
2. 显示数字键盘，默认数量为最小起购数量
3. 用户输入数量后点击确认
4. 如果数量 < 最小起购数量，显示错误提示
5. 用户需要输入满足要求的数量才能确认

### 场景3：商品详情页数量控制

1. 页面加载时，默认数量设置为最小起购数量
2. 用户点击减少按钮时，可以减少到1，但会提示最小起购数量
3. 显示提示："该商品最小起购量为X件，当前数量不足最小起购量"

### 场景5：购物车数量调整

1. 用户在购物车中调整商品数量
2. 当数量低于最小起购数量时，自动删除该商品
3. 显示提示："商品已移除 - [商品名] 的数量低于最小起购量X件，已从购物车中移除"

### 场景4：已有商品再次加购

1. 购物车中已有该商品
2. 用户再次加购时，不受最小起购数量限制
3. 可以加购任意数量（受库存限制）

## 技术特性

### 双重保障
- **前端预检查**: 提升用户体验，减少无效请求
- **后端强制检查**: 确保数据一致性和安全性

### 智能调整
- 自动调整不足的数量
- 友好的用户提示
- 详细的日志记录

### 兼容性
- 兼容现有的购物车功能
- 不影响已有商品的操作
- 支持不同单位的商品

## 测试方案

### 测试页面
- **位置**: `shop530/pages/test-min-quantity/`
- **功能**: 提供不同最小起购数量的测试商品
- **用法**: 在小程序中访问该页面进行功能测试

### 测试用例

1. **正常加购**: 数量 >= 最小起购数量
2. **数量不足**: 数量 < 最小起购数量，应自动调整
3. **数字键盘**: 输入不足数量，应显示错误提示
4. **详情页控制**: 减少到最小数量以下，应显示提示
5. **已有商品**: 购物车中已有商品，再次加购不受限制
6. **购物车减少**: 在购物车中减少数量到最小起购数量以下，应删除商品
7. **商品卡片减少**: 在商品卡片中减少数量到最小起购数量以下，应删除商品

## 配置说明

### 商品配置
在商品管理后台设置每个商品的最小起购数量：
- 默认值：1.00
- 单位：按销售单位计算
- 示例：最小起购5kg，则设置为5.00

### 前端配置
无需额外配置，功能自动生效。

## 日志监控

### 后端日志
```php
\Illuminate\Support\Facades\Log::info('购物车添加商品 - 自动调整到最小起购数量', [
    'product_id' => $productId,
    'product_name' => $product->name,
    'original_quantity' => $originalQuantity,
    'min_sale_quantity' => $minSaleQuantity,
    'adjusted_quantity' => $adjustedQuantity
]);
```

### 前端日志
```javascript
console.log('🔧 检查最小起购数量:', {
    product_id: product.id,
    product_name: product.name,
    min_sale_quantity: minSaleQuantity,
    requested_quantity: quantity
});
```

## 注意事项

1. **单位一致性**: 最小起购数量按销售单位计算
2. **库存检查**: 调整后的数量仍需满足库存限制
3. **用户体验**: 自动调整时提供清晰的提示信息
4. **性能考虑**: 前端预检查减少不必要的API调用
5. **兼容性**: 不影响现有购物车的其他功能

## 未来扩展

1. **批量设置**: 支持批量设置商品的最小起购数量
2. **动态调整**: 根据促销活动动态调整最小起购数量
3. **用户提醒**: 在商品列表中显示最小起购数量信息
4. **统计分析**: 统计最小起购数量对销售的影响 