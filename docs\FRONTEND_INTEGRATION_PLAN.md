# 前端API对接实施计划

## 对接策略概述

基于后端API支持检查结果，制定分阶段、有序的前端对接计划，确保稳步推进，降低风险。

## 对接顺序原则

1. **先公共后私有** - 优先对接无需认证的公共API
2. **先基础后复杂** - 从简单功能开始，逐步增加复杂度
3. **先核心后增值** - 优先保证核心业务流程
4. **先独立后依赖** - 优先对接独立功能，再处理相互依赖

## 第一阶段：基础设施搭建（第1周）

### 1.1 环境配置和工具类完善
**优先级：🔴 最高**

#### 任务清单：
- [x] ✅ 已完成：统一请求配置 (`utils/request-config.js`)
- [x] ✅ 已完成：请求工具类 (`utils/request.js`)
- [x] ✅ 已完成：API服务类 (`utils/api.js`)
- [ ] 🔄 测试和验证配置

#### 实施步骤：
```javascript
// 1. 验证请求配置
console.log('当前环境:', REQUEST_CONFIG.CURRENT_ENV);
console.log('API基础URL:', REQUEST_CONFIG.BASE_URL);

// 2. 测试基础请求功能
import { request } from '@/utils/request';
const testResponse = await request('/test', { method: 'GET' });

// 3. 验证错误处理
// 测试网络错误、超时、401等场景
```

### 1.2 公共API测试验证
**优先级：🔴 最高**

#### 任务清单：
- [ ] 测试轮播图API
- [ ] 测试商品分类API  
- [ ] 测试商品列表API
- [ ] 测试商品详情API
- [ ] 测试搜索API

#### 验证脚本：
```javascript
// 在小程序开发工具控制台执行
import { api } from '@/utils/api';

// 测试所有公共API
const testPublicAPIs = async () => {
  try {
    console.log('=== 开始测试公共API ===');
    
    // 1. 轮播图
    const banners = await api.getBanners();
    console.log('轮播图:', banners);
    
    // 2. 分类
    const categories = await api.getCategories();
    console.log('分类:', categories);
    
    // 3. 商品列表
    const products = await api.getProducts({ page: 1, per_page: 10 });
    console.log('商品列表:', products);
    
    // 4. 商品详情
    if (products.data && products.data.length > 0) {
      const detail = await api.getProductDetail(products.data[0].id);
      console.log('商品详情:', detail);
    }
    
    // 5. 搜索
    const searchResult = await api.searchProducts('测试', 1, 5);
    console.log('搜索结果:', searchResult);
    
    console.log('=== 公共API测试完成 ===');
  } catch (error) {
    console.error('API测试失败:', error);
  }
};

testPublicAPIs();
```

## 第二阶段：公共页面对接（第2周）

### 2.1 首页对接
**优先级：🔴 最高**

#### 对接内容：
- [ ] 轮播图展示
- [ ] 商品分类导航
- [ ] 热门商品列表
- [ ] 搜索功能

#### 实施步骤：
```javascript
// pages/index/index.js
Page({
  data: {
    banners: [],
    categories: [],
    hotProducts: [],
    loading: true
  },

  async onLoad() {
    await this.loadPageData();
  },

  async loadPageData() {
    try {
      wx.showLoading({ title: '加载中...' });
      
      // 并行加载数据
      const [bannersRes, categoriesRes, hotProductsRes] = await Promise.all([
        api.getBanners(),
        api.getCategories(),
        api.getHotProducts(10)
      ]);
      
      this.setData({
        banners: bannersRes.data || [],
        categories: categoriesRes.data || [],
        hotProducts: hotProductsRes.data || [],
        loading: false
      });
      
    } catch (error) {
      console.error('首页数据加载失败:', error);
      wx.showToast({ title: '加载失败', icon: 'error' });
    } finally {
      wx.hideLoading();
    }
  },

  // 搜索功能
  onSearchTap() {
    wx.navigateTo({ url: '/pages/search/search' });
  }
});
```

### 2.2 商品分类页对接
**优先级：🟡 高**

#### 对接内容：
- [ ] 分类树展示
- [ ] 分类商品列表
- [ ] 分类筛选

#### 实施步骤：
```javascript
// pages/category/category.js
Page({
  data: {
    categories: [],
    currentCategory: null,
    products: [],
    loading: false
  },

  async onLoad() {
    await this.loadCategories();
  },

  async loadCategories() {
    try {
      const res = await api.getCategories();
      this.setData({ 
        categories: res.data || [],
        currentCategory: res.data?.[0] || null
      });
      
      if (this.data.currentCategory) {
        await this.loadCategoryProducts(this.data.currentCategory.id);
      }
    } catch (error) {
      console.error('分类加载失败:', error);
    }
  },

  async loadCategoryProducts(categoryId) {
    try {
      this.setData({ loading: true });
      const res = await api.getProducts({ category_id: categoryId });
      this.setData({ 
        products: res.data || [],
        loading: false 
      });
    } catch (error) {
      console.error('分类商品加载失败:', error);
      this.setData({ loading: false });
    }
  }
});
```

### 2.3 商品详情页对接
**优先级：🟡 高**

#### 对接内容：
- [ ] 商品基本信息
- [ ] 商品图片轮播
- [ ] 商品规格选择
- [ ] 加入购物车（需要登录）

#### 实施步骤：
```javascript
// pages/product/detail.js
Page({
  data: {
    product: null,
    loading: true
  },

  async onLoad(options) {
    const { id } = options;
    if (id) {
      await this.loadProductDetail(id);
    }
  },

  async loadProductDetail(productId) {
    try {
      wx.showLoading({ title: '加载中...' });
      const res = await api.getProductDetail(productId);
      this.setData({ 
        product: res.data,
        loading: false 
      });
    } catch (error) {
      console.error('商品详情加载失败:', error);
      wx.showToast({ title: '加载失败', icon: 'error' });
    } finally {
      wx.hideLoading();
    }
  },

  // 加入购物车（暂时跳转到登录）
  onAddToCart() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({ url: '/pages/login/login' });
          }
        }
      });
      return;
    }
    
    // TODO: 实现加入购物车逻辑
    wx.showToast({ title: '功能开发中', icon: 'none' });
  }
});
```

### 2.4 搜索页对接
**优先级：🟢 中**

#### 对接内容：
- [ ] 搜索输入框
- [ ] 搜索结果列表
- [ ] 搜索历史
- [ ] 热门搜索

## 第三阶段：用户认证对接（第3周）

### 3.1 微信登录对接
**优先级：🔴 最高**

#### 对接内容：
- [ ] 微信授权登录
- [ ] 用户信息获取
- [ ] Token存储管理
- [ ] 登录状态检查

#### 实施步骤：
```javascript
// pages/login/login.js
Page({
  data: {
    userInfo: null,
    hasUserInfo: false
  },

  // 微信登录
  async onWxLogin() {
    try {
      wx.showLoading({ title: '登录中...' });
      
      // 1. 获取登录码
      const loginRes = await wx.login();
      if (!loginRes.code) {
        throw new Error('获取登录码失败');
      }
      
      // 2. 调用后端登录接口
      const res = await api.login(loginRes.code);
      
      // 3. 存储token和用户信息
      wx.setStorageSync('token', res.data.token);
      wx.setStorageSync('userInfo', res.data.user);
      
      // 4. 跳转回上一页或首页
      const pages = getCurrentPages();
      if (pages.length > 1) {
        wx.navigateBack();
      } else {
        wx.switchTab({ url: '/pages/index/index' });
      }
      
      wx.showToast({ title: '登录成功', icon: 'success' });
      
    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({ title: '登录失败', icon: 'error' });
    } finally {
      wx.hideLoading();
    }
  },

  // 获取用户信息
  async onGetUserProfile() {
    try {
      const res = await wx.getUserProfile({
        desc: '用于完善用户资料'
      });
      
      this.setData({
        userInfo: res.userInfo,
        hasUserInfo: true
      });
      
      // 更新用户信息到后端
      await api.updateUserInfo(res.userInfo);
      
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  }
});
```

### 3.2 用户信息管理
**优先级：🟡 高**

#### 对接内容：
- [ ] 用户信息展示
- [ ] 用户信息编辑
- [ ] 手机号绑定
- [ ] 退出登录

#### 实施步骤：
```javascript
// pages/profile/profile.js
Page({
  data: {
    userInfo: null,
    isLogin: false
  },

  onShow() {
    this.checkLoginStatus();
  },

  checkLoginStatus() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    this.setData({
      isLogin: !!token,
      userInfo: userInfo
    });
  },

  // 更新用户信息
  async updateUserInfo(newInfo) {
    try {
      await api.updateUserInfo(newInfo);
      
      // 更新本地存储
      const userInfo = wx.getStorageSync('userInfo');
      const updatedInfo = { ...userInfo, ...newInfo };
      wx.setStorageSync('userInfo', updatedInfo);
      
      this.setData({ userInfo: updatedInfo });
      wx.showToast({ title: '更新成功', icon: 'success' });
      
    } catch (error) {
      console.error('更新用户信息失败:', error);
      wx.showToast({ title: '更新失败', icon: 'error' });
    }
  },

  // 退出登录
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');
          this.setData({ isLogin: false, userInfo: null });
          wx.showToast({ title: '已退出', icon: 'success' });
        }
      }
    });
  }
});
```

## 第四阶段：购物流程对接（第4-5周）

### 4.1 购物车对接
**优先级：🔴 最高**

#### 对接内容：
- [ ] 购物车列表
- [ ] 添加商品到购物车
- [ ] 修改商品数量
- [ ] 删除购物车商品
- [ ] 购物车统计

### 4.2 地址管理对接
**优先级：🔴 最高**

#### 对接内容：
- [ ] 地址列表
- [ ] 添加地址
- [ ] 编辑地址
- [ ] 删除地址
- [ ] 设置默认地址

### 4.3 订单流程对接
**优先级：🔴 最高**

#### 对接内容：
- [ ] 订单确认页
- [ ] 创建订单
- [ ] 订单列表
- [ ] 订单详情
- [ ] 订单状态管理

### 4.4 支付流程对接
**优先级：🔴 最高**

#### 对接内容：
- [ ] 支付方式选择
- [ ] 微信支付调用
- [ ] 支付结果处理
- [ ] 支付状态查询

## 第五阶段：功能完善和优化（第6周）

### 5.1 错误处理完善
- [ ] 网络错误处理
- [ ] 业务错误处理
- [ ] 用户友好提示
- [ ] 重试机制

### 5.2 性能优化
- [ ] 数据缓存策略
- [ ] 图片懒加载
- [ ] 分页加载优化
- [ ] 请求去重

### 5.3 用户体验优化
- [ ] 加载状态优化
- [ ] 空状态处理
- [ ] 下拉刷新
- [ ] 上拉加载更多

## 实施检查清单

### 每个阶段完成后检查：
- [ ] 功能是否正常工作
- [ ] 错误处理是否完善
- [ ] 用户体验是否良好
- [ ] 代码是否规范
- [ ] 是否有性能问题

### 测试用例：
- [ ] 正常流程测试
- [ ] 异常情况测试
- [ ] 边界条件测试
- [ ] 网络异常测试

## 风险控制

### 技术风险：
1. **API接口变更** - 与后端保持密切沟通
2. **网络问题** - 完善错误处理和重试机制
3. **数据格式问题** - 严格按照接口文档开发

### 进度风险：
1. **功能复杂度** - 分阶段实施，降低风险
2. **测试时间** - 预留充足的测试时间
3. **联调问题** - 及时沟通解决

## 成功标准

### 第一阶段成功标准：
- [ ] 所有公共API调用成功
- [ ] 错误处理机制正常
- [ ] 基础页面展示正常

### 第二阶段成功标准：
- [ ] 首页完整展示
- [ ] 商品浏览流程顺畅
- [ ] 搜索功能正常

### 第三阶段成功标准：
- [ ] 微信登录流程完整
- [ ] 用户信息管理正常
- [ ] 认证状态管理正确

### 第四阶段成功标准：
- [ ] 完整购物流程可用
- [ ] 订单创建和管理正常
- [ ] 支付流程完整

### 第五阶段成功标准：
- [ ] 用户体验良好
- [ ] 性能表现优秀
- [ ] 错误处理完善

## 下一步行动

**立即开始第一阶段：基础设施搭建**

1. 验证现有配置文件
2. 测试公共API连通性
3. 完善错误处理机制
4. 准备进入第二阶段

准备好开始第一阶段了吗？我们先从验证基础配置开始！ 