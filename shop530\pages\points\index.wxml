<view class="points-mall-container">
  
  <!-- 头部用户信息 -->
  <view class="header-section">
    <view class="user-info">
      <view class="user-avatar">
        <image class="avatar-image" src="{{userInfo.avatar || '../images/default-avatar.png'}}" mode="aspectFill" />
      </view>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickname || '用户'}}</text>
        <view class="user-level">
          <text class="level-text">{{formatMemberLevel(userInfo.level)}}</text>
        </view>
      </view>
      <view class="points-balance">
        <text class="balance-label">我的积分</text>
        <text class="balance-value">{{formatPoints(userBalance)}}</text>
      </view>
    </view>
    
    <!-- 签到按钮 -->
    <view class="checkin-section" wx:if="{{!hasCheckedIn}}">
      <button class="checkin-btn" bindtap="onCheckin">
        <text class="checkin-text">签到领积分</text>
        <text class="checkin-reward">+{{checkinReward}}积分</text>
      </button>
    </view>
    
    <!-- 已签到状态 -->
    <view class="checked-in" wx:if="{{hasCheckedIn}}">
      <text class="checked-text">今日已签到</text>
      <text class="checked-reward">已获得{{checkinReward}}积分</text>
    </view>
  </view>

  <!-- 功能入口 -->
  <view class="function-entries">
    <view class="entry-item" bindtap="goToOrders">
      <image class="entry-icon" src="/images/points/orders.png" mode="aspectFit" />
      <text class="entry-name">我的兑换</text>
    </view>
    <view class="entry-item" bindtap="goToTransactions">
      <image class="entry-icon" src="/images/points/transactions.png" mode="aspectFit" />
      <text class="entry-name">积分明细</text>
    </view>
    <view class="entry-item" bindtap="goToRules">
      <image class="entry-icon" src="/images/points/rules.png" mode="aspectFit" />
      <text class="entry-name">积分规则</text>
    </view>
    <view class="entry-item" bindtap="goToRanking">
      <image class="entry-icon" src="/images/points/ranking.png" mode="aspectFit" />
      <text class="entry-name">积分排行</text>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <image class="search-icon" src="/images/icons/search.png" mode="aspectFit" />
      <input 
        class="search-input" 
        placeholder="搜索积分商品"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
      />
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-tabs">
    <scroll-view class="tabs-scroll" scroll-x="{{true}}">
      <view 
        class="tab-item {{currentCategory === item.id ? 'active' : ''}}"
        wx:for="{{categories}}"
        wx:key="id"
        data-category="{{item.id}}"
        bindtap="onCategoryChange"
      >
        <text class="tab-name">{{item.name}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 热门推荐 -->
  <view class="hot-section" wx:if="{{hotProducts.length > 0}}">
    <view class="section-header">
      <text class="section-title">热门推荐</text>
      <text class="section-more" bindtap="viewAllHot">更多 ></text>
    </view>
    <scroll-view class="hot-scroll" scroll-x="{{true}}">
      <view 
        class="hot-item"
        wx:for="{{hotProducts}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="goToProduct"
      >
        <image class="hot-image" src="{{item.image}}" mode="aspectFill" />
        <view class="hot-info">
          <text class="hot-name">{{item.name}}</text>
          <view class="hot-price">
            <text class="points-price">{{item.points_price}}积分</text>
            <text class="cash-price" wx:if="{{item.cash_price > 0}}">+¥{{item.cash_price}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 商品列表 -->
  <view class="products-section">
    <view class="section-header">
      <text class="section-title">积分商品</text>
      <view class="sort-options">
        <text 
          class="sort-item {{sortBy === 'default' ? 'active' : ''}}"
          data-sort="default"
          bindtap="onSortChange"
        >默认</text>
        <text 
          class="sort-item {{sortBy === 'points_asc' ? 'active' : ''}}"
          data-sort="points_asc"
          bindtap="onSortChange"
        >积分 ↑</text>
        <text 
          class="sort-item {{sortBy === 'points_desc' ? 'active' : ''}}"
          data-sort="points_desc"
          bindtap="onSortChange"
        >积分 ↓</text>
        <text 
          class="sort-item {{sortBy === 'popular' ? 'active' : ''}}"
          data-sort="popular"
          bindtap="onSortChange"
        >热门</text>
      </view>
    </view>

    <view class="products-grid">
      <view 
        class="product-item"
        wx:for="{{products}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="goToProduct"
      >
        <view class="product-image-wrapper">
          <image class="product-image" src="{{item.image}}" mode="aspectFill" />
          <view class="product-tags">
            <text class="tag hot" wx:if="{{item.is_hot}}">热门</text>
            <text class="tag new" wx:if="{{item.is_new}}">新品</text>
            <text class="tag limited" wx:if="{{item.is_limited}}">限量</text>
          </view>
        </view>
        
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <view class="product-price">
            <view class="points-price">
              <text class="points-value">{{item.points_price}}</text>
              <text class="points-unit">积分</text>
            </view>
            <view class="cash-price" wx:if="{{item.cash_price > 0}}">
              <text class="cash-value">+¥{{item.cash_price}}</text>
            </view>
          </view>
          <view class="product-stats">
            <text class="exchange-count">已兑换{{item.exchanged_count || 0}}件</text>
            <text class="stock-status {{item.stock > 0 ? 'in-stock' : 'out-stock'}}">
              {{item.stock > 0 ? '有库存' : '缺货'}}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{products.length === 0 && !loading}}">
      <image class="empty-image" src="/images/empty/products.png" mode="aspectFit" />
      <text class="empty-text">暂无商品</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-more" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && products.length > 0}}">
      <text class="no-more-text">没有更多商品了</text>
    </view>
  </view>

</view> 