<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class WechatUserSeeder extends Seeder
{
    /**
     * 生成微信用户测试数据
     */
    public function run(): void
    {
        // 清除现有的微信测试用户
        User::where('openid', 'like', 'test_%')->delete();
        
        // 创建10个微信测试用户
        for ($i = 1; $i <= 10; $i++) {
            $gender = rand(0, 2);
            $province = ['广东省', '北京市', '上海市', '湖南省', '浙江省'][rand(0, 4)];
            $city = ['深圳市', '广州市', '北京市', '上海市', '杭州市'][rand(0, 4)];
            
            User::create([
                'name' => '微信用户' . $i,
                'nickname' => '微信昵称' . $i,
                'password' => Hash::make('password'),
                'role' => 'customer',
                'openid' => 'test_openid_' . $i,
                'unionid' => 'test_unionid_' . $i,
                'phone' => '1386' . str_pad($i, 7, '0', STR_PAD_LEFT),
                'avatar' => 'https://api.dicebear.com/7.x/avataaars/svg?seed=' . Str::random(5),
                'gender' => $gender,
                'province' => $province,
                'city' => $city,
                'country' => '中国',
            ]);
        }
        
        // 创建5个普通用户(无微信信息)
        for ($i = 1; $i <= 5; $i++) {
            User::create([
                'name' => '普通用户' . $i,
                'password' => Hash::make('password'),
                'role' => 'customer',
                'phone' => '1399' . str_pad($i, 7, '0', STR_PAD_LEFT),
            ]);
        }
        
        $this->command->info('创建了10个微信测试用户和5个普通用户');
    }
} 