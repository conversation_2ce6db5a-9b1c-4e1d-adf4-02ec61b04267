<?php

namespace App\Employee\Api;

/**
 * API响应类
 * 
 * 提供统一的API响应格式
 */
class ApiResponse
{
    /**
     * 创建成功响应
     *
     * @param mixed $data 响应数据
     * @param string $message 成功消息
     * @param int $code 状态码
     * @return array
     */
    public static function success($data = null, string $message = '操作成功', int $code = 200): array
    {
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 创建错误响应
     *
     * @param string $message 错误消息
     * @param int $code 状态码
     * @param mixed $errors 错误详情
     * @return array
     */
    public static function error(string $message = '操作失败', int $code = 400, $errors = null): array
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'data' => null
        ];

        if ($errors) {
            $response['errors'] = $errors;
        }

        return $response;
    }

    /**
     * 创建分页响应
     *
     * @param \Illuminate\Pagination\LengthAwarePaginator $paginator 分页器实例
     * @param string $message 成功消息
     * @return array
     */
    public static function paginate($paginator, string $message = '获取数据成功'): array
    {
        return [
            'code' => 200,
            'message' => $message,
            'data' => $paginator->items(),
            'meta' => [
                'total' => $paginator->total(),
                'current_page' => $paginator->currentPage(),
                'per_page' => $paginator->perPage(),
                'last_page' => $paginator->lastPage()
            ]
        ];
    }
} 