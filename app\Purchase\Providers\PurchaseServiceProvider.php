<?php

namespace App\Purchase\Providers;

use Illuminate\Support\ServiceProvider;

class PurchaseServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     *
     * @return void
     */
    public function register()
    {
        // 注册依赖项
    }

    /**
     * 启动服务
     *
     * @return void
     */
    public function boot()
    {
        // 加载路由
        $this->loadRoutes();
    }

    /**
     * 加载路由文件
     *
     * @return void
     */
    protected function loadRoutes()
    {
        $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
        $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');
    }
} 