<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 修改 inventory_policy 字段，添加 inherit 选项
            $table->enum('inventory_policy', ['inherit', 'strict', 'allow_negative', 'unlimited'])
                  ->default('inherit')
                  ->change()
                  ->comment('库存管理策略：inherit(继承仓库设置), strict(严格), allow_negative(允许负库存), unlimited(无限库存)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 回滚时移除 inherit 选项
            $table->enum('inventory_policy', ['strict', 'allow_negative', 'unlimited'])
                  ->default('strict')
                  ->change()
                  ->comment('库存管理策略：strict(严格), allow_negative(允许负库存), unlimited(无限库存)');
        });
    }
};
