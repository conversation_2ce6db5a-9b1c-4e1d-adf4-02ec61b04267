<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // 添加会员等级关联
            $table->foreignId('membership_level_id')->nullable()->after('role')
                  ->constrained('membership_levels')
                  ->onDelete('set null');
            
            // 添加会员升级日期
            $table->timestamp('level_upgraded_at')->nullable()->after('joined_at')
                  ->comment('会员等级最后升级时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['membership_level_id']);
            $table->dropColumn(['membership_level_id', 'level_upgraded_at']);
        });
    }
}; 