<?php

namespace App\Unit\Services;

use App\Unit\Models\Unit;
use App\Unit\Models\UnitConversionGraph;
use App\Unit\Models\UnitConversionEdge;
use App\Unit\Models\ProductUnit;
use App\Product\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class UnitService
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 无需依赖UnitConversionService
    }

    /**
     * 获取单位列表
     *
     * @param Request $request
     * @return array
     */
    public function getUnits(Request $request)
    {
        try {
            $query = DB::table('units');
            
            Log::info("单位查询开始", ["request" => $request->all()]);
            
            // 过滤条件
            if ($request->has("type")) {
                $query->where("type", $request->type);
            }
            
            if ($request->has("keyword")) {
                $keyword = $request->keyword;
                $query->where(function($q) use ($keyword) {
                    $q->where("name", "like", "%{$keyword}%")
                      ->orWhere("display_name", "like", "%{$keyword}%")
                      ->orWhere("symbol", "like", "%{$keyword}%");
                });
            }
            
            // 是否基本单位过滤
            if ($request->has("is_base_unit") && $request->is_base_unit !== "") {
                $isBase = filter_var($request->is_base_unit, FILTER_VALIDATE_BOOLEAN);
                $query->where("is_base_unit", $isBase);
            }
            
            // 可见性过滤
            if ($request->has("is_visible") && $request->is_visible !== "") {
                $isVisible = filter_var($request->is_visible, FILTER_VALIDATE_BOOLEAN);
                $query->where("is_visible", $isVisible);
            }
            
            // 排序
            if ($request->has("sort_by")) {
                $sortBy = $request->sort_by;
                $sortDir = $request->input("sort_dir", "asc");
                $query->orderBy($sortBy, $sortDir);
            } else {
                $query->orderBy("type", "asc")->orderBy("sort", "asc");
            }
            
            // 记录SQL
            $sql = $query->toSql();
            $bindings = $query->getBindings();
            Log::info("单位查询SQL", ["sql" => $sql, "bindings" => $bindings]);
            
            // 获取总数
            $totalCount = $query->count();
            Log::info("单位总数", ["count" => $totalCount]);
            
            // 处理分页
            if ($request->has("per_page")) {
                $perPage = (int)$request->per_page;
                $page = (int)$request->input('page', 1);
                
                $offset = ($page - 1) * $perPage;
                $items = $query->offset($offset)->limit($perPage)->get();
                
                // 明确指定响应格式
                $responseData = [
                    "data" => $items,
                    "meta" => [
                        "total" => $totalCount,
                        "per_page" => $perPage,
                        "current_page" => $page,
                        "last_page" => ceil($totalCount / $perPage),
                    ]
                ];
            } else {
                $items = $query->get();
                
                $responseData = [
                    "data" => $items
                ];
            }
            
            Log::info("单位列表处理完成", [
                "data_count" => count($responseData["data"]),
                "total" => isset($responseData["meta"]) ? $responseData["meta"]["total"] : count($responseData["data"])
            ]);
            
            return $responseData;
        } catch (\Exception $e) {
            Log::error("获取单位列表失败", [
                "error" => $e->getMessage(),
                "trace" => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * 创建单位
     *
     * @param array $data
     * @return Unit
     */
    public function createUnit(array $data)
    {
        return Unit::create($data);
    }
    
    /**
     * 获取单位详情
     *
     * @param int $id
     * @return array
     */
    public function getUnitDetail($id)
    {
        $unit = Unit::findOrFail($id);
        
        // 获取使用情况统计
        $stats = [
            'as_base_unit' => Product::where('base_unit_id', $unit->id)->count(),
            'as_auxiliary_unit' => ProductUnit::where('unit_id', $unit->id)->count(),
            'in_conversion_edges' => UnitConversionEdge::where('from_unit_id', $unit->id)
                                    ->orWhere('to_unit_id', $unit->id)
                                    ->count(),
            'derived_units' => Unit::where('base_unit_id', $unit->id)->count()
        ];
        
        $stats['total_usage'] = $stats['as_base_unit'] + $stats['as_auxiliary_unit'] 
                              + $stats['in_conversion_edges'] + $stats['derived_units'];
        
        return array_merge($unit->toArray(), [
            'usage_stats' => $stats
        ]);
    }
    
    /**
     * 更新单位
     *
     * @param int $id
     * @param array $data
     * @return Unit
     */
    public function updateUnit($id, array $data)
    {
        $unit = Unit::findOrFail($id);
        
        // 处理字段映射
        if (isset($data['is_base'])) {
            $data['is_base_unit'] = filter_var($data['is_base'], FILTER_VALIDATE_BOOLEAN);
            unset($data['is_base']);
        }
        
        // 处理布尔值字段
        if (isset($data['is_base_unit'])) {
            $data['is_base_unit'] = filter_var($data['is_base_unit'], FILTER_VALIDATE_BOOLEAN);
        }
        
        if (isset($data['is_visible'])) {
            $data['is_visible'] = filter_var($data['is_visible'], FILTER_VALIDATE_BOOLEAN);
        }
        
        // 处理字段映射 - 如果存在category字段，映射到type
        if (isset($data['category'])) {
            $data['type'] = $data['category'];
            unset($data['category']);
        }
        
        // 更新单位
        $unit->update($data);
        
        return $unit;
    }
    
    /**
     * 删除单位
     *
     * @param int $id
     * @return bool
     * @throws \Exception 如果单位正在被使用
     */
    public function deleteUnit($id)
    {
        $unit = Unit::findOrFail($id);
        
        // 检查单位是否在使用中
        if ($unit->isInUse()) {
            throw new \Exception('单位已被使用，不能删除。');
        }
        
        return $unit->delete();
    }
    
    /**
     * 获取单位关联的产品列表
     *
     * @param int $id
     * @return \Illuminate\Support\Collection
     */
    public function getUnitProducts($id)
    {
        $unit = Unit::findOrFail($id);
        
        // 获取作为基本单位的产品
        $baseProducts = Product::where('base_unit_id', $unit->id)
            ->with('category')
            ->get()
            ->map(function($product) {
                return array_merge($product->toArray(), [
                    'relationship' => 'base_unit',
                    'conversion_factor' => 1
                ]);
            });
            
        // 获取作为辅助单位的产品
        $auxiliaryProducts = $unit->products()
            ->with('category')
            ->get()
            ->map(function($product) {
                return array_merge($product->toArray(), [
                    'relationship' => 'auxiliary_unit',
                    'conversion_factor' => $product->pivot->conversion_factor
                ]);
            });
            
        return $baseProducts->merge($auxiliaryProducts);
    }

    /**
     * 获取商品的单位关联
     *
     * @param int $productId
     * @return array
     */
    public function getProductUnits($productId)
    {
        $product = Product::findOrFail($productId);
        
        // 获取商品的基本单位
        $baseUnit = null;
        if ($product->base_unit_id) {
            $baseUnit = Unit::find($product->base_unit_id);
        }
        
        // 获取商品的所有单位关联
        $productUnits = ProductUnit::where('product_id', $productId)
            ->with('unit')
            ->get()
            ->map(function($productUnit) {
                // 处理 roles 字段，添加类型检查
                $roles = [];
                if ($productUnit->roles) {
                    if (is_string($productUnit->roles)) {
                        $roles = json_decode($productUnit->roles, true) ?: [];
                    } else if (is_array($productUnit->roles)) {
                        $roles = $productUnit->roles;
                    }
                }
                
                // 处理 role_priority 字段，添加类型检查
                $priority = [];
                if ($productUnit->role_priority) {
                    if (is_string($productUnit->role_priority)) {
                        $priority = json_decode($productUnit->role_priority, true) ?: [];
                    } else if (is_array($productUnit->role_priority)) {
                        $priority = $productUnit->role_priority;
                    }
                }
                
                return [
                    'id' => $productUnit->id,
                    'product_id' => $productUnit->product_id,
                    'unit_id' => $productUnit->unit_id,
                    'unit' => $productUnit->unit,
                    'conversion_factor' => $productUnit->conversion_factor,
                    'is_default' => $productUnit->is_default,
                    'is_active' => $productUnit->is_active,
                    'roles' => $roles,
                    'role_priority' => $priority,
                    'created_at' => $productUnit->created_at,
                    'updated_at' => $productUnit->updated_at
                ];
            });
        
        // 处理 unit_settings 字段，添加类型检查
        $unitSettings = null;
        if ($product->unit_settings) {
            if (is_string($product->unit_settings)) {
                $unitSettings = json_decode($product->unit_settings, true);
            } else if (is_array($product->unit_settings)) {
                $unitSettings = $product->unit_settings;
            }
        }
        
        return [
            'base_unit' => $baseUnit,
            'product_units' => $productUnits,
            'unit_settings' => $unitSettings,
            'multi_unit_enabled' => $product->multi_unit_enabled
        ];
    }
    
    /**
     * 设置商品的所有单位关联
     *
     * @param int $productId
     * @param array $data
     * @return array
     */
    public function setProductAllUnits($productId, array $data)
    {
        $product = Product::findOrFail($productId);
        
        DB::beginTransaction();
        
        try {
            // 更新产品基本单位
            if (isset($data['base_unit_id'])) {
                $product->update([
                    'base_unit_id' => $data['base_unit_id'],
                ]);
            }
            
            // 处理产品单位关联
            if (isset($data['units']) && is_array($data['units'])) {
                // 获取当前的单位关联
                $currentUnitIds = ProductUnit::where('product_id', $productId)
                                        ->pluck('unit_id')
                                        ->toArray();
                
                // 处理每个提交的单位
                foreach ($data['units'] as $unitData) {
                    if (!isset($unitData['unit_id'])) {
                        continue;
                    }
                    
                    // 记录原始数据
                    Log::info('处理单位数据', [
                        'unit_id' => $unitData['unit_id'],
                        'roles_type' => gettype($unitData['roles'] ?? null),
                        'roles_data' => $unitData['roles'] ?? null,
                        'role_priority_type' => gettype($unitData['role_priority'] ?? null),
                        'role_priority_data' => $unitData['role_priority'] ?? null
                    ]);
                    
                    // 准备数据
                    $unitRecord = [
                        'product_id' => $productId,
                        'unit_id' => $unitData['unit_id'],
                        'conversion_factor' => $unitData['conversion_factor'] ?? 1,
                        'is_default' => $unitData['is_default'] ?? false,
                        'is_active' => $unitData['is_active'] ?? true,
                        'roles' => $this->prepareRolesData($unitData['roles'] ?? null),
                        'role_priority' => $this->prepareRolePriorityData($unitData['role_priority'] ?? null)
                    ];
                    
                    // 记录处理后的数据
                    Log::info('准备存储单位数据', [
                        'unit_id' => $unitData['unit_id'],
                        'roles' => $unitRecord['roles'],
                        'role_priority' => $unitRecord['role_priority']
                    ]);
                    
                    // 创建或更新记录
                    ProductUnit::updateOrCreate(
                        ['product_id' => $productId, 'unit_id' => $unitData['unit_id']],
                        $unitRecord
                    );
                }
                
                // 删除未包含在提交数据中的单位关联
                $unitIdsToKeep = array_column($data['units'], 'unit_id');
                ProductUnit::where('product_id', $productId)
                        ->whereNotIn('unit_id', $unitIdsToKeep)
                        ->delete();
            }
            
            DB::commit();
            
            // 返回更新后的数据
            return $this->getProductUnits($productId);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('设置产品单位失败', [
                'product_id' => $productId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
    
    /**
     * 为产品进行单位转换
     *
     * @param float $quantity 数量
     * @param int $fromUnitId 源单位ID
     * @param int|Unit $toUnit 目标单位ID或单位对象
     * @param int $productId 产品ID
     * @return float 转换后的数量
     */
    public function convertForProduct($quantity, $fromUnitId, $toUnit, $productId)
    {
        // 如果单位相同，直接返回
        if ($fromUnitId == $toUnit || (is_object($toUnit) && $fromUnitId == $toUnit->id)) {
            return $quantity;
        }
        
        // 获取源单位
        $fromUnit = Unit::findOrFail($fromUnitId);
        
        // 获取目标单位
        if (!is_object($toUnit)) {
            $toUnit = Unit::findOrFail($toUnit);
        }
        
        // 获取产品
        $product = Product::findOrFail($productId);
        
        // 使用产品的单位转换逻辑
        return $product->convertQuantity($quantity, $fromUnitId, $toUnit->id) ?? $quantity;
    }
    
    /**
     * 获取所有单位类型
     *
     * @return Collection
     */
    public function getAllTypes(): Collection
    {
        return Unit::select('type')
                ->distinct()
                ->orderBy('type')
                ->pluck('type');
    }
    
    /**
     * 获取指定类型的所有单位
     *
     * @param string $type 单位类型
     * @return Collection
     */
    public function getUnitsByType(string $type): Collection
    {
        return Unit::where('type', $type)
                 ->orderBy('sort')
                 ->get();
    }
    
    /**
     * 根据符号和类型获取单位
     *
     * @param string $symbol 单位符号
     * @param string $type 单位类型
     * @return Unit|null
     */
    public function getUnitBySymbol(string $symbol, string $type): ?Unit
    {
        return Unit::where('symbol', $symbol)
                 ->where('type', $type)
                 ->first();
    }
    
    /**
     * 转换单位值
     * 
     * @param float $value 要转换的值
     * @param Unit $fromUnit 源单位
     * @param Unit $toUnit 目标单位
     * @return float 转换后的值
     */
    public function convertValue(float $value, Unit $fromUnit, Unit $toUnit): float
    {
        // 检查单位类型是否匹配
        if ($fromUnit->type !== $toUnit->type) {
            throw new \Exception("无法转换不同类型的单位：{$fromUnit->type} → {$toUnit->type}");
        }
        
        // 如果单位相同，直接返回原值
        if ($fromUnit->id === $toUnit->id) {
            return $value;
        }
        
        // 获取默认转换图
        $graph = UnitConversionGraph::where('type', $fromUnit->type)
                                ->where('is_default', true)
                                ->first();
        
        if (!$graph) {
            throw new \Exception("找不到类型 {$fromUnit->type} 的默认转换图");
        }
        
        // 使用转换图进行转换
        return $this->convertUsingGraph($value, $fromUnit, $toUnit, $graph);
    }
    
    /**
     * 使用转换图进行单位转换
     * 
     * @param float $value
     * @param Unit $fromUnit
     * @param Unit $toUnit
     * @param UnitConversionGraph $graph
     * @return float
     * @throws \Exception 如果无法找到转换路径
     */
    public function convertUsingGraph(float $value, Unit $fromUnit, Unit $toUnit, UnitConversionGraph $graph): float
    {
        // 如果单位相同，直接返回
        if ($fromUnit->id === $toUnit->id) {
            return $value;
        }
        
        // 使用缓存获取转换路径
        $cacheKey = "unit_conversion_path_{$graph->id}_{$fromUnit->id}_{$toUnit->id}";
        
        $conversionPath = Cache::remember($cacheKey, now()->addDay(), function () use ($graph, $fromUnit, $toUnit) {
            return $this->findConversionPath($graph, $fromUnit, $toUnit);
        });
        
        if (empty($conversionPath)) {
            throw new \Exception("无法找到从 {$fromUnit->symbol} 到 {$toUnit->symbol} 的转换路径");
        }
        
        // 沿路径执行转换
        $result = $value;
        
        foreach ($conversionPath as $step) {
            $result *= $step['conversion_factor'];
        }
        
        return $result;
    }
    
    /**
     * 查找单位转换路径（使用广度优先搜索）
     * 
     * @param UnitConversionGraph $graph
     * @param Unit $fromUnit
     * @param Unit $toUnit
     * @return array 转换步骤数组
     */
    protected function findConversionPath(UnitConversionGraph $graph, Unit $fromUnit, Unit $toUnit): array
    {
        // 如果单位相同，返回空路径
        if ($fromUnit->id === $toUnit->id) {
            return [];
        }
        
        // 构建邻接列表
        $adjacencyList = [];
        
        $edges = $graph->edges()->with(['fromUnit', 'toUnit'])->get();
        
        foreach ($edges as $edge) {
            // 添加正向边
            if (!isset($adjacencyList[$edge->from_unit_id])) {
                $adjacencyList[$edge->from_unit_id] = [];
            }
            
            $adjacencyList[$edge->from_unit_id][] = [
                'unit_id' => $edge->to_unit_id,
                'conversion_factor' => $edge->conversion_factor,
                'edge_id' => $edge->id
            ];
            
            // 如果是双向边，添加反向边
            if ($edge->is_bidirectional) {
                if (!isset($adjacencyList[$edge->to_unit_id])) {
                    $adjacencyList[$edge->to_unit_id] = [];
                }
                
                $adjacencyList[$edge->to_unit_id][] = [
                    'unit_id' => $edge->from_unit_id,
                    'conversion_factor' => 1 / $edge->conversion_factor,
                    'edge_id' => $edge->id
                ];
            }
        }
        
        // BFS查找路径
        $queue = new \SplQueue();
        $queue->enqueue([
            'unit_id' => $fromUnit->id,
            'path' => []
        ]);
        
        $visited = [$fromUnit->id => true];
        
        while (!$queue->isEmpty()) {
            $current = $queue->dequeue();
            $currentUnitId = $current['unit_id'];
            
            // 找到目标单位
            if ($currentUnitId === $toUnit->id) {
                return $current['path'];
            }
            
            // 检查邻接单位
            if (isset($adjacencyList[$currentUnitId])) {
                foreach ($adjacencyList[$currentUnitId] as $neighbor) {
                    if (!isset($visited[$neighbor['unit_id']])) {
                        $newPath = $current['path'];
                        $newPath[] = [
                            'from_unit_id' => $currentUnitId,
                            'to_unit_id' => $neighbor['unit_id'],
                            'conversion_factor' => $neighbor['conversion_factor'],
                            'edge_id' => $neighbor['edge_id']
                        ];
                        
                        $queue->enqueue([
                            'unit_id' => $neighbor['unit_id'],
                            'path' => $newPath
                        ]);
                        
                        $visited[$neighbor['unit_id']] = true;
                    }
                }
            }
        }
        
        // 未找到路径
        return [];
    }
    
    /**
     * 批量转换值
     * 
     * @param array $values 值数组
     * @param Unit $fromUnit 源单位
     * @param Unit $toUnit 目标单位
     * @return array 转换后的值数组
     */
    public function batchConvert(array $values, Unit $fromUnit, Unit $toUnit): array
    {
        $result = [];
        
        // 如果单位相同，直接返回原值数组
        if ($fromUnit->id === $toUnit->id) {
            return $values;
        }
        
        // 获取转换路径（只需获取一次路径，对所有值使用相同的转换）
        $path = null;
        $conversionFactor = null;
        
        try {
            // 获取合适的转换图
            $graph = UnitConversionGraph::where('type', $fromUnit->type)
                                    ->where('is_default', true)
                                    ->first();
            
            if (!$graph) {
                throw new \Exception("找不到类型 {$fromUnit->type} 的默认转换图");
            }
            
            $cacheKey = "unit_conversion_path_{$graph->id}_{$fromUnit->id}_{$toUnit->id}";
            
            $path = Cache::remember($cacheKey, now()->addDay(), function () use ($graph, $fromUnit, $toUnit) {
                return $this->findConversionPath($graph, $fromUnit, $toUnit);
            });
            
            // 如果找到路径，计算总转换系数
            if (empty($path)) {
                throw new \Exception("无法找到从 {$fromUnit->symbol} 到 {$toUnit->symbol} 的转换路径");
            }
            
            $conversionFactor = 1;
            foreach ($path as $step) {
                $conversionFactor *= $step['conversion_factor'];
            }
            
            // 使用计算出的转换系数批量转换
            foreach ($values as $value) {
                $result[] = $value * $conversionFactor;
            }
            
            return $result;
            
        } catch (\Exception $e) {
            // 转换失败，返回null数组
            Log::error("批量单位转换失败", [
                'error' => $e->getMessage(),
                'from_unit' => $fromUnit->symbol,
                'to_unit' => $toUnit->symbol
            ]);
            return array_fill(0, count($values), null);
        }
    }

    /**
     * 准备角色数据，考虑到Laravel会自动使用$casts处理JSON
     *
     * @param mixed $roles
     * @return array|null
     */
    private function prepareRolesData($roles)
    {
        // 如果为空，返回null
        if ($roles === null || $roles === '') {
            return null;
        }
        
        // 如果是字符串，尝试解析为数组
        if (is_string($roles)) {
            try {
                $decoded = json_decode($roles, true);
                // 如果能解析为数组，返回解码后的数组
                if (is_array($decoded)) {
                    return $decoded; // 返回解码后的数组，Laravel会自动处理
                } else {
                    // 不是数组，可能是单个角色名，包装为数组
                    return [$roles];
                }
            } catch (\Exception $e) {
                // 解析失败，假设是单个角色名，包装为数组
                return [$roles];
            }
        }
        
        // 如果是数组，直接返回（Laravel会自动处理JSON序列化）
        if (is_array($roles)) {
            return $roles;
        }
        
        // 其他情况返回null
        return null;
    }

    /**
     * 准备角色优先级数据，考虑到Laravel会自动使用$casts处理JSON
     *
     * @param mixed $rolePriority
     * @return array|null
     */
    private function prepareRolePriorityData($rolePriority)
    {
        if (is_string($rolePriority)) {
            try {
                return json_decode($rolePriority, true) ?: [];
            } catch (\Exception $e) {
                Log::warning("无法解析角色优先级数据", [
                    "data" => $rolePriority,
                    "error" => $e->getMessage()
                ]);
                return [];
            }
        }
        
        if (is_array($rolePriority)) {
            return $rolePriority;
        }
        
        return [];
    }

    /**
     * 获取单位统计数据
     *
     * @return array
     */
    public function getUnitStats()
    {
        try {
            // 获取基本统计
            $totalUnits = Unit::count();
            $activeUnits = Unit::where('is_visible', true)->count();
            
            // 获取转换规则统计 - 修复字段名
            $conversionRules = DB::table('unit_conversion_edges')->count();
            // 由于表中没有is_auto_generated字段，我们基于is_active字段计算
            $autoConversions = DB::table('unit_conversion_edges')
                ->where('is_active', true)
                ->count();
            
            // 获取使用次数统计（本月）
            $usageCount = DB::table('product_units')
                ->whereMonth('created_at', date('m'))
                ->whereYear('created_at', date('Y'))
                ->count();
            
            // 获取类别统计
            $categoryStats = DB::table('units')
                ->select('type', DB::raw('COUNT(*) as count'))
                ->groupBy('type')
                ->get();
            
            $categories = $categoryStats->count();
            $mostUsedCategory = $categoryStats->sortByDesc('count')->first();
            
            // 计算每个类别的使用率
            $categoryStatsWithUsage = [];
            foreach ($categoryStats as $category) {
                $usageRate = $totalUnits > 0 ? round(($category->count / $totalUnits) * 100, 1) : 0;
                $categoryStatsWithUsage[] = [
                    'type' => $category->type,
                    'name' => $this->getCategoryName($category->type),
                    'count' => $category->count,
                    'usage_rate' => $usageRate,
                ];
            }
            
            return [
                'total_units' => $totalUnits,
                'active_units' => $activeUnits,
                'conversion_rules' => $conversionRules,
                'auto_conversions' => $autoConversions,
                'usage_count' => $usageCount,
                'categories' => $categories,
                'most_used_category' => $mostUsedCategory ? $this->getCategoryName($mostUsedCategory->type) : '无',
                'category_stats' => $categoryStatsWithUsage,
            ];
        } catch (\Exception $e) {
            Log::error('获取单位统计失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 切换单位状态
     *
     * @param int $id
     * @return Unit
     */
    public function toggleUnitStatus($id)
    {
        try {
            $unit = Unit::findOrFail($id);
            $unit->is_visible = !$unit->is_visible;
            $unit->save();
            
            Log::info('单位状态切换成功', [
                'unit_id' => $id,
                'new_status' => $unit->is_visible ? 'active' : 'inactive'
            ]);
            
            return $unit;
        } catch (\Exception $e) {
            Log::error('切换单位状态失败', [
                'unit_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 获取单位使用统计
     *
     * @param int $id
     * @return array
     */
    public function getUnitUsageStats($id)
    {
        try {
            $unit = Unit::findOrFail($id);
            
            // 作为基础单位的产品数量
            $asBaseUnit = Product::where('base_unit_id', $unit->id)->count();
            
            // 作为辅助单位的产品数量
            $asAuxiliaryUnit = ProductUnit::where('unit_id', $unit->id)->count();
            
            // 在转换规则中的使用次数
            $inConversionEdges = DB::table('unit_conversion_edges')
                ->where('from_unit_id', $unit->id)
                ->orWhere('to_unit_id', $unit->id)
                ->count();
            
            // 衍生单位数量
            $derivedUnits = Unit::where('base_unit_id', $unit->id)->count();
            
            // 总使用次数
            $totalUsage = $asBaseUnit + $asAuxiliaryUnit + $inConversionEdges + $derivedUnits;
            
            // 最近使用趋势（简单计算）
            $recentUsage = ProductUnit::where('unit_id', $unit->id)
                ->whereDate('created_at', '>=', now()->subDays(30))
                ->count();
            
            $previousUsage = ProductUnit::where('unit_id', $unit->id)
                ->whereDate('created_at', '>=', now()->subDays(60))
                ->whereDate('created_at', '<', now()->subDays(30))
                ->count();
            
            $trend = 'stable';
            if ($recentUsage > $previousUsage) {
                $trend = 'up';
            } elseif ($recentUsage < $previousUsage) {
                $trend = 'down';
            }
            
            return [
                'unit_id' => $unit->id,
                'unit_name' => $unit->name,
                'as_base_unit' => $asBaseUnit,
                'as_auxiliary_unit' => $asAuxiliaryUnit,
                'in_conversion_edges' => $inConversionEdges,
                'derived_units' => $derivedUnits,
                'total_usage' => $totalUsage,
                'recent_usage' => $recentUsage,
                'usage_trend' => $trend,
                'can_delete' => $totalUsage === 0,
            ];
        } catch (\Exception $e) {
            Log::error('获取单位使用统计失败', [
                'unit_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 批量操作单位
     *
     * @param string $action
     * @param array $unitIds
     * @return array
     */
    public function batchOperateUnits($action, $unitIds)
    {
        try {
            $results = [];
            $successCount = 0;
            $failCount = 0;
            
            foreach ($unitIds as $unitId) {
                try {
                    switch ($action) {
                        case 'activate':
                            $unit = Unit::findOrFail($unitId);
                            $unit->is_visible = true;
                            $unit->save();
                            $results[] = ['id' => $unitId, 'status' => 'success', 'message' => '启用成功'];
                            $successCount++;
                            break;
                            
                        case 'deactivate':
                            $unit = Unit::findOrFail($unitId);
                            $unit->is_visible = false;
                            $unit->save();
                            $results[] = ['id' => $unitId, 'status' => 'success', 'message' => '禁用成功'];
                            $successCount++;
                            break;
                            
                        case 'delete':
                            $this->deleteUnit($unitId);
                            $results[] = ['id' => $unitId, 'status' => 'success', 'message' => '删除成功'];
                            $successCount++;
                            break;
                            
                        default:
                            $results[] = ['id' => $unitId, 'status' => 'error', 'message' => '未知操作'];
                            $failCount++;
                    }
                } catch (\Exception $e) {
                    $results[] = ['id' => $unitId, 'status' => 'error', 'message' => $e->getMessage()];
                    $failCount++;
                }
            }
            
            Log::info('批量操作单位完成', [
                'action' => $action,
                'total' => count($unitIds),
                'success' => $successCount,
                'fail' => $failCount
            ]);
            
            return [
                'total' => count($unitIds),
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'results' => $results,
            ];
        } catch (\Exception $e) {
            Log::error('批量操作单位失败', [
                'action' => $action,
                'unit_ids' => $unitIds,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 获取类别名称
     *
     * @param string $type
     * @return string
     */
    private function getCategoryName($type)
    {
        $categoryNames = [
            'weight' => '重量单位',
            'volume' => '体积单位',
            'length' => '长度单位',
            'quantity' => '数量单位',
            'package' => '包装单位',
            'time' => '时间单位',
        ];
        
        return $categoryNames[$type] ?? $type;
    }

    /**
     * 获取单位转换规则
     *
     * @param int $id
     * @return array
     */
    public function getUnitConversions($id)
    {
        try {
            $unit = Unit::findOrFail($id);
            
            // 获取从该单位转换到其他单位的规则
            $fromConversions = DB::table('unit_conversion_edges')
                ->leftJoin('units as to_units', 'unit_conversion_edges.to_unit_id', '=', 'to_units.id')
                ->where('unit_conversion_edges.from_unit_id', $id)
                ->select(
                    'unit_conversion_edges.*',
                    'to_units.name as to_unit_name',
                    'to_units.symbol as to_unit_symbol'
                )
                ->get();
            
            // 获取从其他单位转换到该单位的规则
            $toConversions = DB::table('unit_conversion_edges')
                ->leftJoin('units as from_units', 'unit_conversion_edges.from_unit_id', '=', 'from_units.id')
                ->where('unit_conversion_edges.to_unit_id', $id)
                ->select(
                    'unit_conversion_edges.*',
                    'from_units.name as from_unit_name',
                    'from_units.symbol as from_unit_symbol'
                )
                ->get();
            
            return [
                'unit_id' => $unit->id,
                'unit_name' => $unit->name,
                'unit_symbol' => $unit->symbol,
                'from_conversions' => $fromConversions,
                'to_conversions' => $toConversions,
                'total_conversions' => $fromConversions->count() + $toConversions->count(),
            ];
        } catch (\Exception $e) {
            Log::error('获取单位转换规则失败', [
                'unit_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
} 