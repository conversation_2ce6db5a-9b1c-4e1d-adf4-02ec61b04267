<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade')->comment('订单ID');
            $table->foreignId('correction_id')->nullable()->constrained('order_corrections')->onDelete('set null')->comment('更正单ID（补款/退款时关联）');
            
            // 关联现有微信支付表
            $table->foreignId('wechat_payment_id')->nullable()->constrained('wechat_service_payments')->onDelete('set null')->comment('关联微信支付记录ID');
            $table->foreignId('wechat_refund_id')->nullable()->constrained('wechat_service_refunds')->onDelete('set null')->comment('关联微信退款记录ID');
            
            $table->enum('payment_type', ['initial', 'supplement', 'refund', 'cod'])->comment('付款类型：初始付款/补款/退款/货到付款');
            $table->enum('business_type', ['order_payment', 'correction_supplement', 'correction_refund', 'cod_final'])->comment('业务类型');
            $table->decimal('amount', 10, 2)->comment('金额');
            $table->enum('payment_method', ['wechat', 'alipay', 'cash', 'bank_transfer'])->comment('支付方式');
            
            // 第三方支付信息
            $table->string('transaction_id', 100)->nullable()->comment('第三方交易号');
            $table->string('out_trade_no', 100)->nullable()->comment('商户订单号');
            
            $table->enum('status', ['pending', 'success', 'failed', 'refunded', 'cancelled'])->default('pending')->comment('状态');
            $table->timestamp('paid_at')->nullable()->comment('付款时间');
            $table->timestamp('refunded_at')->nullable()->comment('退款时间');
            
            // 操作员信息
            $table->foreignId('operated_by')->nullable()->constrained('employees')->onDelete('set null')->comment('操作员ID');
            
            $table->text('notes')->nullable()->comment('备注');
            $table->json('extra_data')->nullable()->comment('扩展数据');
            $table->timestamps();
            
            $table->index(['order_id', 'payment_type']);
            $table->index(['correction_id', 'business_type']);
            $table->index('status');
            $table->index('transaction_id');
            $table->index('wechat_payment_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_records');
    }
}; 