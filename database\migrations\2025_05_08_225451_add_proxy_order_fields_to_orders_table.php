<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // 代客下单字段：记录是哪个员工创建的订单
            $table->unsignedBigInteger('created_by_id')->nullable()->after('user_id')->comment('创建者ID（代客下单）');
            $table->foreign('created_by_id')->references('id')->on('employees')->onDelete('set null');
            
            // 添加订单折扣和小计字段
            $table->decimal('subtotal', 10, 2)->default(0)->after('total')->comment('订单小计（未折扣）');
            $table->decimal('discount', 10, 2)->default(0)->after('subtotal')->comment('订单折扣');
            
            // 订单来源
            $table->string('source')->default('user')->after('status')->comment('订单来源: user=用户自己下单, proxy=代客下单');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropForeign(['created_by_id']);
            $table->dropColumn('created_by_id');
            $table->dropColumn('subtotal');
            $table->dropColumn('discount');
            $table->dropColumn('source');
        });
    }
};
