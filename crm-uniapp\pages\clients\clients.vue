<template>
	<view class="clients-container">
		<!-- 固定顶部区域 -->
		<view class="fixed-header">
			<!-- 搜索栏 -->
			<view class="search-section">
				<view class="search-box">
					<text class="search-icon">🔍</text>
					<input 
						class="search-input" 
						type="text" 
						placeholder="搜索商户名称、联系人、手机号或地区" 
						v-model="searchKeyword"
						@input="onSearchInput"
						@confirm="onSearchConfirm"
					/>
					<text class="clear-icon" v-if="searchKeyword" @tap="clearSearch">✕</text>
				</view>
			</view>
			
			<!-- 快速筛选 -->
			<view class="filter-section" v-if="!searchKeyword">
				<scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="false">
					<view 
						class="filter-item" 
						:class="{ active: currentFilter === filter.key }"
						v-for="filter in filterOptions" 
						:key="filter.key"
						@tap="switchFilter(filter.key)"
					>
						<text class="filter-text">{{ filter.label }}</text>
					</view>
				</scroll-view>
			</view>
		</view>
		
		<!-- 客户列表 -->
		<view class="client-list">
			<view 
				class="client-card" 
				v-for="client in clientList" 
				:key="client.id"
				@tap="goToClientDetail(client)"
			>
				<!-- 客户头部信息 -->
				<view class="client-header">
					<view class="client-avatar">
						<text class="avatar-text">{{ getAvatarText(client) }}</text>
						<view class="status-dot" :class="getStatusClass(client.status)"></view>
					</view>
					<view class="client-main-info">
						<view class="client-name-row">
							<text class="client-name">{{ client.merchant_name || client.name || '未知商户' }}</text>
							<view class="client-badges">
								<text class="badge vip-badge" v-if="isVipClient(client)">VIP</text>
								<text class="badge new-badge" v-if="isNewClient(client)">新</text>
							</view>
						</view>
						<view class="client-contact">
							<text class="contact-info" v-if="client.merchant_name && client.name">👤 {{ client.name }}</text>
							<text class="contact-info">📱 {{ client.phone }}</text>
						</view>
					</view>
					<view class="client-actions">
						<button class="action-btn call-btn" @tap.stop="callClient(client)">
							<text class="btn-icon">📞</text>
						</button>
						<button class="action-btn order-btn" @tap.stop="goToProxyOrder(client)">
							<text class="btn-text">下单</text>
						</button>
					</view>
				</view>
				
				<!-- 客户统计信息 -->
				<view class="client-stats">
					<view class="stat-item">
						<text class="stat-label">订单数</text>
						<text class="stat-value">{{ client.order_count || 0 }}</text>
					</view>
					<view class="stat-item">
						<text class="stat-label">总消费</text>
						<text class="stat-value">¥{{ formatAmount(client.total_amount) }}</text>
					</view>
					<view class="stat-item">
						<text class="stat-label">最近下单</text>
						<text class="stat-value">{{ formatTime(client.last_order_at) }}</text>
					</view>
				</view>
			</view>
			
			<!-- 加载更多 -->
			<view class="load-more-section" v-if="hasMore && clientList.length > 0">
				<view class="load-indicator" v-if="listLoading">
					<text class="load-text">加载中...</text>
				</view>
				<button class="load-more-btn" @tap="loadMore" v-else>
					<text class="load-text">加载更多</text>
				</button>
			</view>
			
			<!-- 没有更多数据 -->
			<view class="no-more-section" v-if="!hasMore && clientList.length > 0">
				<text class="no-more-text">已显示全部客户</text>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading-section" v-if="listLoading && clientList.length === 0">
			<view class="loading-content">
				<text class="loading-icon">⏳</text>
				<text class="loading-text">正在加载客户列表...</text>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-section" v-if="!listLoading && clientList.length === 0">
			<view class="empty-content">
				<text class="empty-icon">{{ searchKeyword ? '🔍' : '👥' }}</text>
				<text class="empty-title">{{ searchKeyword ? '未找到相关客户' : '暂无客户数据' }}</text>
				<text class="empty-desc" v-if="searchKeyword">请尝试其他关键词</text>
				<text class="empty-desc" v-else>暂无客户数据</text>
			</view>
		</view>
	</view>
</template>

<script>
import clientApi from '../../api/client.js'
import config from '../../utils/config.js'
import pageMixin from '../../utils/page-mixin.js'
import { formatDateTime, formatDate } from '../../utils/date-formatter.js'

export default {
	mixins: [pageMixin],
	
	data() {
		return {
			// 覆盖混入的缓存配置
			cacheType: 'clients',
			cacheKey: 'list',
			
			// 客户列表数据（使用混入的listData）
			clientList: [],
			
			// 筛选状态
			currentFilter: 'all',
			filterOptions: [
				{ key: 'all', label: '全部客户', icon: '👥' },
				{ key: 'vip', label: 'VIP客户', icon: '👑' },
				{ key: 'new', label: '新客户', icon: '🆕' },
				{ key: 'active', label: '活跃客户', icon: '🔥' }
			],
			
			// 搜索状态
			isSearching: false,
			
			// 统计数据
			clientStats: {
				total: 0,
				vip: 0,
				new: 0,
				active: 0
			}
		}
	},
	
	computed: {
		// 当前筛选选项
		currentFilterOption() {
			return this.filterOptions.find(option => option.key === this.currentFilter) || this.filterOptions[0]
		},
		
		// 搜索状态文本
		searchStatusText() {
			if (this.searchLoading) return '搜索中...'
			if (this.searchKeyword) return `搜索"${this.searchKeyword}"`
			return ''
		}
	},
	
	// 页面生命周期回调
	onPageLoad() {
		console.log('客户列表页面初始化')
		this.loadInitialData()
	},
	
	onPageShow() {
		console.log('客户列表页面显示')
		// 混入会自动处理是否需要刷新
	},
	
	methods: {
		/**
		 * 加载初始数据
		 */
		async loadInitialData() {
			try {
				this.showPageLoading('加载客户列表...')
				await this.loadData(true)
			} catch (error) {
				this.handlePageError(error, '加载客户列表')
			} finally {
				this.hidePageLoading()
			}
		},
		
		/**
		 * 加载数据（混入要求的方法）
		 */
		async loadData(isRefresh = false) {
			const params = {
				page: isRefresh ? 1 : this.currentPage,
				per_page: this.pageSize
			}
			
			// 添加筛选条件
			if (this.currentFilter && this.currentFilter !== 'all') {
				params.filter = this.currentFilter
			}
			
			// 使用缓存加载数据
			const response = await this.loadWithCache(
				() => clientApi.getClientList(params),
				params,
				3 * 60 * 1000 // 3分钟缓存
			)
			
			// 处理响应数据
			this.handleDataResponse(response, isRefresh)
		},
		
		/**
		 * 搜索数据（混入要求的方法）
		 */
		async searchData(keyword) {
			if (!keyword || !keyword.trim()) {
				// 清空搜索，重新加载列表
				this.isSearching = false
				this.clientList = []
				await this.loadData(true)
				return
			}
			
			this.isSearching = true
			
			// 搜索不使用缓存，确保结果实时性
			const response = await clientApi.searchClients(keyword.trim())
			
			// 处理搜索结果
			this.handleSearchResponse(response)
		},
		
		/**
		 * 处理数据响应
		 */
		handleDataResponse(response, isRefresh) {
			let newClients = []
			
			// 解析响应数据
			if (response.data && Array.isArray(response.data)) {
				newClients = response.data
			} else if (response.data && response.data.data && Array.isArray(response.data.data)) {
				newClients = response.data.data
			} else {
				newClients = response.data || []
			}
			
			// 更新客户列表
			this.setListData(newClients, isRefresh)
			this.clientList = this.listData
			
			// 更新分页状态
			this.updatePagination(response)
			
			// 更新统计数据
			this.updateClientStats(response)
			
			console.log(`客户数据加载完成: ${newClients.length} 条`)
		},
		
		/**
		 * 处理搜索响应
		 */
		handleSearchResponse(response) {
			let searchResults = []
			
			// 解析搜索结果
			if (response.data && Array.isArray(response.data)) {
				searchResults = response.data
			} else if (response.data && response.data.data && Array.isArray(response.data.data)) {
				searchResults = response.data.data
			} else {
				searchResults = response.data || []
			}
			
			// 搜索结果直接替换列表
			this.clientList = searchResults
			this.listData = searchResults
			this.pageEmpty = searchResults.length === 0
			this.hasMore = false // 搜索结果不支持分页
			
			console.log(`搜索完成: ${searchResults.length} 条结果`)
		},
		
		/**
		 * 更新客户统计数据
		 */
		updateClientStats(response) {
			// 如果响应中包含统计数据，直接使用
			if (response.stats) {
				this.clientStats = { ...this.clientStats, ...response.stats }
				return
			}
			
			// 否则基于当前数据计算
			const total = this.clientList.length
			const vip = this.clientList.filter(client => client.membership_level).length
			const new_clients = this.clientList.filter(client => this.isNewClient(client)).length
			const active = this.clientList.filter(client => this.isActiveClient(client)).length
			
			this.clientStats = { total, vip, new: new_clients, active }
		},
		
		/**
		 * 处理搜索输入
		 */
		onSearchInput(e) {
			const value = e.detail.value
			this.handleSearchInput(value)
		},
		
		/**
		 * 搜索确认
		 */
		onSearchConfirm() {
			this.performSearch()
		},
		
		/**
		 * 清除搜索
		 */
		clearSearch() {
			this.searchKeyword = ''
			this.isSearching = false
			this.refreshData()
		},
		
		/**
		 * 切换筛选条件
		 */
		async switchFilter(filterKey) {
			if (this.currentFilter === filterKey) return
			
			console.log(`切换筛选: ${this.currentFilter} -> ${filterKey}`)
			
			this.currentFilter = filterKey
			this.isSearching = false
			this.searchKeyword = ''
			
			// 清除相关缓存
			this.clearPageCache()
			
			// 重新加载数据
			try {
				this.showListLoading()
				this.currentPage = 1
				this.hasMore = true
				this.clientList = []
				
				await this.loadData(true)
				
				// 显示切换反馈
				const option = this.currentFilterOption
				this.showToast(`已切换到：${option.label}`, 'none', 1000)
				
			} catch (error) {
				this.handleListError(error, '切换筛选')
			} finally {
				this.hideListLoading()
			}
		},
		
		/**
		 * 跳转到客户详情
		 */
		goToClientDetail(client) {
			uni.navigateTo({
				url: `/pages/clients/client-detail?id=${client.id}`
			})
		},
		
		/**
		 * 跳转到代客下单
		 */
		goToProxyOrder(client) {
			uni.navigateTo({
				url: `/pages/proxy-order/proxy-order?clientId=${client.id}&clientName=${encodeURIComponent(client.merchant_name || client.name)}&clientPhone=${client.phone}`
			})
		},
		
		/**
		 * 判断是否为新客户
		 */
		isNewClient(client) {
			if (!client.created_at) return false
			const createTime = new Date(client.created_at)
			const now = new Date()
			const diffDays = (now - createTime) / (1000 * 60 * 60 * 24)
			return diffDays <= 30 // 30天内注册的为新客户
		},
		
		/**
		 * 判断是否为活跃客户
		 */
		isActiveClient(client) {
			// 根据最近订单时间或订单数量判断
			if (client.last_order_date) {
				const lastOrderTime = new Date(client.last_order_date)
				const now = new Date()
				const diffDays = (now - lastOrderTime) / (1000 * 60 * 60 * 24)
				return diffDays <= 30 // 30天内有订单的为活跃客户
			}
			return (client.order_count || 0) > 0
		},
		
		/**
		 * 获取客户头像文字
		 */
		getAvatarText(client) {
			if (client.merchant_name) {
				return client.merchant_name.charAt(0).toUpperCase()
			}
			if (client.name) {
				return client.name.charAt(0).toUpperCase()
			}
			return '客'
		},
		
		/**
		 * 格式化金额
		 */
		formatAmount(amount) {
			if (!amount) return '0.00'
			return parseFloat(amount).toLocaleString('zh-CN', {
				minimumFractionDigits: 2,
				maximumFractionDigits: 2
			})
		},
		
		/**
		 * 获取角色文本
		 */
		getRoleText(role) {
			return config.roles[role] || role
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				active: '正常',
				inactive: '禁用',
				pending: '待审核'
			}
			return statusMap[status] || status
		},
		
		// 获取状态样式类
		getStatusClass(status) {
			const classMap = {
				active: 'status-active',
				inactive: 'status-inactive',
				pending: 'status-pending'
			}
			return classMap[status] || 'status-active'
		},
		
		// 格式化时间
		formatTime(timeStr) {
			return formatDateTime(timeStr)
		},
		
		// 格式化中文时间
		formatChineseTime(timeStr) {
			return formatDateTime(timeStr)
		},
		
		// 拨打客户电话
		callClient(client) {
			uni.makePhoneCall({
				phoneNumber: client.phone,
				success: () => {
					console.log('拨打电话成功')
				},
				fail: (error) => {
					console.error('拨打电话失败:', error)
					uni.showToast({
						title: '拨打电话失败',
						icon: 'none',
						duration: 2000
					})
				}
			})
		},
		
		// 刷新客户列表
		refreshData() {
			this.currentPage = 1
			this.hasMore = true
			this.clientList = []
			this.loadData(true)
		},
		
		// 加载更多
		loadMore() {
			if (this.hasMore && !this.listLoading) {
				this.loadData(false)
			}
		},
		
		// 判断是否为VIP客户
		isVipClient(client) {
			// VIP客户：有会员等级且等级不是默认等级
			return client.membership_level && client.membership_level.is_default === false
		}
	}
}
</script>

<style scoped>
.clients-container {
	background: #f5f5f5;
	min-height: 100vh;
}

/* 固定顶部区域 */
.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #ffffff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 搜索栏 */
.search-section {
	background: #ffffff;
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.search-box {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 0 24rpx;
	height: 80rpx;
}

.search-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
	color: #999999;
}

.search-input {
	flex: 1;
	font-size: 32rpx;
	color: #333333;
}

.clear-icon {
	font-size: 32rpx;
	color: #999999;
	padding: 8rpx;
}

/* 快速筛选 */
.filter-section {
	background: #ffffff;
	padding: 16rpx 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
	white-space: nowrap;
}

.filter-item {
	display: inline-block;
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	background: #f8f9fa;
	margin-right: 16rpx;
	font-size: 28rpx;
	color: #666666;
}

.filter-item.active {
	background: #007AFF;
	color: #ffffff;
}

.filter-text {
	font-size: 28rpx;
}

/* 客户列表 */
.client-list {
	padding: 16rpx;
	padding-top: 200rpx; /* 为固定头部留出空间 */
}

.client-card {
	background: #ffffff;
	border-radius: 12rpx;
	padding: 32rpx;
	margin-bottom: 16rpx;
	border: 1rpx solid #f0f0f0;
}

.client-card:active {
	background: #f8f9fa;
}

.client-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 24rpx;
}

.client-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	background: #007AFF;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
	position: relative;
}

.avatar-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

.status-dot {
	position: absolute;
	bottom: 0;
	right: 0;
	width: 20rpx;
	height: 20rpx;
	border-radius: 10rpx;
	border: 2rpx solid #ffffff;
}

.status-active {
	background: #52c41a;
}

.status-inactive {
	background: #ff4d4f;
}

.status-pending {
	background: #faad14;
}

.client-main-info {
	flex: 1;
}

.client-name-row {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
}

.client-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-right: 16rpx;
}

.client-badges {
	display: flex;
	align-items: center;
}

.badge {
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	color: #ffffff;
	margin-left: 8rpx;
}

.vip-badge {
	background: #52c41a;
}

.new-badge {
	background: #ff4d4f;
}

.client-contact {
	display: flex;
	flex-direction: column;
}

.contact-info {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 8rpx;
}

.client-actions {
	display: flex;
	align-items: center;
}

.action-btn {
	background: none;
	border: none;
	padding: 0;
	margin-left: 16rpx;
}

.call-btn .btn-icon {
	font-size: 32rpx;
	color: #52c41a;
}

.order-btn {
	background: #007AFF;
	color: #ffffff;
	border-radius: 8rpx;
	padding: 12rpx 20rpx;
}

.btn-text {
	font-size: 28rpx;
	color: #ffffff;
}

.client-stats {
	display: flex;
	justify-content: space-between;
	padding-top: 24rpx;
	border-top: 1rpx solid #f0f0f0;
}

.stat-item {
	text-align: center;
	flex: 1;
}

.stat-label {
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 8rpx;
	display: block;
}

.stat-value {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	display: block;
}

/* 加载更多 */
.load-more-section {
	text-align: center;
	padding: 40rpx 0;
}

.load-indicator {
	margin-bottom: 16rpx;
}

.load-text {
	font-size: 28rpx;
	color: #999999;
}

.load-more-btn {
	background: #f8f9fa;
	color: #666666;
	border: 1rpx solid #e9ecef;
	border-radius: 8rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

.load-more-btn:active {
	background: #e9ecef;
}

/* 没有更多数据 */
.no-more-section {
	text-align: center;
	padding: 40rpx 0;
}

.no-more-text {
	font-size: 28rpx;
	color: #999999;
}

/* 加载状态 */
.loading-section {
	text-align: center;
	padding: 120rpx 60rpx;
	padding-top: 320rpx; /* 为固定头部留出空间 */
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.loading-icon {
	font-size: 80rpx;
	margin-bottom: 24rpx;
	opacity: 0.5;
}

.loading-text {
	font-size: 28rpx;
	color: #999999;
}

/* 空状态 */
.empty-section {
	text-align: center;
	padding: 120rpx 60rpx;
	padding-top: 320rpx; /* 为固定头部留出空间 */
}

.empty-content {
	text-align: center;
}

.empty-icon {
	font-size: 80rpx;
	opacity: 0.3;
	display: block;
	margin-bottom: 24rpx;
}

.empty-title {
	font-size: 32rpx;
	color: #666666;
	display: block;
	margin-bottom: 16rpx;
}

.empty-desc {
	font-size: 28rpx;
	color: #999999;
	display: block;
}
</style> 