<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Product\Models\Product;
use App\Product\Models\Category;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 临时禁用外键约束
        DB::statement('SET FOREIGN_KEY_CHECKS=0');
        
        // 清空现有产品数据
        Product::truncate();
        
        // 获取所有分类
        $categories = Category::all();
        
        if ($categories->isEmpty()) {
            $this->command->info('请先运行CategorySeeder添加分类数据');
            return;
        }
        
        // 创建食品类产品
        $foodCategory = $categories->where('name', '食品')->first() ?? $categories->first();
        for ($i = 1; $i <= 20; $i++) {
            Product::create([
                'name' => "食品{$i}",
                'subtitle' => "优质食品{$i}号",
                'price' => rand(500, 10000) / 100,
                'description' => "这是一个高品质的食品产品，产品编号为{$i}，由专业食品工厂生产。",
                'stock' => rand(50, 500),
                'base_unit' => 'kg',
                'unit_conversion_rate' => 1,
                'category_id' => $foodCategory->id,
                'cover_url' => "https://picsum.photos/id/" . ($i + 100) . "/400/300",
                'status' => 1,
                'created_at' => Carbon::now()->subDays(rand(1, 365)),
                'updated_at' => Carbon::now(),
            ]);
        }
        
        // 创建服装类产品
        $clothingCategory = $categories->where('name', '服装')->first() ?? $categories->first();
        for ($i = 1; $i <= 15; $i++) {
            Product::create([
                'name' => "服装{$i}",
                'subtitle' => "时尚服装{$i}号",
                'price' => rand(5000, 100000) / 100,
                'description' => "这是一款时尚的服装产品，产品编号为{$i}，采用高品质面料制作。",
                'stock' => rand(10, 200),
                'base_unit' => 'pcs',
                'unit_conversion_rate' => 1,
                'category_id' => $clothingCategory->id,
                'cover_url' => "https://picsum.photos/id/" . ($i + 200) . "/400/300",
                'status' => 1,
                'created_at' => Carbon::now()->subDays(rand(1, 365)),
                'updated_at' => Carbon::now(),
            ]);
        }
        
        // 创建电子产品
        $electronicsCategory = $categories->where('name', '电子产品')->first() ?? $categories->first();
        for ($i = 1; $i <= 10; $i++) {
            Product::create([
                'name' => "电子产品{$i}",
                'subtitle' => "高科技电子产品{$i}号",
                'price' => rand(100000, 1000000) / 100,
                'description' => "这是一款高科技电子产品，产品编号为{$i}，搭载最新技术。",
                'stock' => rand(5, 50),
                'base_unit' => 'pcs',
                'unit_conversion_rate' => 1,
                'category_id' => $electronicsCategory->id,
                'cover_url' => "https://picsum.photos/id/" . ($i + 300) . "/400/300",
                'status' => 1,
                'created_at' => Carbon::now()->subDays(rand(1, 365)),
                'updated_at' => Carbon::now(),
            ]);
        }
        
        // 创建家居产品
        $homeCategory = $categories->where('name', '家居')->first() ?? $categories->first();
        for ($i = 1; $i <= 15; $i++) {
            Product::create([
                'name' => "家居产品{$i}",
                'subtitle' => "舒适家居{$i}号",
                'price' => rand(20000, 500000) / 100,
                'description' => "这是一款舒适的家居产品，产品编号为{$i}，为您的家居生活增添舒适感。",
                'stock' => rand(3, 30),
                'base_unit' => 'pcs',
                'unit_conversion_rate' => 1,
                'category_id' => $homeCategory->id,
                'cover_url' => "https://picsum.photos/id/" . ($i + 400) . "/400/300",
                'status' => 1,
                'created_at' => Carbon::now()->subDays(rand(1, 365)),
                'updated_at' => Carbon::now(),
            ]);
        }
        
        // 创建一些特价/折扣商品
        for ($i = 1; $i <= 5; $i++) {
            Product::create([
                'name' => "特价商品{$i}",
                'subtitle' => "限时特惠{$i}号",
                'price' => rand(999, 4999) / 100,
                'description' => "这是一款限时特惠商品，产品编号为{$i}，抓紧时间选购吧！",
                'stock' => rand(10, 100),
                'base_unit' => 'pcs',
                'unit_conversion_rate' => 1,
                'category_id' => $categories->random()->id,
                'cover_url' => "https://picsum.photos/id/" . ($i + 500) . "/400/300",
                'status' => 1,
                'created_at' => Carbon::now()->subDays(rand(1, 30)),
                'updated_at' => Carbon::now(),
            ]);
        }
        
        $this->command->info('产品数据添加完成！共添加 ' . (20 + 15 + 10 + 15 + 5) . ' 条记录');
        
        // 重新启用外键约束
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }
}
