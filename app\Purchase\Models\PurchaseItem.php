<?php

namespace App\Purchase\Models;

use App\Product\Models\Product;
use App\Unit\Models\Unit;
use App\Inventory\Models\InventoryBatch;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'purchase_order_id',
        'product_id',
        'quantity',
        'unit_id',
        'unit_price',
        'received_quantity',
        'total_price',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'received_quantity' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    /**
     * 获取所属采购订单
     */
    public function purchaseOrder()
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    /**
     * 获取商品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 获取单位
     */
    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * 获取关联的库存批次
     */
    public function batches()
    {
        return $this->hasMany(InventoryBatch::class);
    }

    /**
     * 获取待收货数量
     */
    public function getPendingQuantityAttribute()
    {
        return $this->quantity - $this->received_quantity;
    }

    /**
     * 是否已完成收货
     */
    public function getIsFullyReceivedAttribute()
    {
        return $this->pending_quantity <= 0;
    }

    /**
     * 添加批次
     * 
     * @param int $inventoryId 库存ID
     * @param float $quantity 数量
     * @param int $unitId 单位ID
     * @param string|null $batchCode 批次编码（null则自动生成）
     * @param string|null $productionDate 生产日期
     * @param string|null $expiryDate 过期日期
     * @param string|null $notes 备注
     * @param int|null $createdBy 创建人ID
     * @return InventoryBatch
     */
    public function addBatch($inventoryId, $quantity, $unitId, $batchCode = null, $productionDate = null, $expiryDate = null, $notes = null, $createdBy = null)
    {
        // 生成批次编码
        if (!$batchCode) {
            $batchCode = InventoryBatch::generateBatchCode();
        }
        
        // 创建新批次
        $batch = InventoryBatch::create([
            'inventory_id' => $inventoryId,
            'purchase_item_id' => $this->id,
            'batch_code' => $batchCode,
            'purchase_price' => $this->unit_price,
            'quantity' => $quantity,
            'initial_quantity' => $quantity,
            'unit_id' => $unitId,
            'production_date' => $productionDate,
            'expiry_date' => $expiryDate,
            'notes' => $notes,
            'created_by' => $createdBy,
        ]);
        
        return $batch;
    }
} 