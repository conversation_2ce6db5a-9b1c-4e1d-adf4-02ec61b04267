/* pages/points/ranking/index.wxss - 积分排行榜页面样式 */

.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* ==================== 页面头部 ==================== */
.header {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  padding: 60rpx 40rpx 40rpx;
  color: white;
  text-align: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* ==================== 类型切换 ==================== */
.type-tabs {
  display: flex;
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 50rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.type-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  border-radius: 40rpx;
  transition: all 0.3s;
}

.type-tab.active {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
}

.tab-icon {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.tab-name {
  font-size: 24rpx;
  font-weight: 500;
}

/* ==================== 我的排名 ==================== */
.my-ranking {
  background: white;
  margin: 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.my-rank-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.my-rank-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.share-btn {
  display: flex;
  align-items: center;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.share-icon {
  margin-right: 8rpx;
}

.my-rank-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rank-info {
  display: flex;
  flex-direction: column;
}

.rank-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 8rpx;
}

.rank-points {
  font-size: 28rpx;
  color: #666;
}

.rank-avatar .avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 4rpx solid #4CAF50;
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* ==================== 排行榜列表 ==================== */
.ranking-list {
  padding: 0 30rpx;
}

/* 前三名 */
.top-three {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.top-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.top-item:last-child {
  border-bottom: none;
}

.top-item.rank-1 {
  background: linear-gradient(90deg, #FFD700 0%, #FFA500 100%);
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  padding: 20rpx;
  color: white;
}

.top-item.rank-2 {
  background: linear-gradient(90deg, #C0C0C0 0%, #A0A0A0 100%);
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  padding: 20rpx;
  color: white;
}

.top-item.rank-3 {
  background: linear-gradient(90deg, #CD7F32 0%, #B8860B 100%);
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  padding: 20rpx;
  color: white;
}

.top-rank {
  font-size: 40rpx;
  margin-right: 20rpx;
  min-width: 60rpx;
  text-align: center;
}

.top-avatar .avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 3rpx solid rgba(255, 255, 255, 0.5);
  margin-right: 20rpx;
}

.top-info {
  flex: 1;
}

.top-name {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.top-points {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 其他排名 */
.other-ranks {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.rank-header {
  background: #f8f9fa;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #eee;
}

.rank-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.rank-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  transition: background-color 0.3s;
}

.rank-item:last-child {
  border-bottom: none;
}

.rank-item:active {
  background-color: #f8f9fa;
}

.rank-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #666;
  min-width: 60rpx;
  text-align: center;
  margin-right: 20rpx;
}

.rank-avatar .avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.rank-info {
  flex: 1;
}

.rank-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.rank-level {
  font-size: 24rpx;
  color: #999;
}

.rank-points {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: bold;
}

/* ==================== 空状态 ==================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
}

/* ==================== 加载更多 ==================== */
.load-more, .no-more {
  text-align: center;
  padding: 30rpx;
}

.load-more-text, .no-more-text {
  font-size: 26rpx;
  color: #999;
}

/* ==================== 底部操作按钮 ==================== */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #eee;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #333;
  border: 2rpx solid #ddd;
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 24rpx;
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 320px) {
  .header-title {
    font-size: 32rpx;
  }
  
  .top-name {
    font-size: 26rpx;
  }
  
  .rank-name {
    font-size: 24rpx;
  }
} 