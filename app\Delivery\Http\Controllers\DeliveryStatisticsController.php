<?php

namespace App\Delivery\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Delivery\Services\DeliveryStatisticsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DeliveryStatisticsController extends Controller
{
    /**
     * 配送统计服务
     *
     * @var DeliveryStatisticsService
     */
    protected $statisticsService;

    /**
     * 构造函数
     *
     * @param DeliveryStatisticsService $statisticsService
     */
    public function __construct(DeliveryStatisticsService $statisticsService)
    {
        $this->statisticsService = $statisticsService;
    }

    /**
     * 获取配送概览统计
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function overview()
    {
        try {
            $data = $this->statisticsService->getOverview();
            return response()->json(ApiResponse::success($data));
        } catch (\Exception $e) {
            Log::error('获取配送概览统计失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('获取配送概览统计失败', 500), 500);
        }
    }

    /**
     * 获取订单趋势数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function orderTrend(Request $request)
    {
        try {
            $data = $this->statisticsService->getOrderTrend($request);
            return response()->json(ApiResponse::success($data));
        } catch (\Exception $e) {
            Log::error('获取配送趋势数据失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('获取配送趋势数据失败', 500), 500);
        }
    }
} 