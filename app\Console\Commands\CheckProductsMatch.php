<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CheckProductsMatch extends Command
{
    protected $signature = 'check:products-match';
    protected $description = '检查老数据库商品与新数据库商品的匹配情况';

    public function handle()
    {
        $this->info("正在检查商品匹配情况...");
        
        // 获取老数据库的商品
        $oldProducts = DB::connection('mysql_old')
            ->table('zjhj_bd_goods_warehouse as w')
            ->join('zjhj_bd_goods as g', 'w.id', '=', 'g.goods_warehouse_id')
            ->where('w.is_delete', 0)
            ->where('g.is_delete', 0)
            ->select('w.name', 'g.price as sale_price', 'w.original_price')
            ->get();
        
        // 获取新数据库的商品名称
        $newProductNames = DB::table('products')
            ->pluck('name')
            ->toArray();
        
        $this->info("老数据库商品总数: " . $oldProducts->count());
        $this->info("新数据库商品总数: " . count($newProductNames));
        
        // 检查匹配情况
        $matched = 0;
        $unmatched = [];
        $matchedProducts = [];
        
        foreach ($oldProducts as $product) {
            if (in_array($product->name, $newProductNames)) {
                $matched++;
                $matchedProducts[] = $product->name;
            } else {
                $unmatched[] = $product->name;
            }
        }
        
        $this->info("✅ 可以匹配的商品: {$matched} 个");
        $this->info("❌ 无法匹配的商品: " . count($unmatched) . " 个");
        
        // 显示前10个无法匹配的商品
        if (count($unmatched) > 0) {
            $this->warn("前10个无法匹配的商品:");
            foreach (array_slice($unmatched, 0, 10) as $index => $name) {
                $this->line("  " . ($index + 1) . ". {$name}");
            }
            
            if (count($unmatched) > 10) {
                $this->line("  ... 还有 " . (count($unmatched) - 10) . " 个");
            }
        }
        
        // 显示前5个可以匹配的商品
        if (count($matchedProducts) > 0) {
            $this->info("前5个可以匹配的商品:");
            foreach (array_slice($matchedProducts, 0, 5) as $index => $name) {
                $this->line("  " . ($index + 1) . ". {$name}");
            }
        }
        
        return 0;
    }
} 