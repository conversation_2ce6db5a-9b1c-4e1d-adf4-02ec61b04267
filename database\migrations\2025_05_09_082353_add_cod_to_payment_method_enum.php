<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 由于Laravel不直接支持修改enum字段，我们需要使用原始SQL
        DB::statement("ALTER TABLE `orders` CHANGE `payment_method` `payment_method` ENUM('wechat', 'alipay', 'bank', 'cash', 'cod') NULL COMMENT '支付方式'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复到原始状态
        DB::statement("ALTER TABLE `orders` CHANGE `payment_method` `payment_method` ENUM('wechat', 'alipay', 'bank', 'cash') NULL COMMENT '支付方式'");
    }
};
