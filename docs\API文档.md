# Laravel 10 API 文档

本文档描述了系统的所有API接口，包括认证、用户管理、商品管理、仓库管理、库存管理、订单管理等功能。所有接口都采用REST风格设计，并使用JSON格式进行数据交换。

## API 通用说明

### 请求格式

- 所有API请求需要在Header中添加`Content-Type: application/json`
- 除了登录和注册，所有API都需要在Header中添加`Authorization: Bearer {token}`进行认证
- POST和PUT请求的参数都以JSON格式放在请求体中

### 响应格式
111
所有API响应统一返回以下格式：

```json
{
  "code": 200,               // 状态码，200表示成功，其他值表示错误
  "message": "操作成功",      // 提示信息
  "data": {                  // 返回的数据，错误时可能为null
    // 具体数据
  }
}
```

### 分页响应格式

列表类API支持分页，返回格式如下：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,       // 当前页码
    "data": [                // 当前页数据
      // 具体数据列表
    ],
    "from": 1,               // 起始序号
    "last_page": 10,         // 最后页码
    "per_page": 15,          // 每页数量
    "to": 15,                // 结束序号
    "total": 150             // 总记录数
  }
}
```

### 错误处理

当API调用出错时，将返回错误状态码和错误信息。常见的错误状态码包括：

- `401`: 未授权（身份验证失败）
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 请求参数验证失败
- `500`: 服务器内部错误

更多错误响应示例，请参考文档末尾的[错误响应说明](#错误响应说明)章节。

## 目录

- [认证 API](#认证-api)
- [用户管理 API](#用户管理-api)
- [商品管理 API](#商品管理-api)
- [仓库管理 API](#仓库管理-api)
- [库存管理 API](#库存管理-api)
- [客户管理 API](#客户管理-api)
- [员工管理 API](#员工管理-api)
- [订单管理 API](#订单管理-api)
- [配送管理 API](#配送管理-api)
- [支付 API](#支付-api)
- [错误响应说明](#错误响应说明)

## 认证 API

### 用户注册

**接口地址**：`POST /api/auth/register`

**请求参数**：

| 参数名    | 类型   | 必须 | 描述       |
|---------|--------|-----|-----------|
| name    | string | 是   | 用户名称    |
| email   | string | 是   | 用户邮箱    |
| password| string | 是   | 用户密码    |

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/auth/register \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "测试用户",
    "email": "<EMAIL>",
    "password": "password123"
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
    "token_type": "Bearer",
    "user": {
      "id": 1,
      "name": "测试用户",
      "email": "<EMAIL>",
      "role": "user",
      "created_at": "2023-06-15T10:30:00.000000Z",
      "updated_at": "2023-06-15T10:30:00.000000Z"
    }
  }
}
```

### 用户登录

**接口地址**：`POST /api/auth/login`

**请求参数**：

| 参数名    | 类型   | 必须 | 描述       |
|---------|--------|-----|-----------|
| email   | string | 是   | 用户邮箱    |
| password| string | 是   | 用户密码    |

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/auth/login \
  -H 'Content-Type: application/json' \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
    "token_type": "Bearer",
    "user": {
      "id": 1,
      "name": "测试用户",
      "email": "<EMAIL>",
      "role": "user",
      "created_at": "2023-06-15T10:30:00.000000Z",
      "updated_at": "2023-06-15T10:30:00.000000Z"
    }
  }
}
```

### 用户登出

**接口地址**：`POST /api/auth/logout`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/auth/logout \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

### 获取当前用户信息

**接口地址**：`GET /api/auth/user`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/auth/user \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "测试用户",
    "email": "<EMAIL>",
    "role": "user",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-15T10:30:00.000000Z"
  }
}
```

## 用户管理 API

### 获取用户列表

**接口地址**：`GET /api/users`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/users \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "name": "管理员",
        "email": "<EMAIL>",
        "role": "admin",
        "created_at": "2023-06-15T10:30:00.000000Z",
        "updated_at": "2023-06-15T10:30:00.000000Z"
      }
      // 更多用户...
    ],
    "from": 1,
    "last_page": 1,
    "per_page": 10,
    "to": 1,
    "total": 1
  }
}
```

### 获取用户详情

**接口地址**：`GET /api/users/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/users/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "管理员",
    "email": "<EMAIL>",
    "role": "admin",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-15T10:30:00.000000Z"
  }
}
```

### 更新用户信息

**接口地址**：`PUT /api/users/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名    | 类型   | 必须 | 描述       |
|---------|--------|-----|-----------|
| name    | string | 否   | 用户名称    |
| email   | string | 否   | 用户邮箱    |
| password| string | 否   | 用户密码    |
| role    | string | 否   | 用户角色    |

**请求示例**：

```bash
curl -X PUT \
  http://localhost:8000/api/users/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "新名称",
    "role": "admin"
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "用户更新成功",
  "data": {
    "id": 1,
    "name": "新名称",
    "email": "<EMAIL>",
    "role": "admin",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-16T11:30:00.000000Z"
  }
}
```

### 删除用户

**接口地址**：`DELETE /api/users/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X DELETE \
  http://localhost:8000/api/users/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "用户删除成功",
  "data": null
}
```

## 商品管理 API

### 获取商品列表

**接口地址**：`GET /api/products`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/products \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "name": "测试商品",
        "price": "99.99",
        "description": "这是一个测试商品",
        "stock": 100,
        "base_unit": "kg",
        "unit_conversion_rate": "1.00",
        "created_at": "2023-06-15T10:30:00.000000Z",
        "updated_at": "2023-06-15T10:30:00.000000Z"
      }
      // 更多商品...
    ],
    "from": 1,
    "last_page": 1,
    "per_page": 10,
    "to": 1,
    "total": 1
  }
}
```

### 创建商品

**接口地址**：`POST /api/products`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名               | 类型    | 必须 | 描述         |
|--------------------|---------|-----|-------------|
| name               | string  | 是   | 商品名称      |
| price              | numeric | 是   | 商品价格      |
| description        | string  | 否   | 商品描述      |
| stock              | numeric | 否   | 库存量        |
| base_unit          | string  | 是   | 基本单位(kg,pcs,g,lb) |
| unit_conversion_rate | numeric | 是 | 单位转换率     |

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/products \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "测试商品",
    "price": 99.99,
    "description": "这是一个测试商品",
    "stock": 100,
    "base_unit": "kg",
    "unit_conversion_rate": 1
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "商品创建成功",
  "data": {
    "id": 1,
    "name": "测试商品",
    "price": "99.99",
    "description": "这是一个测试商品",
    "stock": 100,
    "base_unit": "kg",
    "unit_conversion_rate": "1.00",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-15T10:30:00.000000Z"
  }
}
```

### 获取商品详情

**接口地址**：`GET /api/products/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/products/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "product": {
      "id": 1,
      "name": "测试商品",
      "price": "99.99",
      "description": "这是一个测试商品",
      "stock": 100,
      "base_unit": "kg",
      "unit_conversion_rate": "1.00",
      "created_at": "2023-06-15T10:30:00.000000Z",
      "updated_at": "2023-06-15T10:30:00.000000Z",
      "warehouses": [
        {
          "id": 1,
          "location": "测试仓库",
          "total_stock": 50,
          "created_at": "2023-06-15T10:30:00.000000Z",
          "updated_at": "2023-06-15T10:30:00.000000Z",
          "pivot": {
            "product_id": 1,
            "warehouse_id": 1,
            "stock": 50,
            "unit": "kg"
          }
        }
      ]
    },
    "units_stock": {
      "kg": 100,
      "g": 100000,
      "lb": 220.46,
      "pcs": 100
    }
  }
}
```

### 更新商品

**接口地址**：`PUT /api/products/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名               | 类型    | 必须 | 描述         |
|--------------------|---------|-----|-------------|
| name               | string  | 否   | 商品名称      |
| price              | numeric | 否   | 商品价格      |
| description        | string  | 否   | 商品描述      |
| stock              | numeric | 否   | 库存量        |
| base_unit          | string  | 否   | 基本单位(kg,pcs,g,lb) |
| unit_conversion_rate | numeric | 否 | 单位转换率     |

**请求示例**：

```bash
curl -X PUT \
  http://localhost:8000/api/products/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "更新后的商品",
    "price": 199.99,
    "description": "这是更新后的商品描述"
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "商品更新成功",
  "data": {
    "id": 1,
    "name": "更新后的商品",
    "price": "199.99",
    "description": "这是更新后的商品描述",
    "stock": 100,
    "base_unit": "kg",
    "unit_conversion_rate": "1.00",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-16T11:30:00.000000Z"
  }
}
```

### 删除商品

**接口地址**：`DELETE /api/products/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X DELETE \
  http://localhost:8000/api/products/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "商品删除成功",
  "data": null
}
```

## 仓库管理 API

### 获取仓库列表

**接口地址**：`GET /api/warehouses`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/warehouses \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "location": "北京仓库",
        "total_stock": 200,
        "created_at": "2023-06-15T10:30:00.000000Z",
        "updated_at": "2023-06-15T10:30:00.000000Z"
      }
      // 更多仓库...
    ],
    "from": 1,
    "last_page": 1,
    "per_page": 10,
    "to": 1,
    "total": 1
  }
}
```

### 创建仓库

**接口地址**：`POST /api/warehouses`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名     | 类型   | 必须 | 描述       |
|----------|--------|-----|-----------|
| location | string | 是   | 仓库位置    |

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/warehouses \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "location": "北京仓库"
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "仓库创建成功",
  "data": {
    "id": 1,
    "location": "北京仓库",
    "total_stock": 0,
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-15T10:30:00.000000Z"
  }
}
```

### 获取仓库详情

**接口地址**：`GET /api/warehouses/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/warehouses/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "location": "北京仓库",
    "total_stock": 200,
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-15T10:30:00.000000Z",
    "products": [
      {
        "id": 1,
        "name": "测试商品",
        "price": "99.99",
        "description": "这是一个测试商品",
        "stock": 100,
        "base_unit": "kg",
        "unit_conversion_rate": "1.00",
        "created_at": "2023-06-15T10:30:00.000000Z",
        "updated_at": "2023-06-15T10:30:00.000000Z",
        "pivot": {
          "warehouse_id": 1,
          "product_id": 1,
          "stock": 50,
          "unit": "kg"
        }
      }
      // 更多商品...
    ]
  }
}
```

### 更新仓库信息

**接口地址**：`PUT /api/warehouses/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名     | 类型   | 必须 | 描述       |
|----------|--------|-----|-----------|
| location | string | 否   | 仓库位置    |

**请求示例**：

```bash
curl -X PUT \
  http://localhost:8000/api/warehouses/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "location": "更新后的北京仓库"
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "仓库信息更新成功",
  "data": {
    "id": 1,
    "location": "更新后的北京仓库",
    "total_stock": 200,
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-16T11:30:00.000000Z"
  }
}
```

### 删除仓库

**接口地址**：`DELETE /api/warehouses/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X DELETE \
  http://localhost:8000/api/warehouses/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "仓库删除成功",
  "data": null
}
```

### 更新仓库中商品库存

**接口地址**：`PUT /api/warehouses/{warehouseId}/product/{productId}/stock`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名  | 类型    | 必须 | 描述       |
|--------|---------|-----|-----------|
| stock  | numeric | 是   | 库存量     |
| unit   | string  | 是   | 单位(kg,pcs,g,lb) |

**请求示例**：

```bash
curl -X PUT \
  http://localhost:8000/api/warehouses/1/product/1/stock \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "stock": 100,
    "unit": "kg"
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "商品库存更新成功",
  "data": {
    "id": 1,
    "warehouse_id": 1,
    "product_id": 1,
    "stock": 100,
    "unit": "kg",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-16T11:30:00.000000Z"
  }
}
```

## 库存管理 API

### 获取仓库中所有商品库存

**接口地址**：`GET /api/warehouses/{warehouseId}/inventory`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/warehouses/1/inventory \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "warehouse_id": 1,
        "product_id": 1,
        "stock": 50,
        "unit": "kg",
        "created_at": "2023-06-15T10:30:00.000000Z",
        "updated_at": "2023-06-15T10:30:00.000000Z",
        "product": {
          "id": 1,
          "name": "测试商品",
          "price": "99.99",
          "description": "这是一个测试商品",
          "stock": 100,
          "base_unit": "kg",
          "unit_conversion_rate": "1.00",
          "created_at": "2023-06-15T10:30:00.000000Z",
          "updated_at": "2023-06-15T10:30:00.000000Z"
        }
      }
      // 更多库存...
    ],
    "from": 1,
    "last_page": 1,
    "per_page": 10,
    "to": 1,
    "total": 1
  }
}
```

### 获取仓库中特定商品库存

**接口地址**：`GET /api/warehouses/{warehouseId}/inventory/{productId}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/warehouses/1/inventory/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "inventory": {
      "id": 1,
      "warehouse_id": 1,
      "product_id": 1,
      "stock": 50,
      "unit": "kg",
      "created_at": "2023-06-15T10:30:00.000000Z",
      "updated_at": "2023-06-15T10:30:00.000000Z"
    },
    "stock_in_base_unit": 50,
    "units_stock": {
      "kg": 50,
      "g": 50000,
      "lb": 110.23,
      "pcs": 50
    },
    "product": {
      "id": 1,
      "name": "测试商品",
      "price": "99.99",
      "description": "这是一个测试商品",
      "stock": 100,
      "base_unit": "kg",
      "unit_conversion_rate": "1.00",
      "created_at": "2023-06-15T10:30:00.000000Z",
      "updated_at": "2023-06-15T10:30:00.000000Z"
    }
  }
}
```

### 更新仓库中商品库存

**接口地址**：`PUT /api/warehouses/{warehouseId}/inventory/{productId}`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名  | 类型    | 必须 | 描述       |
|--------|---------|-----|-----------|
| stock  | numeric | 是   | 库存量     |
| unit   | string  | 是   | 单位(kg,pcs,g,lb) |

**请求示例**：

```bash
curl -X PUT \
  http://localhost:8000/api/warehouses/1/inventory/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "stock": 50,
    "unit": "kg"
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "商品库存更新成功",
  "data": {
    "id": 1,
    "warehouse_id": 1,
    "product_id": 1,
    "stock": 50,
    "unit": "kg",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-16T11:30:00.000000Z"
  }
}
```

### 添加商品到仓库

**接口地址**：`POST /api/warehouses/{warehouseId}/inventory`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名     | 类型    | 必须 | 描述       |
|-----------|---------|-----|-----------|
| product_id| integer | 是   | 商品ID     |
| stock     | numeric | 是   | 库存量     |
| unit      | string  | 是   | 单位(kg,pcs,g,lb) |

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/warehouses/1/inventory \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "product_id": 1,
    "stock": 50,
    "unit": "kg"
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "商品已添加到仓库",
  "data": {
    "id": 1,
    "warehouse_id": 1,
    "product_id": 1,
    "stock": 50,
    "unit": "kg",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-15T10:30:00.000000Z"
  }
}
```

### 从仓库中移除商品

**接口地址**：`DELETE /api/warehouses/{warehouseId}/inventory/{productId}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X DELETE \
  http://localhost:8000/api/warehouses/1/inventory/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "商品已从仓库中移除",
  "data": null
}
```

## 客户管理 API

### 获取客户列表

**接口地址**：`GET /api/customers`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/customers \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "name": "张三",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "address": "北京市海淀区",
        "created_at": "2023-06-15T10:30:00.000000Z",
        "updated_at": "2023-06-15T10:30:00.000000Z"
      }
      // 更多客户...
    ],
    "from": 1,
    "last_page": 1,
    "per_page": 10,
    "to": 1,
    "total": 1
  }
}
```

### 创建客户

**接口地址**：`POST /api/customers`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名   | 类型   | 必须 | 描述       |
|---------|--------|-----|-----------|
| name    | string | 是   | 客户名称    |
| phone   | string | 是   | 客户电话    |
| email   | string | 否   | 客户邮箱    |
| address | string | 否   | 客户地址    |

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/customers \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "张三",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "address": "北京市海淀区"
}'
```

**成功返回示例**：

```json
{
  "code": 200,
  "message": "客户创建成功",
  "data": {
    "id": 1,
    "name": "张三",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "address": "北京市海淀区",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-15T10:30:00.000000Z"
  }
}
```

**错误返回示例**：

```json
{
  "code": 422,
  "message": "The name field is required.",
  "data": null
}
```

**常见错误代码**：

| 错误代码 | 描述                   |
|---------|------------------------|
| 401     | 未授权（无效的token）   |
| 422     | 请求参数验证失败        |
| 500     | 服务器内部错误          |

### 获取客户详情

**接口地址**：`GET /api/customers/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/customers/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "张三",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "address": "北京市海淀区",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-15T10:30:00.000000Z"
  }
}
```

### 更新客户信息

**接口地址**：`PUT /api/customers/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名   | 类型   | 必须 | 描述       |
|---------|--------|-----|-----------|
| name    | string | 否   | 客户名称    |
| phone   | string | 否   | 客户电话    |
| email   | string | 否   | 客户邮箱    |
| address | string | 否   | 客户地址    |

**请求示例**：

```bash
curl -X PUT \
  http://localhost:8000/api/customers/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "李四",
    "phone": "13900139000",
    "address": "北京市朝阳区"
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "客户信息更新成功",
  "data": {
    "id": 1,
    "name": "李四",
    "phone": "13900139000",
    "email": "<EMAIL>",
    "address": "北京市朝阳区",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-16T11:30:00.000000Z"
  }
}
```

### 删除客户

**接口地址**：`DELETE /api/customers/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X DELETE \
  http://localhost:8000/api/customers/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "客户删除成功",
  "data": null
}
```

## 员工管理 API

### 员工登录

**接口地址**：`POST /api/employee/auth/login`

**请求参数**：

| 参数名    | 类型   | 必须 | 描述       |
|---------|--------|-----|-----------|
| username| string | 是   | 员工用户名  |
| password| string | 是   | 员工密码    |

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/employee/auth/login \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "admin",
    "password": "password"
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
    "token_type": "Bearer",
    "employee": {
      "id": 1,
      "name": "系统管理员",
      "username": "admin",
      "phone": "13800000000",
      "position": "系统管理员",
      "role": "admin",
      "created_at": "2023-06-15T10:30:00.000000Z",
      "updated_at": "2023-06-15T10:30:00.000000Z"
    }
  }
}
```

### 获取当前员工信息

**接口地址**：`GET /api/employee/auth/me`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/employee/auth/me \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "系统管理员",
    "username": "admin",
    "phone": "13800000000",
    "position": "系统管理员",
    "role": "admin",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-15T10:30:00.000000Z"
  }
}
```

### 员工登出

**接口地址**：`POST /api/employee/auth/logout`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/employee/auth/logout \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

### 获取员工列表

**接口地址**：`GET /api/employees`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：(查询参数)

| 参数名    | 类型   | 必须 | 描述       |
|---------|--------|-----|-----------|
| keyword | string | 否   | 搜索关键词(匹配姓名、用户名、电话、职位) |
| per_page| integer| 否   | 每页数量，默认10 |

**请求示例**：

```bash
curl -X GET \
  'http://localhost:8000/api/employees?keyword=张三&per_page=15' \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "name": "张三",
        "username": "zhangsan",
        "phone": "13800000001",
        "position": "市场部经理",
        "role": "manager",
        "created_at": "2023-06-15T10:30:00.000000Z",
        "updated_at": "2023-06-15T10:30:00.000000Z"
      },
      // 更多员工数据...
    ],
    "from": 1,
    "last_page": 5,
    "per_page": 15,
    "to": 15,
    "total": 68
  }
}
```

### 创建员工

**接口地址**：`POST /api/employees`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名    | 类型   | 必须 | 描述       |
|---------|--------|-----|-----------|
| name    | string | 是   | 员工姓名    |
| username| string | 是   | 登录用户名(唯一) |
| password| string | 是   | 登录密码，至少6位 |
| phone   | string | 否   | 电话号码    |
| position| string | 是   | 职位       |
| role    | string | 否   | 角色(admin/manager/staff)，默认staff |

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/employees \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "李四",
    "username": "lisi",
    "password": "password123",
    "phone": "13900000002",
    "position": "财务部主管",
    "role": "manager"
  }'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "员工创建成功",
  "data": {
    "id": 10,
    "name": "李四",
    "username": "lisi",
    "phone": "13900000002",
    "position": "财务部主管",
    "role": "manager",
    "created_at": "2023-06-20T15:30:00.000000Z",
    "updated_at": "2023-06-20T15:30:00.000000Z"
  }
}
```

### 获取单个员工信息

**接口地址**：`GET /api/employees/{employee}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/employees/10 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 10,
    "name": "李四",
    "username": "lisi",
    "phone": "13900000002",
    "position": "财务部主管",
    "role": "manager",
    "created_at": "2023-06-20T15:30:00.000000Z",
    "updated_at": "2023-06-20T15:30:00.000000Z"
  }
}
```

### 更新员工信息

**接口地址**：`PUT /api/employees/{employee}`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名    | 类型   | 必须 | 描述       |
|---------|--------|-----|-----------|
| name    | string | 否   | 员工姓名    |
| username| string | 否   | 登录用户名(唯一) |
| password| string | 否   | 登录密码，至少6位 |
| phone   | string | 否   | 电话号码    |
| position| string | 否   | 职位       |
| role    | string | 否   | 角色(admin/manager/staff) |

**请求示例**：

```bash
curl -X PUT \
  http://localhost:8000/api/employees/10 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "李四(已更新)",
    "phone": "13911112222",
    "position": "财务总监"
  }'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "员工信息更新成功",
  "data": {
    "id": 10,
    "name": "李四(已更新)",
    "username": "lisi",
    "phone": "13911112222",
    "position": "财务总监",
    "role": "manager",
    "created_at": "2023-06-20T15:30:00.000000Z",
    "updated_at": "2023-06-20T16:45:00.000000Z"
  }
}
```

### 删除员工

**接口地址**：`DELETE /api/employees/{employee}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X DELETE \
  http://localhost:8000/api/employees/10 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "员工删除成功",
  "data": null
}
```

**错误响应**：

如果尝试删除当前登录的账号，会返回以下错误:

```json
{
  "code": 400,
  "message": "不能删除当前登录的账号",
  "data": null
}
```

## 订单管理 API

### 获取订单列表

**接口地址**：`GET /api/orders`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/orders \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "customer_id": 1,
        "status": "pending",
        "total": "299.97",
        "created_at": "2023-06-15T10:30:00.000000Z",
        "updated_at": "2023-06-15T10:30:00.000000Z",
        "customer": {
          "id": 1,
          "name": "张三",
          "phone": "13800138000",
          "email": "<EMAIL>",
          "address": "北京市海淀区"
        },
        "items": [
          {
            "id": 1,
            "order_id": 1,
            "product_id": 1,
            "quantity": 3,
            "unit_price": "99.99",
            "product": {
              "id": 1,
              "name": "测试商品",
              "price": "99.99"
            }
          }
        ]
      }
      // 更多订单...
    ],
    "from": 1,
    "last_page": 1,
    "per_page": 10,
    "to": 1,
    "total": 1
  }
}
```

### 创建订单

**接口地址**：`POST /api/orders`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名       | 类型    | 必须 | 描述       |
|-------------|---------|-----|-----------|
| customer_id | integer | 是   | 客户ID     |
| items       | array   | 是   | 订单项列表  |
| items.*.product_id | integer | 是 | 商品ID  |
| items.*.quantity | numeric | 是 | 商品数量  |

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/orders \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "customer_id": 1,
    "items": [
      {
        "product_id": 1,
        "quantity": 3
      }
    ]
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "id": 1,
    "customer_id": 1,
    "status": "pending",
    "total": "299.97",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-15T10:30:00.000000Z",
    "items": [
      {
        "id": 1,
        "order_id": 1,
        "product_id": 1,
        "quantity": 3,
        "unit_price": "99.99"
      }
    ]
  }
}
```

### 获取订单详情

**接口地址**：`GET /api/orders/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/orders/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "customer_id": 1,
    "status": "pending",
    "total": "299.97",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-15T10:30:00.000000Z",
    "customer": {
      "id": 1,
      "name": "张三",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "address": "北京市海淀区"
    },
    "items": [
      {
        "id": 1,
        "order_id": 1,
        "product_id": 1,
        "quantity": 3,
        "unit_price": "99.99",
        "product": {
          "id": 1,
          "name": "测试商品",
          "price": "99.99"
        }
      }
    ],
    "delivery": {
      "id": 1,
      "order_id": 1,
      "status": "pending",
      "deliverer_id": null,
      "created_at": "2023-06-15T10:30:00.000000Z",
      "updated_at": "2023-06-15T10:30:00.000000Z"
    }
  }
}
```

### 更新订单状态

**接口地址**：`PUT /api/orders/{id}/status`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名  | 类型   | 必须 | 描述       |
|--------|--------|-----|-----------|
| status | string | 是   | 订单状态(pending, processing, completed, cancelled) |

**请求示例**：

```bash
curl -X PUT \
  http://localhost:8000/api/orders/1/status \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "status": "processing"
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "订单状态更新成功",
  "data": {
    "id": 1,
    "customer_id": 1,
    "status": "processing",
    "total": "299.97",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-16T11:30:00.000000Z"
  }
}
```

### 取消订单

**接口地址**：`POST /api/orders/{id}/cancel`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/orders/1/cancel \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "订单已取消",
  "data": {
    "id": 1,
    "customer_id": 1,
    "status": "cancelled",
    "total": "299.97",
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-16T11:30:00.000000Z"
  }
}
```

## 配送管理 API

### 获取配送列表

**接口地址**：`GET /api/deliveries`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/deliveries \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "order_id": 1,
        "status": "pending",
        "deliverer_id": 1,
        "created_at": "2023-06-15T10:30:00.000000Z",
        "updated_at": "2023-06-15T10:30:00.000000Z",
        "order": {
          "id": 1,
          "customer_id": 1,
          "status": "processing",
          "total": "299.97"
        },
        "deliverer": {
          "id": 1,
          "name": "王五",
          "position": "配送员"
        }
      }
      // 更多配送...
    ],
    "from": 1,
    "last_page": 1,
    "per_page": 10,
    "to": 1,
    "total": 1
  }
}
```

### 分配配送员

**接口地址**：`POST /api/deliveries/order/{orderId}/assign`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名       | 类型    | 必须 | 描述       |
|-------------|---------|-----|-----------|
| deliverer_id| integer | 是   | 配送员ID   |

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/deliveries/order/1/assign \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "deliverer_id": 1
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "配送员分配成功",
  "data": {
    "id": 1,
    "order_id": 1,
    "status": "assigned",
    "deliverer_id": 1,
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-16T11:30:00.000000Z"
  }
}
```

### 获取配送详情

**接口地址**：`GET /api/deliveries/{id}`

**请求头**：
- `Authorization: Bearer {token}`

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/deliveries/1 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "order_id": 1,
    "status": "assigned",
    "deliverer_id": 1,
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-16T11:30:00.000000Z",
    "order": {
      "id": 1,
      "customer_id": 1,
      "status": "processing",
      "total": "299.97",
      "customer": {
        "id": 1,
        "name": "张三",
        "phone": "13800138000",
        "address": "北京市海淀区"
      }
    },
    "deliverer": {
      "id": 1,
      "name": "王五",
      "position": "配送员",
      "phone": "13700137000"
    }
  }
}
```

### 更新配送状态

**接口地址**：`PUT /api/deliveries/{id}/status`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名  | 类型   | 必须 | 描述       |
|--------|--------|-----|-----------|
| status | string | 是   | 配送状态(pending, assigned, in_transit, delivered) |

**请求示例**：

```bash
curl -X PUT \
  http://localhost:8000/api/deliveries/1/status \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "status": "in_transit"
}'
```

**返回示例**：

```json
{
  "code": 200,
  "message": "配送状态更新成功",
  "data": {
    "id": 1,
    "order_id": 1,
    "status": "in_transit",
    "deliverer_id": 1,
    "created_at": "2023-06-15T10:30:00.000000Z",
    "updated_at": "2023-06-16T11:30:00.000000Z"
  }
}
```

## 单位转换示例

系统支持多种单位之间的转换，主要包括以下单位：

- kg (公斤) - 基本重量单位
- g (克) - 1kg = 1000g
- lb (磅) - 1kg ≈ 2.2046lb 或 1lb ≈ 0.4536kg
- pcs (件) - 与基本单位的转换关系由商品的unit_conversion_rate决定

### 单位转换计算示例

1. kg 转 g：
   - 10kg = 10 * 1000 = 10000g

2. kg 转 lb：
   - 10kg = 10 * 2.2046 = 22.046lb

3. g 转 kg：
   - 5000g = 5000 * 0.001 = 5kg

4. lb 转 kg：
   - 11.023lb = 11.023 * 0.4536 = 5kg

5. 自定义转换率示例：
   - 如果一个商品的base_unit为"kg"，unit_conversion_rate为2
   - 则1pcs = 2kg
   - 库存为10pcs时，基本单位库存为20kg

## 错误响应说明

所有API在出错时会返回统一格式的错误响应：

```json
{
  "code": <错误码>,
  "message": "<错误详细信息>",
  "data": null
}
```

### 通用错误码

| 错误代码 | 描述                      |
|---------|---------------------------|
| 401     | 未授权（无效或过期的token）|
| 403     | 权限不足                   |
| 404     | 资源不存在                 |
| 422     | 请求参数验证失败           |
| 500     | 服务器内部错误             |

### 常见错误示例

1. 认证失败：

```json
{
  "code": 401,
  "message": "Unauthenticated.",
  "data": null
}
```

2. 参数验证失败：

```json
{
  "code": 422,
  "message": "The name field is required.",
  "data": null
}
```

3. 资源不存在：

```json
{
  "code": 404,
  "message": "Resource not found.",
  "data": null
}
```

4. 业务逻辑错误：

```json
{
  "code": 422,
  "message": "此仓库中没有该商品库存",
  "data": null
}
```

5. 权限错误：

```json
{
  "code": 403,
  "message": "您没有权限执行此操作",
  "data": null
}
```

## 总结

本API文档全面描述了系统的所有功能接口，包括：

1. 认证和用户管理
2. 商品管理与多单位支持
3. 仓库管理
4. 库存管理与单位转换
5. 客户管理
6. 员工管理
7. 订单管理
8. 配送管理
9. 支付处理

系统的特色在于支持商品多单位功能和库存管理，可以在不同仓库使用不同单位记录商品库存，并自动进行单位换算，满足复杂的库存管理需求。

## 支付 API

本系统集成了微信支付功能，支持在线支付订单。

### 创建支付订单

**接口地址**：`POST /api/payment/order`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名      | 类型    | 必须 | 描述       |
|------------|---------|-----|-----------|
| order_id   | string  | 是  | 订单编号    |
| total_amount | numeric | 是 | 支付金额（元） |
| body       | string  | 否  | 商品描述    |

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/payment/order \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "order_id": "ORD202306150001",
    "total_amount": 299.97,
    "body": "商品购买"
}'
```

**成功返回示例**：

```json
{
  "code": 200,
  "message": "支付订单创建成功",
  "data": {
    "code_url": "weixin://wxpay/bizpayurl?pr=XXXXXXX",
    "out_trade_no": "ORD202306150001",
    "prepay_id": "wx201606081644320983f69e830341986000"
  }
}
```

**错误返回示例**：

```json
{
  "code": 422,
  "message": "支付参数不正确",
  "data": null
}
```

### 支付回调通知

**接口地址**：`POST /api/payment/notify`

**说明**：
此接口由微信支付服务器调用，不需要客户端直接访问。当用户完成支付后，微信服务器会向此接口发送支付结果通知。

**请求参数**：
由微信支付服务器以XML格式发送，主要包含以下字段：

| 参数名        | 描述                |
|--------------|---------------------|
| return_code  | 返回状态码           |
| result_code  | 业务结果码           |
| out_trade_no | 商户订单号           |
| transaction_id | 微信支付订单号      |
| total_fee    | 订单金额（单位：分）   |
| time_end     | 支付完成时间          |

**返回格式**：
系统处理完支付结果后，需要向微信服务器返回处理结果：

```xml
<xml>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <return_msg><![CDATA[OK]]></return_msg>
</xml>
```

### 查询支付状态

**接口地址**：`GET /api/payment/query/{order_id}`

**请求头**：
- `Authorization: Bearer {token}`

**路径参数**：

| 参数名    | 类型   | 必须 | 描述      |
|----------|--------|-----|-----------|
| order_id | string | 是  | 订单编号   |

**请求示例**：

```bash
curl -X GET \
  http://localhost:8000/api/payment/query/ORD202306150001 \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
```

**成功返回示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "order_id": "ORD202306150001",
    "payment_status": "paid",
    "transaction_id": "4200001514202306156785111457",
    "paid_time": "2023-06-15 15:30:45",
    "total_amount": "299.97"
  }
}
```

### 取消支付

**接口地址**：`POST /api/payment/cancel`

**请求头**：
- `Authorization: Bearer {token}`

**请求参数**：

| 参数名    | 类型   | 必须 | 描述      |
|----------|--------|-----|-----------|
| order_id | string | 是  | 订单编号   |

**请求示例**：

```bash
curl -X POST \
  http://localhost:8000/api/payment/cancel \
  -H 'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "order_id": "ORD202306150001"
}'
```

**成功返回示例**：

```json
{
  "code": 200,
  "message": "支付已取消",
  "data": {
    "order_id": "ORD202306150001",
    "status": "cancelled"
  }
}
``` 