<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use App\Employee\Models\Employee;

class EmployeeAuthController extends Controller
{
    /**
     * 登录表单视图
     *
     * @return \Illuminate\View\View
     */
    public function showLoginForm()
    {
        return view('auth.employee.login');
    }

    /**
     * Get the guard to be used during authentication.
     *
     * @return \Illuminate\Contracts\Auth\StatefulGuard
     */
    protected function guard()
    {
        return Auth::guard('employee');
    }

    /**
     * 用户名字段
     *
     * @return string
     */
    public function username()
    {
        return 'username';
    }

    /**
     * 处理登录请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function login(Request $request)
    {
        $this->validateLogin($request);

        // 尝试登录
        if ($this->attemptLogin($request)) {
            return $this->sendLoginResponse($request);
        }

        return $this->sendFailedLoginResponse($request);
    }

    /**
     * 验证登录请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateLogin(Request $request)
    {
        $request->validate([
            $this->username() => 'required|string',
            'password' => 'required|string',
        ]);
    }

    /**
     * 尝试登录用户
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function attemptLogin(Request $request)
    {
        return $this->guard()->attempt(
            $request->only($this->username(), 'password'),
            $request->filled('remember')
        );
    }

    /**
     * 登录成功响应
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    protected function sendLoginResponse(Request $request)
    {
        $request->session()->regenerate();

        return redirect()->intended($this->redirectPath());
    }

    /**
     * 登录失败响应
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function sendFailedLoginResponse(Request $request)
    {
        throw ValidationException::withMessages([
            $this->username() => [trans('auth.failed')],
        ]);
    }

    /**
     * 重定向路径
     *
     * @return string
     */
    protected function redirectPath()
    {
        return $this->redirectTo();
    }

    /**
     * 注销登录
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function logout(Request $request)
    {
        $this->guard()->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('employee.login');
    }

    /**
     * 登录成功后的重定向路径
     *
     * @return string
     */
    protected function redirectTo()
    {
        // 根据员工角色重定向到不同的后台
        /** @var Employee|null $employee */
        $employee = auth('employee')->user();

        if (!$employee) {
            return route('employee.login');
        }

        if ($employee->role === Employee::ROLE_ADMIN || $employee->role === Employee::ROLE_MANAGER || $employee->role === Employee::ROLE_STAFF) {
            return route('admin.dashboard');
        } elseif ($employee->role === Employee::ROLE_CRM_AGENT) {
            return route('crm.dashboard');
        } elseif ($employee->role === Employee::ROLE_DELIVERY) {
            return route('delivery.dashboard');
        } elseif ($employee->role === Employee::ROLE_WAREHOUSE_MANAGER) {
            return route('warehouse.dashboard');
        } else {
            return route('employee.login');
        }
    }
} 