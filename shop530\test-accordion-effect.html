<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手风琴效果测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { display: flex; gap: 20px; }
        .left-panel { width: 300px; border: 1px solid #ddd; padding: 10px; }
        .right-panel { flex: 1; border: 1px solid #ddd; padding: 10px; }
        
        .category-item { 
            padding: 10px; 
            border: 1px solid #eee; 
            margin: 5px 0; 
            cursor: pointer;
            background: #f8f8f8;
        }
        .category-item.active { background: #e8f4fd; }
        .category-item:hover { background: #f0f0f0; }
        
        .subcategory-container { 
            overflow: hidden; 
            transition: height 0.3s ease;
            background: #fff;
        }
        .subcategory-item { 
            padding: 8px 20px; 
            border-bottom: 1px solid #f0f0f0; 
            cursor: pointer;
        }
        .subcategory-item.active { background: #fff2e8; color: #ff6b35; }
        .subcategory-item:hover { background: #f9f9f9; }
        
        .third-category-container {
            background: #fff2e8;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .third-category-item {
            display: inline-block;
            padding: 5px 15px;
            margin: 5px;
            background: #f5f5f5;
            border-radius: 15px;
            cursor: pointer;
        }
        .third-category-item.active {
            background: #ff6b35;
            color: white;
        }
        
        .expand-icon { float: right; }
        .arrow { 
            width: 0; height: 0; 
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #666;
            transition: transform 0.3s ease;
        }
        .expand-icon.expanded .arrow { transform: rotate(180deg); }
    </style>
</head>
<body>
    <h1>分类页面手风琴效果测试</h1>
    
    <div class="test-container">
        <!-- 左侧分类导航 -->
        <div class="left-panel">
            <h3>左侧分类导航（一级+二级）</h3>
            <div id="categoryList"></div>
        </div>
        
        <!-- 右侧内容区域 -->
        <div class="right-panel">
            <h3>右侧内容区域</h3>
            <div id="currentCategory">当前分类: 未选择</div>
            <div id="thirdCategories"></div>
            <div id="productArea">商品列表区域...</div>
        </div>
    </div>

    <script>
        // 模拟分类数据
        const categories = [
            {
                id: 1,
                name: "猪生鲜",
                hasChildren: true,
                isExpanded: false,
                children_data: [
                    { id: 4, name: "鲜货", children_data: [{ id: 108, name: "123" }] },
                    { id: 5, name: "半熟", children_data: [] },
                    { id: 21, name: "冻品", children_data: [] }
                ]
            },
            {
                id: 77,
                name: "鸡类",
                hasChildren: true,
                isExpanded: false,
                children_data: [
                    { id: 106, name: "鲜鸡", children_data: [] },
                    { id: 79, name: "鸡爪类", children_data: [] }
                ]
            }
        ];

        let state = {
            categories: [...categories],
            expandedCategoryId: null,
            activeCategoryId: null,
            activeSubCategoryId: null,
            activeThirdCategoryId: null,
            thirdCategories: []
        };

        function renderCategories() {
            const html = state.categories.map(category => `
                <div class="category-item ${state.activeCategoryId === category.id ? 'active' : ''}" 
                     onclick="handleCategoryClick(${category.id}, 'main')">
                    ${category.name}
                    ${category.hasChildren ? `
                        <div class="expand-icon ${category.isExpanded ? 'expanded' : ''}">
                            <div class="arrow"></div>
                        </div>
                    ` : ''}
                </div>
                <div class="subcategory-container" style="height: ${category.isExpanded && category.children_data.length ? (category.children_data.length * 40) + 'px' : '0'}">
                    ${category.children_data.map(subItem => `
                        <div class="subcategory-item ${state.activeSubCategoryId === subItem.id ? 'active' : ''}"
                             onclick="handleCategoryClick(${subItem.id}, 'sub', ${category.id})">
                            ${subItem.name}
                        </div>
                    `).join('')}
                </div>
            `).join('');
            
            document.getElementById('categoryList').innerHTML = html;
        }

        function renderThirdCategories() {
            if (state.thirdCategories.length > 0) {
                const html = `
                    <div class="third-category-container">
                        <h4>三级分类:</h4>
                        ${state.thirdCategories.map(item => `
                            <span class="third-category-item ${state.activeThirdCategoryId === item.id ? 'active' : ''}"
                                  onclick="handleThirdCategoryClick(${item.id})">
                                ${item.name}
                            </span>
                        `).join('')}
                    </div>
                `;
                document.getElementById('thirdCategories').innerHTML = html;
            } else {
                document.getElementById('thirdCategories').innerHTML = '';
            }
        }

        function handleCategoryClick(id, type, parentId = null) {
            if (type === 'main') {
                const category = state.categories.find(c => c.id === id);
                
                // 更新选中状态
                state.activeCategoryId = id;
                state.activeSubCategoryId = null;
                state.activeThirdCategoryId = null;
                state.thirdCategories = [];
                
                // 切换展开状态
                if (category.hasChildren) {
                    toggleExpansion(id);
                }
                
                document.getElementById('currentCategory').textContent = `当前分类: ${category.name}`;
                
            } else if (type === 'sub') {
                const parentCategory = state.categories.find(c => c.id === parentId);
                const subCategory = parentCategory.children_data.find(c => c.id === id);
                
                // 更新选中状态
                state.activeCategoryId = parentId;
                state.activeSubCategoryId = id;
                state.activeThirdCategoryId = null;
                
                // 获取三级分类
                if (subCategory.children_data && subCategory.children_data.length > 0) {
                    state.thirdCategories = subCategory.children_data.map(thirdCat => ({
                        ...thirdCat,
                        parentName: subCategory.name
                    }));
                } else {
                    state.thirdCategories = [];
                }
                
                document.getElementById('currentCategory').textContent = `当前分类: ${parentCategory.name} > ${subCategory.name}`;
            }
            
            renderCategories();
            renderThirdCategories();
        }

        function handleThirdCategoryClick(id) {
            const thirdCategory = state.thirdCategories.find(c => c.id === id);
            state.activeThirdCategoryId = id;
            
            const parentCategory = state.categories.find(c => c.id === state.activeCategoryId);
            const subCategory = parentCategory.children_data.find(c => c.id === state.activeSubCategoryId);
            
            document.getElementById('currentCategory').textContent = 
                `当前分类: ${parentCategory.name} > ${subCategory.name} > ${thirdCategory.name}`;
            
            renderThirdCategories();
        }

        function toggleExpansion(categoryId) {
            if (state.expandedCategoryId === categoryId) {
                // 收起
                state.categories = state.categories.map(cat => ({
                    ...cat,
                    isExpanded: false
                }));
                state.expandedCategoryId = null;
            } else {
                // 展开
                state.categories = state.categories.map(cat => ({
                    ...cat,
                    isExpanded: cat.id === categoryId
                }));
                state.expandedCategoryId = categoryId;
            }
        }

        // 初始化
        renderCategories();
        
        console.log('✅ 手风琴效果测试页面已加载');
        console.log('📋 测试步骤:');
        console.log('1. 点击一级分类 → 应该展开/收起二级分类');
        console.log('2. 点击二级分类 → 应该在右侧显示三级分类（如果有）');
        console.log('3. 点击三级分类 → 应该更新当前分类显示');
    </script>
</body>
</html>
