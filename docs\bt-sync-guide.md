# 宝塔面板最简单同步方案

## 方案一：使用宝塔Git管理器（推荐）

### 1. 安装Git管理器
- 宝塔面板 → 软件商店 → 搜索"Git" → 安装

### 2. 配置Git仓库
- 网站 → 选择您的站点 → Git管理
- 填入仓库地址：https://github.com/your-username/your-repo.git
- 设置分支：main
- 启用自动拉取

### 3. 设置Webhook（可选）
- 在GitHub仓库设置中添加Webhook
- URL：http://your-domain.com/git-webhook
- 每次推送代码自动更新

## 方案二：计划任务同步

### 1. 上传同步脚本
- 文件管理 → 上传 simple-sync.sh 到网站根目录
- 右键 → 权限 → 设置为 755

### 2. 设置计划任务
- 计划任务 → 添加任务
- 任务类型：Shell脚本
- 任务名称：代码同步
- 执行周期：每5分钟
- 脚本内容：
```bash
cd /www/wwwroot/your-domain && ./simple-sync.sh
```

## 方案三：一键部署页面

### 1. 上传部署页面
- 上传 deploy.php 到网站根目录

### 2. 访问部署
- 浏览器访问：http://your-domain.com/deploy.php?key=your-secret-key
- 点击即可部署

## 数据库同步

### 使用宝塔面板数据库管理
1. 数据库 → 选择数据库 → 备份
2. 下载备份文件到本地
3. 本地开发完成后，导出数据库
4. 上传并导入到服务器

### 或使用Laravel Migration
```bash
# 本地创建迁移
php artisan make:migration add_new_feature

# 推送代码后，服务器自动运行迁移
# （通过同步脚本中的 php artisan migrate --force）
``` 