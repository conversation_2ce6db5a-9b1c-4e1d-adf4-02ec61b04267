<?php

namespace App\Crm\Services;

use App\Crm\Models\CrmAgent;
use App\Models\User;
use App\Employee\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ClientAssignmentService
{
    /**
     * 分配客户给CRM专员
     *
     * @param array $data
     * @return User
     */
    public function assignClient(array $data)
    {
        // 查找客户
        $user = User::findOrFail($data['user_id']);
        
        // 查找CRM专员（员工）
        $employee = Employee::findOrFail($data['agent_id']);
        if (!$employee->isCrmAgent()) {
            Log::error('指定员工不是CRM专员', [
                'employee_id' => $employee->id,
                'role' => $employee->role
            ]);
            throw new \Exception('指定员工不是CRM专员');
        }
        
        // 检查CRM专员是否可以接受新客户
        $crmAgent = $employee->crmAgentInfo;
        if ($crmAgent && method_exists($crmAgent, 'canAcceptClients') && !$crmAgent->canAcceptClients()) {
            Log::error('CRM专员当前无法接受新客户', [
                'agent_id' => $employee->id,
                'clients_count' => $crmAgent->clients_count,
                'max_clients' => $crmAgent->max_clients
            ]);
            throw new \Exception('该CRM专员当前无法接受新客户');
        }
        
        // 直接更新用户的crm_agent_id字段
        $user->crm_agent_id = $employee->id;
        $user->save();
        
        // 更新CRM专员的客户计数
        if ($crmAgent) {
            $crmAgent->updateClientsCount();
        }
        
        return $user;
    }
    
    /**
     * 批量分配客户
     *
     * @param array $data
     * @return array
     */
    public function batchAssignClients(array $data)
    {
        // 查找CRM专员
        $employee = Employee::findOrFail($data['agent_id']);
        if (!$employee->isCrmAgent()) {
            throw new \Exception('指定员工不是CRM专员');
        }
        
        $crmAgent = $employee->crmAgentInfo;
        
        $result = [
            'successful' => [],
            'failed' => []
        ];
        
        // 开始事务
        DB::beginTransaction();
        
        try {
            $userIds = $data['user_ids'];
            
            // 检查客户数量是否超过专员最大客户数
            if ($crmAgent) {
                $currentCount = $crmAgent->clients_count;
                $remainingSlots = $crmAgent->max_clients - $currentCount;
                
                if (count($userIds) > $remainingSlots) {
                    throw new \Exception('客户数量超过专员可接受的最大客户数');
                }
            }
            
            // 为每个客户设置CRM专员
            foreach ($userIds as $userId) {
                try {
                    $user = User::findOrFail($userId);
                    $user->crm_agent_id = $employee->id;
                    $user->save();
                    
                    $result['successful'][] = $userId;
                } catch (\Exception $e) {
                    $result['failed'][] = [
                        'user_id' => $userId,
                        'error' => $e->getMessage()
                    ];
                    
                    Log::error('批量分配客户失败', [
                        'user_id' => $userId,
                        'agent_id' => $employee->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            // 更新CRM专员的客户计数
            if ($crmAgent) {
                $crmAgent->updateClientsCount();
            }
            
            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 取消客户分配
     *
     * @param array $data
     * @return User
     */
    public function unassignClient(array $data)
    {
        // 查找用户
        $user = User::findOrFail($data['user_id']);
        
        // 验证用户是否与此CRM专员关联
        if ($user->crm_agent_id != $data['agent_id']) {
            throw new \Exception('用户未与指定CRM专员关联');
        }
        
        // 清除用户的CRM专员关联
        $user->crm_agent_id = null;
        $user->save();
        
        // 更新专员的客户计数
        $employee = Employee::findOrFail($data['agent_id']);
        if ($employee && $employee->crmAgentInfo) {
            $employee->crmAgentInfo->updateClientsCount();
        }
        
        return $user;
    }
    
    /**
     * 批量取消客户分配
     *
     * @param int $agentId
     * @param array $userIds
     * @return array
     */
    public function batchUnassignClients($agentId, array $userIds)
    {
        $result = [
            'successful' => [],
            'failed' => []
        ];
        
        // 开始事务
        DB::beginTransaction();
        
        try {
            foreach ($userIds as $userId) {
                try {
                    $user = User::findOrFail($userId);
                    
                    // 验证用户是否与此CRM专员关联
                    if ($user->crm_agent_id != $agentId) {
                        throw new \Exception('用户未与指定CRM专员关联');
                    }
                    
                    // 清除用户的CRM专员关联
                    $user->crm_agent_id = null;
                    $user->save();
                    
                    $result['successful'][] = $userId;
                } catch (\Exception $e) {
                    $result['failed'][] = [
                        'user_id' => $userId,
                        'error' => $e->getMessage()
                    ];
                    
                    Log::error('批量取消客户分配失败', [
                        'user_id' => $userId,
                        'agent_id' => $agentId,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            // 更新专员的客户计数
            $employee = Employee::findOrFail($agentId);
            if ($employee && $employee->crmAgentInfo) {
                $employee->crmAgentInfo->updateClientsCount();
            }
            
            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 获取客户的CRM专员列表
     *
     * @param int $userId
     * @return array
     */
    public function getClientAgents($userId)
    {
        $user = User::findOrFail($userId);
        
        if (!$user->crm_agent_id) {
            return [];
        }
        
        $employee = Employee::with(['crmAgentInfo'])->find($user->crm_agent_id);
        
        if (!$employee) {
            return [];
        }
        
        return [$employee];
    }
    
    /**
     * 获取CRM专员的客户列表
     *
     * @param int $agentId
     * @param Request $request
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getAgentClients($agentId, Request $request)
    {
        $employee = Employee::findOrFail($agentId);
        
        $query = User::where('crm_agent_id', $employee->id);
        
        // 筛选和排序
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }
        
        // 排序
        if ($request->has('sort') && $request->sort) {
            $direction = $request->has('direction') && $request->direction === 'desc' ? 'desc' : 'asc';
            $query->orderBy($request->sort, $direction);
        } else {
            $query->latest();
        }
        
        return $query->paginate($request->input('per_page', 15));
    }
    
    /**
     * 获取未分配的客户列表
     *
     * @param Request $request
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getUnassignedClients(Request $request)
    {
        $query = User::whereNull('crm_agent_id');
        
        // 筛选和排序
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }
        
        // 排序
        if ($request->has('sort') && $request->sort) {
            $direction = $request->has('direction') && $request->direction === 'desc' ? 'desc' : 'asc';
            $query->orderBy($request->sort, $direction);
        } else {
            $query->latest();
        }
        
        return $query->paginate($request->input('per_page', 15));
    }
} 