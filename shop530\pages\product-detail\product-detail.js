// 商品详情页 - 1:1还原设计图
const api = require('../../utils/api');
const { isLoggedIn } = require('../../utils/login-state-manager');

Page({
  data: {
    // 商品信息
    product: null,
    productId: null,
    
    // 图片轮播
    currentImageIndex: 0,
    images: [],
    
    // 状态
    loading: true,
    imageLoaded: false,
    
    // 用户操作
    isFavorite: false,
    quantity: 1,
    
    // 推荐商品
    recommendProducts: [],
    
    // 标签页
    activeTab: 0, // 0: 商品详情, 1: 购买记录
    
    // 购物车
    cartCount: 0,
    
    // 配送信息
    deliveryInfo: {
      time: '最快 明天 9:00-10:00',
      fee: '实付满￥40减免基础配送费'
    },
    
    // 系统信息
    statusBarHeight: 44, // 默认状态栏高度
    
    // 登录状态
    isLoggedIn: false,
    
    // 富文本内容
    hasRichContent: false,
    processedRichContent: '',
    loadError: false,
    errorMessage: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('📱 商品详情页加载 - 1:1还原设计图:', options);
    
    // 获取系统信息
    this.getSystemInfo();
    
    const productId = options.id;
    if (productId) {
      this.setData({ productId: parseInt(productId) });
      this.loadProductDetail();
      this.loadRecommendProducts();
    } else {
      wx.showToast({
        title: '商品信息错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('📱 商品详情页显示 - 1:1设计图还原');
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 检查收藏状态
    this.checkFavoriteStatus();
    
    // 更新购物车数量
    this.updateCartCount();
    
    // 监听购物车变化
    this.setupCartListener();
    
    // 初始化登录状态监听
    this.initLoginStateListener();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 移除购物车监听
    this.removeCartListener();
    
    // 移除登录状态监听
    this.removeLoginStateListener();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 移除购物车监听
    this.removeCartListener();
    
    // 移除登录状态监听
    this.removeLoginStateListener();
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight || 44;
      
      this.setData({ statusBarHeight });
      
      console.log('📱 系统信息:', {
        statusBarHeight,
        platform: systemInfo.platform,
        model: systemInfo.model
      });
    } catch (error) {
      console.error('获取系统信息失败:', error);
    }
  },

  /**
   * 设置购物车监听器
   */
  setupCartListener() {
    // 添加购物车状态监听器
    const { addListener, CartEvents } = require('../../utils/cart-unified');

    // 监听购物车数量变化
    this.cartListener = addListener(CartEvents.COUNT, (data) => {
      console.log('🛒 商品详情页收到购物车数量变化:', data);
      this.setData({ cartCount: data.count || 0 });
    });

    // 监听商品添加
    this.addListener = addListener(CartEvents.ADD, (data) => {
      console.log('🛒 商品详情页收到商品添加:', data);
      this.updateCartCount();
    });

    // 监听商品更新
    this.updateListener = addListener(CartEvents.UPDATE, (data) => {
      console.log('🛒 商品详情页收到商品更新:', data);
      this.updateCartCount();
    });

    // 初始更新
    this.updateCartCount();
  },

  /**
   * 移除购物车监听器
   */
  removeCartListener() {
    const { removeListener } = require('../../utils/cart-unified');

    if (this.cartListener) {
      removeListener(this.cartListener);
      this.cartListener = null;
    }

    if (this.addListener) {
      removeListener(this.addListener);
      this.addListener = null;
    }

    if (this.updateListener) {
      removeListener(this.updateListener);
      this.updateListener = null;
    }
  },

  /**
   * 更新购物车数量
   */
  async updateCartCount() {
    try {
      const { getCartCount } = require('../../utils/cart-unified');
      const cartCount = await getCartCount();
      this.setData({ cartCount });
      console.log('🛒 商品详情页购物车数量更新:', cartCount);
    } catch (error) {
      console.error('更新购物车数量失败:', error);
      this.setData({ cartCount: 0 });
    }
  },

  /**
   * 加载商品详情
   */
  async loadProductDetail() {
    try {
      this.setData({ loading: true });
      
      // API调用获取商品详情
      const product = await this.getProductById(this.data.productId);
      
      if (product) {
        // 检查是否是加载失败的备用商品对象
        if (product.loading_failed) {
          console.log('⚠️ 使用备用商品对象，显示错误提示');
          this.setData({ 
            loading: false,
            loadError: true,
            errorMessage: '商品信息加载失败，请稍后重试'
          });
          
          // 显示错误提示
          wx.showToast({
            title: '商品信息加载失败',
            icon: 'none',
            duration: 2000
          });
          
          return;
        }
        
        // 处理商品轮播图
        const images = this.processProductImages(product);
        
        // 检查商品详情和描述是否包含HTML内容
        const detailContent = product.detail || '';
        const descContent = product.description || '';
        
        // 优先使用detail字段，如果为空则使用description字段
        const contentToCheck = detailContent || descContent;
        
        const hasRichContent = typeof contentToCheck === 'string' && 
                              contentToCheck.indexOf('<') !== -1 && 
                              contentToCheck.indexOf('>') !== -1;
        

        
                // 处理富文本图片适配 - 使用正则替换添加响应式样式
        let processedRichContent = contentToCheck || '暂无详细介绍';
        
        if (hasRichContent && processedRichContent) {
          // 为所有img标签添加响应式样式
          processedRichContent = processedRichContent.replace(
            /\<img/gi, 
            '<img style="width:100%;height:auto;max-width:100%;display:block;margin:10px auto;"'
          );
        }
        
        // 🔥 新增：根据最小起购数量设置默认数量
        const minSaleQuantity = product.min_sale_quantity || 1;
        const defaultQuantity = Math.max(this.data.quantity || 1, minSaleQuantity);
        
        console.log('🔧 商品详情页设置默认数量:', {
          product_id: product.id,
          product_name: product.name,
          min_sale_quantity: minSaleQuantity,
          current_quantity: this.data.quantity,
          default_quantity: defaultQuantity
        });

        this.setData({
          product: {
            ...product,
            // 移除价格格式化逻辑，交给price-display组件处理
            stock: product.stock || 0,
            sales: product.sales || 0,
            tags: product.tags || [],
            specifications: product.specifications || []
            // 不在这里处理HTML内容，使用processedRichContent统一处理
          },
          hasRichContent,
          processedRichContent,
          images,
          currentImageIndex: 0, // 重置轮播图索引
          quantity: defaultQuantity, // 🔥 设置默认数量为最小起购数量
          loading: false,
          loadError: false
        });
        
        console.log('📝 商品详情内容:', {
          detail: product.detail,
          description: product.description,
          processedDetail: this.processHtmlContent(product.detail)
        });
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: product.name || '商品详情'
        });
        
        // 保存到最近查看的商品缓存中
        this.saveToRecentViewed(product);
        
        console.log('✅ 商品详情加载完成:', product.name);
      } else {
        throw new Error('商品不存在');
      }
    } catch (error) {
      console.error('❌ 加载商品详情失败:', error);
      
      this.setData({ 
        loading: false,
        loadError: true,
        errorMessage: error.message || '商品加载失败'
      });
      
      wx.showToast({
        title: error.message || '商品加载失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 保存商品到最近查看缓存
   */
  saveToRecentViewed(product) {
    try {
      if (!product || !product.id) return;
      
      // 获取现有缓存
      let recentProducts = wx.getStorageSync('recentViewedProducts') || [];
      
      // 确保是数组
      if (!Array.isArray(recentProducts)) recentProducts = [];
      
      // 移除已存在的相同商品
      recentProducts = recentProducts.filter(p => p.id !== product.id);
      
      // 添加到数组开头
      recentProducts.unshift({
        id: product.id,
        name: product.name,
        price: product.price,
        image: this.getProductMainImage(product),
        updated_at: new Date().toISOString()
      });
      
      // 限制最大数量
      if (recentProducts.length > 50) {
        recentProducts = recentProducts.slice(0, 50);
      }
      
      // 保存回缓存
      wx.setStorageSync('recentViewedProducts', recentProducts);
      console.log('✅ 保存到最近查看商品缓存');
    } catch (error) {
      console.warn('保存最近查看商品失败:', error);
    }
  },

  /**
   * 加载推荐商品
   */
  async loadRecommendProducts() {
    try {
      console.log('📡 加载推荐商品...');
      
      // 获取推荐商品列表
      const recommendProducts = await this.getRecommendProducts();
      
      // 处理推荐商品的图片字段
      const processedProducts = recommendProducts.map(product => {
        return {
          ...product,
          // 确保有图片字段，优先级：image > main_image > cover_image > thumbnail
          image: this.getProductMainImage(product)
        };
      });
      
      this.setData({ recommendProducts: processedProducts });
      
      console.log('✅ 推荐商品加载完成:', processedProducts.length, '个商品');
    } catch (error) {
      console.error('加载推荐商品失败:', error);
    }
  },

  /**
   * 获取商品主图
   */
  getProductMainImage(product) {
    try {
      // 1. 如果有images数组（ProductImage对象数组），优先选择主图
      if (product.images && Array.isArray(product.images) && product.images.length > 0) {
        // 先查找标记为主图的图片
        const mainImage = product.images.find(img => 
          img && typeof img === 'object' && img.is_main && img.status !== false
        );
        
        if (mainImage && mainImage.url) {
          return mainImage.url;
        }
        
        // 如果没有主图，取第一张启用的图片
        const firstActiveImage = product.images.find(img => {
          if (!img) return false;
          
          if (typeof img === 'string' && img.trim() !== '') {
            return true;
          }
          
          if (typeof img === 'object') {
            const url = img.url || img.path || img.src || img.image;
            const isActive = img.status !== false;
            return url && typeof url === 'string' && url.trim() !== '' && isActive;
          }
          
          return false;
        });
        
        if (firstActiveImage) {
          if (typeof firstActiveImage === 'string') {
            return firstActiveImage;
          }
          if (typeof firstActiveImage === 'object') {
            return firstActiveImage.url || firstActiveImage.path || firstActiveImage.src || firstActiveImage.image;
          }
        }
      }
      
      // 2. 尝试使用单个图片字段
      const imageFields = ['image', 'main_image', 'cover_image', 'thumbnail'];
      for (const field of imageFields) {
        if (product[field] && typeof product[field] === 'string' && product[field].trim() !== '') {
          return product[field];
        }
      }
      
      // 3. 返回默认占位图
      return '/images/placeholder-product.png';
      
    } catch (error) {
      console.warn('获取商品主图失败:', error);
      return '/images/placeholder-product.png';
    }
  },

  /**
   * 获取推荐商品
   */
  async getRecommendProducts() {
    try {
      console.log('🔍 开始获取推荐商品...');
      
      // 优先尝试从商品列表API获取推荐商品
      try {
        // 获取当前商品信息以确定分类
        const currentProduct = this.data.product;
        let categoryId = 0; // 默认热门分类
        
        if (currentProduct && currentProduct.category_id) {
          categoryId = currentProduct.category_id;
        }
        
        const result = await api.request.get('/public/products', { 
          category_id: categoryId,
          limit: 8,
          sort: 'sales_count',
          order: 'desc'
        });
        
        if (result && result.data && result.data.data && result.data.data.length > 0) {
          // 过滤掉当前商品，取前6个
          const filtered = result.data.data.filter(p => p.id !== this.data.productId);
          const recommended = filtered.slice(0, 6);
          console.log('✅ API获取推荐商品成功:', recommended.length, '个');
          return recommended;
        }
      } catch (apiError) {
        console.warn('⚠️ API获取推荐商品失败，使用备用方案:', apiError.message);
      }
      
      // 备用方案1：从分类商品中随机选择
      console.log('🔄 使用备用方案1：从分类商品中选择推荐商品...');
      
      try {
        const categoryProducts = await api.getCategoryProducts({
          categoryId: 0, // 热门分类
          page: 1,
          pageSize: 20
        });
        
        if (categoryProducts && categoryProducts.list && categoryProducts.list.length > 0) {
          // 过滤掉当前商品，随机选择4个
          const filtered = categoryProducts.list.filter(p => p.id !== this.data.productId);
          if (filtered.length > 0) {
            const shuffled = filtered.sort(() => 0.5 - Math.random());
            const selected = shuffled.slice(0, 4);
            console.log('✅ 从分类商品中获取推荐商品成功:', selected.length, '个');
            return selected;
          }
        }
      } catch (categoryError) {
        console.warn('⚠️ 从分类商品获取推荐失败:', categoryError.message);
      }
      
      // 备用方案2：返回空数组
      console.log('🔄 使用备用方案2：返回空推荐商品数据...');
      return [];
      
    } catch (error) {
      console.error('❌ 获取推荐商品完全失败:', error);
      return [];
    }
  },

  /**
   * 获取商品详情 - 优先使用API，失败时查找列表
   */
  async getProductById(productId) {
    try {
      console.log('📡 获取商品详情:', productId);
      
      // 首先尝试使用API工具类的专用方法获取商品详情
      try {
        console.log('尝试使用API工具类的getProductDetail方法...');
        const productResult = await api.api.getProductDetail(productId);
        if (productResult) {
          console.log('✅ 商品API获取成功:', productResult.name || '未知商品名');
          return productResult;
        }
      } catch (apiError) {
        console.warn('商品API获取失败:', apiError.message);
      }
      
      // 备用方案：尝试直接请求公共API路径
      const publicApiPaths = [
        'public/products/' + productId,  // 标准公共API，不带前导斜杠
        'products/' + productId,         // 无前缀路径
        'wechat/mp/products/' + productId // 微信小程序专用API
      ];
      
      // 依次尝试不同的API路径
      for (const path of publicApiPaths) {
        try {
          console.log(`尝试API路径: ${path}`);
          
          // 添加错误处理和超时设置
          const result = await api.request.get(path, {}, {
            showError: false,   // 不显示错误提示
            timeout: 8000,      // 增加超时时间
            requireAuth: false, // 公共API不需要认证
            ignoreServerError: true // 忽略服务器内部错误
          });
          
          // 即使后端返回错误状态码，只要有数据也尝试使用
          if (result && result.data) {
            console.log('✅ API获取商品详情成功:', result.data.name || '未知商品名');
            return result.data;
          }
        } catch (pathError) {
          console.warn(`API路径 ${path} 获取失败:`, pathError.message || '未知错误');
        }
      }
      
      // 所有API路径都失败，尝试从缓存恢复
      try {
        const cachedProducts = wx.getStorageSync('recentViewedProducts') || [];
        const cachedProduct = cachedProducts.find(p => p.id == productId);
        
        if (cachedProduct) {
          console.log('✅ 从缓存恢复商品数据:', cachedProduct.name);
          return cachedProduct;
        }
      } catch (cacheError) {
        console.warn('缓存恢复失败:', cacheError.message);
      }
      
      // 所有方法都失败，抛出错误
      throw new Error('商品不存在');
    } catch (error) {
      console.error('获取商品详情失败:', error);
      
      // 返回一个备用的商品对象，标记为加载失败
      return {
        id: productId,
        name: '加载失败的商品',
        price: 0,
        loading_failed: true,
        error_message: error.message || '未知错误'
      };
    }
  },

  /**
   * 处理商品轮播图
   */
  processProductImages(product) {
    try {
      let images = [];
      
      // 1. 优先使用商品的轮播图数组（后端ProductImage模型数组）
      if (product.images && Array.isArray(product.images) && product.images.length > 0) {
        images = product.images
          .filter(img => {
            // 过滤有效的图片对象
            if (!img) return false;
            
            // 如果是字符串，直接检查
            if (typeof img === 'string') {
              return img.trim() !== '';
            }
            
            // 如果是ProductImage对象，检查url字段和status
            if (typeof img === 'object') {
              const url = img.url || img.path || img.src || img.image;
              const isActive = img.status !== false; // 检查图片是否启用
              return url && typeof url === 'string' && url.trim() !== '' && isActive;
            }
            
            return false;
          })
          .sort((a, b) => {
            // 按sort字段排序，主图优先
            if (typeof a === 'object' && typeof b === 'object') {
              // 主图优先
              if (a.is_main && !b.is_main) return -1;
              if (!a.is_main && b.is_main) return 1;
              // 然后按sort排序
              return (a.sort || 0) - (b.sort || 0);
            }
            return 0;
          })
          .map(img => {
            // 统一转换为字符串URL
            if (typeof img === 'string') {
              return img;
            }
            if (typeof img === 'object') {
              return img.url || img.path || img.src || img.image;
            }
            return img;
          });
        console.log('✅ 使用商品轮播图数组:', images.length, '张图片', product.images);
      }
      
      // 2. 如果没有轮播图数组，尝试使用gallery字段
      else if (product.gallery && Array.isArray(product.gallery) && product.gallery.length > 0) {
        images = product.gallery.filter(img => {
          if (!img) return false;
          if (typeof img === 'string') {
            return img.trim() !== '';
          }
          if (typeof img === 'object') {
            const url = img.url || img.path || img.src || img.image;
            return url && typeof url === 'string' && url.trim() !== '';
          }
          return false;
        }).map(img => {
          if (typeof img === 'string') {
            return img;
          }
          if (typeof img === 'object') {
            return img.url || img.path || img.src || img.image;
          }
          return img;
        });
        console.log('✅ 使用商品gallery字段:', images.length, '张图片');
      }
      
      // 3. 如果没有轮播图，尝试使用多个图片字段
      else {
        const imageFields = ['image', 'main_image', 'cover_image', 'thumbnail'];
        for (const field of imageFields) {
          if (product[field] && typeof product[field] === 'string' && product[field].trim() !== '') {
            images.push(product[field]);
            break; // 只取第一个有效的主图
          }
        }
        console.log('✅ 使用单张主图:', images.length, '张图片');
      }
      
      // 4. 如果还是没有图片，使用默认占位图
      if (images.length === 0) {
        images = ['/images/placeholder-product.png'];
        console.log('⚠️ 使用默认占位图');
      }
      
      // 5. 验证图片URL有效性
      const validImages = images.map(img => {
        // 确保img是字符串
        if (!img || typeof img !== 'string') {
          return '/images/placeholder-product.png';
        }
        
        const trimmedImg = img.trim();
        if (trimmedImg === '') {
          return '/images/placeholder-product.png';
        }
        
        // 如果是相对路径，添加域名前缀
        if (trimmedImg.startsWith('/')) {
          return trimmedImg;
        }
        
        // 如果是完整URL，直接使用
        if (trimmedImg.startsWith('http://') || trimmedImg.startsWith('https://')) {
          return trimmedImg;
        }
        
        // 其他情况，尝试添加基础URL
        return trimmedImg.startsWith('/') ? trimmedImg : `/${trimmedImg}`;
      });
      
      console.log('📸 最终轮播图列表:', validImages);
      return validImages;
      
    } catch (error) {
      console.error('❌ 处理商品轮播图失败:', error);
      return ['/images/placeholder-product.png'];
    }
  },

  /**
   * 从商品列表中查找商品（优化版本）
   */
  async getProductFromList(productId) {
    try {
      console.log('🔍 从商品列表中查找商品ID:', productId);
      
      // 优化：扩大搜索范围，包含更多分类
      const mainCategories = [0, 1, 2, 3, 4, 5]; // 主要分类
      
      for (let categoryId of mainCategories) {
        try {
          console.log(`🔍 搜索分类 ${categoryId}...`);
          
          const categoryProducts = await api.getCategoryProducts({
            categoryId,
            page: 1,
            pageSize: 50 // 适中的页面大小
          });
          
          if (categoryProducts && categoryProducts.list) {
            const product = categoryProducts.list.find(p => p.id == productId);
            if (product) {
              console.log('✅ 找到商品:', product.name, '在分类', categoryId);
              return this.enrichProductData(product);
            }
          }
        } catch (categoryError) {
          console.warn('搜索分类失败:', categoryId, categoryError.message);
          // 继续搜索下一个分类，不要中断
        }
      }
      
      // 如果在主要分类中没找到，尝试搜索更多分类
      console.log('在主要分类中未找到，搜索其他分类...');
      for (let categoryId = 6; categoryId <= 20; categoryId++) {
        try {
          const categoryProducts = await api.getCategoryProducts({
            categoryId,
            page: 1,
            pageSize: 30
          });
          
          if (categoryProducts && categoryProducts.list) {
            const product = categoryProducts.list.find(p => p.id == productId);
            if (product) {
              console.log('✅ 找到商品:', product.name, '在分类', categoryId);
              return this.enrichProductData(product);
            }
          }
        } catch (categoryError) {
          console.warn('搜索分类失败:', categoryId, categoryError.message);
        }
      }
      
      // 最后的备用方案：尝试使用搜索API
      console.log('在所有分类中未找到，尝试使用搜索API...');
      try {
        const searchResult = await api.request.get(`/public/products/search`, {
          keyword: productId,
          exact_match: true
        });
        
        if (searchResult && searchResult.data && Array.isArray(searchResult.data.list)) {
          const product = searchResult.data.list.find(p => p.id == productId);
          if (product) {
            console.log('✅ 通过搜索API找到商品:', product.name);
            return this.enrichProductData(product);
          }
        }
      } catch (searchError) {
        console.warn('搜索API失败:', searchError.message);
      }
      
      console.error('❌ 未找到商品ID:', productId);
      throw new Error('商品不存在');
      
    } catch (error) {
      console.error('从商品列表获取商品失败:', error);
      throw error;
    }
  },

  /**
   * 处理HTML内容，确保图片等资源能正确显示
   */
  processHtmlContent(htmlContent) {
    if (!htmlContent || typeof htmlContent !== 'string') {
      return '';
    }
    
    try {
      console.log('📝 处理HTML内容...');
      
      // 处理HTML内容，确保图片路径正确，并添加样式
      let processedContent = htmlContent
        // 处理图片标签，确保能在rich-text中正确显示
        .replace(/<img([^>]*?)>/gi, (match, attrs) => {
          // 提取src属性
          const srcMatch = attrs.match(/src\s*=\s*["']([^"']+)["']/i);
          if (srcMatch) {
            let src = srcMatch[1];
            
            // 使用统一的图片URL处理方法
            src = this.processImageUrl(src);
            
            // 构建完全响应式的img标签
            const responsiveStyle = [
              'width: 100% !important',
              'max-width: 100% !important',
              'min-width: 0 !important',
              'height: auto !important',
              'display: block !important',
              'margin: 15px auto !important',
              'border-radius: 8px',
              'box-sizing: border-box',
              'object-fit: contain',
              'background: #f5f5f5',
              'vertical-align: top'
            ].join('; ');
            
            return `<img src="${src}" style="${responsiveStyle}" />`;
          }
          return match;
        })
        // 处理段落标签
        .replace(/<p([^>]*?)>/gi, '<p style="margin: 10px 0; line-height: 1.6;">')
        // 移除可能影响显示的标签
        .replace(/<script[^>]*>.*?<\/script>/gi, '')
        .replace(/<style[^>]*>.*?<\/style>/gi, '')
        // 确保所有标签都正确闭合
        .replace(/<br\s*\/?>/gi, '<br/>');
      
      console.log('📝 HTML内容处理完成');
      console.log('原始内容:', htmlContent.substring(0, 100) + '...');
      console.log('处理后:', processedContent.substring(0, 200) + '...');
      
      return processedContent;
    } catch (error) {
      console.warn('HTML内容处理失败:', error);
      return htmlContent;
    }
  },

  /**
   * 处理富文本内容，为rich-text组件添加内联样式
   */
  processRichTextContent(htmlContent) {
    if (!htmlContent || typeof htmlContent !== 'string') {
      return htmlContent || '';
    }
    
    try {
      // 简化处理，主要确保基本样式和安全性
      let processedContent = htmlContent.trim();
      
      // 移除危险标签
      processedContent = processedContent
        .replace(/<script[^>]*>.*?<\/script>/gi, '')
        .replace(/<style[^>]*>.*?<\/style>/gi, '');
      
      // 处理图片路径和样式
      processedContent = processedContent.replace(/<img([^>]*?)src\s*=\s*["']([^"']+)["']([^>]*?)>/gi, (match, before, src, after) => {
        // 使用统一的图片URL处理方法
        let newSrc = this.processImageUrl(src);
        
        // 为图片添加完全响应式样式，确保适配所有屏幕尺寸
        let imgStyle = [
          'width: 100% !important',        // 强制宽度100%适配容器
          'max-width: 100% !important',    // 强制最大宽度不超过容器
          'min-width: 0 !important',       // 最小宽度为0，防止溢出
          'height: auto !important',       // 强制高度自适应保持比例
          'max-height: none !important',   // 不限制最大高度，让图片完整显示
          'display: block !important',     // 强制块级元素
          'margin: 15px auto !important',  // 强制上下间距，左右居中
          'border-radius: 8px',            // 圆角
          'box-sizing: border-box',        // 盒模型
          'object-fit: contain',           // 图片完整显示，保持比例
          'background: #f5f5f5',           // 背景色（加载时显示）
          'vertical-align: top',           // 垂直对齐
          'overflow: hidden'               // 防止图片溢出
        ].join('; ');
        
        // 检查是否已有style属性
        const styleMatch = (before + after).match(/style\s*=\s*["']([^"']+)["']/i);
        if (styleMatch) {
          // 移除原有的style属性，使用我们的响应式样式
          before = before.replace(/style\s*=\s*["'][^"']*["']/gi, '');
          after = after.replace(/style\s*=\s*["'][^"']*["']/gi, '');
        }
        
        // 移除可能影响响应式的width和height属性
        before = before.replace(/width\s*=\s*["'][^"']*["']/gi, '');
        after = after.replace(/width\s*=\s*["'][^"']*["']/gi, '');
        before = before.replace(/height\s*=\s*["'][^"']*["']/gi, '');
        after = after.replace(/height\s*=\s*["'][^"']*["']/gi, '');
        
        return `<img${before}src="${newSrc}"${after} style="${imgStyle}">`;
      });
      
      // 为段落添加样式
      processedContent = processedContent.replace(/<p([^>]*?)>/gi, (match, attrs) => {
        if (attrs.indexOf('style') === -1) {
          return `<p${attrs} style="margin: 8px 0; line-height: 1.6; color: #333; width: 100%; max-width: 100%; word-wrap: break-word; overflow-wrap: break-word;">`;
        }
        return match;
      });
      
      // 为标题添加样式
      processedContent = processedContent
        .replace(/<h1([^>]*?)>/gi, '<h1$1 style="font-size: 20px; font-weight: bold; margin: 15px 0 10px 0; color: #333; width: 100%; max-width: 100%; word-wrap: break-word;">')
        .replace(/<h2([^>]*?)>/gi, '<h2$1 style="font-size: 18px; font-weight: bold; margin: 12px 0 8px 0; color: #333; width: 100%; max-width: 100%; word-wrap: break-word;">')
        .replace(/<h3([^>]*?)>/gi, '<h3$1 style="font-size: 16px; font-weight: bold; margin: 10px 0 6px 0; color: #333; width: 100%; max-width: 100%; word-wrap: break-word;">');
      
      // 为列表添加样式
      processedContent = processedContent
        .replace(/<ul([^>]*?)>/gi, '<ul$1 style="margin: 10px 0; padding-left: 20px; width: 100%; max-width: 100%; box-sizing: border-box;">')
        .replace(/<ol([^>]*?)>/gi, '<ol$1 style="margin: 10px 0; padding-left: 20px; width: 100%; max-width: 100%; box-sizing: border-box;">')
        .replace(/<li([^>]*?)>/gi, '<li$1 style="margin: 4px 0; line-height: 1.5; word-wrap: break-word; overflow-wrap: break-word;">');
      
      // 处理强调标签
      processedContent = processedContent
        .replace(/<strong([^>]*?)>/gi, '<strong$1 style="font-weight: bold; color: #333;">')
        .replace(/<b([^>]*?)>/gi, '<b$1 style="font-weight: bold; color: #333;">')
        .replace(/<em([^>]*?)>/gi, '<em$1 style="font-style: italic;">');
      
      // 为div等块级元素添加宽度限制
      processedContent = processedContent
        .replace(/<div([^>]*?)>/gi, (match, attrs) => {
          if (attrs.indexOf('style') === -1) {
            return `<div${attrs} style="width: 100%; max-width: 100%; box-sizing: border-box; word-wrap: break-word; overflow-wrap: break-word;">`;
          } else {
            // 如果已有style，确保包含宽度限制
            const styleMatch = attrs.match(/style\s*=\s*["']([^"']*)["']/i);
            if (styleMatch) {
              const existingStyle = styleMatch[1];
              if (existingStyle.indexOf('width') === -1) {
                const newStyle = existingStyle + '; width: 100%; max-width: 100%; box-sizing: border-box; word-wrap: break-word; overflow-wrap: break-word;';
                return match.replace(styleMatch[0], `style="${newStyle}"`);
              }
            }
          }
          return match;
        })
        .replace(/<table([^>]*?)>/gi, '<table$1 style="width: 100%; max-width: 100%; table-layout: fixed; word-wrap: break-word; overflow-wrap: break-word; box-sizing: border-box;">')
        .replace(/<td([^>]*?)>/gi, '<td$1 style="word-wrap: break-word; overflow-wrap: break-word; max-width: 0;">')
        .replace(/<th([^>]*?)>/gi, '<th$1 style="word-wrap: break-word; overflow-wrap: break-word; max-width: 0;">');
      
      return processedContent;
    } catch (error) {
      console.warn('富文本内容处理失败:', error);
      return htmlContent || '';
    }
  },

  /**
   * 丰富商品数据，为详情页补充完整信息
   */
  enrichProductData(product) {
    return {
      ...product,
      // 确保有多张图片用于轮播
      images: product.images || [
        product.image,
        // 生成一些相似的图片URL作为示例（只有当主图是字符串时）
        ...(product.image && typeof product.image === 'string' ? [
          product.image.replace('400', '600'),
          product.image.replace('q=80', 'q=90')
        ] : [])
      ].filter(img => img && typeof img === 'string' && img.trim() !== ''),
      // 补充详细信息
      detail: product.detail || `${product.description || product.name}。这是一款优质商品，采用精选原料，品质保证，营养丰富，口感极佳。`,
      nutrients: product.nutrients || '富含多种维生素和营养成分，有益身体健康。',
      // 确保有供应商信息
      supplier: product.supplier || '自营',
      // 确保有库存和销量
      stock: product.stock || Math.floor(Math.random() * 1000) + 50,
      sales: product.sales || Math.floor(Math.random() * 500) + 10
    };
  },

  /**
   * 检查收藏状态
   */
  checkFavoriteStatus() {
    // 检查用户是否收藏了该商品
    const favorites = wx.getStorageSync('favorites') || [];
    const isFavorite = favorites.includes(this.data.productId);
    this.setData({ isFavorite });
  },

  /**
   * 图片轮播变化
   */
  onImageSwiper(e) {
    const { current } = e.detail;
    this.setData({ currentImageIndex: current });
    console.log('📸 切换到第', current + 1, '张图片');
  },

  /**
   * 指示器点击
   */
  onIndicatorTap(e) {
    const { index } = e.currentTarget.dataset;
    this.setData({ currentImageIndex: index });
    console.log('📸 点击指示器切换到第', index + 1, '张图片');
  },

  /**
   * 图片点击 - 预览大图
   */
  onImageTap(e) {
    const { index, src } = e.currentTarget.dataset;
    const { images } = this.data;
    
    // 过滤掉占位图
    const validImages = images.filter(img => !img.includes('placeholder'));
    
    if (validImages.length > 0) {
      wx.previewImage({
        current: validImages[index] || validImages[0],
        urls: validImages
      });
    }
  },

  /**
   * 图片加载完成
   */
  onImageLoad(e) {
    this.setData({ imageLoaded: true });
    console.log('📸 图片加载完成');
  },

  /**
   * 图片加载失败
   */
  onImageError(e) {
    const { index } = e.currentTarget.dataset;
    console.warn('📸 图片加载失败:', index, e.detail);
    
    // 可以在这里替换为占位图
    const { images } = this.data;
    if (images[index] && !images[index].includes('placeholder')) {
      const newImages = [...images];
      newImages[index] = '/images/placeholder-product.png';
      this.setData({ images: newImages });
    }
  },

  /**
   * 推荐商品图片加载错误
   */
  onRecommendImageError(e) {
    const { productId } = e.currentTarget.dataset;
    console.warn('📸 推荐商品图片加载失败，商品ID:', productId);
    
    // 替换失败的推荐商品图片
    const recommendProducts = [...this.data.recommendProducts];
    const productIndex = recommendProducts.findIndex(p => p.id == productId);
    
    if (productIndex !== -1) {
      recommendProducts[productIndex] = {
        ...recommendProducts[productIndex],
        image: '/images/placeholder-product.png'
      };
      this.setData({ recommendProducts });
      console.log('📸 已替换推荐商品占位图:', productId);
    }
  },

  /**
   * 切换收藏状态
   */
  onToggleFavorite() {
    const { isFavorite, productId } = this.data;
    const favorites = wx.getStorageSync('favorites') || [];
    
    if (isFavorite) {
      // 取消收藏
      const index = favorites.indexOf(productId);
      if (index > -1) {
        favorites.splice(index, 1);
      }
      wx.showToast({ title: '已取消收藏', icon: 'none' });
    } else {
      // 添加收藏
      favorites.push(productId);
      wx.showToast({ title: '已添加收藏', icon: 'success' });
    }
    
    wx.setStorageSync('favorites', favorites);
    this.setData({ isFavorite: !isFavorite });
  },

  /**
   * 分享商品
   */
  onShare() {
    const { product } = this.data;
    if (!product) {
      wx.showToast({
        title: '商品信息加载中',
        icon: 'none'
      });
      return;
    }
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 标签页切换
   */
  onTabChange(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    this.setData({ activeTab: index });
    
    console.log('标签页切换:', index === 0 ? '商品详情' : '购买记录');
  },

  /**
   * 推荐商品点击
   */
  onRecommendProductTap(e) {
    const product = e.currentTarget.dataset.product;
    if (product && product.id) {
      wx.navigateTo({
        url: `/pages/product-detail/product-detail?id=${product.id}`
      });
    }
  },

  /**
   * 推荐商品加购物车
   */
  onRecommendAddCart(e) {
    // 安全的事件处理
    if (e && typeof e.stopPropagation === 'function') {
      e.stopPropagation();
    }
    
    // 检查登录状态，未登录时跳转到登录页
    const userLoggedIn = isLoggedIn();
    if (!userLoggedIn) {
      wx.navigateTo({
        url: '/pages/login/index'
      });
      return;
    }
    
    const product = e && e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.product;
    if (product) {
      console.log('🛒 推荐商品加购物车:', product.name);
      this.addToCart(product, 1);
      
      // 添加触觉反馈
      try {
        wx.vibrateShort({
          type: 'light'
        });
      } catch (error) {
        console.warn('触觉反馈失败:', error);
      }
    } else {
      console.error('推荐商品数据缺失:', e);
    }
  },

  /**
   * 加入购物车
   */
  onAddToCart() {
    // 检查登录状态，未登录时跳转到登录页
    const userLoggedIn = isLoggedIn();
    if (!userLoggedIn) {
      wx.navigateTo({
        url: '/pages/login/index'
      });
      return;
    }
    
    const { product, quantity } = this.data;
    this.addToCart(product, quantity);
  },

  /**
   * 通用加购物车方法（使用统一管理器）
   */
  async addToCart(product, quantity) {
    try {
      // 🔥 使用统一购物车管理器
      const { addToCart } = require('../../utils/cart-unified');
      const success = await addToCart(product, quantity);
      
      if (success) {
        console.log('✅ 添加到购物车成功:', product.name);
        
        // Toast提示由统一管理器的监听器处理
        // 购物车数量由统一管理器自动更新
      } else {
        console.error('❌ 添加到购物车失败');
        wx.showToast({
          title: '添加失败，请重试',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('❌ 添加到购物车异常:', error);
      
      // 根据错误类型显示不同提示
      let errorMessage = '添加失败';
      if (error.message) {
        if (error.message.includes('登录')) {
          errorMessage = '登录已过期，请重新登录';
        } else if (error.message.includes('网络')) {
          errorMessage = '网络异常，请重试';
        } else {
          errorMessage = error.message;
        }
      }
      
      wx.showToast({
        title: errorMessage,
        icon: 'error'
      });
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp();
    const isLoggedIn = !!(app.globalData && app.globalData.isLoggedIn);
    
    console.log('🔐 详情页检查登录状态:', isLoggedIn);
    
    this.setData({
      isLoggedIn: isLoggedIn
    });
    
    // 通知登录状态管理器状态变化，确保与积分商城等页面同步
    try {
      const { notifyLoginStateChange } = require('../../utils/login-state-manager');
      notifyLoginStateChange();
      console.log('🔔 已通知登录状态管理器状态变化');
    } catch (error) {
      console.warn('⚠️ 通知登录状态管理器失败:', error);
    }
  },

  /**
   * 分享
   */
  onShareAppMessage() {
    const { product } = this.data;
    return {
      title: product ? `${product.name} - 万象生鲜` : '万象生鲜商品',
      path: `/pages/product-detail/product-detail?id=${this.data.productId}`,
      imageUrl: product ? product.image : ''
    };
  },

  /**
   * 初始化登录状态监听
   */
  initLoginStateListener() {
    // 监听登录状态变化，自动刷新商品价格
    const { addLoginStateListener } = require('../../utils/login-state-manager');
    
    this.loginListenerId = `product-detail-${this.data.productId}-${Date.now()}`;
    
    addLoginStateListener(this.loginListenerId, (event) => {
      console.log('💰 商品详情页收到登录状态变化:', event.type);
      
      if (event.type === 'login-status-change') {
        // 登录状态变化时，刷新商品价格
        this.refreshProductPrices();
      }
    });
    
    console.log('✅ 商品详情页登录状态监听器初始化完成:', this.loginListenerId);
  },

  /**
   * 移除登录状态监听
   */
  removeLoginStateListener() {
    if (this.loginListenerId) {
      const { removeLoginStateListener } = require('../../utils/login-state-manager');
      removeLoginStateListener(this.loginListenerId);
      console.log('✅ 商品详情页登录状态监听器移除完成:', this.loginListenerId);
    }
  },

  /**
   * 刷新商品价格
   */
  refreshProductPrices() {
    console.log('💰 刷新商品详情页价格');
    
    // 刷新主商品价格
    const mainPriceDisplay = this.selectComponent('.product-detail-price');
    if (mainPriceDisplay && typeof mainPriceDisplay.refreshPrice === 'function') {
      mainPriceDisplay.refreshPrice();
    }
    
    // 刷新推荐商品价格
    const recommendPriceDisplays = this.selectAllComponents('.recommend-price');
    if (recommendPriceDisplays && recommendPriceDisplays.length > 0) {
      recommendPriceDisplays.forEach(priceDisplay => {
        if (priceDisplay && typeof priceDisplay.refreshPrice === 'function') {
          priceDisplay.refreshPrice();
        }
      });
      
      console.log(`💰 已刷新主商品和 ${recommendPriceDisplays.length} 个推荐商品的价格`);
    } else {
      console.log('💰 已刷新主商品价格');
    }
  },

  /**
   * 商品价格加载完成事件处理
   */
  onProductPriceLoaded(e) {
    const { priceInfo, productId } = e.detail;
    console.log('💰 商品详情页收到价格加载完成:', productId, priceInfo);
  },

  /**
   * 商品价格加载错误事件处理
   */
  onProductPriceError(e) {
    const { error, productId } = e.detail;
    console.warn('💰 商品详情页收到价格加载错误:', productId, error);
  },

  /**
   * 推荐商品价格加载完成事件处理
   */
  onRecommendPriceLoaded(e) {
    const { priceInfo } = e.detail;
    console.log('💰 推荐商品价格加载完成:', priceInfo);
  },

  /**
   * 推荐商品价格加载错误事件处理
   */
  onRecommendPriceError(e) {
    const { error } = e.detail;
    console.warn('💰 推荐商品价格加载错误:', error);
  },

  /**
   * 数量减少
   * 🔥 修改：允许减少到1，但提示最小起购数量
   */
  onQuantityDecrease() {
    const { quantity, product } = this.data;
    const minSaleQuantity = product?.min_sale_quantity || 1;
    
    if (quantity > 1) {
      const newQuantity = quantity - 1;
      this.setData({ quantity: newQuantity });
      
      // 🔥 新增：如果减少后的数量低于最小起购数量，显示提示
      if (newQuantity < minSaleQuantity) {
        wx.showModal({
          title: '提示',
          content: `该商品最小起购量为${minSaleQuantity}${product?.unit || '件'}，当前数量不足最小起购量`,
          showCancel: false,
          confirmText: '知道了'
        });
      }
    } else {
      wx.showToast({
        title: '数量不能少于1',
        icon: 'none'
      });
    }
  },

  /**
   * 数量增加
   */
  onQuantityIncrease() {
    const { quantity, product } = this.data;
    const maxQuantity = product?.stock || 999;
    
    if (quantity < maxQuantity) {
      this.setData({ quantity: quantity + 1 });
    } else {
      wx.showToast({
        title: '库存不足',
        icon: 'none'
      });
    }
  },

  /**
   * 回到首页
   */
  onGoHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  /**
   * 联系客服
   */
  onContactService() {
    wx.showToast({
      title: '客服功能开发中',
      icon: 'none'
    });
  },

  /**
   * 前往购物车
   */
  onGoCart() {
    wx.switchTab({
      url: '/pages/cart/index'
    });
  },

  /**
   * 处理图片URL
   */
  processImageUrl(src) {
    if (!src) return '';
    
    // 如果已经是完整URL，直接返回
    if (src.startsWith('http://') || src.startsWith('https://')) {
      return src;
    }
    
    // 获取API基础URL
    const baseUrl = api.API.BASE_URL.replace('/api', '');
    
    // 确保路径以/开头
    if (!src.startsWith('/')) {
      src = '/' + src;
    }
    
    return baseUrl + src;
  }
}); 