import request from '../utils/request.js'

// 订单相关API
export default {
	// 获取订单列表
	getOrderList(params = {}) {
		return request.get('/orders', params)
	},
	
	// 获取订单详情
	getOrderDetail(orderId) {
		return request.get(`/orders/${orderId}`)
	},
	
	// 创建订单
	createOrder(data) {
		return request.post('/orders', data)
	},
	
	// 代客下单
	createProxyOrder(data) {
		return request.post('/orders/proxy', data)
	},
	
	// 更新订单状态
	updateOrderStatus(orderId, status) {
		return request.put(`/orders/${orderId}/status`, { status })
	},
	
	// 取消订单
	cancelOrder(orderId, reason = '') {
		return request.post(`/orders/${orderId}/cancel`, { reason })
	},
	
	// 获取客户订单列表
	getClientOrders(clientId, params = {}) {
		return request.get(`/crm/users/${clientId}/orders`, params)
	},
	
	// 搜索订单
	searchOrders(keyword, params = {}) {
		return request.get('/orders', { 
			...params, 
			keyword 
		})
	},
	
	// 获取订单统计
	getOrderStats(params = {}) {
		return request.get('/orders/stats', params)
	},
	
	// 批量更新订单状态
	batchUpdateStatus(orderIds, status) {
		return request.post('/orders/batch/status', { 
			order_ids: orderIds, 
			status 
		})
	},
	
	// 导出订单
	exportOrders(params = {}) {
		return request.post('/orders/export', params)
	}
} 