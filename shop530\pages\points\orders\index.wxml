<!-- pages/points/orders/index.wxml - 积分订单列表页 -->
<view class="orders-container">
  
  <!-- 状态标签栏 -->
  <view class="status-tabs">
    <scroll-view class="tabs-scroll" scroll-x="{{true}}">
      <view 
        class="tab-item {{currentTab === item.key ? 'active' : ''}}"
        wx:for="{{tabs}}"
        wx:key="key"
        data-tab="{{item.key}}"
        bindtap="onTabChange"
      >
        <text class="tab-name">{{item.name}}</text>
        <text class="tab-count" wx:if="{{item.count > 0}}">{{item.count}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 订单列表 -->
  <view class="orders-list">
    <view 
      class="order-item"
      wx:for="{{orders}}"
      wx:key="id"
      data-order-id="{{item.id}}"
      bindtap="goToOrderDetail"
    >
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="order-info">
          <text class="order-no">订单号：{{item.order_no}}</text>
          <text class="order-time">{{formatDate(item.created_at)}}</text>
        </view>
        <view class="order-status" style="color: {{getOrderStatusColor(item.status)}}">
          {{formatOrderStatus(item.status)}}
        </view>
      </view>

      <!-- 订单商品 -->
      <view class="order-products">
        <view 
          class="product-item"
          wx:for="{{item.items}}"
          wx:key="id"
          wx:for-item="product"
          data-product-id="{{product.product_id}}"
          bindtap="goToProduct"
        >
          <image class="product-image" src="{{product.image}}" mode="aspectFill" />
          <view class="product-info">
            <text class="product-name">{{product.name}}</text>
            <text class="product-spec" wx:if="{{product.spec_name}}">规格：{{product.spec_name}}</text>
            <view class="product-price">
              <text class="points-price">{{product.points_price}}积分</text>
              <text class="cash-price" wx:if="{{product.cash_price > 0}}">+¥{{product.cash_price}}</text>
            </view>
          </view>
          <view class="product-quantity">
            <text>×{{product.quantity}}</text>
          </view>
        </view>
      </view>

      <!-- 订单总计 -->
      <view class="order-total">
        <view class="total-info">
          <text class="total-label">共{{item.total_quantity}}件商品，合计：</text>
          <view class="total-price">
            <text class="total-points">{{item.total_points}}积分</text>
            <text class="total-cash" wx:if="{{item.total_cash > 0}}">+¥{{item.total_cash}}</text>
          </view>
        </view>
      </view>

      <!-- 订单操作 -->
      <view class="order-actions" wx:if="{{getOrderActions(item).length > 0}}">
        <button 
          class="action-btn {{action.type}}"
          wx:for="{{getOrderActions(item)}}"
          wx:key="key"
          wx:for-item="action"
          data-action="{{action.key}}"
          data-order-id="{{item.id}}"
          bindtap="onOrderAction"
        >
          {{action.name}}
        </button>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{orders.length === 0 && !loading}}">
      <image class="empty-image" src="/images/empty/orders.png" mode="aspectFit" />
      <text class="empty-text">暂无订单记录</text>
      <button class="empty-btn" bindtap="goToPointsMall">去积分商城看看</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-more" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && orders.length > 0}}">
      <text class="no-more-text">没有更多订单了</text>
    </view>
  </view>

</view> 