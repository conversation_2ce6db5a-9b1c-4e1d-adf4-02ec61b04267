<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            // 添加分类图片链接字段
            $table->string('image_url')->nullable()->after('description')->comment('分类图片URL');
            
            // 添加分类图标字段
            $table->string('icon', 50)->nullable()->after('image_url')->comment('分类图标(支持名称或图标代码)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            // 回滚时移除添加的字段
            $table->dropColumn('image_url');
            $table->dropColumn('icon');
        });
    }
};
