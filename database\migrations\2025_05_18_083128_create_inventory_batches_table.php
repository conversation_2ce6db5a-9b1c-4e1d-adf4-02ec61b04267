<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_batches', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('inventory_id');
            $table->foreignId('purchase_item_id')->nullable()->constrained()->comment('采购明细ID');
            $table->string('batch_code', 50)->comment('批次编号');
            $table->date('production_date')->nullable()->comment('生产日期');
            $table->date('expiry_date')->nullable()->comment('到期日期');
            $table->decimal('purchase_price', 10, 2)->comment('该批次采购价');
            $table->decimal('quantity', 10, 2)->comment('当前数量');
            $table->decimal('initial_quantity', 10, 2)->comment('初始数量');
            $table->string('unit', 20)->comment('单位');
            $table->text('notes')->nullable()->comment('备注');
            $table->foreignId('created_by')->nullable()->constrained('users')->comment('创建人');
            $table->timestamps();
            
            // 修改外键引用的表名
            $table->foreign('inventory_id')->references('id')->on('inventory')->onDelete('cascade');
            
            // 索引
            $table->index('batch_code');
            $table->index('expiry_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_batches');
    }
};
