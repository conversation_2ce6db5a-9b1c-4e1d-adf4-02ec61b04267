<?php

namespace App\Supplier\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Supplier\Services\SupplierService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SupplierController extends Controller
{
    /**
     * 供应商服务
     *
     * @var SupplierService
     */
    protected $supplierService;

    /**
     * 构造函数
     *
     * @param SupplierService $supplierService
     */
    public function __construct(SupplierService $supplierService)
    {
        $this->supplierService = $supplierService;
    }

    /**
     * 获取供应商列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $suppliers = $this->supplierService->getSuppliers($request->all());
            return response()->json(ApiResponse::success($suppliers));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('获取供应商列表失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 创建供应商
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'contact_person' => 'nullable|string|max:100',
            'contact_phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:100',
            'address' => 'nullable|string|max:255',
            'credit_limit' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            $supplier = $this->supplierService->createSupplier($request->all());
            return response()->json(ApiResponse::success($supplier, '供应商创建成功'), 201);
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('创建供应商失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 获取供应商详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $supplier = $this->supplierService->getSupplier($id);
            return response()->json(ApiResponse::success($supplier));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('获取供应商详情失败: ' . $e->getMessage()), 404);
        }
    }

    /**
     * 更新供应商
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'contact_person' => 'nullable|string|max:100',
            'contact_phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:100',
            'address' => 'nullable|string|max:255',
            'credit_limit' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            $supplier = $this->supplierService->updateSupplier($id, $request->all());
            return response()->json(ApiResponse::success($supplier, '供应商更新成功'));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('更新供应商失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 删除供应商
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $this->supplierService->deleteSupplier($id);
            return response()->json(ApiResponse::success(null, '供应商删除成功'));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error($e->getMessage(), 422), 422);
        }
    }
    
    /**
     * 获取供应商的采购历史
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function purchaseHistory(Request $request, $id)
    {
        try {
            $history = $this->supplierService->getSupplierPurchaseHistory($id, $request->all());
            
            // 格式化返回数据，使其符合前端期望的格式
            $formattedHistory = $history->map(function ($order) {
                return [
                    'id' => $order->id,
                    'order_no' => $order->order_number,
                    'order_date' => $order->order_date->format('Y-m-d'),
                    'total_amount' => $order->total_amount,
                    'status' => $this->mapOrderStatus($order->status),
                    'payment_status' => $this->getPaymentStatus($order),
                    'items_count' => $order->items->count(),
                    'warehouse_name' => $order->warehouse ? $order->warehouse->name : '-'
                ];
            });
            
            // 直接返回数组数据，不包装在对象中
            return response()->json(ApiResponse::success([
                'data' => $formattedHistory->values()->toArray(),
                'total' => $history->total(),
                'per_page' => $history->perPage(),
                'current_page' => $history->currentPage(),
                'last_page' => $history->lastPage()
            ]));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('获取供应商采购历史失败: ' . $e->getMessage()), 500);
        }
    }
    
    /**
     * 映射订单状态为前端状态
     *
     * @param string $status
     * @return string
     */
    private function mapOrderStatus($status)
    {
        $statusMap = [
            'draft' => 'pending',
            'submitted' => 'processing',
            'approved' => 'processing',
            'received' => 'completed',
            'canceled' => 'cancelled',
            'completed' => 'completed'
        ];
        
        return $statusMap[$status] ?? $status;
    }
    
    /**
     * 获取支付状态
     *
     * @param \App\Purchase\Models\PurchaseOrder $order
     * @return string
     */
    private function getPaymentStatus($order)
    {
        if ($order->paid_amount <= 0) {
            return 'unpaid';
        } elseif ($order->paid_amount < $order->total_amount) {
            return 'partial';
        } else {
            return 'paid';
        }
    }
} 