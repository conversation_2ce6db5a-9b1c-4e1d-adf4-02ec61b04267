<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. 扩展payment_method枚举值（添加cod）
        DB::statement("ALTER TABLE `payment_records` MODIFY COLUMN `payment_method` ENUM(
            'wechat',
            'alipay',
            'cash',
            'bank_transfer',
            'cod'
        ) NOT NULL COMMENT '支付方式'");
        
        // 2. 扩展payment_type枚举值
        DB::statement("ALTER TABLE `payment_records` MODIFY COLUMN `payment_type` ENUM(
            'initial',
            'supplement', 
            'refund',
            'cod',
            'cod_settlement',
            'cod_difference'
        ) NOT NULL COMMENT '付款类型'");
        
        // 3. 扩展business_type枚举值
        DB::statement("ALTER TABLE `payment_records` MODIFY COLUMN `business_type` ENUM(
            'order_payment',
            'correction_supplement',
            'correction_refund', 
            'cod_final',
            'cod_cash_settlement',
            'cod_wechat_settlement',
            'cod_difference_supplement',
            'cod_difference_refund'
        ) NOT NULL COMMENT '业务类型'");
        
        // 4. 扩展status枚举值
        DB::statement("ALTER TABLE `payment_records` MODIFY COLUMN `status` ENUM(
            'pending',
            'success',
            'failed',
            'refunded',
            'cancelled',
            'pending_settlement',
            'settled'
        ) DEFAULT 'pending' COMMENT '状态'");
        
        // 5. 添加新字段和性能优化索引
        Schema::table('payment_records', function (Blueprint $table) {
            $table->timestamp('settlement_at')->nullable()->after('refunded_at')->comment('结算时间');
            
            // 性能优化：为常用查询组合添加复合索引
            $table->index(['payment_type', 'status', 'settlement_at'], 'idx_payment_type_status_settlement');
            $table->index(['business_type', 'status'], 'idx_business_type_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复原始枚举值
        DB::statement("ALTER TABLE `payment_records` MODIFY COLUMN `payment_method` ENUM(
            'wechat',
            'alipay',
            'cash',
            'bank_transfer'
        ) NOT NULL COMMENT '支付方式'");
        
        DB::statement("ALTER TABLE `payment_records` MODIFY COLUMN `payment_type` ENUM(
            'initial',
            'supplement',
            'refund',
            'cod'
        ) NOT NULL COMMENT '付款类型'");
        
        DB::statement("ALTER TABLE `payment_records` MODIFY COLUMN `business_type` ENUM(
            'order_payment',
            'correction_supplement',
            'correction_refund',
            'cod_final'
        ) NOT NULL COMMENT '业务类型'");
        
        DB::statement("ALTER TABLE `payment_records` MODIFY COLUMN `status` ENUM(
            'pending',
            'success',
            'failed',
            'refunded',
            'cancelled'
        ) DEFAULT 'pending' COMMENT '状态'");
        
        // 删除索引和字段
        Schema::table('payment_records', function (Blueprint $table) {
            // 删除索引
            $table->dropIndex('idx_payment_type_status_settlement');
            $table->dropIndex('idx_business_type_status');
            
            // 删除字段
            $table->dropColumn('settlement_at');
        });
    }
}; 