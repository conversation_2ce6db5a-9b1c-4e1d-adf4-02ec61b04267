<!-- pages/points/transactions/index.wxml - 积分流水记录页 -->
<view class="transactions-container">
  
  <!-- 头部统计 -->
  <view class="header-stats">
    <view class="stats-item">
      <text class="stats-label">当前余额</text>
      <text class="stats-value balance">{{formatPoints(userBalance)}}</text>
    </view>
    <view class="stats-item">
      <text class="stats-label">本月收入</text>
      <text class="stats-value income">+{{formatPoints(monthStats.earned)}}</text>
    </view>
    <view class="stats-item">
      <text class="stats-label">本月支出</text>
      <text class="stats-value expense">-{{formatPoints(monthStats.spent)}}</text>
    </view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar">
    <scroll-view class="filter-scroll" scroll-x="{{true}}">
      <view 
        class="filter-item {{currentType === item.key ? 'active' : ''}}"
        wx:for="{{typeFilters}}"
        wx:key="key"
        data-type="{{item.key}}"
        bindtap="onTypeChange"
      >
        <text class="filter-name">{{item.name}}</text>
      </view>
    </scroll-view>
    
    <view class="date-picker" bindtap="toggleDatePicker">
      <text class="date-text">{{dateRange || '全部时间'}}</text>
      <text class="date-icon">▼</text>
    </view>
  </view>

  <!-- 流水列表 -->
  <view class="transactions-list">
    <view 
      class="transaction-item"
      wx:for="{{transactions}}"
      wx:key="id"
    >
      <view class="transaction-icon">
        <image 
          class="icon-image" 
          src="{{getTransactionIcon(item.type)}}" 
          mode="aspectFit"
        />
      </view>
      
      <view class="transaction-info">
        <view class="transaction-header">
          <text class="transaction-title">{{getTransactionTitle(item)}}</text>
          <view class="transaction-amount {{item.amount > 0 ? 'income' : 'expense'}}">
            <text class="amount-sign">{{item.amount > 0 ? '+' : ''}}</text>
            <text class="amount-value">{{formatPoints(item.amount)}}</text>
          </view>
        </view>
        
        <view class="transaction-details">
          <text class="transaction-desc" wx:if="{{item.description}}">{{item.description}}</text>
          <text class="transaction-time">{{formatDateTime(item.created_at)}}</text>
        </view>
        
        <!-- 关联信息 -->
        <view class="transaction-related" wx:if="{{item.related_info}}">
          <text class="related-text">{{item.related_info}}</text>
          <text 
            class="related-link" 
            wx:if="{{item.related_type && item.related_id}}"
            data-type="{{item.related_type}}"
            data-id="{{item.related_id}}"
            bindtap="goToRelated"
          >
            查看详情
          </text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{transactions.length === 0 && !loading}}">
      <image class="empty-image" src="/images/empty/transactions.png" mode="aspectFit" />
      <text class="empty-text">暂无积分记录</text>
      <button class="empty-btn" bindtap="goToPointsMall">去赚取积分</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-more" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && transactions.length > 0}}">
      <text class="no-more-text">没有更多记录了</text>
    </view>
  </view>

</view>

<!-- 日期选择器 -->
<picker 
  mode="date" 
  fields="month"
  value="{{selectedDate}}"
  bindchange="onDateChange"
  wx:if="{{showDatePicker}}"
>
  <view class="picker-mask" bindtap="hideDatePicker"></view>
</picker> 