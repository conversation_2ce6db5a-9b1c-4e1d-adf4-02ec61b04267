<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 如果存在外键，先尝试删除
        if (Schema::hasTable('deliveries')) {
            // 使用原生SQL方式删除可能存在的外键约束
            // 首先获取所有与deliverer_id相关的外键名称
            $constraintName = DB::select("
                SELECT CONSTRAINT_NAME 
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_NAME = 'deliveries' 
                AND COLUMN_NAME = 'deliverer_id'
                AND CONSTRAINT_NAME != 'PRIMARY' 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            
            // 如果找到相关外键约束，删除它们
            if (!empty($constraintName)) {
                foreach ($constraintName as $constraint) {
                    Schema::table('deliveries', function (Blueprint $table) use ($constraint) {
                        $table->dropForeign($constraint->CONSTRAINT_NAME);
                    });
                }
            }
            
            // 添加新的外键约束
            Schema::table('deliveries', function (Blueprint $table) {
                // 确保deliverer_id是整数且允许为空
                $table->unsignedBigInteger('deliverer_id')->nullable()->change();
                
                // 添加新的外键约束
                $table->foreign('deliverer_id')
                    ->references('id')
                    ->on('employees')
                    ->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 尝试移除外键约束
        Schema::table('deliveries', function (Blueprint $table) {
            // 使用Laravel的方式移除外键
            $table->dropForeign(['deliverer_id']);
        });
    }
};
