<?php

namespace App\Delivery\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Delivery\Services\DelivererService;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DelivererController extends Controller
{
    /**
     * 配送员服务
     *
     * @var DelivererService
     */
    protected $delivererService;

    /**
     * 构造函数
     *
     * @param DelivererService $delivererService
     */
    public function __construct(DelivererService $delivererService)
    {
        $this->delivererService = $delivererService;
    }

    /**
     * 获取配送员列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $deliverers = $this->delivererService->getDeliverers($request);
            return response()->json(ApiResponse::success($deliverers));
        } catch (\Exception $e) {
            Log::error('获取配送员列表失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('获取配送员列表失败', 500), 500);
        }
    }

    /**
     * 获取配送员详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $deliverer = $this->delivererService->getDeliverer($id);
            return response()->json(ApiResponse::success($deliverer));
        } catch (\Exception $e) {
            Log::error('获取配送员详情失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('获取配送员详情失败', 404), 404);
        }
    }

    /**
     * 创建配送员
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'employee_id' => 'required|exists:employees,id',
                'delivery_area' => 'nullable|string|max:255',
                'max_orders' => 'nullable|integer|min:1',
                'working_hours' => 'nullable|string|max:255',
                'transportation' => 'nullable|string|max:100',
                'status' => 'nullable|in:available,busy,offline',
            ]);

            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }

            $deliverer = $this->delivererService->createDeliverer($request->all());
            return response()->json(ApiResponse::success($deliverer, '配送员创建成功'), 201);
        } catch (\Exception $e) {
            Log::error('创建配送员失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error($e->getMessage(), 400), 400);
        }
    }

    /**
     * 更新配送员信息
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'delivery_area' => 'nullable|string|max:255',
                'max_orders' => 'nullable|integer|min:1',
                'working_hours' => 'nullable|string|max:255',
                'transportation' => 'nullable|string|max:100',
                'status' => 'nullable|in:available,busy,offline',
                'rating' => 'nullable|numeric|min:0|max:5',
            ]);

            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }

            $deliverer = $this->delivererService->updateDeliverer($id, $request->all());
            return response()->json(ApiResponse::success($deliverer, '配送员信息更新成功'));
        } catch (\Exception $e) {
            Log::error('更新配送员信息失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error($e->getMessage(), 400), 400);
        }
    }

    /**
     * 删除配送员
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $result = $this->delivererService->deleteDeliverer($id);
            return response()->json(ApiResponse::success(null, '配送员删除成功'));
        } catch (\Exception $e) {
            Log::error('删除配送员失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error($e->getMessage(), 400), 400);
        }
    }

    /**
     * 更新配送员位置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateLocation(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'employee_id' => 'required|exists:employees,id',
                'latitude' => 'required|numeric',
                'longitude' => 'required|numeric',
            ]);

            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }

            $deliverer = $this->delivererService->updateLocation($request->all());
            return response()->json(ApiResponse::success($deliverer, '位置更新成功'));
        } catch (\Exception $e) {
            Log::error('更新配送员位置失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('更新配送员位置失败', 400), 400);
        }
    }
    
    /**
     * 配送员登录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        // 尝试查找用户
        $user = User::where('phone', $request->phone)->first();
        
        if ($user && Auth::attempt(['phone' => $request->phone, 'password' => $request->password])) {
            // 检查用户是否有关联的员工记录
            $employee = $user->employee;
            if (!$employee) {
                return response()->json(ApiResponse::error('此账号未关联员工记录', 401), 401);
            }
            
            // 检查员工是否是配送员
            if ($employee->role !== 'delivery') {
                return response()->json(ApiResponse::error('此账号不是配送员', 401), 401);
            }
            
            $deliverer = $this->delivererService->getDelivererByEmployeeId($employee->id);
            if (!$deliverer) {
                return response()->json(ApiResponse::error('此账号未关联配送员记录', 401), 401);
            }
            
            // 删除任何现有令牌（从员工模型删除）
            $employee->tokens()->delete();
            
            // 使用员工模型创建新令牌
            $token = $employee->createToken('deliverer-token')->plainTextToken;
            
            return response()->json(ApiResponse::success([
                'token' => $token,
                'user' => $user,
                'employee' => $employee,
                'deliverer' => $deliverer,
            ]));
        }
        
        return response()->json(ApiResponse::error('手机号或密码错误', 401), 401);
    }

    /**
     * 配送员仪表板数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function dashboard(Request $request)
    {
        try {
            // 现在token绑定到Employee模型，直接获取Employee
            $employee = $request->user();
            
            // 检查员工是否是配送员
            if (!$employee || $employee->role !== 'delivery') {
                return response()->json(ApiResponse::error('此账号不是配送员', 401), 401);
            }
            
            $deliverer = $this->delivererService->getDelivererByEmployeeId($employee->id);
            if (!$deliverer) {
                return response()->json(ApiResponse::error('此账号未关联配送员记录', 401), 401);
            }
            
            // 获取仪表板数据
            $dashboardData = [
                'deliverer' => $deliverer,
                'employee' => $employee,
                'stats' => [
                    'pending_orders' => DB::table('deliveries')
                        ->where('deliverer_id', $deliverer->id)
                        ->where('status', 'pending')
                        ->count(),
                    'in_progress_orders' => DB::table('deliveries')
                        ->where('deliverer_id', $deliverer->id)
                        ->where('status', 'in_progress')
                        ->count(),
                    'completed_today' => DB::table('deliveries')
                        ->where('deliverer_id', $deliverer->id)
                        ->where('status', 'completed')
                        ->whereDate('updated_at', today())
                        ->count(),
                    'total_rating' => $deliverer->rating,
                ],
                'recent_deliveries' => DB::table('deliveries')
                    ->join('orders', 'deliveries.order_id', '=', 'orders.id')
                    ->where('deliveries.deliverer_id', $deliverer->id)
                    ->select(
                        'deliveries.*', 
                        'orders.order_no', 
                        'orders.total', 
                        'orders.contact_name', 
                        'orders.contact_phone',
                        'orders.shipping_address',
                        'orders.status as order_status'
                    )
                    ->orderBy('deliveries.created_at', 'desc')
                    ->limit(10)
                    ->get(),
            ];
            
            return response()->json(ApiResponse::success($dashboardData));
        } catch (\Exception $e) {
            Log::error('获取配送员仪表板数据失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('获取仪表板数据失败', 500), 500);
        }
    }

    /**
     * 配送员统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics(Request $request)
    {
        try {
            // 现在token绑定到Employee模型，直接获取Employee
            $employee = $request->user();
            
            // 检查员工是否是配送员
            if (!$employee || $employee->role !== 'delivery') {
                return response()->json(ApiResponse::error('此账号不是配送员', 401), 401);
            }
            
            $deliverer = $this->delivererService->getDelivererByEmployeeId($employee->id);
            if (!$deliverer) {
                return response()->json(ApiResponse::error('此账号未关联配送员记录', 401), 401);
            }
            
            $period = $request->input('period', 'today'); // today, week, month
            
            // 根据时间段设置查询条件
            $startDate = null;
            $endDate = now();
            $previousStartDate = null;
            $previousEndDate = null;
            
            switch ($period) {
                case 'today':
                    $startDate = now()->startOfDay();
                    $previousStartDate = now()->subDay()->startOfDay();
                    $previousEndDate = now()->subDay()->endOfDay();
                    break;
                case 'week':
                    $startDate = now()->startOfWeek();
                    $previousStartDate = now()->subWeek()->startOfWeek();
                    $previousEndDate = now()->subWeek()->endOfWeek();
                    break;
                case 'month':
                    $startDate = now()->startOfMonth();
                    $previousStartDate = now()->subMonth()->startOfMonth();
                    $previousEndDate = now()->subMonth()->endOfMonth();
                    break;
            }
            
            // 当前时段统计
            $currentStats = $this->getDeliveryStats($deliverer->id, $startDate, $endDate);
            
            // 上一时段统计（用于计算趋势）
            $previousStats = $this->getDeliveryStats($deliverer->id, $previousStartDate, $previousEndDate);
            
            // 计算趋势
            $trends = $this->calculateTrends($currentStats, $previousStats);
            
            // 配送时段分析
            $timeDistribution = $this->getTimeDistribution($deliverer->id, $startDate, $endDate);
            
            // 配送区域统计
            $areaStats = $this->getAreaStats($deliverer->id, $startDate, $endDate);
            
            // 团队排名
            $ranking = $this->getTeamRanking($deliverer->id, $startDate, $endDate);
            
            $statisticsData = [
                'total_orders' => $currentStats['total_orders'],
                'completed_orders' => $currentStats['completed_orders'],
                'pending_orders' => $currentStats['pending_orders'],
                'in_progress_orders' => $currentStats['in_progress_orders'],
                'completion_rate' => $currentStats['completion_rate'],
                'avg_delivery_time' => $currentStats['avg_delivery_time'],
                'average_rating' => $currentStats['average_rating'],
                'orders_trend' => $trends['orders_trend'],
                'completed_trend' => $trends['completed_trend'],
                'time_trend' => $trends['time_trend'],
                'rating_trend' => $trends['rating_trend'],
                'time_distribution' => $timeDistribution,
                'area_stats' => $areaStats,
                'ranking' => $ranking,
            ];
            
            return response()->json(ApiResponse::success($statisticsData));
        } catch (\Exception $e) {
            Log::error('获取配送员统计数据失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('获取统计数据失败', 500), 500);
        }
    }
    
    /**
     * 获取配送统计数据
     */
    private function getDeliveryStats($delivererId, $startDate, $endDate)
    {
        $deliveries = DB::table('deliveries')
            ->join('orders', 'deliveries.order_id', '=', 'orders.id')
            ->where('deliveries.deliverer_id', $delivererId)
            ->whereBetween('deliveries.created_at', [$startDate, $endDate])
            ->select(
                'deliveries.*',
                'orders.total',
                'orders.created_at as order_created_at',
                'orders.shipping_address'
            )
            ->get();
            
        $totalOrders = $deliveries->count();
        $completedOrders = $deliveries->where('status', 'completed')->count();
        $pendingOrders = $deliveries->where('status', 'pending')->count();
        $inProgressOrders = $deliveries->where('status', 'in_progress')->count();
        
        $completionRate = $totalOrders > 0 ? round(($completedOrders / $totalOrders) * 100, 1) : 0;
        
        // 计算平均配送时间（分钟）
        $completedDeliveries = $deliveries->where('status', 'completed');
        $avgDeliveryTime = 0;
        if ($completedDeliveries->count() > 0) {
            $totalTime = 0;
            foreach ($completedDeliveries as $delivery) {
                if ($delivery->updated_at && $delivery->created_at) {
                    $startTime = \Carbon\Carbon::parse($delivery->created_at);
                    $endTime = \Carbon\Carbon::parse($delivery->updated_at);
                    $totalTime += $endTime->diffInMinutes($startTime);
                }
            }
            $avgDeliveryTime = round($totalTime / $completedDeliveries->count());
        }
        
        // 平均评分（模拟数据）
        $averageRating = 4.8;
        
        return [
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'pending_orders' => $pendingOrders,
            'in_progress_orders' => $inProgressOrders,
            'completion_rate' => $completionRate,
            'avg_delivery_time' => $avgDeliveryTime,
            'average_rating' => $averageRating,
        ];
    }
    
    /**
     * 计算趋势百分比
     */
    private function calculateTrends($current, $previous)
    {
        $ordersTrend = $this->calculateTrendPercentage($current['total_orders'], $previous['total_orders']);
        $completedTrend = $this->calculateTrendPercentage($current['completed_orders'], $previous['completed_orders']);
        $timeTrend = $this->calculateTrendPercentage($current['avg_delivery_time'], $previous['avg_delivery_time']);
        $ratingTrend = $this->calculateTrendPercentage($current['average_rating'], $previous['average_rating']);
        
        return [
            'orders_trend' => $ordersTrend,
            'completed_trend' => $completedTrend,
            'time_trend' => $timeTrend,
            'rating_trend' => $ratingTrend,
        ];
    }
    
    /**
     * 计算趋势百分比
     */
    private function calculateTrendPercentage($current, $previous)
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        
        return round((($current - $previous) / $previous) * 100, 1);
    }
    
    /**
     * 获取配送时段分析
     */
    private function getTimeDistribution($delivererId, $startDate, $endDate)
    {
        $timeStats = DB::table('deliveries')
            ->where('deliverer_id', $delivererId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();
            
        // 填充所有小时（8-20点）
        $hours = range(8, 20);
        $distribution = [];
        
        foreach ($hours as $hour) {
            $stat = $timeStats->firstWhere('hour', $hour);
            $distribution[] = [
                'hour' => $hour,
                'count' => $stat ? $stat->count : 0
            ];
        }
        
        return $distribution;
    }
    
    /**
     * 获取配送区域统计
     */
    private function getAreaStats($delivererId, $startDate, $endDate)
    {
        $areaStats = DB::table('deliveries')
            ->join('deliverers', 'deliveries.deliverer_id', '=', 'deliverers.id')
            ->where('deliveries.deliverer_id', $delivererId)
            ->whereBetween('deliveries.created_at', [$startDate, $endDate])
            ->selectRaw('deliverers.delivery_area as name, COUNT(*) as count')
            ->groupBy('deliverers.delivery_area')
            ->orderByDesc('count')
            ->limit(5)
            ->get();
            
        return $areaStats->map(function($item) {
            return [
                'name' => $item->name ?: '未知区域',
                'count' => $item->count
            ];
        })->toArray();
    }
    
    /**
     * 获取团队排名
     */
    private function getTeamRanking($currentDelivererId, $startDate, $endDate)
    {
        $rankings = DB::table('deliveries')
            ->join('deliverers', 'deliveries.deliverer_id', '=', 'deliverers.id')
            ->join('employees', 'deliverers.employee_id', '=', 'employees.id')
            ->whereBetween('deliveries.created_at', [$startDate, $endDate])
            ->selectRaw('
                deliverers.id,
                employees.name,
                COUNT(*) as total_orders,
                SUM(CASE WHEN deliveries.status = "completed" THEN 1 ELSE 0 END) as completed_orders,
                ROUND((SUM(CASE WHEN deliveries.status = "completed" THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as completion_rate
            ')
            ->groupBy('deliverers.id', 'employees.name')
            ->orderByDesc('completed_orders')
            ->limit(10)
            ->get();
            
        return $rankings->map(function($item) use ($currentDelivererId) {
            return [
                'name' => $item->name,
                'completedOrders' => $item->completed_orders,
                'completionRate' => $item->completion_rate,
                'isCurrentUser' => $item->id == $currentDelivererId
            ];
        })->toArray();
    }
} 