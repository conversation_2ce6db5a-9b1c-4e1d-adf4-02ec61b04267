# CRM UniApp 页面加载优化方案

## 概述

本优化方案通过引入统一的加载管理器、缓存管理器和页面混入，解决了以下问题：

1. **重复加载**：onShow时重复请求数据
2. **加载状态冲突**：多个loading同时显示
3. **缺乏缓存机制**：每次都重新请求
4. **防抖处理不完善**：搜索防抖时间过短
5. **错误处理不统一**：各页面错误处理逻辑重复

## 核心组件

### 1. 加载管理器 (loading-manager.js)

统一管理页面加载状态，避免多个loading冲突。

**主要功能：**
- 支持多个加载状态并存
- 自动处理loading和toast的冲突
- 提供唯一标识避免状态混乱

**使用方法：**
```javascript
import loadingManager from '@/utils/loading-manager.js'

// 显示加载
loadingManager.showLoading('myKey', { title: '加载中...' })

// 隐藏加载
loadingManager.hideLoading('myKey')

// 显示提示
loadingManager.showToast('操作成功', 'success', 2000)
```

### 2. 缓存管理器 (cache-manager.js)

提供数据缓存功能，减少重复请求，提升页面加载性能。

**缓存配置：**
- 客户数据：10分钟
- 订单数据：3分钟
- 商品数据：30分钟
- 统计数据：5分钟

**使用方法：**
```javascript
import cacheManager from '@/utils/cache-manager.js'

// 设置缓存
cacheManager.set('clients', 'list', data, params)

// 获取缓存
const cached = cacheManager.get('clients', 'list', params)

// 获取或设置缓存
const data = await cacheManager.getOrSet(
  'clients', 
  'list', 
  () => api.getClientList(params),
  params
)
```

### 3. 页面混入 (page-mixin.js)

提供统一的页面加载、缓存、错误处理逻辑。

**主要功能：**
- 统一的生命周期管理
- 自动的刷新判断逻辑
- 标准化的搜索防抖处理
- 统一的错误处理机制
- 分页加载支持

## 使用指南

### 1. 在页面中使用混入

```javascript
import pageMixin from '@/utils/page-mixin.js'

export default {
  mixins: [pageMixin],
  
  data() {
    return {
      // 覆盖混入的缓存配置
      cacheType: 'clients',
      cacheKey: 'list',
      
      // 页面特有数据
      clientList: []
    }
  },
  
  // 页面生命周期回调
  onPageLoad() {
    this.loadInitialData()
  },
  
  methods: {
    // 必须实现的方法
    async loadData(isRefresh = false) {
      // 加载数据的具体实现
    },
    
    async searchData(keyword) {
      // 搜索数据的具体实现
    }
  }
}
```

### 2. 必须实现的方法

#### loadData(isRefresh)
页面数据加载方法，混入会自动调用。

```javascript
async loadData(isRefresh = false) {
  const params = {
    page: isRefresh ? 1 : this.currentPage,
    per_page: this.pageSize
  }
  
  // 使用缓存加载数据
  const response = await this.loadWithCache(
    () => api.getList(params),
    params,
    3 * 60 * 1000 // 3分钟缓存
  )
  
  // 处理响应数据
  this.handleDataResponse(response, isRefresh)
}
```

#### searchData(keyword)
搜索数据方法，混入会自动调用。

```javascript
async searchData(keyword) {
  if (!keyword || !keyword.trim()) {
    // 清空搜索，重新加载列表
    await this.loadData(true)
    return
  }
  
  // 搜索不使用缓存
  const response = await api.search(keyword.trim())
  
  // 处理搜索结果
  this.handleSearchResponse(response)
}
```

### 3. 可选的生命周期回调

```javascript
// 页面加载完成
onPageLoad(options) {
  console.log('页面加载', options)
},

// 页面显示
onPageShow() {
  console.log('页面显示')
},

// 页面隐藏
onPageHide() {
  console.log('页面隐藏')
},

// 页面卸载
onPageUnload() {
  console.log('页面卸载')
}
```

### 4. 混入提供的方法

#### 加载状态管理
```javascript
this.showPageLoading('加载中...')
this.hidePageLoading()
this.showListLoading()
this.hideListLoading()
this.showSearchLoading()
this.hideSearchLoading()
```

#### 错误处理
```javascript
this.handlePageError(error, '加载数据')
this.handleListError(error, '搜索')
this.showToast('操作成功')
```

#### 数据管理
```javascript
this.setListData(data, isRefresh)
this.updatePagination(response)
this.refreshData()
this.loadMoreData()
```

#### 搜索处理
```javascript
this.handleSearchInput(value)
this.performSearch()
this.clearSearchTimer()
```

#### 缓存管理
```javascript
this.loadWithCache(fetchFn, params, ttl)
this.clearPageCache()
```

## 优化效果

### 1. 性能提升
- **缓存机制**：减少重复请求，提升响应速度
- **防重复请求**：避免用户快速点击导致的重复请求
- **智能刷新**：只在必要时刷新数据

### 2. 用户体验改善
- **统一加载状态**：避免loading冲突，提供一致的加载体验
- **优化搜索体验**：800ms防抖，减少无效请求
- **错误处理统一**：提供友好的错误提示

### 3. 代码质量提升
- **减少重复代码**：统一的页面逻辑处理
- **标准化开发**：统一的开发模式和API
- **易于维护**：集中的状态管理和错误处理

## 配置说明

### 缓存配置
可以在页面中覆盖默认的缓存配置：

```javascript
data() {
  return {
    cacheEnabled: true,        // 是否启用缓存
    cacheType: 'clients',      // 缓存类型
    cacheKey: 'list',          // 缓存键名
    searchDebounceTime: 800,   // 搜索防抖时间
    refreshThrottle: 2000,     // 刷新节流时间
  }
}
```

### 请求配置
在API调用时可以配置加载选项：

```javascript
// 自定义加载提示
api.getList(params, {
  loadingTitle: '正在加载客户列表...',
  loadingMask: true,
  showError: true,
  preventDuplicate: true
})
```

## 注意事项

1. **混入使用**：确保正确引入和使用混入
2. **方法实现**：必须实现`loadData`和`searchData`方法
3. **数据绑定**：使用混入提供的`listData`而不是自定义的数组
4. **缓存清理**：在数据更新后及时清理相关缓存
5. **错误处理**：使用混入提供的错误处理方法

## 示例页面

参考已优化的页面：
- `pages/clients/clients.vue` - 客户列表页面
- `pages/orders/orders.vue` - 订单列表页面

这些页面展示了完整的优化方案应用。 