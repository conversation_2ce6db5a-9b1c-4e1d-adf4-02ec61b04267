<template>
	<view class="orders-container">
		<!-- 顶部统计概览 -->
		<view class="stats-overview">
			<view class="stats-card">
				<text class="stats-number">{{ orderStats.total || 0 }}</text>
				<text class="stats-label">总订单</text>
			</view>
			<view class="stats-card">
				<text class="stats-number">{{ orderStats.pending || 0 }}</text>
				<text class="stats-label">待处理</text>
			</view>
			<view class="stats-card">
				<text class="stats-number">¥{{ getFormattedAmount(orderStats.total_amount || 0) }}</text>
				<text class="stats-label">总金额</text>
			</view>
			<view class="stats-card">
				<text class="stats-number">{{ orderStats.today || 0 }}</text>
				<text class="stats-label">今日订单</text>
			</view>
		</view>
		
		<!-- 搜索和筛选区域 -->
		<view class="search-filter-section">
			<!-- 搜索框 -->
			<view class="search-box">
				<view class="search-input-wrapper">
					<text class="search-icon">🔍</text>
					<input 
						class="search-input" 
						placeholder="搜索订单号、客户名称或手机号"
						v-model="searchKeyword"
						@input="onSearchInput"
						@confirm="onSearchConfirm"
					/>
					<text class="clear-icon" v-if="searchKeyword" @tap="clearSearch">✕</text>
				</view>
			</view>
			
			<!-- 高级筛选 -->
			<view class="filter-row">
				<view class="filter-group">
					<text class="filter-label">状态:</text>
					<scroll-view class="status-filter-scroll" scroll-x="true">
						<view class="status-filter-item" 
							:class="{ active: currentStatus === '' }"
							@tap="filterByStatus('')"
						>
							<text>全部</text>
						</view>
						<view class="status-filter-item" 
							:class="{ active: currentStatus === 'pending' }"
							@tap="filterByStatus('pending')"
						>
							<text>待付款</text>
						</view>
						<view class="status-filter-item" 
							:class="{ active: currentStatus === 'paid' }"
							@tap="filterByStatus('paid')"
						>
							<text>已付款</text>
						</view>
						<view class="status-filter-item" 
							:class="{ active: currentStatus === 'shipped' }"
							@tap="filterByStatus('shipped')"
						>
							<text>已发货</text>
						</view>
						<view class="status-filter-item" 
							:class="{ active: currentStatus === 'delivered' }"
							@tap="filterByStatus('delivered')"
						>
							<text>已送达</text>
						</view>
						<view class="status-filter-item" 
							:class="{ active: currentStatus === 'cancelled' }"
							@tap="filterByStatus('cancelled')"
						>
							<text>已取消</text>
						</view>
					</scroll-view>
				</view>
			</view>
			
			<!-- 时间和排序筛选 -->
			<view class="filter-row">
				<view class="filter-item" @tap="showDatePicker">
					<text class="filter-icon">📅</text>
					<text class="filter-text">{{ dateRangeText }}</text>
				</view>
				<view class="filter-item" @tap="showSortOptions">
					<text class="filter-icon">📊</text>
					<text class="filter-text">{{ sortText }}</text>
				</view>
				<view class="filter-item" @tap="toggleBatchMode">
					<text class="filter-icon">☑️</text>
					<text class="filter-text">{{ getBatchModeText() }}</text>
				</view>
			</view>
		</view>
		
		<!-- 批量操作栏 -->
		<view class="batch-actions" v-if="batchMode && selectedOrders.length > 0">
			<view class="batch-info">
				<text>已选择 {{ selectedOrders.length }} 个订单</text>
			</view>
			<view class="batch-buttons">
				<button class="batch-btn export-btn" @tap="exportOrders">导出</button>
				<button class="batch-btn status-btn" @tap="batchUpdateStatus">批量更新状态</button>
			</view>
		</view>
		
		<!-- 订单列表 -->
		<view class="orders-list">
			<view class="order-card" 
				v-for="order in orderList" 
				:key="order.id" 
				:class="{ selected: isOrderSelected(order.id) }"
				@tap="handleOrderTap(order)"
			>
				<!-- 批量选择复选框 -->
				<view class="order-checkbox" v-if="batchMode" @tap.stop="toggleOrderSelection(order.id)">
					<view class="checkbox" :class="{ checked: isOrderSelected(order.id) }">
						<text class="checkbox-icon" v-if="isOrderSelected(order.id)">✓</text>
					</view>
				</view>
				
				<!-- 订单头部 -->
				<view class="order-header">
					<view class="order-main-info">
						<text class="order-no">{{ order.order_no }}</text>
						<view class="order-status-badge" :class="getStatusClass(order.status)">
							<text class="status-text">{{ getStatusText(order.status) }}</text>
						</view>
					</view>
					<text class="order-time">{{ formatTime(order.created_at) }}</text>
				</view>
				
				<!-- 客户信息 -->
				<view class="order-customer">
					<view class="customer-avatar">
						<text class="avatar-text">{{ getCustomerInitial(getCustomerName(order)) }}</text>
					</view>
					<view class="customer-info">
						<text class="customer-name">{{ getCustomerName(order) }}</text>
						<text class="customer-phone">{{ getCustomerPhone(order) }}</text>
					</view>
				</view>
				
				<!-- 订单详情 -->
				<view class="order-details">
					<view class="detail-row">
						<text class="detail-label">商品数量:</text>
						<text class="detail-value">{{ order.items_count || 0 }} 件</text>
					</view>
					<view class="detail-row">
						<text class="detail-label">订单金额:</text>
						<text class="detail-value amount">¥{{ getFormattedOrderTotal(order.total) }}</text>
					</view>
					<view class="detail-row" v-if="order.payment_method">
						<text class="detail-label">支付方式:</text>
						<text class="detail-value">{{ getPaymentMethodText(order.payment_method) }}</text>
					</view>
					<view class="detail-row" v-if="order.shipping_address">
						<text class="detail-label">收货地址:</text>
						<text class="detail-value address">{{ order.shipping_address }}</text>
					</view>
				</view>
				
				<!-- 快速操作按钮 -->
				<view class="order-actions" v-if="!batchMode">
					<button class="action-btn primary" @tap.stop="goToOrderDetail(order)">
						<text>查看详情</text>
					</button>
					<button class="action-btn secondary" 
						v-if="canUpdateStatus(order.status)" 
						@tap.stop="quickUpdateStatus(order)"
					>
						<text>{{ getNextStatusText(order.status) }}</text>
					</button>
					<button class="action-btn danger" 
						v-if="canCancel(order.status)" 
						@tap.stop="cancelOrder(order)"
					>
						<text>取消订单</text>
					</button>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-if="orderList.length === 0 && !listLoading">
				<view class="empty-icon">📦</view>
				<text class="empty-title">暂无订单数据</text>
				<text class="empty-desc">{{ getEmptyStateDesc() }}</text>
				<button class="empty-action" @tap="refreshData">刷新数据</button>
			</view>
			
			<!-- 加载状态 -->
			<view class="loading-more" v-if="listLoading">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
			
			<!-- 加载完成提示 -->
			<view class="load-complete" v-if="!hasMore && orderList.length > 0">
				<text class="complete-text">已加载全部订单</text>
			</view>
		</view>
		
		<!-- 浮动操作按钮 -->
		<view class="fab-container">
			<button class="fab-btn" @tap="goToProxyOrder">
				<text class="fab-icon">+</text>
				<text class="fab-text">代客下单</text>
			</button>
		</view>
		
		<!-- 日期选择器弹窗 -->
		<view class="modal-overlay" v-if="showDateModal" @tap="closeDateModal">
			<view class="date-modal" @tap.stop>
				<view class="modal-header">
					<text class="modal-title">选择时间范围</text>
					<text class="modal-close" @tap="closeDateModal">✕</text>
				</view>
				<view class="date-options">
					<view class="date-option" @tap="selectDateRange('today')">
						<text>今天</text>
					</view>
					<view class="date-option" @tap="selectDateRange('week')">
						<text>本周</text>
					</view>
					<view class="date-option" @tap="selectDateRange('month')">
						<text>本月</text>
					</view>
					<view class="date-option" @tap="selectDateRange('custom')">
						<text>自定义</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 排序选择弹窗 -->
		<view class="modal-overlay" v-if="showSortModal" @tap="closeSortModal">
			<view class="sort-modal" @tap.stop>
				<view class="modal-header">
					<text class="modal-title">排序方式</text>
					<text class="modal-close" @tap="closeSortModal">✕</text>
				</view>
				<view class="sort-options">
					<view class="sort-option" 
						:class="{ active: currentSort === 'created_at_desc' }"
						@tap="selectSort('created_at_desc')"
					>
						<text>创建时间 (最新)</text>
					</view>
					<view class="sort-option" 
						:class="{ active: currentSort === 'created_at_asc' }"
						@tap="selectSort('created_at_asc')"
					>
						<text>创建时间 (最早)</text>
					</view>
					<view class="sort-option" 
						:class="{ active: currentSort === 'total_desc' }"
						@tap="selectSort('total_desc')"
					>
						<text>金额 (高到低)</text>
					</view>
					<view class="sort-option" 
						:class="{ active: currentSort === 'total_asc' }"
						@tap="selectSort('total_asc')"
					>
						<text>金额 (低到高)</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import orderApi from '../../api/order.js'
import { formatDateTime, formatDate } from '../../utils/date-formatter.js'

export default {
	data() {
		return {
			// 订单数据
			orderList: [],
			
			// 统计数据
			orderStats: {
				total: 0,
				pending: 0,
				total_amount: 0,
				today: 0
			},
			
			// 筛选条件
			currentStatus: '',
			currentSort: 'created_at_desc',
			dateRange: '',
			searchKeyword: '',
			
			// 分页状态
			currentPage: 1,
			pageSize: 20,
			hasMore: true,
			listLoading: false,
			
			// UI状态
			batchMode: false,
			selectedOrders: [],
			showDateModal: false,
			showSortModal: false,
			
			// 状态映射
			statusMap: {
				'pending': '待付款',
				'paid': '已付款', 
				'shipped': '已发货',
				'delivered': '已送达',
				'cancelled': '已取消',
				'refunded': '已退款'
			},
			
			// 支付方式映射
			paymentMethodMap: {
				'cod': '货到付款',
				'wechat': '微信支付',
				'alipay': '支付宝',
				'cash': '现金支付',
				'bank': '银行转账'
			}
		}
	},
	
	computed: {
		dateRangeText() {
			if (!this.dateRange) return '全部时间'
			switch (this.dateRange) {
				case 'today': return '今天'
				case 'week': return '本周'
				case 'month': return '本月'
				default: return '自定义'
			}
		},
		
		sortText() {
			switch (this.currentSort) {
				case 'created_at_desc': return '最新订单'
				case 'created_at_asc': return '最早订单'
				case 'total_desc': return '金额降序'
				case 'total_asc': return '金额升序'
				default: return '默认排序'
			}
		}
	},
	
	onLoad() {
		this.initPage()
	},
	
	onShow() {
		// 页面显示时检查是否需要刷新
		this.checkRefresh()
	},
	
	onPullDownRefresh() {
		this.refreshData()
	},
	
	onReachBottom() {
		this.loadMore()
	},
	
	methods: {
		/**
		 * 初始化页面
		 */
		async initPage() {
			console.log('订单页面初始化')
			try {
				uni.showLoading({ title: '加载中...' })
				
				// 并行加载统计和列表数据
				await Promise.all([
					this.loadStats(),
					this.loadOrders(true)
				])
				
			} catch (error) {
				console.error('页面初始化失败:', error)
				this.showError('加载失败，请重试')
			} finally {
				uni.hideLoading()
			}
		},
		
		/**
		 * 检查是否需要刷新
		 */
		checkRefresh() {
			// 简单的刷新策略：如果列表为空则刷新
			if (this.orderList.length === 0) {
				this.initPage()
			}
		},
		
		/**
		 * 加载统计数据
		 */
		async loadStats() {
			try {
				const response = await orderApi.getOrderStats()
				
				if (response.code === 200 && response.data) {
					this.orderStats = {
						total: response.data.total || 0,
						pending: response.data.pending || 0,
						total_amount: response.data.total_amount || 0,
						today: response.data.today || 0
					}
					console.log('统计数据加载成功:', this.orderStats)
				} else {
					throw new Error(response.message || '统计数据加载失败')
				}
				
			} catch (error) {
				console.error('加载统计数据失败:', error)
				// 统计数据失败不影响主功能，使用默认值
				this.orderStats = { total: 0, pending: 0, total_amount: 0, today: 0 }
			}
		},
		
		/**
		 * 加载订单列表
		 */
		async loadOrders(refresh = false) {
			if (this.listLoading) return
			
			try {
				this.listLoading = true
				
				// 构建请求参数
				const params = {
					page: refresh ? 1 : this.currentPage,
					per_page: this.pageSize
				}
				
				// 添加筛选条件
				if (this.currentStatus) params.status = this.currentStatus
				if (this.currentSort) params.sort = this.currentSort
				if (this.dateRange) params.date_range = this.dateRange
				if (this.searchKeyword) params.keyword = this.searchKeyword.trim()
				
				console.log('请求参数:', params)
				
				const response = await orderApi.getOrderList(params)
				
				if (response.code === 200) {
					const newOrders = this.parseOrderData(response.data)
					
					if (refresh) {
						// 刷新：替换数据
						this.orderList = newOrders
						this.currentPage = 1
						this.hasMore = newOrders.length >= this.pageSize
					} else {
						// 加载更多：追加数据（去重）
						this.appendOrders(newOrders)
					}
					
					console.log(`订单加载完成: ${newOrders.length}条，总计: ${this.orderList.length}条`)
					
				} else {
					throw new Error(response.message || '加载失败')
				}
				
			} catch (error) {
				console.error('加载订单失败:', error)
				this.showError('加载订单失败')
			} finally {
				this.listLoading = false
				uni.stopPullDownRefresh()
			}
		},
		
		/**
		 * 解析订单数据
		 */
		parseOrderData(data) {
			if (Array.isArray(data)) {
				return data
			} else if (data && Array.isArray(data.data)) {
				return data.data
			} else if (data && Array.isArray(data.orders)) {
				return data.orders
			} else {
				console.warn('未知的数据格式:', data)
				return []
			}
		},
		
		/**
		 * 追加订单数据（去重）
		 */
		appendOrders(newOrders) {
			const existingIds = new Set(this.orderList.map(order => order.id))
			const uniqueOrders = newOrders.filter(order => !existingIds.has(order.id))
			
			this.orderList.push(...uniqueOrders)
			this.currentPage++
			this.hasMore = uniqueOrders.length >= this.pageSize
			
			console.log(`追加订单: 新${newOrders.length}条，去重后${uniqueOrders.length}条`)
		},
		
		/**
		 * 刷新数据
		 */
		async refreshData() {
			console.log('刷新订单数据')
			await Promise.all([
				this.loadStats(),
				this.loadOrders(true)
			])
		},
		
		/**
		 * 加载更多
		 */
		async loadMore() {
			if (!this.hasMore || this.listLoading) return
			
			console.log('加载更多订单')
			await this.loadOrders(false)
		},
		
		/**
		 * 搜索处理
		 */
		onSearchInput(e) {
			this.searchKeyword = e.detail.value
			// 防抖搜索
			clearTimeout(this.searchTimer)
			this.searchTimer = setTimeout(() => {
				if (this.searchKeyword.trim()) {
					this.performSearch()
				}
			}, 500)
		},
		
		onSearchConfirm() {
			this.performSearch()
		},
		
		async performSearch() {
			console.log('执行搜索:', this.searchKeyword)
			await this.loadOrders(true)
		},
		
		clearSearch() {
			this.searchKeyword = ''
			this.loadOrders(true)
		},
		
		/**
		 * 筛选处理
		 */
		async filterByStatus(status) {
			if (this.currentStatus === status) return
			
			console.log(`状态筛选: ${this.currentStatus} -> ${status}`)
			this.currentStatus = status
			await this.loadOrders(true)
			
			const statusText = status ? this.statusMap[status] : '全部订单'
			uni.showToast({ title: `已切换到：${statusText}`, icon: 'none' })
		},
		
		async selectSort(sort) {
			if (this.currentSort === sort) {
				this.closeSortModal()
				return
			}
			
			console.log(`排序切换: ${this.currentSort} -> ${sort}`)
			this.currentSort = sort
			this.closeSortModal()
			await this.loadOrders(true)
			
			uni.showToast({ title: '排序已更新', icon: 'none' })
		},
		
		async selectDateRange(range) {
			if (this.dateRange === range) {
				this.closeDateModal()
				return
			}
			
			console.log(`日期范围: ${this.dateRange} -> ${range}`)
			this.dateRange = range
			this.closeDateModal()
			await this.loadOrders(true)
			
			uni.showToast({ title: '时间范围已更新', icon: 'none' })
		},
		
		/**
		 * UI控制
		 */
		showDatePicker() {
			this.showDateModal = true
		},
		
		closeDateModal() {
			this.showDateModal = false
		},
		
		showSortOptions() {
			this.showSortModal = true
		},
		
		closeSortModal() {
			this.showSortModal = false
		},
		
		toggleBatchMode() {
			this.batchMode = !this.batchMode
			if (!this.batchMode) {
				this.selectedOrders = []
			}
		},
		
		/**
		 * 批量操作
		 */
		isOrderSelected(orderId) {
			return this.selectedOrders.includes(orderId)
		},
		
		toggleOrderSelection(orderId) {
			const index = this.selectedOrders.indexOf(orderId)
			if (index > -1) {
				this.selectedOrders.splice(index, 1)
			} else {
				this.selectedOrders.push(orderId)
			}
		},
		
		exportOrders() {
			uni.showToast({ title: '导出功能开发中', icon: 'none' })
		},
		
		batchUpdateStatus() {
			uni.showToast({ title: '批量更新功能开发中', icon: 'none' })
		},
		
		/**
		 * 订单操作
		 */
		handleOrderTap(order) {
			if (this.batchMode) {
				this.toggleOrderSelection(order.id)
			} else {
				this.goToOrderDetail(order)
			}
		},
		
		goToOrderDetail(order) {
			uni.navigateTo({
				url: `/pages/orders/order-detail?id=${order.id}`
			})
		},
		
		goToProxyOrder() {
			uni.navigateTo({
				url: '/pages/proxy-order/select-client'
			})
		},
		
		async quickUpdateStatus(order) {
			const nextStatus = this.getNextStatus(order.status)
			if (!nextStatus) return
			
			try {
				await orderApi.updateOrderStatus(order.id, nextStatus)
				uni.showToast({ title: '状态更新成功', icon: 'success' })
				this.refreshData()
			} catch (error) {
				console.error('更新状态失败:', error)
				uni.showToast({ title: '更新失败', icon: 'none' })
			}
		},
		
		async cancelOrder(order) {
			const result = await this.showConfirm(`确定要取消订单 ${order.order_no} 吗？`)
			if (!result) return
			
			try {
				await orderApi.cancelOrder(order.id)
				uni.showToast({ title: '订单已取消', icon: 'success' })
				this.refreshData()
			} catch (error) {
				console.error('取消订单失败:', error)
				uni.showToast({ title: '取消失败', icon: 'none' })
			}
		},
		
		/**
		 * 工具方法
		 */
		getFormattedAmount(amount) {
			if (!amount && amount !== 0) return '0'
			const numAmount = parseFloat(amount)
			if (isNaN(numAmount)) return '0'
			
			if (numAmount >= 10000) {
				return (numAmount / 10000).toFixed(1) + '万'
			}
			return numAmount.toFixed(0)
		},
		
		getFormattedOrderTotal(total) {
			if (!total && total !== 0) return '0.00'
			const numTotal = parseFloat(total)
			if (isNaN(numTotal)) return '0.00'
			return numTotal.toFixed(2)
		},
		
		getBatchModeText() {
			return this.batchMode ? '取消批量' : '批量操作'
		},
		
		getCustomerName(order) {
			return order.user?.name || order.contact_name || '未知客户'
		},
		
		getCustomerPhone(order) {
			return order.user?.phone || order.contact_phone || ''
		},
		
		getCustomerInitial(name) {
			return name ? name.charAt(0).toUpperCase() : '?'
		},
		
		getStatusText(status) {
			return this.statusMap[status] || status
		},
		
		getStatusClass(status) {
			return `status-${status}`
		},
		
		getPaymentMethodText(method) {
			return this.paymentMethodMap[method] || method
		},
		
		canUpdateStatus(status) {
			return ['pending', 'paid', 'shipped'].includes(status)
		},
		
		canCancel(status) {
			return ['pending', 'paid'].includes(status)
		},
		
		getNextStatus(status) {
			const statusFlow = {
				'pending': 'paid',
				'paid': 'shipped',
				'shipped': 'delivered'
			}
			return statusFlow[status]
		},
		
		getNextStatusText(status) {
			switch (status) {
				case 'pending': return '确认付款'
				case 'paid': return '发货'
				case 'shipped': return '确认送达'
				default: return '更新状态'
			}
		},
		
		getEmptyStateDesc() {
			if (this.searchKeyword) return '没有找到匹配的订单'
			if (this.currentStatus) return `没有${this.getStatusText(this.currentStatus)}的订单`
			return '还没有任何订单，快去创建第一个订单吧'
		},
		
		formatTime(dateString) {
			return formatDateTime(dateString)
		},
		
		formatDate(dateString) {
			return formatDate(dateString)
		},
		
		showError(message) {
			uni.showToast({ title: message, icon: 'none' })
		},
		
		showConfirm(content) {
			return new Promise((resolve) => {
				uni.showModal({
					title: '确认操作',
					content,
					success: (res) => resolve(res.confirm),
					fail: () => resolve(false)
				})
			})
		}
	}
}
</script>

<style scoped>
.orders-container {
	background: #f5f7fa;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 统计概览 */
.stats-overview {
	display: flex;
	background: #ffffff;
	padding: 32rpx 20rpx;
	margin-top: 280rpx; /* 为固定搜索栏预留空间 */
	margin-bottom: 16rpx;
}

.stats-card {
	flex: 1;
	text-align: center;
	padding: 0 16rpx;
}

.stats-card:not(:last-child) {
	border-right: 1rpx solid #f0f0f0;
}

.stats-number {
	display: block;
	font-size: 36rpx;
	font-weight: 600;
	color: #007AFF;
	margin-bottom: 8rpx;
}

.stats-label {
	font-size: 24rpx;
	color: #666666;
}

/* 搜索筛选区域 */
.search-filter-section {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
	background: #ffffff;
	padding: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-box {
	margin-bottom: 24rpx;
}

.search-input-wrapper {
	position: relative;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx 80rpx 24rpx 60rpx;
	display: flex;
	align-items: center;
}

.search-icon {
	position: absolute;
	left: 24rpx;
	font-size: 32rpx;
	color: #999999;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
}

.clear-icon {
	position: absolute;
	right: 24rpx;
	font-size: 32rpx;
	color: #999999;
	padding: 8rpx;
}

.filter-row {
	margin-bottom: 16rpx;
}

.filter-row:last-child {
	margin-bottom: 0;
}

.filter-group {
	display: flex;
	align-items: center;
}

.filter-label {
	font-size: 28rpx;
	color: #333333;
	margin-right: 16rpx;
	min-width: 80rpx;
}

.status-filter-scroll {
	flex: 1;
	white-space: nowrap;
}

.status-filter-item {
	display: inline-block;
	padding: 12rpx 24rpx;
	margin-right: 16rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #666666;
	border: 2rpx solid transparent;
}

.status-filter-item.active {
	background: #007AFF;
	color: #ffffff;
	border-color: #007AFF;
}

.filter-row {
	display: flex;
	gap: 16rpx;
}

.filter-item {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 16rpx 12rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 24rpx;
	color: #666666;
}

.filter-icon {
	margin-right: 8rpx;
	font-size: 28rpx;
}

.filter-text {
	font-size: 24rpx;
}

/* 批量操作栏 */
.batch-actions {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #fff3cd;
	padding: 16rpx 20rpx;
	margin-bottom: 16rpx;
}

.batch-info {
	font-size: 28rpx;
	color: #856404;
}

.batch-buttons {
	display: flex;
	gap: 16rpx;
}

.batch-btn {
	padding: 12rpx 24rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	border: none;
}

.export-btn {
	background: #28a745;
	color: #ffffff;
}

.status-btn {
	background: #007AFF;
	color: #ffffff;
}

/* 订单列表 */
.orders-list {
	padding: 0 20rpx;
}

.order-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	position: relative;
	border: 2rpx solid transparent;
}

.order-card.selected {
	border-color: #007AFF;
	background: #f0f8ff;
}

.order-checkbox {
	position: absolute;
	top: 16rpx;
	right: 16rpx;
}

.checkbox {
	width: 40rpx;
	height: 40rpx;
	border: 2rpx solid #d1d1d6;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.checkbox.checked {
	background: #007AFF;
	border-color: #007AFF;
}

.checkbox-icon {
	color: #ffffff;
	font-size: 24rpx;
	font-weight: 600;
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 24rpx;
}

.order-main-info {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.order-no {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.order-status-badge {
	padding: 6rpx 16rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
}

.status-pending {
	background: #fff3cd;
	color: #856404;
}

.status-paid {
	background: #d4edda;
	color: #155724;
}

.status-shipped {
	background: #cce5ff;
	color: #004085;
}

.status-delivered {
	background: #d1ecf1;
	color: #0c5460;
}

.status-cancelled {
	background: #f8d7da;
	color: #721c24;
}

.order-time {
	font-size: 24rpx;
	color: #999999;
}

.order-customer {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
}

.customer-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	background: #007AFF;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
}

.avatar-text {
	color: #ffffff;
	font-size: 24rpx;
	font-weight: 600;
}

.customer-info {
	flex: 1;
}

.customer-name {
	display: block;
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 4rpx;
}

.customer-phone {
	font-size: 24rpx;
	color: #666666;
}

.order-details {
	margin-bottom: 24rpx;
}

.detail-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8rpx;
}

.detail-row:last-child {
	margin-bottom: 0;
}

.detail-label {
	font-size: 26rpx;
	color: #666666;
}

.detail-value {
	font-size: 26rpx;
	color: #333333;
}

.detail-value.amount {
	font-weight: 600;
	color: #007AFF;
}

.detail-value.address {
	max-width: 400rpx;
	text-align: right;
	word-break: break-all;
}

.order-actions {
	display: flex;
	gap: 16rpx;
	margin-top: 24rpx;
	padding-top: 24rpx;
	border-top: 1rpx solid #f0f0f0;
}

.action-btn {
	flex: 1;
	padding: 16rpx 12rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	border: none;
	text-align: center;
}

.action-btn.primary {
	background: #007AFF;
	color: #ffffff;
}

.action-btn.secondary {
	background: #f8f9fa;
	color: #007AFF;
	border: 1rpx solid #007AFF;
}

.action-btn.danger {
	background: #f8f9fa;
	color: #dc3545;
	border: 1rpx solid #dc3545;
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 32rpx;
	opacity: 0.3;
}

.empty-title {
	display: block;
	font-size: 32rpx;
	color: #333333;
	margin-bottom: 16rpx;
}

.empty-desc {
	display: block;
	font-size: 28rpx;
	color: #999999;
	margin-bottom: 40rpx;
	line-height: 1.5;
}

.empty-action {
	background: #007AFF;
	color: #ffffff;
	border: none;
	border-radius: 8rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
}

/* 加载状态 */
.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx 0;
}

.loading-spinner {
	width: 32rpx;
	height: 32rpx;
	border: 3rpx solid #f0f0f0;
	border-top: 3rpx solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-right: 16rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 24rpx;
	color: #999999;
}

.load-complete {
	text-align: center;
	padding: 40rpx 0;
}

.complete-text {
	font-size: 24rpx;
	color: #999999;
}

/* 浮动操作按钮 */
.fab-container {
	position: fixed;
	bottom: 40rpx;
	right: 40rpx;
	z-index: 100;
}

.fab-btn {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	background: #007AFF;
	color: #ffffff;
	border: none;
	box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.fab-icon {
	font-size: 36rpx;
	font-weight: 300;
	margin-bottom: 4rpx;
}

.fab-text {
	font-size: 20rpx;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.date-modal, .sort-modal {
	background: #ffffff;
	border-radius: 16rpx;
	width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.modal-close {
	font-size: 32rpx;
	color: #999999;
	padding: 8rpx;
}

.date-options, .sort-options {
	padding: 16rpx 0;
}

.date-option, .sort-option {
	padding: 24rpx 32rpx;
	font-size: 28rpx;
	color: #333333;
	border-bottom: 1rpx solid #f8f9fa;
}

.date-option:last-child, .sort-option:last-child {
	border-bottom: none;
}

.sort-option.active {
	background: #f0f8ff;
	color: #007AFF;
}
</style> 