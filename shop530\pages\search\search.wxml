<!-- pages/search/search.wxml -->
<view class="search-page">
  <!-- 搜索框容器 -->
  <view class="search-container" style="padding-top: {{statusBarHeight > 20 ? statusBarHeight - 18 : 0}}px;">
    <view class="search-inner">
      <search-header 
        show="{{true}}"
        value="{{searchKeyword}}"
        placeholder="搜索商品"
        showAction="{{true}}"
        actionText="取消"
        autoFocus="{{true}}"
        searchIcon="search"
        bind:search="handleSearch"
        bind:change="handleSearchChange"
        bind:cancel="handleSearchCancel"
        bind:clear="handleSearchClear"
      />
    </view>
  </view>
  
  <!-- 搜索历史和热门搜索 -->
  <view class="search-content" wx:if="{{!showResults}}">
    <!-- 搜索历史 -->
    <view class="search-section" wx:if="{{historyVisible && searchHistory.length > 0}}">
      <view class="section-header">
        <text class="section-title">搜索历史</text>
        <view class="section-action" bindtap="clearHistory">
          <van-icon name="delete" size="32rpx" color="#999" />
        </view>
      </view>
      <view class="search-tags">
        <view 
          class="search-tag" 
          wx:for="{{searchHistory}}" 
          wx:key="*this"
          bindtap="useHistoryItem"
          data-keyword="{{item}}"
        >{{item}}</view>
      </view>
    </view>
    
    <!-- 热门搜索 -->
    <view class="search-section">
      <view class="section-header">
        <text class="section-title">热门搜索</text>
      </view>
      <view class="search-tags">
        <view 
          class="search-tag {{item.highlight ? 'highlight' : ''}}" 
          wx:for="{{hotKeywords}}" 
          wx:key="keyword"
          bindtap="useHotKeyword"
          data-keyword="{{item.keyword}}"
        >{{item.keyword}}</view>
      </view>
    </view>
    
    <!-- 搜索建议 -->
    <view class="search-suggestions" wx:if="{{suggestions.length > 0}}">
      <view 
        class="suggestion-item" 
        wx:for="{{suggestions}}" 
        wx:key="text"
        bindtap="useSuggestion"
        data-keyword="{{item.text}}"
      >
        <van-icon name="search" size="32rpx" color="#999" />
        <text class="suggestion-text">{{item.text}}</text>
      </view>
    </view>
  </view>
  
  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{showResults}}">
    <!-- 结果排序栏 -->
    <view class="sort-bar">
      <view class="sort-item {{currentSort === 'default' ? 'active' : ''}}" bindtap="changeSort" data-sort="default">
        <text>综合</text>
      </view>
      <view class="sort-item {{currentSort === 'sales' ? 'active' : ''}}" bindtap="changeSort" data-sort="sales">
        <text>销量</text>
      </view>
      <view class="sort-item {{currentSort === 'price' ? 'active' : ''}}" bindtap="changeSort" data-sort="price">
        <text>价格</text>
        <view class="price-sort">
          <view class="arrow up {{priceSortDirection === 'asc' && currentSort === 'price' ? 'active' : ''}}"></view>
          <view class="arrow down {{priceSortDirection === 'desc' && currentSort === 'price' ? 'active' : ''}}"></view>
        </view>
      </view>
      <view class="sort-item" bindtap="toggleListMode">
        <van-icon name="{{listMode === 'grid' ? 'wap-nav' : 'apps-o'}}" size="32rpx" />
      </view>
    </view>
    
    <!-- 加载中 -->
    <view class="loading-container" wx:if="{{loading}}">
      <van-loading size="24px" color="#4CAF50" />
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 错误状态 -->
    <view class="error-container" wx:elif="{{error}}">
      <van-icon name="warning-o" size="80rpx" color="#ff4d4f" />
      <text class="error-text">{{errorMsg || '加载失败'}}</text>
      <view class="error-action" bindtap="retrySearch">
        <text>重新加载</text>
      </view>
    </view>
    
    <!-- 无结果 -->
    <view class="empty-container" wx:elif="{{results.length === 0 && !loading}}">
      <van-icon name="search" size="80rpx" color="#cccccc" />
      <text class="empty-text">没有找到相关商品</text>
      <text class="empty-tips">换个关键词试试吧</text>
    </view>
    
    <!-- 商品列表 - 网格模式 -->
    <view class="product-grid" wx:elif="{{listMode === 'grid' && results.length > 0}}">
      <product-card 
        wx:for="{{results}}" 
        wx:key="id"
        product="{{item}}"
        layout-style="category"
        card-size="medium"
        show-add-cart="{{true}}"
        show-tags="{{true}}"
        bind:productTap="onProductTap"
        bind:addToCart="onAddToCart"
      />
    </view>
    
    <!-- 商品列表 - 列表模式 -->
    <view class="product-list" wx:elif="{{listMode === 'list' && results.length > 0}}">
      <product-card 
        wx:for="{{results}}" 
        wx:key="id"
        product="{{item}}"
        layout-style="list"
        card-size="medium"
        image-height="160rpx"
        show-add-cart="{{true}}"
        show-tags="{{true}}"
        bind:productTap="onProductTap"
        bind:addToCart="onAddToCart"
      />
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && results.length > 0 && !loading}}">
      <van-loading size="24px" color="#999999" />
      <text class="load-more-text">加载更多...</text>
    </view>
    
    <!-- 没有更多 -->
    <view class="no-more" wx:elif="{{!hasMore && results.length > 0}}">
      <text class="no-more-text">没有更多了</text>
    </view>
  </view>
  
  <!-- 回到顶部 -->
  <view class="back-to-top" bindtap="scrollToTop" wx:if="{{scrollTop > 500}}">
    <van-icon name="arrow-up" size="40rpx" color="#ffffff" />
  </view>
</view> 