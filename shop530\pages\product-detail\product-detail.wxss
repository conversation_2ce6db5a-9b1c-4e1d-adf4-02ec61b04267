/* 商品详情页样式 - 1:1还原设计图 */

.product-detail-page {
  height: 100vh;
  background: #f8f8f8;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* ========== 自定义导航栏 ========== */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #fff;
  z-index: 1000;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}

.navbar-left,
.navbar-right {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-left:active {
  background: #f5f5f5;
  border-radius: 50%;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

/* ========== 加载状态 ========== */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #fff;
}

/* ========== 主内容区域 ========== */
.main-content {
  background: #f8f8f8;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  /* 确保主内容区域有正确的宽度约束 */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* ========== 商品图片轮播 ========== */
.product-images {
  position: relative;
  background: #fff;
  flex-shrink: 0;
  width: 100%;
}

.images-swiper {
  width: 100%;
  height: 750rpx;
  display: block;
  position: relative;
}

.images-swiper swiper-item {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  display: block;
  background: #f5f5f5;
  position: relative;
  z-index: 1;
}

/* 图片加载失败占位 */
.image-error-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #ccc;
}

.error-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

/* 自定义指示器 */
.custom-indicators {
  position: absolute;
  bottom: 32rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12rpx;
  z-index: 10;
}

.indicator-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  cursor: pointer;
}

.indicator-dot.active {
  background: #fff;
  transform: scale(1.2);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* 图片数量显示 */
.image-counter {
  position: absolute;
  top: 32rpx;
  left: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(8rpx);
  z-index: 10;
}

/* 收藏按钮浮层 */
.favorite-btn-overlay {
  position: absolute;
  bottom: 32rpx;
  right: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  backdrop-filter: blur(8rpx);
  z-index: 10;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.favorite-btn-overlay:active {
  background: rgba(255, 255, 255, 0.8);
}

.favorite-text {
  font-size: 20rpx;
  color: #333;
  margin-top: 4rpx;
}

/* 分享按钮浮层 */
.share-btn-overlay {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  backdrop-filter: blur(8rpx);
  z-index: 10;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.share-btn-overlay:active {
  background: rgba(255, 255, 255, 0.8);
}

.share-text {
  font-size: 20rpx;
  color: #333;
  margin-top: 4rpx;
}

/* ========== 商品信息卡片 ========== */
.product-info-card {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

/* 商品标签 */
.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

/* 统一标签样式 */
.product-tag {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%) !important;
  color: #fff !important;
  border: none !important;
  font-size: 20rpx !important;
  padding: 4rpx 12rpx !important;
  height: 32rpx !important;
  line-height: 24rpx !important;
}

.product-tag.new-tag {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%) !important;
}

.product-tag.discount-tag {
  background: linear-gradient(135deg, #ff4444 0%, #e53935 100%) !important;
}

.product-tag.hot-tag {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%) !important;
}

/* 商品标题 */
.product-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

/* 商品副标题 */
.product-subtitle {
  font-size: 30rpx;
  color: #888;
  line-height: 1.4;
  margin-bottom: 8rpx;
  font-weight: 500;
}

/* 商品描述 */
.product-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

/* 价格信息 */
.product-price-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

/* 商品详情价格显示组件样式 */
.product-price-section .price-display.product-detail-price .price-symbol {
  font-size: 24rpx;
  color: #ff4444;
  font-weight: 600;
}

.product-price-section .price-display.product-detail-price .price-main {
  font-size: 48rpx;
  color: #ff4444;
  font-weight: 700;
}

.product-price-section .price-display.product-detail-price .price-unit {
  font-size: 24rpx;
  color: #ff4444;
  font-weight: 500;
}

.product-price-section .price-display.product-detail-price .login-prompt {
  font-size: 36rpx;
  color: #999;
  font-weight: 500;
}

.sales-stock-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 24rpx;
  color: #666;
}

.unit-text {
  color: #888;
  font-size: 22rpx;
}

/* ========== 商品规格信息 ========== */
.product-specs-section {
  background: #fff;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
}

.specs-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.spec-item {
  display: flex;
  font-size: 26rpx;
}

.spec-name {
  color: #666;
  min-width: 120rpx;
}

.spec-value {
  color: #333;
  flex: 1;
}

/* ========== 配送信息 ========== */
.delivery-section {
  background: #fff;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
}

.delivery-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.delivery-item:last-child {
  margin-bottom: 0;
}

.delivery-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

/* ========== 推荐商品 ========== */
.recommend-section {
  background: #fff;
  padding: 32rpx 0 24rpx 0;
  margin-bottom: 16rpx;
  min-height: 300rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 0 32rpx;
  margin-bottom: 24rpx;
}

.recommend-scroll {
  white-space: nowrap;
  height: 320rpx;
}

.recommend-list {
  display: inline-flex;
  gap: 16rpx;
  padding: 0 32rpx;
  height: 100%;
}

.recommend-item {
  position: relative;
  width: 200rpx;
  height: 280rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.recommend-image {
  width: 100%;
  height: 160rpx;
  object-fit: cover;
}

/* 推荐商品图片加载失败占位 */
.recommend-image-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #ccc;
}

.recommend-tags {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  display: flex;
  gap: 6rpx;
  z-index: 2;
}

.recommend-tag {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%) !important;
  color: #fff !important;
  border: none !important;
  font-size: 18rpx !important;
  padding: 2rpx 8rpx !important;
}

.recommend-tag.new-tag {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%) !important;
}

.recommend-tag.discount-tag {
  background: linear-gradient(135deg, #ff4444 0%, #e53935 100%) !important;
}

.recommend-info {
  padding: 12rpx 16rpx;
  padding-bottom: 50rpx;
  height: 120rpx;
  box-sizing: border-box;
}

.recommend-name {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
  line-height: 1.2;
}

/* 推荐商品价格显示组件样式 */
.recommend-info .price-display.recommend-price {
  align-self: flex-start;
}

.recommend-info .price-display.recommend-price .price-symbol {
  font-size: 16rpx;
  color: #ff4444;
  font-weight: 600;
}

.recommend-info .price-display.recommend-price .price-main {
  font-size: 24rpx;
  color: #ff4444;
  font-weight: 700;
}

.recommend-info .price-display.recommend-price .price-unit {
  font-size: 16rpx;
  color: #ff4444;
  font-weight: 500;
}

.recommend-info .price-display.recommend-price .login-prompt {
  font-size: 20rpx;
  color: #999;
  font-weight: 500;
}

.recommend-add-btn {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 44rpx;
  height: 44rpx;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
  z-index: 3;
}

.recommend-add-btn:active {
  transform: scale(0.9);
}

/* ========== 详情标签页 ========== */
.detail-tabs {
  background: #fff;
  margin-bottom: 16rpx;
}

.tab-headers {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-header {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  font-weight: 500;
}

.tab-header.active {
  color: #4CAF50;
  font-weight: 600;
}

.tab-header.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #4CAF50;
  border-radius: 2rpx;
}

.tab-content {
  padding: 32rpx;
  min-height: 200rpx;
  /* 确保内容区域有正确的宽度约束 */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 商品详情内容 */
.detail-content {
  min-height: 200rpx;
  padding: 0;
}

/* 商品详细信息 */
.product-detail-info {
  padding: 0;
  /* 确保整个详情区域支持响应式内容 */
  width: 100%;
  max-width: 100%;
  min-width: 0;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: visible;
}

.detail-section {
  margin-bottom: 32rpx;
  /* 确保每个详情区块支持响应式 */
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid #4CAF50;
  display: inline-block;
}

.detail-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  text-align: justify;
}

/* 富文本容器样式 - 统一简化 */
.detail-text {
  width: 100%;
  overflow-x: hidden;
  word-wrap: break-word;
  box-sizing: border-box;
}

/* 富文本内容样式 */
.rich-content {
  width: 100%;
  overflow-x: hidden;
  word-wrap: break-word;
}

/* 富文本中图片适配 */
.rich-content img,
.rich-content [alt] {
  width: 100% !important;
  height: auto !important;
  max-width: 100% !important;
  display: block !important;
  margin: 10rpx auto !important;
}

/* 由于微信小程序不支持深度选择器，rich-text组件的样式需要通过内联样式配置 */
/* 以下样式通过JavaScript动态添加到HTML内容中 */

/* 产品特点列表 */
.feature-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}

.feature-dot {
  width: 12rpx;
  height: 12rpx;
  background: #4CAF50;
  border-radius: 50%;
  margin-top: 10rpx;
  flex-shrink: 0;
}

.feature-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 占位符样式 */
.detail-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300rpx;
  opacity: 0.6;
}

.detail-placeholder-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 16rpx;
  font-weight: 500;
}

.detail-placeholder-desc {
  font-size: 24rpx;
  color: #ccc;
  margin-top: 8rpx;
}

/* 购买记录内容 */
.purchase-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200rpx;
}

/* ========== 底部操作栏 ========== */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  /* box-shadow: 0 -1rpx 4rpx rgba(0, 0, 0, 0.03); */
  z-index: 999;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* 底部导航 */
.bottom-nav {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.nav-item:active {
  opacity: 0.7;
}

.nav-text {
  font-size: 20rpx;
  color: #999;
  margin-top: 4rpx;
}

.cart-item {
  position: relative;
}

.cart-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4444;
  color: #fff;
  font-size: 18rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  box-sizing: border-box;
  font-weight: 600;
}

/* 操作按钮区域 */
.action-buttons {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* 数量选择器 */
.quantity-selector {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 40rpx;
  padding: 8rpx;
  border: 1rpx solid #e0e0e0;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.quantity-btn:active {
  transform: scale(0.95);
  background: #f5f5f5;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  background: transparent;
  border: none;
}

/* 加购物车按钮 */
.add-cart-btn {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  color: #fff;
  font-weight: 600;
  box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.add-cart-btn:active {
  transform: scale(0.98);
  box-shadow: 0 3rpx 10rpx rgba(76, 175, 80, 0.4);
}

.add-cart-text {
  font-size: 30rpx;
  color: #fff;
}

/* ========== 响应式设计 ========== */
@media screen and (max-width: 375px) {
  .product-title {
    font-size: 32rpx;
  }
  
  .price-display.product-detail-price .price-main {
    font-size: 44rpx;
  }
  
  .images-swiper {
    height: 650rpx;
  }
  
  .product-info-card {
    padding: 24rpx;
  }
}

@media screen and (min-width: 414px) {
  .images-swiper {
    height: 800rpx;
  }
}

/* ========== 深色模式适配 ========== */
@media (prefers-color-scheme: dark) {
  .product-detail-page {
    background: #1a1a1a;
  }
  
  .custom-navbar,
  .navbar-content {
    background: #2c2c2c;
    border-bottom: 1rpx solid #444;
  }
  
  .navbar-title {
    color: #fff;
  }
  
  .navbar-left:active,
  .navbar-right:active {
    background: #444;
  }
  
  .loading-container {
    background: #2c2c2c;
  }
  
  .main-content {
    background: #1a1a1a;
  }
  
  .product-info-card {
    background: #2c2c2c;
    border: 1rpx solid #444;
  }
  
  .product-title {
    color: #fff;
  }
  
  .product-description {
    color: #aaa;
  }
  
  .favorite-btn-overlay:active {
    background: rgba(0, 0, 0, 0.7);
  }
  
  .favorite-btn-overlay .favorite-text {
    color: #fff;
  }
  
  .sales-stock-info {
    color: #aaa;
  }
  
  .delivery-section {
    background: #2c2c2c;
    border: 1rpx solid #444;
  }
  
  .delivery-text {
    color: #ccc;
  }
  
  .bottom-actions {
    background: #2c2c2c;
    border-top: 1rpx solid #444;
  }
}

/* 加载错误容器 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  min-height: 600rpx;
}

.error-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
}

.error-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 60rpx;
}

.error-actions {
  display: flex;
  flex-direction: row;
  gap: 30rpx;
}

.btn-retry, .btn-back {
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
}

.btn-retry {
  background-color: #ff6b6b;
  color: #fff;
}

.btn-back {
  background-color: #f5f5f5;
  color: #333;
}

/* 骨架屏样式 */
.loading-skeleton {
  padding: 30rpx;
}

.skeleton-image {
  width: 100%;
  height: 750rpx;
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.skeleton-title {
  width: 80%;
  height: 40rpx;
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 6rpx;
  margin-bottom: 20rpx;
}

.skeleton-price {
  width: 40%;
  height: 60rpx;
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 6rpx;
  margin-bottom: 30rpx;
}

.skeleton-desc {
  width: 100%;
  height: 24rpx;
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 6rpx;
  margin-bottom: 16rpx;
}

.skeleton-tabs {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 6rpx;
  margin-bottom: 30rpx;
  margin-top: 30rpx;
}

.skeleton-content {
  width: 100%;
  height: 400rpx;
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 6rpx;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
} 