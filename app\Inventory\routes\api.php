<?php

use App\Inventory\Http\Controllers\InventoryTransactionController;
use App\Inventory\Http\Controllers\InventoryTransferController;
use App\Inventory\Http\Controllers\InventoryBatchController;
use App\Inventory\Http\Controllers\InventoryFixController;
use App\Warehouse\Http\Controllers\WarehouseController;
use App\Warehouse\Http\Controllers\InventoryController;
use Illuminate\Support\Facades\Route;


// 将所有库存相关API统一放在api前缀下
Route::prefix('api')->middleware(['auth:sanctum'])->group(function () {
    // 库存事务
    Route::prefix('inventory/transactions')->group(function () {
        Route::get('/', [InventoryTransactionController::class, 'index']);
        Route::post('/', [InventoryTransactionController::class, 'store']);
        Route::get('/{id}', [InventoryTransactionController::class, 'show']);
        Route::post('/{id}/complete', [InventoryTransactionController::class, 'complete']);
        Route::post('/{id}/cancel', [InventoryTransactionController::class, 'cancel']);
    });
    
    // 库存调整
    Route::post('/inventory/adjust', [InventoryTransactionController::class, 'adjust']);
    
    // 库存查询
    Route::get('/inventory/stock', [InventoryTransactionController::class, 'getStockList']);
    Route::get('/inventory/stats', [InventoryTransactionController::class, 'getStats']);
    Route::get('/inventory/products/{productId}/warehouses/{warehouseId}', [InventoryTransactionController::class, 'getProductInventory']);
    Route::get('/inventory/products/{productId}/inventories', [InventoryTransactionController::class, 'getProductInventories']);
    
    // 库存操作
    Route::post('/inventory/stock/add', [InventoryTransactionController::class, 'addStock']);
    Route::post('/inventory/stock/reduce', [InventoryTransactionController::class, 'reduceStock']);
    
    // 库存调拨
    Route::post('/inventory/transfer', [InventoryTransferController::class, 'transfer']);
    Route::get('/inventory/transfers', [InventoryTransferController::class, 'getTransferHistory']);
    
    // 商品仓库分配
    Route::post('/inventory/stock/warehouse', [InventoryController::class, 'assignProductWarehouse']);
    
    // 批次管理
    Route::prefix('inventory/batches')->group(function () {
        Route::get('/', [InventoryBatchController::class, 'index']);
        Route::post('/', [InventoryBatchController::class, 'store']);
        Route::get('/near-expiry', [InventoryBatchController::class, 'nearExpiry']);
        Route::get('/expired', [InventoryBatchController::class, 'expired']);
        Route::get('/average-cost', [InventoryBatchController::class, 'getAverageCost']);
        Route::post('/from-purchase', [InventoryBatchController::class, 'createFromPurchase']);
        Route::get('/{id}', [InventoryBatchController::class, 'show']);
        Route::put('/{id}', [InventoryBatchController::class, 'update']);
        Route::delete('/{id}', [InventoryBatchController::class, 'destroy']);
        Route::post('/{id}/reduce', [InventoryBatchController::class, 'reduceStock']);
    });
    
    // 库存系统管理
    Route::prefix('inventory/system')->middleware(['employee.role:admin'])->group(function () {
        Route::get('/overview', [InventoryFixController::class, 'getSystemOverview']);
        Route::post('/repair-units', [InventoryFixController::class, 'checkAndRepairUnits']);
        Route::post('/convert-units', [InventoryFixController::class, 'runConversionCommand']);
        Route::get('/fix', [InventoryFixController::class, 'fixApi'])->name('api.inventory.system.fix');
    });
    
    // 仓库管理API
    Route::prefix('inventory/warehouses')->group(function () {
        Route::get('/', [WarehouseController::class, 'index']);
        Route::post('/', [WarehouseController::class, 'store']);
        Route::get('/stats', [WarehouseController::class, 'stats']);
        Route::post('/batch', [WarehouseController::class, 'batchOperation']);
        Route::get('/{id}', [WarehouseController::class, 'show']);
        Route::put('/{id}', [WarehouseController::class, 'update']);
        Route::delete('/{id}', [WarehouseController::class, 'destroy']);
        Route::get('/{id}/inventory', [WarehouseController::class, 'inventory']);
    });
}); 