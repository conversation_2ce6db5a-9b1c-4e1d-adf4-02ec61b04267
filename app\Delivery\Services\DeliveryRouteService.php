<?php

namespace App\Delivery\Services;

use App\Delivery\Models\DeliveryRoute;
use App\Employee\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class DeliveryRouteService
{
    /**
     * 获取配送路线列表
     *
     * @param Request $request
     * @return \Illuminate\Pagination\LengthAwarePaginator|\Illuminate\Database\Eloquent\Collection
     */
    public function getRoutes(Request $request)
    {
        $query = DeliveryRoute::with(['deliverers', 'deliverers.user']);
        
        // 筛选条件
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        if ($request->has('name') && $request->name !== '') {
            $query->where('name', 'like', '%' . $request->name . '%');
        }
        
        // 分页获取数据
        if ($request->has('per_page')) {
            return $query->paginate($request->per_page);
        }
        
        return $query->get();
    }
    
    /**
     * 获取配送路线详情
     *
     * @param int $id
     * @return DeliveryRoute
     */
    public function getRoute($id)
    {
        return DeliveryRoute::with(['deliverers', 'deliverers.user'])->findOrFail($id);
    }
    
    /**
     * 创建配送路线
     *
     * @param array $data
     * @return DeliveryRoute
     */
    public function createRoute(array $data)
    {
        $route = new DeliveryRoute();
        $route->name = $data['name'];
        $route->coverage_area = $data['coverage_area'];
        $route->starting_point = $data['starting_point'];
        $route->estimated_delivery_time = $data['estimated_delivery_time'];
        $route->max_orders = $data['max_orders'];
        $route->description = $data['description'] ?? null;
        $route->status = $data['status'];
        $route->save();
        
        return $route;
    }
    
    /**
     * 更新配送路线
     *
     * @param int $id
     * @param array $data
     * @return DeliveryRoute
     */
    public function updateRoute($id, array $data)
    {
        $route = DeliveryRoute::findOrFail($id);
        
        if (isset($data['name'])) $route->name = $data['name'];
        if (isset($data['coverage_area'])) $route->coverage_area = $data['coverage_area'];
        if (isset($data['starting_point'])) $route->starting_point = $data['starting_point'];
        if (isset($data['estimated_delivery_time'])) $route->estimated_delivery_time = $data['estimated_delivery_time'];
        if (isset($data['max_orders'])) $route->max_orders = $data['max_orders'];
        if (isset($data['description'])) $route->description = $data['description'];
        if (isset($data['status'])) $route->status = $data['status'];
        
        $route->save();
        
        return $route;
    }
    
    /**
     * 删除配送路线
     *
     * @param int $id
     * @return bool
     * @throws \Exception
     */
    public function deleteRoute($id)
    {
        $route = DeliveryRoute::findOrFail($id);
        
        // 检查路线是否正在使用中（拥有进行中的订单）
        $activeDeliveries = $route->deliveries()->whereIn('status', ['pending', 'in_progress'])->count();
        if ($activeDeliveries > 0) {
            throw new \Exception('该路线有正在进行中的配送任务，无法删除', 400);
        }
        
        // 先解除与配送员的关联
        DB::beginTransaction();
        
        try {
            $route->deliverers()->detach();
            $route->delete();
            
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Delete delivery route error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 分配配送员到路线
     *
     * @param int $id
     * @param array $delivererIds
     * @return DeliveryRoute
     */
    public function assignDeliverers($id, array $delivererIds)
    {
        $route = DeliveryRoute::findOrFail($id);
        
        DB::beginTransaction();
        
        try {
            // 清除现有关联
            $route->deliverers()->detach();
            
            // 添加新关联
            if (!empty($delivererIds)) {
                $route->deliverers()->attach($delivererIds);
            }
            
            DB::commit();
            return $route->load('deliverers');
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Assign deliverers to route error: ' . $e->getMessage());
            throw $e;
        }
    }
} 