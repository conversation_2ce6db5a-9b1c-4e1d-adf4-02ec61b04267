<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('warehouses', function (Blueprint $table) {
            // 基础信息字段
            $table->string('name')->nullable()->after('id')->comment('仓库名称');
            $table->string('code')->nullable()->unique()->after('name')->comment('仓库编码');
            
            // 管理信息
            $table->string('manager')->nullable()->after('location')->comment('负责人');
            $table->string('contact')->nullable()->after('manager')->comment('联系方式');
            
            // 容量信息
            $table->decimal('capacity', 12, 2)->default(0)->after('contact')->comment('仓库总容量');
            $table->decimal('used_capacity', 12, 2)->default(0)->after('capacity')->comment('已使用容量');
            
            // 状态信息
            $table->enum('status', ['active', 'warning', 'inactive'])->default('active')->after('used_capacity')->comment('仓库状态');
            
            // 描述信息
            $table->text('description')->nullable()->after('status')->comment('仓库描述');
            
            // 索引
            $table->index('code');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('warehouses', function (Blueprint $table) {
            $table->dropIndex(['code']);
            $table->dropIndex(['status']);
            
            $table->dropColumn([
                'name',
                'code',
                'manager',
                'contact',
                'capacity',
                'used_capacity',
                'status',
                'description'
            ]);
        });
    }
};
