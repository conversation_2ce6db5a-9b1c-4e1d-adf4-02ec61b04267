<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Inventory\Models\InventoryBatch;
use App\Inventory\Models\Inventory;
use App\Product\Models\Product;
use Illuminate\Support\Facades\Log;

class ConvertInventoryBatchesToBaseUnits extends Migration
{
    /**
     * 运行迁移
     *
     * @return void
     */
    public function up()
    {
        // 记录开始迁移
        Log::info('开始进行库存批次单位转换迁移');
        
        // 获取所有库存批次记录
        $batches = InventoryBatch::with(['inventory', 'inventory.product'])->get();
        $totalCount = $batches->count();
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($batches as $batch) {
            try {
                $inventory = $batch->inventory;
                
                // 如果库存不存在，记录错误并跳过
                if (!$inventory) {
                    Log::error('批次转换失败：找不到库存记录', [
                        'batch_id' => $batch->id,
                        'inventory_id' => $batch->inventory_id
                    ]);
                    $errorCount++;
                    continue;
                }
                
                $product = $inventory->product;
                
                // 如果产品不存在，记录错误并跳过
                if (!$product) {
                    Log::error('批次转换失败：找不到产品', [
                        'batch_id' => $batch->id,
                        'inventory_id' => $batch->inventory_id,
                        'product_id' => $inventory->product_id
                    ]);
                    $errorCount++;
                    continue;
                }
                
                // 获取产品的基本单位ID
                $baseUnitId = $product->base_unit_id;
                
                // 如果批次已经是基本单位，不需要转换
                if ($batch->unit_id === $baseUnitId) {
                    $successCount++;
                    continue;
                }
                
                // 获取从当前单位到基本单位的转换率
                $conversionRate = $product->getUnitConversionRate(
                    $batch->unit_id, 
                    $baseUnitId
                );
                
                // 如果无法获取转换率，记录错误并跳过
                if ($conversionRate === null) {
                    Log::error('批次转换失败：无法获取转换率', [
                        'batch_id' => $batch->id,
                        'from_unit_id' => $batch->unit_id,
                        'to_unit_id' => $baseUnitId
                    ]);
                    $errorCount++;
                    continue;
                }
                
                // 保存原始值用于日志
                $oldUnitId = $batch->unit_id;
                $oldQuantity = $batch->quantity;
                $oldInitialQuantity = $batch->initial_quantity;
                
                // 计算基本单位下的数量
                $quantityInBaseUnit = $batch->quantity * $conversionRate;
                $initialQuantityInBaseUnit = $batch->initial_quantity * $conversionRate;
                
                // 更新批次单位和数量
                $batch->quantity = $quantityInBaseUnit;
                $batch->initial_quantity = $initialQuantityInBaseUnit;
                $batch->unit_id = $baseUnitId;
                $batch->save();
                
                Log::info('批次单位转换成功', [
                    'batch_id' => $batch->id,
                    'from_unit_id' => $oldUnitId,
                    'to_unit_id' => $baseUnitId,
                    'old_quantity' => $oldQuantity,
                    'new_quantity' => $quantityInBaseUnit,
                    'old_initial_quantity' => $oldInitialQuantity,
                    'new_initial_quantity' => $initialQuantityInBaseUnit
                ]);
                
                $successCount++;
            } catch (\Exception $e) {
                Log::error('批次转换异常', [
                    'batch_id' => $batch->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                $errorCount++;
            }
        }
        
        // 记录迁移完成结果
        Log::info('库存批次单位转换迁移完成', [
            'total' => $totalCount,
            'success' => $successCount,
            'error' => $errorCount
        ]);
    }

    /**
     * 回滚迁移
     *
     * @return void
     */
    public function down()
    {
        // 不提供回滚功能，因为无法准确还原原始单位
        Log::warning('无法回滚库存批次单位转换');
    }
} 