# 货到付款订单合并规则

## 🚚 货到付款订单的特殊性

货到付款订单的合并主要目的是**方便配送**，而不是节省金额。因此在自动合并规则上有特殊处理。

## 📋 自动合并条件

### ✅ **货到付款订单**
```php
// 特殊规则：无金额和节省限制
if ($paymentMethod === 'cod') {
    // 1. 必须同一用户
    // 2. 必须同一配送区域 ✓ 重要！
    // 3. 订单数量 ≤ 5个
    // 4. 无定制商品
    // ❌ 无最大金额限制
    // ❌ 无最小节省金额限制
}
```

### 🔄 **其他支付方式**
```php
// 标准规则：有金额和节省限制
if ($paymentMethod !== 'cod') {
    // 1. 必须同一用户
    // 2. 总金额 ≤ ¥1,000
    // 3. 订单数量 ≤ 5个
    // 4. 节省金额 ≥ ¥5
    // 5. 无定制商品
}
```

## 🎯 为什么货到付款订单不限制金额？

### 1. **配送效率优先**
- 主要目的是减少配送员跑腿次数
- 即使大额订单也值得合并配送
- 提高配送路线效率

### 2. **客户体验**
- 减少客户多次收货的麻烦
- 统一收货时间更方便
- 避免分批付款的复杂性

### 3. **业务成本**
- 配送成本远大于潜在的优惠差异
- 合并配送节省的人力成本显著
- 减少配送错误和遗漏

## 📊 实际应用场景

### ✅ **自动合并示例**

#### 场景1：多个大额货到付款订单
```
用户：张三（某餐厅老板）
配送区域：CBD区域A

订单1：蔬菜采购，¥2,500，货到付款
订单2：肉类采购，¥1,800，货到付款  
订单3：调料采购，¥600，货到付款
总计：¥4,900

✅ 自动合并执行
原因：同一区域配送，节省3次配送成本
```

#### 场景2：小额货到付款订单
```
用户：李四
配送区域：住宅区B

订单1：水果，¥50，货到付款
订单2：零食，¥30，货到付款
订单3：饮料，¥25，货到付款
总计：¥105

✅ 自动合并执行
原因：即使金额小，合并配送仍有价值
```

### ❌ **不能自动合并的情况**

#### 场景1：不同配送区域
```
用户：王五（有两个地址）
订单1：¥200，货到付款，配送到家里
订单2：¥300，货到付款，配送到公司

❌ 不能合并
原因：不同配送区域，需要不同配送员
```

#### 场景2：订单数量过多
```
用户：赵六（便利店老板）
当日有8个货到付款订单

❌ 不能自动合并
原因：超过5个订单，需要人工确认
```

## 🔧 配置参数

```php
// 货到付款订单自动合并配置
'cod_auto_merge' => [
    'enabled' => true,
    'max_order_count' => 5,          // 最大订单数量
    'require_same_region' => true,   // 必须同一配送区域
    'allow_customized' => false,     // 不允许定制商品
    'max_total_amount' => null,      // 无金额限制
    'min_savings' => null,           // 无节省金额要求
],

// 其他支付方式配置
'other_payment_auto_merge' => [
    'enabled' => true,
    'max_order_count' => 5,          // 最大订单数量
    'max_total_amount' => 1000,      // 最大总金额¥1000
    'min_savings' => 5,              // 最小节省¥5
    'allow_customized' => false,     // 不允许定制商品
]
```

## 📈 业务价值

### 💰 **成本节省**
- **配送成本**：每次配送节省¥15-30
- **人力成本**：减少配送员工作量
- **时间成本**：提高配送效率

### 😊 **客户满意度**
- **便利性**：一次性收货更方便
- **时间节省**：减少等待收货次数
- **体验优化**：统一处理更专业

### 📊 **运营效率**
- **路线优化**：配送路线更合理
- **错误减少**：减少配送遗漏
- **管理简化**：订单管理更集中

## ⚠️ 注意事项

1. **区域限制**：货到付款订单必须同一配送区域
2. **沟通确认**：大额合并后需要电话确认客户
3. **库存检查**：合并前确保所有商品库存充足
4. **配送安排**：合并后及时安排配送员
5. **收款准备**：提醒配送员准备充足找零 