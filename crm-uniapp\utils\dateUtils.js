/**
 * 日期处理工具函数
 */

/**
 * 将period转换为日期范围
 * @param {string} period - 时间周期 (last_7_days, last_30_days, last_90_days)
 * @returns {object} - 包含start_date和end_date的对象
 */
export function getPeriodDateRange(period) {
	const today = new Date()
	let startDate, endDate
	
	switch (period) {
		case 'last_7_days':
			startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
			break
		case 'last_30_days':
			startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
			break
		case 'last_90_days':
			startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000)
			break
		default:
			startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
	}
	
	endDate = today
	
	return {
		start_date: formatDate(startDate),
		end_date: formatDate(endDate)
	}
}

/**
 * 格式化日期为YYYY-MM-DD格式
 * @param {Date} date - 日期对象
 * @returns {string} - 格式化后的日期字符串
 */
export function formatDate(date) {
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	const day = String(date.getDate()).padStart(2, '0')
	return `${year}-${month}-${day}`
}

/**
 * 格式化日期时间为YYYY-MM-DD HH:mm:ss格式
 * @param {Date} date - 日期对象
 * @returns {string} - 格式化后的日期时间字符串
 */
export function formatDateTime(date) {
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	const day = String(date.getDate()).padStart(2, '0')
	const hours = String(date.getHours()).padStart(2, '0')
	const minutes = String(date.getMinutes()).padStart(2, '0')
	const seconds = String(date.getSeconds()).padStart(2, '0')
	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/**
 * 计算两个日期之间的天数差
 * @param {string|Date} startDate - 开始日期
 * @param {string|Date} endDate - 结束日期
 * @returns {number} - 天数差
 */
export function getDaysDiff(startDate, endDate) {
	const start = typeof startDate === 'string' ? new Date(startDate) : startDate
	const end = typeof endDate === 'string' ? new Date(endDate) : endDate
	const diffTime = Math.abs(end - start)
	return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

/**
 * 获取相对时间描述
 * @param {string|Date} date - 日期
 * @returns {string} - 相对时间描述
 */
export function getRelativeTime(date) {
	const targetDate = typeof date === 'string' ? new Date(date) : date
	const now = new Date()
	const diffMs = now - targetDate
	const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
	
	if (diffDays === 0) {
		return '今天'
	} else if (diffDays === 1) {
		return '昨天'
	} else if (diffDays < 7) {
		return `${diffDays}天前`
	} else if (diffDays < 30) {
		const weeks = Math.floor(diffDays / 7)
		return `${weeks}周前`
	} else if (diffDays < 365) {
		const months = Math.floor(diffDays / 30)
		return `${months}个月前`
	} else {
		const years = Math.floor(diffDays / 365)
		return `${years}年前`
	}
} 