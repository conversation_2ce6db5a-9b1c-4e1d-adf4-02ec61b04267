// 自定义数量控制组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前数量值
    value: {
      type: Number,
      value: 1
    },
    // 最小值
    min: {
      type: Number,
      value: 1
    },
    // 最大值
    max: {
      type: Number,
      value: 999
    },
    // 步长
    step: {
      type: Number,
      value: 1
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否只允许整数
    integer: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    inputValue: 1,
    lastUserAction: 0, // 记录最后一次用户操作的时间戳
    pendingValue: null, // 记录用户操作产生的待确认值
    focus: false // 添加focus状态变量
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('🎯 quantity-stepper组件attached:', {
        value: this.properties.value,
        inputValue: this.data.inputValue
      });
      
      // 初始化输入框值
      this.setData({
        inputValue: this.properties.value
      }, () => {
        console.log('🎯 初始化完成，inputValue:', this.data.inputValue);
      });
    },
    
    ready() {
      console.log('🎯 quantity-stepper组件ready:', {
        value: this.properties.value,
        inputValue: this.data.inputValue
      });
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'value': function(newValue) {
      const now = Date.now();
      const timeSinceLastAction = now - this.data.lastUserAction;
      const hasPendingValue = this.data.pendingValue !== null;
      
      console.log('🎯 value属性变化:', {
        newValue,
        currentInputValue: this.data.inputValue,
        timeSinceLastAction,
        hasPendingValue,
        pendingValue: this.data.pendingValue,
        willUpdate: newValue !== this.data.inputValue
      });
      
      // 防回滚逻辑：如果用户刚刚操作过(2秒内)且有待确认值，优先使用待确认值
      if (timeSinceLastAction < 2000 && hasPendingValue) {
        console.log('🛡️ 防回滚：用户刚操作过，忽略外部value变化，保持待确认值:', this.data.pendingValue);
        
        // 如果外部传来的值和待确认值一致，说明API确认了，清除待确认状态
        if (newValue === this.data.pendingValue) {
          console.log('✅ API确认了用户操作，清除待确认状态');
          this.setData({
            pendingValue: null
          });
        }
        return;
      }
      
      // 正常情况：同步外部value到inputValue
      this.setData({
        inputValue: newValue,
        pendingValue: null // 清除任何待确认状态
      }, () => {
        console.log('🎯 inputValue已同步更新为:', this.data.inputValue);
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 减少数量
     */
    onMinus() {
      console.log('🎯🎯🎯 onMinus被调用了！！！');
      console.log('🎯 点击减少按钮:', {
        disabled: this.properties.disabled,
        currentValue: this.data.inputValue,
        min: this.properties.min,
        step: this.properties.step
      });
      
      if (this.properties.disabled) {
        console.log('❌ 组件被禁用，退出');
        return;
      }
      
      const currentValue = this.data.inputValue;
      const newValue = Math.max(currentValue - this.properties.step, this.properties.min);
      
      console.log('🔽 计算新值:', { currentValue, newValue, willChange: newValue !== currentValue });
      
      if (newValue !== currentValue) {
        console.log('🔽 减少数量:', currentValue, '->', newValue);
        this.updateValueWithUserAction(newValue);
      } else {
        console.log('🔽 数量已达最小值，不变化');
      }
    },

    /**
     * 增加数量
     */
    onPlus() {
      console.log('🎯🎯🎯 onPlus被调用了！！！');
      console.log('🎯 点击增加按钮:', {
        disabled: this.properties.disabled,
        currentValue: this.data.inputValue,
        max: this.properties.max,
        step: this.properties.step
      });
      
      if (this.properties.disabled) {
        console.log('❌ 组件被禁用，退出');
        return;
      }
      
      const currentValue = this.data.inputValue;
      const newValue = Math.min(currentValue + this.properties.step, this.properties.max);
      
      console.log('🔼 计算新值:', { currentValue, newValue, willChange: newValue !== currentValue });
      
      if (newValue !== currentValue) {
        console.log('🔼 增加数量:', currentValue, '->', newValue);
        this.updateValueWithUserAction(newValue);
      } else {
        console.log('🔼 数量已达最大值，不变化');
      }
    },

    /**
     * 输入框输入事件
     */
    onInput(e) {
      const inputValue = e.detail.value;
      console.log('📝 输入数量:', inputValue, typeof inputValue);
      
      // 立即更新显示值（乐观更新）
      this.setData({
        inputValue: inputValue
      }, () => {
        console.log('📝 输入值已更新，当前inputValue:', this.data.inputValue);
      });
    },

    /**
     * 输入框获得焦点事件
     */
    onFocus(e) {
      console.log('📝 输入框获得焦点');
      
      this.setData({
        focus: true
      });
    },

    /**
     * 输入框失焦事件
     */
    onBlur(e) {
      const inputValue = e.detail.value;
      let newValue = this.properties.integer ? parseInt(inputValue) : parseFloat(inputValue);
      
      console.log('📝 输入框失焦，处理输入值:', { 
        inputValue, 
        newValue,
        isNaN: isNaN(newValue)
      });
      
      // 重置焦点状态
      this.setData({
        focus: false
      });
      
      // 处理NaN情况，设为最小值
      if (isNaN(newValue)) {
        newValue = this.properties.min;
        console.log('📝 输入内容非数字，重置为最小值:', newValue);
      }
      
      // 数值验证
      if (newValue < this.properties.min) {
        newValue = this.properties.min;
      } else if (newValue > this.properties.max) {
        newValue = this.properties.max;
      }
      
      console.log('📤 输入完成:', inputValue, '->', newValue);
      this.updateValueWithUserAction(newValue);
    },

    /**
     * 减少按钮触摸开始
     */
    onMinusTouchStart(e) {
      console.log('👆 减少按钮触摸开始');
    },

    /**
     * 减少按钮触摸结束
     */
    onMinusTouchEnd(e) {
      console.log('👆 减少按钮触摸结束');
    },

    /**
     * 增加按钮触摸开始
     */
    onPlusTouchStart(e) {
      console.log('👆 增加按钮触摸开始');
    },

    /**
     * 增加按钮触摸结束
     */
    onPlusTouchEnd(e) {
      console.log('👆 增加按钮触摸结束');
    },

    /**
     * 用户操作触发的数量更新（带防回滚机制）
     */
    updateValueWithUserAction(newValue) {
      const now = Date.now();
      
      console.log('🎯 用户操作更新值:', {
        newValue,
        timestamp: now
      });
      
      // 记录用户操作时间和待确认值
      this.setData({
        lastUserAction: now,
        pendingValue: newValue
      });
      
      // 调用原有的更新逻辑
      this.updateValue(newValue);
    },

    /**
     * 更新数量值
     */
    updateValue(newValue) {
      console.log('🎯 updateValue调用:', {
        newValue,
        type: typeof newValue,
        integer: this.properties.integer,
        min: this.properties.min,
        max: this.properties.max,
        currentInputValue: this.data.inputValue,
        currentValue: this.properties.value
      });
      
      // 确保是数字
      const value = this.properties.integer ? parseInt(newValue) : parseFloat(newValue);
      
      // 边界检查
      const finalValue = Math.max(this.properties.min, Math.min(this.properties.max, value));
      
      console.log('🎯 计算最终值:', {
        原始值: newValue,
        转换后: value,
        最终值: finalValue
      });
      
      // 更新组件内部状态
      this.setData({
        inputValue: finalValue
      }, () => {
        console.log('🎯 setData完成，新的inputValue:', this.data.inputValue);
      });
      
      // 触发change事件，通知父组件
      const eventData = {
        value: finalValue,
        oldValue: this.properties.value
      };
      
      console.log('🎯 触发change事件:', eventData);
      this.triggerEvent('change', eventData);
      
      console.log('✅ 数量更新完成:', finalValue);
    }
  }
}); 