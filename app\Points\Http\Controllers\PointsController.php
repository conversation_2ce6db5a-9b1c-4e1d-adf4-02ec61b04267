<?php

namespace App\Points\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Points\Services\PointsService;
use App\Points\Models\PointsRule;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PointsController extends Controller
{
    protected PointsService $pointsService;

    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * 获取用户积分余额
     */
    public function balance(Request $request): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'balance' => $user->member_points,
                'user_id' => $user->id,
                'membership_level' => $user->membershipLevel?->name ?? '普通会员',
            ]
        ]);
    }

    /**
     * 获取用户积分流水记录
     */
    public function transactions(Request $request): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        $request->validate([
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100',
            'type' => 'sometimes|string|in:earn,spend,refund,expire,admin',
            'source' => 'sometimes|string',
        ]);

        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 20);

        $result = $this->pointsService->getUserPointsTransactions($user->id, $page, $perPage);

        return response()->json([
            'success' => true,
            'data' => $result['data'],
            'pagination' => [
                'current_page' => $result['current_page'],
                'last_page' => $result['last_page'],
                'per_page' => $result['per_page'],
                'total' => $result['total'],
            ]
        ]);
    }

    /**
     * 获取用户积分统计
     */
    public function stats(Request $request): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        $stats = $this->pointsService->getUserPointsStats($user->id);

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * 获取积分排行榜
     */
    public function ranking(Request $request): JsonResponse
    {
        $limit = min($request->get('limit', 10), 50);
        $ranking = $this->pointsService->getPointsRanking($limit);

        return response()->json([
            'success' => true,
            'data' => $ranking
        ]);
    }

    /**
     * 每日签到获取积分
     */
    public function signin(Request $request): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        // 检查今日是否已签到
        $todaySignin = \App\Points\Models\PointsTransaction::where('user_id', $user->id)
            ->where('source', 'signin')
            ->whereDate('created_at', today())
            ->exists();

        if ($todaySignin) {
            return response()->json([
                'success' => false,
                'message' => '今日已签到'
            ], 400);
        }

        // 根据签到规则给予积分
        $awarded = $this->pointsService->awardPointsByRule(
            $user->id,
            PointsRule::RULE_TYPE_SIGNIN,
            ['user_id' => $user->id]
        );

        if ($awarded) {
            return response()->json([
                'success' => true,
                'message' => '签到成功，获得积分奖励'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => '签到失败，暂无签到奖励规则'
            ], 400);
        }
    }

    /**
     * 获取积分获取规则说明
     */
    public function rules(Request $request): JsonResponse
    {
        $rules = PointsRule::valid()
            ->select('name', 'rule_type', 'points_amount', 'description', 'max_times_per_day', 'max_times_total')
            ->orderBy('rule_type')
            ->get()
            ->map(function ($rule) {
                return [
                    'name' => $rule->name,
                    'type' => $rule->rule_type_text,
                    'points' => $rule->points_amount,
                    'description' => $rule->description,
                    'daily_limit' => $rule->max_times_per_day,
                    'total_limit' => $rule->max_times_total,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $rules
        ]);
    }

    /**
     * 检查用户今日签到状态
     */
    public function signinStatus(Request $request): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        $todaySignin = \App\Points\Models\PointsTransaction::where('user_id', $user->id)
            ->where('source', 'signin')
            ->whereDate('created_at', today())
            ->exists();

        // 获取签到规则
        $signinRule = PointsRule::valid()
            ->byType(PointsRule::RULE_TYPE_SIGNIN)
            ->first();

        return response()->json([
            'success' => true,
            'data' => [
                'signed_today' => $todaySignin,
                'can_signin' => !$todaySignin && $signinRule !== null,
                'signin_points' => $signinRule?->points_amount ?? 0,
            ]
        ]);
    }

    /**
     * 获取用户积分获得历史统计
     */
    public function earnHistory(Request $request): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        // 按来源统计积分获得
        $sourceStats = \App\Points\Models\PointsTransaction::where('user_id', $user->id)
            ->where('points', '>', 0)
            ->selectRaw('source, SUM(points) as total_points, COUNT(*) as count')
            ->groupBy('source')
            ->get()
            ->map(function ($item) {
                return [
                    'source' => $item->source,
                    'source_text' => match($item->source) {
                        'order' => '订单完成',
                        'signin' => '每日签到',
                        'invite' => '邀请好友',
                        'review' => '商品评价',
                        'share' => '分享商品',
                        'birthday' => '生日奖励',
                        'upgrade' => '会员升级',
                        'admin' => '管理员操作',
                        default => '其他'
                    },
                    'total_points' => $item->total_points,
                    'count' => $item->count,
                ];
            });

        // 最近7天积分获得趋势
        $recentDays = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->toDateString();
            $dayPoints = \App\Points\Models\PointsTransaction::where('user_id', $user->id)
                ->where('points', '>', 0)
                ->whereDate('created_at', $date)
                ->sum('points');
            
            $recentDays[] = [
                'date' => $date,
                'points' => $dayPoints,
            ];
        }

        return response()->json([
            'success' => true,
            'data' => [
                'source_stats' => $sourceStats,
                'recent_days' => $recentDays,
            ]
        ]);
    }
} 