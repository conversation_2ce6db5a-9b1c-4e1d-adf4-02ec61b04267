# 🚀 订单自动分单打印系统

## 📋 系统概述

本系统实现了订单创建后自动按仓库拆分商品并分别打印小票的完整功能。当用户下单后，系统会：

1. **自动分析订单商品** - 识别每个商品所属的仓库
2. **智能仓库分配** - 支持多种分配策略（商品默认、库存优先、区域就近）
3. **分单打印** - 每个仓库只打印属于该仓库的商品
4. **异步处理** - 不阻塞订单创建流程
5. **失败重试** - 支持打印失败重试机制

## 🏗️ 系统架构

### 核心组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   订单观察者      │    │  飞蛾云服务层     │    │  仓库打印机绑定   │
│  OrderObserver  │ -> │ FlyCloudService │ -> │WarehousePrinter │
│                 │    │                 │    │    Binding     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
          │                       │                       │
          v                       v                       v
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    订单创建       │    │   商品仓库分配    │    │   飞蛾云打印机    │
│  Order Created  │    │Warehouse Assignment│   │ FlyCloudPrinter │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 数据流程

```
用户下单 -> 订单创建 -> 观察者触发 -> 仓库分配 -> 分单生成 -> 打印机发送 -> 小票打印
    ↓           ↓           ↓           ↓           ↓           ↓           ↓
  Order    OrderObserver  智能分析   按仓库分组   生成内容   API调用    实体打印
```

## 🔧 核心文件说明

### 1. 订单观察者 (`app/Order/Observers/OrderObserver.php`)
- **功能**: 监听订单创建和状态变更事件
- **触发时机**: 订单创建时、订单状态变为已付款时
- **处理方式**: 异步队列处理，避免阻塞订单创建

### 2. 飞蛾云服务 (`app/FlyCloud/Services/FlyCloudService.php`)
- **功能**: 核心业务逻辑，负责仓库分配和打印处理
- **关键方法**:
  - `printOrderByWarehouses()` - 按仓库分单打印
  - `getItemWarehouseId()` - 智能仓库分配
  - `generateWarehouseOrderContent()` - 生成仓库分单内容

### 3. 仓库打印机绑定 (`app/FlyCloud/Models/WarehousePrinterBinding.php`)
- **功能**: 管理仓库与打印机的绑定关系
- **支持类型**: 订单打印、拣货单、配送单
- **绑定策略**: 默认打印机、优先级排序

### 4. 配置文件 (`config/flycloud.php`)
- **功能**: 系统配置和参数设置
- **关键配置**:
  - 自动打印开关
  - 触发时机设置
  - 仓库分配策略
  - 打印失败处理

## 🚀 使用指南

### 1. 环境配置

在 `.env` 文件中配置飞蛾云参数：

```bash
# 飞蛾云API配置
FLYCLOUD_API_URL=http://api.feieyun.cn/Api/Open/
FLYCLOUD_USER=your_username
FLYCLOUD_UKEY=your_ukey

# 自动打印配置
FLYCLOUD_AUTO_PRINT_ENABLED=true
FLYCLOUD_AUTO_PRINT_ON_CREATE=true
FLYCLOUD_AUTO_PRINT_ON_PAID=false

# 仓库配置
DEFAULT_WAREHOUSE_ID=1
WAREHOUSE_ASSIGNMENT_STRATEGY=inventory

# 打印配置
FLYCLOUD_STORE_NAME=万家生鲜
FLYCLOUD_SERVICE_PHONE=************
```

### 2. 数据库设置

确保已运行相关数据库迁移：

```bash
php artisan migrate
```

相关数据表：
- `orders` - 订单表
- `order_items` - 订单项表
- `flycloud_printers` - 飞蛾云打印机表
- `warehouse_printer_bindings` - 仓库打印机绑定表
- `warehouses` - 仓库表
- `inventory` - 库存表

### 3. 系统测试

使用测试命令验证系统：

```bash
# 测试现有系统配置
php artisan order:test-auto-print

# 创建测试订单并测试
php artisan order:test-auto-print --create

# 测试指定订单
php artisan order:test-auto-print --order_id=123
```

## ⚙️ 配置管理

### 1. 前端管理界面

访问系统管理 > 飞蛾云管理 > 系统设置，可以配置：

- **API配置**: 飞蛾云连接参数
- **自动打印设置**: 开关、触发时机、分配策略
- **小票格式**: 宽度、字体、自动切纸等
- **系统状态**: 实时查看连接状态和打印机状态

### 2. 仓库打印机绑定

访问系统管理 > 飞蛾云管理 > 仓库绑定，可以：

- 绑定仓库与打印机
- 设置默认打印机
- 配置打印类型（订单/拣货/配送）
- 管理优先级

## 🔄 工作流程详解

### 1. 订单创建流程

```
1. 用户提交订单
   ↓
2. OrderService 创建订单和订单项
   ↓
3. OrderObserver 监听到订单创建事件
   ↓
4. 检查自动打印配置是否启用
   ↓
5. 异步队列执行打印任务（延迟5秒）
```

### 2. 仓库分配逻辑

```
商品仓库分配优先级：
1. 订单项指定仓库（warehouse_id）
2. 根据配置策略分配：
   - product: 商品默认仓库
   - inventory: 库存优先（选择有库存的仓库）
   - region: 区域就近（按配送地址选择）
3. 系统默认仓库
```

### 3. 分单打印流程

```
1. 按仓库分组订单商品
   ↓
2. 获取各仓库的默认打印机
   ↓
3. 生成各仓库的分单内容
   ↓
4. 发送到对应的飞蛾云打印机
   ↓
5. 记录打印结果和日志
```

## 📝 小票格式示例

### 仓库分单格式

```
        万家生鲜
        仓库分单
================================
订单号: 20241203123456789
仓库ID: 1
分单时间: 2024-12-03 14:30:25
下单时间: 2024-12-03 13:45:12
下单人: 张三
下单手机: 13800138000
支付方式: 微信支付
--------------------------------
苹果（红富士）
数量: 2(斤) x ¥5.00
                      小计: ¥10.00

香蕉（进口）
数量: 1(把) x ¥8.50
                      小计: ¥8.50

洗衣液
数量: 3(瓶) x ¥15.80
                      小计: ¥47.40

鸡蛋
数量: 1(盒) x ¥12.50
                      小计: ¥12.50

--------------------------------
商品数量: 4
                  仓库小计: ¥78.40
--------------------------------
收货信息
收货人: 李四
联系电话: 13900139000
收货地址: 北京市朝阳区XX街道123号
订单备注: 请在下午3点后送达
================================
        请按此单拣货
    打印时间: 2024-12-03 14:30:25
```

## 🛠️ API接口

### 分单打印接口

```http
POST /api/flycloud/print-order-by-warehouses
Content-Type: application/json

{
    "order_id": 123,
    "print_type": "order",
    "copies": 1
}
```

### 仓库打印机绑定接口

```http
POST /api/flycloud/warehouse-printer-binding
Content-Type: application/json

{
    "warehouse_id": 1,
    "flycloud_printer_id": 2,
    "print_type": "order",
    "is_default": true
}
```

## 🚨 故障排除

### 常见问题

1. **自动打印不工作**
   - 检查 `FLYCLOUD_AUTO_PRINT_ENABLED` 配置
   - 确认订单观察者已注册
   - 查看日志文件 `storage/logs/laravel.log`

2. **分单打印失败**
   - 检查仓库打印机绑定配置
   - 验证飞蛾云API连接
   - 确认打印机在线状态

3. **商品仓库分配错误**
   - 检查仓库分配策略配置
   - 确认商品的仓库信息
   - 验证默认仓库设置

### 日志查看

```bash
# 查看自动打印日志
tail -f storage/logs/laravel.log | grep "自动分单打印"

# 查看飞蛾云API日志
tail -f storage/logs/laravel.log | grep "FlyCloud"
```

## 🔮 扩展功能

### 计划中的功能

1. **智能库存预警** - 库存不足时自动提醒
2. **多仓库协调** - 单个商品可从多个仓库拣货
3. **配送路线优化** - 按配送路线重新分组
4. **电子面单集成** - 自动生成配送面单
5. **实时状态推送** - WebSocket实时推送打印状态

### 自定义扩展

可以通过以下方式扩展系统：

1. **自定义仓库分配策略** - 在 `FlyCloudService` 中添加新的分配方法
2. **自定义小票格式** - 修改 `generateWarehouseOrderContent()` 方法
3. **自定义通知方式** - 在 `OrderObserver` 中添加邮件、短信等通知
4. **自定义打印触发条件** - 扩展观察者监听更多事件

## 📞 技术支持

如有问题或建议，请：

1. 查看系统日志进行初步排查
2. 使用测试命令验证系统状态
3. 联系技术支持团队

---

**系统版本**: v1.0  
**最后更新**: 2024-12-03  
**兼容性**: Laravel 10.x, PHP 8.1+ 