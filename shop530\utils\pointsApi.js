// utils/pointsApi.js - 积分商城API服务
const { request } = require('./request');

/**
 * 积分商城API服务
 */
class PointsAPI {
  
  // ==================== 用户积分相关 ====================
  
  /**
   * 获取用户积分余额
   */
  static async getUserBalance() {
    return request({
      url: '/api/points/balance',
      method: 'GET'
    });
  }

  /**
   * 获取用户积分流水记录
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.per_page - 每页数量
   * @param {string} params.type - 类型：earn,spend,refund,expire,admin
   * @param {string} params.source - 来源
   */
  static async getUserTransactions(params = {}) {
    return request({
      url: '/api/points/transactions',
      method: 'GET',
      data: params
    });
  }

  /**
   * 获取用户积分统计
   */
  static async getUserStats() {
    return request({
      url: '/api/points/stats',
      method: 'GET'
    });
  }

  /**
   * 获取积分排行榜
   * @param {number} limit - 限制数量
   */
  static async getPointsRanking(limit = 10) {
    return request({
      url: '/api/points/ranking',
      method: 'GET',
      data: { limit }
    });
  }

  /**
   * 每日签到
   */
  static async dailySignin() {
    return request({
      url: '/api/points/signin',
      method: 'POST'
    });
  }

  /**
   * 获取签到状态
   */
  static async getSigninStatus() {
    return request({
      url: '/api/points/signin-status',
      method: 'GET'
    });
  }

  /**
   * 获取积分规则
   */
  static async getPointsRules() {
    return request({
      url: '/api/points/rules',
      method: 'GET'
    });
  }

  /**
   * 获取积分获得历史统计
   */
  static async getEarnHistory() {
    return request({
      url: '/api/points/earn-history',
      method: 'GET'
    });
  }

  // ==================== 积分商品相关 ====================

  /**
   * 获取积分商品列表
   * @param {Object} params - 查询参数
   * @param {string} params.category - 分类：physical,virtual,coupon
   * @param {string} params.exchange_type - 兑换类型：pure_points,mixed_payment
   * @param {number} params.min_points - 最小积分
   * @param {number} params.max_points - 最大积分
   * @param {string} params.sort - 排序：sort_order,points_price,exchanged_count
   * @param {number} params.page - 页码
   * @param {number} params.per_page - 每页数量
   */
  static async getPointsProducts(params = {}) {
    return request({
      url: '/api/points/products',
      method: 'GET',
      data: params
    });
  }

  /**
   * 获取积分商品详情
   * @param {number} productId - 商品ID
   */
  static async getPointsProduct(productId) {
    return request({
      url: `/api/points/products/${productId}`,
      method: 'GET'
    });
  }

  /**
   * 检查商品兑换资格
   * @param {number} productId - 商品ID
   * @param {number} quantity - 数量
   */
  static async checkExchangeEligibility(productId, quantity = 1) {
    return request({
      url: `/api/points/products/${productId}/check-eligibility`,
      method: 'GET',
      data: { quantity }
    });
  }

  /**
   * 获取积分商品分类
   */
  static async getPointsCategories() {
    return request({
      url: '/api/points/products/categories',
      method: 'GET'
    });
  }

  /**
   * 获取热门积分商品
   * @param {number} limit - 限制数量
   */
  static async getPopularProducts(limit = 10) {
    return request({
      url: '/api/points/products/popular',
      method: 'GET',
      data: { limit }
    });
  }

  /**
   * 获取推荐积分商品
   * @param {number} limit - 限制数量
   */
  static async getRecommendedProducts(limit = 10) {
    return request({
      url: '/api/points/products/recommended',
      method: 'GET',
      data: { limit }
    });
  }

  // ==================== 积分订单相关 ====================

  /**
   * 创建积分订单
   * @param {Object} orderData - 订单数据
   * @param {Array} orderData.items - 商品项目
   * @param {Object} orderData.shipping_info - 配送信息
   */
  static async createPointsOrder(orderData) {
    return request({
      url: '/api/points/orders',
      method: 'POST',
      data: orderData
    });
  }

  /**
   * 获取用户积分订单列表
   * @param {Object} params - 查询参数
   * @param {string} params.status - 订单状态
   * @param {string} params.delivery_method - 配送方式
   * @param {number} params.page - 页码
   * @param {number} params.per_page - 每页数量
   */
  static async getUserPointsOrders(params = {}) {
    return request({
      url: '/api/points/orders',
      method: 'GET',
      data: params
    });
  }

  /**
   * 获取积分订单详情
   * @param {number} orderId - 订单ID
   */
  static async getPointsOrder(orderId) {
    return request({
      url: `/api/points/orders/${orderId}`,
      method: 'GET'
    });
  }

  /**
   * 支付积分订单
   * @param {number} orderId - 订单ID
   * @param {string} paymentNo - 支付单号
   */
  static async payPointsOrder(orderId, paymentNo) {
    return request({
      url: `/api/points/orders/${orderId}/pay`,
      method: 'POST',
      data: { payment_no: paymentNo }
    });
  }

  /**
   * 取消积分订单
   * @param {number} orderId - 订单ID
   * @param {string} reason - 取消原因
   */
  static async cancelPointsOrder(orderId, reason = '') {
    return request({
      url: `/api/points/orders/${orderId}/cancel`,
      method: 'POST',
      data: { reason }
    });
  }

  /**
   * 确认收货
   * @param {number} orderId - 订单ID
   */
  static async confirmDelivery(orderId) {
    return request({
      url: `/api/points/orders/${orderId}/confirm-delivery`,
      method: 'POST'
    });
  }

  /**
   * 积分订单预览
   * @param {Array} items - 商品项目
   */
  static async previewPointsOrder(items) {
    return request({
      url: '/api/points/orders/preview',
      method: 'POST',
      data: { items }
    });
  }

  /**
   * 获取订单状态统计
   */
  static async getOrderStatusStats() {
    return request({
      url: '/api/points/orders/status-stats',
      method: 'GET'
    });
  }

  // ==================== 积分奖励相关 ====================

  /**
   * 获取商品积分奖励预览
   * @param {number} productId - 商品ID
   * @param {number} amount - 金额
   * @param {number} quantity - 数量
   */
  static async getProductRewardPreview(productId, amount, quantity = 1) {
    return request({
      url: `/api/points/rewards/products/${productId}/preview`,
      method: 'GET',
      data: { amount, quantity }
    });
  }

  /**
   * 获取购物车积分奖励预览
   * @param {Array} items - 购物车商品
   */
  static async getCartRewardPreview(items) {
    return request({
      url: '/api/points/rewards/cart/preview',
      method: 'POST',
      data: { items }
    });
  }

  /**
   * 获取有积分奖励的商品
   * @param {Object} params - 查询参数
   */
  static async getProductsWithRewards(params = {}) {
    return request({
      url: '/api/points/rewards/products',
      method: 'GET',
      data: params
    });
  }

  /**
   * 获取积分奖励统计
   */
  static async getRewardStats() {
    return request({
      url: '/api/points/rewards/stats',
      method: 'GET'
    });
  }

  /**
   * 获取积分奖励配置选项
   */
  static async getRewardOptions() {
    return request({
      url: '/api/points/rewards/options',
      method: 'GET'
    });
  }

  // ==================== 辅助方法 ====================

  /**
   * 格式化积分显示
   * @param {number} points - 积分数量
   */
  static formatPoints(points) {
    if (!points || points === 0) return '0';
    if (points >= 10000) {
      return (points / 10000).toFixed(1) + '万';
    }
    return points.toString();
  }

  /**
   * 格式化会员等级名称
   * @param {string} level - 等级代码
   */
  static formatMemberLevel(level) {
    const levelNames = {
      bronze: '青铜会员',
      silver: '白银会员', 
      gold: '黄金会员',
      diamond: '钻石会员'
    };
    return levelNames[level] || '普通会员';
  }

  /**
   * 格式化积分变动类型
   * @param {string} source - 来源
   */
  static formatPointsSource(source) {
    const sourceNames = {
      order: '订单完成',
      signin: '每日签到',
      invite: '邀请好友',
      review: '商品评价',
      share: '分享商品',
      birthday: '生日奖励',
      upgrade: '会员升级',
      admin: '管理员操作',
      points_mall: '积分商城',
      refund: '订单退款'
    };
    return sourceNames[source] || '其他';
  }

  /**
   * 格式化订单状态
   * @param {string} status - 状态代码
   */
  static formatOrderStatus(status) {
    const statusNames = {
      pending: '待支付',
      paid: '已支付',
      shipped: '已发货',
      delivered: '已送达',
      completed: '已完成',
      cancelled: '已取消'
    };
    return statusNames[status] || '未知状态';
  }

  /**
   * 格式化商品分类
   * @param {string} category - 分类代码
   */
  static formatProductCategory(category) {
    const categoryNames = {
      physical: '实物商品',
      virtual: '虚拟商品',
      coupon: '优惠券'
    };
    return categoryNames[category] || '其他';
  }
}

module.exports = PointsAPI; 