<?php

namespace App\Payment\Services;

use Illuminate\Support\Facades\Log;

class PaymentOfferService
{
    /**
     * 支付方式优惠配置
     */
    private const PAYMENT_OFFERS = [
        'wechat' => [
            'type' => 'fixed_amount',
            'value' => 5.00,
            'min_amount' => 50.00, // 最低消费金额
            'description' => '微信支付立减5元'
        ],
        'alipay' => [
            'type' => 'fixed_amount', 
            'value' => 3.00,
            'min_amount' => 30.00,
            'description' => '支付宝支付立减3元'
        ],
        'cod' => [
            'type' => 'none',
            'value' => 0,
            'description' => '货到付款无优惠'
        ]
    ];

    /**
     * 计算支付方式优惠（支持叠加优惠）
     *
     * @param string $paymentMethod 支付方式
     * @param float $orderAmount 订单金额（商品总额）
     * @param bool $enableStacking 是否启用叠加优惠
     * @return array 优惠信息
     */
    public function calculatePaymentOffer(string $paymentMethod, float $orderAmount, bool $enableStacking = true): array
    {
        if ($enableStacking) {
            return $this->calculateStackablePaymentOffer($paymentMethod, $orderAmount);
        }
        
        return $this->calculateSinglePaymentOffer($paymentMethod, $orderAmount);
    }

    /**
     * 计算单一支付优惠
     */
    private function calculateSinglePaymentOffer(string $paymentMethod, float $orderAmount): array
    {
        $config = self::PAYMENT_OFFERS[$paymentMethod] ?? null;
        
        if (!$config || $config['type'] === 'none') {
            return [
                'offer_amount' => 0,
                'applicable' => false,
                'reason' => '该支付方式无优惠',
                'description' => '',
                'offers' => []
            ];
        }

        // 检查最低消费金额
        if ($orderAmount < $config['min_amount']) {
            return [
                'offer_amount' => 0,
                'applicable' => false,
                'reason' => "订单金额不足{$config['min_amount']}元",
                'description' => $config['description'],
                'offers' => []
            ];
        }

        $offerAmount = 0;
        
        switch ($config['type']) {
            case 'fixed_amount':
                $offerAmount = min($config['value'], $orderAmount);
                break;
            case 'percentage':
                $offerAmount = $orderAmount * ($config['value'] / 100);
                if (isset($config['max_offer'])) {
                    $offerAmount = min($offerAmount, $config['max_offer']);
                }
                break;
        }

        return [
            'offer_amount' => round($offerAmount, 2),
            'applicable' => $offerAmount > 0,
            'reason' => $config['description'],
            'description' => $config['description'],
            'original_amount' => $orderAmount,
            'final_amount' => $orderAmount - $offerAmount,
            'offers' => [
                [
                    'type' => $config['type'],
                    'amount' => round($offerAmount, 2),
                    'description' => $config['description']
                ]
            ]
        ];
    }

    /**
     * 计算叠加支付优惠
     */
    private function calculateStackablePaymentOffer(string $paymentMethod, float $orderAmount): array
    {
        // 从数据库获取该支付方式的所有有效优惠
        $offers = \App\Payment\Models\PaymentOffer::where('payment_method', $paymentMethod)
            ->where('status', true)
            ->where(function($query) {
                $query->whereNull('start_time')
                      ->orWhere('start_time', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_time')
                      ->orWhere('end_time', '>=', now());
            })
            ->orderBy('sort_order')
            ->get();

        if ($offers->isEmpty()) {
            return [
                'offer_amount' => 0,
                'applicable' => false,
                'reason' => '该支付方式无有效优惠',
                'description' => '',
                'offers' => []
            ];
        }

        $totalOffer = 0;
        $applicableOffers = [];
        $currentAmount = $orderAmount;
        $descriptions = [];

        foreach ($offers as $offer) {
            // 检查最低消费金额
            if ($currentAmount < $offer->min_amount) {
                continue;
            }

            $offerAmount = 0;
            
            switch ($offer->offer_type) {
                case 'fixed_amount':
                    $offerAmount = min($offer->offer_value, $currentAmount);
                    break;
                case 'percentage':
                    $offerAmount = $currentAmount * ($offer->offer_value / 100);
                    if ($offer->max_offer) {
                        $offerAmount = min($offerAmount, $offer->max_offer);
                    }
                    break;
                case 'none':
                    continue 2;
            }

            if ($offerAmount > 0) {
                $totalOffer += $offerAmount;
                $currentAmount -= $offerAmount; // 叠加计算：下一个优惠基于减去当前优惠后的金额
                
                $applicableOffers[] = [
                    'id' => $offer->id,
                    'type' => $offer->offer_type,
                    'amount' => round($offerAmount, 2),
                    'description' => $offer->description,
                    'applied_to_amount' => $currentAmount + $offerAmount
                ];
                
                $descriptions[] = $offer->description;
            }
        }

        return [
            'offer_amount' => round($totalOffer, 2),
            'applicable' => $totalOffer > 0,
            'reason' => $totalOffer > 0 ? '叠加优惠已应用' : '不满足任何优惠条件',
            'description' => implode(' + ', $descriptions),
            'original_amount' => $orderAmount,
            'final_amount' => $orderAmount - $totalOffer,
            'offers' => $applicableOffers,
            'stacking_enabled' => true
        ];
    }

    /**
     * 订单更正时的支付优惠反推计算
     *
     * @param float $originalAmount 原订单商品金额
     * @param float $correctedAmount 更正后商品金额
     * @param float $originalPaymentOffer 原支付优惠金额
     * @param string $paymentMethod 支付方式
     * @return array 新的支付优惠信息
     */
    public function recalculatePaymentOfferForCorrection(
        float $originalAmount,
        float $correctedAmount, 
        float $originalPaymentOffer,
        string $paymentMethod
    ): array {
        // 如果原来没有支付优惠，直接返回
        if ($originalPaymentOffer <= 0) {
            return [
                'new_offer_amount' => 0,
                'offer_change' => 0,
                'calculation_method' => 'no_original_offer',
                'description' => '原订单无支付优惠'
            ];
        }

        // 获取支付方式配置
        $config = self::PAYMENT_OFFERS[$paymentMethod] ?? null;
        if (!$config || $config['type'] === 'none') {
            return [
                'new_offer_amount' => 0,
                'offer_change' => -$originalPaymentOffer,
                'calculation_method' => 'payment_method_no_offer',
                'description' => '该支付方式不支持优惠'
            ];
        }

        // 方案1：按比例计算（推荐）
        if ($originalAmount > 0) {
            $offerRatio = $originalPaymentOffer / $originalAmount;
            $proportionalOffer = $correctedAmount * $offerRatio;
            
            // 重新验证是否符合优惠条件
            $newOfferInfo = $this->calculatePaymentOffer($paymentMethod, $correctedAmount);
            
            if (!$newOfferInfo['applicable']) {
                // 更正后不符合优惠条件
                return [
                    'new_offer_amount' => 0,
                    'offer_change' => -$originalPaymentOffer,
                    'calculation_method' => 'not_eligible_after_correction',
                    'description' => $newOfferInfo['reason']
                ];
            }
            
            // 取比例计算和重新计算的较小值
            $finalOffer = min($proportionalOffer, $newOfferInfo['offer_amount']);
            
            return [
                'new_offer_amount' => round($finalOffer, 2),
                'offer_change' => round($finalOffer - $originalPaymentOffer, 2),
                'calculation_method' => 'proportional_with_validation',
                'description' => "按比例重新计算支付优惠",
                'proportional_offer' => round($proportionalOffer, 2),
                'max_eligible_offer' => $newOfferInfo['offer_amount']
            ];
        }

        return [
            'new_offer_amount' => 0,
            'offer_change' => -$originalPaymentOffer,
            'calculation_method' => 'calculation_error',
            'description' => '计算错误：原订单金额为0'
        ];
    }

    /**
     * 获取支付方式优惠配置
     *
     * @param string|null $paymentMethod 支付方式，null返回所有
     * @return array
     */
    public function getPaymentOfferConfig(?string $paymentMethod = null): array
    {
        if ($paymentMethod) {
            return self::PAYMENT_OFFERS[$paymentMethod] ?? [];
        }
        
        return self::PAYMENT_OFFERS;
    }

    /**
     * 验证支付优惠是否有效
     *
     * @param string $paymentMethod
     * @param float $orderAmount
     * @param float $offerAmount
     * @return bool
     */
    public function validatePaymentOffer(string $paymentMethod, float $orderAmount, float $offerAmount): bool
    {
        $offerInfo = $this->calculatePaymentOffer($paymentMethod, $orderAmount);
        
        return $offerInfo['applicable'] && 
               abs($offerInfo['offer_amount'] - $offerAmount) < 0.01;
    }

    /**
     * 记录支付优惠日志
     *
     * @param array $offerInfo
     * @param array $context
     */
    public function logPaymentOffer(array $offerInfo, array $context = []): void
    {
        Log::info('支付优惠计算', array_merge([
            'offer_info' => $offerInfo,
            'timestamp' => now()->toDateTimeString()
        ], $context));
    }
} 