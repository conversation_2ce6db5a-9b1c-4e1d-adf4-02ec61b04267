<?php

namespace App\Product\Models;

use App\Product\Models\Category;
use App\Product\Models\ProductImage;
use App\Product\Models\ProductTag;
use App\Inventory\Models\Inventory;
use App\Warehouse\Models\Warehouse;
use App\Product\Models\ProductUnit;
use App\Unit\Models\Unit;
use App\Region\Models\RegionPrice;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Laravel\Scout\Searchable;
use Illuminate\Support\Facades\Schema;
use Overtrue\Pinyin\Pinyin;

class Product extends Model
{
    use HasFactory, Searchable;

    /**
     * 模型启动事件
     */
    protected static function boot()
    {
        parent::boot();

        // 商品状态变更为上架时，检查销售单位
        static::updating(function ($product) {
            if ($product->isDirty('status') && $product->status == 1) {
                $product->validateForPublishing();
            }
        });

        // 创建商品时如果状态为上架，检查销售单位
        static::creating(function ($product) {
            if ($product->status == 1) {
                $product->validateForPublishing();
            }
        });
    }

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'code',
        'name',
        'price',
        'sale_price',
        'cost_price',
        'description',
        'base_unit_id',
        'category_id',
        'series_id',
        'status',
        'allow_sale',
        'sort',
        'promoted_at',
        'promoted_end_at',
        'sales_count',
        'views_count',
        'is_featured',
        'min_sale_quantity',
        'inventory_policy',
        'min_stock_threshold',
        'max_negative_stock',
        'track_inventory',
        'inventory_type',
        'auto_reorder',
        'reorder_point',
        'reorder_quantity',
        'tag_ids',
        'tags_cache',
    ];

    /**
     * 类型转换
     */
    protected $casts = [
        'price' => 'float',
        'sale_price' => 'float',
        'cost_price' => 'float',
        'status' => 'integer',
        'allow_sale' => 'boolean',
        'sales_count' => 'integer',
        'views_count' => 'integer',
        'is_featured' => 'boolean',
        'min_sale_quantity' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'promoted_at' => 'datetime',
        'promoted_end_at' => 'datetime',
        'min_stock_threshold' => 'float',
        'max_negative_stock' => 'float',
        'track_inventory' => 'boolean',
        'auto_reorder' => 'boolean',
        'reorder_point' => 'float',
        'reorder_quantity' => 'float',
        'tag_ids' => 'array',
        'tags_cache' => 'array',
        // 积分奖励相关字段
        'points_reward_rate' => 'decimal:4',
        'points_reward_fixed' => 'integer',
        'points_reward_max' => 'integer',
        'points_min_amount' => 'decimal:2',
        'points_reward_enabled' => 'boolean',
    ];

    /**
     * 生成唯一的商品编码
     * 
     * @return string 商品编码
     */
    public static function generateProductCode()
    {
        do {
            $code = 'P' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (self::where('code', $code)->exists());
        
        return $code;
    }

    /**
     * 根据分类生成商品编码
     * 
     * @param int $categoryId 分类ID
     * @return string 商品编码
     */
    public static function generateProductCodeByCategory($categoryId)
    {
        // 获取分类信息
        $category = Category::find($categoryId);
        if (!$category) {
            throw new \Exception('分类不存在');
        }
        
        // 获取分类名称的拼音首字母
        $prefix = self::getCategoryPinyinPrefix($category->name);
        
        // 查找该分类下已有的最大编号
        $maxNumber = self::where('category_id', $categoryId)
            ->where('code', 'like', $prefix . '%')
            ->selectRaw('MAX(CAST(SUBSTRING(code, ' . (strlen($prefix) + 1) . ') AS UNSIGNED)) as max_num')
            ->value('max_num') ?? 0;
        
        // 生成新编号
        $newNumber = $maxNumber + 1;
        $code = $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
        
        // 确保编码唯一性（防重机制）
        $attempts = 0;
        while (self::where('code', $code)->exists() && $attempts < 100) {
            $newNumber++;
            $code = $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
            $attempts++;
        }
        
        if ($attempts >= 100) {
            throw new \Exception('无法生成唯一的商品编码，请联系管理员');
        }
        
        return $code;
    }

    /**
     * 获取分类名称的拼音首字母
     * 
     * @param string $categoryName 分类名称
     * @return string 拼音首字母前缀
     */
    private static function getCategoryPinyinPrefix($categoryName)
    {
        try {
            $pinyin = new Pinyin();
            
            // 获取拼音首字母数组
            $firstLetters = $pinyin->abbr($categoryName);
            
            // 转换Collection为数组
            if (is_object($firstLetters) && method_exists($firstLetters, 'toArray')) {
                $firstLetters = $firstLetters->toArray();
            }
            
            // 转换为大写并连接
            $prefix = strtoupper(implode('', $firstLetters));
            
            // 过滤掉非字母字符
            $prefix = preg_replace('/[^A-Z]/', '', $prefix);
            
            // 如果没有有效字符，使用默认前缀
            if (empty($prefix)) {
                $prefix = 'SP'; // 商品的拼音首字母
            }
            
            // 确保前缀长度为2-3个字符
            if (strlen($prefix) < 2) {
                $prefix = str_pad($prefix, 2, 'X');
            } elseif (strlen($prefix) > 3) {
                $prefix = substr($prefix, 0, 3);
            }
            
            return $prefix;
        } catch (\Exception $e) {
            Log::error('获取拼音首字母失败', [
                'category_name' => $categoryName,
                'error' => $e->getMessage()
            ]);
            
            // 降级处理：使用分类名称的前几个字符
            $prefix = '';
            $chars = mb_str_split($categoryName, 1, 'UTF-8');
            
            foreach ($chars as $char) {
                if (preg_match('/[A-Za-z]/', $char)) {
                    $prefix .= strtoupper($char);
                } elseif (preg_match('/[\x{4e00}-\x{9fff}]/u', $char)) {
                    // 简单的中文字符处理
                    $prefix .= 'C';
                }
                
                if (strlen($prefix) >= 3) {
                    break;
                }
            }
            
            if (empty($prefix)) {
                $prefix = 'SP';
            }
            
            if (strlen($prefix) < 2) {
                $prefix = str_pad($prefix, 2, 'X');
            } elseif (strlen($prefix) > 3) {
                $prefix = substr($prefix, 0, 3);
            }
            
            return $prefix;
        }
    }

    /**
     * 商品所属分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * 商品所属系列
     */
    public function series()
    {
        return $this->belongsTo(ProductSeries::class, 'series_id');
    }

    /**
     * 商品的属性值
     */
    public function attributeValues()
    {
        return $this->hasMany(ProductAttributeValue::class, 'product_id');
    }

    /**
     * 商品的属性（通过属性值关联）
     */
    public function attributes()
    {
        return $this->belongsToMany(ProductAttribute::class, 'product_attribute_values', 'product_id', 'attribute_id')
            ->withPivot('value', 'value_json')
            ->withTimestamps();
    }

    /**
     * 获取商品的格式化属性
     */
    public function getFormattedAttributes()
    {
        return $this->attributeValues()
            ->with('attribute')
            ->get()
            ->map(function ($attributeValue) {
                return [
                    'id' => $attributeValue->attribute->id,
                    'name' => $attributeValue->attribute->name,
                    'key' => $attributeValue->attribute->key,
                    'type' => $attributeValue->attribute->type,
                    'unit' => $attributeValue->attribute->unit,
                    'raw_value' => $attributeValue->getValue(),
                    'formatted_value' => $attributeValue->getFormattedValue(),
                ];
            });
    }

    /**
     * 获取产品在各个仓库的库存
     */
    public function inventories()
    {
        return $this->hasMany(Inventory::class);
    }

    /**
     * 获取存有该产品的仓库
     */
    public function warehouses()
    {
        return $this->belongsToMany(Warehouse::class, 'inventory')
            ->withPivot('stock', 'unit_id')
            ->withTimestamps();
    }

    /**
     * 检查库存是否足够
     * 
     * @param int $quantity 需要的数量
     * @param int|null $unitId 单位ID，默认为基本单位
     * @return bool 库存是否足够
     */
    public function checkStock($quantity, $unitId = null)
    {
        $currentStock = $this->getTotalStock();
        
        if ($unitId === null || $unitId === $this->base_unit_id) {
            return $currentStock >= $quantity;
        }
        
        // 转换为基本单位的数量
        $baseUnitQuantity = $quantity * $this->getUnitConversionRate($unitId, $this->base_unit_id);
        if ($baseUnitQuantity === null) {
            return false;
        }
        
        return $currentStock >= $baseUnitQuantity;
    }
    
    /**
     * 获取商品总库存（所有仓库库存的总和）
     * 支持缓存和批量查询优化
     * 
     * @param bool $useCache 是否使用缓存
     * @param int $cacheMinutes 缓存时间（分钟）
     * @return float 总库存
     */
    public function getTotalStock($useCache = true, $cacheMinutes = 5)
    {
        if (!$useCache) {
            return $this->inventories()->sum('stock');
        }
        
        $cacheKey = "product_stock:{$this->id}";
        
        return \Illuminate\Support\Facades\Cache::remember($cacheKey, $cacheMinutes * 60, function () {
            return $this->inventories()->sum('stock');
        });
    }
    
    /**
     * 批量获取多个商品的库存（性能优化版本）
     * 
     * @param array $productIds 商品ID数组
     * @param bool $useCache 是否使用缓存
     * @return array 商品ID => 库存数量的映射
     */
    public static function getBatchTotalStock(array $productIds, $useCache = true)
    {
        if (empty($productIds)) {
            return [];
        }
        
        $sortedIds = $productIds;
        sort($sortedIds);
        $cacheKey = "batch_stock:" . md5(implode(',', $sortedIds));
        
        if ($useCache) {
            $cached = \Illuminate\Support\Facades\Cache::get($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }
        
        // 批量查询所有库存
        $stockData = \Illuminate\Support\Facades\DB::table('inventory')
            ->whereIn('product_id', $productIds)
            ->groupBy('product_id')
            ->selectRaw('product_id, SUM(stock) as total_stock')
            ->pluck('total_stock', 'product_id')
            ->toArray();
        
        // 确保所有商品都有记录（没有库存的商品返回0）
        $result = [];
        foreach ($productIds as $productId) {
            $result[$productId] = $stockData[$productId] ?? 0;
        }
        
        if ($useCache) {
            \Illuminate\Support\Facades\Cache::put($cacheKey, $result, 5 * 60); // 缓存5分钟
        }
        
        return $result;
    }
    
    /**
     * 清除商品库存缓存
     */
    public function clearStockCache()
    {
        $cacheKey = "product_stock:{$this->id}";
        \Illuminate\Support\Facades\Cache::forget($cacheKey);
        
        // 同时清除可能相关的批量缓存
        // 这里可以更精确地清除相关缓存，但为了简单起见，可以考虑使用标签缓存
    }
    
    /**
     * 减少库存数量（从所有仓库中按比例减少）
     * 
     * @param float $quantity 减少的数量
     * @param int|null $unitId 单位ID，默认为基本单位
     * @return bool 操作是否成功
     */
    public function reduceStock($quantity, $unitId = null)
    {
        // 转换为基本单位的数量
        $baseUnitQuantity = $quantity;
        if ($unitId !== null && $unitId !== $this->base_unit_id) {
            $baseUnitQuantity = $quantity * $this->getUnitConversionRate($unitId, $this->base_unit_id);
            if ($baseUnitQuantity === null) {
                return false;
            }
        }
        
        $inventories = $this->inventories()->where('stock', '>', 0)->get();
        if ($inventories->isEmpty()) {
            // 如果没有库存记录，创建一个默认的库存记录（负库存）
            $defaultWarehouse = \App\Warehouse\Models\Warehouse::first();
            if ($defaultWarehouse) {
                \App\Inventory\Models\Inventory::create([
                    'product_id' => $this->id,
                    'warehouse_id' => $defaultWarehouse->id,
                    'stock' => -$baseUnitQuantity,
                    'unit_id' => $this->base_unit_id,
                ]);
            }
            // 清除库存缓存
            $this->clearStockCache();
            return true;
        }
        
        $totalStock = $inventories->sum('stock');
        $remainingToReduce = $baseUnitQuantity;
        
        foreach ($inventories as $inventory) {
            if ($remainingToReduce <= 0) break;
            
            $reductionFromThis = min($inventory->stock, $remainingToReduce);
            $inventory->stock -= $reductionFromThis;
            $inventory->save();
            
            $remainingToReduce -= $reductionFromThis;
        }
        
        // 清除库存缓存
        $this->clearStockCache();
        return true;
    }
    
    /**
     * 增加库存数量（添加到默认仓库）
     * 
     * @param float $quantity 增加的数量
     * @param int|null $unitId 单位ID，默认为基本单位
     * @return bool 操作是否成功
     */
    public function addStock($quantity, $unitId = null)
    {
        // 转换为基本单位的数量
        $baseUnitQuantity = $quantity;
        if ($unitId !== null && $unitId !== $this->base_unit_id) {
            $baseUnitQuantity = $quantity * $this->getUnitConversionRate($unitId, $this->base_unit_id);
            if ($baseUnitQuantity === null) {
                return false;
            }
        }
        
        // 找到第一个有库存的仓库，或创建默认库存记录
        $inventory = $this->inventories()->first();
        if (!$inventory) {
            $defaultWarehouse = \App\Warehouse\Models\Warehouse::first();
            if ($defaultWarehouse) {
                $inventory = \App\Inventory\Models\Inventory::create([
                    'product_id' => $this->id,
                    'warehouse_id' => $defaultWarehouse->id,
                    'stock' => 0,
                    'unit_id' => $this->base_unit_id,
                ]);
            } else {
                return false;
            }
        }
        
        $inventory->stock += $baseUnitQuantity;
        $result = $inventory->save();
        
        // 清除库存缓存
        if ($result) {
            $this->clearStockCache();
        }
        
        return $result;
    }

    /**
     * 智能库存检查 - 根据库存策略进行检查
     * 
     * @param float $quantity 需要的数量
     * @param int|null $unitId 单位ID，默认为基本单位
     * @param int|null $warehouseId 仓库ID（用于仓库级策略）
     * @return array 检查结果 ['allowed' => bool, 'message' => string, 'warning' => string|null]
     */
    public function checkStockWithPolicy($quantity, $unitId = null, $warehouseId = null)
    {
        // 如果不启用库存追踪，直接允许
        if (!$this->track_inventory) {
            return [
                'allowed' => true,
                'message' => '商品未启用库存追踪',
                'warning' => null
            ];
        }

        // 转换为基本单位的数量
        $baseUnitQuantity = $quantity;
        if ($unitId !== null && $unitId !== $this->base_unit_id) {
            $baseUnitQuantity = $quantity * $this->getUnitConversionRate($unitId, $this->base_unit_id);
            if ($baseUnitQuantity === null) {
                return [
                    'allowed' => false,
                    'message' => '单位转换失败',
                    'warning' => null
                ];
            }
        }

        $currentStock = $this->getTotalStock();

        // 获取实际的库存策略（处理继承逻辑）
        $effectivePolicy = $this->getEffectiveInventoryPolicy($warehouseId);
        
        // 根据库存策略进行检查
        switch ($effectivePolicy) {
            case 'unlimited':
                return [
                    'allowed' => true,
                    'message' => '无限库存商品',
                    'warning' => null
                ];

            case 'strict':
                if ($currentStock >= $baseUnitQuantity) {
                    $warning = null;
                    // 检查是否接近最小库存阈值
                    if ($this->min_stock_threshold && ($currentStock - $baseUnitQuantity) <= $this->min_stock_threshold) {
                        $warning = "出库后库存将低于预警阈值 {$this->min_stock_threshold}";
                    }
                    return [
                        'allowed' => true,
                        'message' => '库存充足',
                        'warning' => $warning
                    ];
                } else {
                    return [
                        'allowed' => false,
                        'message' => "库存不足，当前库存：{$currentStock}，需要：{$baseUnitQuantity}",
                        'warning' => null
                    ];
                }

            case 'allow_negative':
                $afterStock = $currentStock - $baseUnitQuantity;
                
                // 检查是否超过最大负库存限制
                if ($this->max_negative_stock && $afterStock < $this->max_negative_stock) {
                    return [
                        'allowed' => false,
                        'message' => "超过最大负库存限制 {$this->max_negative_stock}，出库后库存将为：{$afterStock}",
                        'warning' => null
                    ];
                }

                $warning = null;
                if ($afterStock < 0) {
                    $warning = "出库后库存将为负数：{$afterStock}";
                } elseif ($this->min_stock_threshold && $afterStock <= $this->min_stock_threshold) {
                    $warning = "出库后库存将低于预警阈值 {$this->min_stock_threshold}";
                }

                return [
                    'allowed' => true,
                    'message' => '允许负库存出库',
                    'warning' => $warning
                ];

            default:
                return [
                    'allowed' => false,
                    'message' => '未知的库存策略',
                    'warning' => null
                ];
        }
    }

    /**
     * 获取商品的实际库存策略（处理继承逻辑）
     * 
     * @param int|null $warehouseId 仓库ID，用于继承仓库策略
     * @return string 实际的库存策略
     */
    public function getEffectiveInventoryPolicy($warehouseId = null)
    {
        // 如果商品策略不是继承，直接返回商品的策略
        if ($this->inventory_policy !== 'inherit') {
            return $this->inventory_policy;
        }

        // 如果是继承策略，需要查找仓库的策略
        if ($warehouseId) {
            $warehouse = \App\Warehouse\Models\Warehouse::find($warehouseId);
            if ($warehouse) {
                // 如果仓库策略也是继承，则使用系统默认策略
                if ($warehouse->inventory_policy === 'inherit') {
                    return config('inventory.default_policy', 'strict');
                }
                return $warehouse->inventory_policy;
            }
        }

        // 如果没有指定仓库或找不到仓库，查找商品所在的默认仓库
        $defaultWarehouse = $this->getDefaultWarehouse();
        if ($defaultWarehouse) {
            if ($defaultWarehouse->inventory_policy === 'inherit') {
                return config('inventory.default_policy', 'strict');
            }
            return $defaultWarehouse->inventory_policy;
        }

        // 最后回退到系统默认策略
        return config('inventory.default_policy', 'strict');
    }

    /**
     * 获取商品的默认仓库
     * 
     * @return \App\Warehouse\Models\Warehouse|null
     */
    public function getDefaultWarehouse()
    {
        // 优先使用库存表中有库存的第一个仓库
        $inventory = $this->inventories()->where('stock', '>', 0)->first();
        if ($inventory && $inventory->warehouse) {
            return $inventory->warehouse;
        }

        // 如果没有库存，使用库存表中的第一个仓库
        $inventory = $this->inventories()->first();
        if ($inventory && $inventory->warehouse) {
            return $inventory->warehouse;
        }

        // 如果没有库存记录，使用系统默认仓库
        return \App\Warehouse\Models\Warehouse::where('is_default', true)->first() 
            ?: \App\Warehouse\Models\Warehouse::first();
    }

    /**
     * 按策略减少库存
     * 
     * @param float $quantity 减少的数量
     * @param int|null $unitId 单位ID，默认为基本单位
     * @param bool $forceReduce 是否强制减少（忽略策略检查）
     * @return array 操作结果 ['success' => bool, 'message' => string, 'warning' => string|null]
     */
    public function reduceStockWithPolicy($quantity, $unitId = null, $forceReduce = false)
    {
        if (!$forceReduce) {
            $checkResult = $this->checkStockWithPolicy($quantity, $unitId);
            if (!$checkResult['allowed']) {
                return [
                    'success' => false,
                    'message' => $checkResult['message'],
                    'warning' => $checkResult['warning']
                ];
            }
        }

        // 执行库存减少
        $originalStock = $this->getTotalStock();
        $success = $this->reduceStock($quantity, $unitId);
        
        if ($success) {
            $newStock = $this->getTotalStock();
            $warning = null;

            // 检查是否需要触发库存预警
            if ($this->shouldTriggerStockAlert($newStock)) {
                $this->triggerStockAlert($originalStock, $newStock);
            }

            // 检查是否需要自动补货
            if ($this->shouldAutoReorder($newStock)) {
                $this->triggerAutoReorder();
            }

            if ($newStock < 0) {
                $warning = "库存已为负数：{$newStock}";
            } elseif ($this->min_stock_threshold && $newStock <= $this->min_stock_threshold) {
                $warning = "库存已低于预警阈值：{$newStock} <= {$this->min_stock_threshold}";
            }

            return [
                'success' => true,
                'message' => "库存减少成功，当前库存：{$newStock}",
                'warning' => $warning,
                'stock_before' => $originalStock,
                'stock_after' => $newStock
            ];
        }

        return [
            'success' => false,
            'message' => '库存减少失败',
            'warning' => null
        ];
    }

    /**
     * 检查是否应该触发库存预警
     * 
     * @param float $currentStock 当前库存
     * @return bool
     */
    public function shouldTriggerStockAlert($currentStock)
    {
        if (!$this->track_inventory) {
            return false;
        }

        // 负库存预警
        if ($currentStock < 0) {
            return true;
        }

        // 低库存预警
        if ($this->min_stock_threshold && $currentStock <= $this->min_stock_threshold) {
            return true;
        }

        return false;
    }

    /**
     * 触发库存预警
     * 
     * @param float $previousStock 之前的库存
     * @param float $currentStock 当前库存
     * @return void
     */
    public function triggerStockAlert($previousStock, $currentStock)
    {
        // 记录库存预警日志
        Log::warning('库存预警触发', [
            'product_id' => $this->id,
            'product_name' => $this->name,
            'previous_stock' => $previousStock,
            'current_stock' => $currentStock,
            'min_threshold' => $this->min_stock_threshold,
            'inventory_policy' => $this->inventory_policy,
            'alert_type' => $currentStock < 0 ? 'negative_stock' : 'low_stock'
        ]);

        // 创建库存预警记录
        $alertType = $currentStock < 0 ? 'negative_stock' : 'low_stock';
        $severity = $this->determineAlertSeverity($currentStock);
        
        $alertData = [
            'product_id' => $this->id,
            'alert_type' => $alertType,
            'severity' => $severity,
            'title' => $this->generateAlertTitle($alertType, $currentStock),
            'message' => $this->generateAlertMessage($alertType, $currentStock, $previousStock),
            'current_stock' => $currentStock,
            'threshold_value' => $this->min_stock_threshold,
            'previous_stock' => $previousStock,
            'alert_data' => [
                'inventory_policy' => $this->inventory_policy,
                'max_negative_stock' => $this->max_negative_stock,
                'stock_change' => $currentStock - $previousStock,
                'triggered_at' => now()->toISOString(),
            ],
            'auto_resolve' => true,
            'notification_channels' => ['system', 'email'],
            'expires_at' => now()->addDays(7), // 7天后过期
        ];

        // 使用InventoryAlert模型创建预警
        $alert = \App\Inventory\Models\InventoryAlert::createAlert($alertData);
        
        // 发送通知
        $alert->sendNotification();
    }

    /**
     * 检查是否应该自动补货
     * 
     * @param float $currentStock 当前库存
     * @return bool
     */
    public function shouldAutoReorder($currentStock)
    {
        return $this->auto_reorder && 
               $this->reorder_point && 
               $currentStock <= $this->reorder_point;
    }

    /**
     * 触发自动补货
     * 
     * @return void
     */
    public function triggerAutoReorder()
    {
        // 记录自动补货日志
        Log::info('自动补货触发', [
            'product_id' => $this->id,
            'product_name' => $this->name,
            'current_stock' => $this->getTotalStock(),
            'reorder_point' => $this->reorder_point,
            'reorder_quantity' => $this->reorder_quantity
        ]);

        // 查找该商品的活跃自动补货规则
        $reorderRules = \App\Inventory\Models\AutoReorderRule::where('product_id', $this->id)
            ->active()
            ->get();

        foreach ($reorderRules as $rule) {
            $triggerResult = $rule->shouldTrigger();
            
            if ($triggerResult['should_trigger']) {
                $reorderRecord = $rule->trigger();
                
                if ($reorderRecord) {
                    Log::info('自动补货规则触发成功', [
                        'product_id' => $this->id,
                        'rule_id' => $rule->id,
                        'record_id' => $reorderRecord->id,
                        'reason' => $triggerResult['reason']
                    ]);
                    
                    // 如果不需要审批，自动批准
                    if (!$rule->require_approval) {
                        $reorderRecord->approve(null, null, '系统自动批准');
                    }
                }
            }
        }
    }

    /**
     * 确定预警严重程度
     * 
     * @param float $currentStock 当前库存
     * @return string
     */
    protected function determineAlertSeverity($currentStock)
    {
        if ($currentStock < 0) {
            // 负库存
            if ($this->max_negative_stock && $currentStock <= $this->max_negative_stock * 0.8) {
                return 'critical'; // 接近最大负库存限制
            }
            return 'high';
        } elseif ($this->min_stock_threshold && $currentStock <= $this->min_stock_threshold * 0.5) {
            return 'high'; // 库存极低
        } elseif ($this->min_stock_threshold && $currentStock <= $this->min_stock_threshold) {
            return 'medium'; // 低于预警阈值
        }
        
        return 'low';
    }

    /**
     * 生成预警标题
     * 
     * @param string $alertType 预警类型
     * @param float $currentStock 当前库存
     * @return string
     */
    protected function generateAlertTitle($alertType, $currentStock)
    {
        switch ($alertType) {
            case 'negative_stock':
                return "商品 {$this->name} 库存为负数";
            case 'low_stock':
                return "商品 {$this->name} 库存不足";
            default:
                return "商品 {$this->name} 库存预警";
        }
    }

    /**
     * 生成预警消息
     * 
     * @param string $alertType 预警类型
     * @param float $currentStock 当前库存
     * @param float $previousStock 之前库存
     * @return string
     */
    protected function generateAlertMessage($alertType, $currentStock, $previousStock)
    {
        $change = $currentStock - $previousStock;
        $changeText = $change < 0 ? "减少了" . abs($change) : "增加了" . $change;
        
        switch ($alertType) {
            case 'negative_stock':
                return "商品 {$this->name} 当前库存为 {$currentStock}，已为负数。库存从 {$previousStock} {$changeText}。";
            case 'low_stock':
                $threshold = $this->min_stock_threshold ?? 0;
                return "商品 {$this->name} 当前库存为 {$currentStock}，低于预警阈值 {$threshold}。库存从 {$previousStock} {$changeText}。";
            default:
                return "商品 {$this->name} 库存发生变化，当前库存：{$currentStock}，之前库存：{$previousStock}。";
        }
    }

    /**
     * 获取商品的库存预警
     */
    public function inventoryAlerts()
    {
        return $this->hasMany(\App\Inventory\Models\InventoryAlert::class);
    }

    /**
     * 获取商品的自动补货规则
     */
    public function autoReorderRules()
    {
        return $this->hasMany(\App\Inventory\Models\AutoReorderRule::class);
    }

    /**
     * 获取商品的补货记录
     */
    public function reorderRecords()
    {
        return $this->hasMany(\App\Inventory\Models\ReorderRecord::class);
    }

    /**
     * 获取活跃的库存预警
     */
    public function getActiveAlertsAttribute()
    {
        return $this->inventoryAlerts()->active()->orderBySeverity()->get();
    }

    /**
     * 检查是否有活跃预警
     * 
     * @return bool
     */
    public function hasActiveAlerts()
    {
        return $this->inventoryAlerts()->active()->exists();
    }

    /**
     * 获取最高严重程度的预警
     * 
     * @return \App\Inventory\Models\InventoryAlert|null
     */
    public function getHighestSeverityAlert()
    {
        return $this->inventoryAlerts()->active()->orderBySeverity()->first();
    }

    /**
     * 获取库存状态信息
     * 
     * @return array
     */
    public function getStockStatus()
    {
        $currentStock = $this->getTotalStock();
        $status = [
            'current_stock' => $currentStock,
            'inventory_policy' => $this->inventory_policy,
            'track_inventory' => $this->track_inventory,
            'inventory_type' => $this->inventory_type,
            'status' => 'normal',
            'alerts' => [],
            'active_alerts_count' => 0,
            'highest_severity' => null,
            'auto_reorder_rules_count' => 0,
            'pending_reorders_count' => 0,
        ];

        if (!$this->track_inventory) {
            $status['status'] = 'untracked';
            return $status;
        }

        if ($this->inventory_policy === 'unlimited') {
            $status['status'] = 'unlimited';
            return $status;
        }

        // 检查库存状态
        if ($currentStock < 0) {
            $status['status'] = 'negative';
            $status['alerts'][] = '库存为负数';
        } elseif ($this->min_stock_threshold && $currentStock <= $this->min_stock_threshold) {
            $status['status'] = 'low';
            $status['alerts'][] = '库存低于预警阈值';
        }

        // 检查自动补货
        if ($this->shouldAutoReorder($currentStock)) {
            $status['alerts'][] = '需要自动补货';
        }

        // 获取活跃预警信息
        $activeAlerts = $this->inventoryAlerts()->active()->get();
        $status['active_alerts_count'] = $activeAlerts->count();
        
        if ($activeAlerts->isNotEmpty()) {
            $highestSeverityAlert = $activeAlerts->sortBy(function($alert) {
                return match($alert->severity) {
                    'critical' => 1,
                    'high' => 2,
                    'medium' => 3,
                    'low' => 4,
                    default => 5
                };
            })->first();
            
            $status['highest_severity'] = $highestSeverityAlert->severity;
            
            // 如果有紧急或高级预警，更新状态
            if (in_array($highestSeverityAlert->severity, ['critical', 'high'])) {
                $status['status'] = 'critical';
            }
        }

        // 获取自动补货规则和待处理补货数量
        $status['auto_reorder_rules_count'] = $this->autoReorderRules()->active()->count();
        $status['pending_reorders_count'] = $this->reorderRecords()->pending()->count();

        return $status;
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        // 加载关联数据以避免N+1问题
        $this->load(['category', 'tags', 'baseUnit']);
        
        // 获取商品总库存
        $totalStock = $this->getTotalStock();
        
        // 构建可搜索数组
        $array = [
            'id' => $this->id,
            'code' => $this->code,
            'name' => $this->name,
            'description' => $this->description,
            'price' => $this->price,
            'sale_price' => $this->sale_price,
            'cost_price' => $this->cost_price,
            'status' => $this->status,
            'allow_sale' => $this->allow_sale,
            'sales_count' => $this->sales_count,
            'views_count' => $this->views_count,
            'is_featured' => $this->is_featured,
            'created_at' => $this->created_at ? $this->created_at->timestamp : null,
            'updated_at' => $this->updated_at ? $this->updated_at->timestamp : null,
            'total_stock' => $totalStock,
            'out_of_stock' => ($totalStock <= 0 && $this->track_inventory),
            'track_inventory' => $this->track_inventory,
            'category_id' => $this->category_id,
            'category_name' => $this->category ? $this->category->name : null,
            'unit_name' => $this->baseUnit ? $this->baseUnit->name : '件',
            'image_url' => $this->image_url ?? $this->cover_url ?? '',
            'tags' => $this->tags->pluck('name')->toArray(),
            'tag_ids' => $this->tags->pluck('id')->toArray(),
        ];
        
        // 添加拼音搜索支持
        try {
            if (class_exists('Overtrue\Pinyin\Pinyin')) {
                $pinyin = new \Overtrue\Pinyin\Pinyin();
                $array['pinyin'] = $pinyin->permalink($this->name);
                $array['pinyin_first'] = $pinyin->abbr($this->name);
            }
        } catch (\Exception $e) {
            // 忽略拼音处理错误
        }
        
        return $array;
    }

    /**
     * 确定模型是否应该被搜索
     *
     * @return bool
     */
    public function shouldBeSearchable()
    {
        // 只有上架且允许销售的商品才可搜索
        return $this->status == 1 && $this->allow_sale;
    }

    /**
     * 获取商品的基本单位
     */
    public function baseUnit()
    {
        return $this->belongsTo(Unit::class, 'base_unit_id');
    }

    /**
     * 获取商品的辅助单位
     */
    public function auxiliaryUnits()
    {
        return $this->belongsToMany(Unit::class, 'product_units')
                    ->withPivot([
                        'conversion_factor', 
                        'roles',
                        'role_priority',
                        'is_default',
                        'is_active'
                    ])
                    ->withTimestamps()
                    ->wherePivot('unit_id', '!=', $this->base_unit_id);
    }

    /**
     * 获取商品的所有单位(基本单位+辅助单位)
     */
    public function getAllUnits()
    {
        try {
            // 获取基本单位
            $baseUnit = null;
            if ($this->base_unit_id) {
                $baseUnit = Unit::find($this->base_unit_id);
                if ($baseUnit) {
                    $baseUnitArray = $baseUnit->toArray();
                    $baseUnitArray['is_base_unit'] = true;
                    $baseUnitArray['conversion_factor'] = 1.0;
                    $baseUnitArray['roles'] = ['base']; // 基础单位角色
                    $baseUnitArray['is_default'] = true;
                    $baseUnitArray['is_active'] = true;
                }
            }
            
            // 使用Eloquent模型获取辅助单位，确保正确应用casts配置
            $auxiliaryUnits = ProductUnit::with('unit')
                ->where('product_id', $this->id)
                ->get()
                ->map(function($productUnit) {
                    $unit = $productUnit->unit;
                    if (!$unit) {
                        return null; // 跳过没有关联单位的记录
                    }
                    
                    $unitArray = $unit->toArray();
                    $unitArray['is_base_unit'] = false;
                    
                    // 使用ProductUnit模型的属性，确保正确应用casts
                    $roles = $productUnit->roles ?: [];
                    $unitArray['roles'] = $roles;
                    $unitArray['conversion_factor'] = $productUnit->conversion_factor;
                    $unitArray['role_priority'] = $productUnit->role_priority ?: [];
                    $unitArray['is_default'] = $productUnit->is_default;
                    $unitArray['is_active'] = $productUnit->is_active;
                    
                    // 添加兼容性字段
                    $unitArray['is_sale_unit'] = in_array('sales', $roles);
                    $unitArray['is_purchase_unit'] = in_array('purchase', $roles);
                    $unitArray['is_inventory_unit'] = in_array('inventory', $roles);
                    $unitArray['conversion_rate'] = $productUnit->conversion_factor;
                    
                    return $unitArray;
                })
                ->filter() // 移除null值
                ->values()
                ->toArray();
            
            // 组合所有单位
            $allUnits = [];
            if (isset($baseUnitArray)) {
                $allUnits[] = $baseUnitArray;
            }
            
            return array_merge($allUnits, $auxiliaryUnits);
        } catch (\Exception $e) {
            Log::error('获取产品单位失败', [
                'product_id' => $this->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * 获取商品的所有单位
     */
    public function units()
    {
        return $this->belongsToMany(Unit::class, 'product_units')
                    ->withPivot([
                        'conversion_factor', 
                        'roles',
                        'role_priority',
                        'is_default',
                        'is_active'
                    ])
                    ->withTimestamps();
    }

    /**
     * 获取商品的销售默认单位
     */
    public function getSaleDefaultUnit()
    {
        try {
            // 优化后的查询方式 - 使用虚拟列（如果存在）或JSON查询
            $query = ProductUnit::where('product_id', $this->id)
                               ->where('is_active', true);
            
            // 如果存在虚拟列，使用虚拟列查询（性能更好）
            if (Schema::hasColumn('product_units', 'has_sales_role')) {
                $query->where('has_sales_role', 1);
            } else {
                // 降级使用JSON查询
                $query->whereJsonContains('roles', 'sales');
            }
            
            $productUnits = $query->orderBy('is_default', 'desc')->get();
            
            if ($productUnits->isEmpty()) {
                return null; // 没有销售单位时返回null，而不是基本单位
            }
            
            // 根据优先级排序
            $sortedUnits = $productUnits->sortBy(function($unit) {
                // 获取sales角色的优先级，优先级数字越小越优先
                $priority = $unit->role_priority['sales'] ?? 999;
                return $priority;
            });
            
            $highestPriorityUnit = $sortedUnits->first();
            if ($highestPriorityUnit) {
                return Unit::find($highestPriorityUnit->unit_id);
            }
            
            return null; // 找不到有效销售单位时返回null
        } catch (\Exception $e) {
            Log::error('getSaleDefaultUnit方法调用失败', [
                'product_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return null; // 出错时也返回null，不回退到基本单位
        }
    }

    /**
     * 获取商品的采购默认单位
     */
    public function getPurchaseDefaultUnit()
    {
        try {
            // 优化后的查询方式 - 使用虚拟列（如果存在）或JSON查询
            $query = ProductUnit::where('product_id', $this->id)
                               ->where('is_active', true);
            
            // 如果存在虚拟列，使用虚拟列查询（性能更好）
            if (Schema::hasColumn('product_units', 'has_purchase_role')) {
                $query->where('has_purchase_role', 1);
            } else {
                // 降级使用JSON查询
                $query->whereJsonContains('roles', 'purchase');
            }
            
            $productUnits = $query->orderBy('is_default', 'desc')->get();
            
            if ($productUnits->isEmpty()) {
                return $this->baseUnit;
            }
            
            // 根据优先级排序
            $sortedUnits = $productUnits->sortBy(function($unit) {
                // 获取purchase角色的优先级，优先级数字越小越优先
                $priority = $unit->role_priority['purchase'] ?? 999;
                return $priority;
            });
            
            $highestPriorityUnit = $sortedUnits->first();
            if ($highestPriorityUnit) {
                return Unit::find($highestPriorityUnit->unit_id);
            }
            
            return $this->baseUnit;
        } catch (\Exception $e) {
            Log::error('getPurchaseDefaultUnit方法调用失败', [
                'product_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return $this->baseUnit;
        }
    }

    /**
     * 获取商品的库存默认单位
     */
    public function getInventoryDefaultUnit()
    {
        try {
            // 优化后的查询方式 - 使用虚拟列（如果存在）或JSON查询
            $query = ProductUnit::where('product_id', $this->id)
                               ->where('is_active', true);
            
            // 如果存在虚拟列，使用虚拟列查询（性能更好）
            if (Schema::hasColumn('product_units', 'has_inventory_role')) {
                $query->where('has_inventory_role', 1);
            } else {
                // 降级使用JSON查询
                $query->whereJsonContains('roles', 'inventory');
            }
            
            $productUnits = $query->orderBy('is_default', 'desc')->get();
            
            if ($productUnits->isEmpty()) {
                return $this->baseUnit;
            }
            
            // 根据优先级排序
            $sortedUnits = $productUnits->sortBy(function($unit) {
                // 获取inventory角色的优先级，优先级数字越小越优先
                $priority = $unit->role_priority['inventory'] ?? 999;
                return $priority;
            });
            
            $highestPriorityUnit = $sortedUnits->first();
            if ($highestPriorityUnit) {
                return Unit::find($highestPriorityUnit->unit_id);
            }
            
            return $this->baseUnit;
        } catch (\Exception $e) {
            Log::error('getInventoryDefaultUnit方法调用失败', [
                'product_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return $this->baseUnit;
        }
    }

    /**
     * 检查单位ID是否为商品的有效单位
     * 
     * @param int $unitId 单位ID
     * @return bool 是否为有效单位
     */
    public function isValidUnit($unitId)
    {
        if ($this->base_unit_id == $unitId) {
            return true;
        }
        
        return $this->auxiliaryUnits()
                    ->where('units.id', $unitId)
                    ->exists();
    }

    /**
     * 检查商品是否可以上架销售
     * 必须有销售单位才能上架
     * 
     * @return array ['can_publish' => bool, 'message' => string, 'missing_requirements' => array]
     */
    public function canBePublishedForSale()
    {
        $missingRequirements = [];
        $messages = [];

        // 检查是否有销售单位
        $saleUnit = $this->getSaleDefaultUnit();
        if (!$saleUnit) {
            $missingRequirements[] = 'sale_unit';
            $messages[] = '商品必须设置销售单位才能上架';
        }

        // 检查基础信息完整性
        if (empty($this->name)) {
            $missingRequirements[] = 'name';
            $messages[] = '商品名称不能为空';
        }

        if (empty($this->price) || $this->price <= 0) {
            $missingRequirements[] = 'price';
            $messages[] = '商品价格必须大于0';
        }

        // 检查基础单位
        if (empty($this->base_unit_id)) {
            $missingRequirements[] = 'base_unit';
            $messages[] = '商品必须设置基础单位';
        }

        $canPublish = empty($missingRequirements);
        $message = $canPublish ? '商品满足上架条件' : implode('；', $messages);

        return [
            'can_publish' => $canPublish,
            'message' => $message,
            'missing_requirements' => $missingRequirements
        ];
    }

    /**
     * 强制检查商品上架条件（用于状态变更时）
     * 
     * @throws \Exception
     * @return bool
     */
    public function validateForPublishing()
    {
        $validation = $this->canBePublishedForSale();
        
        if (!$validation['can_publish']) {
            throw new \Exception($validation['message']);
        }
        
        return true;
    }

    /**
     * 获取单位间的转换率
     * 
     * @param int $fromUnitId 源单位ID
     * @param int $toUnitId 目标单位ID
     * @return float|null 转换率，null表示无法转换
     */
    public function getUnitConversionRate($fromUnitId, $toUnitId)
    {
        // 如果单位相同，转换率为1
        if ($fromUnitId == $toUnitId) {
            return 1;
        }
        
        // 首先检查产品特定的转换关系
        // 如果两个单位都是此产品的单位，使用product_units表中的转换因子
        // 首先找到产品的基本单位
        if ($this->base_unit_id == $fromUnitId) {
            $toUnitRelation = $this->auxiliaryUnits()
                ->where('units.id', $toUnitId)
                ->first();
                
            if ($toUnitRelation) {
                return 1.0 / $toUnitRelation->pivot->conversion_factor;
            }
        } 
        else if ($this->base_unit_id == $toUnitId) {
            $fromUnitRelation = $this->auxiliaryUnits()
                ->where('units.id', $fromUnitId)
                ->first();
                
            if ($fromUnitRelation) {
                return $fromUnitRelation->pivot->conversion_factor;
            }
        } 
        // 如果是两个辅助单位之间的转换
        else {
            $fromUnitRelation = $this->auxiliaryUnits()
                ->where('units.id', $fromUnitId)
                ->first();
                
            $toUnitRelation = $this->auxiliaryUnits()
                ->where('units.id', $toUnitId)
                ->first();
                
            if ($fromUnitRelation && $toUnitRelation) {
                $fromToBaseFactor = $fromUnitRelation->pivot->conversion_factor;
                $baseToToFactor = 1 / $toUnitRelation->pivot->conversion_factor;
                return $fromToBaseFactor * $baseToToFactor;
            }
        }
        
        // 如果没有产品特定的转换关系，使用图论转换系统
        $fromUnit = \App\Unit\Models\Unit::find($fromUnitId);
        $toUnit = \App\Unit\Models\Unit::find($toUnitId);
        
        if (!$fromUnit || !$toUnit || $fromUnit->type !== $toUnit->type) {
            return null;
        }
        
        // 获取默认转换图
        $graph = \App\Unit\Models\UnitConversionGraph::where('type', $fromUnit->type)
                                                  ->where('is_default', true)
                                                  ->first();
        
        if (!$graph) {
            return null;
        }
        
        try {
            // 使用图论转换服务进行转换
            return app(\App\Unit\Services\UnitService::class)
                 ->convertUsingGraph(1, $fromUnit, $toUnit, $graph);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('单位转换错误', [
                'message' => $e->getMessage(),
                'from_unit' => $fromUnitId,
                'to_unit' => $toUnitId
            ]);
            return null;
        }
    }

    /**
     * 在指定单位下获取库存数量
     * 
     * @param int $unitId 单位ID
     * @return float|null 单位下的库存数量，null表示无法转换
     */
    public function getStockInSpecificUnit($unitId)
    {
        // 获取基本单位的库存（从所有库存记录计算总和）
        $baseStock = $this->getTotalStock();
        
        // 如果请求的是基本单位，直接返回
        if ($unitId == $this->base_unit_id) {
            return $baseStock;
        }
        
        // 获取从基本单位到请求单位的转换率
        $conversionFactor = $this->getUnitConversionRate($this->base_unit_id, $unitId);
        
        if ($conversionFactor === null) {
            return null; // 无法转换
        }
        
        // 转换库存
        return $baseStock * $conversionFactor;
    }

    /**
     * 设置商品基本单位
     * 
     * @param int $unitId 单位ID
     * @return bool 设置是否成功
     */
    public function setBaseUnit($unitId)
    {
        try {
            // 检查单位是否存在
            if (!Unit::where('id', $unitId)->exists()) {
                return false;
            }
            
            $this->base_unit_id = $unitId;
            return $this->save();
        } catch (\Exception $e) {
            Log::error('setBaseUnit方法调用失败', [
                'product_id' => $this->id,
                'unit_id' => $unitId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 添加辅助单位
     * 
     * @param int $unitId 单位ID
     * @param float $conversionFactor 转换因子
     * @param array $options 选项数组
     * @return bool 添加是否成功
     */
    public function addAuxiliaryUnit($unitId, $conversionFactor, $options = [])
    {
        try {
            // 检查单位是否存在
            if (!Unit::where('id', $unitId)->exists() || $unitId == $this->base_unit_id) {
                return false;
            }
            
            // 检查是否已存在此辅助单位
            $existingUnit = DB::table('product_units')
                ->where('product_id', $this->id)
                ->where('unit_id', $unitId)
                ->exists();
                
            if ($existingUnit) {
                return false;
            }
            
            // 添加辅助单位关联
            DB::table('product_units')->insert([
                'product_id' => $this->id,
                'unit_id' => $unitId,
                'conversion_factor' => $conversionFactor,
                'is_sale_unit' => $options['is_sale_unit'] ?? false,
                'is_purchase_unit' => $options['is_purchase_unit'] ?? false,
                'is_inventory_unit' => $options['is_inventory_unit'] ?? false,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('addAuxiliaryUnit方法调用失败', [
                'product_id' => $this->id,
                'unit_id' => $unitId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 更新商品总库存（此方法已废弃，保留以兼容性，但不再更新products.stock字段）
     * @deprecated 使用 getTotalStock() 方法直接计算总库存
     */
    public function updateTotalStock()
    {
        // 直接返回计算的总库存，不再保存到products.stock字段
        return $this->getTotalStock();
    }

    /**
     * 转换商品数量到另一个单位
     * 
     * @param float $quantity 数量
     * @param int $fromUnitId 源单位ID
     * @param int $toUnitId 目标单位ID
     * @return float|null 转换后的数量，null表示无法转换
     */
    public function convertQuantity($quantity, $fromUnitId, $toUnitId)
    {
        $conversionFactor = $this->getUnitConversionRate($fromUnitId, $toUnitId);
        if ($conversionFactor === null) {
            return null;
        }
        
        return $quantity * $conversionFactor;
    }

    /**
     * 商品的所有图片
     */
    public function images()
    {
        return $this->hasMany(ProductImage::class)->orderBy('sort', 'asc');
    }
    
    /**
     * 商品的主图
     */
    public function mainImage()
    {
        return $this->hasOne(ProductImage::class)->where('is_main', true);
    }

    /**
     * 商品在各区域的价格
     */
    public function regionPrices()
    {
        return $this->hasMany(RegionPrice::class);
    }
    
    /**
     * 获取商品图片的数组形式
     * 
     * @return array
     */
    public function getImagesAttribute()
    {
        return $this->images()->get()->toArray();
    }

    /**
     * 计算商品的最终价格
     * 使用统一的价格计算服务，避免重复逻辑
     *
     * @param User|null $user 用户（用于获取会员等级）
     * @param int|null $regionId 区域ID
     * @param int $quantity 数量
     * @return array 价格信息
     */
    public function calculatePrice($user = null, $regionId = null, $quantity = 1)
    {
        // 使用统一的价格计算服务
        $priceService = app(\App\Product\Services\PriceCalculationService::class);
        return $priceService->calculatePrice($this, $user, $regionId, $quantity);
    }

    /**
     * 获取商品的价格展示信息
     * 用于前端显示价格
     *
     * @param User|null $user 用户
     * @param int|null $regionId 区域ID
     * @return array 价格展示信息
     */
    public function getPriceDisplay($user = null, $regionId = null)
    {
        $priceInfo = $this->calculatePrice($user, $regionId);

        $display = [
            'product_id' => $this->id,
            'base_price' => $priceInfo['base_price'],
            'current_price' => $priceInfo['final_price'],
            'original_price' => $priceInfo['original_price'] ?? $priceInfo['base_price'],
            'has_discount' => $priceInfo['total_discount'] > 0,
            'discount_amount' => $priceInfo['total_discount'],
            'price_type' => $priceInfo['price_type'],
            'price_labels' => []
        ];

        // 生成价格标签
        foreach ($priceInfo['discount_info'] as $discount) {
            switch ($discount['type']) {
                case 'region_price':
                    $display['price_labels'][] = [
                        'type' => 'region',
                        'text' => '区域价',
                        'color' => 'blue'
                    ];
                    break;
                case 'product_member_discount':
                case 'category_member_discount':
                case 'global_member_discount':
                    $display['price_labels'][] = [
                        'type' => 'member',
                        'text' => $user?->membershipLevel?->name ?? '会员价',
                        'color' => 'gold'
                    ];
                    break;
                case 'category_region_discount':
                    $display['price_labels'][] = [
                        'type' => 'category_region',
                        'text' => '分类优惠',
                        'color' => 'green'
                    ];
                    break;
            }
        }

        return $display;
    }

    /**
     * 检查商品在指定区域和数量下是否可购买
     *
     * @param int $quantity 数量
     * @param int|null $regionId 区域ID
     * @return array 检查结果
     */
    public function checkAvailability($quantity = 1, $regionId = null)
    {
        $result = [
            'available' => true,
            'message' => '',
            'stock_info' => []
        ];

        // 检查基础库存
        if (!$this->checkStock($quantity)) {
            $result['available'] = false;
            $result['message'] = "商品 {$this->name} 库存不足";
            $result['stock_info']['base_stock'] = $this->stock;
            $result['stock_info']['required'] = $quantity;
            return $result;
        }

        // 检查区域库存（如果有区域价格设置）
        if ($regionId) {
            $regionPrice = $this->getRegionPrice($regionId);
            if ($regionPrice && $regionPrice->stock !== null) {
                if ($regionPrice->stock < $quantity) {
                    $result['available'] = false;
                    $result['message'] = "商品 {$this->name} 在当前区域的价格库存限额不足";
                    $result['stock_info']['region_stock'] = $regionPrice->stock;
                    $result['stock_info']['required'] = $quantity;
                    return $result;
                }
                $result['stock_info']['region_stock'] = $regionPrice->stock;
            }
        }

        $result['stock_info']['base_stock'] = $this->getTotalStock();
        return $result;
    }

    /**
     * 获取商品在指定条件下的完整信息
     * 包含价格、库存、可用性等
     *
     * @param User|null $user 用户
     * @param int|null $regionId 区域ID
     * @param int $quantity 数量
     * @return array 完整商品信息
     */
    public function getFullInfo($user = null, $regionId = null, $quantity = 1)
    {
        $priceInfo = $this->calculatePrice($user, $regionId, $quantity);
        $priceDisplay = $this->getPriceDisplay($user, $regionId);
        $availability = $this->checkAvailability($quantity, $regionId);

        return [
            'product' => [
                'id' => $this->id,
                'name' => $this->name,
                'sku' => $this->sku,
                'description' => $this->description,
                'category_id' => $this->category_id,
                'brand_id' => $this->brand_id,
                'status' => $this->status,
                'images' => $this->images,
                'cover' => $this->cover,
            ],
            'pricing' => $priceInfo,
            'display' => $priceDisplay,
            'availability' => $availability,
            'context' => [
                'user_id' => $user ? $user->id : null,
                'region_id' => $regionId,
                'quantity' => $quantity,
                'calculated_at' => now()->toISOString()
            ]
        ];
    }

    /**
     * 保存商品图片
     * 
     * @param array $uploadResults 上传结果数组
     * @param bool $setFirstAsMain 是否将第一张图片设为主图
     * @return array 保存的图片模型数组
     */
    public function saveImages(array $uploadResults, bool $setFirstAsMain = true)
    {
        $savedImages = [];
        $existingImageMap = [];
        $processedIds = [];
        
        // 获取商品当前的所有图片，建立ID映射
        $currentImages = $this->images()->get();
        foreach ($currentImages as $image) {
            $existingImageMap[$image->id] = $image;
        }
        
        $hasMainImage = $currentImages->where('is_main', true)->count() > 0;
        
        Log::info('开始保存商品图片', [
            'product_id' => $this->id,
            'existing_images_count' => count($existingImageMap),
            'has_main_image' => $hasMainImage,
            'upload_results_count' => count($uploadResults)
        ]);
        
        // 第一步：处理已存在的图片（更新排序和主图标记）
        $mainImageSet = false;
        
        foreach ($uploadResults as $index => $result) {
            // 检查是否是已存在的图片
            if (isset($result['id']) && !empty($result['id']) && isset($existingImageMap[$result['id']])) {
                $existingImage = $existingImageMap[$result['id']];
                
                // 检查图片是否标记为删除
                if (isset($result['deleted']) && $result['deleted'] === true) {
                    // 删除图片
                    try {
                        Log::info('删除商品图片', [
                            'product_id' => $this->id,
                            'image_id' => $existingImage->id,
                            'url' => $existingImage->url
                        ]);
                        $existingImage->delete();
                    } catch (\Exception $e) {
                        Log::error('删除商品图片失败', [
                            'product_id' => $this->id,
                            'image_id' => $existingImage->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                    continue;
                }
                
                $isMain = isset($result['is_main']) && $result['is_main'];
                
                // 更新排序和主图标记
                $existingImage->sort = $index;
                
                // 如果有图片明确标记为主图，使用该标记
                if ($isMain) {
                    $mainImageSet = true;
                }
                
                $existingImage->is_main = $isMain;
                $existingImage->save();
                
                $savedImages[] = $existingImage;
                $processedIds[] = $existingImage->id;
                
                Log::info('更新已存在图片', [
                    'product_id' => $this->id,
                    'image_id' => $existingImage->id,
                    'sort' => $index,
                    'is_main' => $isMain
                ]);
            }
        }
        
        // 第二步：处理新上传的图片
        $existingUrls = $currentImages->pluck('url')->toArray();
        $nextSort = count($processedIds);
        
        foreach ($uploadResults as $index => $result) {
            // 跳过已处理的图片、标记为删除的图片和上传失败的图片
            if ((isset($result['id']) && in_array($result['id'], $processedIds)) || 
                (isset($result['deleted']) && $result['deleted'] === true) ||
                !isset($result['is_success']) || !$result['is_success']) {
                continue;
            }
            
            // 检查URL是否已存在，避免重复
            if (isset($result['url']) && in_array($result['url'], $existingUrls)) {
                Log::info('图片URL已存在，跳过处理', [
                    'product_id' => $this->id,
                    'url' => $result['url']
                ]);
                continue;
            }
            
            // 确定是否设置为主图
            $isMain = false;
            if (isset($result['is_main']) && $result['is_main'] && !$mainImageSet) {
                $isMain = true;
                $mainImageSet = true;
            } else if (!$hasMainImage && !$mainImageSet && $index === 0 && $setFirstAsMain) {
                $isMain = true;
                $mainImageSet = true;
            }
            
            $imageData = [
                'url' => $result['url'],
                'path' => $result['path'] ?? '',
                'driver' => $result['driver'] ?? null,
                'original_name' => $result['original_name'] ?? null,
                'size' => $result['size'] ?? null,
                'mime_type' => $result['mime_type'] ?? null,
                'is_main' => $isMain,
                'sort' => $nextSort++,
                'status' => true
            ];
            
            Log::info('保存新上传图片', [
                'product_id' => $this->id,
                'url' => $result['url'],
                'is_main' => $isMain,
                'sort' => $imageData['sort']
            ]);
            
            $productImage = ProductImage::createOrUpdateFromUpload($this->id, $imageData);
            
            if ($isMain) {
                $productImage->setAsMain();
            }
            
            $savedImages[] = $productImage;
            $existingUrls[] = $result['url'];
        }
        
        // 如果还是没有主图但有图片，将第一张图片设为主图
        if (!$mainImageSet && count($savedImages) > 0 && $setFirstAsMain) {
            $firstImage = $savedImages[0];
            $firstImage->is_main = true;
            $firstImage->save();
            $firstImage->setAsMain();
            
            Log::info('没有主图，将第一张图片设为主图', [
                'product_id' => $this->id,
                'image_id' => $firstImage->id
            ]);
        }
        
        // 第三步：删除没有在上传结果中出现的现有图片（清理遗留图片）
        $uploadedImageIds = array_filter(array_column($uploadResults, 'id'));
        $uploadedUrls = array_filter(array_column($uploadResults, 'url'));
        
        foreach ($existingImageMap as $imageId => $image) {
            // 检查ID是否在处理过的列表中或上传结果ID列表中
            // 或者URL是否在上传结果URL列表中（适用于刚刚上传的图片，可能没有ID）
            if (!in_array($imageId, $processedIds) && 
                !in_array($imageId, $uploadedImageIds) && 
                !in_array($image->url, $uploadedUrls)) {
                try {
                    Log::info('删除遗留商品图片', [
                        'product_id' => $this->id,
                        'image_id' => $image->id,
                        'url' => $image->url
                    ]);
                    $image->delete();
                } catch (\Exception $e) {
                    Log::error('删除遗留商品图片失败', [
                        'product_id' => $this->id,
                        'image_id' => $image->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }
        
        return $savedImages;
    }

    /**
     * 获取商品主图URL
     * 
     * @return string
     */
    public function getCoverUrlAttribute()
    {
        // 首先查找标记为主图的图片
        $mainImage = $this->mainImage;
        
        if ($mainImage) {
            return $mainImage->url;
        }
        
        // 如果没有主图，但是有任意图片，则使用第一张图片
        if ($this->images()->exists()) {
            return $this->images()->orderBy('sort', 'asc')->first()->url;
        }
        
        // 如果还是找不到，查看是否有老的cover_url字段（向后兼容）
        if (Schema::hasColumn('products', 'cover_url') && !empty($this->getAttributes()['cover_url'])) {
            return $this->getAttributes()['cover_url'];
        }
        
        // 最后返回默认图片
        return config('app.default_product_image', '/images/default-product.png');
    }

    /**
     * 标签关联关系
     */
    public function tags()
    {
        return $this->belongsToMany(ProductTag::class, 'product_tag_relations', 'product_id', 'tag_id')
                    ->withTimestamps();
    }

    /**
     * 获取标签缓存
     */
    public function getTagsCacheAttribute($value)
    {
        if ($value) {
            return json_decode($value, true);
        }
        
        // 如果缓存为空，从关联关系获取
        return $this->tags->map(function ($tag) {
            return $tag->toDisplayArray();
        })->toArray();
    }

    /**
     * 获取标签ID数组
     */
    public function getTagIdsAttribute($value)
    {
        if ($value) {
            return json_decode($value, true);
        }
        
        // 如果为空，从关联关系获取
        return $this->tags->pluck('id')->toArray();
    }

    /**
     * 同步标签并更新缓存
     */
    public function syncTags(array $tagIds)
    {
        // 同步关联关系
        $this->tags()->sync($tagIds);
        
        // 更新缓存字段
        $this->updateTagsCache();
        
        return $this;
    }

    /**
     * 添加标签
     */
    public function attachTags(array $tagIds)
    {
        $this->tags()->attach($tagIds);
        $this->updateTagsCache();
        
        return $this;
    }

    /**
     * 移除标签
     */
    public function detachTags(array $tagIds)
    {
        $this->tags()->detach($tagIds);
        $this->updateTagsCache();
        
        return $this;
    }

    /**
     * 更新标签缓存
     */
    public function updateTagsCache()
    {
        $tags = $this->tags()->active()->ordered()->get();
        
        $this->update([
            'tag_ids' => $tags->pluck('id')->toArray(),
            'tags_cache' => $tags->map(function ($tag) {
                return $tag->toDisplayArray();
            })->toArray()
        ]);
        
        return $this;
    }

    /**
     * 检查是否有指定标签
     */
    public function hasTag($tagId)
    {
        $tagIds = $this->tag_ids ?? [];
        return in_array($tagId, $tagIds);
    }

    /**
     * 检查是否有任意指定标签
     */
    public function hasAnyTag(array $tagIds)
    {
        $productTagIds = $this->tag_ids ?? [];
        return !empty(array_intersect($productTagIds, $tagIds));
    }

    /**
     * 作用域：按标签筛选
     */
    public function scopeWithTags($query, array $tagIds)
    {
        return $query->where(function ($q) use ($tagIds) {
            foreach ($tagIds as $tagId) {
                $q->orWhereJsonContains('tag_ids', $tagId);
            }
        });
    }

    /**
     * 作用域：按标签名称筛选
     */
    public function scopeWithTagNames($query, array $tagNames)
    {
        return $query->whereHas('tags', function ($q) use ($tagNames) {
            $q->whereIn('name', $tagNames);
        });
    }

    // ==================== 积分奖励相关方法 ====================

    /**
     * 积分奖励类型常量
     */
    const POINTS_REWARD_TYPE_FIXED = 'fixed';   // 固定积分
    const POINTS_REWARD_TYPE_RATE = 'rate';     // 按比例
    const POINTS_REWARD_TYPE_NONE = 'none';     // 无奖励

    /**
     * 计算商品购买积分奖励
     *
     * @param float $orderAmount 订单金额
     * @param int $quantity 购买数量
     * @return int 积分奖励数量
     */
    public function calculatePointsReward($orderAmount, $quantity = 1)
    {
        // 检查是否启用积分奖励
        if (!$this->points_reward_enabled) {
            return 0;
        }

        // 检查最小金额要求
        if ($this->points_min_amount > 0 && $orderAmount < $this->points_min_amount) {
            return 0;
        }

        $points = 0;

        switch ($this->points_reward_type) {
            case self::POINTS_REWARD_TYPE_FIXED:
                // 固定积分 * 数量
                $points = $this->points_reward_fixed * $quantity;
                break;

            case self::POINTS_REWARD_TYPE_RATE:
                // 按金额比例计算
                $points = (int) floor($orderAmount * $this->points_reward_rate);
                break;

            case self::POINTS_REWARD_TYPE_NONE:
            default:
                $points = 0;
                break;
        }

        // 应用最大积分限制
        if ($this->points_reward_max > 0 && $points > $this->points_reward_max) {
            $points = $this->points_reward_max;
        }

        return max(0, $points);
    }

    /**
     * 获取积分奖励说明文本
     *
     * @return string
     */
    public function getPointsRewardDescription()
    {
        if (!$this->points_reward_enabled) {
            return '暂无积分奖励';
        }

        if (!empty($this->points_reward_desc)) {
            return $this->points_reward_desc;
        }

        switch ($this->points_reward_type) {
            case self::POINTS_REWARD_TYPE_FIXED:
                $desc = "购买获得 {$this->points_reward_fixed} 积分";
                if ($this->points_reward_max > 0) {
                    $desc .= "（最多 {$this->points_reward_max} 积分）";
                }
                break;

            case self::POINTS_REWARD_TYPE_RATE:
                $rate = $this->points_reward_rate * 100;
                $desc = "按订单金额 {$rate}% 获得积分";
                if ($this->points_reward_max > 0) {
                    $desc .= "（最多 {$this->points_reward_max} 积分）";
                }
                break;

            case self::POINTS_REWARD_TYPE_NONE:
            default:
                $desc = '暂无积分奖励';
                break;
        }

        if ($this->points_min_amount > 0) {
            $desc .= "（订单满 {$this->points_min_amount} 元）";
        }

        return $desc;
    }

    /**
     * 检查是否有积分奖励
     *
     * @return bool
     */
    public function hasPointsReward()
    {
        return $this->points_reward_enabled && 
               $this->points_reward_type !== self::POINTS_REWARD_TYPE_NONE &&
               ($this->points_reward_fixed > 0 || $this->points_reward_rate > 0);
    }

    /**
     * 获取积分奖励配置信息
     *
     * @return array
     */
    public function getPointsRewardConfig()
    {
        return [
            'enabled' => $this->points_reward_enabled,
            'type' => $this->points_reward_type,
            'fixed_points' => $this->points_reward_fixed,
            'rate' => $this->points_reward_rate,
            'max_points' => $this->points_reward_max,
            'min_amount' => $this->points_min_amount,
            'description' => $this->getPointsRewardDescription(),
            'has_reward' => $this->hasPointsReward(),
        ];
    }

    /**
     * 设置固定积分奖励
     *
     * @param int $points 固定积分数
     * @param int|null $maxPoints 最大积分限制
     * @param float $minAmount 最小金额要求
     * @return $this
     */
    public function setFixedPointsReward($points, $maxPoints = null, $minAmount = 0)
    {
        $this->points_reward_type = self::POINTS_REWARD_TYPE_FIXED;
        $this->points_reward_fixed = $points;
        $this->points_reward_max = $maxPoints;
        $this->points_min_amount = $minAmount;
        $this->points_reward_enabled = $points > 0;

        return $this;
    }

    /**
     * 设置比例积分奖励
     *
     * @param float $rate 积分比例（如0.01表示1%）
     * @param int|null $maxPoints 最大积分限制
     * @param float $minAmount 最小金额要求
     * @return $this
     */
    public function setRatePointsReward($rate, $maxPoints = null, $minAmount = 0)
    {
        $this->points_reward_type = self::POINTS_REWARD_TYPE_RATE;
        $this->points_reward_rate = $rate;
        $this->points_reward_max = $maxPoints;
        $this->points_min_amount = $minAmount;
        $this->points_reward_enabled = $rate > 0;

        return $this;
    }

    /**
     * 禁用积分奖励
     *
     * @return $this
     */
    public function disablePointsReward()
    {
        $this->points_reward_enabled = false;
        $this->points_reward_type = self::POINTS_REWARD_TYPE_NONE;

        return $this;
    }

    /**
     * 查询有积分奖励的商品
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithPointsReward($query)
    {
        return $query->where('points_reward_enabled', true)
                    ->where('points_reward_type', '!=', self::POINTS_REWARD_TYPE_NONE)
                    ->where(function ($q) {
                        $q->where('points_reward_fixed', '>', 0)
                          ->orWhere('points_reward_rate', '>', 0);
                    });
    }

    /**
     * 查询指定积分奖励类型的商品
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByPointsRewardType($query, $type)
    {
        return $query->where('points_reward_type', $type);
    }

    /**
     * 监控库存查询性能并提供优化建议
     * 
     * @param callable $callback 需要监控的回调函数
     * @param string $operation 操作名称
     * @return mixed 回调函数的返回值
     */
    public static function monitorStockPerformance(callable $callback, string $operation = 'stock_operation')
    {
        $startTime = microtime(true);
        $startQueries = \Illuminate\Support\Facades\DB::getQueryLog();
        
        // 启用查询日志
        \Illuminate\Support\Facades\DB::enableQueryLog();
        
        $result = $callback();
        
        $endTime = microtime(true);
        $executedQueries = \Illuminate\Support\Facades\DB::getQueryLog();
        $queryCount = count($executedQueries) - count($startQueries);
        $executionTime = ($endTime - $startTime) * 1000; // 转换为毫秒
        
        // 性能分析
        $performanceData = [
            'operation' => $operation,
            'execution_time_ms' => round($executionTime, 2),
            'query_count' => $queryCount,
            'average_query_time_ms' => $queryCount > 0 ? round($executionTime / $queryCount, 2) : 0,
        ];
        
        // 性能建议
        $suggestions = [];
        if ($queryCount > 10) {
            $suggestions[] = '查询次数过多，建议使用批量查询 getBatchTotalStock()';
        }
        if ($executionTime > 1000) {
            $suggestions[] = '执行时间过长，建议启用缓存';
        }
        if ($queryCount > 0 && $executionTime / $queryCount > 100) {
            $suggestions[] = '单次查询时间过长，检查数据库索引';
        }
        
        $performanceData['suggestions'] = $suggestions;
        
        // 记录性能日志
        if (!empty($suggestions)) {
            \Illuminate\Support\Facades\Log::warning('库存查询性能警告', $performanceData);
        } else {
            \Illuminate\Support\Facades\Log::info('库存查询性能正常', $performanceData);
        }
        
        return $result;
    }
    
    /**
     * 获取库存缓存统计信息
     * 
     * @return array 缓存统计
     */
    public static function getStockCacheStats()
    {
        $cache = \Illuminate\Support\Facades\Cache::getStore();
        $stats = [
            'cache_type' => get_class($cache),
            'single_cache_count' => 0,
            'batch_cache_count' => 0,
            'cache_hit_rate' => 'N/A'
        ];
        
        // 如果是Redis缓存，可以获取更详细的统计
        if (config('cache.default') === 'redis') {
            try {
                /** @var \Illuminate\Redis\RedisManager $redis */
                $redis = app('redis');
                $keys = $redis->keys('*product_stock:*');
                $stats['single_cache_count'] = count($keys);
                
                $batchKeys = $redis->keys('*batch_stock:*');
                $stats['batch_cache_count'] = count($batchKeys);
            } catch (\Exception $e) {
                $stats['error'] = '无法获取Redis统计信息: ' . $e->getMessage();
            }
        }
        
        return $stats;
    }
} 