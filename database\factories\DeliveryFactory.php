<?php

namespace Database\Factories;

use App\Order\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Delivery\Models\Delivery>
 */
class DeliveryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'order_id' => Order::factory()->paid(),
            'status' => $this->faker->randomElement(['pending', 'in_progress', 'completed']),
            'deliverer_id' => User::factory()->merchant(),
        ];
    }
    
    /**
     * 设置配送状态为待处理
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }
    
    /**
     * 设置配送状态为进行中
     */
    public function inProgress(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'in_progress',
        ]);
    }
    
    /**
     * 设置配送状态为已完成
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
        ]);
    }
    
    /**
     * 设置没有配送员
     */
    public function withoutDeliverer(): static
    {
        return $this->state(fn (array $attributes) => [
            'deliverer_id' => null,
        ]);
    }
} 