<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WechatServiceProvider extends Model
{
    use HasFactory;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'wechat_service_provider';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'mch_id',
        'appid',
        'api_key',
        'api_v3_key',
        'cert_path',
        'key_path',
        'notify_url',
        'refund_notify_url',
        'is_sandbox',
        'is_active',
    ];

    /**
     * 类型转换
     *
     * @var array
     */
    protected $casts = [
        'is_sandbox' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * 隐藏的属性
     *
     * @var array
     */
    protected $hidden = [
        'api_key',
        'api_v3_key',
        'cert_path',
        'key_path',
    ];

    /**
     * 获取该服务商下的所有子商户
     */
    public function subMerchants(): HasMany
    {
        return $this->hasMany(WechatSubMerchant::class, 'provider_id');
    }

    /**
     * 获取该服务商下的所有支付记录
     */
    public function payments(): HasMany
    {
        return $this->hasMany(WechatServicePayment::class, 'provider_id');
    }
} 