# CRM系统API文档

## 概述

本文档描述了CRM系统的API接口。系统支持CRM专员管理、客户分配以及客户跟进记录等功能。所有API除特别说明外，均需要认证才能访问。

## 认证方式

系统使用基于令牌的认证。调用API时需要在请求头中包含`Authorization`字段：

```
Authorization: Bearer {token}
```

## 通用响应格式

所有API响应均使用以下格式：

```json
{
  "code": 200,           // 状态码，200表示成功，其他值表示错误
  "message": "操作成功",   // 状态描述
  "data": { ... }        // 返回的数据，错误时可能为null
}
```

## API端点

### 1. CRM专员管理 

#### 1.1 获取CRM专员列表

**接口地址**：`GET /api/crm-agents`

**权限要求**：admin或manager

**查询参数**：

| 参数名      | 类型    | 必须 | 描述           |
|------------|---------|-----|--------------|
| keyword    | string  | 否   | 搜索关键词     |
| status     | string  | 否   | 状态筛选       |
| specialty  | string  | 否   | 专长领域筛选    |
| per_page   | integer | 否   | 每页数量，默认10 |

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "user_id": 10,
        "service_area": "华南区",
        "max_clients": 50,
        "performance_rating": "4.50",
        "status": "available",
        "specialty": "大客户关系维护",
        "monthly_target": 50000,
        "clients_count": 12,
        "created_at": "2023-07-01T10:00:00.000000Z",
        "updated_at": "2023-07-01T10:00:00.000000Z",
        "deleted_at": null,
        "user": {
          "id": 10,
          "name": "李明",
          "phone": "13800138000",
          "role": "crm_agent",
          "joined_at": "2023-06-01T00:00:00.000000Z"
        }
      }
    ],
    "first_page_url": "...",
    "from": 1,
    "last_page": 1,
    "last_page_url": "...",
    "links": [ ... ],
    "next_page_url": null,
    "path": "...",
    "per_page": 10,
    "prev_page_url": null,
    "to": 1,
    "total": 1
  }
}
```

#### 1.2 创建CRM专员

**接口地址**：`POST /api/crm-agents`

**权限要求**：admin

**请求参数**：

| 参数名           | 类型    | 必须  | 描述                   |
|-----------------|---------|------|------------------------|
| name            | string  | 是    | 专员姓名                |
| phone           | string  | 否    | 手机号码 (唯一)          |
| password        | string  | 是    | 登录密码 (最少8位)       |
| service_area    | string  | 否    | 服务区域                |
| max_clients     | integer | 否    | 最大客户数量，默认50      |
| specialty       | string  | 否    | 专长领域                |
| monthly_target  | integer | 否    | 月度目标金额            |

**请求示例**：

```json
{
  "name": "张三",
  "phone": "13900000001",
  "password": "password123",
  "service_area": "华东区",
  "max_clients": 60,
  "specialty": "新客户开发",
  "monthly_target": 60000
}
```

**响应示例**：

```json
{
  "code": 200,
  "message": "CRM专员创建成功",
  "data": {
    "id": 2,
    "user_id": 11,
    "service_area": "华东区",
    "max_clients": 60,
    "performance_rating": "5.00",
    "status": "available",
    "specialty": "新客户开发",
    "monthly_target": 60000,
    "clients_count": 0,
    "created_at": "2023-07-05T14:25:00.000000Z",
    "updated_at": "2023-07-05T14:25:00.000000Z",
    "deleted_at": null,
    "user": {
      "id": 11,
      "name": "张三",
      "phone": "13900000001",
      "role": "crm_agent",
      "joined_at": "2023-07-05T14:25:00.000000Z"
    }
  }
}
```

#### 1.3 获取CRM专员详情

**接口地址**：`GET /api/crm-agents/{id}`

**权限要求**：admin、manager或专员本人

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "user_id": 10,
    "service_area": "华南区",
    "max_clients": 50,
    "performance_rating": "4.50",
    "status": "available",
    "specialty": "大客户关系维护",
    "monthly_target": 50000,
    "clients_count": 12,
    "created_at": "2023-07-01T10:00:00.000000Z",
    "updated_at": "2023-07-01T10:00:00.000000Z",
    "deleted_at": null,
    "user": {
      "id": 10,
      "name": "李明",
      "phone": "13800138000",
      "role": "crm_agent",
      "joined_at": "2023-06-01T00:00:00.000000Z"
    },
    "clients": [
      // 客户列表...
    ]
  }
}
```

#### 1.4 更新CRM专员信息

**接口地址**：`PUT /api/crm-agents/{id}`

**权限要求**：admin或专员本人

**请求参数**：

| 参数名           | 类型    | 必须  | 描述                   |
|-----------------|---------|------|------------------------|
| service_area    | string  | 否    | 服务区域                |
| max_clients     | integer | 否    | 最大客户数量            |
| specialty       | string  | 否    | 专长领域                |
| monthly_target  | integer | 否    | 月度目标金额            |
| status          | string  | 否    | 状态(available/busy/offline) |

**响应示例**：

```json
{
  "code": 200,
  "message": "CRM专员信息更新成功",
  "data": {
    // 专员信息...
  }
}
```

#### 1.5 删除CRM专员

**接口地址**：`DELETE /api/crm-agents/{id}`

**权限要求**：admin

**响应示例**：

```json
{
  "code": 200,
  "message": "CRM专员删除成功",
  "data": null
}
```

#### 1.6 获取CRM专员的客户列表

**接口地址**：`GET /api/crm-agents/{id}/clients`

**权限要求**：admin、manager或专员本人

**查询参数**：

| 参数名      | 类型    | 必须  | 描述                   |
|------------|---------|------|------------------------|
| status     | string  | 否    | 状态筛选(active/inactive) |
| per_page   | integer | 否    | 每页数量，默认10         |

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 20,
        "name": "王五",
        "phone": "13700000000",
        "role": "customer",
        "joined_at": "2023-05-01T00:00:00.000000Z",
        "pivot": {
          "agent_id": 1,
          "user_id": 20,
          "assigned_at": "2023-07-02T09:30:00.000000Z",
          "status": "active",
          "notes": "重要客户，每月跟进"
        }
      }
    ],
    // 分页信息...
  }
}
```

#### 1.7 更新CRM专员状态

**接口地址**：`PUT /api/crm-agents/{id}/status`

**权限要求**：admin、manager或专员本人

**请求参数**：

| 参数名  | 类型    | 必须  | 描述                   |
|--------|---------|------|------------------------|
| status | string  | 是    | 状态(available/busy/offline) |

**响应示例**：

```json
{
  "code": 200,
  "message": "CRM专员状态更新成功",
  "data": {
    // 专员信息...
  }
}
```

### 2. 客户分配管理

#### 2.1 分配客户给CRM专员

**接口地址**：`POST /api/client-assignments/assign`

**权限要求**：admin或manager

**请求参数**：

| 参数名     | 类型    | 必须  | 描述                   |
|-----------|---------|------|------------------------|
| user_id   | integer | 是    | 客户ID                  |
| agent_id  | integer | 是    | CRM专员ID               |
| notes     | string  | 否    | 分配备注                |

**响应示例**：

```json
{
  "code": 200,
  "message": "客户分配成功",
  "data": null
}
```

#### 2.2 批量分配客户

**接口地址**：`POST /api/client-assignments/batch-assign`

**权限要求**：admin或manager

**请求参数**：

| 参数名     | 类型    | 必须  | 描述                   |
|-----------|---------|------|------------------------|
| user_ids  | array   | 是    | 客户ID数组              |
| agent_id  | integer | 是    | CRM专员ID               |
| notes     | string  | 否    | 分配备注                |

**请求示例**：

```json
{
  "user_ids": [20, 21, 22],
  "agent_id": 1,
  "notes": "批量分配客户"
}
```

**响应示例**：

```json
{
  "code": 200,
  "message": "成功分配 3 个客户",
  "data": null
}
```

#### 2.3 取消客户分配

**接口地址**：`POST /api/client-assignments/unassign`

**权限要求**：admin或manager

**请求参数**：

| 参数名     | 类型    | 必须  | 描述                   |
|-----------|---------|------|------------------------|
| user_id   | integer | 是    | 客户ID                  |
| agent_id  | integer | 是    | CRM专员ID               |

**响应示例**：

```json
{
  "code": 200,
  "message": "客户分配已取消",
  "data": null
}
```

#### 2.4 获取客户的CRM专员列表

**接口地址**：`GET /api/client-assignments/user/{userId}/agents`

**权限要求**：admin、manager或客户本人

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 10,
        "name": "李明",
        "phone": "13800138000",
        "role": "crm_agent",
        "joined_at": "2023-06-01T00:00:00.000000Z",
        "pivot": {
          "user_id": 20,
          "agent_id": 10,
          "assigned_at": "2023-07-02T09:30:00.000000Z",
          "status": "active",
          "notes": "重要客户，每月跟进"
        }
      }
    ],
    // 分页信息...
  }
}
```

#### 2.5 获取CRM专员的客户列表

**接口地址**：`GET /api/client-assignments/agent/{agentId}/clients`

**权限要求**：admin、manager或专员本人

**查询参数**：

| 参数名    | 类型    | 必须  | 描述                   |
|----------|---------|------|------------------------|
| status   | string  | 否    | 状态筛选(active/inactive) |

**响应示例**：同1.6节

### 3. 客户跟进记录

#### 3.1 获取跟进记录列表

**接口地址**：`GET /api/follow-ups`

**权限要求**：所有认证用户（根据角色限制查看范围）

**查询参数**：

| 参数名      | 类型    | 必须  | 描述                   |
|------------|---------|------|------------------------|
| user_id    | integer | 否    | 按客户ID筛选            |
| agent_id   | integer | 否    | 按专员ID筛选            |
| start_date | date    | 否    | 开始日期                |
| end_date   | date    | 否    | 结束日期                |
| result     | string  | 否    | 按结果筛选              |
| per_page   | integer | 否    | 每页数量，默认10        |

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "user_id": 20,
        "agent_id": 10,
        "follow_up_date": "2023-07-05",
        "contact_method": "phone",
        "notes": "讨论了新产品需求",
        "result": "successful",
        "next_follow_up": "2023-07-15",
        "created_at": "2023-07-05T14:00:00.000000Z",
        "updated_at": "2023-07-05T14:00:00.000000Z",
        "client": {
          "id": 20,
          "name": "王五",
          "phone": "13700000000"
        },
        "agent": {
          "id": 10,
          "name": "李明",
          "phone": "13800138000"
        }
      }
    ],
    // 分页信息...
  }
}
```

#### 3.2 创建跟进记录

**接口地址**：`POST /api/follow-ups`

**权限要求**：admin或对应的CRM专员

**请求参数**：

| 参数名         | 类型    | 必须  | 描述                   |
|---------------|---------|------|------------------------|
| user_id       | integer | 是    | 客户ID                  |
| agent_id      | integer | 是    | CRM专员ID               |
| follow_up_date| date    | 是    | 跟进日期                |
| contact_method| string  | 是    | 联系方式(phone/sms/email/visit/wechat/other) |
| notes         | string  | 否    | 跟进内容                |
| result        | string  | 是    | 跟进结果(successful/follow_up/no_answer/rejected/other) |
| next_follow_up| date    | 否    | 下次跟进日期             |

**请求示例**：

```json
{
  "user_id": 20,
  "agent_id": 10,
  "follow_up_date": "2023-07-10",
  "contact_method": "phone",
  "notes": "讨论了产品使用情况",
  "result": "successful",
  "next_follow_up": "2023-07-25"
}
```

**响应示例**：

```json
{
  "code": 200,
  "message": "跟进记录创建成功",
  "data": {
    // 跟进记录信息...
  }
}
```

#### 3.3 获取跟进记录详情

**接口地址**：`GET /api/follow-ups/{id}`

**权限要求**：admin、对应的专员或客户

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "user_id": 20,
    "agent_id": 10,
    "follow_up_date": "2023-07-05",
    "contact_method": "phone",
    "notes": "讨论了新产品需求",
    "result": "successful",
    "next_follow_up": "2023-07-15",
    "created_at": "2023-07-05T14:00:00.000000Z",
    "updated_at": "2023-07-05T14:00:00.000000Z",
    "client": {
      "id": 20,
      "name": "王五",
      "phone": "13700000000"
    },
    "agent": {
      "id": 10,
      "name": "李明",
      "phone": "13800138000"
    }
  }
}
```

#### 3.4 更新跟进记录

**接口地址**：`PUT /api/follow-ups/{id}`

**权限要求**：admin或对应的专员

**请求参数**：

| 参数名         | 类型    | 必须  | 描述                   |
|---------------|---------|------|------------------------|
| follow_up_date| date    | 否    | 跟进日期                |
| contact_method| string  | 否    | 联系方式                |
| notes         | string  | 否    | 跟进内容                |
| result        | string  | 否    | 跟进结果                |
| next_follow_up| date    | 否    | 下次跟进日期             |

**响应示例**：

```json
{
  "code": 200,
  "message": "跟进记录更新成功",
  "data": {
    // 更新后的跟进记录...
  }
}
```

#### 3.5 删除跟进记录

**接口地址**：`DELETE /api/follow-ups/{id}`

**权限要求**：admin或对应的专员

**响应示例**：

```json
{
  "code": 200,
  "message": "跟进记录删除成功",
  "data": null
}
```

#### 3.6 创建后续跟进记录

**接口地址**：`POST /api/follow-ups/{id}/next`

**权限要求**：admin或对应的专员

**请求参数**：

| 参数名         | 类型    | 必须  | 描述                   |
|---------------|---------|------|------------------------|
| follow_up_date| date    | 是    | 跟进日期                |
| contact_method| string  | 是    | 联系方式                |
| notes         | string  | 否    | 跟进内容                |

**响应示例**：

```json
{
  "code": 200,
  "message": "后续跟进记录创建成功",
  "data": {
    // 新创建的跟进记录...
  }
}
```

#### 3.7 获取用户的跟进记录

**接口地址**：`GET /api/follow-ups/user/{userId}`

**权限要求**：admin、用户本人或其CRM专员

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 跟进记录列表，格式同3.1节
  }
}
```

#### 3.8 获取CRM专员的跟进记录

**接口地址**：`GET /api/follow-ups/agent/{agentId}`

**权限要求**：admin或专员本人

**查询参数**：

| 参数名      | 类型    | 必须  | 描述                   |
|------------|---------|------|------------------------|
| start_date | date    | 否    | 开始日期                |
| end_date   | date    | 否    | 结束日期                |
| per_page   | integer | 否    | 每页数量，默认10        |

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 跟进记录列表，格式同3.1节
  }
}
```

## 错误码说明

| 错误码 | 描述                |
|-------|---------------------|
| 200   | 操作成功             |
| 400   | 请求参数错误          |
| 401   | 未授权（未登录）      |
| 403   | 无权限执行此操作      |
| 404   | 请求的资源不存在      |
| 422   | 请求验证失败          |
| 500   | 服务器内部错误        |

## 注意事项

1. 所有API请求需要在Header中包含正确的Authorization令牌
2. 日期格式统一使用ISO 8601格式：`YYYY-MM-DD`
3. 创建和更新请求应确保提供的数据符合验证规则
4. API返回的数据中可能包含分页信息，前端应正确处理分页逻辑 