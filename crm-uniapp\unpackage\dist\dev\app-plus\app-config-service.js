
var isReady=false;var onReadyCallbacks=[];
var isServiceReady=false;var onServiceReadyCallbacks=[];
var __uniConfig = {"pages":["pages/login/login","pages/index/index","pages/proxy-order/proxy-order","pages/proxy-order/select-client","pages/proxy-order/select-product","pages/proxy-order/add-address","pages/clients/clients","pages/clients/client-detail","pages/orders/orders","pages/orders/order-detail","pages/profile/profile","pages/analytics/analytics","pages/analytics/client-behavior","pages/analytics/purchase-analysis","pages/analytics/product-analysis","pages/analytics/customer-segment","pages/analytics/trend-analysis","pages/analytics/churn-warning","pages/analytics/browse-analysis","pages/analytics/time-analysis","pages/analytics/customer-activity","pages/analytics/order-value-analysis"],"window":{"navigationBarTextStyle":"black","navigationBarTitleText":"CRM管理系统","navigationBarBackgroundColor":"#FFFFFF","backgroundColor":"#F8F8F8","background":"#efeff4"},"tabBar":{"color":"#7A7E83","selectedColor":"#007AFF","borderStyle":"black","backgroundColor":"#F8F8F8","list":[{"pagePath":"pages/index/index","text":"工作台"},{"pagePath":"pages/analytics/analytics","text":"数据分析"},{"pagePath":"pages/clients/clients","text":"客户管理"},{"pagePath":"pages/orders/orders","text":"订单管理"},{"pagePath":"pages/profile/profile","text":"个人中心"}]},"darkmode":false,"nvueCompiler":"uni-app","nvueStyleCompiler":"uni-app","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":false},"appname":"crm-uniapp","compilerVersion":"4.66","entryPagePath":"pages/login/login","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000}};
var __uniRoutes = [{"path":"/pages/login/login","meta":{"isQuit":true},"window":{"navigationBarTitleText":"员工登录","navigationStyle":"custom"}},{"path":"/pages/index/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"CRM管理","enablePullDownRefresh":true}},{"path":"/pages/proxy-order/proxy-order","meta":{},"window":{"navigationBarTitleText":"代客下单","enablePullDownRefresh":true}},{"path":"/pages/proxy-order/select-client","meta":{},"window":{"navigationBarTitleText":"选择客户"}},{"path":"/pages/proxy-order/select-product","meta":{},"window":{"navigationBarTitleText":"选择商品"}},{"path":"/pages/proxy-order/add-address","meta":{},"window":{"navigationBarTitleText":"添加地址"}},{"path":"/pages/clients/clients","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"客户管理","enablePullDownRefresh":true}},{"path":"/pages/clients/client-detail","meta":{},"window":{"navigationBarTitleText":"客户详情","enablePullDownRefresh":true}},{"path":"/pages/orders/orders","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"订单管理","enablePullDownRefresh":true}},{"path":"/pages/orders/order-detail","meta":{},"window":{"navigationBarTitleText":"订单详情","enablePullDownRefresh":true}},{"path":"/pages/profile/profile","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"个人中心","enablePullDownRefresh":true}},{"path":"/pages/analytics/analytics","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"行为分析","enablePullDownRefresh":true}},{"path":"/pages/analytics/client-behavior","meta":{},"window":{"navigationBarTitleText":"客户行为详情"}},{"path":"/pages/analytics/purchase-analysis","meta":{},"window":{"navigationBarTitleText":"购买分析"}},{"path":"/pages/analytics/product-analysis","meta":{},"window":{"navigationBarTitleText":"商品分析"}},{"path":"/pages/analytics/customer-segment","meta":{},"window":{"navigationBarTitleText":"客户细分"}},{"path":"/pages/analytics/trend-analysis","meta":{},"window":{"navigationBarTitleText":"趋势分析"}},{"path":"/pages/analytics/churn-warning","meta":{},"window":{"navigationBarTitleText":"流失预警","enablePullDownRefresh":true}},{"path":"/pages/analytics/browse-analysis","meta":{},"window":{"navigationBarTitleText":"浏览分析"}},{"path":"/pages/analytics/time-analysis","meta":{},"window":{"navigationBarTitleText":"时间分析"}},{"path":"/pages/analytics/customer-activity","meta":{},"window":{"navigationBarTitleText":"客户活跃度分析","enablePullDownRefresh":true}},{"path":"/pages/analytics/order-value-analysis","meta":{},"window":{"navigationBarTitleText":"客单价分析","enablePullDownRefresh":true}}];
__uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
__uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:Math.round(f/20)})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:void 0,window:void 0,document:void 0,frames:void 0,self:void 0,location:void 0,navigator:void 0,localStorage:void 0,history:void 0,Caches:void 0,screen:void 0,alert:void 0,confirm:void 0,prompt:void 0,fetch:void 0,XMLHttpRequest:void 0,WebSocket:void 0,webkit:void 0,print:void 0}}}});
