# 电商系统后台管理文档

## 1. 系统概述

本系统是基于Laravel 10和Vue的电商平台，包含前端商城和后台管理两大部分。前端商城面向普通用户(User)提供购物功能，后台管理系统则面向员工(Employee)提供系统管理功能。

### 1.1 技术架构

- **后端**：Laravel 10 (PHP框架)
- **前端**：Vue-Vben-Admin (基于Vue 3的管理后台框架)
- **数据库**：MySQL
- **认证方式**：Sanctum (Token认证)

## 2. 用户与认证系统

系统包含两类主要用户：

### 2.1 商城用户(User)

用于前端商城的普通客户账号：

- 主要用于在商城购物
- 可以通过微信小程序或网页端登录
- 角色包括：`customer`(普通客户)、`merchant`(商户)等

### 2.2 员工账户(Employee)

用于后台管理系统的员工账号：

- 专门用于管理系统登录
- 使用用户名(username)和密码登录
- 角色分为：`admin`(管理员)、`manager`(经理)、`staff`(普通员工)

## 3. 员工管理系统

### 3.1 员工模型

员工模型(`App\Models\Employee`)主要字段：

```php
protected $fillable = [
    'name',        // 员工姓名
    'username',    // 登录用户名(唯一)
    'password',    // 密码(加密存储)
    'phone',       // 电话号码
    'position',    // 职位
    'role',        // 角色(admin, manager, staff)
    'user_id',     // 关联的用户ID(可选)
];
```

### 3.2 员工登录流程

1. 访问前端登录页面：`/auth/login`（使用框架原有的登录页面）
2. 提交登录表单(username和password)到API接口
3. 后端验证成功后返回Bearer Token
4. 前端存储Token并获取员工信息
5. 根据员工角色设置对应权限和菜单

### 3.3 API接口

#### 员工登录

- **接口地址**：`POST /api/employee/auth/login`
- **请求参数**：
  - `username`: 员工用户名
  - `password`: 员工密码
- **返回数据**：
  - 访问令牌(access_token)
  - 员工基本信息(id, name, username, role等)

#### 获取当前员工信息

- **接口地址**：`GET /api/employee/auth/me`
- **请求头**：`Authorization: Bearer {token}`
- **返回数据**：当前登录的员工信息

#### 员工登出

- **接口地址**：`POST /api/employee/auth/logout`
- **请求头**：`Authorization: Bearer {token}`

## 4. 员工权限管理

### 4.1 角色与权限

系统基于角色控制权限：

- **admin(管理员)**：最高权限，可管理所有功能
- **manager(经理)**：中级权限，管理日常业务功能
- **staff(普通员工)**：基础权限，处理常规操作

### 4.2 前端权限控制

前端使用Vue Router和权限守卫实现：

```ts
// 路由守卫中检查员工角色
const PUBLIC_ROUTE_NAMES = ['Login', 'EmployeeLoginPage'];

router.beforeEach(async (to, from, next) => {
  // 检查是否公共路由
  if (PUBLIC_ROUTE_NAMES.includes(to.name as string)) {
    return next();
  }
  
  // 检查是否已登录
  const hasToken = authStore.getToken;
  if (!hasToken) {
    return next({ name: 'EmployeeLoginPage' });
  }
  
  // 获取用户信息
  await authStore.getUserInfoAction();
  
  // 根据角色检查权限
  const userRoles = authStore.getRoles;
  if (to.meta.roles && !to.meta.roles.some(role => userRoles.includes(role))) {
    return next('/403');
  }
  
  next();
});
```

## 5. 系统功能模块

后台管理系统主要包含以下功能模块：

### 5.1 用户管理

- 商城用户管理(查看、编辑、禁用账号等)
- 员工账户管理(创建、编辑、删除员工账号)

### 5.2 商品管理

- 商品分类管理
- 商品信息维护(上架、下架、编辑)
- 库存管理

### 5.3 订单管理

- 订单查询与统计
- 订单状态更新
- 订单详情查看

### 5.4 配送管理

- 配送员管理
- 配送任务分配
- 配送状态跟踪

### 5.5 客户反馈

- 反馈列表查看
- 反馈处理与回复
- 反馈统计分析

## 6. 前端技术实现

### 6.1 员工登录实现

在前端重构中，我们将使用框架原有的登录页面（`/auth/login`）进行员工登录，而不是创建新的登录页面。这样可以保持界面统一性，并利用框架已有的组件和样式。

登录组件示例：

```vue
<script lang="ts" setup>
import { computed } from 'vue';
import { AuthenticationLogin, z } from '@vben/common-ui';
import { useAuthStore } from '#/store';

defineOptions({ name: 'Login' });

const authStore = useAuthStore();

const formSchema = computed(() => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入用户名',
      },
      fieldName: 'username',
      label: '用户名',
      rules: z.string().min(1, { message: '请输入用户名' }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: '请输入密码',
      },
      fieldName: 'password',
      label: '密码',
      rules: z.string().min(1, { message: '请输入密码' }),
    },
  ];
});
</script>

<template>
  <AuthenticationLogin
    :form-schema="formSchema"
    :loading="authStore.loginLoading"
    @submit="authStore.authLogin"
    title="系统登录"
  />
</template>
```

### 6.2 Auth Store

```ts
// 员工登录处理
const login = async (params: LoginParams): Promise<void> => {
  try {
    loginLoading.value = true;
    
    // 从参数中提取用户名和密码
    const { username, password, ...rest } = params;
    
    // 调用员工登录API而非普通用户登录API
    const response = await employeeLogin({ username, password });
    
    // 获取访问令牌
    const accessToken = response.data?.access_token || response.access_token;
    
    if (!accessToken) {
      throw new Error('登录失败：无法获取访问令牌');
    }
    
    // 保存Token
    setAuthCache(TOKEN_KEY, accessToken);
    setToken(accessToken);
    
    // 获取员工信息，而不是普通用户信息
    const employeeInfo = await getEmployeeInfo();
    const employeeData = employeeInfo.data || employeeInfo;
    
    // 构造用户信息
    const userInfo: UserInfo = {
      userId: employeeData.id,
      username: employeeData.username,
      realName: employeeData.name,
      avatar: '',
      desc: employeeData.position,
      homePath: '/dashboard',
      roles: [employeeData.role],
    };
    
    // 设置用户信息
    userStore.setUserInfo(userInfo);
    
    // 登录成功提示
    ElNotification({
      message: `登录成功，欢迎回来：${userInfo.realName}`,
      title: '登录成功',
      type: 'success',
    });
    
    // 跳转到首页
    router.push(userInfo.homePath);
    
  } catch (error: any) {
    // 错误处理...（与之前相同）
  } finally {
    loginLoading.value = false;
  }
};
```

## 7. 后端实现

### 7.1 员工认证控制器

```php
// app/Admin/Http/Controllers/AuthController.php
public function login(Request $request)
{
    $request->validate([
        'username' => 'required|string',
        'password' => 'required|string',
    ]);

    $employee = Employee::where('username', $request->username)->first();

    if (!$employee || !Hash::check($request->password, $employee->password)) {
        throw ValidationException::withMessages([
            'username' => ['用户名或密码错误'],
        ]);
    }

    // 创建token
    $token = $employee->createToken('employee-token')->plainTextToken;

    return response()->json([
        'code' => 200,
        'message' => '登录成功',
        'data' => [
            'access_token' => $token,
            'token_type' => 'Bearer',
            'employee' => [
                'id' => $employee->id,
                'name' => $employee->name,
                'username' => $employee->username,
                'role' => $employee->role,
                // 其他信息...
            ],
        ],
    ]);
}
```

## 8. 常见问题与解决方案

### 8.1 登录问题

- **问题**：员工账号无法登录
- **解决方案**：
  1. 确认使用的是正确的登录页面(`/auth/login`)
  2. 确保输入正确的用户名(username)和密码
  3. 检查网络连接是否正常
  4. 查看浏览器控制台和后端日志确认具体错误

### 8.2 权限问题

- **问题**：登录后看不到某些菜单或功能
- **解决方案**：
  1. 检查当前员工的角色权限
  2. 确认相关功能对应的权限设置
  3. 可能需要管理员提升权限

## 9. 安全建议

1. 定期修改密码，尤其是管理员账号
2. 不要在公共场所登录并保持登录状态
3. 完成操作后及时登出
4. 遵循最小权限原则，根据实际需要配置员工权限

## 10. 系统优化与统一规范

为确保系统的稳定性和可维护性，我们对前后端接口进行了统一调整：

### 10.1 前端接口调整

1. **使用框架原有登录页面**：
   - 使用框架自带的登录页面(`/auth/login`)，而非创建新的员工登录页面
   - 修改表单字段，确保与后端API参数匹配
   - 简化用户登录路径，提高用户体验

2. **统一表单字段名称**：
   - 将登录表单中的字段名从`employeeId`修改为`username`，与后端API参数保持一致
   - 更新相关的标签和占位符文本，提高用户体验

3. **统一API响应处理**：
   - 对API响应采用统一的数据格式处理
   - 支持标准Laravel API响应格式：`{code, message, data}`
   - 确保各种响应格式都能被正确处理

4. **增强错误处理机制**：
   - 根据HTTP状态码提供更具体的错误信息
   - 区分网络错误、认证错误和服务器错误
   - 提供用户友好的错误提示

### 10.2 安全性增强

1. **限制错误信息暴露**：
   - 避免在前端显示详细的技术错误信息
   - 对用户展示友好且有指导性的错误提示

2. **请求参数验证**：
   - 前后端同时进行参数验证
   - 防止恶意或格式不正确的数据提交

3. **权限检查优化**：
   - 完善路由守卫和权限控制
   - 确保用户只能访问其角色允许的功能

通过以上调整，系统各组件间的交互更加一致和可靠，提高了系统的整体质量和用户体验。 