<?php

namespace App\WechatPayment\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WechatServiceProvider extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'wechat_service_provider';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'mch_id',
        'appid',
        'api_key',
        'api_v3_key',
        'cert_path',
        'key_path',
        'notify_url',
        'refund_notify_url',
        'is_active',
        'description',
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * 获取该服务商的所有子商户
     */
    public function subMerchants()
    {
        return $this->hasMany(WechatSubMerchant::class, 'provider_id');
    }

    /**
     * 获取该服务商的支付记录
     */
    public function payments()
    {
        return $this->hasManyThrough(
            WechatServicePayment::class, 
            WechatSubMerchant::class,
            'provider_id', // 子商户表的外键
            'sub_merchant_id', // 支付表的外键
            'id', // 服务商表的本地键
            'id' // 子商户表的本地键
        );
    }

    /**
     * 获取该服务商的退款记录
     */
    public function refunds()
    {
        return $this->hasManyThrough(
            WechatServiceRefund::class,
            WechatServicePayment::class,
            'provider_id', // 支付表的外键
            'payment_id', // 退款表的外键
            'id', // 服务商表的本地键
            'id' // 支付表的本地键
        );
    }
} 