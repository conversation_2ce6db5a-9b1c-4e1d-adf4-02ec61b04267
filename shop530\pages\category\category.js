/**
 * 分类页面 - 性能优化版
 */
const { api } = require('../../utils/api');
const { isLoggedIn, addLoginStateListener, removeLoginStateListener } = require('../../utils/login-state-manager');
const { addToCart, getCartCount, updateBadge, addListener, removeListener, onPageShow, CartEvents } = require('../../utils/cart-unified');

/**
 * 简化的缓存管理
 */
class CategoryCache {
  constructor() {
    this.categoriesCache = null;
    this.productCache = new Map();
    this.cacheExpiry = 3 * 60 * 1000; // 3分钟
  }

  setCategories(categories) {
    this.categoriesCache = { data: categories, time: Date.now() };
  }

  getCategories() {
    if (!this.categoriesCache || Date.now() - this.categoriesCache.time > this.cacheExpiry) {
      return null;
    }
    return this.categoriesCache.data;
  }

  setProducts(key, products, hasMore) {
    this.productCache.set(key, { products, hasMore, time: Date.now() });
  }

  getProducts(key) {
    const cached = this.productCache.get(key);
    if (!cached || Date.now() - cached.time > this.cacheExpiry) {
      return null;
    }
    return cached;
  }

  clear() {
    this.productCache.clear();
  }
}

const cache = new CategoryCache();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 基础数据
    loading: true,
    loadingMore: false,
    
    // 分类数据
    categories: [],
    expandedCategoryId: null, // 当前展开的分类ID
    activeCategoryId: null,   // 当前选中的分类ID
    activeSubCategoryId: null, // 当前选中的子分类ID
    currentCategory: null,    // 当前选中的分类对象
    
    // 商品数据
    products: [],
    hasMore: true,
    currentPage: 1,
    
    // 搜索相关
    searchKeyword: '',
    
    // 筛选相关
    sortText: '综合排序',
    priceSort: '',
    
    // 请求控制
    shouldAbortRequest: false,
    
    // 页面参数
    targetCategoryId: null,
    
    // 购物车
    cartCount: 0,
    cartAnimation: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 处理页面参数
    if (options.categoryId) {
      this.setData({ targetCategoryId: parseInt(options.categoryId) });
    }
    
    // 初始化数据
    this.initData();
    
    // 初始化购物车监听
    this.initCartListener();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 更新购物车数量
    this.updateCartCount();
    
    // 通知购物车统一管理器 - 仅在用户已登录时执行
    if (isLoggedIn()) {
      onPageShow('category');
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 中断所有请求
    this.shouldAbortRequest = true;
    
    // 清理购物车监听器
    this.cleanupCartListener();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData().then(() => {
      wx.stopPullDownRefresh();
    }).catch(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 使用scroll-view的bindscrolltolower代替
  },

  /**
   * 初始化数据
   */
  async initData() {
    try {
      this.setData({ loading: true });
      
      // 加载分类
      await this.loadCategories();
      
      // 如果有当前分类，加载商品
      if (this.data.currentCategory?.id) {
        await this.loadProducts(true);
      }
      
      this.setData({ loading: false });
      
    } catch (error) {
      this.setData({ loading: false });
      this.handleError(error, '加载');
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      wx.showLoading({ title: '刷新中...' });
      
      this.setData({ 
        products: [], 
        currentPage: 1, 
        hasMore: true 
      });
      
      await this.loadCategories();
      
      if (this.data.currentCategory?.id) {
        await this.loadProducts(true);
      }
      
      wx.hideLoading();
      
    } catch (error) {
      wx.hideLoading();
      this.handleError(error, '刷新');
    }
  },

  /**
   * 加载分类
   */
  async loadCategories() {
    try {
      // 尝试从缓存获取
      const cachedCategories = cache.getCategories();
      if (cachedCategories) {
        this.processCategories(cachedCategories);
        return;
      }
      
      // 获取分类树
      let result = await api.getCategoryTree(0);
      
      if (!result || !Array.isArray(result) || result.length === 0) {
        result = await api.getCategories();
      }
      
      if (!result || !Array.isArray(result)) {
        throw new Error('分类数据格式错误');
      }
      
      // 并行加载所有分类的子分类
      const categories = await this.loadCategoriesWithChildren(result);
      
      // 缓存结果
      cache.setCategories(categories);
      
      // 处理分类数据
      this.processCategories(categories);
      
    } catch (error) {
      throw error;
    }
  },

  /**
   * 并行加载分类的子分类
   */
  async loadCategoriesWithChildren(categories) {
    // 创建所有子分类加载的Promise
    const promises = categories.map(async (category) => {
      try {
        const children = await api.getCategoryChildren(category.id);
        
        return {
          id: category.id,
          name: category.name || '未命名分类',
          icon: category.image_url || category.custom_icon_url || category.icon || '',
          hasChildren: children && children.length > 0,
          children: (children || []).map(child => ({
            id: child.id,
            parentId: category.id,
            name: child.name || '未命名子分类',
            icon: child.image_url || child.custom_icon_url || child.icon || '',
          }))
        };
      } catch (error) {
        return {
          id: category.id,
          name: category.name || '未命名分类',
          icon: category.image_url || category.custom_icon_url || category.icon || '',
          hasChildren: false,
          children: []
        };
      }
    });

    // 并行执行所有Promise
    return await Promise.all(promises);
  },

  /**
   * 处理分类数据
   */
  processCategories(categories) {
    // 处理目标分类ID
    const targetCategoryId = this.data.targetCategoryId;
    let targetCategory = null;
    let targetParentCategory = null;
    
    if (targetCategoryId) {
      // 查找目标分类
      for (const category of categories) {
        // 检查主分类
        if (category.id === targetCategoryId) {
          targetCategory = category;
          break;
        }
        
        // 检查子分类
        if (category.hasChildren) {
          const child = category.children.find(c => c.id === targetCategoryId);
          if (child) {
            targetCategory = child;
            targetParentCategory = category;
            break;
          }
        }
      }
    }
    
    // 如果没有找到目标分类，使用第一个分类
    if (!targetCategory) {
      targetCategory = categories[0];
    }
    
    // 构建平铺的分类列表（只包含主分类）
    const flatCategories = categories.map(category => ({
      id: category.id,
      name: category.name,
      icon: category.icon,
      hasChildren: category.hasChildren,
      children: category.children,
      isExpanded: false
    }));
    
    // 更新状态
    const updates = {
      categories: flatCategories,
      currentCategory: targetCategory,
      activeCategoryId: targetParentCategory ? targetParentCategory.id : targetCategory.id,
      activeSubCategoryId: targetParentCategory ? targetCategory.id : null
    };
    
    this.setData(updates);
    
    // 默认展开第一个分类
    if (flatCategories.length > 0) {
      this.expandCategory(flatCategories[0].id);
    }
    
    // 如果有目标子分类，展开其父分类
    if (targetParentCategory) {
      this.expandCategory(targetParentCategory.id);
    }
  },

  /**
   * 展开分类
   */
  expandCategory(categoryId) {
    const { categories, expandedCategoryId } = this.data;
    
    // 如果已经展开，不做任何操作
    if (expandedCategoryId === categoryId) {
      return;
    }
    
    // 找到目标分类
    const targetCategory = categories.find(c => c.id === categoryId);
    if (!targetCategory || !targetCategory.hasChildren) {
      return;
    }
    
    // 构建新的分类列表
    const newCategories = categories.map(category => {
      if (category.id === categoryId) {
        return { ...category, isExpanded: true };
      }
      return { ...category, isExpanded: false };
    });
    
    // 更新状态
    this.setData({
      categories: newCategories,
      expandedCategoryId: categoryId
    });
  },

  /**
   * 收起分类
   */
  collapseCategory(categoryId) {
    const { categories, expandedCategoryId } = this.data;
    
    // 如果没有展开，不做任何操作
    if (expandedCategoryId !== categoryId) {
      return;
    }
    
    // 构建新的分类列表
    const newCategories = categories.map(category => {
      if (category.id === categoryId) {
        return { ...category, isExpanded: false };
      }
      return category;
    });
    
    // 更新状态
    this.setData({
      categories: newCategories,
      expandedCategoryId: null
    });
  },

  /**
   * 切换分类展开状态
   */
  toggleCategoryExpansion(categoryId) {
    const { expandedCategoryId } = this.data;
    
    if (expandedCategoryId === categoryId) {
      this.collapseCategory(categoryId);
    } else {
      this.expandCategory(categoryId);
    }
  },

  /**
   * 处理分类点击
   */
  handleCategoryTap(e) {
    const { id, type } = e.currentTarget.dataset;
    
    if (type === 'main') {
      // 主分类点击
      const category = this.data.categories.find(c => c.id === id);
      
      if (category) {
        // 更新选中状态
        this.setData({ 
          activeCategoryId: id,
          activeSubCategoryId: null,
          currentCategory: category
        });
        
        // 加载商品
        this.loadProducts(true);
        
        // 如果有子分类，切换展开状态
        if (category.hasChildren) {
          this.toggleCategoryExpansion(id);
        }
      }
    } else if (type === 'sub') {
      // 子分类点击
      const parentId = e.currentTarget.dataset.parentId;
      const parentCategory = this.data.categories.find(c => c.id === parentId);
      
      if (parentCategory) {
        const subCategory = parentCategory.children.find(c => c.id === id);
        
        if (subCategory) {
          // 更新选中状态
          this.setData({
            activeCategoryId: parentId,
            activeSubCategoryId: id,
            currentCategory: { ...subCategory, parentId }
          });
          
          // 加载商品
          this.loadProducts(true);
        }
      }
    }
  },

  /**
   * 生成缓存键
   */
  generateCacheKey(category, page = 1) {
    const id = category.id;
    const parentId = category.parentId || 0;
    
    return `${parentId}_${id}_${page}_${Math.floor(Date.now() / 300000)}`; // 5分钟时间窗口
  },

  /**
   * 加载商品
   */
  async loadProducts(reset = false) {
    try {
      const currentCategory = this.data.currentCategory;
      
      if (!currentCategory?.id) {
        this.setData({
          products: [],
          hasMore: false,
          loading: false,
          loadingMore: false
        });
        return;
      }
      
      // 如果是重置，中断之前的请求
      if (reset) {
        this.shouldAbortRequest = true;
      }
      
      const page = reset ? 1 : this.data.currentPage;
      const cacheKey = this.generateCacheKey(currentCategory, page);
      
      // 检查缓存
      if (reset) {
        const cached = cache.getProducts(cacheKey);
        if (cached) {
          // 过滤并格式化缓存的商品数据
          let validProducts = (cached.products || []).filter(p => p && typeof p === 'object' && p.id);
          
          // 确保每个商品对象都有必要的属性
          validProducts = validProducts.map(p => ({
            id: p.id || 0,
            name: p.name || '未命名商品',
            image: p.image || '',
            price: p.price || '0.00',
            unit: p.unit || '',
            tags: Array.isArray(p.tags) ? p.tags : [],
            out_of_stock: !!p.out_of_stock
          }));
          
          this.setData({
            products: validProducts,
            hasMore: cached.hasMore,
            currentPage: 2,
            loading: false,
            loadingMore: false
          });
          
          return;
        }
      }
      
      // 显示加载状态
      this.setData({
        loading: reset,
        loadingMore: !reset
      });
      
      // 重置中断标志
      this.shouldAbortRequest = false;
      
      // 请求参数
      const params = {
        page,
        limit: 20, // 增加每页数量
        category_id: currentCategory.id
      };
      
      // 发起请求
      const result = await api.getProducts(params);
      
      // 如果请求被中止，直接返回
      if (this.shouldAbortRequest) {
        return;
      }
      
      // 检查API返回的数据结构
      if (!result) {
        this.setData({ loading: false, loadingMore: false });
        return;
      }
      
      // 处理结果，确保每个商品都是有效的对象
      let products = [];
      
      // 根据API返回的实际数据结构提取商品数据
      let productData = [];
      
      if (result && result.data && result.data.data) {
        // 新的API结构，data.data是商品数组
        productData = result.data.data;
      } else if (result && result.list && result.list.data) {
        // 备选结构，list.data是商品数组
        productData = result.list.data;
      } else if (result && Array.isArray(result.data)) {
        // 旧的API结构，data直接是商品数组
        productData = result.data;
      }
      
      // 检查第一个商品的图片字段
      if (productData && productData.length > 0) {
        const firstProduct = productData[0];
        const possibleImageFields = ['image', 'image_url', 'thumbnail', 'cover', 'picture', 'photo', 'img'];
        
        const imageFields = possibleImageFields.filter(field => firstProduct[field]);
        if (imageFields.length === 0) {
          // 静默处理
        }
      }
      
      // 过滤和格式化商品数据
      if (Array.isArray(productData)) {
        // 过滤掉无效的商品数据
        products = productData.filter(p => p && typeof p === 'object' && p.id);
        
        // 确保每个商品对象都有必要的属性，并处理图片URL
        products = products.map(p => {
          // 尝试获取图片URL
          let imageUrl = '';
          
          // 检查所有可能的图片字段
          const imageFields = ['image', 'image_url', 'thumbnail', 'cover', 'picture', 'photo', 'img', 'images'];
          for (const field of imageFields) {
            if (p[field]) {
              // 如果是数组，取第一个
              if (Array.isArray(p[field]) && p[field].length > 0) {
                // 数组可能是URL字符串数组，也可能是对象数组
                const firstImage = p[field][0];
                if (typeof firstImage === 'string') {
                  imageUrl = firstImage;
                } else if (typeof firstImage === 'object' && firstImage !== null) {
                  // 对象可能有url或src字段
                  imageUrl = firstImage.url || firstImage.src || firstImage.path || '';
                }
              } else if (typeof p[field] === 'string') {
                imageUrl = p[field];
              } else if (typeof p[field] === 'object' && p[field] !== null) {
                // 对象可能有url或src字段
                imageUrl = p[field].url || p[field].src || p[field].path || '';
              }
              
              if (imageUrl) break;
            }
          }
          
          // 处理相对路径
          if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('data:')) {
            // 移除前导斜杠以避免双斜杠
            if (imageUrl.startsWith('/')) {
              imageUrl = imageUrl.substring(1);
            }
            
            // 添加基础URL
            imageUrl = `http://localhost/${imageUrl}`;
          }
          
          // 如果仍然没有图片，使用默认图片
          if (!imageUrl) {
            imageUrl = '/images/default-product.png';
          }
          
          return {
            id: p.id || 0,
            name: p.name || p.title || '未命名商品',
            image: imageUrl,
            price: p.price || p.sale_price || '0.00',
            unit: p.unit || p.unit_name || '',
            tags: Array.isArray(p.tags) ? p.tags : [],
            out_of_stock: !!p.out_of_stock
          };
        });
        

      } else if (productData && typeof productData === 'object' && productData.id) {
        // 单个商品对象
        const p = productData;
        
        // 尝试获取图片URL
        let imageUrl = '';
        
        // 检查所有可能的图片字段
        const imageFields = ['image', 'image_url', 'thumbnail', 'cover', 'picture', 'photo', 'img', 'images'];
        for (const field of imageFields) {
          if (p[field]) {
            // 如果是数组，取第一个
            if (Array.isArray(p[field]) && p[field].length > 0) {
              // 数组可能是URL字符串数组，也可能是对象数组
              const firstImage = p[field][0];
              if (typeof firstImage === 'string') {
                imageUrl = firstImage;
              } else if (typeof firstImage === 'object' && firstImage !== null) {
                // 对象可能有url或src字段
                imageUrl = firstImage.url || firstImage.src || firstImage.path || '';
              }
            } else if (typeof p[field] === 'string') {
              imageUrl = p[field];
            } else if (typeof p[field] === 'object' && p[field] !== null) {
              // 对象可能有url或src字段
              imageUrl = p[field].url || p[field].src || p[field].path || '';
            }
            
            if (imageUrl) break;
          }
        }
        
        // 处理相对路径
        if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('data:')) {
          // 移除前导斜杠以避免双斜杠
          if (imageUrl.startsWith('/')) {
            imageUrl = imageUrl.substring(1);
          }
          
          // 添加基础URL
          imageUrl = `http://localhost/${imageUrl}`;
        }
        
        // 如果仍然没有图片，使用默认图片
        if (!imageUrl) {
          imageUrl = '/images/default-product.png';
        }
        
        products = [{
          id: p.id || 0,
          name: p.name || p.title || '未命名商品',
          image: imageUrl,
          price: p.price || p.sale_price || '0.00',
          unit: p.unit || p.unit_name || '',
          tags: Array.isArray(p.tags) ? p.tags : [],
          out_of_stock: !!p.out_of_stock
        }];
      }
      
      // 判断是否有更多数据
      const hasMore = result?.hasMore !== false;
      
      // 缓存结果
      if (page === 1) {
        cache.setProducts(cacheKey, products, hasMore);
      }
      
      // 更新状态
      const currentProducts = reset ? [] : [...this.data.products];
      const newProducts = [...currentProducts, ...products];
      
      this.setData({
        products: newProducts,
        hasMore,
        currentPage: page + 1,
        loading: false,
        loadingMore: false
      });
      
    } catch (error) {
      this.setData({ loading: false, loadingMore: false });
      this.handleError(error, '加载商品');
    }
  },

  /**
   * 加载更多
   */
  loadMore() {
    if (this.data.hasMore && !this.data.loadingMore && !this.data.loading) {
      this.loadProducts(false);
    }
  },

  /**
   * 处理快捷入口点击
   */
  handleQuickEntryTap(e) {
    const { type } = e.currentTarget.dataset;
    
    this.setData({
      activeCategoryId: null,
      activeSubCategoryId: null,
      expandedCategoryId: null,
      currentCategory: { id: type, name: this.getQuickEntryName(type), type }
    });
    
    // 根据类型加载不同的商品
    switch (type) {
      case 'frequent':
        this.loadFrequentProducts();
        break;
      case 'new':
        this.loadNewProducts();
        break;
      case 'sale':
        this.loadSaleProducts();
        break;
    }
  },

  /**
   * 获取快捷入口名称
   */
  getQuickEntryName(type) {
    switch (type) {
      case 'frequent': return '常购清单';
      case 'new': return '新品';
      case 'sale': return '特价';
      default: return '未知';
    }
  },

  /**
   * 加载常购商品
   */
  async loadFrequentProducts(page = 1) {
    try {
      // 中断之前的请求
      this.shouldAbortRequest = true;
      
      // 显示加载状态
      this.setData({
        loading: page === 1,
        loadingMore: page > 1,
        products: page === 1 ? [] : this.data.products
      });
      
      // 重置中断标志
      this.shouldAbortRequest = false;
      
      // 检查登录状态
      if (!isLoggedIn()) {
        this.setData({ 
          loading: false,
          loadingMore: false,
          products: [],
          hasMore: false,
          emptyMessage: '请先登录查看您的常购清单',
          showLoginButton: true
        });
        return;
      }
      
      // 发起请求
      const result = await api.getUserFrequentProducts({
        page,
        limit: 10
      });
      
      // 如果请求被中止，直接返回
      if (this.shouldAbortRequest) {
        return;
      }
      
      // 处理结果
      const products = result?.data || [];
      const hasMore = result?.has_more !== false;
      
      // 更新状态
      this.setData({
        products: page === 1 ? products : [...this.data.products, ...products],
        hasMore,
        currentPage: page + 1,
        loading: false,
        loadingMore: false,
        emptyMessage: products.length === 0 ? '暂无常购商品，快去购物吧' : '',
        showLoginButton: false
      });
      
    } catch (error) {
      this.setData({ 
        loading: false, 
        loadingMore: false,
        products: [],
        hasMore: false
      });
      
      // 处理401未授权错误
      if (error.statusCode === 401 || error.message?.includes('未认证') || error.message?.includes('认证已过期')) {
        this.setData({
          emptyMessage: '请先登录查看您的常购清单',
          showLoginButton: true
        });
      } else {
        this.handleError(error, '加载常购商品');
      }
    }
  },

  /**
   * 加载新品
   */
  async loadNewProducts(page = 1) {
    try {
      // 中断之前的请求
      this.shouldAbortRequest = true;
      
      // 显示加载状态
      this.setData({
        loading: page === 1,
        loadingMore: page > 1,
        products: page === 1 ? [] : this.data.products
      });
      
      // 重置中断标志
      this.shouldAbortRequest = false;
      
      // 发起请求 - 使用标签筛选
      const result = await api.getProductsByTag('new', {
        page,
        per_page: 10
      });
      
      // 如果请求被中止，直接返回
      if (this.shouldAbortRequest) {
        return;
      }
      
      // 处理结果
      const products = result?.data || [];
      const hasMore = result?.meta?.current_page < result?.meta?.last_page;
      
      // 更新状态
      this.setData({
        products: page === 1 ? products : [...this.data.products, ...products],
        hasMore,
        currentPage: page + 1,
        loading: false,
        loadingMore: false
      });
      
    } catch (error) {
      logger.error('加载新品失败', error);
      this.setData({ loading: false, loadingMore: false });
      this.handleError(error, '加载新品');
    }
  },

  /**
   * 加载特价商品
   */
  async loadSaleProducts(page = 1) {
    try {
      // 中断之前的请求
      this.shouldAbortRequest = true;
      
      // 显示加载状态
      this.setData({
        loading: page === 1,
        loadingMore: page > 1,
        products: page === 1 ? [] : this.data.products
      });
      
      // 重置中断标志
      this.shouldAbortRequest = false;
      
      // 发起请求 - 使用标签筛选
      const result = await api.getProductsByTag('promotion', {
        page,
        per_page: 10
      });
      
      // 如果请求被中止，直接返回
      if (this.shouldAbortRequest) {
        return;
      }
      
      // 处理结果
      const products = result?.data || [];
      const hasMore = result?.meta?.current_page < result?.meta?.last_page;
      
      // 更新状态
      this.setData({
        products: page === 1 ? products : [...this.data.products, ...products],
        hasMore,
        currentPage: page + 1,
        loading: false,
        loadingMore: false
      });
      
    } catch (error) {
      this.setData({ loading: false, loadingMore: false });
      this.handleError(error, '加载特价商品');
    }
  },

  /**
   * 处理商品点击
   */
  handleProductTap(e) {
    const { id } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${id}`
    });
  },
  
  /**
   * 处理商品卡片点击
   */
  handleProductCardTap(e) {
    const { product } = e.detail;
    if (product && product.id) {
      wx.navigateTo({
        url: `/pages/product-detail/product-detail?id=${product.id}`
      });
    }
  },

  /**
   * 处理添加到购物车
   */
  async handleAddToCart(e) {
    try {
      // 检查用户是否已登录
      if (!isLoggedIn()) {
        // 未登录时，导航到登录页面
        wx.showToast({ title: '请先登录', icon: 'none' });
        setTimeout(() => {
          this.navigateToLogin();
        }, 1000);
        return;
      }

      const { product, quantity = 1 } = e.detail;

      if (!product || !product.id) {
        wx.showToast({ title: '商品信息错误', icon: 'none' });
        return;
      }

      console.log('📦 分类页添加到购物车:', {
        product_id: product.id,
        product_name: product.name || '未知商品',
        quantity
      });

      wx.showLoading({ title: '添加中...' });

      // 使用统一的购物车管理器
      const { addToCart } = require('../../utils/cart-unified');
      const result = await addToCart({
        product_id: product.id,
        quantity: quantity
      });

      wx.hideLoading();

      if (result) {
        // 触发动画
        this.triggerCartAnimation(e);

        // 更新购物车数量
        this.updateCartCount();

        // 不显示加购提示，让用户直接看到数量变化
        console.log('✅ 分类页添加购物车成功');
      } else {
        wx.showToast({
          title: '添加失败，请重试',
          icon: 'none'
        });
        console.error('❌ 分类页添加购物车失败:', result);
      }

    } catch (error) {
      wx.hideLoading();
      console.error('❌ 分类页添加购物车异常:', error);
      this.handleError(error, '添加到购物车');
    }
  },

  /**
   * 触发购物车动画
   */
  triggerCartAnimation(e) {
    // 获取点击位置
    const { clientX, clientY } = e.detail;
    
    // 创建动画元素
    const animation = wx.createAnimation({
      duration: 500,
      timingFunction: 'ease',
    });
    
    // 设置动画
    animation.opacity(0).scale(0.1).step();
    
    // 更新状态
    this.setData({
      cartAnimation: {
        x: clientX,
        y: clientY,
        animation: animation.export()
      }
    });
    
    // 动画结束后清理
    setTimeout(() => {
      this.setData({ cartAnimation: null });
    }, 500);
  },

  /**
   * 处理搜索
   */
  handleSearch(e) {
    const value = e;
    if (value && value.trim()) {
      wx.navigateTo({
        url: `/pages/search/search?keyword=${encodeURIComponent(value.trim())}`
      });
    }
  },

  /**
   * 处理搜索输入变化
   */
  handleSearchChange(e) {
    const value = e.detail;
    this.setData({
      searchKeyword: value
    });
  },

  /**
   * 处理清除搜索
   */
  handleSearchClear() {
    this.setData({
      searchKeyword: ''
    });
  },

  /**
   * 搜索按钮点击
   */
  handleSearchButtonTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    });
  },

  /**
   * 处理排序点击
   */
  handleSortTap() {
    // 实现排序逻辑
    wx.showActionSheet({
      itemList: ['综合排序', '销量优先', '价格从低到高', '价格从高到低'],
      success: (res) => {
        if (!res.cancel) {
          const sortTexts = ['综合排序', '销量优先', '价格从低到高', '价格从高到低'];
          this.setData({ sortText: sortTexts[res.tapIndex] });
          
          // 重新加载商品
          this.loadProducts(true);
        }
      }
    });
  },

  /**
   * 处理价格排序点击
   */
  handlePriceTap() {
    const priceSort = this.data.priceSort === 'asc' ? 'desc' : 'asc';
    this.setData({ priceSort });
    
    // 重新加载商品
    this.loadProducts(true);
  },

  /**
   * 处理筛选点击
   */
  handleFilterTap() {
    // 实现筛选逻辑
    wx.showToast({ title: '筛选功能开发中', icon: 'none' });
  },

  /**
   * 处理错误
   */
  handleError(error, context = '操作', showToast = true) {
    let message = '发生错误，请重试';
    
    if (error.message) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    }
    
    if (showToast) {
      wx.showToast({
        title: message.length > 7 ? message.substring(0, 7) + '...' : message,
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 初始化购物车监听
   */
  initCartListener() {
    // 检查用户是否已登录
    if (!isLoggedIn()) {
      return; // 用户未登录，不初始化监听器
    }

    // 添加购物车变化监听器
    this.cartChangeListener = (event) => {
      // 根据事件类型处理不同的变化
      switch (event.action) {
        case CartEvents.COUNT:
          // 购物车数量变化事件
          if (event.data && typeof event.data.count === 'number') {
            this.setData({ cartCount: event.data.count });
          }
          break;
    
        case CartEvents.ADD:
        case CartEvents.REMOVE:
        case CartEvents.UPDATE:
          // 这些事件可能影响购物车数量，但我们已经通过COUNT事件处理了
          // 所以这里不需要额外处理
          break;
      }
    };
    
    // 添加监听器，设置只关心特定事件类型
    addListener('categoryPage', this.cartChangeListener, {
      events: [CartEvents.COUNT],
      immediate: true // 立即触发以获取当前数量
    });
  },

  /**
   * 清理购物车监听
   */
  cleanupCartListener() {
    if (this.cartChangeListener) {
      removeListener('categoryPage');
      this.cartChangeListener = null;
    }
  },

  /**
   * 更新购物车数量
   */
  async updateCartCount() {
    try {
      // 检查用户是否已登录
      if (!isLoggedIn()) {
        this.setData({ cartCount: 0 });
        return; // 用户未登录，直接返回
      }
      
      const count = await getCartCount();
      this.setData({ cartCount: count });
      updateBadge(count);
    } catch (error) {
      // 静默失败
    }
  },
  
  /**
   * 导航到购物车页面
   */
  navigateToCart() {
    wx.switchTab({
      url: '/pages/cart/cart'
    });
  },
  
  /**
   * 导航到登录页面
   */
  navigateToLogin() {
    wx.navigateTo({
      url: '/pages/login/index'
    });
  }
}); 