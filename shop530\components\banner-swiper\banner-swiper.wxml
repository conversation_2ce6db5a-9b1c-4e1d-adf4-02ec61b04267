<!-- components/banner-swiper/banner-swiper.wxml -->
<view class="banner-swiper-container" style="height: {{height}};">
  
  <!-- 三层切割轮播效果 -->
  <view class="banner-section-layered" wx:if="{{bannerList && bannerList.length > 0 && enableLayered}}">
    
    <!-- 上层 - 静态装饰层 -->
    <view class="banner-layer banner-layer-top">
      <view class="banner-item-top">
        <lazy-image
          src="{{bannerList[0].image || ''}}"
          mode="aspectFill"
          width="100%"
          height="100%"
          custom-class="banner-image-top"
        />
      </view>
    </view>
    
    <!-- 中层 - 主轮播区域 -->
    <view class="banner-layer banner-layer-middle">
      <swiper 
        class="banner-swiper-middle" 
        indicator-dots="{{indicatorDots}}"
        autoplay="{{autoplay}}"
        interval="{{interval}}"
        duration="{{duration}}"
        circular="{{true}}"
        indicator-color="rgba(255,255,255,0.4)"
        indicator-active-color="rgba(255,255,255,0.9)"
        bindchange="onBannerChange"
        easing-function="easeInOutCubic"
        current="{{currentIndex}}"
      >
        <block wx:for="{{bannerList}}" wx:key="id">
          <swiper-item>
            <view class="banner-item-middle" bindtap="onBannerTap" data-item="{{item}}">
              <lazy-image
                src="{{item.image || ''}}"
                mode="aspectFill"
                width="100%"
                height="100%"
                custom-class="banner-image-middle"
              />
            </view>
          </swiper-item>
        </block>
      </swiper>
    </view>
    
    <!-- 下层 - 静态装饰层 -->
    <view class="banner-layer banner-layer-bottom">
      <view class="banner-item-bottom">
        <lazy-image
          src="{{bannerList[0].image || ''}}"
          mode="aspectFill"
          width="100%"
          height="100%"
          custom-class="banner-image-bottom"
        />
      </view>
    </view>
  </view>

  <!-- 普通轮播效果 -->
  <view class="banner-section-normal" wx:elif="{{bannerList && bannerList.length > 0}}">
    <swiper 
      class="banner-swiper-normal" 
      indicator-dots="{{indicatorDots}}"
      autoplay="{{autoplay}}"
      interval="{{interval}}"
      duration="{{duration}}"
      circular="{{true}}"
      indicator-color="rgba(255,255,255,0.4)"
      indicator-active-color="rgba(255,255,255,0.9)"
      bindchange="onBannerChange"
      easing-function="easeInOutCubic"
      current="{{currentIndex}}"
    >
      <block wx:for="{{bannerList}}" wx:key="id">
        <swiper-item>
          <view class="banner-item-normal" bindtap="onBannerTap" data-item="{{item}}">
            <lazy-image
              src="{{item.image || ''}}"
              mode="aspectFill"
              width="100%"
              height="100%"
              custom-class="banner-image-normal"
            />
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <!-- 空状态占位 - 移除加载提示 -->
  <view class="banner-placeholder" wx:else></view>

</view> 