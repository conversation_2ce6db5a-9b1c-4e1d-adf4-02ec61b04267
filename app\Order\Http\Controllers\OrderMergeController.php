<?php

namespace App\Order\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Order\Services\OrderMergeDataService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Exception;

class OrderMergeController extends Controller
{
    protected OrderMergeDataService $orderMergeService;

    public function __construct(OrderMergeDataService $orderMergeService)
    {
        $this->orderMergeService = $orderMergeService;
    }

    /**
     * 预览订单合并效果
     */
    public function preview(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_ids' => 'required|array|min:2',
            'order_ids.*' => 'integer|exists:orders,id',
            'merge_type' => 'in:manual,auto',
            'merge_reason' => 'string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $preview = $this->orderMergeService->previewMerge($request->order_ids);
            
            return response()->json($preview);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取合并预览失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 执行订单合并
     */
    public function execute(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_ids' => 'required|array|min:2',
            'order_ids.*' => 'integer|exists:orders,id',
            'merge_type' => 'in:manual,auto',
            'merge_reason' => 'string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // 先预览获取合并数据
            $preview = $this->orderMergeService->previewMerge($request->order_ids);
            
            if (!$preview['can_merge']) {
                return response()->json([
                    'success' => false,
                    'message' => '订单无法合并',
                    'errors' => $preview['errors'] ?? []
                ], 400);
            }
            
            // 执行合并
            $mergedOrder = $this->orderMergeService->executeMerge(
                $request->order_ids,
                $preview['merged_preview'],
                [
                    'merge_type' => $request->merge_type ?? 'manual',
                    'merge_reason' => $request->merge_reason ?? '手动合并订单'
                ]
            );
            
            return response()->json([
                'success' => true,
                'message' => '订单合并成功',
                'merged_order' => $mergedOrder,
                'original_order_ids' => $request->order_ids,
                'savings' => $preview['merged_preview']['savings'] ?? 0
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '订单合并失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取当日合并候选订单
     */
    public function candidates(Request $request): JsonResponse
    {
        $date = $request->get('date', now()->toDateString());
        
        try {
            $candidates = $this->orderMergeService->getDailyMergeCandidates($date);
            
            return response()->json($candidates);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取合并候选失败: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * 撤销订单合并
     */
    public function revert(Request $request, $mergeId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->orderMergeService->revertMerge(
                $mergeId,
                $request->reason ?? '手动撤销合并'
            );
            
            return response()->json($result);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '撤销合并失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取订单合并历史
     */
    public function history(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer|min:1',
            'per_page' => 'integer|min:1|max:100',
            'user_id' => 'integer|exists:users,id',
            'date_from' => 'date',
            'date_to' => 'date|after_or_equal:date_from'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $filters = $request->only(['user_id', 'date_from', 'date_to']);
            $page = $request->get('page', 1);
            $perPage = $request->get('per_page', 20);
            
            $history = $this->orderMergeService->getMergeHistory($filters, $page, $perPage);
            
            return response()->json($history);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取合并历史失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 自动合并当日订单
     */
    public function autoMergeToday(): JsonResponse
    {
        try {
            $result = $this->orderMergeService->autoMergeTodayOrders();
            
            return response()->json([
                'success' => true,
                'message' => "自动合并完成，共合并 {$result['merged_count']} 组订单，节省金额 ¥{$result['total_savings']}",
                'data' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '自动合并失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 