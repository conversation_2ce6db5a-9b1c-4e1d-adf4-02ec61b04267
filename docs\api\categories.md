# 商品分类接口文档

该文档描述了商品分类模块的所有API接口。

## 基础信息

- 基础路径: `/api/categories`
- 认证方式: 需要Bearer Token认证
- 请求头要求: 
  - `Authorization: Bearer {your_token}`
  - `Accept: application/json`
  - `Content-Type: application/json`（POST/PUT请求）

## 1. 获取分类列表

获取商品分类的分页列表。

### 请求

```
GET /api/categories
```

### 查询参数

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| page | integer | 否 | 页码，默认为1 |
| limit | integer | 否 | 每页记录数，默认为10 |
| keyword | string | 否 | 关键字搜索(分类名称) |
| status | integer | 否 | 状态筛选(1启用，0禁用) |
| parent_id | integer | 否 | 父级ID筛选 |

### 示例请求

```
GET /api/categories?page=1&limit=10&keyword=食品&status=1
```

### 响应

成功响应 (200 OK):

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "name": "食品饮料",
        "description": "包含各类食品和饮料",
        "parent_id": 0,
        "sort": 1,
        "status": 1,
        "created_at": "2023-06-15T10:00:00.000000Z",
        "updated_at": "2023-06-15T10:00:00.000000Z"
      },
      {
        "id": 2,
        "name": "生鲜水果",
        "description": "新鲜水果和蔬菜",
        "parent_id": 1,
        "sort": 1,
        "status": 1,
        "created_at": "2023-06-15T10:00:00.000000Z",
        "updated_at": "2023-06-15T10:00:00.000000Z"
      }
    ],
    "from": 1,
    "last_page": 1,
    "per_page": 10,
    "to": 2,
    "total": 2
  }
}
```

## 2. 获取分类树

获取分类的树形结构，只返回状态为启用的分类。

### 请求

```
GET /api/categories/tree
```

### 示例请求

```
GET /api/categories/tree
```

### 响应

成功响应 (200 OK):

```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "食品饮料",
      "description": "包含各类食品和饮料",
      "parent_id": 0,
      "sort": 1,
      "status": 1,
      "created_at": "2023-06-15T10:00:00.000000Z",
      "updated_at": "2023-06-15T10:00:00.000000Z",
      "children": [
        {
          "id": 2,
          "name": "生鲜水果",
          "description": "新鲜水果和蔬菜",
          "parent_id": 1,
          "sort": 1,
          "status": 1,
          "created_at": "2023-06-15T10:00:00.000000Z",
          "updated_at": "2023-06-15T10:00:00.000000Z"
        },
        {
          "id": 3,
          "name": "休闲零食",
          "description": "各类零食小吃",
          "parent_id": 1,
          "sort": 2,
          "status": 1,
          "created_at": "2023-06-15T10:00:00.000000Z",
          "updated_at": "2023-06-15T10:00:00.000000Z"
        }
      ]
    },
    {
      "id": 4,
      "name": "日用百货",
      "description": "日常生活用品",
      "parent_id": 0,
      "sort": 2,
      "status": 1,
      "created_at": "2023-06-15T10:00:00.000000Z",
      "updated_at": "2023-06-15T10:00:00.000000Z",
      "children": [
        {
          "id": 5,
          "name": "清洁用品",
          "description": "家居清洁用品",
          "parent_id": 4,
          "sort": 1,
          "status": 1,
          "created_at": "2023-06-15T10:00:00.000000Z",
          "updated_at": "2023-06-15T10:00:00.000000Z"
        }
      ]
    }
  ]
}
```

## 3. 获取分类详情

获取指定ID的分类详情。

### 请求

```
GET /api/categories/{id}
```

### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | integer | 是 | 分类ID |

### 示例请求

```
GET /api/categories/1
```

### 响应

成功响应 (200 OK):

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "食品饮料",
    "description": "包含各类食品和饮料",
    "parent_id": 0,
    "sort": 1,
    "status": 1,
    "created_at": "2023-06-15T10:00:00.000000Z",
    "updated_at": "2023-06-15T10:00:00.000000Z"
  }
}
```

失败响应 (404 Not Found):

```json
{
  "code": 404,
  "message": "分类不存在",
  "data": null
}
```

## 4. 创建分类

创建新的商品分类。

### 请求

```
POST /api/categories
```

### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| name | string | 是 | 分类名称(最大50个字符) |
| description | string | 否 | 分类描述(最大255个字符) |
| parent_id | integer | 否 | 父级ID，默认为0(顶级分类) |
| sort | integer | 否 | 排序值，默认为0 |
| status | integer | 否 | 状态，1启用(默认)，0禁用 |

### 示例请求

```json
{
  "name": "厨房用品",
  "description": "各类厨房工具和用品",
  "parent_id": 4,
  "sort": 3,
  "status": 1
}
```

### 响应

成功响应 (200 OK):

```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 7,
    "name": "厨房用品",
    "description": "各类厨房工具和用品",
    "parent_id": 4,
    "sort": 3,
    "status": 1,
    "created_at": "2023-06-15T15:30:00.000000Z",
    "updated_at": "2023-06-15T15:30:00.000000Z"
  }
}
```

失败响应 (400 Bad Request):

```json
{
  "code": 400,
  "message": "分类名称不能为空",
  "data": null
}
```

## 5. 更新分类

更新指定ID的分类信息。

### 请求

```
PUT /api/categories/{id}
```

### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | integer | 是 | 分类ID |

### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| name | string | 否 | 分类名称(最大50个字符) |
| description | string | 否 | 分类描述(最大255个字符) |
| parent_id | integer | 否 | 父级ID |
| sort | integer | 否 | 排序值 |
| status | integer | 否 | 状态，1启用，0禁用 |

### 示例请求

```json
{
  "name": "厨房工具",
  "sort": 4
}
```

### 响应

成功响应 (200 OK):

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 7,
    "name": "厨房工具",
    "description": "各类厨房工具和用品",
    "parent_id": 4,
    "sort": 4,
    "status": 1,
    "created_at": "2023-06-15T15:30:00.000000Z",
    "updated_at": "2023-06-15T15:45:00.000000Z"
  }
}
```

失败响应 (404 Not Found):

```json
{
  "code": 404,
  "message": "分类不存在",
  "data": null
}
```

失败响应 (400 Bad Request - 不能设置自己为父类):

```json
{
  "code": 400,
  "message": "不能将分类设为自己的父类",
  "data": null
}
```

## 6. 删除分类

删除指定ID的分类。如果分类下有子分类或关联的商品，则无法删除。

### 请求

```
DELETE /api/categories/{id}
```

### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | integer | 是 | 分类ID |

### 示例请求

```
DELETE /api/categories/7
```

### 响应

成功响应 (200 OK):

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

失败响应 (404 Not Found):

```json
{
  "code": 404,
  "message": "分类不存在",
  "data": null
}
```

失败响应 (400 Bad Request - 有子分类):

```json
{
  "code": 400,
  "message": "该分类下有子分类，不能删除",
  "data": null
}
```

失败响应 (400 Bad Request - 有商品):

```json
{
  "code": 400,
  "message": "该分类下有商品，不能删除",
  "data": null
}
```

## 数据结构

### 分类对象

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| id | integer | 分类ID |
| name | string | 分类名称 |
| description | string | 分类描述 |
| parent_id | integer | 父级ID，0表示顶级分类 |
| sort | integer | 排序值，值越小越靠前 |
| status | integer | 状态：1启用，0禁用 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |
| children | array | 子分类数组(仅在tree接口中返回) |

## 错误码

| 错误码 | 描述 |
| ------ | ---- |
| 200 | 成功 |
| 400 | 参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 | 