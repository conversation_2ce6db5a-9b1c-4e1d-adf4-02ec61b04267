<?php

namespace App\Inventory\Http\Controllers;

use App\Inventory\Models\Inventory;
use App\Inventory\Models\InventoryBatch;
use App\Product\Models\Product;
use App\Unit\Services\UnitService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;

class InventoryFixController extends Controller
{
    /**
     * 单位服务
     * 
     * @var UnitService
     */
    protected $unitService;

    /**
     * 创建一个新的控制器实例
     *
     * @param UnitService $unitService
     * @return void
     */
    public function __construct(UnitService $unitService)
    {
        $this->unitService = $unitService;
    }

    /**
     * 显示修复界面
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // 获取库存信息
        $totalInventories = Inventory::count();
        $totalBatches = InventoryBatch::count();
        $totalProducts = Product::count();
        
        // 检查存在问题的单位
        $inconsistentInventories = $this->checkInconsistentInventories();
        
        return view('inventory.fix', [
            'totalInventories' => $totalInventories,
            'totalBatches' => $totalBatches,
            'totalProducts' => $totalProducts,
            'inconsistentInventories' => count($inconsistentInventories),
            'needsFix' => count($inconsistentInventories) > 0
        ]);
    }
    
    /**
     * 执行修复操作
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function fix(Request $request)
    {
        try {
            // 开始事务
            DB::beginTransaction();
            
            // 1. 修复库存单位
            $inventoryResults = $this->fixInventories();
            
            // 2. 修复批次单位
            $batchResults = $this->fixInventoryBatches();
            
            // 3. 更新所有产品的总库存
            $this->updateAllProductsStock();
            
            // 提交事务
            DB::commit();
            
            // 记录成功日志
            Log::info('库存单位修复成功', [
                'inventories' => $inventoryResults,
                'batches' => $batchResults,
            ]);
            
            // 返回成功结果
            return redirect()->route('inventory.fix.index')
                ->with('success', '库存单位修复成功！')
                ->with('results', [
                    'inventories' => $inventoryResults,
                    'batches' => $batchResults,
                ]);
                
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            
            // 记录错误日志
            Log::error('库存单位修复失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回错误信息
            return redirect()->route('inventory.fix.index')
                ->with('error', '库存单位修复失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 直接执行修复（API方式）
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function fixApi()
    {
        try {
            // 开始事务
            DB::beginTransaction();
            
            // 1. 修复库存单位
            $inventoryResults = $this->fixInventories();
            
            // 2. 修复批次单位
            $batchResults = $this->fixInventoryBatches();
            
            // 3. 更新所有产品的总库存
            $this->updateAllProductsStock();
            
            // 提交事务
            DB::commit();
            
            // 记录成功日志
            Log::info('库存单位修复成功', [
                'inventories' => $inventoryResults,
                'batches' => $batchResults,
            ]);
            
            // 返回成功结果
            return response()->json([
                'success' => true,
                'message' => '库存单位修复成功！',
                'results' => [
                    'inventories' => $inventoryResults,
                    'batches' => $batchResults,
                ]
            ]);
                
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            
            // 记录错误日志
            Log::error('库存单位修复失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回错误信息
            return response()->json([
                'success' => false,
                'message' => '库存单位修复失败: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * 检查不一致的库存单位
     *
     * @return array
     */
    protected function checkInconsistentInventories()
    {
        $inconsistentInventories = [];
        
        $inventories = Inventory::with('product')->get();
        foreach ($inventories as $inventory) {
            if ($inventory->product && $inventory->product->base_unit_id && $inventory->unit_id != $inventory->product->base_unit_id) {
                $inconsistentInventories[] = [
                    'id' => $inventory->id,
                    'product_id' => $inventory->product_id,
                    'product_name' => $inventory->product->name ?? '未知',
                    'current_unit_id' => $inventory->unit_id,
                    'base_unit_id' => $inventory->product->base_unit_id,
                ];
            }
        }
        
        return $inconsistentInventories;
    }
    
    /**
     * 修复库存单位
     *
     * @param bool $logDetails 是否记录详细日志
     * @return array
     */
    protected function fixInventories($logDetails = true)
    {
        // 获取所有库存记录
        /** @var \Illuminate\Database\Eloquent\Collection $inventories */
        $inventories = Inventory::with(['product'])->get();
        $totalCount = $inventories->count();
        $successCount = 0;
        $errorCount = 0;
        $details = [];
        
        foreach ($inventories as $inventory) {
            try {
                /** @var \App\Inventory\Models\Inventory $inventory */
                $product = $inventory->product;
                
                // 如果产品不存在，记录错误并跳过
                if (!$product) {
                    $details[] = [
                        'inventory_id' => $inventory->id,
                        'status' => 'error',
                        'message' => '找不到产品'
                    ];
                    $errorCount++;
                    continue;
                }
                
                // 获取产品的基本单位ID
                $baseUnitId = $product->base_unit_id;
                
                // 如果库存已经是基本单位，不需要转换
                if ($inventory->unit_id === $baseUnitId) {
                    $successCount++;
                    continue;
                }
                
                // 获取从当前单位到基本单位的转换率
                $conversionRate = $product->getUnitConversionRate(
                    $inventory->unit_id, 
                    $baseUnitId
                );
                
                // 如果无法获取转换率，记录错误并跳过
                if ($conversionRate === null) {
                    $details[] = [
                        'inventory_id' => $inventory->id,
                        'status' => 'error',
                        'message' => '无法获取转换率'
                    ];
                    $errorCount++;
                    continue;
                }
                
                // 计算基本单位下的库存量
                $stockInBaseUnit = $inventory->stock * $conversionRate;
                $oldStock = $inventory->stock;
                $oldUnitId = $inventory->unit_id;
                
                // 更新库存单位和数量
                $inventory->stock = $stockInBaseUnit;
                $inventory->unit_id = $baseUnitId;
                $inventory->save();
                
                if ($logDetails) {
                    Log::info('库存单位转换成功', [
                        'inventory_id' => $inventory->id,
                        'product_id' => $inventory->product_id,
                        'from_unit_id' => $oldUnitId,
                        'to_unit_id' => $baseUnitId,
                        'old_stock' => $oldStock,
                        'new_stock' => $stockInBaseUnit
                    ]);
                }
                
                $details[] = [
                    'inventory_id' => $inventory->id,
                    'status' => 'success',
                    'message' => "单位从{$oldUnitId}转换到{$baseUnitId}，库存从{$oldStock}修改为{$stockInBaseUnit}"
                ];
                
                $successCount++;
            } catch (\Exception $e) {
                $details[] = [
                    'inventory_id' => $inventory->id,
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
                $errorCount++;
            }
        }
        
        return [
            'total' => $totalCount,
            'success' => $successCount,
            'error' => $errorCount,
            'details' => $details
        ];
    }
    
    /**
     * 修复库存批次单位
     *
     * @param bool $logDetails 是否记录详细日志
     * @return array
     */
    protected function fixInventoryBatches($logDetails = true)
    {
        // 获取所有库存批次记录
        /** @var \Illuminate\Database\Eloquent\Collection $batches */
        $batches = InventoryBatch::with(['inventory', 'inventory.product'])->get();
        $totalCount = $batches->count();
        $successCount = 0;
        $errorCount = 0;
        $details = [];
        
        foreach ($batches as $batch) {
            try {
                /** @var \App\Inventory\Models\InventoryBatch $batch */
                $inventory = $batch->inventory;
                
                // 如果库存不存在，记录错误并跳过
                if (!$inventory) {
                    $details[] = [
                        'batch_id' => $batch->id,
                        'status' => 'error',
                        'message' => '找不到库存记录'
                    ];
                    $errorCount++;
                    continue;
                }
                
                $product = $inventory->product;
                
                // 如果产品不存在，记录错误并跳过
                if (!$product) {
                    $details[] = [
                        'batch_id' => $batch->id,
                        'status' => 'error',
                        'message' => '找不到产品'
                    ];
                    $errorCount++;
                    continue;
                }
                
                // 获取产品的基本单位ID
                $baseUnitId = $product->base_unit_id;
                
                // 如果批次已经是基本单位，不需要转换
                if ($batch->unit_id === $baseUnitId) {
                    $successCount++;
                    continue;
                }
                
                // 获取从当前单位到基本单位的转换率
                $conversionRate = $product->getUnitConversionRate(
                    $batch->unit_id, 
                    $baseUnitId
                );
                
                // 如果无法获取转换率，记录错误并跳过
                if ($conversionRate === null) {
                    $details[] = [
                        'batch_id' => $batch->id,
                        'status' => 'error',
                        'message' => '无法获取转换率'
                    ];
                    $errorCount++;
                    continue;
                }
                
                // 保存原始值用于日志
                $oldUnitId = $batch->unit_id;
                $oldQuantity = $batch->quantity;
                $oldInitialQuantity = $batch->initial_quantity;
                
                // 计算基本单位下的数量
                $quantityInBaseUnit = $batch->quantity * $conversionRate;
                $initialQuantityInBaseUnit = $batch->initial_quantity * $conversionRate;
                
                // 更新批次单位和数量
                $batch->quantity = $quantityInBaseUnit;
                $batch->initial_quantity = $initialQuantityInBaseUnit;
                $batch->unit_id = $baseUnitId;
                $batch->save();
                
                if ($logDetails) {
                    Log::info('批次单位转换成功', [
                        'batch_id' => $batch->id,
                        'from_unit_id' => $oldUnitId,
                        'to_unit_id' => $baseUnitId,
                        'old_quantity' => $oldQuantity,
                        'new_quantity' => $quantityInBaseUnit,
                        'old_initial_quantity' => $oldInitialQuantity,
                        'new_initial_quantity' => $initialQuantityInBaseUnit
                    ]);
                }
                
                $details[] = [
                    'batch_id' => $batch->id,
                    'status' => 'success',
                    'message' => "单位从{$oldUnitId}转换到{$baseUnitId}，数量从{$oldQuantity}修改为{$quantityInBaseUnit}"
                ];
                
                $successCount++;
            } catch (\Exception $e) {
                $details[] = [
                    'batch_id' => $batch->id,
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
                $errorCount++;
            }
        }
        
        return [
            'total' => $totalCount,
            'success' => $successCount,
            'error' => $errorCount,
            'details' => $details
        ];
    }
    
    /**
     * 更新所有产品的总库存
     *
     * @return void
     */
    protected function updateAllProductsStock()
    {
        $products = Product::all();
        foreach ($products as $product) {
            $product->updateTotalStock();
        }
    }

    /**
     * 获取系统概览
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSystemOverview()
    {
        try {
            // 获取库存系统概览数据
            $summary = [
                'total_inventory_items' => Inventory::count(),
                'total_warehouses' => DB::table('warehouses')->count(),
                'total_batches' => InventoryBatch::count(),
                'total_transactions' => DB::table('inventory_transactions')->count(),
                // 单位相关统计由Unit模块负责，这里不再包含
            ];
            
            return response()->json([
                'success' => true,
                'data' => $summary
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取系统概览失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 检查并修复单位
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkAndRepairUnits()
    {
        try {
            // 检查不一致的库存单位
            $inconsistentInventories = $this->checkInconsistentInventories();
            
            if (count($inconsistentInventories) === 0) {
                return response()->json([
                    'success' => true,
                    'message' => '所有库存单位已经一致，无需修复'
                ]);
            }
            
            // 执行修复
            $result = $this->fixApi();
            
            return $result;
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '检查并修复单位失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 运行单位转换命令
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function runConversionCommand(Request $request)
    {
        try {
            // 执行单位转换
            $command = $request->input('command', 'fix');
            
            if ($command === 'fix') {
                return $this->fixApi();
            }
            
            return response()->json([
                'success' => false,
                'message' => '未知命令: ' . $command
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '运行单位转换命令失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 