<?php

namespace App\Region\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class RegionResource extends JsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'parent_id' => $this->parent_id,
            'level' => $this->level,
            'status' => $this->status,
            'sort' => $this->sort,
            'full_name' => $this->full_name,
            'metadata' => $this->metadata,
            'created_at' => $this->created_at ? $this->created_at->format('Y-m-d H:i:s') : null,
            'updated_at' => $this->updated_at ? $this->updated_at->format('Y-m-d H:i:s') : null,
            
            // 如果加载了关联数据，则包含它们
            'parent' => $this->whenLoaded('parent', function () {
                return new RegionResource($this->parent);
            }),
            'children' => $this->whenLoaded('children', function () {
                return RegionResource::collection($this->children);
            }),
            'children_data' => $this->when(isset($this->children_data), function () {
                return RegionResource::collection($this->children_data);
            }),
            'has_children' => $this->when(isset($this->has_children), function () {
                return $this->has_children;
            }, function () {
                // 如果has_children属性未设置，但children关系已加载，则计算
                if ($this->relationLoaded('children')) {
                    return $this->children->isNotEmpty();
                }
                return null;
            }),
        ];
    }
} 