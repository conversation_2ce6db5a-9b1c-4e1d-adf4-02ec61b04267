/* 水平间距 */
/* 水平间距 */
.uniui-cart-filled[data-v-d31e1c47]:before {
  content: "\e6d0";
}
.uniui-gift-filled[data-v-d31e1c47]:before {
  content: "\e6c4";
}
.uniui-color[data-v-d31e1c47]:before {
  content: "\e6cf";
}
.uniui-wallet[data-v-d31e1c47]:before {
  content: "\e6b1";
}
.uniui-settings-filled[data-v-d31e1c47]:before {
  content: "\e6ce";
}
.uniui-auth-filled[data-v-d31e1c47]:before {
  content: "\e6cc";
}
.uniui-shop-filled[data-v-d31e1c47]:before {
  content: "\e6cd";
}
.uniui-staff-filled[data-v-d31e1c47]:before {
  content: "\e6cb";
}
.uniui-vip-filled[data-v-d31e1c47]:before {
  content: "\e6c6";
}
.uniui-plus-filled[data-v-d31e1c47]:before {
  content: "\e6c7";
}
.uniui-folder-add-filled[data-v-d31e1c47]:before {
  content: "\e6c8";
}
.uniui-color-filled[data-v-d31e1c47]:before {
  content: "\e6c9";
}
.uniui-tune-filled[data-v-d31e1c47]:before {
  content: "\e6ca";
}
.uniui-calendar-filled[data-v-d31e1c47]:before {
  content: "\e6c0";
}
.uniui-notification-filled[data-v-d31e1c47]:before {
  content: "\e6c1";
}
.uniui-wallet-filled[data-v-d31e1c47]:before {
  content: "\e6c2";
}
.uniui-medal-filled[data-v-d31e1c47]:before {
  content: "\e6c3";
}
.uniui-fire-filled[data-v-d31e1c47]:before {
  content: "\e6c5";
}
.uniui-refreshempty[data-v-d31e1c47]:before {
  content: "\e6bf";
}
.uniui-location-filled[data-v-d31e1c47]:before {
  content: "\e6af";
}
.uniui-person-filled[data-v-d31e1c47]:before {
  content: "\e69d";
}
.uniui-personadd-filled[data-v-d31e1c47]:before {
  content: "\e698";
}
.uniui-arrowthinleft[data-v-d31e1c47]:before {
  content: "\e6d2";
}
.uniui-arrowthinup[data-v-d31e1c47]:before {
  content: "\e6d3";
}
.uniui-arrowthindown[data-v-d31e1c47]:before {
  content: "\e6d4";
}
.uniui-back[data-v-d31e1c47]:before {
  content: "\e6b9";
}
.uniui-forward[data-v-d31e1c47]:before {
  content: "\e6ba";
}
.uniui-arrow-right[data-v-d31e1c47]:before {
  content: "\e6bb";
}
.uniui-arrow-left[data-v-d31e1c47]:before {
  content: "\e6bc";
}
.uniui-arrow-up[data-v-d31e1c47]:before {
  content: "\e6bd";
}
.uniui-arrow-down[data-v-d31e1c47]:before {
  content: "\e6be";
}
.uniui-arrowthinright[data-v-d31e1c47]:before {
  content: "\e6d1";
}
.uniui-down[data-v-d31e1c47]:before {
  content: "\e6b8";
}
.uniui-bottom[data-v-d31e1c47]:before {
  content: "\e6b8";
}
.uniui-arrowright[data-v-d31e1c47]:before {
  content: "\e6d5";
}
.uniui-right[data-v-d31e1c47]:before {
  content: "\e6b5";
}
.uniui-up[data-v-d31e1c47]:before {
  content: "\e6b6";
}
.uniui-top[data-v-d31e1c47]:before {
  content: "\e6b6";
}
.uniui-left[data-v-d31e1c47]:before {
  content: "\e6b7";
}
.uniui-arrowup[data-v-d31e1c47]:before {
  content: "\e6d6";
}
.uniui-eye[data-v-d31e1c47]:before {
  content: "\e651";
}
.uniui-eye-filled[data-v-d31e1c47]:before {
  content: "\e66a";
}
.uniui-eye-slash[data-v-d31e1c47]:before {
  content: "\e6b3";
}
.uniui-eye-slash-filled[data-v-d31e1c47]:before {
  content: "\e6b4";
}
.uniui-info-filled[data-v-d31e1c47]:before {
  content: "\e649";
}
.uniui-reload[data-v-d31e1c47]:before {
  content: "\e6b2";
}
.uniui-micoff-filled[data-v-d31e1c47]:before {
  content: "\e6b0";
}
.uniui-map-pin-ellipse[data-v-d31e1c47]:before {
  content: "\e6ac";
}
.uniui-map-pin[data-v-d31e1c47]:before {
  content: "\e6ad";
}
.uniui-location[data-v-d31e1c47]:before {
  content: "\e6ae";
}
.uniui-starhalf[data-v-d31e1c47]:before {
  content: "\e683";
}
.uniui-star[data-v-d31e1c47]:before {
  content: "\e688";
}
.uniui-star-filled[data-v-d31e1c47]:before {
  content: "\e68f";
}
.uniui-calendar[data-v-d31e1c47]:before {
  content: "\e6a0";
}
.uniui-fire[data-v-d31e1c47]:before {
  content: "\e6a1";
}
.uniui-medal[data-v-d31e1c47]:before {
  content: "\e6a2";
}
.uniui-font[data-v-d31e1c47]:before {
  content: "\e6a3";
}
.uniui-gift[data-v-d31e1c47]:before {
  content: "\e6a4";
}
.uniui-link[data-v-d31e1c47]:before {
  content: "\e6a5";
}
.uniui-notification[data-v-d31e1c47]:before {
  content: "\e6a6";
}
.uniui-staff[data-v-d31e1c47]:before {
  content: "\e6a7";
}
.uniui-vip[data-v-d31e1c47]:before {
  content: "\e6a8";
}
.uniui-folder-add[data-v-d31e1c47]:before {
  content: "\e6a9";
}
.uniui-tune[data-v-d31e1c47]:before {
  content: "\e6aa";
}
.uniui-auth[data-v-d31e1c47]:before {
  content: "\e6ab";
}
.uniui-person[data-v-d31e1c47]:before {
  content: "\e699";
}
.uniui-email-filled[data-v-d31e1c47]:before {
  content: "\e69a";
}
.uniui-phone-filled[data-v-d31e1c47]:before {
  content: "\e69b";
}
.uniui-phone[data-v-d31e1c47]:before {
  content: "\e69c";
}
.uniui-email[data-v-d31e1c47]:before {
  content: "\e69e";
}
.uniui-personadd[data-v-d31e1c47]:before {
  content: "\e69f";
}
.uniui-chatboxes-filled[data-v-d31e1c47]:before {
  content: "\e692";
}
.uniui-contact[data-v-d31e1c47]:before {
  content: "\e693";
}
.uniui-chatbubble-filled[data-v-d31e1c47]:before {
  content: "\e694";
}
.uniui-contact-filled[data-v-d31e1c47]:before {
  content: "\e695";
}
.uniui-chatboxes[data-v-d31e1c47]:before {
  content: "\e696";
}
.uniui-chatbubble[data-v-d31e1c47]:before {
  content: "\e697";
}
.uniui-upload-filled[data-v-d31e1c47]:before {
  content: "\e68e";
}
.uniui-upload[data-v-d31e1c47]:before {
  content: "\e690";
}
.uniui-weixin[data-v-d31e1c47]:before {
  content: "\e691";
}
.uniui-compose[data-v-d31e1c47]:before {
  content: "\e67f";
}
.uniui-qq[data-v-d31e1c47]:before {
  content: "\e680";
}
.uniui-download-filled[data-v-d31e1c47]:before {
  content: "\e681";
}
.uniui-pyq[data-v-d31e1c47]:before {
  content: "\e682";
}
.uniui-sound[data-v-d31e1c47]:before {
  content: "\e684";
}
.uniui-trash-filled[data-v-d31e1c47]:before {
  content: "\e685";
}
.uniui-sound-filled[data-v-d31e1c47]:before {
  content: "\e686";
}
.uniui-trash[data-v-d31e1c47]:before {
  content: "\e687";
}
.uniui-videocam-filled[data-v-d31e1c47]:before {
  content: "\e689";
}
.uniui-spinner-cycle[data-v-d31e1c47]:before {
  content: "\e68a";
}
.uniui-weibo[data-v-d31e1c47]:before {
  content: "\e68b";
}
.uniui-videocam[data-v-d31e1c47]:before {
  content: "\e68c";
}
.uniui-download[data-v-d31e1c47]:before {
  content: "\e68d";
}
.uniui-help[data-v-d31e1c47]:before {
  content: "\e679";
}
.uniui-navigate-filled[data-v-d31e1c47]:before {
  content: "\e67a";
}
.uniui-plusempty[data-v-d31e1c47]:before {
  content: "\e67b";
}
.uniui-smallcircle[data-v-d31e1c47]:before {
  content: "\e67c";
}
.uniui-minus-filled[data-v-d31e1c47]:before {
  content: "\e67d";
}
.uniui-micoff[data-v-d31e1c47]:before {
  content: "\e67e";
}
.uniui-closeempty[data-v-d31e1c47]:before {
  content: "\e66c";
}
.uniui-clear[data-v-d31e1c47]:before {
  content: "\e66d";
}
.uniui-navigate[data-v-d31e1c47]:before {
  content: "\e66e";
}
.uniui-minus[data-v-d31e1c47]:before {
  content: "\e66f";
}
.uniui-image[data-v-d31e1c47]:before {
  content: "\e670";
}
.uniui-mic[data-v-d31e1c47]:before {
  content: "\e671";
}
.uniui-paperplane[data-v-d31e1c47]:before {
  content: "\e672";
}
.uniui-close[data-v-d31e1c47]:before {
  content: "\e673";
}
.uniui-help-filled[data-v-d31e1c47]:before {
  content: "\e674";
}
.uniui-paperplane-filled[data-v-d31e1c47]:before {
  content: "\e675";
}
.uniui-plus[data-v-d31e1c47]:before {
  content: "\e676";
}
.uniui-mic-filled[data-v-d31e1c47]:before {
  content: "\e677";
}
.uniui-image-filled[data-v-d31e1c47]:before {
  content: "\e678";
}
.uniui-locked-filled[data-v-d31e1c47]:before {
  content: "\e668";
}
.uniui-info[data-v-d31e1c47]:before {
  content: "\e669";
}
.uniui-locked[data-v-d31e1c47]:before {
  content: "\e66b";
}
.uniui-camera-filled[data-v-d31e1c47]:before {
  content: "\e658";
}
.uniui-chat-filled[data-v-d31e1c47]:before {
  content: "\e659";
}
.uniui-camera[data-v-d31e1c47]:before {
  content: "\e65a";
}
.uniui-circle[data-v-d31e1c47]:before {
  content: "\e65b";
}
.uniui-checkmarkempty[data-v-d31e1c47]:before {
  content: "\e65c";
}
.uniui-chat[data-v-d31e1c47]:before {
  content: "\e65d";
}
.uniui-circle-filled[data-v-d31e1c47]:before {
  content: "\e65e";
}
.uniui-flag[data-v-d31e1c47]:before {
  content: "\e65f";
}
.uniui-flag-filled[data-v-d31e1c47]:before {
  content: "\e660";
}
.uniui-gear-filled[data-v-d31e1c47]:before {
  content: "\e661";
}
.uniui-home[data-v-d31e1c47]:before {
  content: "\e662";
}
.uniui-home-filled[data-v-d31e1c47]:before {
  content: "\e663";
}
.uniui-gear[data-v-d31e1c47]:before {
  content: "\e664";
}
.uniui-smallcircle-filled[data-v-d31e1c47]:before {
  content: "\e665";
}
.uniui-map-filled[data-v-d31e1c47]:before {
  content: "\e666";
}
.uniui-map[data-v-d31e1c47]:before {
  content: "\e667";
}
.uniui-refresh-filled[data-v-d31e1c47]:before {
  content: "\e656";
}
.uniui-refresh[data-v-d31e1c47]:before {
  content: "\e657";
}
.uniui-cloud-upload[data-v-d31e1c47]:before {
  content: "\e645";
}
.uniui-cloud-download-filled[data-v-d31e1c47]:before {
  content: "\e646";
}
.uniui-cloud-download[data-v-d31e1c47]:before {
  content: "\e647";
}
.uniui-cloud-upload-filled[data-v-d31e1c47]:before {
  content: "\e648";
}
.uniui-redo[data-v-d31e1c47]:before {
  content: "\e64a";
}
.uniui-images-filled[data-v-d31e1c47]:before {
  content: "\e64b";
}
.uniui-undo-filled[data-v-d31e1c47]:before {
  content: "\e64c";
}
.uniui-more[data-v-d31e1c47]:before {
  content: "\e64d";
}
.uniui-more-filled[data-v-d31e1c47]:before {
  content: "\e64e";
}
.uniui-undo[data-v-d31e1c47]:before {
  content: "\e64f";
}
.uniui-images[data-v-d31e1c47]:before {
  content: "\e650";
}
.uniui-paperclip[data-v-d31e1c47]:before {
  content: "\e652";
}
.uniui-settings[data-v-d31e1c47]:before {
  content: "\e653";
}
.uniui-search[data-v-d31e1c47]:before {
  content: "\e654";
}
.uniui-redo-filled[data-v-d31e1c47]:before {
  content: "\e655";
}
.uniui-list[data-v-d31e1c47]:before {
  content: "\e644";
}
.uniui-mail-open-filled[data-v-d31e1c47]:before {
  content: "\e63a";
}
.uniui-hand-down-filled[data-v-d31e1c47]:before {
  content: "\e63c";
}
.uniui-hand-down[data-v-d31e1c47]:before {
  content: "\e63d";
}
.uniui-hand-up-filled[data-v-d31e1c47]:before {
  content: "\e63e";
}
.uniui-hand-up[data-v-d31e1c47]:before {
  content: "\e63f";
}
.uniui-heart-filled[data-v-d31e1c47]:before {
  content: "\e641";
}
.uniui-mail-open[data-v-d31e1c47]:before {
  content: "\e643";
}
.uniui-heart[data-v-d31e1c47]:before {
  content: "\e639";
}
.uniui-loop[data-v-d31e1c47]:before {
  content: "\e633";
}
.uniui-pulldown[data-v-d31e1c47]:before {
  content: "\e632";
}
.uniui-scan[data-v-d31e1c47]:before {
  content: "\e62a";
}
.uniui-bars[data-v-d31e1c47]:before {
  content: "\e627";
}
.uniui-checkbox[data-v-d31e1c47]:before {
  content: "\e62b";
}
.uniui-checkbox-filled[data-v-d31e1c47]:before {
  content: "\e62c";
}
.uniui-shop[data-v-d31e1c47]:before {
  content: "\e62f";
}
.uniui-headphones[data-v-d31e1c47]:before {
  content: "\e630";
}
.uniui-cart[data-v-d31e1c47]:before {
  content: "\e631";
}
@font-face {
  font-family: uniicons;
  src: url("../../assets/uniicons.32e978a5.ttf");
}
.uni-icons[data-v-d31e1c47] {
  font-family: uniicons;
  text-decoration: none;
  text-align: center;
}
/* 水平间距 */
/* 水平间距 */
.uni-badge--x[data-v-c97cb896] {
  display: inline-block;
  position: relative;
}
.uni-badge--absolute[data-v-c97cb896] {
  position: absolute;
}
.uni-badge--small[data-v-c97cb896] {
  transform: scale(0.8);
  transform-origin: center center;
}
.uni-badge[data-v-c97cb896] {
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
  font-feature-settings: "tnum";
  min-width: 20px;
  justify-content: center;
  flex-direction: row;
  height: 20px;
  padding: 0 4px;
  line-height: 18px;
  color: #fff;
  border-radius: 100px;
  background-color: #8f939c;
  background-color: transparent;
  border: 1px solid #fff;
  text-align: center;
  font-family: "Helvetica Neue", Helvetica, sans-serif;
  font-size: 12px;
}
.uni-badge--info[data-v-c97cb896] {
  color: #fff;
  background-color: #8f939c;
}
.uni-badge--primary[data-v-c97cb896] {
  background-color: #2979ff;
}
.uni-badge--success[data-v-c97cb896] {
  background-color: #18bc37;
}
.uni-badge--warning[data-v-c97cb896] {
  background-color: #f3a73f;
}
.uni-badge--error[data-v-c97cb896] {
  background-color: #e43d33;
}
.uni-badge--inverted[data-v-c97cb896] {
  padding: 0 5px 0 0;
  color: #8f939c;
}
.uni-badge--info-inverted[data-v-c97cb896] {
  color: #8f939c;
  background-color: transparent;
}
.uni-badge--primary-inverted[data-v-c97cb896] {
  color: #2979ff;
  background-color: transparent;
}
.uni-badge--success-inverted[data-v-c97cb896] {
  color: #18bc37;
  background-color: transparent;
}
.uni-badge--warning-inverted[data-v-c97cb896] {
  color: #f3a73f;
  background-color: transparent;
}
.uni-badge--error-inverted[data-v-c97cb896] {
  color: #e43d33;
  background-color: transparent;
}
/* 水平间距 */
/* 水平间距 */
.uni-list-item[data-v-c7524739] {
  display: flex;
  font-size: 16px;
  position: relative;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  flex-direction: row;
}
.uni-list-item--disabled[data-v-c7524739] {
  opacity: 0.3;
}
.uni-list-item--hover[data-v-c7524739] {
  background-color: #f1f1f1;
}
.uni-list-item__container[data-v-c7524739] {
  position: relative;
  display: flex;
  flex-direction: row;
  padding: 12px 15px;
  padding-left: 15px;
  flex: 1;
  overflow: hidden;
}
.container--right[data-v-c7524739] {
  padding-right: 0;
}
.uni-list--border[data-v-c7524739] {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}
.uni-list--border[data-v-c7524739]:after {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: "";
  transform: scaleY(0.5);
  background-color: #e5e5e5;
}
.uni-list-item__content[data-v-c7524739] {
  display: flex;
  padding-right: 8px;
  flex: 1;
  color: #3b4144;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}
.uni-list-item__content--center[data-v-c7524739] {
  justify-content: center;
}
.uni-list-item__content-title[data-v-c7524739] {
  font-size: 14px;
  color: #3b4144;
  overflow: hidden;
}
.uni-list-item__content-note[data-v-c7524739] {
  margin-top: 0.1875rem;
  color: #999;
  font-size: 12px;
  overflow: hidden;
}
.uni-list-item__extra[data-v-c7524739] {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.uni-list-item__header[data-v-c7524739] {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uni-list-item__icon[data-v-c7524739] {
  margin-right: 0.5625rem;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.uni-list-item__icon-img[data-v-c7524739] {
  display: block;
  height: 26px;
  width: 26px;
  margin-right: 10px;
}
.uni-icon-wrapper[data-v-c7524739] {
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.flex--direction[data-v-c7524739] {
  flex-direction: column;
  align-items: initial;
}
.flex--justify[data-v-c7524739] {
  justify-content: initial;
}
.uni-list--lg[data-v-c7524739] {
  height: 40px;
  width: 40px;
}
.uni-list--base[data-v-c7524739] {
  height: 26px;
  width: 26px;
}
.uni-list--sm[data-v-c7524739] {
  height: 20px;
  width: 20px;
}
.uni-list-item__extra-text[data-v-c7524739] {
  color: #999;
  font-size: 12px;
}
.uni-ellipsis-1[data-v-c7524739] {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.uni-ellipsis-2[data-v-c7524739] {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
/* 水平间距 */
/* 水平间距 */
.uni-list[data-v-c2f1266a] {
  display: flex;
  background-color: #ffffff;
  position: relative;
  flex-direction: column;
}
.uni-list--border[data-v-c2f1266a] {
  position: relative;
  z-index: -1;
}
.uni-list--border-top[data-v-c2f1266a] {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  transform: scaleY(0.5);
  background-color: #e5e5e5;
  z-index: 1;
}
.uni-list--border-bottom[data-v-c2f1266a] {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  height: 1px;
  transform: scaleY(0.5);
  background-color: #e5e5e5;
}
/* 水平间距 */
/* 水平间距 */
.uni-section[data-v-637fd36b] {
  background-color: #fff;
}
.uni-section .uni-section-header[data-v-637fd36b] {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px 10px;
  font-weight: normal;
}
.uni-section .uni-section-header__decoration[data-v-637fd36b] {
  margin-right: 6px;
  background-color: #2979ff;
}
.uni-section .uni-section-header__decoration.line[data-v-637fd36b] {
  width: 4px;
  height: 12px;
  border-radius: 10px;
}
.uni-section .uni-section-header__decoration.circle[data-v-637fd36b] {
  width: 8px;
  height: 8px;
  border-top-right-radius: 50px;
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;
  border-bottom-right-radius: 50px;
}
.uni-section .uni-section-header__decoration.square[data-v-637fd36b] {
  width: 8px;
  height: 8px;
}
.uni-section .uni-section-header__content[data-v-637fd36b] {
  display: flex;
  flex-direction: column;
  flex: 1;
  color: #333;
}
.uni-section .uni-section-header__content .distraction[data-v-637fd36b] {
  flex-direction: row;
  align-items: center;
}
.uni-section .uni-section-header__content-sub[data-v-637fd36b] {
  margin-top: 2px;
}
.uni-section .uni-section-header__slot-right[data-v-637fd36b] {
  font-size: 14px;
}
.uni-section .uni-section-content[data-v-637fd36b] {
  font-size: 14px;
}
/* 水平间距 */
/* 水平间距 */
.profile-container {
  padding-bottom: 1.25rem;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.user-card {
  display: flex;
  align-items: center;
  padding: 1.25rem 0.9375rem;
  background-color: #007AFF;
  position: relative;
}
.user-card .avatar {
  width: 3.75rem;
  height: 3.75rem;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.9375rem;
  border: 0.125rem solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}
.user-card .avatar .avatar-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}
.user-card .user-info {
  color: #ffffff;
  flex: 1;
}
.user-card .user-info .name {
  font-size: 1.125rem;
  font-weight: bold;
  margin-bottom: 0.3125rem;
  display: block;
}
.user-card .user-info .phone {
  font-size: 0.875rem;
  opacity: 0.8;
  display: block;
}
.user-card .status-indicator {
  padding: 0.25rem 0.625rem;
  border-radius: 0.9375rem;
  font-size: 0.75rem;
}
.user-card .status-indicator.available {
  background-color: #4caf50;
  color: white;
}
.user-card .status-indicator.busy {
  background-color: #ff9800;
  color: white;
}
.user-card .status-indicator.offline {
  background-color: rgba(255, 255, 255, 0.3);
  color: white;
}
.logout-btn {
  margin: 1.875rem 0.9375rem 0.9375rem;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 1rem;
  padding: 0.625rem 0;
}