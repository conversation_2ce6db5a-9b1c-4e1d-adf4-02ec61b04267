<?php

namespace App\Unit\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UnitProperty extends Model
{
    use HasFactory;

    protected $fillable = [
        'unit_id', 'key', 'value'
    ];

    /**
     * 关联单位
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }
} 