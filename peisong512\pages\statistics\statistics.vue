<template>
	<view class="statistics-container">
		<!-- 顶部时间选择器 -->
		<view class="time-selector">
			<uni-segmented-control :current="currentPeriod" :values="periodTabs" @clickItem="onPeriodChange"></uni-segmented-control>
		</view>
		
		<!-- 核心数据卡片 -->
		<view class="stats-cards">
			<view class="stats-card primary">
				<view class="card-icon">
					<uni-icons type="list" size="24" color="#fff"></uni-icons>
				</view>
				<view class="card-content">
					<text class="card-number">{{ stats.totalOrders }}</text>
					<text class="card-label">总订单数</text>
					<text class="card-trend" :class="getTrendClass(stats.ordersTrend)">
						{{ formatTrend(stats.ordersTrend) }}
					</text>
				</view>
			</view>
			
			<view class="stats-card success">
				<view class="card-icon">
					<uni-icons type="checkmarkempty" size="24" color="#fff"></uni-icons>
				</view>
				<view class="card-content">
					<text class="card-number">{{ stats.completedOrders }}</text>
					<text class="card-label">已完成</text>
					<text class="card-trend" :class="getTrendClass(stats.completedTrend)">
						{{ formatTrend(stats.completedTrend) }}
					</text>
				</view>
			</view>
			
			<view class="stats-card warning">
				<view class="card-icon">
					<uni-icons type="clock" size="24" color="#fff"></uni-icons>
				</view>
				<view class="card-content">
					<text class="card-number">{{ stats.avgTime }}min</text>
					<text class="card-label">平均用时</text>
					<text class="card-trend" :class="getTrendClass(-stats.timeTrend)">
						{{ formatTrend(-stats.timeTrend) }}
					</text>
				</view>
			</view>
			
			<view class="stats-card info">
				<view class="card-icon">
					<uni-icons type="star" size="24" color="#fff"></uni-icons>
				</view>
				<view class="card-content">
					<text class="card-number">{{ stats.rating }}</text>
					<text class="card-label">评分</text>
					<text class="card-trend" :class="getTrendClass(stats.ratingTrend)">
						{{ formatTrend(stats.ratingTrend) }}
					</text>
				</view>
			</view>
		</view>
		
		<!-- 完成率环形图 -->
		<view class="completion-section">
			<uni-card title="完成率统计" :is-shadow="false">
				<view class="completion-chart">
					<view class="chart-circle">
						<view class="circle-progress" :style="getCircleStyle()">
							<view class="circle-inner">
								<text class="completion-rate">{{ stats.completionRate }}%</text>
								<text class="completion-label">完成率</text>
							</view>
						</view>
					</view>
					<view class="completion-details">
						<view class="detail-item">
							<view class="detail-dot pending"></view>
							<text class="detail-label">待配送</text>
							<text class="detail-value">{{ stats.pendingOrders }}</text>
						</view>
						<view class="detail-item">
							<view class="detail-dot progress"></view>
							<text class="detail-label">配送中</text>
							<text class="detail-value">{{ stats.inProgressOrders }}</text>
						</view>
						<view class="detail-item">
							<view class="detail-dot completed"></view>
							<text class="detail-label">已完成</text>
							<text class="detail-value">{{ stats.completedOrders }}</text>
						</view>
					</view>
				</view>
			</uni-card>
		</view>
		
		<!-- 配送时段分析 -->
		<view class="time-analysis">
			<uni-card title="配送时段分析" :is-shadow="false">
				<view class="time-chart">
					<view class="time-bars">
						<view 
							v-for="(item, index) in timeDistribution" 
							:key="index" 
							class="time-bar-item"
						>
							<view class="time-bar">
								<view 
									class="time-bar-fill" 
									:style="{ height: (item.count / maxTimeCount * 100) + '%' }"
								></view>
							</view>
							<text class="time-label">{{ item.hour }}:00</text>
							<text class="time-count">{{ item.count }}</text>
						</view>
					</view>
				</view>
			</uni-card>
		</view>
		
		<!-- 配送区域统计 -->
		<view class="area-stats">
			<uni-card title="配送区域统计" :is-shadow="false">
				<view class="area-list">
					<view 
						v-for="(area, index) in areaStats" 
						:key="index" 
						class="area-item"
					>
						<view class="area-info">
							<text class="area-name">{{ area.name }}</text>
							<text class="area-count">{{ area.count }}单</text>
						</view>
						<view class="area-progress">
							<view 
								class="area-progress-fill" 
								:style="{ width: (area.count / maxAreaCount * 100) + '%' }"
							></view>
						</view>
						<text class="area-percentage">{{ ((area.count / stats.totalOrders) * 100).toFixed(1) }}%</text>
					</view>
				</view>
			</uni-card>
		</view>
		
		<!-- 个人排名 -->
		<view class="ranking-section">
			<uni-card title="团队排名" :is-shadow="false">
				<view class="ranking-list">
					<view class="ranking-header">
						<text class="rank-col">排名</text>
						<text class="name-col">配送员</text>
						<text class="orders-col">完成量</text>
						<text class="rate-col">完成率</text>
					</view>
					<view 
						v-for="(item, index) in rankingList" 
						:key="index" 
						class="ranking-item"
						:class="{ 'current-user': item.isCurrentUser }"
					>
						<view class="rank-col">
							<view v-if="index < 3" class="rank-medal" :class="'medal-' + (index + 1)">
								{{ index + 1 }}
							</view>
							<text v-else class="rank-number">{{ index + 1 }}</text>
						</view>
						<view class="name-col">
							<text class="deliverer-name">{{ item.name }}</text>
							<text v-if="item.isCurrentUser" class="current-tag">我</text>
						</view>
						<text class="orders-col">{{ item.completedOrders }}</text>
						<text class="rate-col">{{ item.completionRate }}%</text>
					</view>
				</view>
			</uni-card>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentPeriod: 0,
			periodTabs: ['今日', '本周', '本月'],
			stats: {
				totalOrders: 0,
				completedOrders: 0,
				pendingOrders: 0,
				inProgressOrders: 0,
				completionRate: 0,
				avgTime: 0,
				rating: 0,
				ordersTrend: 0,
				completedTrend: 0,
				timeTrend: 0,
				ratingTrend: 0
			},
			timeDistribution: [],
			maxTimeCount: 0,
			areaStats: [],
			maxAreaCount: 0,
			rankingList: []
		}
	},
	onLoad() {
		this.loadStatistics();
	},
	onShow() {
		// 每次显示页面时刷新数据
		this.loadStatistics();
	},
	methods: {
		onPeriodChange(e) {
			this.currentPeriod = e.currentIndex;
			this.loadStatistics();
		},
		
		loadStatistics() {
			uni.showLoading({
				title: '加载中...'
			});
			
			const token = uni.getStorageSync('token');
			if (!token) {
				uni.hideLoading();
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages/login/login'
					});
				}, 1500);
				return;
			}
			
			const period = ['today', 'week', 'month'][this.currentPeriod];
			
			uni.request({
				url: getApp().globalData.BASE_API + '/api/deliverers/statistics',
				method: 'GET',
				header: {
					'Authorization': 'Bearer ' + token,
					'Accept': 'application/json'
				},
				data: {
					period: period
				},
				success: (res) => {
					console.log('统计API响应:', res);
					
					if (res.statusCode === 200 && res.data && res.data.code === 200) {
						const data = res.data.data || {};
						this.updateStats(data);
					} else if (res.statusCode === 401) {
						// token过期或无效
						uni.showToast({
							title: '登录已过期，请重新登录',
							icon: 'none'
						});
						setTimeout(() => {
							uni.redirectTo({
								url: '/pages/login/login'
							});
						}, 1500);
					} else {
						console.error('获取统计数据失败', res);
						uni.showToast({
							title: '获取数据失败，显示模拟数据',
							icon: 'none'
						});
						this.loadMockData();
					}
				},
				fail: (err) => {
					console.error('请求统计数据失败', err);
					uni.showToast({
						title: '网络请求失败，显示模拟数据',
						icon: 'none'
					});
					this.loadMockData();
				},
				complete: () => {
					uni.hideLoading();
				}
			});
		},
		
		updateStats(data) {
			this.stats = {
				totalOrders: data.total_orders || 0,
				completedOrders: data.completed_orders || 0,
				pendingOrders: data.pending_orders || 0,
				inProgressOrders: data.in_progress_orders || 0,
				completionRate: data.completion_rate || 0,
				avgTime: data.avg_delivery_time || 0,
				rating: data.average_rating || 0,
				ordersTrend: data.orders_trend || 0,
				completedTrend: data.completed_trend || 0,
				timeTrend: data.time_trend || 0,
				ratingTrend: data.rating_trend || 0
			};
			
			// 处理时段分布数据
			this.timeDistribution = data.time_distribution || this.generateMockTimeData();
			this.maxTimeCount = this.timeDistribution.length > 0 ? 
				Math.max(...this.timeDistribution.map(item => item.count)) : 1;
			
			// 处理区域统计数据
			this.areaStats = data.area_stats || this.generateMockAreaData();
			this.maxAreaCount = this.areaStats.length > 0 ? 
				Math.max(...this.areaStats.map(item => item.count)) : 1;
			
			// 处理团队排名数据
			this.rankingList = data.ranking || this.generateMockRankingData();
			
			console.log('统计数据更新完成:', {
				stats: this.stats,
				timeDistribution: this.timeDistribution,
				areaStats: this.areaStats,
				ranking: this.rankingList
			});
		},
		
		loadMockData() {
			// 模拟数据
			const mockData = {
				total_orders: 45,
				completed_orders: 38,
				pending_orders: 3,
				in_progress_orders: 4,
				completion_rate: 84.4,
				avg_delivery_time: 25,
				average_rating: 4.8,
				orders_trend: 12.5,
				completed_trend: 8.3,
				time_trend: -5.2,
				rating_trend: 2.1
			};
			
			this.updateStats(mockData);
		},
		
		generateMockTimeData() {
			const hours = [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20];
			return hours.map(hour => ({
				hour: hour,
				count: Math.floor(Math.random() * 8) + 1
			}));
		},
		
		generateMockAreaData() {
			return [
				{ name: '朝阳区', count: 15 },
				{ name: '海淀区', count: 12 },
				{ name: '西城区', count: 8 },
				{ name: '东城区', count: 6 },
				{ name: '丰台区', count: 4 }
			];
		},
		
		generateMockRankingData() {
			return [
				{ name: '张三', completedOrders: 52, completionRate: 92.8, isCurrentUser: false },
				{ name: '李四', completedOrders: 48, completionRate: 89.6, isCurrentUser: true },
				{ name: '王五', completedOrders: 45, completionRate: 87.2, isCurrentUser: false },
				{ name: '赵六', completedOrders: 42, completionRate: 85.1, isCurrentUser: false },
				{ name: '钱七', completedOrders: 38, completionRate: 82.3, isCurrentUser: false }
			];
		},
		
		getTrendClass(trend) {
			if (trend > 0) return 'trend-up';
			if (trend < 0) return 'trend-down';
			return 'trend-stable';
		},
		
		formatTrend(trend) {
			if (trend === 0) return '0%';
			return (trend > 0 ? '+' : '') + trend.toFixed(1) + '%';
		},
		
		getCircleStyle() {
			const rate = this.stats.completionRate;
			const circumference = 2 * Math.PI * 45; // 半径45
			const offset = circumference - (rate / 100) * circumference;
			
			return {
				'stroke-dasharray': circumference,
				'stroke-dashoffset': offset
			};
		},
		
		getPeriodText() {
			return ['今日', '本周', '本月'][this.currentPeriod];
		}
	}
}
</script>

<style lang="scss">
.statistics-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.time-selector {
	margin-bottom: 30rpx;
	background-color: #fff;
	border-radius: 16rpx;
	padding: 20rpx;
}

.stats-cards {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.stats-card {
	background: linear-gradient(135deg, var(--card-color-start), var(--card-color-end));
	border-radius: 16rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	
	&.primary {
		--card-color-start: #007AFF;
		--card-color-end: #5AC8FA;
	}
	
	&.success {
		--card-color-start: #4CAF50;
		--card-color-end: #8BC34A;
	}
	
	&.warning {
		--card-color-start: #FF9800;
		--card-color-end: #FFC107;
	}
	
	&.info {
		--card-color-start: #9C27B0;
		--card-color-end: #E91E63;
	}
}

.card-icon {
	margin-right: 20rpx;
}

.card-content {
	flex: 1;
}

.card-number {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #fff;
	line-height: 1.2;
}

.card-label {
	display: block;
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	margin-top: 8rpx;
}

.card-trend {
	display: block;
	font-size: 20rpx;
	margin-top: 8rpx;
	
	&.trend-up {
		color: #4CAF50;
	}
	
	&.trend-down {
		color: #F44336;
	}
	
	&.trend-stable {
		color: rgba(255, 255, 255, 0.6);
	}
}

.completion-section,
.time-analysis,
.area-stats,
.ranking-section {
	margin-bottom: 30rpx;
}

.completion-section {
	margin-bottom: 60rpx;
}

.completion-chart {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
}

.chart-circle {
	width: 200rpx;
	height: 200rpx;
	position: relative;
	margin-right: 40rpx;
}

.circle-progress {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background: conic-gradient(#007AFF 0deg, #007AFF calc(var(--completion-rate, 0) * 3.6deg), #f0f0f0 calc(var(--completion-rate, 0) * 3.6deg));
	display: flex;
	align-items: center;
	justify-content: center;
}

.circle-inner {
	width: 140rpx;
	height: 140rpx;
	background-color: #fff;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.completion-rate {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.completion-label {
	font-size: 24rpx;
	color: #666;
	margin-top: 8rpx;
}

.completion-details {
	flex: 1;
}

.detail-item {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.detail-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	margin-right: 16rpx;
	
	&.pending {
		background-color: #FF9800;
	}
	
	&.progress {
		background-color: #2196F3;
	}
	
	&.completed {
		background-color: #4CAF50;
	}
}

.detail-label {
	flex: 1;
	font-size: 28rpx;
	color: #666;
}

.detail-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.time-chart {
	padding: 20rpx 0;
}

.time-bars {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
	height: 200rpx;
	padding: 0 20rpx;
}

.time-bar-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.time-bar {
	width: 20rpx;
	height: 160rpx;
	background-color: #f0f0f0;
	border-radius: 10rpx;
	position: relative;
	margin-bottom: 16rpx;
}

.time-bar-fill {
	position: absolute;
	bottom: 0;
	width: 100%;
	background: linear-gradient(to top, #007AFF, #5AC8FA);
	border-radius: 10rpx;
	min-height: 4rpx;
}

.time-label {
	font-size: 20rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.time-count {
	font-size: 24rpx;
	font-weight: bold;
	color: #333;
}

.area-list {
	padding: 20rpx 0;
}

.area-item {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
}

.area-info {
	width: 160rpx;
	margin-right: 20rpx;
}

.area-name {
	display: block;
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.area-count {
	display: block;
	font-size: 24rpx;
	color: #666;
	margin-top: 4rpx;
}

.area-progress {
	flex: 1;
	height: 12rpx;
	background-color: #f0f0f0;
	border-radius: 6rpx;
	margin-right: 20rpx;
	overflow: hidden;
}

.area-progress-fill {
	height: 100%;
	background: linear-gradient(to right, #007AFF, #5AC8FA);
	border-radius: 6rpx;
}

.area-percentage {
	width: 80rpx;
	text-align: right;
	font-size: 24rpx;
	color: #666;
}

.ranking-list {
	padding: 20rpx 0;
}

.ranking-header {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
	margin-bottom: 20rpx;
}

.ranking-item {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
	
	&.current-user {
		background-color: #f8f9ff;
		border: 2rpx solid #007AFF;
		padding: 22rpx 20rpx;
	}
}

.rank-col {
	width: 80rpx;
	text-align: center;
}

.rank-medal {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 24rpx;
	font-weight: bold;
	margin: 0 auto;
	
	&.medal-1 {
		background: linear-gradient(135deg, #FFD700, #FFA500);
	}
	
	&.medal-2 {
		background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
	}
	
	&.medal-3 {
		background: linear-gradient(135deg, #CD7F32, #B8860B);
	}
}

.rank-number {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}

.name-col {
	flex: 1;
	display: flex;
	align-items: center;
	margin-left: 20rpx;
}

.deliverer-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.current-tag {
	background-color: #007AFF;
	color: #fff;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	margin-left: 16rpx;
}

.orders-col,
.rate-col {
	width: 120rpx;
	text-align: center;
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.ranking-header .rank-col,
.ranking-header .name-col,
.ranking-header .orders-col,
.ranking-header .rate-col {
	font-size: 24rpx;
	color: #666;
	font-weight: normal;
}
</style> 