<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 微信小程序配置
    |--------------------------------------------------------------------------
    |
    | 小程序相关配置，包括AppID和密钥等
    |
    */
    'mini_program' => [
        'app_id' => env('WECHAT_MINI_PROGRAM_APPID', ''),
        'secret' => env('WECHAT_MINI_PROGRAM_SECRET', ''),
        'token' => env('WECHAT_MINI_PROGRAM_TOKEN', ''),
        'aes_key' => env('WECHAT_MINI_PROGRAM_AES_KEY', ''),
    ],

    /*
    |--------------------------------------------------------------------------
    | 微信公众号配置
    |--------------------------------------------------------------------------
    |
    | 公众号相关配置
    |
    */
    'official_account' => [
        'app_id' => env('WECHAT_OFFICIAL_ACCOUNT_APPID', ''),
        'secret' => env('WECHAT_OFFICIAL_ACCOUNT_SECRET', ''),
        'token' => env('WECHAT_OFFICIAL_ACCOUNT_TOKEN', ''),
        'aes_key' => env('WECHAT_OFFICIAL_ACCOUNT_AES_KEY', ''),
    ],

    /*
    |--------------------------------------------------------------------------
    | 微信支付配置
    |--------------------------------------------------------------------------
    |
    | 微信支付相关配置
    |
    */
    'payment' => [
        'app_id' => env('WECHAT_PAYMENT_APPID', ''),
        'mch_id' => env('WECHAT_PAYMENT_MCH_ID', ''),
        'key' => env('WECHAT_PAYMENT_KEY', ''),
        'cert_path' => env('WECHAT_PAYMENT_CERT_PATH', ''),
        'key_path' => env('WECHAT_PAYMENT_KEY_PATH', ''),
        'notify_url' => env('WECHAT_PAYMENT_NOTIFY_URL', ''),
    ],
]; 