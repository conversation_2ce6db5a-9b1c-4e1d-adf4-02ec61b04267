<?php

namespace App\WechatMp\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use EasyWeChat\MiniApp\Application;

class WechatMpService
{
    /**
     * 获取微信小程序配置
     *
     * @return array
     */
    public function getMiniProgramConfig()
    {
        // 直接从数据库获取微信小程序配置
        $appIdConfig = \App\Models\ShopConfig::where('key', 'wx_mini_program_app_id')->first();
        $secretConfig = \App\Models\ShopConfig::where('key', 'wx_mini_program_secret')->first();
        
        // 获取配置值
        $appId = $appIdConfig ? $appIdConfig->value : null;
        $appSecret = $secretConfig ? $secretConfig->value : null;
        
        // 如果数据库中配置不存在，使用默认配置
        if (empty($appId) || empty($appSecret)) {
            $appId = config('wechat.mini_program.app_id', '');
            $appSecret = config('wechat.mini_program.app_secret', '');
        }
        
        $config = [
            'app_id' => $appId,
            'app_secret' => $appSecret,
            'enabled' => !empty($appId) && !empty($appSecret),
        ];
        
        return $config;
    }
    
    /**
     * 获取EasyWeChat小程序实例
     * 
     * @return \EasyWeChat\MiniApp\Application
     */
    public function getMiniApp()
    {
        $config = $this->getMiniProgramConfig();
        
        return new Application([
            'app_id' => $config['app_id'],
            'secret' => $config['app_secret'],
            // 可选：设置 token 和 aes key
            // 'token' => 'your-token',
            // 'aes_key' => 'your-aes-key',
            
            /**
             * 接口请求相关配置，超时时间等，具体可用参数请参考：
             * https://github.com/symfony/symfony/blob/5.3/src/Symfony/Component/HttpClient/HttpClient.php
             */
            'http' => [
                'throw'  => true, // 是否抛出异常
                'timeout' => 5.0,
            ],
        ]);
    }
    
    /**
     * 获取微信小程序用户信息
     *
     * @param string $code 微信小程序登录码
     * @return array
     */
    public function getUserInfoByCode($code)
    {
        try {
            $config = $this->getMiniProgramConfig();
            if (!$config['enabled']) {
                Log::error('微信小程序配置缺失');
                return [
                    'success' => false,
                    'message' => '微信小程序配置缺失'
                ];
            }
            
            // 使用EasyWeChat获取session信息
            // EasyWeChat 6.0版本使用方式
            $miniApp = $this->getMiniApp();
            $response = $miniApp->getUtils()->codeToSession($code);
            
            if (isset($response['errcode']) && $response['errcode'] != 0) {
                Log::error('微信小程序获取用户信息失败', [
                    'code' => $code,
                    'error_code' => $response['errcode'],
                    'error_message' => $response['errmsg'] ?? '未知错误'
                ]);
                
                return [
                    'success' => false,
                    'message' => $response['errmsg'] ?? '获取微信用户信息失败'
                ];
            }
            
            return [
                'success' => true,
                'openid' => $response['openid'],
                'session_key' => $response['session_key'],
                'unionid' => $response['unionid'] ?? null,
            ];
            
        } catch (\Exception $e) {
            Log::error('微信小程序获取用户信息异常', [
                'code' => $code,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '获取微信用户信息失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 解密微信小程序加密数据
     * 使用EasyWeChat库实现
     *
     * @param string $sessionKey 会话密钥
     * @param string $iv 加密算法的初始向量
     * @param string $encryptedData 包括敏感数据在内的完整用户信息的加密数据
     * @return array
     */
    public function decryptData($sessionKey, $iv, $encryptedData)
    {
        Log::info('尝试解密微信数据', [
            'session_key_length' => strlen($sessionKey),
            'iv_length' => strlen($iv),
            'encrypted_data_length' => strlen($encryptedData)
        ]);
        
        try {
            // 使用EasyWeChat解密数据
            $miniApp = $this->getMiniApp();
            $config = $this->getMiniProgramConfig();
            
            // EasyWeChat v6 API, 参数顺序为：sessionKey, iv, encryptedData, appId
            $decryptedData = $miniApp->getEncryptor()->decrypt(
                $sessionKey,
                $iv, 
                $encryptedData,
                $config['app_id']
            );
            
            Log::info('微信数据解密成功', [
                'data_type' => gettype($decryptedData),
                'has_phone' => isset($decryptedData['phoneNumber']) || isset($decryptedData['purePhoneNumber'])
            ]);
            
            return [
                'success' => true,
                'data' => $decryptedData,
                'message' => '解密成功'
            ];
            
        } catch (\Exception $e) {
            Log::error('解密微信数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '解密数据失败: ' . $e->getMessage()
            ];
        }
    }
} 