// components/product-item/product-item.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 商品数据
    product: {
      type: Object,
      value: {}
    },
    // 显示模式：grid（网格）或 list（列表）
    mode: {
      type: String,
      value: 'grid'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击商品
     */
    onTap() {
      this.triggerEvent('tap', {
        product: this.data.product
      });
    },
    
    /**
     * 添加到购物车
     */
    onAddToCart(e) {
      // 阻止事件冒泡，避免触发商品点击事件
      e.stopPropagation();
      
      this.triggerEvent('addcart', {
        product: this.data.product
      });
    }
  }
}) 