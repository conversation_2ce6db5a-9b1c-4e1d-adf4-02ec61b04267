<?php

namespace App\Inventory\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryTransactionType extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name',
        'affects_inventory',
        'effect_direction',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'affects_inventory' => 'boolean',
        'effect_direction' => 'integer',
    ];

    /**
     * 获取此类型的所有库存事务
     */
    public function transactions()
    {
        return $this->hasMany(InventoryTransaction::class, 'transaction_type_id');
    }
} 