<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 检查并添加orders表索引
        if (!$this->indexExists('orders', 'idx_orders_user_status_created')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->index(['user_id', 'status', 'created_at'], 'idx_orders_user_status_created');
            });
        }

        // 检查并添加order_items表索引
        if (!$this->indexExists('order_items', 'idx_order_items_order')) {
            Schema::table('order_items', function (Blueprint $table) {
                $table->index(['order_id'], 'idx_order_items_order');
            });
        }

        if (!$this->indexExists('order_items', 'idx_order_items_product')) {
            Schema::table('order_items', function (Blueprint $table) {
                $table->index(['product_id'], 'idx_order_items_product');
            });
        }
    }

    /**
     * 检查索引是否存在
     */
    private function indexExists($table, $indexName)
    {
        $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Key_name = ?", [$indexName]);
        return !empty($indexes);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_orders_user_status_created');
        });

        Schema::table('order_items', function (Blueprint $table) {
            $table->dropIndex('idx_order_items_order');
            $table->dropIndex('idx_order_items_product');
        });
    }
};
