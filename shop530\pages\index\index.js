// pages/index/index.js - 首页逻辑
const api = require('../../utils/api');
const cartManager = require('../../utils/cart-unified');
const imageOptimizer = require('../../utils/image');
const { isLoggedIn } = require('../../utils/login-state-manager');
const { createLogger } = require('../../utils/logger');
const pageLogger = createLogger('首页');

// 工具方法
const utils = {
  /**
   * 防抖函数
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * 节流函数
   */
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  /**
   * 数据验证
   */
  validateProduct(product) {
    return product && 
           typeof product === 'object' && 
           product.id && 
           product.name && 
           (product.price || product.price === 0);
  },

  /**
   * 格式化价格
   */
  formatPrice(price) {
    if (typeof price !== 'number') {
      price = parseFloat(price) || 0;
    }
    return price.toFixed(2);
  }
};

// 安全获取App实例
function getAppInstance() {
  try {
    const app = getApp();
    if (app && app.globalData !== undefined) {
      return app;
    }
  } catch (error) {
    console.warn('获取App实例失败:', error);
  }
  
  // 返回默认实例
  return {
    globalData: {
      token: null,
      userInfo: null,
      cartCount: 0
    },
    updateCartCount: () => {
      console.warn('App实例不可用，无法更新购物车数量');
    },
    showToast: (title, icon = 'none') => {
      wx.showToast({ title, icon });
    }
  };
}

Page({
  data: {
    // 基础数据
    searchValue: '',
    bannerHeight: '300rpx',
    statusBarHeight: 44,
    
    // 配送时间显示
    deliveryText: '明日达', // 默认值，会被动态计算
    
    // 标题栏背景显示状态
    showHeaderBg: false, // 控制标题栏背景是否显示
    
    // 数据状态
    loading: true, // 初始显示骨架屏
    tabSwitching: false, // 标签切换加载状态
    bannerList: [],
    categoryList: [],
    productSections: [],
    serviceFeatures: [
      { id: 1, icon: 'logistics', text: '急速送达' },
      { id: 2, icon: 'like-o', text: '品质稳定' },
      { id: 3, icon: 'gold-coin-o', text: '成本更优' },
      { id: 4, icon: 'service-o', text: '售后无忧' }
    ],
    
    // 标签页相关
    activeTab: 0, // 当前选中的标签页索引
    sectionTabs: [
      { type: 'all', name: '全部' } // 默认标签，其他标签将从API动态获取
    ],
    
    // 当前Tab显示的商品数据
    currentSectionProducts: [],
    
    // 懒加载缓存
    tabDataCache: {}, // 缓存各个标签页的数据
    
    // 轮播图当前索引
    currentBannerIndex: 0,
    
    // 轮播图控制
    bannerAutoplay: true,
    bannerInterval: 6000,
    
    // 标签页吸顶状态
    tabsSticky: false,
    
    // 动态计算的偏移量
    offsetTop: 180, // 默认值，会被动态计算替换
    
    // 回到顶部按钮显示状态
    showBackToTop: false,
    
    // 客服相关数据
    pageInfo: {
      path: '/pages/index/index',
      title: '首页'
    },
    
    // 登录状态
    isLoggedIn: false,

    searchKeyword: '',  // 搜索关键词
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    pageLogger.info('页面加载开始');
    
    // 初始化页面
    this.initPage();
    
    // 计算配送时间
    this.calculateDeliveryTime();
    
    // 添加购物车监听器
    this.initCartListener();
  },

  /**
   * 页面显示
   */
  onShow() {
    console.log('🏠 首页显示');
    
    // 设置tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
    }
    
    // 简单的紧急恢复机制
    if (this.data.loading) {
      console.log('⚠️ 检测到骨架屏仍在显示，1秒后强制隐藏');
      setTimeout(() => {
        if (this.data.loading) {
          console.log('🚨 强制隐藏骨架屏');
          this.setData({ loading: false });
        }
      }, 1000);
    }
    
    // 确保关键布局数据存在
    if (!this.data.statusBarHeight) {
      const systemInfo = wx.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight || 44;
      console.log('🔧 重新设置状态栏高度:', statusBarHeight);
      
      this.setData({
        statusBarHeight: statusBarHeight
      });
    }
    
    // 更新配送时间（防止跨天后时间不准确）
    this.calculateDeliveryTime();
    
    // 刷新购物车数量
    const cart = require('../../utils/cart-unified');
    cart.onPageShow();
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 恢复轮播图自动播放
    this.controlBannerAutoplay(true);
  },

  /**
   * 页面准备完毕
   */
  onReady() {
    // 设置Sticky偏移量
    this.setOffsetTop();
    
    // 确保页面在顶部
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0
    });
    
    console.log('🔍 页面准备完毕，等待数据加载完成后检查DOM元素');
  },

  /**
   * 检查DOM元素状态 - 在数据加载完成后调用
   */
  checkDOMElements() {
    // 只在非loading状态下检查元素
    if (this.data.loading) {
      console.log('⏳ 页面仍在加载中，跳过DOM元素检查');
      return;
    }

    wx.nextTick(() => {
      console.log('🔍 检查固定定位元素状态:');
      console.log('📍 statusBarHeight:', this.data.statusBarHeight);
      console.log('📍 deliveryText:', this.data.deliveryText);
      
      // 检查标题栏元素
      wx.createSelectorQuery()
        .select('.simple-title-bar')
        .boundingClientRect()
        .exec((res) => {
          if (res[0]) {
            console.log('🎯 标题栏位置:', res[0]);
            console.log('✅ 标题栏固定定位正常');
          } else {
            console.warn('⚠️ 未找到标题栏元素 - 这是正常的，因为元素可能还在渲染中');
          }
        });
        
      // 检查搜索框元素
      wx.createSelectorQuery()
        .select('.simple-search-bar')
        .boundingClientRect()
        .exec((res) => {
          if (res[0]) {
            console.log('📍 搜索框位置:', res[0]);
            console.log('✅ 搜索框固定定位正常');
          } else {
            console.warn('⚠️ 未找到搜索框元素 - 这是正常的，因为元素可能还在渲染中');
          }
        });

      // 检查主容器元素
      wx.createSelectorQuery()
        .select('.home-container')
        .boundingClientRect()
        .exec((res) => {
          if (res[0]) {
            console.log('🏠 主容器位置:', res[0]);
            console.log('✅ 主容器渲染正常');
          } else {
            console.warn('⚠️ 未找到主容器元素 - 这是正常的，因为元素可能还在渲染中');
          }
        });
    });
  },

  /**
   * 页面隐藏时暂停轮播图
   */
  onHide() {
    console.log('🏠 首页隐藏');
    
    // 暂停轮播图自动播放，节省性能
    this.controlBannerAutoplay(false);
  },

  /**
   * 页面卸载
   */
  onUnload() {
    console.log('🏠 首页开始卸载');
    
    // 移除购物车监听器（统一购物车管理器不需要手动移除监听器）
    // if (this.cartListener && getAppInstance().globalData.cartManager) {
    //   getAppInstance().globalData.cartManager.removeListener(this.cartListener);
    // }
    
    // 使用try-catch包裹所有定时器清理代码，防止出现异常
    try {
      // 清理定时器
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
        this.refreshTimer = null;
      }
      
      // 清理轮播图切换定时器
      if (this.bannerChangeTimer) {
        clearTimeout(this.bannerChangeTimer);
        this.bannerChangeTimer = null;
      }
      
      // 清理骨架屏定时器
      if (this.skeletonTimer) {
        clearTimeout(this.skeletonTimer);
        this.skeletonTimer = null;
      }
      
      // 清理滚动更新定时器
      if (this.scrollUpdateTimer) {
        clearTimeout(this.scrollUpdateTimer);
        this.scrollUpdateTimer = null;
      }
      
      // 清理可能存在的其他定时器
      const possibleTimers = ['animationTimer', 'loadTimer', 'searchTimer', 'debounceTimer'];
      possibleTimers.forEach(timerName => {
        if (this[timerName]) {
          clearTimeout(this[timerName]);
          this[timerName] = null;
        }
      });
    } catch (error) {
      console.error('清理定时器出错:', error);
    }
    
    // 清理防抖标志
    this.addingToCart = false;
    this.removingFromCart = false;
    this.lastScrollTime = null;
    
    // 只清理缓存数据，保留基础布局数据
    try {
      this.setData({
        tabDataCache: {},
        // 保留基础数据，不清理 statusBarHeight、bannerHeight、deliveryText 等
        // bannerList: [],  // 注释掉，避免影响页面布局
        // categoryList: [], // 注释掉，避免影响页面布局
        // productSections: [], // 注释掉，避免影响页面布局
        currentSectionProducts: []
      });
    } catch (error) {
      console.error('清理数据出错:', error);
    }
    
    console.log('🏠 首页卸载完成');
  },

  /**
   * 页面滚动监听 - 节流优化版本
   */
  onPageScroll(e) {
    // 节流处理：限制执行频率
    const now = Date.now();
    if (this.lastScrollTime && now - this.lastScrollTime < 16) { // 约60fps
      return;
    }
    this.lastScrollTime = now;
    
    const scrollTop = e.scrollTop;
    const showBackToTop = scrollTop > 500; // 滚动超过500px显示回到顶部按钮
    
    // 基于轮播图固定高度600rpx来计算标题栏背景显示的临界点
    // 600rpx ≈ 300px (在大多数设备上，1rpx ≈ 0.5px)
    const bannerHeightPx = 300; // 轮播图高度600rpx转换为px
    const mainContentPaddingPx = 80; // 主内容padding-top: 160rpx ≈ 80px
    const headerHeightPx = 60; // 标题栏+搜索框总高度约60px
    
    // 轮播图实际可见高度 = 轮播图总高度 - 被标题栏遮挡的部分
    const bannerVisibleHeight = bannerHeightPx - mainContentPaddingPx; // 300 - 80 = 220px
    
    // 当轮播图快要完全划过搜索框时显示背景，提前20px触发
    const threshold = bannerVisibleHeight - 20; // 220 - 20 = 200px
    
    const showHeaderBg = scrollTop > threshold;
    
    // 只在状态改变时更新，避免频繁setData
    const updates = {};
    if (showBackToTop !== this.data.showBackToTop) {
      updates.showBackToTop = showBackToTop;
    }
    if (showHeaderBg !== this.data.showHeaderBg) {
      updates.showHeaderBg = showHeaderBg;
      console.log('🎯 标题栏背景状态变化:', showHeaderBg ? '显示' : '隐藏', 
                  '滚动位置:', scrollTop + 'px', 
                  '临界点:', threshold + 'px',
                  '轮播图可见高度:', bannerVisibleHeight + 'px');
    }
    
    // 批量更新，减少setData调用
    if (Object.keys(updates).length > 0) {
      // 防抖处理：延迟执行setData
      if (this.scrollUpdateTimer) {
        clearTimeout(this.scrollUpdateTimer);
      }
      
      this.scrollUpdateTimer = setTimeout(() => {
        this.setData(updates);
        this.scrollUpdateTimer = null;
      }, 10);
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('🔄 首页下拉刷新开始');
    
    // 清除API缓存，强制重新请求数据
    const request = require('../../utils/request');
    if (request.clearCache) {
      request.clearCache();
      console.log('🧹 已清除API缓存');
    }
    
    // 清除缓存，强制重新加载数据
    this.setData({
      tabDataCache: {}, // 清空标签页缓存
      currentSectionProducts: [] // 清空当前显示的商品
    });
    
    // 重新加载所有数据
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
      console.log('✅ 首页下拉刷新完成');
    });
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 显示骨架屏
      this.setData({ loading: true });
      
      // 设置简单的超时保护（3秒后强制隐藏）
      this.skeletonTimer = setTimeout(() => {
        if (this.data.loading) {
          console.log('⏰ 骨架屏超时，强制隐藏');
          this.setData({ loading: false });
        }
      }, 3000);
      
      // 获取系统信息，包括状态栏高度
      const systemInfo = wx.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight || 44;
      console.log('📱 系统状态栏高度:', statusBarHeight + 'px');
      
      const bannerHeight = Math.floor(systemInfo.windowWidth * 1.0) + 'rpx';
      
      this.setData({ 
        bannerHeight,
        statusBarHeight 
      });

      // 加载数据
      await this.loadAllData();
      
    } catch (error) {
      console.error('首页初始化失败:', error);
      wx.showToast({
        title: '页面加载失败，请重试',
        icon: 'none'
      });
    } finally {
      // 确保隐藏骨架屏
      if (this.skeletonTimer) {
        clearTimeout(this.skeletonTimer);
        this.skeletonTimer = null;
      }
      this.setData({ loading: false });
    }
  },

  /**
   * 加载页面数据
   */
  async loadAllData() {
    pageLogger.time('loadAllData');
    
    try {
      pageLogger.info('开始并行加载数据');
      
      // 并行加载数据，提高加载速度
      const [bannerData, categoryData, productData, tabsData] = await Promise.allSettled([
        this.loadBannerData(),
        this.loadCategoryData(), 
        this.loadProductData(),
        this.loadTabsData()
      ]);
      
      // 统计加载结果
      const results = {
        banners: bannerData.status === 'fulfilled' ? bannerData.value?.length || 0 : 0,
        categories: categoryData.status === 'fulfilled' ? categoryData.value?.length || 0 : 0,
        products: productData.status === 'fulfilled' ? productData.value?.length || 0 : 0,
        tabs: tabsData.status === 'fulfilled' ? tabsData.value?.length || 0 : 0
      };
      
      pageLogger.info('基础数据加载完成', results);
      
      // 记录失败的加载项
      const failures = [];
      if (bannerData.status === 'rejected') failures.push('轮播图');
      if (categoryData.status === 'rejected') failures.push('分类');
      if (productData.status === 'rejected') failures.push('商品');
      if (tabsData.status === 'rejected') failures.push('标签');
      
      if (failures.length > 0) {
        pageLogger.warn('部分数据加载失败', failures);
      }
      
      // 处理标签数据
      if (tabsData.status === 'fulfilled' && tabsData.value) {
        this.setData({ sectionTabs: tabsData.value });
        pageLogger.debug('使用API标签数据', `${tabsData.value.length}个`);
      } else {
        pageLogger.debug('从商品分区生成标签');
        const validProductData = productData.status === 'fulfilled' ? productData.value : [];
        this.generateTabsFromSections(validProductData);
      }
      
      // 等待一个微任务，确保setData完成
      await new Promise(resolve => wx.nextTick(resolve));
      
      // 加载初始商品数据
      try {
        await this.loadAllTagProducts();
        pageLogger.debug('初始商品数据加载完成');
      } catch (error) {
        pageLogger.warn('初始商品数据加载失败', error.message);
      }
      
      pageLogger.info('所有数据加载完成');
      
      // 数据加载完成后隐藏骨架屏
      this.setData({ loading: false });
      
      // 数据加载完成后计算offset-top，确保DOM完全渲染
      setTimeout(() => {
        this.setOffsetTop();
        wx.pageScrollTo({
          scrollTop: 0,
          duration: 0
        });
      }, 300);
      
      pageLogger.timeEnd('loadAllData');
      
    } catch (error) {
      pageLogger.error('首页数据加载失败', {
        message: error.message,
        name: error.name
      });
      
      // 即使失败也要隐藏骨架屏
      this.setData({ loading: false });
      
      wx.showToast({
        title: '数据加载失败，请下拉刷新重试',
        icon: 'none',
        duration: 3000
      });
      
      pageLogger.timeEnd('loadAllData');
    }
  },

  /**
   * 加载轮播图数据
   */
  async loadBannerData() {
    try {
      const banners = await api.api.getBanners();
      this.setData({ bannerList: banners || [] });
      return banners;
    } catch (error) {
      console.error('❌ 轮播图数据加载失败:', error);
      return [];
    }
  },

  /**
   * 加载分类数据
   */
  async loadCategoryData() {
    try {
      const categories = await api.api.getHomeCategories();
      this.setData({ categoryList: categories || [] });
      return categories;
    } catch (error) {
      console.error('❌ 分类数据加载失败:', error);
      return [];
    }
  },

  /**
   * 加载商品数据
   */
  async loadProductData() {
    try {
      const products = await api.api.getProductSections();
      this.setData({ productSections: products || [] });
      return products;
    } catch (error) {
      console.error('❌ 商品数据加载失败:', error);
      return [];
    }
  },

  /**
   * 预加载轮播图图片
   */
  preloadBannerImages(bannerList) {
    if (!bannerList || bannerList.length === 0) return;
    
    const imageUrls = bannerList.map(item => item.image).filter(Boolean);
    if (imageUrls.length > 0) {
      // 直接预加载原始图片，不进行优化
      imageOptimizer.preloadImages(imageUrls);
    }
  },

  /**
   * 预加载商品图片
   */
  preloadProductImages(productSections) {
    if (!productSections || productSections.length === 0) return;
    
    const allImages = [];
    
    // 收集所有商品图片URL
    productSections.forEach(section => {
      if (section.products && section.products.length > 0) {
        section.products.forEach(product => {
          if (product.image) {
            allImages.push(product.image);
          }
        });
      }
    });
    
    // 预加载图片
    if (allImages.length > 0) {
      // 直接预加载原始图片，不进行优化
      imageOptimizer.preloadImages(allImages);
    }
  },

  /**
   * 刷新页面数据
   */
  async refreshData() {
    // 显示加载状态
    this.setData({ isRefreshing: true });
    
    // 获取全局应用实例
    const app = getApp();
    
    // 重新加载轮播图
    this.loadBanners();
    
    // 重新加载分类
    this.loadCategories();
    
    // 重新加载商品数据
    this.loadProducts();
    
    // 更新购物车数量
    app.updateCartBadge();
    
    // 延迟关闭刷新状态
    setTimeout(() => {
      this.setData({ isRefreshing: false });
    }, 800);
  },

  /**
   * 计算配送时间
   */
  calculateDeliveryTime() {
    const now = new Date();
    const dayOfWeek = now.getDay();
    
    let deliveryText = '';
    
    if (dayOfWeek === 0) {
      deliveryText = '后日达';
    } else {
      deliveryText = '明日达';
    }
    
    const dayName = dayOfWeek === 0 ? '星期天' : `星期${['', '一', '二', '三', '四', '五', '六'][dayOfWeek]}`;
    pageLogger.debug('配送时间计算', { day: dayName, delivery: deliveryText });
    
    this.setData({
      deliveryText: deliveryText
    });
  },

  // ========== 标签页相关方法 ==========

  /**
   * 加载标签数据
   */
  async loadTabsData() {
    try {
      const tabsData = await api.api.getProductTabs();
      return tabsData;
    } catch (error) {
      console.error('❌ 标签数据加载失败:', error);
      return null;
    }
  },

  /**
   * 根据商品分区数据生成标签
   */
  generateTabsFromSections(productSections) {
    if (!productSections || productSections.length === 0) {
      console.log('📋 没有商品分区数据，使用默认标签');
      return;
    }

    // 基础标签（全部）
    const tabs = [
      { type: 'all', name: '全部' }
    ];

    // 根据商品分区生成标签
    productSections.forEach(section => {
      if (section.type && section.title) {
        tabs.push({
          type: section.type,
          name: section.title
        });
      }
    });

    // 更新标签数据
    this.setData({
      sectionTabs: tabs
    });

    console.log(`📋 动态生成标签: ${tabs.length}个标签`, tabs.map(tab => tab.name));
  },

  /**
   * 标签页切换
   */
  async onTabChange(event) {
    // 修复事件参数解析，优先使用 event.detail.name，然后是 dataset.index
    let index;
    if (event.detail && typeof event.detail.name === 'number') {
      index = event.detail.name;
    } else if (event.detail && typeof event.detail.index === 'number') {
      index = event.detail.index;
    } else if (event.currentTarget && event.currentTarget.dataset && typeof event.currentTarget.dataset.index === 'number') {
      index = event.currentTarget.dataset.index;
    } else {
      // 尝试从字符串转换
      const nameStr = event.detail?.name;
      const indexStr = event.detail?.index || event.currentTarget?.dataset?.index;
      if (typeof nameStr === 'string' && !isNaN(nameStr)) {
        index = parseInt(nameStr);
      } else if (typeof indexStr === 'string' && !isNaN(indexStr)) {
        index = parseInt(indexStr);
      }
    }
    
    // 安全检查：确保index是有效数字
    if (typeof index !== 'number' || index < 0 || isNaN(index)) {
      console.error('❌ 无效的标签页索引:', index);
      return;
    }
    
    // 安全检查：确保sectionTabs数组存在且index在范围内
    if (!this.data.sectionTabs || index >= this.data.sectionTabs.length) {
      console.error('❌ 标签页索引超出范围:', index, 'sectionTabs长度:', this.data.sectionTabs?.length);
      return;
    }
    
    if (index !== this.data.activeTab) {
      const currentTab = this.data.sectionTabs[index];
      
      // 显示切换加载状态
      this.setData({ 
        activeTab: index,
        tabSwitching: true
      });
      
      // 只有在标签页未固定时才触发滚动，固定后切换标签不影响位置
      if (!this.data.tabsSticky) {
        this.scrollToTabs();
      }
      
      try {
        // 根据tab切换加载对应的商品数据
        if (index === 0) {
          // 全部商品 - 加载所有标签商品的合集
          await this.loadAllTagProducts();
          console.log('📋 切换到全部标签，显示所有标签商品合集');
        } else {
          // 特定标签 - 直接调用API获取商品
          const tabType = currentTab.type;
          await this.loadSectionData(tabType);
        }
      } catch (error) {
        console.error('❌ 标签切换失败:', error);
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      } finally {
        // 隐藏切换加载状态
        setTimeout(() => {
          this.setData({ tabSwitching: false });
        }, 300); // 延迟300ms隐藏，确保切换动画完成
      }
    }
  },

  /**
   * 平滑滚动到标签页位置
   */
  scrollToTabs() {
    // 使用动态计算的offset-top值
    const stickyOffsetTop = this.data.offsetTop;
    const query = wx.createSelectorQuery();
    
    // 获取标签页容器的位置
    query.select('.product-tabs-container').boundingClientRect();
    query.selectViewport().scrollOffset();
    
    query.exec((res) => {
      if (res[0] && res[1]) {
        const tabsRect = res[0];
        const scrollOffset = res[1];
        
        // 计算目标滚动位置：标签页顶部位置 - Sticky的offset-top
        const targetScrollTop = tabsRect.top + scrollOffset.scrollTop - stickyOffsetTop;
        
        wx.pageScrollTo({
          scrollTop: Math.max(0, targetScrollTop), // 确保不小于0
          duration: 300
        });
      }
    });
  },

  /**
   * Sticky组件滚动事件监听
   */
  onStickyScroll(event) {
    const { scrollTop, isFixed } = event.detail;
    
    // 当Sticky组件固定状态改变时，更新状态
    if (isFixed !== this.data.tabsSticky) {
      this.setData({ tabsSticky: isFixed });
      
      // 可以在这里添加一些状态变化时的UI反馈
      if (isFixed) {
        console.log('📌 标签页已固定在顶部');
      } else {
        console.log('📌 标签页取消固定');
      }
    }
  },

  /**
   * 回到顶部
   */
  scrollToTop() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  },

  /**
   * 加载所有标签的商品
   */
  async loadAllTagProducts() {
    console.log('📋 开始加载所有标签商品合集...');
    
    const { sectionTabs } = this.data;
    
    // 如果没有标签，加载默认商品
    if (!sectionTabs || sectionTabs.length <= 1) {
      console.log('📋 没有其他标签，加载默认商品...');
      try {
        const result = await api.api.getProducts({
          page: 1,
          per_page: 20
        });
        
        // 检查返回数据结构
        console.log('📦 API返回数据结构:', result);
        
        let products = [];
        if (result.data && Array.isArray(result.data)) {
          products = result.data;
        } else if (result.list && Array.isArray(result.list)) {
          products = result.list;
        } else if (result.data && result.data.data && Array.isArray(result.data.data)) {
          products = result.data.data;
        }
        
        this.setData({
          currentSectionProducts: products,
          ['tabDataCache.all']: products
        });
        
        return products;
      } catch (error) {
        console.error('❌ 加载默认商品失败:', error);
        return [];
      }
    }
    
    // 标签数据缓存和所有商品合集
    const tabDataCache = {};
    let allProducts = [];
    
    // 遍历所有标签（除了"全部"标签），预加载数据并收集到合集中
    for (let i = 0; i < sectionTabs.length; i++) {
      const tab = sectionTabs[i];
      
      // 跳过"全部"标签，它是所有其他标签的汇总
      if (tab.type === 'all') {
        continue;
      }
      
      // 加载特定标签的商品
      try {
        console.log(`📋 加载标签 ${tab.name} 的商品...`);
        const result = await api.api.getProductsByTag(tab.type, {
          page: 1,
          per_page: 20
        });
        
        // 检查返回数据结构
        let products = [];
        if (result.data && Array.isArray(result.data)) {
          products = result.data;
        } else if (result.list && Array.isArray(result.list)) {
          products = result.list;
        } else if (result.data && result.data.data && Array.isArray(result.data.data)) {
          products = result.data.data;
        }
        
        console.log(`📦 标签 ${tab.name} 获取到 ${products.length} 个商品`);
        
        // 保存到特定标签的缓存中
        tabDataCache[tab.type] = products;
        
        // 将这个标签的商品添加到合集中
        allProducts = allProducts.concat(products);
      } catch (error) {
        console.error(`❌ 加载标签 ${tab.name} 商品失败:`, error);
        tabDataCache[tab.type] = [];
      }
    }
    
    // 去重合并所有标签商品（根据商品ID去重）
    const uniqueAllProducts = this.deduplicateProducts(allProducts);
    console.log(`📋 全部标签合并后共有 ${uniqueAllProducts.length} 个商品（去重前 ${allProducts.length} 个）`);
    
    // 将去重后的合集保存到"all"标签缓存
    tabDataCache.all = uniqueAllProducts;
    
    // 更新标签数据缓存
    this.setData({
      tabDataCache,
      currentSectionProducts: this.data.activeTab === 0 ? uniqueAllProducts : (tabDataCache[sectionTabs[this.data.activeTab]?.type] || [])
    });
    
    return tabDataCache;
  },
  
  /**
   * 商品数据去重（根据ID）
   */
  deduplicateProducts(products) {
    const seen = new Map();
    return products.filter(product => {
      const id = product.id;
      if (!id) return true; // 保留没有ID的商品
      
      if (seen.has(id)) {
        return false; // 已经存在，过滤掉
      } else {
        seen.set(id, true); // 标记为已见过
        return true;
      }
    });
  },
  
  /**
   * 加载特定分区的商品数据
   */
  async loadSectionData(type) {
    console.log(`📋 加载分区商品: ${type}`);
    
    try {
      const result = await api.api.getProductsByTag(type, {
        page: 1,
        per_page: 20
      });
      
      // 检查返回数据结构
      console.log('📦 分区商品API返回数据结构:', result);
      
      let products = [];
      if (result.data && Array.isArray(result.data)) {
        products = result.data;
      } else if (result.list && Array.isArray(result.list)) {
        products = result.list;
      } else if (result.data && result.data.data && Array.isArray(result.data.data)) {
        products = result.data.data;
      }
      
      // 更新缓存
      this.setData({
        [`tabDataCache.${type}`]: products,
        currentSectionProducts: products
      });
      
      return products;
    } catch (error) {
      console.error(`❌ 加载分区商品失败: ${type}`, error);
      return [];
    }
  },

  /**
   * 轮播图切换事件（优化版本）
   */
  onBannerChange(event) {
    const current = event.detail.current;
    
    // 防抖处理，避免频繁切换
    if (this.bannerChangeTimer) {
      clearTimeout(this.bannerChangeTimer);
    }
    
    this.bannerChangeTimer = setTimeout(() => {
      // 更新当前轮播图索引
      this.setData({
        currentBannerIndex: current
      });
      
      // 清理定时器
      this.bannerChangeTimer = null;
    }, 150); // 防抖处理
  },

  /**
   * 轮播图点击事件
   */
  onBannerTap(event) {
    const item = event.currentTarget.dataset.item;
    console.log('🎠 轮播图点击:', item);
    
    if (item && item.link) {
      // 处理轮播图跳转
      wx.navigateTo({
        url: item.link
      });
    }
  },

  /**
   * 控制轮播图播放状态
   */
  controlBannerAutoplay(autoplay = true) {
    this.setData({
      bannerAutoplay: autoplay
    });
    console.log('🎠 轮播图自动播放:', autoplay ? '开启' : '关闭');
  },

  /**
   * 初始化购物车监听器
   */
  initCartListener() {
    // 监听购物车变化
    console.log('🛒 初始化购物车监听器');
  },

  /**
   * 设置Sticky偏移量
   */
  setOffsetTop() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 44;
    
    // 根据WXML结构计算正确的偏移量：
    // 1. 标题栏：style="top: {{statusBarHeight}}px;" 高度约50px
    // 2. 搜索框：style="top: {{statusBarHeight + 50}}px;" 高度约60rpx转px
    
    const titleBarHeight = 50; // 标题栏固定高度50px
    const searchBarHeight = Math.ceil(60 * (systemInfo.windowWidth / 750)); // 60rpx转px
    
    // 总偏移量 = 状态栏 + 标题栏 + 搜索框
    const offsetTop = statusBarHeight + titleBarHeight + searchBarHeight;
    
    this.setData({
      offsetTop: offsetTop
    });
    
    console.log('📍 设置Sticky偏移量:', {
      statusBarHeight,
      titleBarHeight,
      searchBarHeight,
      totalOffsetTop: offsetTop,
      screenWidth: systemInfo.windowWidth
    });
  },

  /**
   * 客服打开事件处理
   */
  onServiceOpen(e) {
    const { type, success } = e.detail;
    console.log('📞 客服打开:', type, success);
    
    // 统计客服使用情况
    if (success) {
      this.trackServiceUsage(type);
    }
  },

  /**
   * 记录客服使用情况
   */
  trackServiceUsage(type) {
    try {
      // 记录客服使用情况
      wx.request({
        url: `${api.API.BASE_URL}/track/service-usage`,
        method: 'POST',
        data: {
          type,
          page: 'index',
          timestamp: Date.now()
        },
        success: () => {
          console.log('📊 客服使用记录成功');
        },
        fail: (error) => {
          console.error('❌ 客服使用记录失败:', error);
        }
      });
    } catch (error) {
      console.error('❌ 客服使用记录异常:', error);
    }
  },

  /**
   * 商品点击事件
   */
  onProductTap(e) {
    const { product } = e.detail;
    if (!product || !product.id) {
      console.warn('⚠️ 商品信息无效:', product);
      return;
    }
    
    console.log('🛍️ 商品点击:', product.name);
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${product.id}`
    });
  },

  /**
   * 添加到购物车
   */
  async onAddToCart(e) {
    const { product, quantity = 1 } = e.detail;
    
    if (!product) {
      console.warn('⚠️ 商品信息无效:', product);
      return;
    }
    
    // 检查商品是否可购买
    if (product.out_of_stock || product.can_purchase === false) {
      wx.showToast({
        title: product.purchase_message || '商品已售罄',
        icon: 'none'
      });
      return;
    }
    
    console.log('🛒 添加到购物车:', product.name, '数量:', quantity);
    
    try {
      // 🔥 使用统一购物车管理器
      const { addToCart } = require('../../utils/cart-unified');
      
      // 确保传入正确的参数格式
      const params = {
        product_id: product.id,
        quantity: quantity
      };
      
      // 如果有销售单位信息，也一并传入
      if (product.sale_unit && product.sale_unit.id) {
        params.unit_id = product.sale_unit.id;
      }
      
      console.log('🔧 添加购物车参数:', params);
      const success = await addToCart(params);
      
      if (success) {
        console.log('✅ 添加到购物车成功:', product.name);
        
        // Toast提示由统一管理器的监听器处理
        // 更新购物车徽标数量（统一管理器自动处理）
      } else {
        console.error('❌ 添加到购物车失败');
        wx.showToast({
          title: '添加失败，请重试',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('❌ 添加到购物车异常:', error);
      
      // 根据错误类型显示不同提示
      let errorMessage = '添加失败';
      if (error.message) {
        if (error.message.includes('登录')) {
          errorMessage = '登录已过期，请重新登录';
        } else if (error.message.includes('网络')) {
          errorMessage = '网络异常，请重试';
        } else {
          errorMessage = error.message;
        }
      }
      
      wx.showToast({
        title: errorMessage,
        icon: 'error'
      });
    }
  },

  /**
   * 从购物车移除
   */
  async onRemoveFromCart(e) {
    const { product, quantity = 1 } = e.detail;
    
    if (!product) {
      console.warn('⚠️ 商品信息无效:', product);
      return;
    }
    
    console.log('🗑️ 从购物车移除:', product.name, '数量:', quantity);
    
    // 🔥 使用统一购物车管理器
    try {
      const { removeFromCart } = require('../../utils/cart-unified');
      const success = await removeFromCart(product.id);
      if (success) {
        console.log('✅ 移除购物车成功');
        // Toast提示由统一管理器的监听器处理
      }
    } catch (error) {
      console.error('❌ 移除购物车失败:', error);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 商品图片加载成功
   */
  onProductImageLoad(e) {
    const { product, detail } = e.detail;
    console.log('🖼️ 商品图片加载成功:', product?.name);
    
    // 瀑布流图片尺寸优化
    if (detail && detail.width && detail.height && product) {
      const { width, height } = detail;
      const aspectRatio = height / width;
      
      // 计算合适的图片高度（基于容器宽度和宽高比）
      // 容器宽度大约是 (750rpx - 60rpx - 20rpx) / 2 = 335rpx
      const containerWidth = 335;
      let targetHeight = Math.round(containerWidth * aspectRatio);
      
      // 限制高度范围，保持瀑布流效果
      const minHeight = 200;
      const maxHeight = 400;
      targetHeight = Math.max(minHeight, Math.min(maxHeight, targetHeight));
      
      // 更新商品数据中的图片高度
      const currentProducts = this.data.currentSectionProducts;
      const updatedProducts = currentProducts.map(p => {
        if (p.id === product.id) {
          return {
            ...p,
            imageHeight: `${targetHeight}rpx`
          };
        }
        return p;
      });
      
      // 只有当高度发生变化时才更新数据
      if (JSON.stringify(updatedProducts) !== JSON.stringify(currentProducts)) {
        this.setData({
          currentSectionProducts: updatedProducts
        });
      }
    }
  },

  /**
   * 商品图片加载失败
   */
  onProductImageError(e) {
    const { product, detail } = e.detail;
    console.warn('❌ 商品图片加载失败:', product?.name, detail);
    
    // 可以在这里添加图片加载失败的处理逻辑
    // 比如显示默认图片、重试加载等
  },

  /**
   * 购物车动画事件
   */
  onCartAnimation(e) {
    const { product, animationType } = e.detail;
    console.log('🎬 购物车动画:', animationType, product?.name);
    
    // 可以在这里添加购物车动画的处理逻辑
    // 比如播放音效、显示特效等
  },

  /**
   * 搜索输入变化
   */
  onSearchChange(e) {
    const value = e.detail;
    this.setData({
      searchKeyword: value
    });
  },

  /**
   * 搜索提交
   */
  onSearch(e) {
    const value = e;
    if (value && value.trim()) {
      wx.navigateTo({
        url: `/pages/search/search?keyword=${encodeURIComponent(value.trim())}`
      });
    }
  },

  /**
   * 搜索框获得焦点
   */
  onSearchFocus(e) {
    console.log('🔍 搜索框获得焦点');
    
    // 可以在这里添加搜索框获得焦点的处理逻辑
    // 比如显示搜索历史、热门搜索等
  },

  /**
   * 搜索按钮点击
   */
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    });
  },

  /**
   * 分类点击事件
   */
  onCategoryTap(e) {
    const category = e.currentTarget.dataset.category;
    if (!category) {
      console.warn('⚠️ 分类信息无效:', category);
      return;
    }
    
    console.log('📂 分类点击:', category.name, 'ID:', category.id);
    
    // 将分类信息存储到全局状态，供分类页面使用
    const app = getApp();
    if (app.globalData) {
      app.globalData.selectedCategoryFromHome = {
        id: category.id,
        name: category.name,
        icon: category.icon,
        timestamp: Date.now() // 添加时间戳，确保是最新的选择
      };
      console.log('📂 已保存分类信息到全局状态:', app.globalData.selectedCategoryFromHome);
    }
    
    // 跳转到分类页面
    wx.switchTab({
      url: '/pages/category/category'
    });
  },

  /**
   * 通知栏点击事件
   */
  onNoticeTap(e) {
    console.log('📢 通知栏点击');
    
    // 可以在这里添加通知栏点击的处理逻辑
    // 比如显示详细通知、跳转到活动页面等
    wx.showToast({
      title: '欢迎使用天心食品',
      icon: 'none'
    });
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp();
    const isLoggedIn = !!(app.globalData && app.globalData.isLoggedIn);
    
    console.log('🔐 首页检查登录状态:', isLoggedIn);
    
    this.setData({
      isLoggedIn: isLoggedIn
    });
    
    // 通知登录状态管理器状态变化，确保与积分商城等页面同步
    try {
      const { notifyLoginStateChange } = require('../../utils/login-state-manager');
      notifyLoginStateChange();
      console.log('🔔 已通知登录状态管理器状态变化');
    } catch (error) {
      console.warn('⚠️ 通知登录状态管理器失败:', error);
    }
  },

  /**
   * 搜索框清除
   */
  onSearchClear() {
    this.setData({
      searchKeyword: ''
    });
  },
});