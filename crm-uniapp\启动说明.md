# CRM UniApp 项目启动说明

## 当前状态
✅ 项目结构已完成  
✅ 所有页面代码已完成  
✅ API接口已封装  
✅ Vue 2 配置已完成  
⚠️ 需要配置后端API地址  
⚠️ 需要准备底部导航图标  

## 快速启动步骤

### 1. 配置API地址
编辑 `utils/config.js` 文件，修改 `baseUrl`：
```javascript
baseUrl: 'http://你的后端地址/api'
```

### 2. 在HBuilderX中打开项目
1. 打开HBuilderX
2. 文件 → 打开目录 → 选择 `crm-uniapp` 文件夹
3. 等待项目加载完成

### 3. 运行项目
1. 点击工具栏的"运行"按钮
2. 选择运行到浏览器或手机模拟器
3. 首次运行会自动安装依赖

### 4. 测试登录功能
1. 打开登录页面
2. 输入测试账号密码
3. 查看控制台日志，确认网络请求正常

## 当前已解决的问题

### ✅ Vue版本配置
- 已配置为Vue 2
- 所有代码基于Vue 2 Options API

### ✅ 网络请求修复
- 修复了headers未定义的问题
- 添加了详细的调试日志
- 完善了错误处理机制

### ✅ JSON配置修复
- 移除了pages.json中的注释
- 移除了manifest.json中的注释
- 暂时移除了tabBar配置（避免图标缺失问题）

### ✅ 导航功能
- 在首页添加了功能导航菜单
- 修改了导航方法适配当前配置

## 待完善的功能

### 🔄 底部导航栏
当图标准备好后，可以恢复tabBar配置：
1. 准备图标文件（参考 `static/tabbar/README.md`）
2. 恢复 `pages.json` 中的 tabBar 配置
3. 修改导航方法为 `uni.switchTab`

### 🔄 后端API对接
需要确保后端API接口正常：
- `/employee/auth/login` - 员工登录
- `/crm/users` - 客户列表
- `/orders` - 订单相关
- `/products` - 商品相关

## 调试技巧

### 查看网络请求
1. 打开浏览器开发者工具
2. 查看Console标签页的日志
3. 查看Network标签页的网络请求

### 常见问题排查
1. **登录失败**: 检查API地址是否正确
2. **页面空白**: 检查控制台是否有JavaScript错误
3. **图标不显示**: 检查图标文件是否存在

## 项目特色

### 🎨 现代化UI设计
- 渐变色彩搭配
- 卡片式布局
- 圆角设计和阴影效果

### 📱 移动端优化
- 专为APP设计
- 触摸友好交互
- 下拉刷新和上拉加载

### ⚡ 性能优化
- 分页加载
- 防抖搜索
- 缓存管理

### 🔒 安全特性
- Token自动管理
- 请求拦截器
- 统一错误处理

## 联系支持
如遇到问题，请检查：
1. 控制台错误日志
2. 网络请求状态
3. API接口返回数据

项目已经可以正常启动和运行，主要功能都已实现！ 