// pages/address-add/index.js - 新增/编辑地址页面
const api = require('../../utils/api');

Page({
  data: {
    // 页面模式：add新增 / edit编辑
    mode: 'add',
    
    // 地址ID（编辑模式时使用）
    addressId: null,
    
    // 表单数据
    formData: {
      name: '',
      phone: '',
      gender: 'male', // male先生 / female女士
      province: '',
      city: '',
      district: '',
      detail: '',
      isDefault: false,
      latitude: null, // 纬度
      longitude: null // 经度
    },
    

    
    // 提交状态
    submitting: false,
    
    // 系统状态栏高度
    statusBarHeight: 44
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 44;
    
    this.setData({
      statusBarHeight: statusBarHeight,
      mode: options.mode || 'add',
      addressId: options.id || null
    });
    
    console.log('新增地址页面状态栏高度:', statusBarHeight);
    
    // 如果是编辑模式，加载地址数据
    if (options.mode === 'edit' && options.data) {
      try {
        const addressData = JSON.parse(decodeURIComponent(options.data));
        // 确保isDefault是布尔值
        const isDefault = Boolean(addressData.isDefault || addressData.is_default || false);
        
        this.setData({
          formData: {
            name: addressData.name || '',
            phone: addressData.phone || '',
            gender: addressData.gender || 'male',
            province: addressData.province || '',
            city: addressData.city || '',
            district: addressData.district || '',
            detail: addressData.detail || '',
            isDefault: isDefault,
            latitude: addressData.latitude || null,
            longitude: addressData.longitude || null
          }
        });
        
        console.log('📝 编辑模式加载地址数据:', {
          原始数据: addressData,
          处理后isDefault: isDefault,
          最终formData: this.data.formData
        });
      } catch (error) {
        console.error('解析地址数据失败:', error);
      }
    }
  },

  /**
   * 输入框内容改变
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 性别选择
   */
  onGenderChange(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      'formData.gender': value
    });
  },

  /**
   * 选择收货地址
   */
  onSelectAddress() {
    console.log('🗺️ 开始选择地址...');
    
    // 添加测试提示，确认方法被调用
    wx.showToast({
      title: '正在打开地图...',
      icon: 'loading',
      duration: 1000
    });
    
    wx.chooseLocation({
      success: (res) => {
        console.log('🗺️ 选择地址成功:', res);
        console.log('  - 地址:', res.address);
        console.log('  - 名称:', res.name);
        console.log('  - 经纬度:', res.latitude, res.longitude);
        
        // 解析地址信息
        const addressParts = this.parseAddress(res.address);
        
        // 构建详细地址（如果有地点名称，优先使用地点名称）
        let detailAddress = res.name || '';
        if (!detailAddress && res.address) {
          // 如果没有地点名称，从完整地址中提取详细地址部分
          let fullAddress = res.address;
          if (addressParts.province) fullAddress = fullAddress.replace(addressParts.province, '');
          if (addressParts.city && addressParts.city !== addressParts.province) {
            fullAddress = fullAddress.replace(addressParts.city, '');
          }
          if (addressParts.district) fullAddress = fullAddress.replace(addressParts.district, '');
          detailAddress = fullAddress.trim();
        }
        
        console.log('📍 准备填入的地址信息:');
        console.log('  - 省份:', addressParts.province);
        console.log('  - 城市:', addressParts.city);
        console.log('  - 区县:', addressParts.district);
        console.log('  - 详细地址:', detailAddress);
        console.log('  - 经纬度:', res.latitude, res.longitude);
        
        this.setData({
          'formData.province': addressParts.province,
          'formData.city': addressParts.city,
          'formData.district': addressParts.district,
          'formData.detail': detailAddress,
          'formData.latitude': res.latitude,
          'formData.longitude': res.longitude
        });
        
        // 显示成功提示
        wx.showToast({
          title: '地址选择成功',
          icon: 'success',
          duration: 1500
        });
      },
      fail: (error) => {
        console.log('❌ 选择地址失败:', error);
        console.log('❌ 错误信息:', error.errMsg);
        
        let errorMessage = '选择地址失败';
        
        if (error.errMsg.includes('deny')) {
          errorMessage = '需要位置权限来选择地址，请在设置中开启';
        } else if (error.errMsg.includes('requiredPrivateInfos')) {
          errorMessage = '小程序权限配置问题，请联系开发者';
        } else if (error.errMsg.includes('cancel')) {
          errorMessage = '已取消选择地址';
          // 取消操作不显示错误提示
          return;
        }
        
        wx.showModal({
          title: '提示',
          content: errorMessage + '\n错误详情: ' + error.errMsg,
          showCancel: false,
          confirmText: '确定'
        });
      },
      complete: () => {
        console.log('🗺️ 地址选择操作完成');
      }
    });
  },

  /**
   * 解析地址字符串
   */
  parseAddress(address) {
    console.log('🔍 开始解析地址:', address);
    
    const result = {
      province: '',
      city: '',
      district: ''
    };
    
    // 直辖市列表
    const municipalities = ['北京市', '上海市', '天津市', '重庆市'];
    
    // 先匹配省份/自治区/直辖市
    const provinceMatch = address.match(/^(.+?省|.+?自治区|北京市|上海市|天津市|重庆市)/);
    if (provinceMatch) {
      result.province = provinceMatch[1];
      console.log('  - 匹配到省份:', result.province);
    }
    
    // 判断是否为直辖市
    const isMunicipality = municipalities.includes(result.province);
    
    if (isMunicipality) {
      // 直辖市情况：省份和城市相同
      result.city = result.province;
      console.log('  - 直辖市，城市设为:', result.city);
      
      // 匹配区县（去掉省份部分后匹配）
      const remainingAddress = address.replace(result.province, '');
      const districtMatch = remainingAddress.match(/^(.+?区|.+?县)/);
      if (districtMatch) {
        result.district = districtMatch[1];
        console.log('  - 匹配到区县:', result.district);
      }
    } else {
      // 非直辖市情况
      // 去掉省份部分后匹配城市
      const remainingAfterProvince = address.replace(result.province, '');
      const cityMatch = remainingAfterProvince.match(/^(.+?市)/);
      if (cityMatch) {
        result.city = cityMatch[1];
        console.log('  - 匹配到城市:', result.city);
        
        // 去掉城市部分后匹配区县
        const remainingAfterCity = remainingAfterProvince.replace(result.city, '');
        const districtMatch = remainingAfterCity.match(/^(.+?区|.+?县|.+?镇)/);
        if (districtMatch) {
          result.district = districtMatch[1];
          console.log('  - 匹配到区县:', result.district);
        }
      } else {
        // 如果没有匹配到市，可能是地级市的情况，直接匹配区县
        const districtMatch = remainingAfterProvince.match(/^(.+?区|.+?县|.+?镇)/);
        if (districtMatch) {
          result.district = districtMatch[1];
          console.log('  - 直接匹配到区县:', result.district);
        }
      }
    }
    
    console.log('🔍 地址解析结果:', result);
    return result;
  },



  /**
   * 设为默认地址切换
   */
  onDefaultToggle(e) {
    console.log('🔄 默认地址切换事件:', e);
    console.log('🔄 事件详情:', e.detail);
    
    // 处理不同的事件格式
    let value = false;
    
    if (e.detail && typeof e.detail.value !== 'undefined') {
      value = e.detail.value;
    } else if (e.detail && typeof e.detail !== 'undefined') {
      value = e.detail;
    } else if (typeof e.currentTarget !== 'undefined' && e.currentTarget.dataset && typeof e.currentTarget.dataset.value !== 'undefined') {
      value = e.currentTarget.dataset.value;
    }
    
    // 确保value是布尔值
    const boolValue = Boolean(value);
    
    console.log('🔄 原始value:', value, '转换后:', boolValue);
    
    this.setData({
      'formData.isDefault': boolValue
    });
    
    console.log('🔄 设置完成，当前isDefault:', this.data.formData.isDefault);
  },

  /**
   * 验证表单
   */
  validateForm() {
    const { formData } = this.data;
    
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入收货人姓名',
        icon: 'none'
      });
      return false;
    }
    
    if (!formData.phone.trim()) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return false;
    }
    
    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return false;
    }
    
    if (!formData.province || !formData.city || !formData.district) {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none'
      });
      return false;
    }
    
    if (!formData.detail.trim()) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  /**
   * 保存地址
   */
  async onSave() {
    if (this.data.submitting) return;
    
    if (!this.validateForm()) return;
    
    // 检查登录状态
    const { getLoginStatus } = require('../../utils/login-state-manager');
    const loginState = getLoginStatus();
    console.log('💾 保存地址前检查登录状态:', loginState);
    
    if (!loginState.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再保存地址',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/index'
            });
          }
        }
      });
      return;
    }
    
    try {
      this.setData({ submitting: true });
      
      const { formData, mode, addressId } = this.data;
      
      // 构建提交数据，映射字段名
      const submitData = {
        contact_name: formData.name,
        contact_phone: formData.phone,
        province: formData.province,
        city: formData.city,
        district: formData.district,
        address: formData.detail, // 前端detail字段对应后端address字段
        postal_code: formData.postalCode || null,
        notes: formData.notes || null,
        is_default: formData.isDefault || false,
        latitude: formData.latitude || null,
        longitude: formData.longitude || null
      };
      
      // 编辑模式时添加ID
      if (mode === 'edit') {
        submitData.id = addressId;
      }
      
      // 详细的字段映射调试
      console.log('🔍 地址保存 - 字段映射调试:');
      console.log('  前端表单数据:', formData);
      console.log('  后端提交数据:', submitData);
      console.log('  字段映射关系:');
      console.log('    name -> contact_name:', formData.name, '->', submitData.contact_name);
      console.log('    phone -> contact_phone:', formData.phone, '->', submitData.contact_phone);
      console.log('    detail -> address:', formData.detail, '->', submitData.address);
      console.log('    isDefault -> is_default:', formData.isDefault, '->', submitData.is_default);
      console.log('    latitude -> latitude:', formData.latitude, '->', submitData.latitude);
      console.log('    longitude -> longitude:', formData.longitude, '->', submitData.longitude);
      
      // 验证必填字段
      const requiredFields = ['contact_name', 'contact_phone', 'address'];
      const missingFields = requiredFields.filter(field => !submitData[field]);
      if (missingFields.length > 0) {
        console.error('❌ 缺少必填字段:', missingFields);
        throw new Error(`缺少必填字段: ${missingFields.join(', ')}`);
      }
      
      // 调用API保存地址
      let result;
      if (mode === 'edit') {
        console.log('📡 调用 api.updateAddress()');
        result = await api.updateAddress(addressId, submitData);
      } else {
        console.log('📡 调用 api.addAddress()');
        result = await api.addAddress(submitData);
      }
      
      // 处理API返回结果
      console.log('地址保存API返回结果:', result);
      console.log('🔍 返回结果类型:', typeof result);
      console.log('🔍 返回结果键:', Object.keys(result || {}));
      
      // 根据实际返回的数据结构判断成功
      // 从日志看，成功时返回: {data: {...}, meta: {...}, message: "地址添加成功"}
      let isSuccess = false;
      let message = '';
      
      if (result) {
        // 检查是否有message字段且包含"成功"
        if (result.message && result.message.includes('成功')) {
          isSuccess = true;
          message = result.message;
        }
        // 检查是否有data字段（通常表示成功）
        else if (result.data && typeof result.data === 'object') {
          isSuccess = true;
          message = result.message || (mode === 'edit' ? '修改成功' : '添加成功');
        }
        // 检查传统的code字段
        else if (result.code === 200 || result.code === 201) {
          isSuccess = true;
          message = result.message || (mode === 'edit' ? '修改成功' : '添加成功');
        }
        // 检查success字段
        else if (result.success === true) {
          isSuccess = true;
          message = result.message || (mode === 'edit' ? '修改成功' : '添加成功');
        }
      }
      
      console.log('🔍 成功判断结果:', { isSuccess, message });
      
      if (isSuccess) {
        wx.showToast({
          title: message,
          icon: 'success'
        });
        
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(result?.message || '保存失败');
      }
      
    } catch (error) {
      console.error('保存地址失败:', error);
      wx.showToast({
        title: error.message || '保存失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  }
}); 