<?php

namespace App\Order\Services;

use App\Order\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

class AutoOrderMergeService
{
    protected $orderMergeDataService;
    
    public function __construct(OrderMergeDataService $orderMergeDataService)
    {
        $this->orderMergeDataService = $orderMergeDataService;
    }
    
    /**
     * 自动扫描并合并当日订单
     */
    public function autoMergeTodayOrders(): array
    {
        $results = [
            'total_users_processed' => 0,
            'total_merges_created' => 0,
            'total_orders_merged' => 0,
            'failed_merges' => [],
            'successful_merges' => [],
        ];
        
        // 获取今日有多个待付款订单的用户
        $usersWithMultipleOrders = $this->getUsersWithMultiplePendingOrders();
        
        Log::info('开始自动合并当日订单', [
            'users_count' => $usersWithMultipleOrders->count(),
            'date' => Carbon::today()->toDateString(),
        ]);
        
        foreach ($usersWithMultipleOrders as $userId => $orderCount) {
            try {
                $mergeResult = $this->autoMergeUserOrders($userId);
                
                if ($mergeResult['merged']) {
                    $results['total_merges_created']++;
                    $results['total_orders_merged'] += $mergeResult['orders_count'];
                    $results['successful_merges'][] = $mergeResult;
                }
                
                $results['total_users_processed']++;
                
            } catch (\Exception $e) {
                $results['failed_merges'][] = [
                    'user_id' => $userId,
                    'error' => $e->getMessage(),
                ];
                
                Log::error('自动合并用户订单失败', [
                    'user_id' => $userId,
                    'error' => $e->getMessage(),
                ]);
            }
        }
        
        Log::info('自动合并完成', $results);
        
        return $results;
    }
    
    /**
     * 自动合并单个用户的订单
     */
    public function autoMergeUserOrders(int $userId): array
    {
        // 获取用户今日待付款订单，按支付方式分组
        $orderGroups = $this->getUserOrderGroupsByPaymentMethod($userId);
        
        $result = [
            'user_id' => $userId,
            'merged' => false,
            'groups_processed' => 0,
            'orders_count' => 0,
            'merge_details' => [],
        ];
        
        foreach ($orderGroups as $paymentMethod => $orders) {
            // 只有2个或以上订单才需要合并
            if ($orders->count() < 2) {
                continue;
            }
            
            try {
                // 检查是否可以自动合并
                if (!$this->canAutoMerge($orders)) {
                    Log::info('订单不满足自动合并条件', [
                        'user_id' => $userId,
                        'payment_method' => $paymentMethod,
                        'orders_count' => $orders->count(),
                    ]);
                    continue;
                }
                
                // 执行自动合并
                $mergedData = $this->prepareMergeData($orders);
                $mergedOrder = $this->orderMergeDataService->executeMerge(
                    $orders->pluck('id')->toArray(),
                    $mergedData,
                    [
                        'type' => 'auto',
                        'reason' => '系统自动合并当日订单',
                        'strategy' => 'conservative', // 保守策略
                    ]
                );
                
                $result['merged'] = true;
                $result['groups_processed']++;
                $result['orders_count'] += $orders->count();
                $result['merge_details'][] = [
                    'payment_method' => $paymentMethod,
                    'original_orders' => $orders->count(),
                    'merged_order_id' => $mergedOrder->id,
                    'savings' => $orders->sum('total') - $mergedOrder->total,
                ];
                
            } catch (\Exception $e) {
                Log::error('自动合并失败', [
                    'user_id' => $userId,
                    'payment_method' => $paymentMethod,
                    'error' => $e->getMessage(),
                ]);
                throw $e;
            }
        }
        
        return $result;
    }
    
    /**
     * 获取今日有多个待付款订单的用户
     */
    private function getUsersWithMultiplePendingOrders(): Collection
    {
        return Order::where('status', 'pending')
            ->where('is_merged_from', false)
            ->whereDate('created_at', Carbon::today())
            ->groupBy('user_id')
            ->havingRaw('COUNT(*) >= 2')
            ->selectRaw('user_id, COUNT(*) as order_count')
            ->pluck('order_count', 'user_id');
    }
    
    /**
     * 按支付方式分组用户订单
     */
    private function getUserOrderGroupsByPaymentMethod(int $userId): array
    {
        $orders = Order::where('user_id', $userId)
            ->where('status', 'pending')
            ->where('is_merged_from', false)
            ->whereDate('created_at', Carbon::today())
            ->with(['items.product', 'user'])
            ->get();
            
        return $orders->groupBy('payment_method')->toArray();
    }
    
    /**
     * 检查订单是否可以自动合并
     */
    private function canAutoMerge(Collection $orders): bool
    {
        // 自动合并的保守条件
        
        // 1. 必须是相同支付方式
        $paymentMethods = $orders->pluck('payment_method')->unique();
        if ($paymentMethods->count() > 1) {
            return false;
        }
        
        $paymentMethod = $paymentMethods->first();
        
        // 2. 货到付款必须同一区域
        if ($paymentMethod === 'cod') {
            $regions = $orders->pluck('region_id')->unique();
            if ($regions->count() > 1) {
                return false;
            }
        }
        
        // 3. 订单金额限制（仅对非货到付款订单）
        if ($paymentMethod !== 'cod') {
            $totalAmount = $orders->sum('total');
            if ($totalAmount > 1000) { // 超过1000元需要手动确认
                return false;
            }
        }
        // 货到付款订单无金额限制，因为主要目的是方便配送
        
        // 4. 订单数量不能过多
        if ($orders->count() > 5) { // 超过5个订单需要手动确认
            return false;
        }
        
        // 5. 检查是否有特殊商品（例如定制商品、预售商品等）
        foreach ($orders as $order) {
            foreach ($order->items as $item) {
                if ($item->product && $item->product->is_customized) {
                    return false; // 定制商品不自动合并
                }
            }
        }
        
        return true;
    }
    
    /**
     * 准备合并数据
     */
    private function prepareMergeData(Collection $orders): array
    {
        // 这里需要调用价格计算服务重新计算优惠
        // 简化实现，实际需要完整的价格计算逻辑
        
        $mergedItems = [];
        $subtotal = 0;
        
        // 合并相同商品
        foreach ($orders as $order) {
            foreach ($order->items as $item) {
                $key = $item->product_id . '_' . ($item->unit_id ?? 'default');
                
                if (!isset($mergedItems[$key])) {
                    $mergedItems[$key] = [
                        'product_id' => $item->product_id,
                        'product_name' => $item->product_name,
                        'product_sku' => $item->product_sku,
                        'quantity' => 0,
                        'base_price' => $item->price,
                        'final_price' => $item->price,
                        'unit_id' => $item->unit_id,
                    ];
                }
                
                $mergedItems[$key]['quantity'] += $item->quantity;
                $mergedItems[$key]['item_total'] = $mergedItems[$key]['quantity'] * $mergedItems[$key]['final_price'];
                $subtotal += $item->total;
            }
        }
        
        return [
            'items' => array_values($mergedItems),
            'subtotal' => $subtotal,
            'total_discount' => 0, // 简化，实际需要重新计算
            'payment_discount' => 0,
            'final_total' => $subtotal,
            'original_total' => $subtotal,
            'discount_info' => [],
            'payment_method' => $orders->first()->payment_method,
        ];
    }
} 