<?php

namespace App\Printing\Services\Drivers;

use App\Printing\Contracts\PrintDriverInterface;
use Illuminate\Support\Facades\Log;

class CLodopDriver implements PrintDriverInterface
{
    protected array $config = [];
    protected string $serviceUrl;
    protected string $defaultPrinter;

    public function initialize(array $config = []): void
    {
        $this->config = array_merge([
            'service_url' => env('CLODOP_SERVICE_URL', 'http://localhost:8000/CLodopfuncs.js'),
            'license' => env('CLODOP_LICENSE', ''),
            'default_printer' => env('CLODOP_DEFAULT_PRINTER', ''),
            'debug' => env('PRINTING_DEBUG', false)
        ], $config);

        $this->serviceUrl = $this->config['service_url'];
        $this->defaultPrinter = $this->config['default_printer'];

        if ($this->config['debug']) {
            Log::info('CLodop Driver initialized', $this->config);
        }
    }

    public function printText(string $content, array $options = []): bool
    {
        // C-Lodop是客户端打印，这里返回打印数据供前端使用
        return true;
    }

    public function printHtml(string $html, array $options = []): bool
    {
        // C-Lodop是客户端打印，这里返回打印数据供前端使用
        return true;
    }

    public function getPrinters(): array
    {
        // 返回空数组，实际打印机列表由前端C-Lodop获取
        return [];
    }

    public function getPrinterStatus(string $printerName): array
    {
        return ['status' => 'unknown', 'message' => 'Use frontend CLodop to check printer status'];
    }

    public function setDefaultPrinter(string $printerName): bool
    {
        $this->defaultPrinter = $printerName;
        return true;
    }

    public function preview(string $content, array $options = []): string
    {
        return $this->prepareHtmlContent($content);
    }

    /**
     * 生成C-Lodop打印脚本
     */
    public function generatePrintScript(string $content, array $options = []): string
    {
        $options = array_merge([
            'printer_name' => $this->defaultPrinter,
            'copies' => 1,
            'orientation' => 'portrait',
            'paper_size' => 'A4',
            'margin_top' => 10,
            'margin_bottom' => 10,
            'margin_left' => 10,
            'margin_right' => 10,
            'font_size' => 12,
            'font_name' => '宋体'
        ], $options);

        $escapedContent = $this->escapePrintContent($content);
        $printerName = $options['printer_name'];
        $copies = $options['copies'];
        $orientation = $options['orientation'];
        $paperSize = $options['paper_size'];
        $marginTop = $options['margin_top'];
        $marginLeft = $options['margin_left'];

        $script = "
function getLodop() {
    var LODOP;
    try {
        // 尝试获取C-Lodop对象
        if (typeof CLodopfuncs !== 'undefined') {
            LODOP = CLodopfuncs.getCLodop();
        } else if (typeof getCLodop !== 'undefined') {
            LODOP = getCLodop();
        } else {
            // 兼容旧版本
            LODOP = document.getElementById('LODOP_OB') || document.getElementById('LODOP_EM');
        }
        
        if (!LODOP) {
            console.error('无法获取LODOP对象');
            return null;
        }
        
        return LODOP;
    } catch (error) {
        console.error('获取LODOP对象失败:', error);
        return null;
    }
}

function doPrint() {
    try {
        // 创建LODOP对象
        var LODOP = getLodop();
        if (!LODOP) {
            alert('打印失败：未检测到C-Lodop打印控件！\\n\\n解决方案：\\n1. 请先下载并安装C-Lodop客户端\\n2. 下载地址：http://www.lodop.net/download.html\\n3. 安装后重启浏览器\\n4. 确保C-Lodop服务已启动（默认端口8000）');
            return false;
        }

        // 初始化打印任务
        LODOP.PRINT_INIT('{$paperSize}');
        
        // 设置打印机
        if ('{$printerName}') {
            LODOP.SET_PRINTER_INDEX('{$printerName}');
        }
        
        // 设置份数
        LODOP.SET_PRINT_COPIES({$copies});
        
        // 设置页边距
        LODOP.SET_PRINT_PAGESIZE('{$orientation}', 0, 0, '{$paperSize}');
        
        // 添加打印内容
        LODOP.ADD_PRINT_HTM(
            {$marginTop}, 
            {$marginLeft}, 
            '100%', 
            '100%', 
            `{$escapedContent}`
        );
        
        // 执行打印
        LODOP.PRINT();
        
        return true;
    } catch (error) {
        console.error('打印失败:', error);
        var errorMsg = '打印失败：' + error.message;
        if (error.message.includes('LODOP')) {
            errorMsg += '\\n\\n可能的解决方案：\\n1. 检查C-Lodop服务是否正常运行\\n2. 检查打印机是否正确连接\\n3. 尝试重新安装C-Lodop客户端';
        }
        alert(errorMsg);
        return false;
    }
}

function doPreview() {
    try {
        var LODOP = getLodop();
        if (!LODOP) {
            alert('预览失败：未检测到C-Lodop打印控件！\\n\\n解决方案：\\n1. 请先下载并安装C-Lodop客户端\\n2. 下载地址：http://www.lodop.net/download.html\\n3. 安装后重启浏览器\\n4. 确保C-Lodop服务已启动（默认端口8000）');
            return false;
        }

        LODOP.PRINT_INIT('{$paperSize}');
        
        if ('{$printerName}') {
            LODOP.SET_PRINTER_INDEX('{$printerName}');
        }
        
        LODOP.SET_PRINT_COPIES({$copies});
        LODOP.SET_PRINT_PAGESIZE('{$orientation}', 0, 0, '{$paperSize}');
        
        LODOP.ADD_PRINT_HTM(
            {$marginTop}, 
            {$marginLeft}, 
            '100%', 
            '100%', 
            `{$escapedContent}`
        );
        
        // 打印预览
        LODOP.PREVIEW();
        
        return true;
    } catch (error) {
        console.error('预览失败:', error);
        var errorMsg = '预览失败：' + error.message;
        if (error.message.includes('LODOP')) {
            errorMsg += '\\n\\n可能的解决方案：\\n1. 检查C-Lodop服务是否正常运行\\n2. 检查打印机是否正确连接\\n3. 尝试重新安装C-Lodop客户端';
        }
        alert(errorMsg);
        return false;
    }
}

// 检查C-Lodop是否可用
function checkCLodop() {
    var LODOP = getLodop();
    if (LODOP) {
        console.log('C-Lodop版本:', LODOP.VERSION);
        return true;
    }
    return false;
}
";

        return $script;
    }

    /**
     * 生成小票打印脚本
     */
    public function generateReceiptScript(string $content, array $options = []): string
    {
        $options = array_merge([
            'printer_name' => $this->defaultPrinter,
            'copies' => 1,
            'paper_width' => 80, // 80mm热敏纸
            'font_size' => 12,
            'font_name' => '宋体'
        ], $options);

        $escapedContent = $this->escapePrintContent($content);
        $printerName = $options['printer_name'];
        $copies = $options['copies'];
        $paperWidth = $options['paper_width'];

        $paperWidthMm = $paperWidth * 10; // 转换为0.1mm单位
        $contentWidth = $paperWidthMm - 10; // 内容宽度

        $script = "
function getLodop() {
    var LODOP;
    try {
        // 尝试获取C-Lodop对象
        if (typeof CLodopfuncs !== 'undefined') {
            LODOP = CLodopfuncs.getCLodop();
        } else if (typeof getCLodop !== 'undefined') {
            LODOP = getCLodop();
        } else {
            // 兼容旧版本
            LODOP = document.getElementById('LODOP_OB') || document.getElementById('LODOP_EM');
        }
        
        if (!LODOP) {
            console.error('无法获取LODOP对象');
            return null;
        }
        
        return LODOP;
    } catch (error) {
        console.error('获取LODOP对象失败:', error);
        return null;
    }
}

function doPrintReceipt() {
    try {
        var LODOP = getLodop();
        if (!LODOP) {
            alert('打印失败：未检测到C-Lodop打印控件！\\n\\n解决方案：\\n1. 请先下载并安装C-Lodop客户端\\n2. 下载地址：http://www.lodop.net/download.html\\n3. 安装后重启浏览器\\n4. 确保C-Lodop服务已启动（默认端口8000）');
            return false;
        }

        // 初始化小票打印
        LODOP.PRINT_INIT('小票打印');
        
        // 设置打印机
        if ('{$printerName}') {
            LODOP.SET_PRINTER_INDEX('{$printerName}');
        }
        
        // 设置纸张大小 (80mm宽度)
        LODOP.SET_PRINT_PAGESIZE(1, {$paperWidthMm}, 0, '');
        
        // 设置份数
        LODOP.SET_PRINT_COPIES({$copies});
        
        // 添加小票内容
        LODOP.ADD_PRINT_HTM(
            5,  // 上边距
            5,  // 左边距
            {$contentWidth}, // 宽度
            0,  // 高度自适应
            `{$escapedContent}`
        );
        
        // 执行打印
        LODOP.PRINT();
        
        return true;
    } catch (error) {
        console.error('小票打印失败:', error);
        var errorMsg = '打印失败：' + error.message;
        if (error.message.includes('LODOP')) {
            errorMsg += '\\n\\n可能的解决方案：\\n1. 检查C-Lodop服务是否正常运行\\n2. 检查打印机是否正确连接\\n3. 尝试重新安装C-Lodop客户端';
        }
        alert(errorMsg);
        return false;
    }
}

// 检查C-Lodop是否可用
function checkCLodop() {
    var LODOP = getLodop();
    if (LODOP) {
        console.log('C-Lodop版本:', LODOP.VERSION);
        return true;
    }
    return false;
}
";

        return $script;
    }

    /**
     * 转义打印内容中的特殊字符
     */
    protected function escapePrintContent(string $content): string
    {
        // 转义JavaScript字符串中的特殊字符
        $content = str_replace(['\\', '`', '$'], ['\\\\', '\\`', '\\$'], $content);
        return $content;
    }

    /**
     * 准备HTML内容，添加必要的样式和结构
     */
    protected function prepareHtmlContent(string $html): string
    {
        // 如果HTML不包含完整的文档结构，则添加
        if (!str_contains($html, '<html>')) {
            $html = "
<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <style>
        body { 
            font-family: '宋体', SimSun, serif; 
            font-size: 12px; 
            margin: 0; 
            padding: 10px; 
            line-height: 1.4;
        }
        .receipt { 
            width: 100%; 
            max-width: 300px; 
        }
        .header { 
            text-align: center; 
            margin-bottom: 10px; 
        }
        .store-name { 
            font-size: 16px; 
            font-weight: bold; 
            margin-bottom: 5px;
        }
        .info { 
            margin-bottom: 10px; 
        }
        .info-item { 
            margin: 2px 0; 
            font-size: 11px;
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin: 5px 0;
        }
        th, td { 
            padding: 2px 4px; 
            text-align: left; 
            border-bottom: 1px dashed #ccc; 
            font-size: 11px;
        }
        th { 
            font-weight: bold; 
        }
        .total { 
            font-weight: bold; 
            font-size: 14px; 
            text-align: right;
            margin-top: 10px;
        }
        .footer { 
            text-align: center; 
            margin-top: 15px; 
            font-size: 10px; 
            border-top: 1px dashed #ccc;
            padding-top: 5px;
        }
        .divider {
            border-top: 1px dashed #ccc;
            margin: 10px 0;
        }
        @media print {
            body { margin: 0; padding: 5px; }
        }
    </style>
</head>
<body>
    {$html}
</body>
</html>";
        }

        return $html;
    }
} 