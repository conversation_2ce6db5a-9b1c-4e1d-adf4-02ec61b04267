/* components/search-header/search-header.wxss */

/* 全新搜索头部组件样式 */

/* 搜索头部容器 */
.search-header {
  display: flex;
  align-items: center;
  width: 100%;
  height: 64rpx;
  box-sizing: border-box;
}

/* 搜索输入区域 */
.search-input-area {
  display: flex;
  flex: 1;
  align-items: center;
  height: 64rpx;
  padding: 0 16rpx;
  background-color: rgba(255, 255, 255, 0.9); /* 半透明白色背景 */
  border-radius: 32rpx;
  box-sizing: border-box;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

/* 搜索输入区域获得焦点时 */
.search-input-area.focused {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
}

/* 搜索图标 */
.search-icon {
  margin-right: 6rpx;
  color: #45a049; /* 与背景色匹配的图标颜色 */
}

/* 清除图标 */
.clear-icon {
  color: #999;
}

/* 搜索输入框 */
.search-input {
  flex: 1;
  height: 64rpx;
  line-height: 64rpx;
  font-size: 28rpx;
  background-color: transparent;
  margin-left: 10rpx;
  color: #333;
}

/* 输入框占位符 */
.placeholder {
  color: #999999;
  font-size: 26rpx;
}

/* 搜索按钮 */
.search-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #4CAF50;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
  transition: all 0.2s;
  flex-shrink: 0;
}

/* 左侧搜索按钮 */
.search-header .search-btn:first-child {
  margin-right: 10rpx;
}

/* 右侧搜索按钮 */
.search-header .search-btn:last-child {
  margin-left: 10rpx;
}

/* 搜索按钮点击效果 */
.search-btn:active {
  transform: scale(0.92);
  background-color: #43a047;
  box-shadow: 0 1rpx 4rpx rgba(76, 175, 80, 0.2);
}

/* 动作按钮（取消按钮） */
.action-btn {
  padding-left: 16rpx;
  font-size: 28rpx;
  color: #fff;
  display: flex;
  align-items: center;
  height: 64rpx;
}

.action-btn:active {
  opacity: 0.7;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .search-input-area {
    background-color: #2a2a2a;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
  }
  
  .search-input {
    color: #ffffff;
  }
  
  .placeholder {
    color: #888888;
  }
  
  .search-btn {
    background-color: #43a047;
  }
  
  .search-btn:active {
    background-color: #388e3c;
  }
  
  .action-btn {
    color: #dddddd;
  }
} 