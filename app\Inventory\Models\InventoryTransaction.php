<?php

namespace App\Inventory\Models;

use App\Models\User;use App\Warehouse\Models\Warehouse;use App\Product\Models\Product;
use App\Unit\Models\Unit;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryTransaction extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'transaction_type_id',
        'reference_type',
        'reference_id',
        'product_id',
        'warehouse_id',
        'quantity',
        'unit_id',
        'unit_price',
        'total_amount',
        'status',
        'notes',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    /**
     * 获取事务类型
     */
    public function transactionType()
    {
        return $this->belongsTo(InventoryTransactionType::class, 'transaction_type_id');
    }

    /**
     * 获取关联对象（多态关联）
     */
    public function reference()
    {
        return $this->morphTo();
    }

    /**
     * 获取商品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 获取仓库
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * 获取单位
     */
    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * 获取创建人
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取更新人
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * 应用库存事务（更新库存）
     */
    public function applyToInventory()
    {
        if ($this->status !== 'completed' || !$this->transactionType->affects_inventory) {
            return false;
        }

        $inventory = Inventory::firstOrCreate(
            [
                'warehouse_id' => $this->warehouse_id,
                'product_id' => $this->product_id,
            ],
            [
                'unit_id' => $this->unit_id,
                'stock' => 0,
            ]
        );

        // 使用新的单位转换系统将事务数量转换为库存单位
        try {
            $product = $this->product;
            $convertedQuantity = $product->convertQuantity(
                $this->quantity,
                $this->unit_id,
                $inventory->unit_id
            );
            
            if ($convertedQuantity === null) {
                throw new \Exception("无法转换单位：从 {$this->unit_id} 到 {$inventory->unit_id}");
            }
            
            $inventory->stock += $convertedQuantity;
            $inventory->save();

            // 更新产品总库存
            $this->product->updateTotalStock();

            // 更新仓库总库存
            $this->warehouse->updateTotalStock();

            return true;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('应用库存事务失败', [
                'transaction_id' => $this->id,
                'error' => $e->getMessage(),
                'product_id' => $this->product_id,
                'from_unit_id' => $this->unit_id,
                'to_unit_id' => $inventory->unit_id
            ]);
            return false;
        }
    }
}
