<?php

namespace App\Printing\Providers;

use Illuminate\Support\ServiceProvider;
use App\Printing\Contracts\PrintDriverInterface;
use App\Printing\Services\Drivers\BrowserDriver;
use App\Printing\Services\Drivers\CLodopDriver;
use App\Printing\Services\PrintingService;

class PrintingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // 注册打印驱动
        $this->app->bind(PrintDriverInterface::class, function ($app) {
            $driver = config('printing.default', 'clodop');
            
            switch ($driver) {
                case 'browser':
                    $config = config('printing.drivers.browser', []);
                    $browserDriver = new BrowserDriver();
                    $browserDriver->initialize($config);
                    return $browserDriver;

                case 'clodop':
                case 'lodop':
                    $config = config('printing.drivers.lodop', []);
                    $clodopDriver = new CLodopDriver();
                    $clodopDriver->initialize($config);
                    return $clodopDriver;

                default:
                    // 默认使用CLodop
                    $config = config('printing.drivers.lodop', []);
                    $clodopDriver = new CLodopDriver();
                    $clodopDriver->initialize($config);
                    return $clodopDriver;
            }
        });

        // 注册打印服务
        $this->app->singleton(PrintingService::class, function ($app) {
            $driver = $app->make(PrintDriverInterface::class);
            return new PrintingService($driver);
        });

        // 注册别名
        $this->app->alias(PrintingService::class, 'printing');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // 发布配置文件
        $this->publishes([
            __DIR__ . '/../../../config/printing.php' => config_path('printing.php'),
        ], 'printing-config');

        // 发布迁移文件
        $this->publishes([
            __DIR__ . '/../../../database/migrations/2024_01_03_000001_create_print_records_table.php' => database_path('migrations/2024_01_03_000001_create_print_records_table.php'),
        ], 'printing-migrations');

        // 加载路由
        if (file_exists(__DIR__ . '/../routes/api.php')) {
            $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
        }
    }
} 