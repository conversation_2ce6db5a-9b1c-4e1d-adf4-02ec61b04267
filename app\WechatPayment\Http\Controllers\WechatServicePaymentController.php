<?php

namespace App\WechatPayment\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Order\Models\Order;
use App\WechatPayment\Models\WechatServicePayment;
use App\WechatPayment\Models\WechatServiceProvider;
use App\WechatPayment\Models\WechatSubMerchant;
use App\WechatPayment\Services\WechatServiceProviderPayment;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WechatServicePaymentController extends Controller
{
    /**
     * 获取小程序支付参数
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function miniAppPay(Request $request)
    {
        try {
            // 验证请求参数
            $request->validate([
                'order_id' => 'required|exists:orders,id',
                'sub_merchant_id' => 'required|exists:wechat_sub_merchants,id',
            ]);
            
            $orderId = $request->input('order_id');
            $subMerchantId = $request->input('sub_merchant_id');
            
            // 获取当前认证用户的openid
            $user = auth('sanctum')->user();
            if (!$user || !$user->openid) {
                return response()->json([
                    'code' => 400,
                    'message' => '用户未绑定微信或openid缺失，请重新登录',
                ]);
            }
            $openid = $user->openid;
            
            // 获取订单信息
            $order = Order::findOrFail($orderId);
            
            // 检查订单状态
            if ($order->payment_status === 'paid') {
                return response()->json([
                    'code' => 400,
                    'message' => '该订单已支付',
                ]);
            }
            
            // 直接获取指定的子商户
            $subMerchant = WechatSubMerchant::where('id', $subMerchantId)
                ->where('is_active', 1)
                ->first();
                
            if (!$subMerchant) {
                return response()->json([
                    'code' => 400,
                    'message' => '未找到有效的微信支付子商户',
                ]);
            }
            
            // 获取服务商信息
            $provider = WechatServiceProvider::find($subMerchant->provider_id);
            if (!$provider || !$provider->is_active) {
                return response()->json([
                    'code' => 400,
                    'message' => '微信支付服务商不可用',
                ]);
            }
            
            // 创建支付服务实例
            $paymentService = new WechatServiceProviderPayment($provider, $subMerchant);
            
            // 生成支付参数
            $payParams = $paymentService->createMiniAppPay($order, $openid);
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $payParams,
            ]);
            
        } catch (Exception $e) {
            Log::error('微信支付下单失败: ' . $e->getMessage(), [
                'order_id' => $request->input('order_id'),
                'openid' => $request->input('openid'),
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '支付请求失败: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * 查询订单支付状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function queryOrderStatus(Request $request)
    {
        try {
            // 验证请求参数
            $request->validate([
                'order_id' => 'required|exists:orders,id',
                'sub_merchant_id' => 'required|exists:wechat_sub_merchants,id',
            ]);
            
            $orderId = $request->input('order_id');
            $subMerchantId = $request->input('sub_merchant_id');
            
            // 获取订单信息
            $order = Order::findOrFail($orderId);
            
            // 如果订单已支付，直接返回成功
            if ($order->payment_status === 'paid') {
                return response()->json([
                    'code' => 0,
                    'message' => 'success',
                    'data' => [
                        'paid' => true,
                        'payment_method' => $order->payment_method,
                        'paid_at' => $order->paid_at,
                    ],
                ]);
            }
            
            // 如果订单未支付且有支付单号，查询支付状态
            if ($order->payment_no) {
                // 直接获取指定的子商户
                $subMerchant = WechatSubMerchant::where('id', $subMerchantId)
                    ->where('is_active', 1)
                    ->first();
                    
                if (!$subMerchant) {
                    return response()->json([
                        'code' => 400,
                        'message' => '未找到有效的微信支付子商户',
                    ]);
                }
                
                // 获取服务商信息
                $provider = WechatServiceProvider::find($subMerchant->provider_id);
                if (!$provider || !$provider->is_active) {
                    return response()->json([
                        'code' => 400,
                        'message' => '微信支付服务商不可用',
                    ]);
                }
                
                // 创建支付服务实例
                $paymentService = new WechatServiceProviderPayment($provider, $subMerchant);
                
                // 查询订单状态
                $result = $paymentService->queryOrder($order->payment_no);
                
                // 检查支付结果
                $paid = isset($result['trade_state']) && $result['trade_state'] === 'SUCCESS';
                
                // 如果已支付，更新订单状态
                if ($paid && $order->payment_status !== 'paid') {
                    $order->update([
                        'payment_status' => 'paid',
                        'paid_at' => now(),
                    ]);
                }
                
                return response()->json([
                    'code' => 0,
                    'message' => 'success',
                    'data' => [
                        'paid' => $paid,
                        'payment_method' => '微信支付',
                        'trade_state' => $result['trade_state'] ?? 'NOTPAY',
                        'paid_at' => $paid ? now() : null,
                    ],
                ]);
            }
            
            // 订单未支付
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => [
                    'paid' => false,
                ],
            ]);
            
        } catch (Exception $e) {
            Log::error('查询订单支付状态失败: ' . $e->getMessage(), [
                'order_id' => $request->input('order_id'),
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '查询支付状态失败: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * 微信支付回调
     *
     * @param Request $request
     * @return string
     */
    public function payNotify(Request $request)
    {
        // 获取XML数据
        $xml = $request->getContent();
        Log::info('微信支付回调数据: ' . $xml);
        
        try {
            // 解析XML数据
            $data = $this->xmlToArray($xml);
            
            // 判断返回状态
            if ($data['return_code'] !== 'SUCCESS') {
                Log::error('微信支付回调通信失败: ' . $data['return_msg']);
                return $this->returnXml(['return_code' => 'FAIL', 'return_msg' => $data['return_msg']]);
            }
            
            // 判断业务结果
            if ($data['result_code'] !== 'SUCCESS') {
                Log::error('微信支付回调业务失败: ' . $data['err_code'] . ' - ' . $data['err_code_des']);
                return $this->returnXml(['return_code' => 'FAIL', 'return_msg' => $data['err_code_des']]);
            }
            
            // 首先检查是否为订单更正补款
            $correctionService = app(\App\Order\Services\OrderCorrectionService::class);
            $correctionService->handleWechatSupplementCallback($data['out_trade_no'], $data);

            // 查找对应的订单
            $order = Order::where('payment_no', $data['out_trade_no'])->first();
            
            if ($order) {
                // 如果订单已支付，直接返回成功
                if ($order->payment_status === 'paid') {
                    return $this->returnXml(['return_code' => 'SUCCESS', 'return_msg' => 'OK']);
                }
                
                // 更新订单状态
                $order->update([
                    'payment_status' => 'paid',
                    'paid_at' => now(),
                ]);
                
                // 记录支付数据
                WechatServicePayment::create([
                    'provider_id' => $data['mch_id'] ?? null,
                    'sub_merchant_id' => $data['sub_mch_id'] ?? null,
                    'order_id' => $order->id,
                    'transaction_id' => $data['transaction_id'],
                    'out_trade_no' => $data['out_trade_no'],
                    'trade_type' => $data['trade_type'],
                    'amount' => $data['total_fee'] / 100, // 转换为元
                    'currency' => 'CNY',
                    'status' => 'success',
                    'paid_at' => now(),
                    'raw_data' => json_encode($data),
                ]);
            } else {
                // 订单不存在，可能是订单更正补款，已在上面处理
                Log::info('微信支付回调：未找到对应订单，可能为订单更正补款', ['out_trade_no' => $data['out_trade_no']]);
            }
            
            // 返回成功
            return $this->returnXml(['return_code' => 'SUCCESS', 'return_msg' => 'OK']);
            
        } catch (Exception $e) {
            Log::error('微信支付回调处理异常: ' . $e->getMessage());
            return $this->returnXml(['return_code' => 'FAIL', 'return_msg' => '服务器内部错误']);
        }
    }

    /**
     * 微信退款回调
     *
     * @param Request $request
     * @return string
     */
    public function refundNotify(Request $request)
    {
        // 获取XML数据
        $xml = $request->getContent();
        Log::info('微信退款回调数据: ' . $xml);
        
        try {
            // 解析XML数据
            $data = $this->xmlToArray($xml);
            
            // 判断返回状态
            if ($data['return_code'] !== 'SUCCESS') {
                Log::error('微信退款回调通信失败: ' . $data['return_msg']);
                return $this->returnXml(['return_code' => 'FAIL', 'return_msg' => $data['return_msg']]);
            }
            
            // 更新退款记录状态
            // 实际业务中需要解密退款结果
            
            // 返回成功
            return $this->returnXml(['return_code' => 'SUCCESS', 'return_msg' => 'OK']);
            
        } catch (Exception $e) {
            Log::error('微信退款回调处理异常: ' . $e->getMessage());
            return $this->returnXml(['return_code' => 'FAIL', 'return_msg' => '服务器内部错误']);
        }
    }

    /**
     * 返回XML响应
     *
     * @param array $array
     * @return string
     */
    protected function returnXml(array $array): string
    {
        $xml = '<xml>';
        foreach ($array as $key => $val) {
            $xml .= '<' . $key . '>' . $val . '</' . $key . '>';
        }
        $xml .= '</xml>';
        
        return $xml;
    }

    /**
     * 将XML转换为数组
     *
     * @param string $xml
     * @return array
     */
    protected function xmlToArray(string $xml): array
    {
        libxml_disable_entity_loader(true);
        $data = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
        return json_decode(json_encode($data), true);
    }

    /**
     * 获取支付记录列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function records(Request $request)
    {
        $query = WechatServicePayment::query()
            ->with(['subMerchant', 'provider', 'order']);
            
        // 搜索条件
        if ($request->has('order_id') && $request->input('order_id') > 0) {
            $query->where('order_id', $request->input('order_id'));
        }
        
        if ($request->has('transaction_id')) {
            $query->where('transaction_id', 'like', '%' . $request->input('transaction_id') . '%');
        }
        
        if ($request->has('out_trade_no')) {
            $query->where('out_trade_no', 'like', '%' . $request->input('out_trade_no') . '%');
        }
        
        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }
        
        if ($request->has('provider_id') && $request->input('provider_id') > 0) {
            $query->where('provider_id', $request->input('provider_id'));
        }
        
        if ($request->has('sub_merchant_id') && $request->input('sub_merchant_id') > 0) {
            $query->where('sub_merchant_id', $request->input('sub_merchant_id'));
        }
        
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('paid_at', [$request->input('start_date'), $request->input('end_date')]);
        }
        
        $payments = $query->orderBy('created_at', 'desc')->paginate(10);
        
        return response()->json([
            'data' => $payments->items(),
            'total' => $payments->total(),
            'current_page' => $payments->currentPage(),
            'last_page' => $payments->lastPage(),
        ]);
    }


} 