<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Facades\Log;
use Illuminate\Auth\AuthenticationException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            // 记录详细的异常信息
            Log::error('应用异常', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'class' => get_class($e)
            ]);
        });
        
        // 增加超时错误的处理
        $this->renderable(function (\Exception $e, $request) {
            if ($request->expectsJson()) {
                if ($e instanceof \Illuminate\Http\Client\ConnectionException) {
                    return response()->json([
                        'code' => 500,
                        'message' => '请求超时，请稍后重试',
                        'error' => 'connection_timeout'
                    ], 500);
                }
                
                if ($e instanceof \Symfony\Component\HttpKernel\Exception\HttpException 
                    && $e->getStatusCode() === 504) {
                    return response()->json([
                        'code' => 504,
                        'message' => '请求处理超时，请重试',
                        'error' => 'gateway_timeout'
                    ], 504);
                }
            }
            
            return null;
        });
    }

    /**
     * Convert an authentication exception into a response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Auth\AuthenticationException  $exception
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        // 对于API请求，返回JSON响应而不是重定向
        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'code' => 401,
                'message' => '未认证或认证已过期',
                'error' => 'unauthenticated'
            ], 401);
        }

        // 对于web请求，尝试重定向到登录页面
        try {
            return redirect()->guest(route('login'));
        } catch (\Exception $e) {
            // 如果没有login路由，返回401错误页面
            return response()->view('errors.401', [], 401);
        }
    }
}
