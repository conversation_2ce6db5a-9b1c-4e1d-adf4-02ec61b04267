<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 简化折扣表，移除百分比选项，只保留固定金额
     */
    public function up(): void
    {
        // 1. 更新所有现有记录为 fixed_amount
        DB::table('product_member_discounts')
            ->where('discount_type', '!=', 'fixed_amount')
            ->update(['discount_type' => 'fixed_amount']);
            
        DB::table('category_member_discounts')
            ->where('discount_type', '!=', 'fixed_amount')
            ->update(['discount_type' => 'fixed_amount']);
            
        DB::table('category_region_prices')
            ->where('discount_type', '!=', 'fixed_amount')
            ->update(['discount_type' => 'fixed_amount']);

        // 2. 修改字段为只支持 fixed_amount
        Schema::table('product_member_discounts', function (Blueprint $table) {
            $table->enum('discount_type', ['fixed_amount'])->default('fixed_amount')->comment('优惠类型：固定金额')->change();
        });
        
        Schema::table('category_member_discounts', function (Blueprint $table) {
            $table->enum('discount_type', ['fixed_amount'])->default('fixed_amount')->comment('优惠类型：固定金额')->change();
        });
        
        Schema::table('category_region_prices', function (Blueprint $table) {
            $table->enum('discount_type', ['fixed_amount'])->default('fixed_amount')->comment('优惠类型：固定金额')->change();
        });

        // 3. 更新字段注释，明确说明是固定金额
        Schema::table('product_member_discounts', function (Blueprint $table) {
            $table->decimal('discount_value', 10, 2)->comment('固定减免金额（元）')->change();
        });
        
        Schema::table('category_member_discounts', function (Blueprint $table) {
            $table->decimal('discount_value', 10, 2)->comment('固定减免金额（元）')->change();
        });
        
        Schema::table('category_region_prices', function (Blueprint $table) {
            $table->decimal('discount_value', 10, 2)->comment('固定减免金额（元）')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复原来的 enum 定义
        Schema::table('product_member_discounts', function (Blueprint $table) {
            $table->enum('discount_type', ['fixed_amount', 'percentage'])->default('fixed_amount')->comment('优惠类型')->change();
            $table->decimal('discount_value', 10, 2)->comment('优惠值')->change();
        });
        
        Schema::table('category_member_discounts', function (Blueprint $table) {
            $table->enum('discount_type', ['fixed_amount', 'percentage'])->default('fixed_amount')->comment('优惠类型')->change();
            $table->decimal('discount_value', 10, 2)->comment('优惠值')->change();
        });
        
        Schema::table('category_region_prices', function (Blueprint $table) {
            $table->enum('discount_type', ['fixed_amount', 'percentage'])->default('fixed_amount')->comment('优惠类型')->change();
            $table->decimal('discount_value', 10, 2)->comment('优惠值')->change();
        });
    }
}; 