<?php

namespace App\WechatMp\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WechatUser extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'wechat_users';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'openid',
        'unionid',
        'session_key',
        'nickname',
        'avatar_url',
        'gender',
        'country',
        'province',
        'city',
        'language',
    ];

    /**
     * 隐藏的属性
     *
     * @var array
     */
    protected $hidden = [
        'session_key',
    ];

    /**
     * 获取关联的用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
} 