<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wechat_service_refunds', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payment_id')->comment('关联支付记录ID')
                  ->constrained('wechat_service_payments')
                  ->onDelete('cascade');
            $table->foreignId('order_id')->nullable()->comment('相关订单ID')
                  ->constrained('orders')
                  ->onDelete('set null');
            $table->string('out_refund_no')->comment('商户退款单号')->unique();
            $table->string('refund_id')->nullable()->comment('微信退款单号');
            $table->string('transaction_id')->nullable()->comment('微信支付交易号');
            $table->string('out_trade_no')->comment('商户订单号');
            $table->decimal('total_fee', 10, 2)->comment('订单总金额');
            $table->decimal('refund_fee', 10, 2)->comment('退款金额');
            $table->decimal('service_refund_fee', 10, 2)->default(0)->comment('退回的服务费');
            $table->string('refund_status')->default('PROCESSING')->comment('退款状态');
            $table->string('refund_channel')->nullable()->comment('退款渠道');
            $table->text('refund_reason')->nullable()->comment('退款原因');
            $table->timestamp('refund_time')->nullable()->comment('退款完成时间');
            $table->text('notify_data')->nullable()->comment('回调原始数据');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wechat_service_refunds');
    }
};
