<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 飞蛾云配置
    |--------------------------------------------------------------------------
    |
    | 飞蛾云云打印机相关配置
    |
    */

    // 自动打印设置
    'auto_print_enabled' => env('FLYCLOUD_AUTO_PRINT_ENABLED', true),

    // 自动打印触发时机
    'auto_print_triggers' => [
        'order_created' => env('FLYCLOUD_AUTO_PRINT_ON_CREATE', true),  // 订单创建时
        'order_paid' => env('FLYCLOUD_AUTO_PRINT_ON_PAID', false),      // 订单付款时
    ],

    // 默认打印设置
    'default_print_type' => env('FLYCLOUD_DEFAULT_PRINT_TYPE', 'order'),
    'default_copies' => env('FLYCLOUD_DEFAULT_COPIES', 1),

    // API配置
    'api_url' => env('FLYCLOUD_API_URL', 'http://api.feieyun.cn/Api/Open/'),
    'user' => env('FLYCLOUD_USER', ''),
    'ukey' => env('FLYCLOUD_UKEY', ''),
    'timeout' => env('FLYCLOUD_TIMEOUT', 30),

    // 仓库商品分配规则
    'warehouse_assignment' => [
        // 默认仓库ID（当商品没有指定仓库时使用）
        'default_warehouse_id' => env('DEFAULT_WAREHOUSE_ID', 1),
        
        // 商品仓库分配策略：product（商品默认仓库）、inventory（库存优先）、region（区域优先）
        'assignment_strategy' => env('WAREHOUSE_ASSIGNMENT_STRATEGY', 'product'),
    ],

    // 打印失败处理
    'print_failure_handling' => [
        'retry_attempts' => env('FLYCLOUD_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('FLYCLOUD_RETRY_DELAY', 10), // 秒
        'notify_on_failure' => env('FLYCLOUD_NOTIFY_ON_FAILURE', true),
    ],

    // 小票格式设置
    'receipt_format' => [
        'store_name' => env('FLYCLOUD_STORE_NAME', '万家生鲜'),
        'service_phone' => env('FLYCLOUD_SERVICE_PHONE', '************'),
        'width' => env('FLYCLOUD_RECEIPT_WIDTH', '58'), // 58mm or 80mm
        'font_size' => env('FLYCLOUD_FONT_SIZE', 12),
        'line_height' => env('FLYCLOUD_LINE_HEIGHT', 1.2),
        'auto_cut' => env('FLYCLOUD_AUTO_CUT', true),
    ],
]; 