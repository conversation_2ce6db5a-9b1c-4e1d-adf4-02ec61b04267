<?php

namespace App\Migration\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class DatabaseMigrationService
{
    private $oldConnection;
    private $newConnection;
    
    public function __construct($oldConnectionName = 'mysql_old', $newConnectionName = 'mysql')
    {
        $this->oldConnection = $oldConnectionName;
        $this->newConnection = $newConnectionName;
    }
    
    /**
     * 执行完整的数据库迁移
     */
    public function migrate()
    {
        try {
            Log::info('开始数据库迁移从MySQL 5.7到8.4');
            
            // 1. 预检查
            $this->preCheck();
            
            // 2. 处理字符集兼容性
            $this->handleCharsetCompatibility();
            
            // 3. 处理SQL模式兼容性
            $this->handleSqlModeCompatibility();
            
            // 4. 迁移数据
            $this->migrateData();
            
            // 5. 后处理
            $this->postProcessing();
            
            Log::info('数据库迁移完成');
            return true;
            
        } catch (Exception $e) {
            Log::error('数据库迁移失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 预检查
     */
    private function preCheck()
    {
        // 检查旧数据库连接
        if (!$this->testConnection($this->oldConnection)) {
            throw new Exception('无法连接到旧数据库');
        }
        
        // 检查新数据库连接
        if (!$this->testConnection($this->newConnection)) {
            throw new Exception('无法连接到新数据库');
        }
        
        // 检查MySQL版本
        $oldVersion = $this->getMysqlVersion($this->oldConnection);
        $newVersion = $this->getMysqlVersion($this->newConnection);
        
        Log::info("旧数据库版本: {$oldVersion}, 新数据库版本: {$newVersion}");
    }
    
    /**
     * 处理字符集兼容性
     */
    private function handleCharsetCompatibility()
    {
        // MySQL 8.0默认字符集是utf8mb4，排序规则是utf8mb4_0900_ai_ci
        // 但为了兼容性，我们使用utf8mb4_unicode_ci
        
        DB::connection($this->newConnection)->statement("
            SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci
        ");
        
        // 设置字符集变量
        DB::connection($this->newConnection)->statement("
            SET character_set_client = utf8mb4,
                character_set_connection = utf8mb4,
                character_set_results = utf8mb4,
                collation_connection = utf8mb4_unicode_ci
        ");
    }
    
    /**
     * 处理SQL模式兼容性
     */
    private function handleSqlModeCompatibility()
    {
        // MySQL 8.0的SQL模式更严格，需要调整
        $compatibleSqlMode = "STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION";
        
        DB::connection($this->newConnection)->statement("
            SET SESSION sql_mode = '{$compatibleSqlMode}'
        ");
        
        // 禁用ONLY_FULL_GROUP_BY模式，避免GROUP BY问题
        DB::connection($this->newConnection)->statement("
            SET SESSION sql_mode = REPLACE(@@sql_mode, 'ONLY_FULL_GROUP_BY', '')
        ");
    }
    
    /**
     * 迁移数据
     */
    private function migrateData()
    {
        // 获取所有表
        $tables = $this->getTables($this->oldConnection);
        
        foreach ($tables as $table) {
            Log::info("迁移表: {$table}");
            $this->migrateTable($table);
        }
    }
    
    /**
     * 迁移单个表
     */
    private function migrateTable($tableName)
    {
        try {
            // 获取表结构
            $createTableSql = $this->getCreateTableSql($tableName);
            
            // 修复表结构兼容性问题
            $createTableSql = $this->fixTableStructure($createTableSql);
            
            // 在新数据库中创建表
            DB::connection($this->newConnection)->statement("DROP TABLE IF EXISTS `{$tableName}`");
            DB::connection($this->newConnection)->statement($createTableSql);
            
            // 迁移数据
            $this->migrateTableData($tableName);
            
        } catch (Exception $e) {
            Log::error("迁移表 {$tableName} 失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 修复表结构兼容性问题
     */
    private function fixTableStructure($createTableSql)
    {
        // 1. 处理时间戳默认值问题
        $createTableSql = preg_replace(
            '/timestamp\s+NOT\s+NULL\s+DEFAULT\s+CURRENT_TIMESTAMP\s+ON\s+UPDATE\s+CURRENT_TIMESTAMP/i',
            'timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            $createTableSql
        );
        
        // 2. 处理零日期问题
        $createTableSql = str_replace(
            "DEFAULT '0000-00-00 00:00:00'",
            "DEFAULT NULL",
            $createTableSql
        );
        
        $createTableSql = str_replace(
            "DEFAULT '0000-00-00'",
            "DEFAULT NULL",
            $createTableSql
        );
        
        // 3. 处理字符集和排序规则
        $createTableSql = preg_replace(
            '/CHARSET=utf8(?!mb4)/i',
            'CHARSET=utf8mb4',
            $createTableSql
        );
        
        $createTableSql = preg_replace(
            '/COLLATE=utf8_general_ci/i',
            'COLLATE=utf8mb4_unicode_ci',
            $createTableSql
        );
        
        // 4. 处理MyISAM引擎（如果有的话）
        $createTableSql = str_replace('ENGINE=MyISAM', 'ENGINE=InnoDB', $createTableSql);
        
        return $createTableSql;
    }
    
    /**
     * 迁移表数据
     */
    private function migrateTableData($tableName)
    {
        $batchSize = 1000;
        $offset = 0;
        
        do {
            $data = DB::connection($this->oldConnection)
                ->table($tableName)
                ->offset($offset)
                ->limit($batchSize)
                ->get();
            
            if ($data->count() > 0) {
                // 处理数据兼容性
                $processedData = $this->processDataCompatibility($data->toArray(), $tableName);
                
                // 插入数据
                DB::connection($this->newConnection)
                    ->table($tableName)
                    ->insert($processedData);
                
                $offset += $batchSize;
                Log::info("已迁移表 {$tableName} 的 {$offset} 条记录");
            }
            
        } while ($data->count() == $batchSize);
    }
    
    /**
     * 处理数据兼容性
     */
    private function processDataCompatibility($data, $tableName)
    {
        $processedData = [];
        
        foreach ($data as $row) {
            $processedRow = [];
            
            foreach ($row as $column => $value) {
                // 处理零日期
                if (in_array($value, ['0000-00-00', '0000-00-00 00:00:00'])) {
                    $processedRow[$column] = null;
                } else {
                    $processedRow[$column] = $value;
                }
            }
            
            $processedData[] = $processedRow;
        }
        
        return $processedData;
    }
    
    /**
     * 后处理
     */
    private function postProcessing()
    {
        // 重建索引和外键
        $this->rebuildIndexes();
        
        // 更新表统计信息
        $this->updateTableStatistics();
    }
    
    /**
     * 重建索引
     */
    private function rebuildIndexes()
    {
        $tables = $this->getTables($this->newConnection);
        
        foreach ($tables as $table) {
            try {
                DB::connection($this->newConnection)->statement("ANALYZE TABLE `{$table}`");
            } catch (Exception $e) {
                Log::warning("分析表 {$table} 失败: " . $e->getMessage());
            }
        }
    }
    
    /**
     * 更新表统计信息
     */
    private function updateTableStatistics()
    {
        try {
            DB::connection($this->newConnection)->statement("FLUSH TABLES");
        } catch (Exception $e) {
            Log::warning("刷新表缓存失败: " . $e->getMessage());
        }
    }
    
    /**
     * 测试数据库连接
     */
    private function testConnection($connectionName)
    {
        try {
            DB::connection($connectionName)->getPdo();
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取MySQL版本
     */
    private function getMysqlVersion($connectionName)
    {
        return DB::connection($connectionName)->selectOne('SELECT VERSION() as version')->version;
    }
    
    /**
     * 获取所有表名
     */
    private function getTables($connectionName)
    {
        $database = DB::connection($connectionName)->getDatabaseName();
        
        $tables = DB::connection($connectionName)
            ->select("SHOW TABLES FROM `{$database}`");
        
        return array_map(function($table) use ($database) {
            return $table->{"Tables_in_{$database}"};
        }, $tables);
    }
    
    /**
     * 获取建表SQL
     */
    private function getCreateTableSql($tableName)
    {
        $result = DB::connection($this->oldConnection)
            ->select("SHOW CREATE TABLE `{$tableName}`");
        
        return $result[0]->{'Create Table'};
    }
} 