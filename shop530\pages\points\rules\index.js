// pages/points/rules/index.js - 积分规则页
const PointsAPI = require('../../../utils/pointsApi');

Page({
  data: {
    // 积分规则数据
    rulesData: {},
    
    // 页面加载状态
    loading: true,
    
    // 当前展开的规则项
    expandedItems: {}
  },

  onLoad() {
    this.loadRulesData();
  },

  onPullDownRefresh() {
    this.loadRulesData();
  },

  // ==================== 数据加载 ====================

  async loadRulesData() {
    wx.showLoading({ title: '加载中...' });

    try {
      const result = await PointsAPI.getPointsRules();
      this.setData({
        rulesData: result.data || {},
        loading: false
      });
    } catch (error) {
      console.error('加载积分规则失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
      wx.stopPullDownRefresh();
    }
  },

  // ==================== 用户交互 ====================

  onToggleExpand(e) {
    const key = e.currentTarget.dataset.key;
    const expandedItems = { ...this.data.expandedItems };
    expandedItems[key] = !expandedItems[key];
    
    this.setData({ expandedItems });
  },

  onContactService() {
    wx.showModal({
      title: '联系客服',
      content: '如有疑问，请联系在线客服或拨打客服热线',
      confirmText: '联系客服',
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到客服页面或拨打电话
          wx.showToast({
            title: '客服功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // ==================== 页面跳转 ====================

  goToPointsMall() {
    wx.switchTab({
      url: '/pages/points/index'
    });
  },

  goToTransactions() {
    wx.navigateTo({
      url: '/pages/points/transactions/index'
    });
  },

  // ==================== 工具方法 ====================

  formatPoints(points) {
    return PointsAPI.formatPoints(points);
  }
}); 