<?php

namespace App\Employee;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Illuminate\Routing\Router;

class EmployeeServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     *
     * @return void
     */
    public function register()
    {
        // 注册服务
        $this->app->bind('App\Employee\Services\EmployeeService', function ($app) {
            return new \App\Employee\Services\EmployeeService();
        });
    }

    /**
     * 引导服务
     *
     * @return void
     */
    public function boot()
    {
        // 注册路由
        $this->registerRoutes();
        
        // 注册中间件
        $this->registerMiddleware();
        
        // 记录模块启动信息
        if (config('app.env') === 'local' || config('app.env') === 'development') {
            \Illuminate\Support\Facades\Log::info('Employee模块已加载');
        }
    }
    
    /**
     * 注册Employee模块的路由
     *
     * @return void
     */
    protected function registerRoutes()
    {
        Route::prefix('api')
            ->middleware('api')
            ->group(function () {
                Route::group([], base_path('app/Employee/routes/api.php'));
            });
    }
    
    /**
     * 注册中间件
     *
     * @return void
     */
    protected function registerMiddleware()
    {
        // 注册模块内的中间件
        $router = $this->app->make(Router::class);
        
        // 用模块自己的中间件替换主中间件
        $router->aliasMiddleware('employee.role', \App\Employee\Http\Middleware\CheckEmployeeRole::class);
    }
} 