<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('points_rules', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('规则名称');
            $table->string('rule_type', 50)->comment('规则类型');
            $table->unsignedInteger('points_amount')->default(0)->comment('积分数量');
            $table->json('conditions')->nullable()->comment('触发条件');
            $table->unsignedInteger('max_times_per_day')->nullable()->comment('每日最大次数');
            $table->unsignedInteger('max_times_total')->nullable()->comment('总最大次数');
            $table->boolean('status')->default(true)->comment('状态');
            $table->timestamp('valid_from')->nullable()->comment('有效开始时间');
            $table->timestamp('valid_to')->nullable()->comment('有效结束时间');
            $table->text('description')->nullable()->comment('规则描述');
            $table->timestamps();

            $table->index(['rule_type', 'status']);
            $table->index(['status', 'valid_from', 'valid_to']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('points_rules');
    }
}; 