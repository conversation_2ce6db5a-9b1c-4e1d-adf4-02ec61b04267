<?php

namespace App\Purchase\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\Inventory;
use App\Models\InventoryBatch;
use App\Models\InventoryTransaction;
use App\Models\InventoryTransactionType;
use App\Purchase\Models\PurchaseOrder;
use App\Purchase\Models\PurchaseItem;
use App\Purchase\Services\PurchaseService;
use App\Models\Supplier;
use App\Models\Warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PurchaseOrderController extends Controller
{
    /**
     * @var PurchaseService
     */
    protected $purchaseService;
    
    /**
     * 构造函数
     * 
     * @param PurchaseService $purchaseService
     */
    public function __construct(PurchaseService $purchaseService)
    {
        $this->purchaseService = $purchaseService;
    }

    /**
     * 获取采购订单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // 记录请求信息
        Log::info('Purchase Orders request', [
            'params' => $request->all(),
            'user' => auth()->user() ? auth()->user()->id : 'unauthenticated',
            'ip' => $request->ip(),
            'is_debug' => $request->has('_debug')
        ]);
        
        try {
            $params = $request->all();
            $orders = $this->purchaseService->getPurchaseOrders($params);
            
            // 记录查询结果
            Log::info('Purchase Orders query success', [
                'count' => count($orders->items()),
                'total' => $orders->total(),
                'first_id' => !empty($orders->items()) ? $orders->items()[0]->id : null
            ]);
            
            return response()->json(ApiResponse::success($orders));
            
        } catch (\Exception $e) {
            Log::error('Purchase Orders query error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 出错时返回所有订单（降级处理）
            $params = [
                'per_page' => $request->per_page ?? 15,
                'order_by' => 'created_at',
                'direction' => 'desc'
            ];
            $orders = $this->purchaseService->getPurchaseOrders($params);
            
            return response()->json(ApiResponse::success($orders));
        }
    }

    /**
     * 获取采购统计数据
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats()
    {
        try {
            $stats = $this->purchaseService->getPurchaseStats();
            return response()->json(ApiResponse::success($stats));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('获取统计数据失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取采购订单详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $order = $this->purchaseService->getPurchaseOrder($id);
            return response()->json(ApiResponse::success($order));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('采购订单不存在: ' . $e->getMessage(), 404), 404);
        }
    }

    /**
     * 创建采购订单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        Log::info('🚀 Purchase Order Store Request', [
            'request_data' => $request->all(),
            'user_id' => auth()->id()
        ]);
        
        $validator = Validator::make($request->all(), [
            'supplier_id' => 'nullable|exists:suppliers,id',
            'warehouse_id' => 'nullable|exists:warehouses,id',
            'order_date' => 'required|date',
            'expected_delivery_date' => 'nullable|date|after_or_equal:order_date',
            'notes' => 'nullable|string',
            'payment_method' => 'nullable|string',
            'priority' => 'nullable|in:low,medium,high,urgent',
            'shipping_fee' => 'nullable|numeric|min:0',
            'tax_fee' => 'nullable|numeric|min:0',
            'other_fee' => 'nullable|numeric|min:0',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_id' => 'required|exists:units,id',
            'items.*.unit_price' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            Log::error('❌ Purchase Order Validation Failed', [
                'errors' => $validator->errors()->toArray(),
                'request_data' => $request->all()
            ]);
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            $order = $this->purchaseService->createPurchaseOrder($request->all());
            return response()->json(ApiResponse::success($order, '采购订单创建成功'), 201);
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('采购订单创建失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 更新采购订单（仅草稿状态可更新）
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'supplier_id' => 'nullable|exists:suppliers,id',
            'warehouse_id' => 'nullable|exists:warehouses,id',
            'order_date' => 'required|date',
            'expected_delivery_date' => 'nullable|date|after_or_equal:order_date',
            'notes' => 'nullable|string',
            'payment_method' => 'nullable|string',
            'priority' => 'nullable|in:low,medium,high,urgent',
            'shipping_fee' => 'nullable|numeric|min:0',
            'tax_fee' => 'nullable|numeric|min:0',
            'other_fee' => 'nullable|numeric|min:0',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_id' => 'required|exists:units,id',
            'items.*.unit_price' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            Log::error('❌ Purchase Order Validation Failed', [
                'errors' => $validator->errors()->toArray(),
                'request_data' => $request->all()
            ]);
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            $order = $this->purchaseService->updatePurchaseOrder($id, $request->all());
            return response()->json(ApiResponse::success($order, '采购订单更新成功'));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('采购订单更新失败: ' . $e->getMessage(), 400), 400);
        }
    }

    /**
     * 审批采购订单
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function approve(Request $request, $id)
    {
        try {
            $order = $this->purchaseService->approvePurchaseOrder($id);
            return response()->json(ApiResponse::success($order, '采购订单已审批'));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('采购订单审批失败: ' . $e->getMessage(), 400), 400);
        }
    }

    /**
     * 提交采购订单
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function submit(Request $request, $id)
    {
        try {
            $order = $this->purchaseService->submitPurchaseOrder($id);
            return response()->json(ApiResponse::success($order, '采购订单已提交'));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('采购订单提交失败: ' . $e->getMessage(), 400), 400);
        }
    }

    /**
     * 取消采购订单
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel(Request $request, $id)
    {
        try {
            $order = $this->purchaseService->cancelPurchaseOrder($id);
            return response()->json(ApiResponse::success($order, '采购订单已取消'));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('采购订单取消失败: ' . $e->getMessage(), 400), 400);
        }
    }

    /**
     * 处理采购订单收货
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function receiveItems(Request $request, $id)
    {
        Log::info('🚚 Purchase Order Receive Request', [
            'order_id' => $id,
            'request_data' => $request->all(),
            'user_id' => auth()->id()
        ]);
        
        $validator = Validator::make($request->all(), [
            'items' => 'required|array|min:1',
            'items.*.id' => 'required|exists:purchase_items,id',
            'items.*.receive_quantity' => 'required|numeric|min:0.01',
            'items.*.batch_code' => 'nullable|string',
            'items.*.production_date' => 'nullable|date',
            'items.*.expiry_date' => 'nullable|date|after:production_date',
            'items.*.notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            Log::error('❌ Purchase Order Receive Validation Failed', [
                'errors' => $validator->errors()->toArray(),
                'request_data' => $request->all(),
                'order_id' => $id
            ]);
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            $order = $this->purchaseService->receiveItems($id, $request->all());
            Log::info('✅ Purchase Order Receive Success', [
                'order_id' => $id,
                'user_id' => auth()->id()
            ]);
            return response()->json(ApiResponse::success($order, '收货处理成功'));
        } catch (\Exception $e) {
            Log::error('❌ Purchase Order Receive Failed', [
                'order_id' => $id,
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(ApiResponse::error('收货处理失败: ' . $e->getMessage(), 400), 400);
        }
    }
} 