import { get, post, put, del } from '../../utils/request';
import { isDev } from '../../utils/env';

/**
 * 获取库存列表
 * @param params 查询参数
 */
export const getInventoryList = (params: any) => {
  // 直接调用后端API获取库存数据
  return get('/inventory/products/stock', params);
};

/**
 * 获取指定商品的库存详情
 * @param productId 商品ID
 */
export const getProductInventory = (productId: string | number) => {
  return get(`/inventory/products/${productId}/stock`);
};

/**
 * 获取指定仓库的库存列表
 * @param warehouseId 仓库ID
 * @param params 查询参数
 */
export const getWarehouseInventory = (warehouseId: string | number, params?: any) => {
  return get(`/warehouses/${warehouseId}/inventory`, params);
};

/**
 * 获取指定仓库中指定商品的库存
 * @param warehouseId 仓库ID
 * @param productId 商品ID
 */
export const getWarehouseProductInventory = (warehouseId: string | number, productId: string | number) => {
  return get(`/warehouses/${warehouseId}/inventory/${productId}`);
};

/**
 * 调整库存
 * @param data 库存调整数据
 */
export const adjustInventory = (data: any) => {
  return post('/inventory/transactions/adjust', data);
};

/**
 * 获取库存交易记录
 * @param params 查询参数
 */
export const getInventoryTransactions = (params: any) => {
  return get('/inventory/transactions', params);
}; 