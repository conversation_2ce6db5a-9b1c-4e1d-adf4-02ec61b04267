<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 更新 unit_conversion_edges 表
        if (Schema::hasTable('unit_conversion_edges')) {
            Schema::table('unit_conversion_edges', function (Blueprint $table) {
                if (!Schema::hasColumn('unit_conversion_edges', 'description')) {
                    $table->text('description')->nullable()->comment('描述')->after('is_bidirectional');
                }
                if (!Schema::hasColumn('unit_conversion_edges', 'is_active')) {
                    $table->boolean('is_active')->default(true)->comment('是否启用')->after('description');
                }
                if (!Schema::hasColumn('unit_conversion_edges', 'meta_data')) {
                    $table->json('meta_data')->nullable()->comment('元数据')->after('is_active');
                }
            });
        }
        
        // 更新 unit_conversion_graphs 表
        if (Schema::hasTable('unit_conversion_graphs')) {
            Schema::table('unit_conversion_graphs', function (Blueprint $table) {
                if (!Schema::hasColumn('unit_conversion_graphs', 'is_active')) {
                    $table->boolean('is_active')->default(true)->comment('是否启用')->after('is_default');
                }
                if (!Schema::hasColumn('unit_conversion_graphs', 'meta_data')) {
                    $table->json('meta_data')->nullable()->comment('元数据')->after('is_active');
                }
            });
        }
        
        // 更新 unit_properties 表
        if (Schema::hasTable('unit_properties')) {
            Schema::table('unit_properties', function (Blueprint $table) {
                if (Schema::hasColumn('unit_properties', 'value')) {
                    $table->text('value')->nullable()->comment('属性值')->change();
                }
                if (!Schema::hasColumn('unit_properties', 'value_type')) {
                    $table->string('value_type')->default('string')->comment('值类型')->after('value');
                }
            });
        }
        
        // 更新 units 表
        if (Schema::hasTable('units')) {
            Schema::table('units', function (Blueprint $table) {
                if (Schema::hasColumn('units', 'conversion_factor')) {
                    // 移除 units 表中的 conversion_factor 字段，因为这应该在关系表中
                    $table->dropColumn('conversion_factor');
                }
                if (!Schema::hasColumn('units', 'properties')) {
                    $table->json('properties')->nullable()->comment('属性数据')->after('sort');
                }
            });
        }
        
        // 更新 product_units 表
        if (Schema::hasTable('product_units')) {
            Schema::table('product_units', function (Blueprint $table) {
                if (Schema::hasColumn('product_units', 'conversion_factor')) {
                    $table->decimal('conversion_factor', 20, 10)->comment('与基本单位的换算比率')->change();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 由于字段更新涉及字段类型变更，回滚不容易实现
        // 保持向前兼容，不提供回滚功能
    }
}; 