<?php

namespace App\Unit\Http\Controllers;

use App\Api\Models\ApiResponse;

use App\Unit\Services\UnitService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class UnitController extends Controller
{
    /**
     * 单位服务
     *
     * @var UnitService
     */
    protected $unitService;

    /**
     * 构造函数
     *
     * @param UnitService $unitService
     */
    public function __construct(UnitService $unitService)
    {
        $this->unitService = $unitService;
    }

    /**
     * 获取单位列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 防止直接源码输出，确保内容类型正确
            header('Content-Type: application/json; charset=utf-8');
            
            $responseData = $this->unitService->getUnits($request);
            
            return response()->json([
                "code" => 200,
                "message" => "Success",
                "data" => $responseData['data'],
                "meta" => $responseData['meta'] ?? null
            ]);
        } catch (\Exception $e) {
            Log::error("获取单位列表失败", [
                "error" => $e->getMessage(),
                "trace" => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ApiResponse::error("获取单位列表失败: " . $e->getMessage(), 500),
                500
            );
        }
    }

    /**
     * 创建单位
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                "name" => "required|string|max:20|unique:units",
                "display_name" => "required|string|max:50",
                "category" => "required|string|in:weight,volume,length,quantity,time,package",
                "is_base" => "boolean",
                "symbol" => "nullable|string|max:10",
                "description" => "nullable|string",
            ]);

            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }

            $unit = $this->unitService->createUnit($request->all());
            
            return response()->json(ApiResponse::success($unit, "单位创建成功"), 201);
        } catch (\Exception $e) {
            Log::error("创建单位失败", [
                "error" => $e->getMessage(),
                "trace" => $e->getTraceAsString(),
                "request" => $request->all()
            ]);
            
            return response()->json(
                ApiResponse::error("创建单位失败: " . $e->getMessage(), 500),
                500
            );
        }
    }
    
    /**
     * 显示单位详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $data = $this->unitService->getUnitDetail($id);
            
            return response()->json(ApiResponse::success($data));
        } catch (\Exception $e) {
            Log::error("获取单位详情失败", [
                "unit_id" => $id,
                "error" => $e->getMessage(),
                "trace" => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ApiResponse::error("获取单位详情失败: " . $e->getMessage(), 500),
                500
            );
        }
    }
    
    /**
     * 更新单位
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, int $id)
    {
        try {
            // 记录更新请求
            Log::info('更新单位请求', [
                'unit_id' => $id,
                'request_data' => $request->all()
            ]);
            
            // 验证输入
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:20|unique:units,name,'.$id,
                'display_name' => 'required|string|max:50',
                'type' => 'required|string|in:weight,volume,length,quantity,time,package',
                'is_base' => 'boolean',
                'symbol' => 'nullable|string|max:10',
                'description' => 'nullable|string',
            ]);
            
            if ($validator->fails()) {
                Log::error('单位更新验证失败', [
                    'unit_id' => $id,
                    'errors' => $validator->errors()->toArray()
                ]);
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            // 更新单位
            $unit = $this->unitService->updateUnit($id, $request->all());
            
            Log::info('单位更新成功', [
                'unit_id' => $id,
                'updated_data' => $unit->toArray()
            ]);
            
            return response()->json(ApiResponse::success($unit, '单位更新成功'));
        } catch (\Exception $e) {
            Log::error('单位更新失败', [
                'unit_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ApiResponse::error('单位更新失败: ' . $e->getMessage(), 500),
                500
            );
        }
    }
    
    /**
     * 获取单位关联的产品列表
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProducts($id)
    {
        try {
            $products = $this->unitService->getUnitProducts($id);
            
            return response()->json(ApiResponse::success($products));
        } catch (\Exception $e) {
            Log::error("获取单位关联产品失败", [
                "unit_id" => $id,
                "error" => $e->getMessage(),
                "trace" => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ApiResponse::error("获取单位关联产品失败: " . $e->getMessage(), 500),
                500
            );
        }
    }

    /**
     * 获取商品的单位关联
     *
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductUnits($productId)
    {
        try {
            $unitsData = $this->unitService->getProductUnits($productId);
            
            return response()->json(ApiResponse::success($unitsData));
        } catch (\Exception $e) {
            Log::error("获取商品单位失败", [
                "product_id" => $productId,
                "error" => $e->getMessage(),
                "trace" => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ApiResponse::error("获取商品单位失败: " . $e->getMessage(), 500),
                500
            );
        }
    }

    /**
     * 设置商品的所有单位关联
     *
     * @param int $productId
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function setProductAllUnits($productId, Request $request)
    {
        try {
            // 验证输入
            $validator = Validator::make($request->all(), [
                'base_unit_id' => 'required|exists:units,id',
                'auxiliary_units' => 'array',
                'auxiliary_units.*.unit_id' => 'required|exists:units,id',
                'auxiliary_units.*.conversion_factor' => 'required|numeric|gt:0',
                'auxiliary_units.*.is_sale_unit' => 'boolean',
                'auxiliary_units.*.is_purchase_unit' => 'boolean',
                'auxiliary_units.*.is_inventory_unit' => 'boolean',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $result = $this->unitService->setProductAllUnits($productId, $request->all());
            
            return response()->json(ApiResponse::success($result, '商品单位设置成功'));
        } catch (\Exception $e) {
            Log::error("设置商品单位失败", [
                "product_id" => $productId,
                "error" => $e->getMessage(),
                "trace" => $e->getTraceAsString(),
                "request" => $request->all()
            ]);
            
            return response()->json(
                ApiResponse::error("设置商品单位失败: " . $e->getMessage(), 500),
                500
            );
        }
    }

    /**
     * 删除单位
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            // 记录删除请求
            Log::info('删除单位请求', [
                'unit_id' => $id
            ]);
            
            // 调用服务删除单位
            $result = $this->unitService->deleteUnit($id);
            
            Log::info('单位删除成功', [
                'unit_id' => $id,
                'result' => $result
            ]);
            
            return response()->json(ApiResponse::success(null, '单位删除成功'));
        } catch (\Exception $e) {
            Log::error('单位删除失败', [
                'unit_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 使用统一的错误响应格式
            $statusCode = strpos($e->getMessage(), '单位已被使用') !== false ? 422 : 500;
            
            return response()->json(
                ApiResponse::error('删除单位失败: ' . $e->getMessage(), $statusCode),
                $statusCode
            );
        }
    }
    
    /**
     * 批量创建单位
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchStore(Request $request)
    {
        // TODO: 实现批量创建单位功能
        return response()->json(ApiResponse::error('功能尚未实现', 501), 501);
    }
} 