// pages/points-record/index.js - 积分记录页
const PointsAPI = require('../../utils/pointsApi');
const app = getApp();

Page({
  data: {
    // 当前选中的标签
    activeTab: 'all',
    
    // 标签列表
    tabs: [
      { key: 'all', name: '全部' },
      { key: 'earn', name: '获得' },
      { key: 'spend', name: '消费' },
      { key: 'exchange', name: '兑换' }
    ],
    
    // 积分记录列表
    pointsRecords: [],
    
    // 用户积分信息
    userPointsInfo: {
      availablePoints: 0,
      totalEarned: 0,
      totalUsed: 0,
      expiringSoon: 0
    },
    
    // 页面状态
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    // 检查是否有指定的标签
    const tab = options.tab || 'all';
    if (tab !== 'all') {
      this.setActiveTab(tab);
    }
    
    this.initPage();
  },

  /**
   * 页面显示
   */
  onShow() {
    // 刷新积分信息
    this.loadUserPointsInfo();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.setData({ 
      page: 1, 
      hasMore: true,
      pointsRecords: []
    });
    this.loadUserPointsInfo();
    this.loadPointsRecords();
    wx.stopPullDownRefresh();
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreRecords();
    }
  },

  /**
   * 初始化页面
   */
  async initPage() {
    wx.showLoading({ title: '加载中...' });

    try {
      await Promise.all([
        this.loadUserPointsInfo(),
        this.loadPointsRecords()
      ]);
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 加载用户积分信息
   */
  async loadUserPointsInfo() {
    try {
      const result = await PointsAPI.getUserPointsStats();
      const userPointsInfo = {
        availablePoints: result.data.balance || 0,
        totalEarned: result.data.total_earned || 0,
        totalUsed: result.data.total_used || 0,
        expiringSoon: result.data.expiring_soon || 0
      };
      
      this.setData({ userPointsInfo });
      
    } catch (error) {
      console.error('加载积分信息失败:', error);
    }
  },

  /**
   * 加载积分记录
   */
  async loadPointsRecords() {
    try {
      this.setData({ loading: true });
      
      const params = {
        page: this.data.page,
        per_page: this.data.pageSize
      };

      // 添加类型筛选
      if (this.data.activeTab !== 'all') {
        params.type = this.data.activeTab;
      }
      
      const result = await PointsAPI.getUserPointsTransactions(params);
      const records = result.data.data || [];
      
      this.setData({
        pointsRecords: this.data.page === 1 ? records : [...this.data.pointsRecords, ...records],
        hasMore: records.length === this.data.pageSize
      });
      
    } catch (error) {
      console.error('加载积分记录失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载更多记录
   */
  async loadMoreRecords() {
    this.setData({ 
      page: this.data.page + 1 
    });
    await this.loadPointsRecords();
  },

  /**
   * 标签点击事件
   */
  onTabTap(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setActiveTab(tab);
  },

  /**
   * 设置活动标签
   */
  setActiveTab(tab) {
    if (tab === this.data.activeTab) return;
    
    this.setData({
      activeTab: tab,
      page: 1,
      hasMore: true,
      pointsRecords: []
    });
    
    this.loadPointsRecords();
  },

  /**
   * 查看积分规则
   */
  onViewRules() {
    wx.navigateTo({
      url: '/pages/points/rules/index'
    });
  },

  /**
   * 积分记录点击事件
   */
  onRecordTap(e) {
    const record = e.currentTarget.dataset.record;
    
    // 如果有关联订单或商品，可以跳转到详情页
    if (record.related_type && record.related_id) {
      this.goToRelatedDetail(record.related_type, record.related_id);
    }
  },

  /**
   * 跳转到关联详情页
   */
  goToRelatedDetail(type, id) {
    let url = '';
    
    switch (type) {
      case 'order':
        url = `/pages/order-detail/index?id=${id}`;
        break;
      case 'points_order':
        url = `/pages/points/orders/detail/index?id=${id}`;
        break;
      case 'product':
        url = `/pages/product-detail/product-detail?id=${id}`;
        break;
      case 'points_product':
        url = `/pages/points/product/index?id=${id}`;
        break;
      default:
        return;
    }
    
    wx.navigateTo({ url });
  },

  /**
   * 工具方法
   */
  formatPoints(points) {
    return PointsAPI.formatPoints(points);
  },

  formatDateTime(dateString) {
    return PointsAPI.formatDateTime(dateString);
  },

  getTransactionTypeText(type) {
    const typeMap = {
      'earn': '获得',
      'spend': '消费', 
      'exchange': '兑换',
      'refund': '退还',
      'expire': '过期'
    };
    return typeMap[type] || type;
  },

  getTransactionSourceText(source) {
    const sourceMap = {
      'order': '订单消费',
      'signin': '签到奖励',
      'review': '评价奖励',
      'invite': '邀请奖励',
      'exchange': '积分兑换',
      'refund': '订单退款',
      'admin': '系统调整'
    };
    return sourceMap[source] || source;
  }
}); 