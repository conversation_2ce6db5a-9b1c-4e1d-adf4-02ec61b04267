<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->json('tag_ids')->nullable()->comment('标签ID数组，用于快速筛选');
            $table->json('tags_cache')->nullable()->comment('标签信息缓存，减少关联查询');
            
            // 添加索引用于JSON查询优化
            $table->index(['status', 'category_id'], 'idx_products_status_category_tags');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('idx_products_status_category_tags');
            $table->dropColumn(['tag_ids', 'tags_cache']);
        });
    }
};
