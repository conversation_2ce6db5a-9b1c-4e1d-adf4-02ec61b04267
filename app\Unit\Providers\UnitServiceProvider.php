<?php

namespace App\Unit\Providers;

use Illuminate\Support\ServiceProvider;
use App\Unit\Services\UnitService;
use App\Unit\Services\ProductUnitService;

class UnitServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     */
    public function register()
    {
        // 注册单位服务
        $this->app->singleton('unit.service', function ($app) {
            return new UnitService();
        });
        
        // 注册产品单位服务
        $this->app->singleton('product.unit.service', function ($app) {
            return new ProductUnitService(
                $app->make('unit.service')
            );
        });
    }

    /**
     * 引导服务
     */
    public function boot()
    {
        // 加载路由
        if (file_exists(__DIR__ . '/../routes/api.php')) {
            $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
        }
        
        if (file_exists(__DIR__ . '/../routes/web.php')) {
            $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');
        }
        
        // 加载迁移
        $this->loadMigrationsFrom(database_path('migrations'));
    }
} 