<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            // 添加付款方式字段
            $table->string('payment_method')->nullable()->after('notes')->comment('付款方式');
            
            // 添加优先级字段
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium')->after('payment_method')->comment('优先级');
            
            // 添加费用字段
            $table->decimal('shipping_fee', 10, 2)->default(0)->after('priority')->comment('运费');
            $table->decimal('tax_fee', 10, 2)->default(0)->after('shipping_fee')->comment('税费');
            $table->decimal('other_fee', 10, 2)->default(0)->after('tax_fee')->comment('其他费用');
            
            // 添加商品小计字段（不包含额外费用）
            $table->decimal('subtotal_amount', 12, 2)->default(0)->after('other_fee')->comment('商品小计');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            $table->dropColumn([
                'payment_method',
                'priority', 
                'shipping_fee',
                'tax_fee',
                'other_fee',
                'subtotal_amount'
            ]);
        });
    }
}; 