<template>
	<view class="order-value-container">
		<!-- 页面头部 -->
		<view class="header-section">
			<view class="header-title">
				<text class="title-text">客单价表现分析</text>
				<text class="subtitle-text">深度分析客单价表现与优化策略</text>
			</view>
		</view>

		<!-- 时间筛选 -->
		<view class="filter-section">
			<view class="filter-tabs">
				<view 
					class="filter-tab" 
					v-for="(period, index) in timePeriods" 
					:key="index"
					:class="{ active: selectedPeriod === period.value }"
					@tap="selectPeriod(period.value)"
				>
					<text class="tab-text">{{ period.label }}</text>
				</view>
			</view>
		</view>

		<!-- 客单价概览 -->
		<view class="overview-section">
			<view class="section-title">
				<text class="title-text">💰 客单价概览</text>
			</view>
			<view class="stats-grid">
				<view class="stat-item">
					<text class="stat-number">¥{{ orderValueOverview.avgOrderValue || 0 }}</text>
					<text class="stat-label">平均客单价</text>
					<text class="stat-trend" :class="getTrendClass(orderValueOverview.avgTrend)">
						{{ getTrendText(orderValueOverview.avgTrend) }}
					</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">¥{{ orderValueOverview.medianOrderValue || 0 }}</text>
					<text class="stat-label">中位数客单价</text>
					<text class="stat-trend" :class="getTrendClass(orderValueOverview.medianTrend)">
						{{ getTrendText(orderValueOverview.medianTrend) }}
					</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">¥{{ orderValueOverview.maxOrderValue || 0 }}</text>
					<text class="stat-label">最高客单价</text>
					<text class="stat-trend positive">
						{{ orderValueOverview.highValueOrders || 0 }}笔
					</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ orderValueOverview.orderCount || 0 }}</text>
					<text class="stat-label">总订单数</text>
					<text class="stat-trend" :class="getTrendClass(orderValueOverview.orderTrend)">
						{{ getTrendText(orderValueOverview.orderTrend) }}
					</text>
				</view>
			</view>
		</view>

		<!-- 客单价趋势 -->
		<view class="trend-section">
			<view class="section-title">
				<text class="title-text">📈 客单价趋势</text>
				<view class="trend-tabs">
					<text class="trend-tab" :class="{ active: selectedTrendType === 'daily' }" @tap="selectTrendType('daily')">日趋势</text>
					<text class="trend-tab" :class="{ active: selectedTrendType === 'weekly' }" @tap="selectTrendType('weekly')">周趋势</text>
				</view>
			</view>
			<view class="trend-chart">
				<view class="chart-container">
					<view class="trend-line" v-for="(point, index) in orderValueTrend" :key="index">
						<view class="trend-point" :style="{ height: point.percentage + '%' }">
							<text class="point-value">¥{{ point.avgValue }}</text>
						</view>
						<text class="trend-date">{{ formatTrendDate(point.date) }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 客单价分布 -->
		<view class="distribution-section">
			<view class="section-title">
				<text class="title-text">📊 客单价分布</text>
			</view>
			<view class="distribution-chart">
				<view class="distribution-item" v-for="range in priceRanges" :key="range.range">
					<view class="range-header">
						<text class="range-label">{{ range.range }}</text>
						<view class="range-stats">
							<text class="range-count">{{ range.count }}笔</text>
							<text class="range-percentage">{{ range.percentage }}%</text>
						</view>
					</view>
					<view class="range-bar">
						<view class="bar-fill" :style="{ width: range.percentage + '%' }" :class="getRangeClass(range.range)"></view>
					</view>
					<view class="range-details">
						<text class="detail-text">总金额: ¥{{ range.totalAmount }}</text>
						<text class="detail-text">平均值: ¥{{ range.avgValue }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 客户价值分层 -->
		<view class="customer-segments-section">
			<view class="section-title">
				<text class="title-text">🎯 客户价值分层</text>
			</view>
			<view class="segments-grid">
				<view class="segment-card" v-for="segment in customerSegments" :key="segment.level" :class="segment.level">
					<view class="segment-header">
						<text class="segment-icon">{{ segment.icon }}</text>
						<text class="segment-title">{{ segment.title }}</text>
					</view>
					<view class="segment-stats">
						<text class="segment-count">{{ segment.customerCount }}人</text>
						<text class="segment-avg">¥{{ segment.avgOrderValue }}</text>
					</view>
					<view class="segment-desc">
						<text class="desc-text">{{ segment.description }}</text>
					</view>
					<view class="segment-actions">
						<button class="segment-btn" @tap="viewSegmentCustomers(segment.level)">
							<text class="btn-text">查看客户</text>
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 影响因素分析 -->
		<view class="factors-section">
			<view class="section-title">
				<text class="title-text">🔍 影响因素分析</text>
			</view>
			<view class="factors-list">
				<view class="factor-item" v-for="factor in influenceFactors" :key="factor.id">
					<view class="factor-header">
						<text class="factor-icon">{{ factor.icon }}</text>
						<view class="factor-info">
							<text class="factor-title">{{ factor.title }}</text>
							<text class="factor-impact">{{ factor.impact }}</text>
						</view>
						<view class="factor-score" :class="getScoreClass(factor.score)">
							<text class="score-text">{{ factor.score }}</text>
						</view>
					</view>
					<text class="factor-desc">{{ factor.description }}</text>
					<view class="factor-suggestions">
						<text class="suggestion-text" v-for="suggestion in factor.suggestions" :key="suggestion">• {{ suggestion }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 提升策略 -->
		<view class="strategies-section">
			<view class="section-title">
				<text class="title-text">🚀 客单价提升策略</text>
			</view>
			<view class="strategies-grid">
				<view class="strategy-card" v-for="strategy in improvementStrategies" :key="strategy.id">
					<view class="strategy-header">
						<text class="strategy-icon">{{ strategy.icon }}</text>
						<text class="strategy-title">{{ strategy.title }}</text>
					</view>
					<text class="strategy-desc">{{ strategy.description }}</text>
					<view class="strategy-metrics">
						<view class="metric-item">
							<text class="metric-label">预期提升</text>
							<text class="metric-value">{{ strategy.expectedIncrease }}</text>
						</view>
						<view class="metric-item">
							<text class="metric-label">实施难度</text>
							<text class="metric-value" :class="getDifficultyClass(strategy.difficulty)">{{ strategy.difficulty }}</text>
						</view>
					</view>
					<view class="strategy-actions">
						<button class="strategy-btn" @tap="implementStrategy(strategy)">
							<text class="btn-text">{{ strategy.actionText }}</text>
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 高价值订单分析 -->
		<view class="high-value-section">
			<view class="section-title">
				<text class="title-text">💎 高价值订单分析</text>
				<text class="more-link" @tap="viewAllHighValueOrders">查看全部</text>
			</view>
			<view class="high-value-list">
				<view class="order-card" v-for="order in highValueOrders" :key="order.id" @tap="viewOrderDetail(order.id)">
					<view class="order-header">
						<view class="order-info">
							<text class="order-id">#{{ order.orderNumber }}</text>
							<text class="order-date">{{ formatDate(order.createdAt) }}</text>
						</view>
						<view class="order-amount">
							<text class="amount-text">¥{{ order.totalAmount }}</text>
						</view>
					</view>
					<view class="order-customer">
						<text class="customer-name">{{ order.customerName }}</text>
						<text class="customer-type">{{ order.customerType }}</text>
					</view>
					<view class="order-items">
						<text class="items-count">{{ order.itemsCount }}件商品</text>
						<text class="items-categories">{{ order.categories.join('、') }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
import analyticsApi from '../../api/analytics.js'
import { formatDateTime, formatDate as formatDateString } from '../../utils/date-formatter.js'

export default {
	data() {
		return {
			loading: false,
			selectedPeriod: 'last_30_days',
			selectedTrendType: 'daily',
			timePeriods: [
				{ label: '近7天', value: 'last_7_days' },
				{ label: '近30天', value: 'last_30_days' },
				{ label: '近90天', value: 'last_90_days' }
			],
			orderValueOverview: {},
			orderValueTrend: [],
			priceRanges: [],
			customerSegments: [],
			influenceFactors: [],
			improvementStrategies: [],
			highValueOrders: []
		}
	},
	
	onLoad() {
		this.loadOrderValueAnalysis()
	},
	
	onShow() {
		this.loadOrderValueAnalysis()
	},
	
	onPullDownRefresh() {
		this.refreshData()
	},
	
	methods: {
		// 选择时间周期
		selectPeriod(period) {
			this.selectedPeriod = period
			this.loadOrderValueAnalysis()
		},
		
		// 选择趋势类型
		selectTrendType(type) {
			this.selectedTrendType = type
			this.generateOrderValueTrend()
		},
		
		// 加载客单价分析数据
		async loadOrderValueAnalysis() {
			this.loading = true
			try {
				// 获取购买分析数据
				const purchaseResponse = await analyticsApi.getPurchaseAnalysis({
					period: this.selectedPeriod
				})
				
				if (purchaseResponse && purchaseResponse.code === 0) {
					const data = purchaseResponse.data
					
					// 处理客单价概览
					this.generateOrderValueOverview(data)
					
					// 生成客单价趋势
					this.generateOrderValueTrend()
					
					// 生成价格分布
					this.generatePriceRanges(data)
					
					// 生成客户价值分层
					this.generateCustomerSegments()
					
					// 生成影响因素分析
					this.generateInfluenceFactors()
					
					// 生成提升策略
					this.generateImprovementStrategies()
					
					// 生成高价值订单
					this.generateHighValueOrders()
				}
			} catch (error) {
				console.error('加载客单价分析数据失败:', error)
				uni.showToast({
					title: '加载数据失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 生成客单价概览
		generateOrderValueOverview(data) {
			// 模拟客单价数据
			const avgOrderValue = 680 // 生鲜配送平均客单价
			const medianOrderValue = 620
			const maxOrderValue = 1580
			const orderCount = 156
			
			this.orderValueOverview = {
				avgOrderValue: avgOrderValue,
				avgTrend: 'up',
				medianOrderValue: medianOrderValue,
				medianTrend: 'up',
				maxOrderValue: maxOrderValue,
				highValueOrders: 12,
				orderCount: orderCount,
				orderTrend: 'up'
			}
		},
		
		// 生成客单价趋势
		generateOrderValueTrend() {
			const days = this.selectedPeriod === 'last_7_days' ? 7 : 
						 this.selectedPeriod === 'last_30_days' ? 30 : 90
			
			this.orderValueTrend = []
			const baseValue = 680 // 基础客单价
			
			for (let i = days - 1; i >= 0; i--) {
				const date = new Date()
				date.setDate(date.getDate() - i)
				
				// 模拟趋势数据
				const variation = Math.random() * 0.2 - 0.1 // ±10%变化
				const avgValue = Math.round(baseValue * (1 + variation))
				
				this.orderValueTrend.push({
					date: date.toISOString().split('T')[0],
					avgValue: avgValue,
					percentage: Math.min(100, (avgValue / baseValue) * 50 + 25) // 25-75%的高度
				})
			}
		},
		
		// 生成价格分布
		generatePriceRanges(data) {
			this.priceRanges = [
				{
					range: '0-300元',
					count: 25,
					percentage: 16,
					totalAmount: 6250,
					avgValue: 250
				},
				{
					range: '300-500元',
					count: 38,
					percentage: 24,
					totalAmount: 15200,
					avgValue: 400
				},
				{
					range: '500-800元',
					count: 52,
					percentage: 33,
					totalAmount: 33800,
					avgValue: 650
				},
				{
					range: '800-1200元',
					count: 28,
					percentage: 18,
					totalAmount: 28000,
					avgValue: 1000
				},
				{
					range: '1200元以上',
					count: 13,
					percentage: 9,
					totalAmount: 18200,
					avgValue: 1400
				}
			]
		},
		
		// 生成客户价值分层
		generateCustomerSegments() {
			this.customerSegments = [
				{
					level: 'premium',
					icon: '💎',
					title: '高价值客户',
					customerCount: 15,
					avgOrderValue: 1200,
					description: '客单价超过1000元，是核心客户群体'
				},
				{
					level: 'high',
					icon: '🥇',
					title: '中高价值客户',
					customerCount: 32,
					avgOrderValue: 750,
					description: '客单价600-1000元，有提升潜力'
				},
				{
					level: 'medium',
					icon: '🥈',
					title: '中等价值客户',
					customerCount: 58,
					avgOrderValue: 450,
					description: '客单价300-600元，需要激活'
				},
				{
					level: 'low',
					icon: '🥉',
					title: '低价值客户',
					customerCount: 41,
					avgOrderValue: 220,
					description: '客单价低于300元，需要引导'
				}
			]
		},
		
		// 生成影响因素分析
		generateInfluenceFactors() {
			this.influenceFactors = [
				{
					id: 1,
					icon: '🛒',
					title: '商品组合',
					impact: '高影响',
					score: 85,
					description: '商品搭配和套餐设计直接影响客单价',
					suggestions: [
						'推出生鲜套餐组合',
						'设计满减优惠活动',
						'推荐互补商品'
					]
				},
				{
					id: 2,
					icon: '🎯',
					title: '客户定位',
					impact: '中高影响',
					score: 72,
					description: '不同客户群体的消费能力和需求差异',
					suggestions: [
						'细分客户群体',
						'个性化推荐',
						'差异化定价策略'
					]
				},
				{
					id: 3,
					icon: '💰',
					title: '价格策略',
					impact: '中影响',
					score: 68,
					description: '价格设定和优惠政策影响购买决策',
					suggestions: [
						'优化价格梯度',
						'设置满额免配送费',
						'会员专享价格'
					]
				},
				{
					id: 4,
					icon: '📱',
					title: '用户体验',
					impact: '中影响',
					score: 65,
					description: '购买流程和界面设计影响下单体验',
					suggestions: [
						'简化下单流程',
						'优化商品展示',
						'提升加载速度'
					]
				}
			]
		},
		
		// 生成提升策略
		generateImprovementStrategies() {
			this.improvementStrategies = [
				{
					id: 1,
					icon: '🎁',
					title: '满减促销',
					description: '设置满额减免活动，鼓励客户增加购买金额',
					expectedIncrease: '+15%',
					difficulty: '简单',
					actionText: '立即设置'
				},
				{
					id: 2,
					icon: '📦',
					title: '套餐组合',
					description: '推出生鲜套餐，提供更优惠的组合价格',
					expectedIncrease: '+20%',
					difficulty: '中等',
					actionText: '设计套餐'
				},
				{
					id: 3,
					icon: '🔄',
					title: '交叉销售',
					description: '在购买页面推荐相关商品，增加购买数量',
					expectedIncrease: '+12%',
					difficulty: '简单',
					actionText: '配置推荐'
				},
				{
					id: 4,
					icon: '👑',
					title: '会员特权',
					description: '为会员提供专属商品和价格优惠',
					expectedIncrease: '+25%',
					difficulty: '复杂',
					actionText: '制定方案'
				}
			]
		},
		
		// 生成高价值订单
		generateHighValueOrders() {
			this.highValueOrders = [
				{
					id: 1,
					orderNumber: 'ORD20241201001',
					createdAt: '2024-12-01T10:30:00Z',
					totalAmount: 1580,
					customerName: '张三食品店',
					customerType: '高价值客户',
					itemsCount: 15,
					categories: ['蔬菜类', '肉类', '海鲜类']
				},
				{
					id: 2,
					orderNumber: 'ORD20241201002',
					createdAt: '2024-12-01T14:20:00Z',
					totalAmount: 1320,
					customerName: '李四超市',
					customerType: '中高价值客户',
					itemsCount: 12,
					categories: ['水果类', '蔬菜类', '肉类']
				},
				{
					id: 3,
					orderNumber: 'ORD20241130003',
					createdAt: '2024-11-30T16:45:00Z',
					totalAmount: 1150,
					customerName: '王五便利店',
					customerType: '中高价值客户',
					itemsCount: 10,
					categories: ['蔬菜类', '水果类']
				}
			]
		},
		
		// 刷新数据
		async refreshData() {
			await this.loadOrderValueAnalysis()
			uni.stopPullDownRefresh()
		},
		
		// 格式化趋势日期
		formatTrendDate(dateStr) {
			const date = new Date(dateStr)
			return `${date.getMonth() + 1}/${date.getDate()}`
		},
		
		// 格式化日期
		formatDate(dateStr) {
			return formatDateString(dateStr)
		},
		
		// 获取趋势样式类
		getTrendClass(trend) {
			return trend === 'up' ? 'positive' : 'negative'
		},
		
		// 获取趋势文本
		getTrendText(trend) {
			return trend === 'up' ? '↗ 上升' : '↘ 下降'
		},
		
		// 获取价格区间样式类
		getRangeClass(range) {
			if (range.includes('1200元以上')) return 'premium'
			if (range.includes('800-1200')) return 'high'
			if (range.includes('500-800')) return 'medium'
			return 'low'
		},
		
		// 获取评分样式类
		getScoreClass(score) {
			if (score >= 80) return 'excellent'
			if (score >= 70) return 'good'
			if (score >= 60) return 'average'
			return 'poor'
		},
		
		// 获取难度样式类
		getDifficultyClass(difficulty) {
			if (difficulty === '简单') return 'easy'
			if (difficulty === '中等') return 'medium'
			return 'hard'
		},
		
		// 查看分层客户
		viewSegmentCustomers(level) {
			uni.navigateTo({
				url: `/pages/clients/client-list?segment=${level}`
			})
		},
		
		// 实施策略
		implementStrategy(strategy) {
			uni.showToast({
				title: `正在实施：${strategy.title}`,
				icon: 'none'
			})
		},
		
		// 查看所有高价值订单
		viewAllHighValueOrders() {
			uni.navigateTo({
				url: '/pages/orders/order-list?filter=high_value'
			})
		},
		
		// 查看订单详情
		viewOrderDetail(orderId) {
			uni.navigateTo({
				url: `/pages/orders/order-detail?id=${orderId}`
			})
		}
	}
}
</script>

<style scoped>
.order-value-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 页面头部 */
.header-section {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	padding: 40rpx 32rpx 32rpx;
	color: #ffffff;
}

.header-title {
	text-align: center;
}

.title-text {
	font-size: 40rpx;
	font-weight: 700;
	color: #ffffff;
	margin-bottom: 8rpx;
	display: block;
}

.subtitle-text {
	font-size: 28rpx;
	color: #ffffff;
	opacity: 0.8;
	display: block;
}

/* 筛选区域 */
.filter-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.filter-tabs {
	display: flex;
	gap: 16rpx;
}

.filter-tab {
	flex: 1;
	padding: 16rpx 12rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	text-align: center;
	transition: all 0.3s ease;
}

.filter-tab.active {
	background: #f093fb;
	color: #ffffff;
}

.tab-text {
	font-size: 28rpx;
	font-weight: 600;
}

/* 概览区域 */
.overview-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.section-title {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.more-link {
	font-size: 28rpx;
	color: #f093fb;
}

.stats-grid {
	display: flex;
	gap: 24rpx;
}

.stat-item {
	flex: 1;
	text-align: center;
}

.stat-number {
	font-size: 48rpx;
	font-weight: 700;
	color: #333333;
	display: block;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 4rpx;
	display: block;
}

.stat-trend {
	font-size: 20rpx;
}

.stat-trend.positive {
	color: #52c41a;
}

.stat-trend.negative {
	color: #f5222d;
}

/* 趋势区域 */
.trend-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.trend-tabs {
	display: flex;
	gap: 16rpx;
}

.trend-tab {
	padding: 8rpx 16rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 24rpx;
	color: #666666;
}

.trend-tab.active {
	background: #f093fb;
	color: #ffffff;
}

.trend-chart {
	margin-top: 24rpx;
}

.chart-container {
	display: flex;
	align-items: end;
	gap: 8rpx;
	height: 200rpx;
	padding: 20rpx 0;
}

.trend-line {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.trend-point {
	background: linear-gradient(to top, #f093fb, #f5576c);
	border-radius: 4rpx 4rpx 0 0;
	min-height: 20rpx;
	width: 100%;
	display: flex;
	align-items: end;
	justify-content: center;
	padding-bottom: 8rpx;
	margin-bottom: 8rpx;
}

.point-value {
	font-size: 18rpx;
	color: #ffffff;
	font-weight: 600;
}

.trend-date {
	font-size: 20rpx;
	color: #666666;
	transform: rotate(-45deg);
}

/* 分布区域 */
.distribution-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.distribution-chart {
	margin-top: 24rpx;
}

.distribution-item {
	margin-bottom: 24rpx;
}

.range-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8rpx;
}

.range-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.range-stats {
	text-align: right;
}

.range-count {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-right: 8rpx;
}

.range-percentage {
	font-size: 24rpx;
	color: #666666;
}

.range-bar {
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
	margin-bottom: 8rpx;
}

.bar-fill {
	height: 100%;
	border-radius: 6rpx;
	transition: width 0.3s ease;
}

.bar-fill.premium {
	background: #722ed1;
}

.bar-fill.high {
	background: #1890ff;
}

.bar-fill.medium {
	background: #52c41a;
}

.bar-fill.low {
	background: #faad14;
}

.range-details {
	display: flex;
	gap: 24rpx;
}

.detail-text {
	font-size: 24rpx;
	color: #666666;
}

/* 客户分层 */
.customer-segments-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.segments-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	margin-top: 24rpx;
}

.segment-card {
	width: calc(50% - 8rpx);
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	text-align: center;
}

.segment-card.premium {
	background: linear-gradient(135deg, #722ed1, #9254de);
	color: #ffffff;
}

.segment-card.high {
	background: linear-gradient(135deg, #1890ff, #40a9ff);
	color: #ffffff;
}

.segment-card.medium {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	color: #ffffff;
}

.segment-card.low {
	background: linear-gradient(135deg, #faad14, #ffc53d);
	color: #ffffff;
}

.segment-header {
	margin-bottom: 16rpx;
}

.segment-icon {
	font-size: 48rpx;
	margin-bottom: 8rpx;
	display: block;
}

.segment-title {
	font-size: 28rpx;
	font-weight: 600;
}

.segment-stats {
	margin-bottom: 12rpx;
}

.segment-count {
	font-size: 32rpx;
	font-weight: 700;
	margin-bottom: 4rpx;
	display: block;
}

.segment-avg {
	font-size: 24rpx;
	opacity: 0.9;
}

.segment-desc {
	margin-bottom: 16rpx;
}

.desc-text {
	font-size: 22rpx;
	line-height: 1.4;
	opacity: 0.8;
}

.segment-btn {
	background: rgba(255, 255, 255, 0.2);
	color: inherit;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 8rpx;
	padding: 8rpx 16rpx;
	font-size: 24rpx;
}

.btn-text {
	color: inherit;
}

/* 影响因素 */
.factors-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.factors-list {
	margin-top: 24rpx;
}

.factor-item {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
}

.factor-header {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
}

.factor-icon {
	font-size: 32rpx;
	margin-right: 12rpx;
}

.factor-info {
	flex: 1;
}

.factor-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
	display: block;
}

.factor-impact {
	font-size: 24rpx;
	color: #666666;
}

.factor-score {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.factor-score.excellent {
	background: #52c41a;
	color: #ffffff;
}

.factor-score.good {
	background: #1890ff;
	color: #ffffff;
}

.factor-score.average {
	background: #faad14;
	color: #ffffff;
}

.factor-score.poor {
	background: #f5222d;
	color: #ffffff;
}

.score-text {
	font-size: 24rpx;
	font-weight: 600;
}

.factor-desc {
	font-size: 24rpx;
	color: #666666;
	line-height: 1.4;
	margin-bottom: 12rpx;
}

.factor-suggestions {
	margin-top: 12rpx;
}

.suggestion-text {
	font-size: 22rpx;
	color: #666666;
	line-height: 1.4;
	margin-bottom: 4rpx;
	display: block;
}

/* 提升策略 */
.strategies-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.strategies-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	margin-top: 24rpx;
}

.strategy-card {
	width: calc(50% - 8rpx);
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
}

.strategy-header {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
}

.strategy-icon {
	font-size: 32rpx;
	margin-right: 12rpx;
}

.strategy-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.strategy-desc {
	font-size: 24rpx;
	color: #666666;
	line-height: 1.4;
	margin-bottom: 16rpx;
}

.strategy-metrics {
	display: flex;
	gap: 16rpx;
	margin-bottom: 16rpx;
}

.metric-item {
	flex: 1;
	text-align: center;
}

.metric-label {
	font-size: 20rpx;
	color: #666666;
	margin-bottom: 4rpx;
	display: block;
}

.metric-value {
	font-size: 24rpx;
	font-weight: 600;
	color: #333333;
}

.metric-value.easy {
	color: #52c41a;
}

.metric-value.medium {
	color: #faad14;
}

.metric-value.hard {
	color: #f5222d;
}

.strategy-btn {
	background: #f093fb;
	color: #ffffff;
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 24rpx;
	font-size: 24rpx;
	width: 100%;
}

/* 高价值订单 */
.high-value-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.high-value-list {
	margin-top: 24rpx;
}

.order-card {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
}

.order-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 12rpx;
}

.order-info {
	flex: 1;
}

.order-id {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
	display: block;
}

.order-date {
	font-size: 24rpx;
	color: #666666;
}

.order-amount {
	text-align: right;
}

.amount-text {
	font-size: 32rpx;
	font-weight: 700;
	color: #f093fb;
}

.order-customer {
	margin-bottom: 8rpx;
}

.customer-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-right: 12rpx;
}

.customer-type {
	font-size: 24rpx;
	color: #666666;
}

.order-items {
	display: flex;
	gap: 16rpx;
}

.items-count {
	font-size: 24rpx;
	color: #666666;
}

.items-categories {
	font-size: 24rpx;
	color: #666666;
}

/* 加载状态 */
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}
</style> 