<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 添加分类ID字段
            $table->unsignedBigInteger('category_id')->nullable()->after('id')->comment('分类ID');
            
            // 添加外键约束
            $table->foreign('category_id')
                  ->references('id')
                  ->on('categories')
                  ->onDelete('set null');
                  
            // 添加商品子标题字段
            $table->string('subtitle')->nullable()->after('name')->comment('商品副标题');
            
            // 添加图片字段
            $table->string('cover_url')->nullable()->after('description')->comment('封面图片URL');
            
            // 添加状态字段
            $table->tinyInteger('status')->default(1)->after('unit_conversion_rate')->comment('状态：1上架，0下架');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 删除外键约束
            $table->dropForeign(['category_id']);
            
            // 删除字段
            $table->dropColumn(['category_id', 'subtitle', 'cover_url', 'status']);
        });
    }
}; 