<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flycloud_printers', function (Blueprint $table) {
            $table->id();
            $table->string('sn')->unique()->comment('飞蛾云打印机编号');
            $table->string('name')->comment('打印机名称');
            $table->string('key')->comment('打印机密钥');
            $table->string('location')->nullable()->comment('打印机位置');
            $table->string('description')->nullable()->comment('描述');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->boolean('is_default')->default(false)->comment('是否默认打印机');
            $table->string('status')->default('unknown')->comment('打印机状态');
            $table->timestamp('last_status_check')->nullable()->comment('最后状态检查时间');
            $table->json('settings')->nullable()->comment('打印机设置');
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['is_active', 'is_default']);
            $table->index('status');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flycloud_printers');
    }
}; 