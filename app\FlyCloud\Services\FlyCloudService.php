<?php

namespace App\FlyCloud\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\FlyCloud\Models\FlyCloudPrinter;
use App\FlyCloud\Models\WarehousePrinterBinding;

class FlyCloudService
{
    protected array $config = [];
    protected string $apiUrl;
    protected string $user;
    protected string $ukey;

    public function __construct()
    {
        $this->config = [
            'api_url' => env('FLYCLOUD_API_URL', 'http://api.feieyun.cn/Api/Open/'),
            'user' => env('FLYCLOUD_USER', ''), // 飞蛾云后台注册账号
            'ukey' => env('FLYCLOUD_UKEY', ''), // 飞蛾云后台生成的UKEY
            'timeout' => 30,
            'debug' => env('FLYCLOUD_DEBUG', false)
        ];

        $this->apiUrl = $this->config['api_url'];
        $this->user = $this->config['user'];
        $this->ukey = $this->config['ukey'];

        if ($this->config['debug']) {
            Log::info('FlyCloud Service initialized', [
                'api_url' => $this->apiUrl,
                'user' => $this->user
            ]);
        }
    }

    /**
     * 打印文本内容
     */
    public function printText(string $content, array $options = []): bool
    {
        try {
            $printer = $this->resolvePrinter($options);
            if (!$printer) {
                throw new \Exception('No available printer found');
            }

            $copies = $options['copies'] ?? 1;
            
            return $this->sendPrintRequest($printer->sn, $content, $copies);
        } catch (\Exception $e) {
            Log::error('FlyCloud print text failed', [
                'error' => $e->getMessage(),
                'content' => $content,
                'options' => $options
            ]);
            return false;
        }
    }

    /**
     * 打印HTML内容
     */
    public function printHtml(string $html, array $options = []): bool
    {
        try {
            $printer = $this->resolvePrinter($options);
            if (!$printer) {
                throw new \Exception('No available printer found');
            }

            // 将HTML转换为飞蛾云小票格式
            $content = $this->convertHtmlToReceiptFormat($html);
            $copies = $options['copies'] ?? 1;
            
            return $this->sendPrintRequest($printer->sn, $content, $copies);
        } catch (\Exception $e) {
            Log::error('FlyCloud print HTML failed', [
                'error' => $e->getMessage(),
                'options' => $options
            ]);
            return false;
        }
    }

    /**
     * 打印订单小票
     */
    public function printOrderReceipt($order, array $options = []): bool
    {
        try {
            $printer = $this->resolvePrinter($options);
            if (!$printer) {
                throw new \Exception('No available printer found');
            }

            $content = $this->generateOrderReceiptContent($order);
            $copies = $options['copies'] ?? 1;
            
            return $this->sendPrintRequest($printer->sn, $content, $copies);
        } catch (\Exception $e) {
            Log::error('FlyCloud print order receipt failed', [
                'error' => $e->getMessage(),
                'order_id' => $order->id ?? null
            ]);
            return false;
        }
    }

    /**
     * 获取打印机列表（从数据库）
     */
    public function getPrinters(): array
    {
        try {
            $printers = FlyCloudPrinter::active()->get();
            $result = [];

            foreach ($printers as $printer) {
                // 获取实时状态
                $status = $this->getPrinterStatus($printer->sn);
                
                $result[] = [
                    'id' => $printer->id,
                    'sn' => $printer->sn,
                    'name' => $printer->name,
                    'location' => $printer->location,
                    'status' => $status['status'] ?? 'unknown',
                    'is_default' => $printer->is_default,
                    'is_online' => $status['online'] ?? false,
                    'last_check' => $printer->last_status_check
                ];
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('FlyCloud get printers failed', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * 获取打印机状态
     */
    public function getPrinterStatus(string $printerSn): array
    {
        try {
            $response = $this->makeApiRequest('Open_queryPrinterStatus', [
                'sn' => $printerSn
            ]);

            if ($response && isset($response['ret']) && $response['ret'] == 0) {
                $status = $this->parsePrinterStatus($response['data'] ?? '');
                
                // 更新数据库中的状态
                $printer = FlyCloudPrinter::where('sn', $printerSn)->first();
                if ($printer) {
                    $printer->updateStatus($status, $response['data'] ?? '');
                }
                
                return [
                    'status' => $status,
                    'message' => $response['data'] ?? 'Unknown status',
                    'online' => $status === 'online'
                ];
            }

            return [
                'status' => 'error',
                'message' => $response['msg'] ?? 'Failed to get printer status',
                'online' => false
            ];
        } catch (\Exception $e) {
            Log::error('FlyCloud get printer status failed', [
                'error' => $e->getMessage(),
                'printer' => $printerSn
            ]);
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
                'online' => false
            ];
        }
    }

    /**
     * 添加打印机到飞蛾云账户和数据库
     */
    public function addPrinter(string $sn, string $key, string $name = '', array $options = []): bool
    {
        try {
            // 验证打印机编号和密钥格式
            if (!FlyCloudPrinter::validateSn($sn)) {
                throw new \Exception('Invalid printer SN format');
            }
            
            if (!FlyCloudPrinter::validateKey($key)) {
                throw new \Exception('Invalid printer key format');
            }

            // 添加到飞蛾云账户
            $printerContent = $sn . '#' . $key . '#' . $name . '#' . ($options['cardno'] ?? '');
            
            $response = $this->makeApiRequest('Open_printerAddlist', [
                'printerContent' => $printerContent
            ]);

            if ($response && isset($response['ret']) && $response['ret'] == 0) {
                // 添加到数据库
                $printer = FlyCloudPrinter::create([
                    'sn' => $sn,
                    'name' => $name ?: '飞蛾云打印机',
                    'key' => $key,
                    'location' => $options['location'] ?? '',
                    'description' => $options['description'] ?? '',
                    'is_active' => true,
                    'is_default' => $options['is_default'] ?? false,
                    'created_by' => $options['created_by'] ?? null
                ]);

                // 如果设置为默认打印机
                if ($options['is_default'] ?? false) {
                    $printer->setAsDefault();
                }

                Log::info('FlyCloud printer added successfully', [
                    'sn' => $sn,
                    'name' => $name
                ]);
                
        return true;
    }

            Log::error('FlyCloud add printer failed', [
                'sn' => $sn,
                'response' => $response
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('FlyCloud add printer exception', [
                'error' => $e->getMessage(),
                'sn' => $sn
            ]);
            return false;
        }
    }

    /**
     * 删除打印机
     */
    public function deletePrinter(string $sn): bool
    {
        try {
            // 从飞蛾云账户删除
            $response = $this->makeApiRequest('Open_printerDelList', [
                'snlist' => $sn
            ]);

            $cloudDeleted = $response && isset($response['ret']) && $response['ret'] == 0;
            
            // 从数据库删除（软删除）
            $printer = FlyCloudPrinter::where('sn', $sn)->first();
            if ($printer) {
                $printer->delete();
            }

            Log::info('FlyCloud printer deleted', [
                'sn' => $sn,
                'cloud_deleted' => $cloudDeleted,
                'db_deleted' => $printer ? true : false
            ]);

            return true; // 即使云端删除失败，数据库删除成功也返回true
        } catch (\Exception $e) {
            Log::error('FlyCloud delete printer exception', [
                'error' => $e->getMessage(),
                'sn' => $sn
            ]);
            return false;
        }
    }

    /**
     * 清空打印队列
     */
    public function clearPrintQueue(string $sn): bool
    {
        try {
            $response = $this->makeApiRequest('Open_delPrinterSqs', [
                'sn' => $sn
            ]);

            if ($response && isset($response['ret']) && $response['ret'] == 0) {
                Log::info('FlyCloud print queue cleared successfully', ['sn' => $sn]);
                return true;
            }

            Log::error('FlyCloud clear print queue failed', [
                'sn' => $sn,
                'response' => $response
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('FlyCloud clear print queue exception', [
                'error' => $e->getMessage(),
                'sn' => $sn
            ]);
        return false;
    }
}

    /**
     * 批量检查打印机状态
     */
    public function checkAllPrintersStatus(): array
    {
        $printers = FlyCloudPrinter::active()->get();
        $results = [];

        foreach ($printers as $printer) {
            $status = $this->getPrinterStatus($printer->sn);
            $results[$printer->sn] = $status;
        }

        return $results;
    }

    /**
     * 获取在线打印机列表
     */
    public function getOnlinePrinters(): array
    {
        $this->checkAllPrintersStatus(); // 更新状态
        
        return FlyCloudPrinter::active()->online()->get()->map(function ($printer) {
            return [
                'id' => $printer->id,
                'sn' => $printer->sn,
                'name' => $printer->name,
                'location' => $printer->location
            ];
        })->toArray();
    }

    /**
     * 解析打印机参数，优先级：指定 > 默认 > 第一台在线
     */
    protected function resolvePrinter(array $options = []): ?FlyCloudPrinter
    {
        // 1. 如果指定了打印机SN
        if (!empty($options['printer_sn'])) {
            $printer = FlyCloudPrinter::active()->where('sn', $options['printer_sn'])->first();
            if ($printer) {
                return $printer;
            }
        }

        // 2. 如果指定了打印机ID
        if (!empty($options['printer_id'])) {
            $printer = FlyCloudPrinter::active()->find($options['printer_id']);
            if ($printer) {
                return $printer;
            }
        }

        // 3. 使用默认打印机
        $defaultPrinter = FlyCloudPrinter::getDefault();
        if ($defaultPrinter) {
            return $defaultPrinter;
        }

        // 4. 使用第一台活跃的打印机
        return FlyCloudPrinter::active()->first();
    }

    /**
     * 生成订单小票内容
     */
    protected function generateOrderReceiptContent($order): string
    {
        $content = "";
        
        // 店铺名称和标题
        $storeName = env('FLYCLOUD_STORE_NAME', '万家生鲜');
        $content .= "<C><B>" . $storeName . "</B></C><BR>";
        $content .= "<C>订单小票</C><BR>";
        $content .= "================================<BR>";
        
        // 订单信息
        $content .= "<L>订单号: " . ($order->order_no ?? '') . "</L><BR>";
        $content .= "<L>下单时间: " . ($order->created_at ? $order->created_at->format('Y-m-d H:i:s') : '') . "</L><BR>";
        $content .= "--------------------------------<BR>";
        
        // 商品列表
        if ($order->items && count($order->items) > 0) {
            foreach ($order->items as $item) {
                $productName = $item->product_name ?? ($item->product->name ?? '商品');
                $price = number_format($item->price ?? 0, 2);
                $quantity = $item->quantity ?? 1;
                $subtotal = number_format(($item->price ?? 0) * ($item->quantity ?? 1), 2);
                
                $content .= "<L>" . $productName . "</L><BR>";
                $content .= "<L>数量: " . $quantity . " x ¥" . $price . "</L><BR>";
                $content .= "<R>小计: ¥" . $subtotal . "</R><BR>";
                $content .= "<BR>";
            }
        }
        
        $content .= "--------------------------------<BR>";
        
        // 金额信息
        $total = number_format($order->total ?? 0, 2);
        $content .= "<L>商品总额: ¥" . $total . "</L><BR>";
        $content .= "<L>配送费: ¥0.00</L><BR>";
        $content .= "<R><B>实付金额: ¥" . $total . "</B></R><BR>";
        
        $content .= "--------------------------------<BR>";
        
        // 收货信息
        $content .= "<L>收货人: " . ($order->contact_name ?? '') . "</L><BR>";
        $content .= "<L>联系电话: " . ($order->contact_phone ?? '') . "</L><BR>";
        $content .= "<L>收货地址: " . ($order->shipping_address ?? '') . "</L><BR>";
        
        $content .= "================================<BR>";
        $content .= "<C>感谢您的惠顾！</C><BR>";
        $content .= "<C>打印时间: " . now()->format('Y-m-d H:i:s') . "</C><BR>";
        
        // 添加切纸指令
        $content .= "<CUT>";
        
        return $content;
    }

    /**
     * 发送打印请求到飞蛾云API
     */
    protected function sendPrintRequest(string $sn, string $content, int $copies = 1): bool
    {
        try {
            $response = $this->makeApiRequest('Open_printMsg', [
                'sn' => $sn,
                'content' => $content,
                'times' => $copies
            ]);

            if ($response && isset($response['ret']) && $response['ret'] == 0) {
                if ($this->config['debug']) {
                    Log::info('FlyCloud print request sent successfully', [
                        'sn' => $sn,
                        'response' => $response
                    ]);
                }
                return true;
            }

            Log::error('FlyCloud print request failed', [
                'sn' => $sn,
                'response' => $response
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('FlyCloud print request exception', [
                'error' => $e->getMessage(),
                'sn' => $sn
            ]);
            return false;
        }
    }

    /**
     * 调用飞蛾云API
     */
    protected function makeApiRequest(string $apiname, array $params = []): ?array
    {
        try {
            $stime = time();
            $sig = $this->generateSignature($apiname, $stime);

            $postData = array_merge([
                'user' => $this->user,
                'stime' => $stime,
                'sig' => $sig,
                'apiname' => $apiname
            ], $params);

            $response = Http::timeout($this->config['timeout'])
                ->asForm()
                ->post($this->apiUrl, $postData);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('FlyCloud API request failed', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('FlyCloud API request exception', [
                'error' => $e->getMessage(),
                'apiname' => $apiname
            ]);
            return null;
        }
    }

    /**
     * 生成飞蛾云API签名
     */
    protected function generateSignature(string $apiname, int $stime): string
    {
        // 飞蛾云签名算法: SHA1(user + UKEY + stime + apiname)
        $signStr = $this->user . $this->ukey . $stime . $apiname;
        return sha1($signStr);
    }

    /**
     * 解析打印机状态
     */
    protected function parsePrinterStatus(string $statusData): string
    {
        // 飞蛾云打印机状态解析
        if (strpos($statusData, '离线') !== false) {
            return 'offline';
        } elseif (strpos($statusData, '在线') !== false) {
            return 'online';
        } elseif (strpos($statusData, '缺纸') !== false) {
            return 'no_paper';
        } elseif (strpos($statusData, '正常') !== false) {
            return 'online';
        } else {
            return 'unknown';
        }
    }

    /**
     * 将HTML转换为飞蛾云小票打印格式
     */
    protected function convertHtmlToReceiptFormat(string $html): string
    {
        // 移除HTML标签，保留文本内容
        $text = strip_tags($html);
        
        // 处理特殊字符
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
        
        // 分行处理
        $lines = explode("\n", $text);
        $formattedContent = '';
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line)) {
                // 店铺名称居中
                if (strpos($line, '万家生鲜') !== false || strpos($line, '订单小票') !== false) {
                    $formattedContent .= "<C>" . $line . "</C><BR>";
                }
                // 分割线
                elseif (strpos($line, '---') !== false || strpos($line, '===') !== false) {
                    $formattedContent .= "--------------------------------<BR>";
                }
                // 订单信息左对齐
                elseif (strpos($line, '订单号:') !== false || strpos($line, '下单时间:') !== false || 
                        strpos($line, '收货人:') !== false || strpos($line, '联系电话:') !== false ||
                        strpos($line, '收货地址:') !== false) {
                    $formattedContent .= "<L>" . $line . "</L><BR>";
                }
                // 金额信息右对齐
                elseif (strpos($line, '¥') !== false || strpos($line, '实付金额') !== false || 
                        strpos($line, '商品总额') !== false || strpos($line, '配送费') !== false) {
                    $formattedContent .= "<R>" . $line . "</R><BR>";
                }
                // 普通文本左对齐
                else {
                    $formattedContent .= "<L>" . $line . "</L><BR>";
                }
            }
        }
        
        // 添加切纸指令
        $formattedContent .= "<CUT>";
        
        return $formattedContent;
    }

    /**
     * 按仓库分单打印订单
     */
    public function printOrderByWarehouses($order, array $options = []): array
    {
        try {
            // 1. 按仓库分组订单商品
            $warehouseGroups = $this->groupOrderItemsByWarehouse($order);
            
            if (empty($warehouseGroups)) {
                throw new \Exception('No warehouse found for order items');
            }

            // 2. 获取各仓库的默认打印机，只处理有绑定打印机的仓库
            $warehouseIds = array_keys($warehouseGroups);
            $printType = $options['print_type'] ?? WarehousePrinterBinding::PRINT_TYPE_ORDER;
            $warehousePrinters = WarehousePrinterBinding::getMultiWarehouseDefaultPrinters($warehouseIds, $printType);

            // 3. 逐个仓库打印（只处理有绑定打印机的仓库）
            $results = [];
            $processedWarehouses = [];
            $skippedWarehouses = [];
            
            foreach ($warehouseGroups as $warehouseId => $items) {
                $printer = $warehousePrinters[$warehouseId] ?? null;
                
                if (!$printer) {
                    // 仓库没有绑定打印机，跳过此仓库
                    $skippedWarehouses[] = $warehouseId;
                    Log::info('跳过无打印机绑定的仓库', [
                        'order_id' => $order->id ?? null,
                        'order_no' => $order->order_no ?? null,
                        'warehouse_id' => $warehouseId,
                        'items_count' => count($items),
                        'reason' => '仓库未绑定打印机'
                    ]);
                    continue;
                }

                // 生成仓库分单内容
                $content = $this->generateWarehouseOrderContent($order, $items, $warehouseId);
                $copies = $options['copies'] ?? 1;

                // 发送打印请求
                $printResult = $this->sendPrintRequest($printer->sn, $content, $copies);
                
                $results[$warehouseId] = [
                    'success' => $printResult,
                    'message' => $printResult ? '打印成功' : '打印失败',
                    'warehouse_id' => $warehouseId,
                    'printer_sn' => $printer->sn,
                    'printer_name' => $printer->name,
                    'items_count' => count($items)
                ];

                if ($printResult) {
                    $processedWarehouses[] = $warehouseId;
                }

                // 记录打印日志
                Log::info('FlyCloud warehouse order printed', [
                    'order_id' => $order->id ?? null,
                    'order_no' => $order->order_no ?? null,
                    'warehouse_id' => $warehouseId,
                    'printer_sn' => $printer->sn,
                    'items_count' => count($items),
                    'success' => $printResult
                ]);
            }

            // 记录处理汇总日志
            Log::info('FlyCloud order warehouses processing summary', [
                'order_id' => $order->id ?? null,
                'order_no' => $order->order_no ?? null,
                'total_warehouses' => count($warehouseGroups),
                'processed_warehouses' => $processedWarehouses,
                'skipped_warehouses' => $skippedWarehouses,
                'processed_count' => count($processedWarehouses),
                'skipped_count' => count($skippedWarehouses)
            ]);

            return $results;
        } catch (\Exception $e) {
            Log::error('FlyCloud print order by warehouses failed', [
                'error' => $e->getMessage(),
                'order_id' => $order->id ?? null
            ]);
            
            return [
                'error' => [
                    'success' => false,
                    'message' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 打印指定仓库的订单分单
     */
    public function printWarehouseOrder($order, int $warehouseId, array $options = []): bool
    {
        try {
            // 1. 获取该仓库的订单商品
            $items = $this->getOrderItemsByWarehouse($order, $warehouseId);
            
            if (empty($items)) {
                throw new \Exception("Order has no items for warehouse {$warehouseId}");
            }

            // 2. 获取仓库打印机
            $printType = $options['print_type'] ?? WarehousePrinterBinding::PRINT_TYPE_ORDER;
            $printer = WarehousePrinterBinding::getWarehouseDefaultPrinter($warehouseId, $printType);
            
            if (!$printer) {
                // 使用全局默认打印机
                $printer = $this->resolvePrinter($options);
            }

            if (!$printer) {
                throw new \Exception("No available printer for warehouse {$warehouseId}");
            }

            // 3. 生成打印内容
            $content = $this->generateWarehouseOrderContent($order, $items, $warehouseId);
            $copies = $options['copies'] ?? 1;

            // 4. 发送打印请求
            return $this->sendPrintRequest($printer->sn, $content, $copies);
        } catch (\Exception $e) {
            Log::error('FlyCloud print warehouse order failed', [
                'error' => $e->getMessage(),
                'order_id' => $order->id ?? null,
                'warehouse_id' => $warehouseId
            ]);
            return false;
        }
    }

    /**
     * 按仓库分组订单商品
     */
    protected function groupOrderItemsByWarehouse($order): array
    {
        $groups = [];
        
        if (!$order->items || $order->items->isEmpty()) {
            return $groups;
        }

        foreach ($order->items as $item) {
            $warehouseId = $this->getItemWarehouseId($item);
            
            if ($warehouseId) {
                if (!isset($groups[$warehouseId])) {
                    $groups[$warehouseId] = [];
                }
                $groups[$warehouseId][] = $item;
            }
        }

        return $groups;
    }

    /**
     * 获取指定仓库的订单商品
     */
    protected function getOrderItemsByWarehouse($order, int $warehouseId): array
    {
        $items = [];
        
        if (!$order->items || $order->items->isEmpty()) {
            return $items;
        }

        foreach ($order->items as $item) {
            if ($this->getItemWarehouseId($item) === $warehouseId) {
                $items[] = $item;
            }
        }

        return $items;
    }

    /**
     * 获取商品的仓库ID
     */
    protected function getItemWarehouseId($item): ?int
    {
        // 获取仓库分配策略
        $strategy = config('flycloud.warehouse_assignment.assignment_strategy', 'product');
        
        // 1. 检查订单商品是否指定了仓库（最高优先级）
        if (isset($item->warehouse_id) && $item->warehouse_id) {
            return $item->warehouse_id;
        }

        // 2. 根据策略分配仓库
        switch ($strategy) {
            case 'inventory':
                return $this->getWarehouseByInventory($item);
            
            case 'region':
                return $this->getWarehouseByRegion($item);
            
            case 'product':
            default:
                return $this->getWarehouseByProduct($item);
        }
    }
    
    /**
     * 根据商品默认仓库分配
     */
    protected function getWarehouseByProduct($item): ?int
    {
        // 检查商品是否有默认仓库
        if ($item->product && isset($item->product->warehouse_id) && $item->product->warehouse_id) {
            return $item->product->warehouse_id;
        }

        // 检查SKU是否有仓库信息
        if ($item->sku && isset($item->sku->warehouse_id) && $item->sku->warehouse_id) {
            return $item->sku->warehouse_id;
        }

        // 返回默认仓库
        return config('flycloud.warehouse_assignment.default_warehouse_id', 1);
    }
    
    /**
     * 根据库存情况分配仓库（优先选择有库存的仓库）
     */
    protected function getWarehouseByInventory($item): ?int
    {
        try {
            if (!$item->product_id) {
                return $this->getWarehouseByProduct($item);
            }

            // 查找该商品有库存的仓库
            $inventories = \App\Inventory\Models\Inventory::where('product_id', $item->product_id)
                ->where('stock', '>', 0)
                ->with('warehouse')
                ->orderBy('stock', 'desc') // 优先选择库存多的仓库
                ->get();

            if ($inventories->isEmpty()) {
                // 没有库存，回退到商品默认仓库
                return $this->getWarehouseByProduct($item);
            }

            // 检查库存是否足够
            $requiredQuantity = $item->quantity ?? 1;
            foreach ($inventories as $inventory) {
                if ($inventory->stock >= $requiredQuantity) {
                    return $inventory->warehouse_id;
                }
            }

            // 没有足够库存的单个仓库，选择库存最多的
            return $inventories->first()->warehouse_id;

        } catch (\Exception $e) {
            Log::warning('根据库存分配仓库失败，回退到默认策略', [
                'item_id' => $item->id ?? null,
                'product_id' => $item->product_id ?? null,
                'error' => $e->getMessage()
            ]);
            
            return $this->getWarehouseByProduct($item);
        }
    }
    
    /**
     * 根据区域分配仓库（按配送区域优化）
     */
    protected function getWarehouseByRegion($item): ?int
    {
        try {
            // 如果订单有区域信息，尝试匹配最近的仓库
            $order = $item->order ?? null;
            if ($order && $order->region_id) {
                // 这里可以实现根据区域距离选择最优仓库的逻辑
                // 目前先回退到库存策略
                return $this->getWarehouseByInventory($item);
            }

            return $this->getWarehouseByInventory($item);

        } catch (\Exception $e) {
            Log::warning('根据区域分配仓库失败，回退到库存策略', [
                'item_id' => $item->id ?? null,
                'error' => $e->getMessage()
            ]);
            
            return $this->getWarehouseByInventory($item);
        }
    }

    /**
     * 生成仓库分单内容
     */
    protected function generateWarehouseOrderContent($order, array $items, int $warehouseId): string
    {
        $content = "";
        
        // 店铺名称和标题
        $storeName = env('FLYCLOUD_STORE_NAME', '万家生鲜');
        $content .= "<C><B>" . $storeName . "</B></C><BR>";
        $content .= "<C>仓库分单</C><BR>";
        $content .= "================================<BR>";
        
        // 基本信息
        $content .= "<L>订单号: " . ($order->order_no ?? '') . "</L><BR>";
        $content .= "<L>仓库ID: " . $warehouseId . "</L><BR>";
        $content .= "<L>分单时间: " . now()->format('Y-m-d H:i:s') . "</L><BR>";
        $content .= "<L>下单时间: " . ($order->created_at ? $order->created_at->format('Y-m-d H:i:s') : '') . "</L><BR>";
        
        // 下单人信息
        $customerName = $this->getCustomerName($order);
        $customerPhone = $this->getCustomerPhone($order);
        if ($customerName) {
            $content .= "<L>下单人: " . $customerName . "</L><BR>";
        }
        if ($customerPhone) {
            $content .= "<L>下单手机: " . $customerPhone . "</L><BR>";
        }
        
        // 支付信息
        if (isset($order->payment_method) && $order->payment_method) {
            $paymentMethod = $this->getPaymentMethodName($order->payment_method);
            $content .= "<L>支付方式: " . $paymentMethod . "</L><BR>";
        }
        
        $content .= "--------------------------------<BR>";
        
        // 商品列表（仅显示该仓库的商品）
        $warehouseTotal = 0;
        foreach ($items as $item) {
            $productName = $item->product_name ?? ($item->product->name ?? '商品');
            $price = $item->price ?? 0;
            $quantity = $item->quantity ?? 1;
            $subtotal = $price * $quantity;
            $warehouseTotal += $subtotal;
            
            // 获取单位信息
            $unit = $this->getItemUnit($item);
            $unitDisplay = $unit ? "({$unit})" : '';
            
            $content .= "<L>" . $productName . "</L><BR>";
            $content .= "<L>数量: " . $quantity . $unitDisplay . " x ¥" . number_format($price, 2) . "</L><BR>";
            $content .= "<R>小计: ¥" . number_format($subtotal, 2) . "</R><BR>";
            
            // 显示SKU信息（如果有）
            if (isset($item->sku_code) && $item->sku_code) {
                $content .= "<L>SKU: " . $item->sku_code . "</L><BR>";
            }
            
            $content .= "<BR>";
        }
        
        $content .= "--------------------------------<BR>";
        
        // 仓库小计
        $content .= "<L>商品数量: " . count($items) . "</L><BR>";
        $content .= "<R><B>仓库小计: ¥" . number_format($warehouseTotal, 2) . "</B></R><BR>";
        
        $content .= "--------------------------------<BR>";
        
        // 收货信息
        $content .= "<L><B>收货信息</B></L><BR>";
        $content .= "<L>收货人: " . ($order->contact_name ?? '') . "</L><BR>";
        $content .= "<L>联系电话: " . ($order->contact_phone ?? '') . "</L><BR>";
        $content .= "<L>收货地址: " . ($order->shipping_address ?? '') . "</L><BR>";
        
        // 备注信息
        if (isset($order->notes) && $order->notes) {
            $content .= "<L>订单备注: " . $order->notes . "</L><BR>";
        }
        
        $content .= "================================<BR>";
        $content .= "<C>请按此单拣货</C><BR>";
        $content .= "<C>打印时间: " . now()->format('Y-m-d H:i:s') . "</C><BR>";
        
        // 添加切纸指令
        $content .= "<CUT>";
        
        return $content;
    }
    
    /**
     * 获取客户姓名
     */
    protected function getCustomerName($order): ?string
    {
        try {
            // 1. 优先使用订单中的客户姓名
            if (isset($order->customer_name) && $order->customer_name) {
                return $order->customer_name;
            }
            
            // 2. 从关联用户获取
            if ($order->user) {
                if ($order->user->name) {
                    return $order->user->name;
                }
                if ($order->user->nickname) {
                    return $order->user->nickname;
                }
                if ($order->user->phone) {
                    return substr($order->user->phone, 0, 3) . '****' . substr($order->user->phone, -4);
                }
            }
            
            // 3. 使用收货人姓名作为备选
            if (isset($order->contact_name) && $order->contact_name) {
                return $order->contact_name;
            }
            
            return null;
        } catch (\Exception $e) {
            Log::warning('获取客户姓名失败', [
                'order_id' => $order->id ?? null,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取客户手机号
     */
    protected function getCustomerPhone($order): ?string
    {
        try {
            // 1. 从关联用户获取
            if ($order->user && $order->user->phone) {
                return $order->user->phone;
            }
            
            // 2. 使用收货人电话作为备选
            if (isset($order->contact_phone) && $order->contact_phone) {
                return $order->contact_phone;
            }
            
            return null;
        } catch (\Exception $e) {
            Log::warning('获取客户手机号失败', [
                'order_id' => $order->id ?? null,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * 获取支付方式名称
     */
    protected function getPaymentMethodName($paymentMethod): string
    {
        $paymentMethods = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'cash' => '现金支付',
            'cod' => '货到付款',
            'bank_transfer' => '银行转账',
            'pos' => 'POS机刷卡',
            'credit' => '信用支付'
        ];
        
        return $paymentMethods[$paymentMethod] ?? $paymentMethod;
    }

    /**
     * 获取订单项的单位信息
     */
    protected function getItemUnit($item): ?string
    {
        try {
            // 1. 优先使用订单项的单位信息
            if (isset($item->unit_name) && $item->unit_name) {
                return $item->unit_name;
            }
            
            if (isset($item->unit_symbol) && $item->unit_symbol) {
                return $item->unit_symbol;
            }
            
            // 2. 从关联的单位模型获取
            if ($item->unit) {
                return $item->unit->symbol ?? $item->unit->name ?? null;
            }
            
            // 3. 从商品获取基本单位
            if ($item->product && $item->product->baseUnit) {
                return $item->product->baseUnit->symbol ?? $item->product->baseUnit->name ?? null;
            }
            
            // 4. 通过unit_id加载单位信息
            if ($item->unit_id) {
                $unit = \App\Unit\Models\Unit::find($item->unit_id);
                if ($unit) {
                    return $unit->symbol ?? $unit->name ?? null;
                }
            }
            
            return null;
        } catch (\Exception $e) {
            Log::warning('获取订单项单位信息失败', [
                'item_id' => $item->id ?? null,
                'unit_id' => $item->unit_id ?? null,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取订单所涉及的仓库列表
     */
    public function getOrderWarehouses($order): array
    {
        $warehouseIds = [];
        
        if (!$order->items || $order->items->isEmpty()) {
            return $warehouseIds;
        }

        foreach ($order->items as $item) {
            $warehouseId = $this->getItemWarehouseId($item);
            if ($warehouseId && !in_array($warehouseId, $warehouseIds)) {
                $warehouseIds[] = $warehouseId;
            }
        }

        return $warehouseIds;
    }

    /**
     * 批量设置仓库打印机绑定
     */
    public function bindWarehousePrinters(array $bindings): array
    {
        $results = [];

        foreach ($bindings as $binding) {
            try {
                $result = WarehousePrinterBinding::bindWarehousePrinter(
                    $binding['warehouse_id'],
                    $binding['flycloud_printer_id'],
                    $binding['print_type'] ?? WarehousePrinterBinding::PRINT_TYPE_ORDER,
                    $binding['options'] ?? []
                );

                $results[] = [
                    'success' => true,
                    'warehouse_id' => $binding['warehouse_id'],
                    'printer_id' => $binding['flycloud_printer_id'],
                    'binding_id' => $result->id
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'success' => false,
                    'warehouse_id' => $binding['warehouse_id'],
                    'printer_id' => $binding['flycloud_printer_id'],
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 获取仓库打印机绑定信息
     */
    public function getWarehousePrinterBindings(int $warehouseId): array
    {
        return WarehousePrinterBinding::active()
            ->byWarehouse($warehouseId)
            ->with('flyCloudPrinter')
            ->orderByPriority()
            ->get()
            ->map(function ($binding) {
                return [
                    'id' => $binding->id,
                    'print_type' => $binding->print_type,
                    'print_type_name' => WarehousePrinterBinding::getPrintTypes()[$binding->print_type] ?? $binding->print_type,
                    'is_default' => $binding->is_default,
                    'priority' => $binding->priority,
                    'printer' => [
                        'id' => $binding->flyCloudPrinter->id,
                        'sn' => $binding->flyCloudPrinter->sn,
                        'name' => $binding->flyCloudPrinter->name,
                        'location' => $binding->flyCloudPrinter->location,
                        'status' => $binding->flyCloudPrinter->status
                    ]
                ];
            })
            ->toArray();
    }
}