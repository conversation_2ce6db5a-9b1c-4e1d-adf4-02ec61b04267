<?php

use Illuminate\Support\Facades\Route;
use App\WechatMp\Http\Controllers\ConfigController;

/*
|--------------------------------------------------------------------------
| 微信小程序模块 Web 路由
|--------------------------------------------------------------------------
|
| 这里定义微信小程序模块的所有Web路由
|
*/

// 微信小程序管理Web路由 - 管理后台
Route::group(['prefix' => 'admin/wechat/mp', 'middleware' => ['web', 'auth']], function () {
    // 微信小程序配置管理
    Route::get('/config', [ConfigController::class, 'adminConfig'])->name('admin.wechat.mp.config');
    
    // 微信小程序用户管理
    Route::get('/users', [ConfigController::class, 'adminUsers'])->name('admin.wechat.mp.users');
    
    // 微信小程序数据统计
    Route::get('/statistics', [ConfigController::class, 'adminStatistics'])->name('admin.wechat.mp.statistics');
}); 