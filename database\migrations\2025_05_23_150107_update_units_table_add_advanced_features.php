<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('units', function (Blueprint $table) {
            // 检查并添加可能缺失的列
            if (!Schema::hasColumn('units', 'symbol')) {
                $table->string('symbol', 20)->nullable()->comment('单位符号');
            }
            
            // 处理category和type列的兼容性问题
            if (Schema::hasColumn('units', 'category') && !Schema::hasColumn('units', 'type')) {
                // 如果有category但没有type，则重命名
                $table->renameColumn('category', 'type');
            } else if (!Schema::hasColumn('units', 'category') && !Schema::hasColumn('units', 'type')) {
                // 如果两者都不存在，则创建type
                $table->string('type', 50)->default('weight')->comment('单位类型(weight/volume/length/package/etc)');
            } else if (Schema::hasColumn('units', 'category') && Schema::hasColumn('units', 'type')) {
                // 如果两者都存在，则将category的值复制到type（如果type是空的）
                DB::statement('UPDATE units SET type = category WHERE type IS NULL OR type = ""');
                
                // 然后删除category列
                $table->dropColumn('category');
            }
            
            if (!Schema::hasColumn('units', 'base_unit_id')) {
                $table->unsignedBigInteger('base_unit_id')->nullable()->comment('基本单位ID');
                $table->foreign('base_unit_id')->references('id')->on('units')->onDelete('set null');
            }
            
            if (!Schema::hasColumn('units', 'conversion_factor')) {
                $table->decimal('conversion_factor', 20, 10)->nullable()->comment('与基本单位的转换系数');
            }
            
            if (!Schema::hasColumn('units', 'is_visible')) {
                $table->boolean('is_visible')->default(true)->comment('是否可见');
            }
            
            // 修改列名
            if (Schema::hasColumn('units', 'is_base') && !Schema::hasColumn('units', 'is_base_unit')) {
                $table->renameColumn('is_base', 'is_base_unit');
            }
            
            if (Schema::hasColumn('units', 'sort_order') && !Schema::hasColumn('units', 'sort')) {
                $table->renameColumn('sort_order', 'sort');
            }
            
            // 确保所有列具有适当的注释
            $table->string('name', 100)->comment('单位名称')->change();
            $table->string('display_name', 100)->comment('显示名称')->change();
            
            // 确保我们有正确的索引
            if (!Schema::hasIndex('units', ['symbol', 'type'])) {
                try {
                    $table->unique(['symbol', 'type']);
                } catch (\Exception $e) {
                    // 如果添加索引失败，记录错误并继续
                    Log::warning('添加索引失败: ' . $e->getMessage());
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('units', function (Blueprint $table) {
            // 我们不会撤销这些更改，因为我们不希望丢失数据
        });
    }
};
