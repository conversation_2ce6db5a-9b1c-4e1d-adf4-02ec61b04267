<!-- 自定义数量控制组件 -->
<view class="quantity-stepper">
  <!-- 减少按钮 -->
  <view 
    class="stepper-btn stepper-minus {{inputValue <= min ? 'disabled' : ''}}"
    catchtap="onMinus"
    bindtouchstart="onMinusTouchStart"
    bindtouchend="onMinusTouchEnd"
    data-action="minus"
  >
    <text class="stepper-icon">-</text>
  </view>
  
  <!-- 数量输入框 -->
  <input 
    class="stepper-input {{focus ? 'stepper-input-focus' : ''}} {{disabled ? 'stepper-input-disabled' : ''}}"
    type="number"
    value="{{inputValue}}"
    min="{{min}}"
    max="{{max}}"
    bindinput="onInput"
    bindblur="onBlur"
    bindfocus="onFocus"
    disabled="{{disabled}}"
  />
  
  <!-- 增加按钮 -->
  <view 
    class="stepper-btn stepper-plus {{inputValue >= max ? 'disabled' : ''}}"
    catchtap="onPlus"
    bindtouchstart="onPlusTouchStart"
    bindtouchend="onPlusTouchEnd"
    data-action="plus"
  >
    <text class="stepper-icon">+</text>
  </view>
</view> 