<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('regions', function (Blueprint $table) {
            // 添加地理位置相关字段
            $table->decimal('latitude', 10, 7)->nullable()->comment('纬度')->after('level');
            $table->decimal('longitude', 10, 7)->nullable()->comment('经度')->after('latitude');
            $table->json('boundary')->nullable()->comment('边界多边形坐标')->after('longitude');
            
            // 添加组合索引
            $table->index(['latitude', 'longitude'], 'idx_regions_coordinates');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('regions', function (Blueprint $table) {
            // 删除索引
            $table->dropIndex('idx_regions_coordinates');
            
            // 删除字段
            $table->dropColumn(['latitude', 'longitude', 'boundary']);
        });
    }
};
