<?php

namespace App\Unit\Http\Controllers;

use App\Unit\Models\UnitConversionGraph;
use App\Unit\Models\UnitConversionEdge;
use App\Unit\Models\Unit;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class UnitConversionGraphController extends Controller
{
    /**
     * 获取所有单位转换图
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $graphs = UnitConversionGraph::with('edges')->get();
        return response()->json(['data' => $graphs]);
    }

    /**
     * 创建新的单位转换图
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:50',
            'is_default' => 'boolean',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        DB::beginTransaction();
        try {
            $graph = UnitConversionGraph::create($request->all());
            
            // 如果设置为默认图，需要更新其他同类型图
            if ($request->input('is_default', false)) {
                $graph->setAsDefault();
            }
            
            DB::commit();
            return response()->json(['data' => $graph, 'message' => '转换图创建成功'], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => '创建失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 获取指定转换图详情
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $graph = UnitConversionGraph::with('edges')->find($id);
        
        if (!$graph) {
            return response()->json(['message' => '转换图不存在'], 404);
        }
        
        return response()->json(['data' => $graph]);
    }

    /**
     * 更新转换图
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $graph = UnitConversionGraph::find($id);
        
        if (!$graph) {
            return response()->json(['message' => '转换图不存在'], 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'type' => 'string|max:50',
            'is_default' => 'boolean',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        DB::beginTransaction();
        try {
            $graph->update($request->all());
            
            // 如果设置为默认图，需要更新其他同类型图
            if ($request->has('is_default') && $request->input('is_default')) {
                $graph->setAsDefault();
            }
            
            DB::commit();
            return response()->json(['data' => $graph, 'message' => '转换图更新成功']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => '更新失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 删除转换图
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $graph = UnitConversionGraph::find($id);
        
        if (!$graph) {
            return response()->json(['message' => '转换图不存在'], 404);
        }
        
        DB::beginTransaction();
        try {
            // 先删除所有关联的边
            $graph->edges()->delete();
            // 再删除图本身
            $graph->delete();
            
            DB::commit();
            return response()->json(['message' => '转换图删除成功']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => '删除失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 设置为默认转换图
     *
     * @param int $id
     * @return JsonResponse
     */
    public function setAsDefault(int $id): JsonResponse
    {
        $graph = UnitConversionGraph::find($id);
        
        if (!$graph) {
            return response()->json(['message' => '转换图不存在'], 404);
        }
        
        try {
            $graph->setAsDefault();
            return response()->json(['data' => $graph, 'message' => '已设置为默认转换图']);
        } catch (\Exception $e) {
            return response()->json(['message' => '设置失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 获取转换图的所有边
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getEdges(int $id): JsonResponse
    {
        $graph = UnitConversionGraph::find($id);
        
        if (!$graph) {
            return response()->json(['message' => '转换图不存在'], 404);
        }
        
        $edges = $graph->edges()->with(['fromUnit', 'toUnit'])->get();
        return response()->json(['data' => $edges]);
    }

    /**
     * 添加转换边
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function addEdge(Request $request, int $id): JsonResponse
    {
        $graph = UnitConversionGraph::find($id);
        
        if (!$graph) {
            return response()->json(['message' => '转换图不存在'], 404);
        }
        
        $validator = Validator::make($request->all(), [
            'from_unit_id' => 'required|exists:units,id',
            'to_unit_id' => 'required|exists:units,id',
            'conversion_factor' => 'required|numeric|gt:0',
            'bidirectional' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        // 检查单位类型是否一致
        $fromUnit = Unit::find($request->input('from_unit_id'));
        $toUnit = Unit::find($request->input('to_unit_id'));
        
        if ($fromUnit->type !== $toUnit->type) {
            return response()->json(['message' => '单位类型不匹配，无法创建转换关系'], 422);
        }
        
        // 检查图类型与单位类型是否一致
        if ($graph->type !== $fromUnit->type) {
            return response()->json(['message' => '单位类型与转换图类型不匹配'], 422);
        }
        
        // 检查是否已存在相同的边
        $existingEdge = UnitConversionEdge::where('graph_id', $id)
            ->where('from_unit_id', $request->input('from_unit_id'))
            ->where('to_unit_id', $request->input('to_unit_id'))
            ->first();
            
        if ($existingEdge) {
            return response()->json(['message' => '该转换关系已存在'], 422);
        }

        DB::beginTransaction();
        try {
            $edge = UnitConversionEdge::create([
                'graph_id' => $id,
                'from_unit_id' => $request->input('from_unit_id'),
                'to_unit_id' => $request->input('to_unit_id'),
                'conversion_factor' => $request->input('conversion_factor'),
            ]);
            
            // 如果是双向的，创建反向边
            if ($request->input('bidirectional', false)) {
                $reverseEdge = UnitConversionEdge::create([
                    'graph_id' => $id,
                    'from_unit_id' => $request->input('to_unit_id'),
                    'to_unit_id' => $request->input('from_unit_id'),
                    'conversion_factor' => 1 / $request->input('conversion_factor'),
                ]);
            }
            
            DB::commit();
            return response()->json(['data' => $edge, 'message' => '转换边添加成功'], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => '添加失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 删除转换边
     *
     * @param int $edgeId
     * @return JsonResponse
     */
    public function deleteEdge(int $edgeId): JsonResponse
    {
        $edge = UnitConversionEdge::find($edgeId);
        
        if (!$edge) {
            return response()->json(['message' => '转换边不存在'], 404);
        }
        
        try {
            $edge->delete();
            return response()->json(['message' => '转换边删除成功']);
        } catch (\Exception $e) {
            return response()->json(['message' => '删除失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 获取指定类型的默认转换图
     *
     * @param string $type
     * @return JsonResponse
     */
    public function getDefaultGraph(string $type): JsonResponse
    {
        $graph = UnitConversionGraph::getDefaultForType($type);
        
        if (!$graph) {
            return response()->json(['message' => '未找到类型为 ' . $type . ' 的默认转换图'], 404);
        }
        
        return response()->json(['data' => $graph->load('edges')]);
    }
} 