<?php

use App\Order\Http\Controllers\OrderController;
use App\Order\Http\Controllers\OrderMergeController;
use Illuminate\Support\Facades\Route;

// 需要用户认证的订单路由（普通用户商城系统）
Route::group(['prefix' => 'api/orders', 'middleware' => ['api', 'auth:sanctum']], function () {
    Route::get('/', [OrderController::class, 'index']); // 用户查看自己的订单列表
    Route::post('/', [OrderController::class, 'create']);
    Route::get('/stats', [OrderController::class, 'getStats']);
    Route::get('/{id}', [OrderController::class, 'show']);
    Route::put('/{id}/status', [OrderController::class, 'updateStatus']);
    Route::post('/{id}/cancel', [OrderController::class, 'cancel']);
});

// 需要员工权限的订单管理路由（员工系统）
Route::group(['prefix' => 'api/admin/orders', 'middleware' => ['api', 'auth:sanctum', 'employee.role:admin,manager,staff,warehouse_manager,crm_agent']], function () {
    Route::get('/', [OrderController::class, 'index']); // 员工查看所有订单列表
});

// 需要员工认证和CRM权限的代客下单路由
Route::group(['prefix' => 'api/orders', 'middleware' => ['api', 'auth:sanctum', 'employee.role:admin,manager,crm_agent']], function () {
    Route::post('/proxy', [OrderController::class, 'createForClient']);
    
    // 订单合并相关路由
    Route::prefix('merge')->group(function () {
        Route::post('/preview', [OrderMergeController::class, 'preview']);
        Route::post('/execute', [OrderMergeController::class, 'execute']);
        Route::get('/candidates', [OrderMergeController::class, 'candidates']);
        Route::post('/{merge}/revert', [OrderMergeController::class, 'revert']);
        Route::get('/history', [OrderMergeController::class, 'history']);
        Route::post('/auto-merge-today', [OrderMergeController::class, 'autoMergeToday']);
    });
}); 