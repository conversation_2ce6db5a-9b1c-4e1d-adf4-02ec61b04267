<?php

use App\Warehouse\Http\Controllers\WarehouseController;
use App\Warehouse\Http\Controllers\InventoryController;
use App\Warehouse\Http\Controllers\InventorySystemController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 仓库模块 Web 路由
|--------------------------------------------------------------------------
|
| 这里定义仓库管理模块的所有Web路由
|
*/

// 仓库管理Web路由 - 管理后台
Route::group(['prefix' => 'admin/warehouse', 'middleware' => ['web', 'auth']], function () {
    // 仓库列表
    Route::get('/', [WarehouseController::class, 'adminIndex'])->name('admin.warehouse.index');
    
    // 仓库详情
    Route::get('/{id}', [WarehouseController::class, 'adminShow'])->name('admin.warehouse.show');
    
    // 创建仓库页面
    Route::get('/create', [WarehouseController::class, 'adminCreate'])->name('admin.warehouse.create');
    
    // 编辑仓库页面
    Route::get('/{id}/edit', [WarehouseController::class, 'adminEdit'])->name('admin.warehouse.edit');
    
    // 库存管理
    Route::get('/{warehouseId}/inventory', [InventoryController::class, 'adminIndex'])->name('admin.warehouse.inventory.index');
    
    // 库存系统
    Route::get('/inventory-system/transactions', [InventorySystemController::class, 'adminTransactions'])->name('admin.warehouse.transactions');
    Route::get('/inventory-system/transaction/{id}', [InventorySystemController::class, 'adminShowTransaction'])->name('admin.warehouse.transaction.show');
}); 