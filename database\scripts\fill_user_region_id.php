<?php

use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Region\Models\Region;

require __DIR__ . '/../../vendor/autoload.php';

// Laravel 应用启动
$app = require __DIR__ . '/../../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

$updated = 0;
$total = 0;

User::whereNull('region_id')->chunk(100, function ($users) use (&$updated, &$total) {
    foreach ($users as $user) {
        $total++;
        if (!$user->province || !$user->city || !$user->district) {
            echo "[SKIP] User #{$user->id} 缺少省市区\n";
            continue;
        }
        $region = Region::where('name', $user->district)
            ->where('level', 3)
            ->whereHas('parent', function($q) use ($user) {
                $q->where('name', $user->city)
                  ->where('level', 2)
                  ->whereHas('parent', function($q2) use ($user) {
                      $q2->where('name', $user->province)
                         ->where('level', 1);
                  });
            })
            ->first();
        if ($region) {
            $user->region_id = $region->id;
            $user->save();
            $updated++;
            echo "[OK] User #{$user->id} {$user->province}{$user->city}{$user->district} => region_id={$region->id}\n";
        } else {
            echo "[FAIL] User #{$user->id} {$user->province}{$user->city}{$user->district} => 匹配不到region_id\n";
        }
    }
});

echo "\n共处理用户: $total, 成功填充: $updated\n"; 