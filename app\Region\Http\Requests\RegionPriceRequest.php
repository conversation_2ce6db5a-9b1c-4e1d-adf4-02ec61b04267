<?php

namespace App\Region\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RegionPriceRequest extends FormRequest
{
    /**
     * 确定用户是否有权限执行此请求
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * @return array
     */
    public function rules()
    {
        $rules = [];
        
        // 根据不同的操作路径选择不同的验证规则
        switch ($this->route()->getName()) {
            case 'regions.prices.get':
                $rules = [
                    'product_id' => 'required|integer|exists:products,id',
                    'region_id' => 'required|integer|exists:regions,id',
                ];
                break;
                
            case 'regions.products.prices.batch':
                $rules = [
                    'prices' => 'required|array',
                    'prices.*.region_id' => 'required|integer|exists:regions,id',
                    'prices.*.price' => 'required|numeric|min:0',
                    'prices.*.original_price' => 'nullable|numeric|min:0',
                    'prices.*.stock' => 'nullable|integer|min:0',
                    'prices.*.status' => 'nullable|boolean',
                    'prices.*.start_date' => 'nullable|date',
                    'prices.*.end_date' => 'nullable|date|after_or_equal:prices.*.start_date',
                    'prices.*.special_conditions' => 'nullable|array',
                ];
                break;
                
            case 'regions.prices.copy':
                $rules = [
                    'source_region_id' => 'required|integer|exists:regions,id',
                    'target_region_id' => 'required|integer|exists:regions,id',
                    'product_id' => 'nullable|integer|exists:products,id',
                ];
                break;
                
            case 'regions.prices.adjust':
                $rules = [
                    'adjustment_value' => 'required|numeric',
                    'adjustment_type' => 'required|in:amount,percent',
                    'product_id' => 'nullable|integer|exists:products,id',
                    'min_price' => 'nullable|numeric|min:0',
                    'max_price' => 'nullable|numeric|min:0',
                ];
                break;
                
            case 'regions.products.prices.delete':
                $rules = [
                    'region_id' => 'nullable|integer|exists:regions,id',
                ];
                break;
        }
        
        return $rules;
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages()
    {
        return [
            'product_id.required' => '商品ID不能为空',
            'product_id.exists' => '商品不存在',
            'region_id.required' => '区域ID不能为空',
            'region_id.exists' => '区域不存在',
            'prices.required' => '价格数据不能为空',
            'prices.*.price.required' => '价格不能为空',
            'prices.*.price.min' => '价格不能小于0',
            'prices.*.original_price.min' => '原价不能小于0',
            'prices.*.stock.min' => '库存不能小于0',
            'prices.*.end_date.after_or_equal' => '结束日期必须晚于或等于开始日期',
            'source_region_id.required' => '源区域ID不能为空',
            'source_region_id.exists' => '源区域不存在',
            'target_region_id.required' => '目标区域ID不能为空',
            'target_region_id.exists' => '目标区域不存在',
            'adjustment_value.required' => '调整值不能为空',
            'adjustment_type.required' => '调整类型不能为空',
            'adjustment_type.in' => '调整类型必须是金额(amount)或百分比(percent)',
        ];
    }
} 