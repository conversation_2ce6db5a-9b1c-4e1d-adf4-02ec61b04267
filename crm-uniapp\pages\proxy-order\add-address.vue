<template>
	<view class="add-address-container">
		<!-- 表单卡片 -->
		<view class="form-card">
			<view class="form-header">
				<text class="form-title">新增收货地址</text>
			</view>
			
			<!-- 调试信息 -->
			<view class="debug-info" style="background: #fff3cd; padding: 20rpx; margin: 20rpx; border-radius: 8rpx;">
				<text style="color: #856404; font-size: 24rpx;">调试信息：</text>
				<text style="color: #856404; font-size: 24rpx; display: block;">客户ID: {{ clientId }}</text>
				<text style="color: #856404; font-size: 24rpx; display: block;">联系人: {{ addressForm.contact_name }}</text>
				<text style="color: #856404; font-size: 24rpx; display: block;">电话: {{ addressForm.contact_phone }}</text>
				<text style="color: #856404; font-size: 24rpx; display: block;">选择的地区: {{ selectedRegion || '未选择' }}</text>
				<text style="color: #856404; font-size: 24rpx; display: block;">可提交: {{ canSubmit ? '是' : '否' }}</text>
			</view>
			
			<view class="form-content">
				<!-- 联系人信息 -->
				<view class="form-section">
					<text class="section-title">联系人信息</text>
					<view class="form-item">
						<text class="form-label">联系人姓名 <text class="required">*</text></text>
						<input 
							class="form-input" 
							type="text" 
							placeholder="请输入联系人姓名"
							v-model="addressForm.contact_name"
							maxlength="20"
						/>
					</view>
					<view class="form-item">
						<text class="form-label">联系电话 <text class="required">*</text></text>
						<input 
							class="form-input" 
							type="text" 
							placeholder="请输入联系电话"
							v-model="addressForm.contact_phone"
							maxlength="11"
						/>
					</view>
				</view>
				
				<!-- 地址信息 -->
				<view class="form-section">
					<text class="section-title">地址信息</text>
					<view class="form-item">
						<text class="form-label">所在地区 <text class="required">*</text></text>
						<picker 
							mode="region" 
							:value="regionArray" 
							@change="onRegionChange"
							@tap="testRegionPicker"
						>
							<view class="region-picker">
								<text class="region-text" v-if="selectedRegion">{{ selectedRegion }}</text>
								<text class="region-placeholder" v-else>请选择省市区</text>
								<text class="arrow-icon">></text>
							</view>
						</picker>
					</view>
					<view class="form-item">
						<text class="form-label">详细地址 <text class="required">*</text></text>
						<textarea 
							class="form-textarea" 
							placeholder="请输入详细地址（街道、门牌号等）"
							v-model="addressForm.detail_address"
							maxlength="100"
						></textarea>
					</view>
				</view>
				
				<!-- 地址标签 -->
				<view class="form-section">
					<text class="section-title">地址标签</text>
					<view class="tag-list">
						<view 
							class="tag-item" 
							:class="{ active: addressForm.tag === tag.value }"
							v-for="tag in addressTags" 
							:key="tag.value"
							@tap="selectTag(tag.value)"
						>
							<text class="tag-text">{{ tag.label }}</text>
						</view>
					</view>
					<view class="form-item" v-if="addressForm.tag === 'custom'">
						<input 
							class="form-input" 
							type="text" 
							placeholder="请输入自定义标签"
							v-model="addressForm.custom_tag"
							maxlength="10"
						/>
					</view>
				</view>
				
				<!-- 设置选项 -->
				<view class="form-section">
					<view class="form-item checkbox-item">
						<view class="checkbox-wrapper" @tap="toggleDefault">
							<view class="checkbox" :class="{ checked: addressForm.is_default }">
								<text class="checkbox-icon" v-if="addressForm.is_default">✓</text>
							</view>
							<text class="checkbox-label">设为默认地址</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 提交按钮 -->
		<view class="submit-section">
			<button 
				class="submit-btn" 
				:class="{ disabled: !canSubmit }" 
				:disabled="!canSubmit || submitting"
				@tap="submitAddress"
			>
				<text v-if="submitting">保存中...</text>
				<text v-else>保存地址</text>
			</button>
		</view>
	</view>
</template>

<script>
import addressApi from '../../api/address.js'

export default {
	data() {
		return {
			clientId: null,
			addressForm: {
				contact_name: '',
				contact_phone: '',
				province: '',
				city: '',
				district: '',
				detail_address: '',
				tag: 'home',
				custom_tag: '',
				is_default: false
			},
			
			// 地址标签选项
			addressTags: [
				{ value: 'home', label: '家' },
				{ value: 'company', label: '公司' },
				{ value: 'school', label: '学校' },
				{ value: 'custom', label: '自定义' }
			],
			
			regionArray: [],
			selectedRegion: '',
			submitting: false
		}
	},
	
	computed: {
		canSubmit() {
			return this.addressForm.contact_name.trim() &&
				   this.addressForm.contact_phone.trim() &&
				   this.addressForm.province &&
				   this.addressForm.city &&
				   this.addressForm.district &&
				   this.addressForm.detail_address.trim() &&
				   (this.addressForm.tag !== 'custom' || this.addressForm.custom_tag.trim())
		}
	},
	
	onLoad(options) {
		if (options.clientId) {
			this.clientId = parseInt(options.clientId)
		}
		
		// 如果有客户信息，预填联系人信息
		if (options.clientName) {
			this.addressForm.contact_name = decodeURIComponent(options.clientName)
		}
		if (options.clientPhone) {
			this.addressForm.contact_phone = options.clientPhone
		}
	},
	
	methods: {
		// 测试地区选择器点击
		testRegionPicker() {
			console.log('地区选择器被点击了')
			uni.showToast({
				title: '地区选择器被点击',
				icon: 'none'
			})
		},
		
		// 地区选择改变
		onRegionChange(event) {
			console.log('地区选择改变:', event.detail.value)
			const regions = event.detail.value
			this.regionArray = regions
			this.addressForm.province = regions[0]
			this.addressForm.city = regions[1]
			this.addressForm.district = regions[2]
			this.selectedRegion = regions.join(' ')
		},
		
		// 选择标签
		selectTag(tag) {
			this.addressForm.tag = tag
			if (tag !== 'custom') {
				this.addressForm.custom_tag = ''
			}
		},
		
		// 切换默认地址
		toggleDefault() {
			this.addressForm.is_default = !this.addressForm.is_default
		},
		
		// 提交地址
		async submitAddress() {
			if (!this.canSubmit || this.submitting) return
			
			// 验证手机号格式
			const phoneRegex = /^1[3-9]\d{9}$/
			if (!phoneRegex.test(this.addressForm.contact_phone)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				})
				return
			}
			
			this.submitting = true
			
			try {
				// 准备提交数据
				const submitData = {
					contact_name: this.addressForm.contact_name.trim(),
					contact_phone: this.addressForm.contact_phone.trim(),
					province: this.addressForm.province,
					city: this.addressForm.city,
					district: this.addressForm.district,
					address: this.addressForm.detail_address.trim(),
					notes: this.addressForm.tag === 'custom' ? this.addressForm.custom_tag.trim() : this.addressForm.tag,
					is_default: this.addressForm.is_default
				}
				
				console.log('提交地址数据:', submitData)
				
				// 调用API创建地址
				const response = await addressApi.createAddress(this.clientId, submitData)
				
				if (response && response.data) {
					uni.showToast({
						title: '地址保存成功',
						icon: 'success',
						duration: 2000
					})
					
					// 通过页面栈传递新地址数据
					const pages = getCurrentPages()
					const prevPage = pages[pages.length - 2]
					
					if (prevPage) {
						prevPage.data = prevPage.data || {}
						prevPage.data.newAddress = response.data
					}
					
					// 返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 2000)
				} else {
					throw new Error('保存失败')
				}
				
			} catch (error) {
				console.error('保存地址失败:', error)
				
				let errorMessage = '保存失败，请重试'
				if (error.response && error.response.data && error.response.data.message) {
					errorMessage = error.response.data.message
				} else if (error.message) {
					errorMessage = error.message
				}
				
				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
			} finally {
				this.submitting = false
			}
		}
	}
}
</script>

<style scoped>
.add-address-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.form-card {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
}

.form-header {
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.form-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
}

.form-content {
	padding: 32rpx;
}

.form-section {
	margin-bottom: 40rpx;
}

.form-section:last-child {
	margin-bottom: 0;
}

.section-title {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 24rpx;
}

.form-item {
	margin-bottom: 24rpx;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	display: block;
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 12rpx;
}

.required {
	color: #ff4757;
}

.form-input {
	width: 100%;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #333333;
	border: 2rpx solid transparent;
}

.form-input:focus {
	border-color: #007AFF;
	background: #ffffff;
}

.form-textarea {
	width: 100%;
	min-height: 120rpx;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #333333;
	border: 2rpx solid transparent;
	resize: none;
}

.form-textarea:focus {
	border-color: #007AFF;
	background: #ffffff;
}

.region-picker {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: 2rpx solid transparent;
}

.region-text {
	font-size: 28rpx;
	color: #333333;
}

.region-placeholder {
	font-size: 28rpx;
	color: #999999;
}

.arrow-icon {
	font-size: 24rpx;
	color: #cccccc;
}

.tag-list {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	margin-bottom: 24rpx;
}

.tag-item {
	padding: 16rpx 32rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: 2rpx solid transparent;
}

.tag-item.active {
	background: #007AFF;
	border-color: #007AFF;
}

.tag-text {
	font-size: 28rpx;
	color: #666666;
}

.tag-item.active .tag-text {
	color: #ffffff;
}

.checkbox-item {
	margin-bottom: 0;
}

.checkbox-wrapper {
	display: flex;
	align-items: center;
}

.checkbox {
	width: 40rpx;
	height: 40rpx;
	border: 2rpx solid #d1d1d6;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
}

.checkbox.checked {
	background: #007AFF;
	border-color: #007AFF;
}

.checkbox-icon {
	color: #ffffff;
	font-size: 24rpx;
	font-weight: 600;
}

.checkbox-label {
	font-size: 28rpx;
	color: #333333;
}

.submit-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	padding: 24rpx 32rpx;
	border-top: 1rpx solid #f0f0f0;
}

.submit-btn {
	width: 100%;
	padding: 24rpx;
	background: #007AFF;
	color: #ffffff;
	border: none;
	border-radius: 12rpx;
	font-size: 32rpx;
	font-weight: 600;
}

.submit-btn.disabled {
	background: #cccccc;
	color: #999999;
}
</style> 