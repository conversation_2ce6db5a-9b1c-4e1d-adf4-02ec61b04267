<?php

namespace App\Unit\DTO;

class UnitConversionData
{
    /**
     * 转换关系ID
     *
     * @var int|null
     */
    public ?int $id;
    
    /**
     * 源单位ID
     *
     * @var int
     */
    public int $fromUnitId;
    
    /**
     * 目标单位ID
     *
     * @var int
     */
    public int $toUnitId;
    
    /**
     * 转换系数
     *
     * @var float
     */
    public float $conversionFactor;
    
    /**
     * 是否双向转换
     *
     * @var bool
     */
    public bool $isBidirectional;
    
    /**
     * 适用角色
     *
     * @var string|null
     */
    public ?string $role;
    
    /**
     * 源单位对象
     *
     * @var UnitData|null
     */
    public ?UnitData $fromUnit;
    
    /**
     * 目标单位对象
     *
     * @var UnitData|null
     */
    public ?UnitData $toUnit;
    
    /**
     * 创建实例
     */
    public function __construct()
    {
        $this->id = null;
        $this->fromUnitId = 0;
        $this->toUnitId = 0;
        $this->conversionFactor = 1.0;
        $this->isBidirectional = false;
        $this->role = null;
        $this->fromUnit = null;
        $this->toUnit = null;
    }
    
    /**
     * 从数组创建DTO
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        $dto = new self();
        
        if (isset($data['id'])) {
            $dto->id = (int)$data['id'];
        }
        
        if (isset($data['from_unit_id'])) {
            $dto->fromUnitId = (int)$data['from_unit_id'];
        }
        
        if (isset($data['to_unit_id'])) {
            $dto->toUnitId = (int)$data['to_unit_id'];
        }
        
        if (isset($data['conversion_factor'])) {
            $dto->conversionFactor = (float)$data['conversion_factor'];
        }
        
        if (isset($data['is_bidirectional'])) {
            $dto->isBidirectional = (bool)$data['is_bidirectional'];
        }
        
        if (isset($data['role'])) {
            $dto->role = $data['role'];
        }
        
        // 处理关联的单位
        if (isset($data['from_unit']) && is_array($data['from_unit'])) {
            $dto->fromUnit = UnitData::fromModel((object)$data['from_unit']);
        }
        
        if (isset($data['to_unit']) && is_array($data['to_unit'])) {
            $dto->toUnit = UnitData::fromModel((object)$data['to_unit']);
        }
        
        return $dto;
    }
    
    /**
     * 转换为数组
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [
            'from_unit_id' => $this->fromUnitId,
            'to_unit_id' => $this->toUnitId,
            'conversion_factor' => $this->conversionFactor,
            'is_bidirectional' => $this->isBidirectional,
        ];
        
        if ($this->id) {
            $data['id'] = $this->id;
        }
        
        if ($this->role) {
            $data['role'] = $this->role;
        }
        
        if ($this->fromUnit) {
            $data['from_unit'] = $this->fromUnit->toArray();
        }
        
        if ($this->toUnit) {
            $data['to_unit'] = $this->toUnit->toArray();
        }
        
        return $data;
    }
    
    /**
     * 转换为模型数据
     *
     * @return array
     */
    public function toModelData(): array
    {
        $data = [
            'from_unit_id' => $this->fromUnitId,
            'to_unit_id' => $this->toUnitId,
            'conversion_factor' => $this->conversionFactor,
            'is_bidirectional' => $this->isBidirectional,
        ];
        
        if ($this->role) {
            $data['role'] = $this->role;
        }
        
        return $data;
    }
} 