# FlyCloud 飞蛾云打印模块

这是一个独立的飞蛾云打印模块，专门用于远程小票打印。支持多台打印机管理、仓库分单打印等功能。

## 主要功能

### 1. 基础打印功能
- 文本打印
- HTML 打印
- 订单小票打印

### 2. 多台打印机管理
- 打印机添加/删除
- 打印机状态监控
- 默认打印机设置
- 数据库存储管理

### 3. 仓库分单打印 ⭐ 核心功能
- 按仓库自动分组打印
- 仓库与打印机绑定
- 支持多种打印类型（订单、拣货、配送）
- 智能打印机选择

## 环境配置

在 `.env` 文件中添加以下配置：

```env
# 飞蛾云打印配置
FLYCLOUD_API_URL=http://api.feieyun.cn/Api/Open/
FLYCLOUD_USER=你的飞蛾云账号
FLYCLOUD_UKEY=你的UKEY
FLYCLOUD_DEBUG=false
FLYCLOUD_STORE_NAME=万家生鲜

# 默认仓库ID（可选）
DEFAULT_WAREHOUSE_ID=1
```

## 数据库表

### 1. flycloud_printers - 打印机管理表
```sql
- id: 主键
- sn: 打印机编号（唯一）
- name: 打印机名称
- key: 打印机密钥
- location: 位置
- description: 描述
- status: 状态
- is_active: 是否启用
- is_default: 是否默认
- last_status_check: 最后状态检查时间
- created_by: 创建人
```

### 2. warehouse_printer_bindings - 仓库打印机绑定表
```sql
- id: 主键
- warehouse_id: 仓库ID
- flycloud_printer_id: 飞蛾云打印机ID
- print_type: 打印类型（order/picking/delivery）
- is_active: 是否启用
- is_default: 是否为仓库默认打印机
- priority: 优先级
- settings: 打印设置（JSON）
- created_by: 创建人
```

## API 接口

### 基础打印功能

#### 打印文本
```http
POST /api/flycloud/print-text
Content-Type: application/json

{
    "content": "打印内容",
    "printer_sn": "打印机编号(可选)",
    "copies": 1
}
```

#### 打印HTML
```http
POST /api/flycloud/print-html
Content-Type: application/json

{
    "content": "<C>居中文本</C><BR>",
    "printer_sn": "打印机编号(可选)",
    "copies": 1
}
```

#### 打印订单小票
```http
POST /api/flycloud/print-order
Content-Type: application/json

{
    "order_id": 123,
    "printer_sn": "打印机编号(可选)",
    "copies": 1
}
```

### 分单打印功能

#### 按仓库分单打印订单
```http
POST /api/flycloud/print-order-by-warehouses
Content-Type: application/json

{
    "order_id": 123,
    "print_type": "order",
    "copies": 1
}
```

#### 打印指定仓库的订单分单
```http
POST /api/flycloud/print-warehouse-order
Content-Type: application/json

{
    "order_id": 123,
    "warehouse_id": 1,
    "print_type": "picking",
    "copies": 1
}
```

#### 获取订单涉及的仓库列表
```http
GET /api/flycloud/order/{orderId}/warehouses
```

### 打印机管理

#### 获取打印机列表
```http
GET /api/flycloud/printers
```

#### 添加打印机
```http
POST /api/flycloud/printers
Content-Type: application/json

{
    "sn": "打印机编号",
    "key": "打印机密钥",
    "name": "打印机名称",
    "location": "位置",
    "is_default": false
}
```

#### 删除打印机
```http
DELETE /api/flycloud/printers
Content-Type: application/json

{
    "sn": "打印机编号"
}
```

#### 获取打印机状态
```http
POST /api/flycloud/printer-status
Content-Type: application/json

{
    "printer_sn": "打印机编号"
}
```

### 仓库打印机绑定

#### 绑定仓库和打印机
```http
POST /api/flycloud/warehouse-printer-binding
Content-Type: application/json

{
    "warehouse_id": 1,
    "flycloud_printer_id": 1,
    "print_type": "order",
    "is_default": true,
    "priority": 10
}
```

#### 批量绑定
```http
POST /api/flycloud/warehouse-printer-binding/batch
Content-Type: application/json

{
    "bindings": [
        {
            "warehouse_id": 1,
            "flycloud_printer_id": 1,
            "print_type": "order"
        },
        {
            "warehouse_id": 2,
            "flycloud_printer_id": 2,
            "print_type": "picking"
        }
    ]
}
```

#### 获取仓库打印机绑定信息
```http
GET /api/flycloud/warehouse/{warehouseId}/printer-bindings
```

## 使用示例

### PHP 代码示例
```php
use App\FlyCloud\Services\FlyCloudService;

// 获取飞蛾云服务
$flyCloudService = app(FlyCloudService::class);

// 按仓库分单打印订单
$results = $flyCloudService->printOrderByWarehouses($order, [
    'print_type' => 'order',
    'copies' => 1
]);

// 打印指定仓库的订单
$result = $flyCloudService->printWarehouseOrder($order, $warehouseId, [
    'print_type' => 'picking',
    'copies' => 1
]);
```

### JavaScript 前端调用
```javascript
// 按仓库分单打印
fetch('/api/flycloud/print-order-by-warehouses', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    },
    body: JSON.stringify({
        order_id: 123,
        print_type: 'order',
        copies: 1
    })
})
.then(response => response.json())
.then(data => {
    console.log('分单打印结果:', data);
});
```

## 小票格式标签

飞蛾云支持以下格式控制标签：

- `<BR>` - 换行符
- `<CUT>` - 切刀指令(主动切纸)
- `<LOGO>` - 打印LOGO指令
- `<C>文本</C>` - 居中对齐
- `<L>文本</L>` - 左对齐
- `<R>文本</R>` - 右对齐
- `<B>文本</B>` - 粗体
- `<QR>二维码内容</QR>` - 二维码

## 打印机选择逻辑

系统按以下优先级选择打印机：

1. **指定打印机SN** - 如果传入了 `printer_sn`
2. **指定打印机ID** - 如果传入了 `printer_id`
3. **仓库默认打印机** - 仓库绑定的默认打印机
4. **全局默认打印机** - 系统设置的默认打印机
5. **第一台活跃打印机** - 第一台可用的打印机

## 文件结构

```
app/FlyCloud/
├── Http/
│   └── Controllers/
│       └── FlyCloudController.php    # API 控制器
├── Models/
│   ├── FlyCloudPrinter.php          # 打印机模型
│   └── WarehousePrinterBinding.php  # 仓库绑定模型
├── Services/
│   └── FlyCloudService.php          # 核心服务类
├── routes/
│   └── api.php                      # 路由配置
└── README.md                        # 说明文档

database/migrations/
├── 2024_01_01_000001_create_flycloud_printers_table.php
└── 2024_01_02_000001_create_warehouse_printer_bindings_table.php
```

## 注意事项

1. **网络连接**：确保服务器能访问飞蛾云API
2. **打印机状态**：定期检查打印机在线状态
3. **API限制**：注意飞蛾云API的调用频率限制
4. **仓库配置**：确保订单商品正确关联仓库
5. **错误处理**：关注日志中的错误信息

## 故障排除

### 常见问题

1. **打印机离线**
   - 检查打印机网络连接
   - 确认打印机已在飞蛾云后台添加

2. **API调用失败**
   - 检查UKEY和用户名配置
   - 确认API签名算法正确

3. **分单打印异常**
   - 检查商品是否正确关联仓库
   - 确认仓库是否绑定了打印机

## 版本历史

- **v1.0.0** - 基础打印功能
- **v2.0.0** - 多台打印机管理
- **v3.0.0** - 仓库分单打印功能

## 参考资料

- [飞蛾云官方文档](https://help.feieyun.com/home/<USER>/zh)
- [飞蛾云开放平台](http://www.feieyun.com/open/index.html) 