<?php

namespace App\Points\Providers;

use Illuminate\Support\ServiceProvider;
use App\Points\Services\PointsService;
use App\Points\Services\PointsOrderService;

class PointsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // 注册积分服务
        $this->app->singleton(PointsService::class, function ($app) {
            return new PointsService();
        });

        // 注册积分订单服务
        $this->app->singleton(PointsOrderService::class, function ($app) {
            return new PointsOrderService($app->make(PointsService::class));
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // 加载路由
        $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');

        // 加载迁移文件
        $this->loadMigrationsFrom(__DIR__ . '/../../database/migrations');

        // 发布配置文件（如果需要）
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/../config/points.php' => config_path('points.php'),
            ], 'points-config');
        }
    }
} 