## 1.0.5（2024-03-20）
- 修复 单选模式下选中样式不生效的bug
## 1.0.4（2024-01-27）
- 修复 修复错别字chagne为change
## 1.0.3（2022-09-16）
- 可以使用 uni-scss 控制主题色
## 1.0.2（2022-06-30）
- 优化 在 uni-forms 中的依赖注入方式
## 1.0.1（2022-02-07）
- 修复 multiple 为 true 时，v-model 的值为 null 报错的 bug
## 1.0.0（2021-11-19）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-data-checkbox](https://uniapp.dcloud.io/component/uniui/uni-data-checkbox)
## 0.2.5（2021-08-23）
- 修复 在uni-forms中 modelValue 中不存在当前字段，当前字段必填写也不参与校验的问题
## 0.2.4（2021-08-17）
- 修复 单选 list 模式下 ，icon 为 left 时，选中图标不显示的问题
## 0.2.3（2021-08-11）
- 修复 在 uni-forms 中重置表单，错误信息无法清除的问题
## 0.2.2（2021-07-30）
- 优化 在uni-forms组件，与label不对齐的问题
## 0.2.1（2021-07-27）
- 修复 单选默认值为0不能选中的Bug
## 0.2.0（2021-07-13）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 0.1.11（2021-07-06）
- 优化 删除无用日志
## 0.1.10（2021-07-05）
- 修复 由 0.1.9 引起的非 nvue 端图标不显示的问题
## 0.1.9（2021-07-05）
- 修复 nvue 黑框样式问题
## 0.1.8（2021-06-28）
- 修复 selectedTextColor 属性不生效的Bug
## 0.1.7（2021-06-02）
- 新增 map 属性，可以方便映射text/value属性
## 0.1.6（2021-05-26）
- 修复 不关联服务空间的情况下组件报错的Bug
## 0.1.5（2021-05-12）
- 新增 组件示例地址
## 0.1.4（2021-04-09）
- 修复 nvue 下无法选中的问题
## 0.1.3（2021-03-22）
- 新增 disabled属性
## 0.1.2（2021-02-24）
- 优化 默认颜色显示
## 0.1.1（2021-02-24）
- 新增 支持nvue
## 0.1.0（2021-02-18）
- “暂无数据”显示居中
