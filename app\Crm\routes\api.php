<?php

use App\Crm\Http\Controllers\UserController;
use App\Crm\Http\Controllers\CrmAgentController;
use App\Crm\Http\Controllers\ClientAssignmentController;
use App\Crm\Http\Controllers\ClientFollowUpController;
use App\Crm\Http\Controllers\MembershipLevelController;
use App\Crm\Http\Controllers\UserAddressController;
use App\Crm\Http\Controllers\FeedbackController;
use App\Crm\Http\Controllers\BehaviorAnalyticsController;
use App\Admin\Http\Controllers\AddressController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| CRM API 路由
|--------------------------------------------------------------------------
|
| CRM模块的API路由定义
|
*/

// 用户管理路由
Route::prefix('users')->group(function () {
    Route::get('/', [UserController::class, 'index']);
    Route::get('/debug', [UserController::class, 'debugIndex']); // 临时调试路由
    Route::post('/', [UserController::class, 'store'])->middleware('employee.role:admin,manager');
    Route::get('/available-employees', [UserController::class, 'availableEmployees']);
    Route::get('/available-deliverers', [UserController::class, 'availableDeliverers']);
    Route::get('/available-crm-agents', [UserController::class, 'availableCrmAgents']);
    Route::post('/sync-wechat', [UserController::class, 'syncWechatUsers'])->middleware('employee.role:admin,manager');
    Route::get('/search', [UserController::class, 'search']);
    Route::get('/export', [UserController::class, 'exportUsers'])->middleware('employee.role:admin,manager');
    Route::get('/by-phone/{phone}', [UserController::class, 'findByPhone']);
    Route::get('/{id}', [UserController::class, 'show']);
    Route::put('/{id}', [UserController::class, 'update'])->middleware('employee.role:admin,manager');
    Route::delete('/{id}', [UserController::class, 'destroy'])->middleware('employee.role:admin');
    Route::put('/{id}/status', [UserController::class, 'updateStatus'])->middleware('employee.role:admin,manager');
    Route::put('/{id}/membership', [UserController::class, 'updateMembership'])->middleware('employee.role:admin,manager');
    Route::put('/{id}/balance', [UserController::class, 'updateBalance'])->middleware('employee.role:admin,manager');
    Route::put('/{id}/points', [UserController::class, 'updatePoints'])->middleware('employee.role:admin,manager');
    Route::put('/{id}/membership-level', [UserController::class, 'updateMembershipLevel'])->middleware('employee.role:admin,manager');
    Route::post('/{id}/refresh-level', [UserController::class, 'refreshMembershipLevel'])->middleware('employee.role:admin,manager');
    Route::get('/{id}/orders', [UserController::class, 'getUserOrders']);
    Route::get('/{id}/statistics', [UserController::class, 'getUserStatistics']);
    Route::post('/{id}/assign-agent', [UserController::class, 'assignAgent'])->middleware('employee.role:admin,manager');
    
    // 用户地址快捷路由 - 匹配前端调用方式
    Route::get('/{id}/addresses', [UserAddressController::class, 'getUserAddresses']);
    Route::post('/{id}/addresses', [AddressController::class, 'store']);
    
    // 用户跟进记录快捷路由 - 匹配前端调用方式  
    Route::get('/{id}/follow-ups', [ClientFollowUpController::class, 'getUserFollowUps']);
});

// 用户地址管理
Route::prefix('user-addresses')->group(function () {
    Route::get('/user/{userId}', [UserAddressController::class, 'index']);
    Route::post('/', [UserAddressController::class, 'store']);
    Route::get('/{id}', [UserAddressController::class, 'show']);
    Route::put('/{id}', [UserAddressController::class, 'update']);
    Route::delete('/{id}', [UserAddressController::class, 'destroy']);
    Route::put('/{id}/default', [UserAddressController::class, 'setDefault']);
});

// 会员等级管理
Route::prefix('membership-levels')->group(function () {
    Route::get('/', [MembershipLevelController::class, 'index']);
    Route::post('/', [MembershipLevelController::class, 'store']);
    Route::get('/{id}', [MembershipLevelController::class, 'show']);
    Route::put('/{id}', [MembershipLevelController::class, 'update']);
    Route::delete('/{id}', [MembershipLevelController::class, 'destroy']);
    Route::put('/{id}/default', [MembershipLevelController::class, 'setAsDefault']);
});

// CRM专员管理
Route::prefix('crm-agents')->group(function () {
    Route::get('/', [CrmAgentController::class, 'index']);
    Route::post('/', [CrmAgentController::class, 'store']);
    Route::get('/{id}', [CrmAgentController::class, 'show']);
    Route::put('/{id}', [CrmAgentController::class, 'update']);
    Route::delete('/{id}', [CrmAgentController::class, 'destroy']);
    Route::get('/{id}/clients', [CrmAgentController::class, 'clients']);
    Route::put('/{id}/status', [CrmAgentController::class, 'updateStatus']);
});

// 客户分配管理
Route::prefix('client-assignments')->group(function () {
    Route::post('/assign', [ClientAssignmentController::class, 'assign']);
    Route::post('/batch-assign', [ClientAssignmentController::class, 'batchAssign']);
    Route::post('/unassign', [ClientAssignmentController::class, 'unassign']);
    Route::delete('/unassign/{agentId}/{userId}', [ClientAssignmentController::class, 'unassignSingle']);
    Route::post('/batch-unassign/{agentId}', [ClientAssignmentController::class, 'batchUnassign']);
    Route::get('/user/{userId}/agents', [ClientAssignmentController::class, 'getClientAgents']);
    Route::get('/agent/{agentId}/clients', [ClientAssignmentController::class, 'getAgentClients']);
    Route::get('/unassigned-clients', [ClientAssignmentController::class, 'getUnassignedClients']);
});

// 客户跟进记录
Route::prefix('follow-ups')->group(function () {
    Route::get('/', [ClientFollowUpController::class, 'index']);
    // 注意：stats 和 debug-stats 路由已移至主路由文件，只需要基本认证
    Route::post('/', [ClientFollowUpController::class, 'store']);
    Route::get('/{id}', [ClientFollowUpController::class, 'show']);
    Route::put('/{id}', [ClientFollowUpController::class, 'update']);
    Route::delete('/{id}', [ClientFollowUpController::class, 'destroy']);
    Route::post('/{id}/next', [ClientFollowUpController::class, 'createNextFollowUp']);
    Route::get('/user/{userId}', [ClientFollowUpController::class, 'getUserFollowUps']);
    Route::get('/agent/{agentId}', [ClientFollowUpController::class, 'getAgentFollowUps']);
});

// 客户反馈管理
Route::prefix('feedback')->group(function () {
    Route::get('/', [FeedbackController::class, 'index']);
    Route::post('/', [FeedbackController::class, 'store']);
    Route::get('/{id}', [FeedbackController::class, 'show']);
    Route::put('/{id}', [FeedbackController::class, 'update']);
    Route::delete('/{id}', [FeedbackController::class, 'destroy']);
    Route::get('/statistics', [FeedbackController::class, 'statistics']);
    Route::put('/{id}/resolve', [FeedbackController::class, 'resolve']);
    Route::put('/{id}/close', [FeedbackController::class, 'close']);
    Route::post('/{id}/reply', [FeedbackController::class, 'reply']);
});

// 行为分析模块
Route::prefix('behavior-analytics')->group(function () {
    // 概览数据
    Route::get('/overview', [BehaviorAnalyticsController::class, 'getBehaviorOverview']);
    
    // 今日统计
    Route::get('/today-stats', [BehaviorAnalyticsController::class, 'getTodayStats']);
    
    // 客户行为详情
    Route::get('/client/{clientId}', [BehaviorAnalyticsController::class, 'getClientBehavior']);
    
    // 各类分析
    Route::get('/purchase', [BehaviorAnalyticsController::class, 'getPurchaseAnalysis']);
    Route::get('/browse', [BehaviorAnalyticsController::class, 'getBrowseAnalysis']);
    Route::get('/time', [BehaviorAnalyticsController::class, 'getTimeAnalysis']);
    Route::get('/geo', [BehaviorAnalyticsController::class, 'getGeoAnalysis']);
    Route::get('/trend', [BehaviorAnalyticsController::class, 'getTrendAnalysis']);
    Route::get('/customer-value', [BehaviorAnalyticsController::class, 'getCustomerValueAnalysis']);
    Route::get('/customer-segment', [BehaviorAnalyticsController::class, 'getCustomerSegmentAnalysis']);
    Route::get('/product-preference', [BehaviorAnalyticsController::class, 'getProductPreferenceAnalysis']);
    Route::get('/churn-warning', [BehaviorAnalyticsController::class, 'getChurnWarning']);
    
    // 对比分析和生命周期分析
    Route::get('/comparison', [BehaviorAnalyticsController::class, 'getComparisonAnalysis']);
    Route::get('/product-lifecycle', [BehaviorAnalyticsController::class, 'getProductLifecycleAnalysis']);
    
    // 客户活跃度分析
    Route::get('/customer-activity', [BehaviorAnalyticsController::class, 'getCustomerActivityAnalysis']);
    
    // 配置和导出
    Route::get('/config', [BehaviorAnalyticsController::class, 'getAnalysisConfig']);
    Route::post('/export', [BehaviorAnalyticsController::class, 'exportAnalysisData']);
}); 