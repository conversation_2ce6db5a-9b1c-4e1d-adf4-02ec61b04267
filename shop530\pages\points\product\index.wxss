/* pages/points/product/index.wxss - 积分商品详情页样式 */

/* 主容器 */
.product-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* ===== 商品图片轮播 ===== */
.product-swiper {
  width: 100%;
  height: 600rpx;
  background: #fff;
}

.product-image {
  width: 100%;
  height: 100%;
}

/* ===== 商品基本信息 ===== */
.product-info {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-header {
  margin-bottom: 20rpx;
}

.product-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.5;
  display: block;
  margin-bottom: 16rpx;
}

.product-tags {
  display: flex;
  gap: 12rpx;
}

.tag {
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
}

.tag.category {
  background: #2196F3;
}

.tag.hot {
  background: #FF5722;
}

.tag.new {
  background: #4CAF50;
}

.product-price {
  display: flex;
  align-items: baseline;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.points-price {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.points-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #FF6B35;
}

.points-unit {
  font-size: 24rpx;
  color: #FF6B35;
}

.cash-price {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.cash-label {
  font-size: 24rpx;
  color: #666;
}

.cash-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.original-price {
  margin-left: auto;
}

.original-value {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.product-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #666;
}

.stat-item.stock-out {
  color: #FF5722;
}

/* ===== 用户积分信息 ===== */
.user-points-info {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.points-balance {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.balance-label {
  font-size: 24rpx;
  color: #666;
}

.balance-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #4CAF50;
}

.points-sufficient {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.points-sufficient.sufficient {
  background: #E8F5E8;
  color: #4CAF50;
}

.points-sufficient.insufficient {
  background: #FFEBEE;
  color: #F44336;
}

/* ===== 商品规格选择 ===== */
.product-specs {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.specs-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.specs-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.spec-item {
  border: 2rpx solid #E0E0E0;
  border-radius: 12rpx;
  padding: 20rpx;
  min-width: 160rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.spec-item.selected {
  border-color: #4CAF50;
  background: #E8F5E8;
}

.spec-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.spec-price {
  font-size: 24rpx;
  color: #FF6B35;
  font-weight: 600;
}

/* ===== 数量选择 ===== */
.quantity-selector {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0;
  border: 2rpx solid #E0E0E0;
  border-radius: 8rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background: #fff;
  border: none;
  font-size: 32rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  border-radius: 0;
}

.quantity-btn.disabled {
  color: #ccc;
}

.quantity-btn::after {
  border: none;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  border: none;
  border-left: 2rpx solid #E0E0E0;
  border-right: 2rpx solid #E0E0E0;
}

/* ===== 商品详情 ===== */
.product-detail {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.detail-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.detail-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.detail-images {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-image {
  width: 100%;
  border-radius: 12rpx;
}

/* ===== 兑换须知 ===== */
.exchange-notice {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.notice-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.notice-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.notice-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* ===== 相关推荐 ===== */
.related-products {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.related-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.related-scroll {
  white-space: nowrap;
}

.related-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  vertical-align: top;
}

.related-image {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
}

.related-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.related-name {
  font-size: 24rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-points {
  font-size: 22rpx;
  color: #FF6B35;
  font-weight: 600;
}

/* ===== 加载状态 ===== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E0E0E0;
  border-top: 4rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/* ===== 底部操作栏 ===== */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #E0E0E0;
  display: flex;
  align-items: center;
  gap: 30rpx;
  z-index: 100;
}

.action-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.total-points,
.total-cash {
  display: flex;
  align-items: baseline;
  gap: 12rpx;
}

.total-label {
  font-size: 24rpx;
  color: #666;
}

.total-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.exchange-btn {
  background: #4CAF50;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
  min-width: 200rpx;
}

.exchange-btn.disabled {
  background: #BDBDBD;
  color: #fff;
}

.exchange-btn::after {
  border: none;
} 