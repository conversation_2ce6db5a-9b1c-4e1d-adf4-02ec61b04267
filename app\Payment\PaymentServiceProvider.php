<?php

namespace App\Payment;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;

class PaymentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // 注册Payment模块的服务
        $this->app->singleton(\App\Payment\Services\PaymentOfferService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // 加载路由
        $this->loadRoutes();
    }

    /**
     * 加载路由
     */
    protected function loadRoutes(): void
    {
        // API路由
        Route::middleware(['api'])
            ->prefix('api')
            ->group(__DIR__ . '/routes/api.php');
    }
} 