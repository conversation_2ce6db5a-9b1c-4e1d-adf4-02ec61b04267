<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('merchant_name')->nullable()->comment('商户名称');
            $table->decimal('balance', 10, 2)->default(0)->comment('用户余额');
            $table->integer('points')->default(0)->comment('用户积分');
            $table->timestamp('joined_at')->nullable()->comment('加入时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('merchant_name');
            $table->dropColumn('balance');
            $table->dropColumn('points');
            $table->dropColumn('joined_at');
        });
    }
};
