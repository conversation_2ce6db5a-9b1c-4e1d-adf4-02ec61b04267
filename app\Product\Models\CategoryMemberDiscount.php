<?php

namespace App\Product\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Product\Models\Category;
use App\Crm\Models\MembershipLevel;

class CategoryMemberDiscount extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'membership_level_id',
        'discount_type',
        'discount_value',
        'max_discount',
        'status',
        'start_time',
        'end_time',
        'description',
    ];

    protected $casts = [
        'discount_value' => 'decimal:2',
        'max_discount' => 'decimal:2',
        'status' => 'boolean',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * 关联会员等级
     */
    public function membershipLevel()
    {
        return $this->belongsTo(MembershipLevel::class);
    }

    /**
     * 检查优惠规则是否有效
     */
    public function isValid()
    {
        if (!$this->status) {
            return false;
        }

        $now = now();

        if ($this->start_time && $this->start_time->gt($now)) {
            return false;
        }

        if ($this->end_time && $this->end_time->lt($now)) {
            return false;
        }

        return true;
    }
} 