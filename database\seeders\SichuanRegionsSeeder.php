<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SichuanRegionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清除已有数据
        // DB::table('regions')->truncate();
        
        $now = Carbon::now()->toDateTimeString();
        
        // 添加四川省
        $sichuanId = DB::table('regions')->insertGetId([
            'name' => '四川省',
            'code' => 'sichuan',
            'parent_id' => 0,
            'level' => 0,
            'status' => true,
            'sort' => 0,
            'full_name' => '四川省',
            'latitude' => 30.651600,
            'longitude' => 104.075931,
            'created_at' => $now,
            'updated_at' => $now,
        ]);
        
        // 添加城市
        $cities = [
            [
                'name' => '成都市',
                'code' => 'chengdu',
                'latitude' => 30.572816,
                'longitude' => 104.066801,
                'districts' => [
                    ['name' => '锦江区', 'code' => 'jinjiang', 'latitude' => 30.657689, 'longitude' => 104.080989],
                    ['name' => '青羊区', 'code' => 'qingyang', 'latitude' => 30.674406, 'longitude' => 104.062499],
                    ['name' => '金牛区', 'code' => 'jinniu', 'latitude' => 30.691359, 'longitude' => 104.052236],
                    ['name' => '武侯区', 'code' => 'wuhou', 'latitude' => 30.641893, 'longitude' => 104.04339],
                    ['name' => '成华区', 'code' => 'chenghua', 'latitude' => 30.659966, 'longitude' => 104.101255],
                    ['name' => '龙泉驿区', 'code' => 'longquanyi', 'latitude' => 30.556507, 'longitude' => 104.274632],
                    ['name' => '青白江区', 'code' => 'qingbaijiang', 'latitude' => 30.878681, 'longitude' => 104.250839],
                    ['name' => '新都区', 'code' => 'xindu', 'latitude' => 30.823499, 'longitude' => 104.158705],
                    ['name' => '温江区', 'code' => 'wenjiang', 'latitude' => 30.682203, 'longitude' => 103.856646],
                    ['name' => '双流区', 'code' => 'shuangliu', 'latitude' => 30.574449, 'longitude' => 103.923566],
                    ['name' => '郫都区', 'code' => 'pidu', 'latitude' => 30.808752, 'longitude' => 103.900486],
                    ['name' => '金堂县', 'code' => 'jintang', 'latitude' => 30.861979, 'longitude' => 104.412005],
                    ['name' => '大邑县', 'code' => 'dayi', 'latitude' => 30.587437, 'longitude' => 103.511865],
                    ['name' => '蒲江县', 'code' => 'pujiang', 'latitude' => 30.196789, 'longitude' => 103.506498],
                    ['name' => '新津区', 'code' => 'xinjin', 'latitude' => 30.410222, 'longitude' => 103.811345],
                    ['name' => '都江堰市', 'code' => 'dujiangyan', 'latitude' => 30.988435, 'longitude' => 103.646912],
                    ['name' => '彭州市', 'code' => 'pengzhou', 'latitude' => 30.990108, 'longitude' => 103.958014],
                    ['name' => '邛崃市', 'code' => 'qionglai', 'latitude' => 30.410354, 'longitude' => 103.464156],
                    ['name' => '崇州市', 'code' => 'chongzhou', 'latitude' => 30.630212, 'longitude' => 103.673001],
                    ['name' => '简阳市', 'code' => 'jianyang', 'latitude' => 30.410667, 'longitude' => 104.547357],
                ]
            ],
            [
                'name' => '自贡市',
                'code' => 'zigong',
                'latitude' => 29.339300,
                'longitude' => 104.778442,
                'districts' => [
                    ['name' => '自流井区', 'code' => 'ziliujing', 'latitude' => 29.337361, 'longitude' => 104.777207],
                    ['name' => '贡井区', 'code' => 'gongjing', 'latitude' => 29.345675, 'longitude' => 104.715293],
                    ['name' => '大安区', 'code' => 'daan', 'latitude' => 29.363811, 'longitude' => 104.773506],
                    ['name' => '沿滩区', 'code' => 'yantan', 'latitude' => 29.272586, 'longitude' => 104.873981],
                    ['name' => '荣县', 'code' => 'rongxian', 'latitude' => 29.445101, 'longitude' => 104.417488],
                    ['name' => '富顺县', 'code' => 'fushun', 'latitude' => 29.181282, 'longitude' => 104.975193],
                ]
            ],
            [
                'name' => '攀枝花市',
                'code' => 'panzhihua',
                'latitude' => 26.582347,
                'longitude' => 101.718637,
                'districts' => [
                    ['name' => '东区', 'code' => 'dongqu', 'latitude' => 26.546491, 'longitude' => 101.704134],
                    ['name' => '西区', 'code' => 'xiqu', 'latitude' => 26.597724, 'longitude' => 101.630619],
                    ['name' => '仁和区', 'code' => 'renhe', 'latitude' => 26.497765, 'longitude' => 101.738528],
                    ['name' => '米易县', 'code' => 'miyi', 'latitude' => 26.890669, 'longitude' => 102.110339],
                    ['name' => '盐边县', 'code' => 'yanbian', 'latitude' => 26.682712, 'longitude' => 101.855071],
                ]
            ],
            [
                'name' => '泸州市',
                'code' => 'luzhou',
                'latitude' => 28.871810,
                'longitude' => 105.442258,
                'districts' => [
                    ['name' => '江阳区', 'code' => 'jiangyang', 'latitude' => 28.878818, 'longitude' => 105.434904],
                    ['name' => '纳溪区', 'code' => 'naxi', 'latitude' => 28.773423, 'longitude' => 105.371156],
                    ['name' => '龙马潭区', 'code' => 'longmatan', 'latitude' => 28.913257, 'longitude' => 105.437757],
                    ['name' => '泸县', 'code' => 'luxian', 'latitude' => 29.151466, 'longitude' => 105.381898],
                    ['name' => '合江县', 'code' => 'hejiang', 'latitude' => 28.811203, 'longitude' => 105.831001],
                    ['name' => '叙永县', 'code' => 'xuyong', 'latitude' => 28.155671, 'longitude' => 105.444765],
                    ['name' => '古蔺县', 'code' => 'gulin', 'latitude' => 28.038801, 'longitude' => 105.812796],
                ]
            ],
            [
                'name' => '德阳市',
                'code' => 'deyang',
                'latitude' => 31.126856,
                'longitude' => 104.398651,
                'districts' => [
                    ['name' => '旌阳区', 'code' => 'jingyang', 'latitude' => 31.142534, 'longitude' => 104.416941],
                    ['name' => '罗江区', 'code' => 'luojiang', 'latitude' => 31.317025, 'longitude' => 104.510676],
                    ['name' => '中江县', 'code' => 'zhongjiang', 'latitude' => 31.033051, 'longitude' => 104.678751],
                    ['name' => '广汉市', 'code' => 'guanghan', 'latitude' => 30.976165, 'longitude' => 104.282431],
                    ['name' => '什邡市', 'code' => 'shifang', 'latitude' => 31.126881, 'longitude' => 104.167501],
                    ['name' => '绵竹市', 'code' => 'mianzhu', 'latitude' => 31.338077, 'longitude' => 104.221101],
                ]
            ],
            [
                'name' => '绵阳市',
                'code' => 'mianyang',
                'latitude' => 31.467459,
                'longitude' => 104.679114,
                'districts' => [
                    ['name' => '涪城区', 'code' => 'fucheng', 'latitude' => 31.455101, 'longitude' => 104.756944],
                    ['name' => '游仙区', 'code' => 'youxian', 'latitude' => 31.473788, 'longitude' => 104.766392],
                    ['name' => '安州区', 'code' => 'anzhou', 'latitude' => 31.534319, 'longitude' => 104.567187],
                    ['name' => '三台县', 'code' => 'santai', 'latitude' => 31.095448, 'longitude' => 105.094586],
                    ['name' => '盐亭县', 'code' => 'yanting', 'latitude' => 31.208362, 'longitude' => 105.389453],
                    ['name' => '梓潼县', 'code' => 'zitong', 'latitude' => 31.642677, 'longitude' => 105.170783],
                    ['name' => '北川羌族自治县', 'code' => 'beichuan', 'latitude' => 31.617202, 'longitude' => 104.467745],
                    ['name' => '平武县', 'code' => 'pingwu', 'latitude' => 32.408967, 'longitude' => 104.529685],
                    ['name' => '江油市', 'code' => 'jiangyou', 'latitude' => 31.778022, 'longitude' => 104.745743],
                ]
            ],
            // 这里只列举了部分城市，您可以继续添加其他城市
        ];
        
        // 遍历添加城市和区县
        foreach ($cities as $city) {
            $cityId = DB::table('regions')->insertGetId([
                'name' => $city['name'],
                'code' => $city['code'],
                'parent_id' => $sichuanId,
                'level' => 1,
                'status' => true,
                'sort' => 0,
                'full_name' => '四川省 > ' . $city['name'],
                'latitude' => $city['latitude'],
                'longitude' => $city['longitude'],
                'created_at' => $now,
                'updated_at' => $now,
            ]);
            
            // 添加区县
            foreach ($city['districts'] as $district) {
                DB::table('regions')->insert([
                    'name' => $district['name'],
                    'code' => $district['code'],
                    'parent_id' => $cityId,
                    'level' => 2,
                    'status' => true,
                    'sort' => 0,
                    'full_name' => '四川省 > ' . $city['name'] . ' > ' . $district['name'],
                    'latitude' => $district['latitude'],
                    'longitude' => $district['longitude'],
                    'created_at' => $now,
                    'updated_at' => $now,
                ]);
            }
        }
        
        $this->command->info('四川省行政区划数据已成功导入！');
    }
}
