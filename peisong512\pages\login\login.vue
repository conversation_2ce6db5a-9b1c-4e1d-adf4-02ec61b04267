<template>
	<view class="login-container">
		<view class="logo">
			<image src="/static/logo.png" mode="aspectFit"></image>
		</view>
		<view class="login-form">
			<uni-forms ref="loginForm" :modelValue="formData" :rules="rules">
				<uni-forms-item name="phone" required>
					<uni-easyinput v-model="formData.phone" placeholder="请输入手机号" />
				</uni-forms-item>
				<uni-forms-item name="password" required>
					<uni-easyinput v-model="formData.password" type="password" placeholder="请输入密码" />
				</uni-forms-item>
				<view class="form-options">
					<label class="remember-box" @click="toggleRemember">
						<checkbox :checked="rememberLogin" color="#007AFF" style="transform:scale(0.8)"/>
						<text>记住账号</text>
					</label>
					<label class="auto-login" @click="toggleAutoLogin">
						<checkbox :checked="autoLogin" color="#007AFF" style="transform:scale(0.8)"/>
						<text>自动登录</text>
					</label>
				</view>
			</uni-forms>
			<button class="login-btn" type="primary" @click="doLogin">登录</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			formData: {
				phone: '',
				password: ''
			},
			rules: {
				phone: {
					rules: [{
						required: true,
						errorMessage: '请输入手机号'
					}, {
						pattern: /^1[3-9]\d{9}$/,
						errorMessage: '请输入正确的手机号'
					}]
				},
				password: {
					rules: [{
						required: true,
						errorMessage: '请输入密码'
					}]
				}
			},
			rememberLogin: true, // 默认记住账号
			autoLogin: false     // 默认不自动登录
		}
	},
	onLoad() {
		console.log('登录页面加载');
		
		// 检查是否是从首页跳转回来的
		const pages = getCurrentPages();
		const currentPage = pages[pages.length - 1];
		if (currentPage && currentPage.route === 'pages/login/login') {
			// 获取页面参数
			const eventChannel = this.getOpenerEventChannel();
			if (eventChannel && eventChannel.on) {
				eventChannel.on('loginError', (data) => {
					console.log('接收到首页传来的错误信息:', data);
					if (data && data.error) {
						uni.showToast({
							title: data.error,
							icon: 'none',
							duration: 3000
						});
					}
				});
			}
		}
		
		this.loadSavedLoginInfo();
	},
	onShow() {
		// 延迟检查自动登录，确保数据已加载
		setTimeout(() => {
			this.checkAutoLogin();
		}, 100);
	},
	methods: {
		// 切换记住账号
		toggleRemember() {
			this.rememberLogin = !this.rememberLogin;
			console.log('记住账号:', this.rememberLogin);
			
			// 如果取消记住账号，同时取消自动登录
			if (!this.rememberLogin) {
				this.autoLogin = false;
			}
		},
		
		// 切换自动登录
		toggleAutoLogin() {
			this.autoLogin = !this.autoLogin;
			console.log('自动登录:', this.autoLogin);
			
			// 如果开启自动登录，必须同时开启记住账号
			if (this.autoLogin) {
				this.rememberLogin = true;
			}
		},
		
		// 加载保存的登录信息
		loadSavedLoginInfo() {
			try {
				const savedConfig = uni.getStorageSync('delivererLoginConfig');
				if (savedConfig) {
					const config = JSON.parse(savedConfig);
					console.log('读取到已保存的登录配置:', config);
					
					// 恢复设置
					this.rememberLogin = config.rememberLogin !== false;
					this.autoLogin = config.autoLogin === true;
					
					// 恢复账号
					if (this.rememberLogin && config.phone) {
						this.formData.phone = config.phone;
						
						// 如果设置了自动登录，也恢复密码
						if (this.autoLogin && config.password) {
							this.formData.password = config.password;
						}
					}
					
					// 加载完成后检查自动登录
					this.$nextTick(() => {
						this.checkAutoLogin();
					});
				} else {
					console.log('未找到已保存的登录配置，使用默认值');
				}
			} catch (e) {
				console.error('读取保存的登录信息出错:', e);
			}
		},
		
		// 检查并执行自动登录
		checkAutoLogin() {
			// 检查是否已经登录
			const token = uni.getStorageSync('token');
			const isLoggedIn = uni.getStorageSync('isLoggedIn');
			
			if (token && isLoggedIn === 'true') {
				console.log('用户已登录，直接跳转到首页');
				uni.reLaunch({
					url: '/pages/index/index'
				});
				return;
			}
			
			// 如果配置了自动登录且有账号密码，执行登录
			if (this.autoLogin && this.formData.phone && this.formData.password) {
				console.log('检测到自动登录设置，尝试自动登录');
				// 延迟一点时间，让用户看到界面
				setTimeout(() => {
					this.doLogin();
				}, 500);
			} else if (this.autoLogin && this.formData.phone && !this.formData.password) {
				// 如果开启了自动登录但没有密码，提示用户
				console.log('自动登录已开启但密码已清除，需要重新输入密码');
				uni.showToast({
					title: '请输入密码以完成自动登录',
					icon: 'none',
					duration: 2000
				});
			}
		},
		
		// 保存登录信息
		saveLoginInfo() {
			const config = {
				rememberLogin: this.rememberLogin,
				autoLogin: this.autoLogin,
				phone: this.rememberLogin ? this.formData.phone : '',
				password: this.autoLogin ? this.formData.password : ''
			};
			
			console.log('保存登录配置:', config);
			uni.setStorageSync('delivererLoginConfig', JSON.stringify(config));
		},
		
		// 执行登录
		async doLogin() {
			try {
				// 表单验证
				const valid = await this.$refs.loginForm.validate();
				if (!valid) return;
				
				// 显示加载中
				uni.showLoading({
					title: '登录中...',
					mask: true
				});
				
				// 准备登录数据
				const loginData = {
					phone: this.formData.phone.trim(),
					password: this.formData.password
				};
				
				// 登录API URL
				const apiUrl = getApp().globalData.BASE_API + '/api/deliverers/login';
				console.log('发送登录请求到:', apiUrl);
				
				// 发送登录请求
				try {
					const result = await uni.request({
						url: apiUrl,
						method: 'POST',
						data: loginData,
						header: {
							'Content-Type': 'application/json',
							'Accept': 'application/json',
							'X-Requested-With': 'XMLHttpRequest'
						}
					});
					
					// 隐藏加载提示
					uni.hideLoading();
					
					console.log('登录响应:', result);
					
					// 检查登录是否成功
					if (result.data && result.data.code === 200 && result.data.data) {
						// 保存令牌和用户信息
						const loginResult = result.data.data;
						
						console.log('登录成功，保存数据:', loginResult);
						
						// 保存token
						if (loginResult.token) {
							uni.setStorageSync('token', loginResult.token);
							getApp().globalData.token = loginResult.token;
							getApp().globalData.isLoggedIn = true;
							getApp().globalData.requestHeader = {
								'Authorization': 'Bearer ' + loginResult.token,
								'Content-Type': 'application/json',
								'Accept': 'application/json'
							};
						}
						
						// 保存用户信息
						if (loginResult.user) {
							uni.setStorageSync('userInfo', JSON.stringify(loginResult.user));
						}
						
						// 保存配送员信息
						if (loginResult.deliverer) {
							uni.setStorageSync('delivererInfo', JSON.stringify(loginResult.deliverer));
							getApp().globalData.delivererId = loginResult.deliverer.id;
						}
						
						// 保存员工信息
						if (loginResult.employee) {
							uni.setStorageSync('employeeInfo', JSON.stringify(loginResult.employee));
						}
						
						// 设置登录状态
						uni.setStorageSync('isLoggedIn', 'true');
						
						// 保存登录配置
						this.saveLoginInfo();
						
						// 显示成功提示
						uni.showToast({
							title: '登录成功',
							icon: 'success',
							duration: 1500
						});
						
						// 延迟跳转到首页
						setTimeout(() => {
							uni.reLaunch({
								url: '/pages/index/index'
							});
						}, 1500);
						
					} else {
						// 登录失败
						const errorMessage = result.data?.message || '登录失败，请检查账号密码';
						console.error('登录失败:', errorMessage);
						
						uni.showToast({
							title: errorMessage,
							icon: 'none',
							duration: 3000
						});
					}
				} catch (requestError) {
					// 隐藏加载提示
					uni.hideLoading();
					
					console.error('登录请求异常:', requestError);
					
					// 处理网络错误
					let errorMessage = '网络连接失败，请检查网络设置';
					if (requestError.errMsg) {
						if (requestError.errMsg.includes('timeout')) {
							errorMessage = '请求超时，请稍后重试';
						} else if (requestError.errMsg.includes('fail')) {
							errorMessage = '网络连接失败，请检查网络设置';
						}
					}
					
					uni.showToast({
						title: errorMessage,
						icon: 'none',
						duration: 3000
					});
				}
			} catch (error) {
				// 隐藏加载提示
				uni.hideLoading();
				
				console.error('登录过程异常:', error);
				uni.showToast({
					title: '登录过程出现异常，请稍后重试',
					icon: 'none',
					duration: 3000
				});
			}
		}
	}
}
</script>

<style lang="scss">
.login-container {
	padding: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 100vh;
	box-sizing: border-box;
	
	.logo {
		margin-bottom: 80rpx;
		image {
			width: 200rpx;
			height: 200rpx;
		}
	}
	
	.login-form {
		width: 100%;
		
		.form-options {
			display: flex;
			justify-content: space-between;
			margin: 20rpx 0 40rpx;
			padding: 0 20rpx;
			
			.remember-box, .auto-login {
				display: flex;
				align-items: center;
				font-size: 28rpx;
				color: #666;
				
				text {
					margin-left: 8rpx;
				}
			}
		}
	}
	
	.login-btn {
		margin-top: 60rpx;
		width: 100%;
		background-color: #007AFF;
	}
}
</style> 