<?php

namespace App\Points\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Points\Models\PointsProduct;
use App\Points\Services\PointsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PointsProductController extends Controller
{
    protected PointsService $pointsService;

    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * 获取积分商品列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = PointsProduct::available()->with(['membershipLevel']);

        // 分类筛选
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        // 兑换类型筛选
        if ($request->filled('exchange_type')) {
            $query->byExchangeType($request->exchange_type);
        }

        // 会员等级筛选
        if ($request->filled('membership_level_id')) {
            $query->where('membership_level_id', $request->membership_level_id);
        }

        // 搜索
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // 排序
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortOrder = $request->get('sort_order', 'asc');
        
        if (in_array($sortBy, ['sort_order', 'points_price', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // 分页
        $perPage = min($request->get('per_page', 20), 100);
        $products = $query->paginate($perPage);

        // 如果用户已登录，检查每个商品的兑换状态
        $user = auth()->user();
        if ($user) {
            $products->getCollection()->transform(function ($product) use ($user) {
                $product->can_exchange = $product->canUserExchange($user);
                $product->user_points_sufficient = $user->member_points >= $product->points_price;
                return $product;
            });
        }

        return response()->json([
            'success' => true,
            'data' => $products->items(),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
            ]
        ]);
    }

    /**
     * 获取积分商品详情
     */
    public function show(Request $request, int $id): JsonResponse
    {
        $product = PointsProduct::with(['product', 'membershipLevel'])->find($id);

        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => '商品不存在'
            ], 404);
        }

        $user = auth()->user();
        $productData = $product->toArray();

        if ($user) {
            $productData['can_exchange'] = $product->canUserExchange($user);
            $productData['user_points_sufficient'] = $user->member_points >= $product->points_price;
            $productData['user_points_balance'] = $user->member_points;
            
            // 检查每日限兑情况
            if ($product->daily_limit) {
                $todayExchanged = $product->orderItems()
                    ->whereHas('order', function ($query) use ($user) {
                        $query->where('user_id', $user->id)
                              ->whereDate('created_at', today())
                              ->where('status', '!=', 'cancelled');
                    })->sum('quantity');
                
                $productData['daily_exchanged'] = $todayExchanged;
                $productData['daily_remaining'] = max(0, $product->daily_limit - $todayExchanged);
            }
        }

        return response()->json([
            'success' => true,
            'data' => $productData
        ]);
    }

    /**
     * 获取积分商品分类统计
     */
    public function categories(): JsonResponse
    {
        $categories = PointsProduct::available()
            ->selectRaw('category, COUNT(*) as count')
            ->groupBy('category')
            ->get()
            ->map(function ($item) {
                return [
                    'category' => $item->category,
                    'name' => match($item->category) {
                        'physical' => '实物商品',
                        'virtual' => '虚拟商品',
                        'coupon' => '优惠券',
                        default => '其他'
                    },
                    'count' => $item->count
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * 检查商品兑换资格
     */
    public function checkExchangeEligibility(Request $request, int $id): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        $product = PointsProduct::find($id);
        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => '商品不存在'
            ], 404);
        }

        $quantity = $request->get('quantity', 1);
        $canExchange = true;
        $reasons = [];

        // 检查商品可用性
        if (!$product->isAvailable()) {
            $canExchange = false;
            $reasons[] = '商品暂不可兑换';
        }

        // 检查会员等级
        if ($product->membership_level_id && $user->membership_level_id !== $product->membership_level_id) {
            $canExchange = false;
            $reasons[] = '会员等级不符合要求';
        }

        // 检查积分
        $requiredPoints = $product->points_price * $quantity;
        if ($user->member_points < $requiredPoints) {
            $canExchange = false;
            $reasons[] = '积分不足';
        }

        // 检查库存
        if ($product->category === PointsProduct::CATEGORY_PHYSICAL && 
            $product->stock_quantity < $quantity) {
            $canExchange = false;
            $reasons[] = '库存不足';
        }

        // 检查每日限兑
        if ($product->daily_limit) {
            $todayExchanged = $product->orderItems()
                ->whereHas('order', function ($query) use ($user) {
                    $query->where('user_id', $user->id)
                          ->whereDate('created_at', today())
                          ->where('status', '!=', 'cancelled');
                })->sum('quantity');
            
            if ($todayExchanged + $quantity > $product->daily_limit) {
                $canExchange = false;
                $reasons[] = '超出每日限兑数量';
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'can_exchange' => $canExchange,
                'reasons' => $reasons,
                'required_points' => $requiredPoints,
                'user_points' => $user->member_points,
                'points_sufficient' => $user->member_points >= $requiredPoints,
            ]
        ]);
    }

    /**
     * 获取热门商品
     */
    public function popular(Request $request): JsonResponse
    {
        $limit = min($request->get('limit', 10), 50);
        
        $products = PointsProduct::available()
            ->with(['membershipLevel'])
            ->orderBy('exchanged_count', 'desc')
            ->orderBy('sort_order', 'asc')
            ->limit($limit)
            ->get();

        $user = auth()->user();
        if ($user) {
            $products->transform(function ($product) use ($user) {
                $product->can_exchange = $product->canUserExchange($user);
                return $product;
            });
        }

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }

    /**
     * 获取推荐商品
     */
    public function recommended(Request $request): JsonResponse
    {
        $user = auth()->user();
        $limit = min($request->get('limit', 10), 50);
        
        $query = PointsProduct::available()->with(['membershipLevel']);

        // 如果用户已登录，根据会员等级推荐
        if ($user && $user->membership_level_id) {
            $query->where(function ($q) use ($user) {
                $q->whereNull('membership_level_id')
                  ->orWhere('membership_level_id', $user->membership_level_id);
            });
        }

        $products = $query->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        if ($user) {
            $products->transform(function ($product) use ($user) {
                $product->can_exchange = $product->canUserExchange($user);
                return $product;
            });
        }

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }
} 