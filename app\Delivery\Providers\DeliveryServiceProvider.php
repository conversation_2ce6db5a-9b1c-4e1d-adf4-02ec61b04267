<?php

namespace App\Delivery\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;

class DeliveryServiceProvider extends ServiceProvider
{
    /**
     * 注册应用服务
     *
     * @return void
     */
    public function register()
    {
        // 注册服务
        $this->app->singleton('delivery.service', function ($app) {
            return new \App\Delivery\Services\DeliveryService();
        });
        
        $this->app->singleton('deliverer.service', function ($app) {
            return new \App\Delivery\Services\DelivererService();
        });
        
        $this->app->singleton('delivery.route.service', function ($app) {
            return new \App\Delivery\Services\DeliveryRouteService();
        });
        
        $this->app->singleton('delivery.statistics.service', function ($app) {
            return new \App\Delivery\Services\DeliveryStatisticsService();
        });
    }
    
    /**
     * 引导应用服务
     *
     * @return void
     */
    public function boot()
    {
        // 加载API路由 - 使用正确的前缀
        Route::group([
            'prefix' => 'api',
            'middleware' => ['api']
        ], function () {
            $this->loadRoutesFrom(__DIR__.'/../routes/api.php');
        });
    }
    
    /**
     * 加载路由文件
     *
     * @return void
     */
    protected function loadRoutes()
    {
        // API路由
        $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
        
        // Web路由
        if (file_exists(__DIR__ . '/../routes/web.php')) {
            $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');
        }
    }
} 