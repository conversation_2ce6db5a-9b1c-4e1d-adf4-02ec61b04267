<?php

namespace App\Search\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Search\Services\SearchService;

class SearchController extends Controller
{
    /**
     * @var SearchService
     */
    protected $searchService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->searchService = app('search.service');
    }

    /**
     * 搜索商品
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchProducts(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'keyword' => 'required|string|max:100',
            'page' => 'integer|min:1',
            'pageSize' => 'integer|min:1|max:50',
            'sort' => 'string|in:default,sales,price',
            'order' => 'string|in:asc,desc',
            'categoryId' => 'integer|min:1|nullable',
            'minPrice' => 'numeric|nullable',
            'maxPrice' => 'numeric|nullable',
            'tags' => 'array|nullable',
            'platform' => 'string|nullable',
            'includeOutOfStock' => 'boolean|nullable', // 是否包含无库存商品
            'clearCache' => 'boolean|nullable', // 是否清除缓存
        ]);

        // 获取请求参数
        $keyword = $request->input('keyword');
        $options = [
            'page' => (int)$request->input('page', 1),
            'pageSize' => (int)$request->input('pageSize', 10),
            'sort' => $request->input('sort', 'default'),
            'order' => $request->input('order', 'desc'),
            'platform' => $request->input('platform', $this->detectPlatform()),
            'includeOutOfStock' => $request->boolean('includeOutOfStock', true),
            'clearCache' => $request->boolean('clearCache', false),
        ];
        
        // 只添加有效的参数
        if ($request->has('categoryId') && $request->input('categoryId') !== null && $request->input('categoryId') !== 'null') {
            $options['categoryId'] = (int)$request->input('categoryId');
        }
        
        if ($request->has('minPrice') && $request->input('minPrice') !== null && $request->input('minPrice') !== 'null') {
            $options['minPrice'] = (float)$request->input('minPrice');
        }
        
        if ($request->has('maxPrice') && $request->input('maxPrice') !== null && $request->input('maxPrice') !== 'null') {
            $options['maxPrice'] = (float)$request->input('maxPrice');
        }
        
        if ($request->has('tags') && is_array($request->input('tags')) && !empty($request->input('tags'))) {
            $options['tags'] = $request->input('tags');
        }

        // 调用服务执行搜索
        $result = $this->searchService->searchProducts($keyword, $options);

        // 返回结果
        return response()->json([
            'code' => 200,
            'message' => '搜索成功',
            'data' => $result
        ]);
    }

    /**
     * 获取热门搜索关键词
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHotKeywords(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'limit' => 'integer|min:1|max:20',
        ]);

        // 获取请求参数
        $limit = $request->input('limit', 10);

        // 调用服务获取热门搜索词
        $result = $this->searchService->getHotKeywords($limit);

        // 返回结果
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $result
        ]);
    }

    /**
     * 获取搜索建议
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSuggestions(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'keyword' => 'required|string|max:100',
            'limit' => 'integer|min:1|max:20',
        ]);

        // 获取请求参数
        $keyword = $request->input('keyword');
        $limit = $request->input('limit', 10);

        // 调用服务获取搜索建议
        $result = $this->searchService->getSuggestions($keyword, $limit);

        // 返回结果
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $result
        ]);
    }

    /**
     * 获取搜索统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSearchStats(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'days' => 'integer|min:1|max:365',
            'limit' => 'integer|min:1|max:100',
        ]);

        // 获取请求参数
        $options = [
            'days' => $request->input('days', 30),
            'limit' => $request->input('limit', 10),
        ];

        // 调用服务获取搜索统计数据
        $result = $this->searchService->getSearchStats($options);

        // 返回结果
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $result
        ]);
    }

    /**
     * 设置热门搜索词
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function setHotKeywords(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'keywords' => 'required|array',
            'keywords.*.keyword' => 'required|string|max:100',
            'keywords.*.highlight' => 'boolean',
            'keywords.*.weight' => 'integer|min:0',
        ]);

        // 获取请求参数
        $keywords = $request->input('keywords');

        // TODO: 实现设置热门搜索词的功能

        // 返回结果
        return response()->json([
            'code' => 200,
            'message' => '设置成功',
            'data' => []
        ]);
    }

    /**
     * 检测请求平台
     *
     * @return string
     */
    protected function detectPlatform()
    {
        $userAgent = request()->userAgent();
        
        if (strpos($userAgent, 'MicroMessenger') !== false) {
            return 'wechat';
        } elseif (strpos($userAgent, 'miniProgram') !== false) {
            return 'miniprogram';
        } elseif (strpos($userAgent, 'Android') !== false) {
            return 'android';
        } elseif (strpos($userAgent, 'iPhone') !== false || strpos($userAgent, 'iPad') !== false) {
            return 'ios';
        } else {
            return 'web';
        }
    }
} 