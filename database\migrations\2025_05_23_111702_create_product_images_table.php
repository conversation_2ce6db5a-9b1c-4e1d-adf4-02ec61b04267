<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_images', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id')->comment('商品ID');
            $table->string('url')->comment('图片URL');
            $table->string('path')->comment('图片存储路径');
            $table->integer('sort')->default(0)->comment('排序');
            $table->boolean('is_main')->default(false)->comment('是否主图');
            $table->string('driver', 50)->nullable()->comment('存储驱动');
            $table->string('original_name', 255)->nullable()->comment('原始文件名');
            $table->unsignedBigInteger('size')->nullable()->comment('文件大小(字节)');
            $table->string('mime_type', 100)->nullable()->comment('文件类型');
            $table->boolean('status')->default(true)->comment('状态：1启用，0禁用');
            $table->timestamps();

            // 外键约束
            $table->foreign('product_id')
                  ->references('id')
                  ->on('products')
                  ->onDelete('cascade');
                  
            // 索引
            $table->index('product_id');
            $table->index(['product_id', 'is_main', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_images');
    }
};
