import request from '../utils/request.js'

// 行为分析相关API
export default {
	// 获取行为分析概览
	getBehaviorOverview(params = {}) {
		return request.get('/crm/behavior-analytics/overview', { params })
	},
	
	// 获取客户行为详情
	getClientBehavior(clientId, params = {}) {
		return request.get(`/crm/behavior-analytics/client/${clientId}`, { params })
	},
	
	// 获取购买行为分析
	getPurchaseAnalysis(params = {}) {
		return request.get('/crm/behavior-analytics/purchase', { params })
	},
	
	// 获取浏览行为分析
	getBrowseAnalysis(params = {}) {
		return request.get('/crm/behavior-analytics/browse', { params })
	},
	
	// 获取时间行为分析
	getTimeAnalysis(params = {}) {
		return request.get('/crm/behavior-analytics/time', { params })
	},
	
	// 获取地理行为分析
	getGeoAnalysis(params = {}) {
		return request.get('/crm/behavior-analytics/geo', { params })
	},
	
	// 获取趋势分析
	getTrendAnalysis(params = {}) {
		return request.get('/crm/behavior-analytics/trend', { params })
	},
	
	// 获取客户价值分析
	getCustomerValueAnalysis(params = {}) {
		return request.get('/crm/behavior-analytics/customer-value', { params })
	},
	
	// 获取客户细分分析
	getCustomerSegmentAnalysis(params = {}) {
		return request.get('/crm/behavior-analytics/customer-segment', { params })
	},
	
	// 获取商品偏好分析
	getProductPreferenceAnalysis(params = {}) {
		return request.get('/crm/behavior-analytics/product-preference', { params })
	},
	
	// 获取流失预警
	getChurnWarning(params = {}) {
		// 生鲜配送业务默认7天预警，最长14天
		const defaultParams = {
			warning_days: 7,
			limit: 50,
			...params
		}
		return request.get('/crm/behavior-analytics/churn-warning', { params: defaultParams })
	},
	
	// 获取今日统计
	getTodayStats(params = {}) {
		return request.get('/crm/behavior-analytics/today-stats', { params })
	},
	
	// 获取对比分析
	getComparisonAnalysis(params = {}) {
		return request.get('/crm/behavior-analytics/comparison', { params })
	},
	
	// 获取商品生命周期分析
	getProductLifecycleAnalysis(params = {}) {
		return request.get('/crm/behavior-analytics/product-lifecycle', { params })
	},
	
	// 获取客户活跃度分析
	getCustomerActivityAnalysis(params = {}) {
		return request.get('/crm/behavior-analytics/customer-activity', { params })
	}
} 