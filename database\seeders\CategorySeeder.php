<?php

namespace Database\Seeders;

use App\Product\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 临时禁用外键约束
        DB::statement('SET FOREIGN_KEY_CHECKS=0');
        
        // 清空表
        Category::truncate();

        // 创建示例分类数据
        $categories = [
            [
                'name' => '食品',
                'description' => '各类食品和饮料产品',
                'parent_id' => 0,
                'sort' => 1,
                'status' => 1,
            ],
            [
                'name' => '生鲜水果',
                'description' => '新鲜水果和蔬菜',
                'parent_id' => 1,
                'sort' => 1,
                'status' => 1,
            ],
            [
                'name' => '休闲零食',
                'description' => '各类零食小吃',
                'parent_id' => 1,
                'sort' => 2,
                'status' => 1,
            ],
            [
                'name' => '服装',
                'description' => '各类服装产品',
                'parent_id' => 0,
                'sort' => 2,
                'status' => 1,
            ],
            [
                'name' => '男装',
                'description' => '男士服装',
                'parent_id' => 4,
                'sort' => 1,
                'status' => 1,
            ],
            [
                'name' => '女装',
                'description' => '女士服装',
                'parent_id' => 4,
                'sort' => 2,
                'status' => 1,
            ],
            [
                'name' => '电子产品',
                'description' => '各类电子产品',
                'parent_id' => 0,
                'sort' => 3,
                'status' => 1,
            ],
            [
                'name' => '智能手机',
                'description' => '智能手机产品',
                'parent_id' => 7,
                'sort' => 1,
                'status' => 1,
            ],
            [
                'name' => '电脑平板',
                'description' => '电脑和平板产品',
                'parent_id' => 7,
                'sort' => 2,
                'status' => 1,
            ],
            [
                'name' => '家居',
                'description' => '家居装饰和家具产品',
                'parent_id' => 0,
                'sort' => 4,
                'status' => 1,
            ],
            [
                'name' => '家具',
                'description' => '各类家具产品',
                'parent_id' => 10,
                'sort' => 1,
                'status' => 1,
            ],
            [
                'name' => '家居装饰',
                'description' => '家居装饰产品',
                'parent_id' => 10,
                'sort' => 2,
                'status' => 1,
            ],
        ];

        // 逐个插入数据
        foreach ($categories as $category) {
            Category::create($category);
        }
        
        $this->command->info('分类数据添加完成！共添加 ' . count($categories) . ' 条记录');
        
        // 重新启用外键约束
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }
} 