/**
 * 统一购物车管理器
 * 合并所有购物车相关功能，消除重复代码
 */

const { api } = require('./api');
const request = require('./request');

class UnifiedCartManager {
  constructor() {
    this.listeners = new Set();
    this.updateTimer = null;
    this.badgeTimer = null;
  }

  // ==================== 购物车操作 ====================

  /**
   * 获取购物车列表（仅API，需要登录）
   */
  async getCartList() {
    try {
      console.log('🛒 开始获取购物车列表...');
      const token = wx.getStorageSync('token');
      
      if (!token) {
        console.log('👤 用户未登录，返回空购物车');
        return {
          success: true,
          data: {
            items: [],
            total_price: 0,
            total_quantity: 0
          }
        };
      }
      
      console.log('🔐 用户已登录，从API获取购物车数据');
      const response = await api.getCartList();
      console.log('📡 购物车API响应:', response);
      
      if (response && response.data) {
        console.log('✅ 购物车API调用成功');
        return {
          success: true,
          data: response.data
        };
      } else {
        console.warn('⚠️ 购物车API返回数据为空');
        return {
          success: true,
          data: {
            items: [],
            total_price: 0,
            total_quantity: 0
          }
        };
      }
    } catch (apiError) {
      console.error('❌ 购物车API调用失败:', apiError);
      
      // 检查是否是认证错误
      if (apiError.message && (
        apiError.message.includes('登录') || 
        apiError.message.includes('授权') ||
        apiError.message.includes('Unauthorized')
      )) {
        console.log('🔐 检测到认证错误，清除登录状态');
        // 清除登录状态
        wx.removeStorageSync('token');
        wx.removeStorageSync('userInfo');
        
        return {
          success: false,
          message: '登录已过期，请重新登录',
          data: {
            items: [],
            total_price: 0,
            total_quantity: 0
          }
        };
      }
      
      // 其他API错误
      return {
        success: false,
        message: apiError.message || '获取购物车失败',
        data: {
          items: [],
          total_price: 0,
          total_quantity: 0
        }
      };
    }
  }

  /**
   * 格式化购物车数据
   */
  formatCartData(apiData) {
    if (!apiData || !apiData.items) {
      return {
        items: [],
        totalPrice: 0,
        totalCount: 0,
        selectedCount: 0,
        selectedPrice: 0,
        allSelected: false
      };
    }
    
    // 转换商品格式
    const items = apiData.items.map(item => {
      // 🚨 关键修复：明确区分购物车项ID和商品ID
      const formattedItem = {
        // 购物车项ID（用于购物车操作：删除、更新数量等）
        id: item.id,
        cart_item_id: item.id,
        
        // 商品ID（用于订单创建、商品详情等）
        product_id: item.product_id,
        
        // SKU信息
        sku_id: item.sku_id,
        
        // 商品基本信息
        name: item.name,
        image: item.cover_url || item.image_url,
        image_url: item.cover_url || item.image_url,
        cover_url: item.cover_url || item.image_url,
        
        // 价格信息
        price: parseFloat(item.price),
        original_price: item.original_price ? parseFloat(item.original_price) : null,
        has_discount: item.has_discount || false,
        price_labels: item.price_labels || [],
        discount_info: item.discount_info || [],
        price_type: item.price_type || 'base',
        
        // 数量和选中状态
        quantity: item.quantity,
        selected: item.is_selected !== false,
        is_selected: item.is_selected !== false,
        
        // 单位和规格 - 只显示销售单位
        unit: item.product?.unit || item.unit || '',
        spec: item.sku_name || null,
        sku_name: item.sku_name || null,
        
        // 调试信息
        _debug: {
          购物车项ID: item.id,
          商品ID: item.product_id,
          原始数据: item
        }
      };
      
      console.log('🔧 格式化购物车项:', {
        购物车项ID: formattedItem.id,
        商品ID: formattedItem.product_id,
        商品名称: formattedItem.name,
        数量: formattedItem.quantity
      });
      
      return formattedItem;
    });
    
    // 计算统计数据
    const totalCount = items.reduce((sum, item) => sum + item.quantity, 0);
    const selectedItems = items.filter(item => item.selected);
    const selectedCount = selectedItems.reduce((sum, item) => sum + item.quantity, 0);
    const selectedPrice = selectedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const allSelected = items.length > 0 && items.every(item => item.selected);
    
    return {
      items,
      totalPrice: apiData.total_price || 0,
      totalCount,
      selectedCount,
      selectedPrice,
      allSelected
    };
  }

  /**
   * 更新商品选中状态（需要登录）
   */
  async updateSelection(itemId, selected) {
    try {
      const token = wx.getStorageSync('token');
      
      if (!token) {
        console.log('❌ 用户未登录，无法更新选中状态');
        throw new Error('请先登录');
      }
      
      // 调用API更新选中状态
      const response = await request.put(`/cart/items/${itemId}/toggle-select`, {
        is_selected: selected
      });
      
      if (response && response.data) {
        console.log('✅ 选中状态更新成功');
        return true;
      } else {
        throw new Error('更新选中状态失败');
      }
    } catch (error) {
      console.error('更新选中状态失败:', error);
      throw error;
    }
  }

  /**
   * 删除商品（兼容CartAPI接口）
   */
  async deleteItem(itemId) {
    return await this.removeItem(itemId);
  }

  /**
   * 添加商品（需要登录）
   */
  async addItem(product, quantity = 1) {
    try {
      // 检查登录状态
      const token = wx.getStorageSync('token');
      
      if (!token) {
        console.log('❌ 用户未登录，无法添加商品到购物车');
        wx.showToast({ title: '请先登录', icon: 'none' });
        return false;
      }
      
      // 构建请求数据
      const requestData = {
        product_id: product.id,
        quantity: quantity
      };
      
      // 如果商品有销售单位信息，添加单位ID
      if (product.sale_unit && product.sale_unit.id) {
        requestData.unit_id = product.sale_unit.id;
        console.log('🔧 添加购物车使用销售单位:', {
          product_id: product.id,
          unit_id: product.sale_unit.id,
          unit_name: product.sale_unit.name
        });
      }
      
      // 调用API添加商品
      const response = await request.post('/cart', requestData);
      
      if (response && response.data) {
        this._updateBadge();
        wx.showToast({ title: '已加入购物车', icon: 'success' });
        return true;
      } else {
        wx.showToast({ title: '添加失败', icon: 'error' });
        return false;
      }
    } catch (error) {
      console.error('添加商品失败:', error);
      
      if (error.message && error.message.includes('登录')) {
        wx.showToast({ title: '请先登录', icon: 'none' });
      } else {
        wx.showToast({ title: '添加失败', icon: 'error' });
      }
      return false;
    }
  }

  /**
   * 更新数量（需要登录）
   */
  async updateQuantity(productId, quantity) {
    try {
      const token = wx.getStorageSync('token');
      
      if (!token) {
        console.log('❌ 用户未登录，无法更新商品数量');
        throw new Error('请先登录');
      }
      
      // 调用API更新数量
      const response = await request.put(`/cart/items/${productId}`, { quantity });
      if (response && response.data) {
        this._updateBadge();
        return true;
      } else {
        throw new Error('更新数量失败');
      }
    } catch (error) {
      console.error('更新数量失败:', error);
      throw error;
    }
  }

  /**
   * 删除商品（需要登录）
   */
  async removeItem(productId) {
    try {
      const token = wx.getStorageSync('token');
      
      if (!token) {
        console.log('❌ 用户未登录，无法删除商品');
        throw new Error('请先登录');
      }
      
      // 调用API删除商品
      const response = await request.del(`/cart/items/${productId}`);
      if (response && response.data) {
        this._updateBadge();
        return true;
      } else {
        throw new Error('删除商品失败');
      }
    } catch (error) {
      console.error('删除商品失败:', error);
      throw error;
    }
  }

  /**
   * 清空购物车（需要登录）
   */
  async clear() {
    try {
      const token = wx.getStorageSync('token');
      
      if (!token) {
        console.log('❌ 用户未登录，无法清空购物车');
        throw new Error('请先登录');
      }
      
            const response = await request.del('/cart/clear');
      if (response && response.data) {
        this._updateBadge();
        wx.showToast({ title: '购物车已清空', icon: 'success' });
        return true;
      } else {
        throw new Error('清空购物车失败');
      }
    } catch (error) {
      console.error('清空购物车失败:', error);
      throw error;
    }
  }

    // ==================== 徽标管理 ====================

  /**
   * 更新徽标（从API获取数量）
   */
  _updateBadge() {
    if (this.badgeTimer) {
      clearTimeout(this.badgeTimer);
    }
    
    this.badgeTimer = setTimeout(async () => {
      const token = wx.getStorageSync('token');
      
      if (!token) {
        // 未登录：移除徽标
        wx.removeTabBarBadge({ index: 2 }).catch(() => {});
        
        // 更新全局状态
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.cartCount = 0;
        }
        return;
      }
      
      // 已登录：从API获取购物车数量
      try {
        const response = await api.getCartCount();
        const count = response?.data || 0;
        
        if (count > 0) {
          const text = count > 99 ? '99+' : count.toString();
          wx.setTabBarBadge({ index: 2, text }).catch(() => {});
        } else {
          wx.removeTabBarBadge({ index: 2 }).catch(() => {});
        }
        
        // 更新全局状态
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.cartCount = count;
        }
      } catch (error) {
        console.error('获取购物车数量失败:', error);
        // 失败时移除徽标
        wx.removeTabBarBadge({ index: 2 }).catch(() => {});
      }
      
      this.badgeTimer = null;
    }, 300);
  }

  // ==================== 状态获取 ====================

  /**
   * 获取商品数量（从API获取）
   */
  async getItemQuantity(productId) {
    try {
      const result = await this.getCartList();
      if (result.success && result.data.items) {
        const item = result.data.items.find(item => item.product_id === productId);
        return item ? item.quantity : 0;
      }
      return 0;
    } catch (error) {
      console.error('获取商品数量失败:', error);
      return 0;
    }
  }

  /**
   * 检查商品是否在购物车中（从API获取）
   */
  async hasItem(productId) {
    try {
      const result = await this.getCartList();
      if (result.success && result.data.items) {
        return result.data.items.some(item => item.product_id === productId);
      }
      return false;
    } catch (error) {
      console.error('检查商品是否在购物车失败:', error);
      return false;
    }
  }

  // ==================== 事件监听 ====================

  /**
   * 添加监听器
   */
  addListener(callback) {
    this.listeners.add(callback);
  }

  /**
   * 移除监听器
   */
  removeListener(callback) {
    this.listeners.delete(callback);
  }

  /**
   * 通知变化
   */
  _notifyChange(action, data = {}) {
    const event = {
      action,
      data,
      timestamp: Date.now()
    };

    this.listeners.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.warn('监听器执行失败:', error);
      }
    });

    // 更新徽标
    this._updateBadge();
  }

  // ==================== 页面显示时更新 ====================

  /**
   * 页面显示时刷新
   */
  async onPageShow() {
    console.log('🔄 购物车管理器 - 页面显示时刷新');
    
    // 更新徽标（会自动检查登录状态）
    this._updateBadge();
  }
}

// 创建单例
const cartManager = new UnifiedCartManager();

// 创建CartAPI兼容接口
const CartAPI = {
  getCartList: () => cartManager.getCartList(),
  formatCartData: (data) => cartManager.formatCartData(data),
  addToCart: async (product, quantity) => {
    try {
      const success = await cartManager.addItem(product, quantity);
      return {
        success: success,
        message: success ? '添加成功' : '添加失败'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '添加失败'
      };
    }
  },
  updateQuantity: (itemId, quantity) => cartManager.updateQuantity(itemId, quantity),
  deleteItem: (itemId) => cartManager.deleteItem(itemId),
  updateSelection: (itemId, selected) => cartManager.updateSelection(itemId, selected)
};

// 导出简洁接口
module.exports = {
  // 购物车操作
  addToCart: (product, quantity) => cartManager.addItem(product, quantity),
  removeFromCart: (productId) => cartManager.removeItem(productId),
  updateQuantity: (productId, quantity) => cartManager.updateQuantity(productId, quantity),
  clearCart: () => cartManager.clear(),
  
  // 状态获取
  getCartCount: async () => {
    const result = await cartManager.getCartList();
    return result.success ? result.data.total_quantity || 0 : 0;
  },
  getCartList: () => cartManager.getCartList(),
  getCartState: async () => {
    const result = await cartManager.getCartList();
    return result.success ? cartManager.formatCartData(result.data) : {
      items: [],
      totalPrice: 0,
      totalCount: 0,
      selectedCount: 0,
      selectedPrice: 0,
      allSelected: false
    };
  },
  hasItem: (productId) => cartManager.hasItem(productId),
  getItemQuantity: (productId) => cartManager.getItemQuantity(productId),
  
  // 事件监听
  addListener: (callback) => cartManager.addListener(callback),
  removeListener: (callback) => cartManager.removeListener(callback),
  
  // 页面生命周期
  onPageShow: () => cartManager.onPageShow(),
  
  // 徽标更新
  updateBadge: () => cartManager._updateBadge(),
  
  // CartAPI兼容接口
  CartAPI,
  
  // 内部实例（用于高级操作）
  _instance: cartManager
}; 