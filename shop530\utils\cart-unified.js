/**
 * 统一购物车管理器
 * 合并所有购物车相关功能，消除重复代码
 * 
 * 优化版本：增强事件通知机制，解决页面间通信问题
 */

const apiModule = require('./api');
const api = apiModule.api; // 正确获取api实例
const request = require('./request');
const { isLoggedIn } = require('./login-state-manager');

// 全局购物车数据缓存
let globalCartCache = {
  items: [],
  timestamp: Date.now(),
  totalCount: 0
};

// 定义事件类型常量，避免字符串错误
const EVENT_TYPES = {
  ADD: 'add',           // 添加商品
  REMOVE: 'remove',     // 移除商品
  UPDATE: 'update',     // 更新商品数量
  COUNT: 'count',       // 购物车数量变化
  CLEAR: 'clear',       // 清空购物车
  SELECTION: 'selection', // 选择状态变化
  SYNC: 'sync'          // 数据同步
};

class UnifiedCartManager {
  constructor() {
    // 使用Map存储监听器，提高查找效率
    this.listeners = new Map();
    
    // 定时器
    this.updateTimer = null;
    this.badgeTimer = null;
    this.syncTimer = null;
    
    // 操作队列和状态
    this._pendingOperations = [];
    this._isRecovering = false;
    this._isSyncing = false;
    
    // 初始化加载缓存
    this._loadCacheFromStorage();
    
    // 定期检查缓存状态
    this._startBackgroundSync();
    
    console.log('✅ 购物车管理器初始化完成');
  }

  // ==================== 购物车操作 ====================

  /**
   * 获取购物车列表（仅API，需要登录）
   */
  async getCartList() {
    try {
      console.log('🛒 开始获取购物车列表...');
      const token = wx.getStorageSync('token');
      
      if (!token) {
        console.log('👤 用户未登录，返回空购物车');
        return {
          success: true,
          data: this._getDefaultCartData()
        };
      }
      
      console.log('🔐 用户已登录，从API获取购物车数据');
      
      try {
        const response = await api.getCartList();
        console.log('📡 购物车API响应:', response);
        
        // 检查响应数据
        if (!response) {
          throw new Error('API返回为空');
        }
        
        // 标准化响应数据结构
        let cartData;
        
        // 情况1: response.data是数组
        if (Array.isArray(response.data)) {
          cartData = {
            items: response.data,
            total_price: this._calculateTotalPrice(response.data),
            total_quantity: this._calculateTotalQuantity(response.data)
          };
        }
        // 情况2: response.data.items是数组
        else if (response.data && response.data.items && Array.isArray(response.data.items)) {
          cartData = response.data;
        }
        // 情况3: response.data.data是数组
        else if (response.data && response.data.data && Array.isArray(response.data.data)) {
          cartData = {
            items: response.data.data,
            total_price: response.data.total_price || this._calculateTotalPrice(response.data.data),
            total_quantity: response.data.total_quantity || this._calculateTotalQuantity(response.data.data)
          };
        }
        // 情况4: response.data是对象但不符合上述结构
        else if (response.data && typeof response.data === 'object') {
          // 尝试将对象转换为数组
          const items = Object.values(response.data).filter(item => 
            item && typeof item === 'object' && (item.id || item.product_id)
          );
          
          cartData = {
            items: items,
            total_price: this._calculateTotalPrice(items),
            total_quantity: this._calculateTotalQuantity(items)
          };
        }
        // 默认情况: 返回空购物车
        else {
          console.warn('⚠️ 购物车API返回数据格式异常，使用空购物车');
          cartData = this._getDefaultCartData();
        }
        
        // 确保所有商品图片URL正确
        if (cartData.items && Array.isArray(cartData.items)) {
          // 获取基础URL
          const baseUrl = this._getBaseUrl();
          console.log('🔄 使用基础URL处理图片:', baseUrl);
          
          cartData.items = cartData.items.map(item => {
            // 处理图片URL - 优先使用cover_url（后端字段），然后是image_url，最后是image
            let imageUrl = item.cover_url || item.image_url || item.image || '';
            
            // 如果图片URL是相对路径，添加基础URL
            if (imageUrl && !this._isAbsoluteUrl(imageUrl) && !imageUrl.startsWith('/images/')) {
              // 如果以/开头，直接拼接基础URL
              if (imageUrl.startsWith('/')) {
                imageUrl = `${baseUrl}${imageUrl}`;
        } else {
                // 否则添加/前缀再拼接
                imageUrl = `${baseUrl}/${imageUrl}`;
              }
              
              console.log(`🖼️ 图片URL处理: ${item.cover_url || item.image_url || item.image} -> ${imageUrl}`);
            }
            
          return {
              ...item,
              image: imageUrl
            };
          });
        }
        
        console.log('✅ 处理后的购物车数据:', cartData);
        
        // 保存到全局缓存和本地存储
        this._updateCartCache(cartData);
        
        // 通知所有监听器数据已同步
        this._notifyChange(EVENT_TYPES.SYNC, { 
          items: cartData.items,
          source: 'api'
        });
        
        return {
            success: true,
          data: cartData
          };
      } catch (apiError) {
        console.error('❌ 购物车API调用失败:', apiError);
        
        // 尝试使用缓存数据
        if (globalCartCache.items && globalCartCache.items.length > 0) {
          console.log('🔄 使用缓存购物车数据:', globalCartCache);
          
          // 通知监听器使用了缓存数据
          this._notifyChange(EVENT_TYPES.SYNC, { 
            items: globalCartCache.items,
            source: 'cache',
            error: apiError.message
          });
          
          return {
            success: true,
            data: {
              items: globalCartCache.items,
              total_price: this._calculateTotalPrice(globalCartCache.items),
              total_quantity: this._calculateTotalQuantity(globalCartCache.items)
            }
          };
        }
        
        // 检查是否是认证错误
        if (apiError.message && (
          apiError.message.includes('登录') || 
          apiError.message.includes('授权') ||
          apiError.message.includes('Unauthorized')
        )) {
          console.log('🔐 检测到认证错误，清除登录状态');
          // 清除登录状态
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');
        }
        
        // 返回空数据
        return {
          success: false,
          message: apiError.message || '获取购物车失败',
          data: this._getDefaultCartData()
        };
      }
    } catch (error) {
      console.error('❌❌ 获取购物车发生严重错误:', error);
      return {
        success: false,
        message: '获取购物车数据时发生错误',
        data: this._getDefaultCartData()
      };
    }
  }
  
  /**
   * 获取默认的空购物车数据结构
   */
  _getDefaultCartData() {
    return {
      items: [],
      total_price: 0,
      total_quantity: 0
    };
  }
  
  /**
   * 计算购物车总价格
   */
  _calculateTotalPrice(items = []) {
    if (!Array.isArray(items)) return 0;
    return items.reduce((sum, item) => {
      return sum + (parseFloat(item.price) || 0) * (item.quantity || 0);
    }, 0).toFixed(2);
  }
  
  /**
   * 计算购物车总数量
   */
  _calculateTotalQuantity(items = []) {
    if (!Array.isArray(items)) return 0;
    return items.reduce((sum, item) => sum + (item.quantity || 0), 0);
  }
  
  /**
   * 更新购物车缓存
   */
  _updateCartCache(data) {
    if (!data || !data.items) return;
    
    globalCartCache = {
      items: Array.isArray(data.items) ? [...data.items] : [],
      timestamp: Date.now(),
      totalCount: this._calculateTotalQuantity(data.items)
    };
    
    // 保存到本地存储
    try {
      wx.setStorageSync('cartItemsCache', JSON.stringify(globalCartCache));
    } catch (e) {
      console.error('保存购物车缓存失败:', e);
    }
  }
  
  /**
   * 从本地存储加载缓存
   */
  _loadCacheFromStorage() {
    try {
      const cachedData = wx.getStorageSync('cartItemsCache');
      if (cachedData) {
        const parsedCache = JSON.parse(cachedData);
        if (parsedCache && parsedCache.items) {
          globalCartCache = parsedCache;
          console.log('✅ 成功从本地存储加载购物车缓存:', globalCartCache.items.length);
        }
      }
    } catch (e) {
      console.error('加载购物车缓存失败:', e);
    }
  }
  
  /**
   * 开始后台同步
   */
  _startBackgroundSync() {
    // 清除可能存在的旧定时器
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }
    
    // 每60秒检查一次缓存有效性
    this.syncTimer = setInterval(() => {
      const now = Date.now();
      if (now - globalCartCache.timestamp > 300000) { // 5分钟后缓存过期
        console.log('🔄 购物车缓存已过期，尝试刷新');
        this.getCartList().catch(e => console.error('后台同步购物车失败:', e));
      }
    }, 60000);
  }

  /**
   * 格式化购物车数据
   */
  formatCartData(apiData) {
    if (!apiData || !apiData.items) {
      return {
        items: [],
        totalPrice: 0,
        totalCount: 0,
        selectedCount: 0,
        selectedPrice: 0,
        allSelected: false
      };
    }
    
    // 检查数据完整性
    if (!Array.isArray(apiData.items)) {
      console.warn('⚠️ 购物车数据不是数组, 强制转换为空数组');
      apiData.items = [];
    }
    
    // 转换商品格式
    const items = apiData.items.map(item => {
      // 确保item是对象
      if (!item || typeof item !== 'object') {
        console.warn('⚠️ 购物车项不是对象:', item);
        return null;
      }
      
      try {
        // 🚨 关键修复：明确区分购物车项ID和商品ID
        const formattedItem = {
          // 购物车项ID（用于购物车操作：删除、更新数量等）
          id: item.id,
          cart_item_id: item.id,
          
          // 商品ID（用于订单创建、商品详情等）
          product_id: item.product_id,
          
          // SKU信息
          sku_id: item.sku_id,
          
          // 商品基本信息
          name: item.name || '未知商品',
          image: item.cover_url || item.image_url || '/images/placeholder.png',
          image_url: item.cover_url || item.image_url || '/images/placeholder.png',
          cover_url: item.cover_url || item.image_url || '/images/placeholder.png',
          
          // 价格信息
          price: parseFloat(item.price) || 0,
          original_price: item.original_price ? parseFloat(item.original_price) : null,
          has_discount: item.has_discount || false,
          price_labels: item.price_labels || [],
          discount_info: item.discount_info || [],
          price_type: item.price_type || 'base',
          
          // 数量和选中状态
          quantity: parseInt(item.quantity) || 1,
          selected: item.is_selected !== false,
          is_selected: item.is_selected !== false,
          
          // 单位和规格 - 只显示销售单位
          unit: item.product?.unit || item.unit || '',
          spec: item.sku_name || null,
          sku_name: item.sku_name || null
        };
        
        return formattedItem;
      } catch (e) {
        console.error('格式化购物车项出错:', e, item);
        return null;
      }
    }).filter(item => item !== null); // 过滤掉格式化失败的项
    
    // 计算统计数据
    const totalCount = items.reduce((sum, item) => sum + (parseInt(item.quantity) || 0), 0);
    const selectedItems = items.filter(item => item.selected);
    const selectedCount = selectedItems.reduce((sum, item) => sum + (parseInt(item.quantity) || 0), 0);
    const selectedPrice = selectedItems.reduce((sum, item) => sum + ((parseFloat(item.price) || 0) * (parseInt(item.quantity) || 0)), 0);
    const allSelected = items.length > 0 && items.every(item => item.selected);
    
    return {
      items,
      totalPrice: apiData.total_price || this._calculateTotalPrice(items),
      totalCount,
      selectedCount,
      selectedPrice,
      allSelected
    };
  }

  /**
   * 更新商品选中状态（需要登录）
   */
  async updateSelection(itemId, selected) {
    try {
      const token = wx.getStorageSync('token');
      
      if (!token) {
        console.log('❌ 用户未登录，无法更新选中状态');
        throw new Error('请先登录');
      }
      
      // 调用API更新选中状态
      const response = await request.put(`/cart/items/${itemId}/toggle-select`, {
        is_selected: selected
      });
      
      if (response && response.data) {
        console.log('✅ 选中状态更新成功');
        return {
          success: true,
          message: '选中状态更新成功'
        };
      } else {
        throw new Error('更新选中状态失败');
      }
    } catch (error) {
      console.error('更新选中状态失败:', error);
      throw error;
    }
  }

  /**
   * 删除商品（兼容CartAPI接口）
   */
  async deleteItem(itemId) {
    return await this.removeItem(itemId);
  }

  /**
   * 添加商品到购物车（公共方法）
   * @param {Object} params - 参数对象
   * @param {number} params.product_id - 商品ID
   * @param {number} params.quantity - 数量
   * @param {number} params.unit_id - 单位ID
   * @returns {Promise<boolean>}
   */
  async addToCart(params) {
    console.log('🛒 添加商品到购物车:', params);
    
    // 验证参数
    if (!params || !params.product_id) {
      console.error('❌ 添加购物车失败: 缺少商品ID');
      wx.showToast({
        title: '商品信息不完整',
        icon: 'none'
      });
      return {
        success: false,
        message: '商品信息不完整'
      };
    }
    
    // 如果传入的是商品对象，提取ID
    if (typeof params === 'object' && params.id && !params.product_id) {
      params = {
        product_id: params.id,
        quantity: params.quantity || 1,
        unit_id: params.unit_id || (params.sale_unit ? params.sale_unit.id : null)
      };
    }
    
    // 确保quantity有默认值
    if (!params.quantity) {
      params.quantity = 1;
    }
    
    try {
      // 获取商品详细信息
      let productDetail = null;
      try {
        // 尝试获取商品详情以获取单位信息
        const api = require('./api');
        productDetail = await api.getProductDetail(params.product_id);
      } catch (error) {
        console.warn('⚠️ 获取商品详情失败，继续使用现有参数');
      }
      
      // 如果获取到了商品详情，补充单位信息
      if (productDetail && !params.unit_id && productDetail.sale_unit) {
        params.unit_id = productDetail.sale_unit.id;
      }
      
      // 调用内部方法添加商品
      return await this.addItem(productDetail || { id: params.product_id }, params.quantity, params.unit_id);
    } catch (error) {
      console.error('❌ 添加购物车失败:', error);
      return {
        success: false,
        message: error.message || '添加购物车失败'
      };
    }
  }

  /**
   * 更新数量（需要登录）
   * 🔥 新增：当数量低于最小起购数量时，自动删除商品
   */
  async updateQuantity(productId, quantity) {
    try {
      const token = wx.getStorageSync('token');
      
      if (!token) {
        console.log('❌ 用户未登录，无法更新商品数量');
        throw new Error('请先登录');
      }
      
      // 🔥 新增：获取商品信息检查最小起购数量
      let shouldDelete = false;
      let productInfo = null;
      let cartItemId = null;

      try {
        // 从购物车列表中获取商品信息
        const cartResult = await this.getCartList();
        if (cartResult.success && cartResult.data.items) {
          const cartItem = cartResult.data.items.find(item => item.product_id === productId);
          if (cartItem) {
            // 获取购物车项ID
            cartItemId = cartItem.id;

            // 从购物车项中获取商品的最小起购数量信息
            const minSaleQuantity = cartItem.min_sale_quantity || cartItem.product?.min_sale_quantity || 1;
            productInfo = {
              name: cartItem.name,
              min_sale_quantity: minSaleQuantity,
              unit: cartItem.unit || '件'
            };

            console.log('🔍 找到购物车项:', {
              product_id: productId,
              cart_item_id: cartItemId,
              product_name: productInfo.name,
              current_quantity: cartItem.quantity,
              target_quantity: quantity
            });

            // 检查是否需要删除
            if (quantity < minSaleQuantity) {
              shouldDelete = true;
              console.log('🔧 数量低于最小起购数量，将删除商品:', {
                product_id: productId,
                product_name: productInfo.name,
                quantity: quantity,
                min_sale_quantity: minSaleQuantity
              });
            }
          } else {
            // 购物车中没有找到该商品
            console.error('❌ 购物车中没有找到商品:', productId);
            throw new Error('购物车商品不存在');
          }
        }
      } catch (error) {
        console.warn('⚠️ 获取商品信息失败:', error);
        throw error;
      }
      
      // 如果需要删除，直接删除
      if (shouldDelete) {
        const deleteResult = await this.removeItem(productId);

        if (deleteResult && deleteResult.success && productInfo) {
          // 显示删除提示
          wx.showModal({
            title: '商品已移除',
            content: `${productInfo.name} 的数量低于最小起购量${productInfo.min_sale_quantity}${productInfo.unit}，已从购物车中移除`,
            showCancel: false,
            confirmText: '知道了'
          });
        }

        return deleteResult;
      }
      
      // 检查是否找到了购物车项ID
      if (!cartItemId) {
        throw new Error('购物车商品不存在');
      }

      // 正常更新数量 - 使用购物车项ID而不是商品ID
      console.log('🔄 更新购物车项:', {
        cart_item_id: cartItemId,
        product_id: productId,
        quantity: quantity
      });

      const response = await request.put(`/cart/items/${cartItemId}`, { quantity });
       if (response && response.data) {
         this._updateBadge();
         
         // 🔥 新增：检查后端是否删除了商品
         if (response.data.deleted) {
           console.log('🔧 后端删除了商品（数量低于最小起购数量）:', {
             product_id: productId,
             reason: response.data.reason
           });
           
           // 显示删除提示
           if (productInfo) {
             wx.showModal({
               title: '商品已移除',
               content: response.message || `${productInfo.name} 的数量低于最小起购量，已从购物车中移除`,
               showCancel: false,
               confirmText: '知道了'
             });
           }
           
           // 通知监听器商品被删除
           this._notifyChange('remove', { productId, reason: 'below_min_quantity' });
         } else {
           // 正常更新，通知监听器
           this._notifyChange('update', { productId, quantity });
         }
         
         return {
           success: true,
           message: '更新成功'
         };
       } else {
         throw new Error('更新数量失败');
       }
    } catch (error) {
      console.error('更新数量失败:', error);
      throw error;
    }
  }

  /**
   * 删除商品（需要登录）
   */
  async removeItem(productId) {
    try {
      const token = wx.getStorageSync('token');
      
      if (!token) {
        console.log('❌ 用户未登录，无法删除商品');
        throw new Error('请先登录');
      }
      
      // 调用API删除商品
      const response = await request.del(`/cart/items/${productId}`);
      if (response && response.data) {
        this._updateBadge();
        
        // 🔥 新增：通知监听器
        this._notifyChange('remove', { productId });
        
        return {
          success: true,
          message: '删除成功'
        };
      } else {
        throw new Error('删除商品失败');
      }
    } catch (error) {
      console.error('删除商品失败:', error);
      throw error;
    }
  }

  /**
   * 清空购物车（需要登录）
   */
  async clear() {
    try {
      const token = wx.getStorageSync('token');
      
      if (!token) {
        console.log('❌ 用户未登录，无法清空购物车');
        throw new Error('请先登录');
      }
      
            const response = await request.del('/cart/clear');
      if (response && response.data) {
        this._updateBadge();
        
        // 🔥 新增：通知监听器
        this._notifyChange('clear', {});
        
        wx.showToast({ title: '购物车已清空', icon: 'success' });
        return {
          success: true,
          message: '购物车已清空'
        };
      } else {
        throw new Error('清空购物车失败');
      }
    } catch (error) {
      console.error('清空购物车失败:', error);
      throw error;
    }
  }

    // ==================== 徽标管理 ====================

  /**
   * 更新购物车徽标
   */
  _updateBadge() {
    // 延迟执行，避免频繁调用
    if (this._updateTimer) {
      clearTimeout(this._updateTimer);
    }
    
    this._updateTimer = setTimeout(async () => {
      try {
        // 检查登录状态
        if (!isLoggedIn()) {
          // 用户未登录，移除购物车徽标
          this._removeBadge();
          // 通知监听器购物车数量为0
          this._notifyChange('count', { count: 0, userNotLoggedIn: true }, false);
          return;
        }
        
        // 获取购物车数量
        const count = await this.getCartCount();
        
        // 更新小程序右上角购物车图标的徽标
        if (typeof count === 'number') {
          // 设置tabBar的badge
          if (count > 0) {
            wx.setTabBarBadge({
              index: 2, // 购物车的tabBar索引
              text: count.toString()
            }).catch(e => {
              // 忽略设置badge的错误，这通常是因为tabBar还未准备好
              console.log('设置badge失败，可能tabBar未准备好:', e);
            });
          } else {
            // 数量为0时移除徽标
            this._removeBadge();
          }
        }
        
        // 通知监听器，但不再触发徽标更新，避免循环调用
        this._notifyChange('count', { count }, false);
      } catch (error) {
        console.error('获取购物车数量失败:', error);
        
        // 使用缓存的购物车数量
        try {
          // 再次检查登录状态
          if (!isLoggedIn()) {
            // 用户未登录，移除购物车徽标
            this._removeBadge();
            // 通知监听器购物车数量为0
            this._notifyChange('count', { count: 0, userNotLoggedIn: true }, false);
            return;
          }
          
          const cachedCount = wx.getStorageSync('cartCount') || 0;
          
          // 设置tabBar的badge
          if (cachedCount > 0) {
            wx.setTabBarBadge({
              index: 2, // 购物车的tabBar索引
              text: cachedCount.toString()
            }).catch(() => {
              // 忽略错误
            });
          } else {
            // 数量为0时移除徽标
            this._removeBadge();
          }
          
          // 通知监听器使用缓存数据，但不再触发徽标更新
          this._notifyChange('count', { count: cachedCount, fromCache: true }, false);
        } catch (cacheError) {
          // 如果缓存也失败，设置为0
          this._removeBadge();
          this._notifyChange('count', { count: 0, error: true }, false);
        }
      }
    }, 300);
  }

  /**
   * 移除购物车徽标
   */
  _removeBadge() {
    wx.removeTabBarBadge({
      index: 2
    }).catch(() => {
      // 忽略错误，可能是tabBar未准备好或徽标不存在
    });
  }

  // ==================== 状态获取 ====================

  /**
   * 获取商品数量（从API获取）
   */
  async getItemQuantity(productId) {
    try {
      const result = await this.getCartList();
      if (result.success && result.data.items) {
        const item = result.data.items.find(item => item.product_id === productId);
        return item ? item.quantity : 0;
      }
      return 0;
    } catch (error) {
      console.error('获取商品数量失败:', error);
      return 0;
    }
  }

  /**
   * 检查商品是否在购物车中（从API获取）
   */
  async hasItem(productId) {
    try {
      const result = await this.getCartList();
      if (result.success && result.data.items) {
        return result.data.items.some(item => item.product_id === productId);
      }
      return false;
    } catch (error) {
      console.error('检查商品是否在购物车失败:', error);
      return false;
    }
  }

  // ==================== 事件监听 ====================

  /**
   * 添加监听器
   * @param {string} key - 监听器唯一标识
   * @param {Function} callback - 回调函数
   * @param {Object} options - 配置选项
   * @param {Array<string>} options.events - 只监听特定类型的事件
   * @param {boolean} options.once - 是否只触发一次
   */
  addListener(key, callback, options = {}) {
    console.log(`🔔 添加购物车监听器: ${key}`, options);
    
    // 存储键值对形式的监听器
    if (!this.listeners) {
      this.listeners = new Map();
    }
    
    this.listeners.set(key, {
      callback,
      options,
      timestamp: Date.now()
    });
    
    // 立即发送当前购物车状态
    if (options.immediate !== false) {
      setTimeout(() => {
        const count = globalCartCache.totalCount || 0;
        try {
          callback({
            action: EVENT_TYPES.COUNT,
            data: { count, fromCache: true },
            timestamp: Date.now()
          });
        } catch (error) {
          console.warn(`监听器${key}初始化调用失败:`, error);
        }
      }, 0);
    }
  }

  /**
   * 移除监听器
   */
  removeListener(key) {
    if (this.listeners) {
      console.log(`🔕 移除购物车监听器: ${key}`);
      this.listeners.delete(key);
    }
  }

  /**
   * 通知变化
   */
  _notifyChange(action, data = {}, shouldUpdateBadge = true) {
    const event = {
      action,
      data: {
        ...data,
        // 确保count永远不会是undefined
        count: data.count !== undefined ? data.count : (globalCartCache.totalCount || 0)
      },
      timestamp: Date.now()
    };

    if (this.listeners) {
      this.listeners.forEach((listenerInfo, key) => {
        try {
          const { callback, options } = listenerInfo;
          
          // 检查是否只监听特定事件类型
          if (options.events && Array.isArray(options.events) && !options.events.includes(action)) {
            return; // 跳过不关心的事件类型
          }
          
          if (typeof callback === 'function') {
            callback(event);
            
            // 如果是一次性监听器，触发后移除
            if (options.once) {
              this.listeners.delete(key);
            }
          } else {
            console.warn(`监听器${key}不是一个函数:`, callback);
            // 自动清理无效的监听器
            this.listeners.delete(key);
          }
        } catch (error) {
          console.warn('监听器执行失败:', error);
        }
      });
    }

    // 只有在需要时才更新徽标，避免循环调用
    if (shouldUpdateBadge && action !== EVENT_TYPES.COUNT) {
      this._updateBadge();
    }
  }

  // ==================== 页面显示时更新 ====================

  /**
   * 页面显示时刷新
   */
  async onPageShow() {
    console.log('🔄 购物车管理器 - 页面显示时刷新');
    
    // 更新徽标（会自动检查登录状态）
    this._updateBadge();
  }

  /**
   * 添加商品（内部方法）
   */
  async addItem(product, quantity = 1, unitId = null) {
    try {
      // 检查登录状态
      const token = wx.getStorageSync('token');
      
      if (!token) {
        console.log('❌ 用户未登录，无法添加商品到购物车');
        wx.showToast({ title: '请先登录', icon: 'none' });
        return {
          success: false,
          message: '请先登录'
        };
      }
      
      // 确保product_id存在
      if (!product || !product.id) {
        console.error('❌ 添加商品失败: 缺少商品ID');
        wx.showToast({ title: '商品信息不完整', icon: 'none' });
        return {
          success: false,
          message: '商品信息不完整'
        };
      }
      
      // 🔥 新增：检查最小起购数量
      const minSaleQuantity = product.min_sale_quantity || 1;
      console.log('🔧 检查最小起购数量:', {
        product_id: product.id,
        product_name: product.name,
        min_sale_quantity: minSaleQuantity,
        requested_quantity: quantity
      });
      
      // 检查当前购物车中是否已有该商品
      let existingQuantity = 0;
      try {
        existingQuantity = await this.getItemQuantity(product.id);
        console.log('🔧 购物车中已有数量:', existingQuantity);
      } catch (error) {
        console.log('🔧 获取购物车中商品数量失败，默认为0');
        existingQuantity = 0;
      }
      
      // 计算加购后的总数量
      const totalQuantityAfterAdd = existingQuantity + quantity;
      
      // 如果是首次加购（购物车中没有该商品），确保数量满足最小起购量
      if (existingQuantity === 0 && quantity < minSaleQuantity) {
        console.log('⚠️ 首次加购数量不足最小起购量，自动调整');
        quantity = minSaleQuantity;
        
        wx.showModal({
          title: '提示',
          content: `该商品最小起购量为${minSaleQuantity}${product.unit || '件'}，已为您调整数量`,
          showCancel: false,
          confirmText: '知道了'
        });
      }
      
      // 构建请求数据
      const requestData = {
        product_id: product.id,
        quantity: quantity
      };
      
      // 处理销售单位
      if (unitId) {
        // 如果直接传入了unitId，优先使用
        requestData.unit_id = unitId;
        console.log('🔧 添加购物车使用传入的unitId:', unitId);
      } else if (product.sale_unit && product.sale_unit.id) {
        // 直接使用商品的销售单位ID
        requestData.unit_id = product.sale_unit.id;
        console.log('🔧 添加购物车使用销售单位:', {
          product_id: product.id,
          unit_id: product.sale_unit.id,
          unit_name: product.sale_unit.name
        });
      } else if (product.unit_id) {
        // 如果商品有unit_id属性，直接使用
        requestData.unit_id = product.unit_id;
        console.log('🔧 添加购物车使用商品的unit_id:', product.unit_id);
      } else {
        // 尝试从商品的其他属性中获取单位ID
        const possibleUnitId = product.default_unit_id || product.defaultUnitId;
        if (possibleUnitId) {
          requestData.unit_id = possibleUnitId;
          console.log('🔧 添加购物车使用默认单位ID:', possibleUnitId);
        } else {
          console.log('⚠️ 商品没有单位ID信息，使用默认单位');
        }
      }
      
      console.log('🔧 最终加购数据:', requestData);
      
      // 调用API添加商品
      const response = await request.post('/cart', requestData);
      
      if (response && response.data) {
        this._updateBadge();
        
        // 🔥 检查是否有后端数量调整
        const responseData = response.data;
        if (responseData.adjusted_quantity && responseData.adjusted_quantity !== responseData.original_quantity) {
          console.log('🔧 后端调整了加购数量:', {
            original: responseData.original_quantity,
            adjusted: responseData.adjusted_quantity,
            min_sale_quantity: responseData.min_sale_quantity
          });
          
          // 显示调整提示
          wx.showModal({
            title: '数量已调整',
            content: response.message || `该商品最小起购量为${responseData.min_sale_quantity}${product.unit || '件'}，已为您调整数量`,
            showCancel: false,
            confirmText: '知道了'
          });
        }
        
        // 🔥 新增：通知监听器
        this._notifyChange('add', { product, quantity: responseData.adjusted_quantity || quantity });
        
        // Toast由监听器统一处理
        // wx.showToast({ title: '已加入购物车', icon: 'success' });
        return {
          success: true,
          message: '已加入购物车'
        };
      } else {
        wx.showToast({ title: '添加失败', icon: 'error' });
        return {
          success: false,
          message: '添加失败'
        };
      }
    } catch (error) {
      console.error('添加商品失败:', error);
      
      if (error.message && error.message.includes('登录')) {
        wx.showToast({ title: '请先登录', icon: 'none' });
      } else if (error.message && error.message.includes('参数错误')) {
        // 处理参数错误 - 可能是单位ID问题
        wx.showModal({
          title: '添加失败',
          content: '商品信息不完整，请刷新页面后重试',
          showCancel: false,
          confirmText: '知道了'
        });
      } else {
        wx.showToast({ title: '添加失败', icon: 'error' });
      }
      return {
        success: false,
        message: '添加失败'
      };
    }
  }

  /**
   * 获取购物车数量
   */
  async getCartCount() {
    try {
      // 检查登录状态
      if (!isLoggedIn()) {
        // 未登录时返回0
        return 0;
      }
      
      // 从API获取购物车数量
      const response = await api.getCartCount();
      const count = response?.data || 0;
      
      // 缓存购物车数量
      wx.setStorageSync('cartCount', count);
      
      return count;
    } catch (error) {
      console.error('获取购物车数量失败:', error);
      
      // 尝试从本地缓存获取
      try {
        return wx.getStorageSync('cartCount') || 0;
      } catch (cacheError) {
        return 0;
      }
    }
  }

  /**
   * 检查URL是否是绝对路径
   */
  _isAbsoluteUrl(url) {
    if (!url) return false;
    return url.startsWith('http://') || url.startsWith('https://') || url.startsWith('//');
  }
  
  /**
   * 获取基础URL
   */
  _getBaseUrl() {
    try {
      // 尝试从request-config.js获取当前环境配置
      const { getCurrentConfig } = require('./request-config');
      const config = getCurrentConfig();
      return config.baseUrl.replace('/api', '') || 'https://n8n.cdtxsp.com';
    } catch (error) {
      console.error('获取API基础URL失败:', error);
      // 默认返回生产环境URL
      return 'https://n8n.cdtxsp.com';
    }
  }

  /**
   * 更新购物车商品数量
   * @param {number} itemId 购物车项ID
   * @param {number} quantity 新数量
   */
  async updateCartItem(itemId, quantity) {
    try {
      console.log(`🛒 更新购物车商品 ${itemId} 数量为 ${quantity}`);
      
      const token = wx.getStorageSync('token');
      if (!token) {
        console.log('👤 用户未登录，无法更新购物车');
        return {
          success: false,
          message: '用户未登录'
        };
      }
      
      // 调用API更新数量
      const response = await api.request.put(`${api.API.CART_ITEM}/${itemId}`, {
        quantity
      });
      
      console.log('🛒 更新购物车商品响应:', response);
      
      // 检查是否因为低于最小起购量而被删除
      if (response && response.data && response.data.deleted) {
        console.log('🛒 商品因数量低于最小起购量被删除');
        
        // 更新本地缓存
        if (globalCartCache.items && Array.isArray(globalCartCache.items)) {
          globalCartCache.items = globalCartCache.items.filter(item => item.id !== itemId);
        }
        
        // 通知监听器
        this._notifyChange(EVENT_TYPES.REMOVE, { 
          itemId,
          reason: 'below_min_quantity'
        });
        
        return {
          success: true,
          deleted: true,
          message: '商品数量低于最小起购量，已从购物车中移除'
        };
      }
      
      // 正常更新
      // 更新本地缓存
      if (globalCartCache.items && Array.isArray(globalCartCache.items)) {
        const itemIndex = globalCartCache.items.findIndex(item => item.id === itemId);
        if (itemIndex !== -1) {
          globalCartCache.items[itemIndex].quantity = quantity;
        }
      }
      
      // 通知监听器
      this._notifyChange(EVENT_TYPES.UPDATE, { 
        itemId,
        quantity
      });
      
      return {
        success: true,
        message: '购物车商品数量已更新'
      };
    } catch (error) {
      console.error('❌ 更新购物车商品数量失败:', error);
      return {
        success: false,
        message: error.message || '更新购物车商品数量失败'
      };
    }
  }
  
  /**
   * 从购物车中移除商品
   * @param {number} itemId 购物车项ID
   */
  async removeCartItem(itemId) {
    try {
      console.log(`🛒 从购物车中移除商品 ${itemId}`);
      
      const token = wx.getStorageSync('token');
      if (!token) {
        console.log('👤 用户未登录，无法移除购物车商品');
        return {
          success: false,
          message: '用户未登录'
        };
      }
      
      // 调用API删除商品
      const response = await api.request.delete(`${api.API.CART_ITEM}/${itemId}`);
      
      console.log('🛒 移除购物车商品响应:', response);
      
      // 更新本地缓存
      if (globalCartCache.items && Array.isArray(globalCartCache.items)) {
        globalCartCache.items = globalCartCache.items.filter(item => item.id !== itemId);
      }
      
      // 通知监听器
      this._notifyChange(EVENT_TYPES.REMOVE, { 
        itemId
      });
      
      return {
        success: true,
        message: '商品已从购物车移除'
      };
    } catch (error) {
      console.error('❌ 从购物车中移除商品失败:', error);
      return {
        success: false,
        message: error.message || '从购物车中移除商品失败'
      };
    }
  }
  
  /**
   * 切换购物车商品选中状态
   * @param {number} itemId 购物车项ID
   * @param {boolean} isSelected 是否选中
   */
  async toggleCartItemSelected(itemId, isSelected) {
    try {
      console.log(`🛒 切换购物车商品 ${itemId} 选中状态为 ${isSelected}`);
      
      const token = wx.getStorageSync('token');
      if (!token) {
        console.log('👤 用户未登录，无法切换购物车商品选中状态');
        return {
          success: false,
          message: '用户未登录'
        };
      }
      
      // 调用API切换选中状态
      const response = await api.request.put(`${api.API.CART_ITEM}/${itemId}/toggle-select`, {
        is_selected: isSelected
      });
      
      console.log('🛒 切换购物车商品选中状态响应:', response);
      
      // 更新本地缓存
      if (globalCartCache.items && Array.isArray(globalCartCache.items)) {
        const itemIndex = globalCartCache.items.findIndex(item => item.id === itemId);
        if (itemIndex !== -1) {
          globalCartCache.items[itemIndex].selected = isSelected;
          globalCartCache.items[itemIndex].is_selected = isSelected;
        }
      }
      
      // 通知监听器
      this._notifyChange(EVENT_TYPES.SELECT, { 
        itemId,
        selected: isSelected
      });
      
      return {
        success: true,
        message: '商品选中状态已更新'
      };
    } catch (error) {
      console.error('❌ 切换购物车商品选中状态失败:', error);
      return {
        success: false,
        message: error.message || '切换购物车商品选中状态失败'
      };
    }
  }
  
  /**
   * 全选/取消全选
   * @param {boolean} isSelected 是否全选
   */
  async toggleAllSelected(isSelected) {
    try {
      console.log(`🛒 ${isSelected ? '全选' : '取消全选'}购物车商品`);
      
      const token = wx.getStorageSync('token');
      if (!token) {
        console.log('👤 用户未登录，无法全选/取消全选购物车商品');
        return {
          success: false,
          message: '用户未登录'
        };
      }
      
      // 调用API全选/取消全选
      const response = await api.request.put(api.API.CART_SELECT_ALL, {
        is_selected: isSelected
      });
      
      console.log('🛒 全选/取消全选购物车商品响应:', response);
      
      // 更新本地缓存
      if (globalCartCache.items && Array.isArray(globalCartCache.items)) {
        globalCartCache.items.forEach(item => {
          item.selected = isSelected;
          item.is_selected = isSelected;
        });
      }
      
      // 通知监听器
      this._notifyChange(EVENT_TYPES.SELECT_ALL, { 
        selected: isSelected
      });
      
      return {
        success: true,
        message: `已${isSelected ? '全选' : '取消全选'}购物车商品`
      };
    } catch (error) {
      console.error('❌ 全选/取消全选购物车商品失败:', error);
      return {
        success: false,
        message: error.message || '全选/取消全选购物车商品失败'
      };
    }
  }
}

// 创建统一的购物车管理器实例
const cartManager = new UnifiedCartManager();

/**
 * 导出全局函数 - 添加商品到购物车
 */
async function addToCart(params) {
  return await cartManager.addToCart(params);
}

/**
 * 导出全局函数 - 获取购物车列表
 */
async function getCartList() {
  try {
    console.log('cart-unified: 开始获取购物车列表');
    const result = await cartManager.getCartList();
    console.log('cart-unified: 获取购物车列表原始结果', result);
    
    // 确保返回一致的数据结构
    if (result && result.success) {
      // 标准化数据结构
      let items = [];
      
      if (result.data) {
        if (Array.isArray(result.data)) {
          items = result.data;
        } else if (result.data.items && Array.isArray(result.data.items)) {
          items = result.data.items;
        } else if (typeof result.data === 'object') {
          // 尝试将对象转换为数组
          items = Object.values(result.data).filter(item => 
            item && typeof item === 'object' && (item.id || item.product_id)
          );
        }
      }
      
      console.log('cart-unified: 处理后的购物车项目数量', items.length);
      
      return {
        success: true,
        data: items
      };
    }
    
    return result;
  } catch (error) {
    console.error('cart-unified: 获取购物车列表失败', error);
    return {
      success: false,
      message: error.message || '获取购物车失败',
      data: []
    };
  }
}

/**
 * 导出全局函数 - 获取购物车数量
 */
async function getCartCount() {
  return await cartManager.getCartCount();
}

/**
 * 导出全局函数 - 更新购物车数量
 */
async function updateCartQuantity(cartId, quantity) {
  return await cartManager.updateQuantity(cartId, quantity);
}

/**
 * 导出全局函数 - 从购物车中移除商品
 */
async function removeFromCart(cartId) {
  return await cartManager.removeItem(cartId);
}

/**
 * 导出全局函数 - 获取商品在购物车中的数量
 */
async function getItemQuantity(productId) {
  return await cartManager.getItemQuantity(productId);
}

/**
 * 导出全局函数 - 基于商品ID更新购物车数量
 */
async function updateCartItemByProductId(productId, quantity) {
  return await cartManager.updateQuantity(productId, quantity);
}

/**
 * 导出全局函数 - 基于商品ID从购物车中移除商品
 */
async function removeCartItemByProductId(productId) {
  return await cartManager.removeItem(productId);
}

/**
 * 导出全局函数 - 更新购物车徽标
 */
function updateBadge() {
  cartManager._updateBadge();
}

/**
 * 导出全局函数 - 添加购物车变化监听器
 * @param {string} key - 监听器唯一标识
 * @param {Function} callback - 回调函数
 * @param {Object} options - 配置选项
 */
function addListener(key, callback, options = {}) {
  cartManager.addListener(key, callback, options);
}

/**
 * 导出全局函数 - 移除购物车变化监听器
 */
function removeListener(key) {
  cartManager.removeListener(key);
}

/**
 * 导出事件类型常量
 */
const CartEvents = EVENT_TYPES;

// 导出函数和管理器实例
module.exports = {
  addToCart,
  getCartList,
  getCartCount,
  updateCartQuantity,
  removeFromCart,
  getItemQuantity,
  updateCartItemByProductId,
  removeCartItemByProductId,
  updateBadge,
  addListener,
  removeListener,
  CartEvents,  // 导出事件类型常量
  cartManager,
  onPageShow: async function() {
    return await cartManager.onPageShow();
  }
};