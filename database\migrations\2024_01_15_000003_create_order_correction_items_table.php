<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_correction_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('correction_id')->constrained('order_corrections')->onDelete('cascade')->comment('更正单ID');
            $table->foreignId('order_item_id')->constrained('order_items')->onDelete('cascade')->comment('原订单项ID');
            $table->integer('original_quantity')->comment('原数量');
            $table->integer('corrected_quantity')->comment('更正后数量');
            $table->decimal('original_weight', 8, 3)->nullable()->comment('原重量');
            $table->decimal('corrected_weight', 8, 3)->nullable()->comment('更正后重量');
            $table->decimal('original_price', 10, 2)->comment('原单价');
            $table->decimal('corrected_price', 10, 2)->comment('更正后单价');
            $table->decimal('original_total', 10, 2)->comment('原小计');
            $table->decimal('corrected_total', 10, 2)->comment('更正后小计');
            $table->decimal('difference_amount', 10, 2)->comment('差额');
            $table->string('correction_reason')->nullable()->comment('更正原因');
            $table->timestamp('created_at')->useCurrent();
            
            $table->index('correction_id');
            $table->index('order_item_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_correction_items');
    }
}; 