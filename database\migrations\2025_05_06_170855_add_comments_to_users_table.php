<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 由于Laravel的Schema构建器不支持为现有列添加注释，
        // 我们需要使用原始SQL语句来添加注释
        
        // 为merchant_name字段添加注释
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `merchant_name` VARCHAR(255) NULL COMMENT '商户名称'");
        
        // 为balance字段添加注释
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `balance` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '用户余额'");
        
        // 为points字段添加注释
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `points` INT NOT NULL DEFAULT 0 COMMENT '用户积分'");
        
        // 为joined_at字段添加注释
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `joined_at` TIMESTAMP NULL COMMENT '加入时间'");
        
        // 添加其他字段的注释
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `name` VARCHAR(255) NOT NULL COMMENT '用户名称'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `password` VARCHAR(255) NOT NULL COMMENT '密码'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `phone` VARCHAR(255) NULL COMMENT '手机号码'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `role` VARCHAR(255) NOT NULL DEFAULT 'customer' COMMENT '用户角色'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `nickname` VARCHAR(255) NULL COMMENT '用户昵称'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `avatar` VARCHAR(255) NULL COMMENT '用户头像'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `gender` INT UNSIGNED NULL DEFAULT '0' COMMENT '性别:0未知,1男,2女'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `province` VARCHAR(255) NULL COMMENT '省份'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `city` VARCHAR(255) NULL COMMENT '城市'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `country` VARCHAR(255) NULL COMMENT '国家'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `openid` VARCHAR(255) NULL COMMENT '微信openid'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `unionid` VARCHAR(255) NULL COMMENT '微信unionid'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `default_deliverer_id` BIGINT UNSIGNED NULL COMMENT '默认配送员ID'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `created_at` TIMESTAMP NULL COMMENT '创建时间'");
        DB::statement("ALTER TABLE `users` MODIFY COLUMN `updated_at` TIMESTAMP NULL COMMENT '更新时间'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 由于这是添加注释，down方法不需要执行任何操作
        // 注释是表的元数据，不会影响实际功能
    }
};
