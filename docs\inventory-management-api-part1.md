# 进销存系统API文档 - 第一部分

## 目录
1. [单位管理](#单位管理)
2. [单位转换](#单位转换)
3. [供应商管理](#供应商管理)
4. [采购订单管理](#采购订单管理)

## 单位管理

### 获取单位列表
- **URL**: `/api/units`
- **方法**: GET
- **响应**: 
  ```json
  {
    "code": 200,
    "message": "success",
    "data": [
      {
        "id": 1,
        "name": "kg",
        "display_name": "公斤",
        "category": "weight",
        "is_base": true,
        "created_at": "2023-08-01T00:00:00.000000Z",
        "updated_at": "2023-08-01T00:00:00.000000Z"
      }
    ]
  }
  ```

### 创建单位
- **URL**: `/api/units`
- **方法**: POST
- **参数**:
  ```json
  {
    "name": "kg",
    "display_name": "公斤",
    "category": "weight",
    "is_base": true
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "单位创建成功",
    "data": {
      "id": 1,
      "name": "kg",
      "display_name": "公斤",
      "category": "weight",
      "is_base": true,
      "created_at": "2023-08-01T00:00:00.000000Z",
      "updated_at": "2023-08-01T00:00:00.000000Z"
    }
  }
  ```

### 获取单位详情
- **URL**: `/api/units/{id}`
- **方法**: GET
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": 1,
      "name": "kg",
      "display_name": "公斤",
      "category": "weight",
      "is_base": true,
      "created_at": "2023-08-01T00:00:00.000000Z",
      "updated_at": "2023-08-01T00:00:00.000000Z",
      "fromConversions": [],
      "toConversions": []
    }
  }
  ```

### 更新单位
- **URL**: `/api/units/{id}`
- **方法**: PUT
- **参数**:
  ```json
  {
    "display_name": "千克",
    "category": "weight"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "单位更新成功",
    "data": {
      "id": 1,
      "name": "kg",
      "display_name": "千克",
      "category": "weight",
      "is_base": true,
      "created_at": "2023-08-01T00:00:00.000000Z",
      "updated_at": "2023-08-01T10:00:00.000000Z"
    }
  }
  ```

### 删除单位
- **URL**: `/api/units/{id}`
- **方法**: DELETE
- **响应**:
  ```json
  {
    "code": 200,
    "message": "单位删除成功",
    "data": null
  }
  ```

### 为商品设置单位
- **URL**: `/api/units/product/{productId}`
- **方法**: POST
- **参数**:
  ```json
  {
    "base_unit_id": 1,
    "units": [
      {
        "unit_id": 1,
        "conversion_factor": 1,
        "is_purchase_unit": true,
        "is_sale_unit": true,
        "is_inventory_unit": true
      },
      {
        "unit_id": 2,
        "conversion_factor": 1000,
        "is_purchase_unit": false,
        "is_sale_unit": true,
        "is_inventory_unit": false
      }
    ]
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "商品单位设置成功",
    "data": {
      "id": 1,
      "name": "产品A",
      "units": [
        {
          "id": 1,
          "name": "kg",
          "display_name": "公斤",
          "pivot": {
            "conversion_factor": 1,
            "is_purchase_unit": true,
            "is_sale_unit": true,
            "is_inventory_unit": true
          }
        },
        {
          "id": 2,
          "name": "g",
          "display_name": "克",
          "pivot": {
            "conversion_factor": 1000,
            "is_purchase_unit": false,
            "is_sale_unit": true,
            "is_inventory_unit": false
          }
        }
      ]
    }
  }
  ```

## 单位转换

### 获取单位转换列表
- **URL**: `/api/unit-conversions`
- **方法**: GET
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": [
      {
        "id": 1,
        "from_unit_id": 1,
        "to_unit_id": 2,
        "conversion_factor": 1000,
        "created_at": "2023-08-01T00:00:00.000000Z",
        "updated_at": "2023-08-01T00:00:00.000000Z",
        "fromUnit": {
          "id": 1,
          "name": "kg",
          "display_name": "公斤"
        },
        "toUnit": {
          "id": 2,
          "name": "g",
          "display_name": "克"
        }
      }
    ]
  }
  ```

### 创建单位转换
- **URL**: `/api/unit-conversions`
- **方法**: POST
- **参数**:
  ```json
  {
    "from_unit_id": 1,
    "to_unit_id": 2,
    "conversion_factor": 1000
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "单位转换创建成功",
    "data": {
      "id": 1,
      "from_unit_id": 1,
      "to_unit_id": 2,
      "conversion_factor": 1000,
      "created_at": "2023-08-01T00:00:00.000000Z",
      "updated_at": "2023-08-01T00:00:00.000000Z",
      "fromUnit": {
        "id": 1,
        "name": "kg",
        "display_name": "公斤"
      },
      "toUnit": {
        "id": 2,
        "name": "g",
        "display_name": "克"
      }
    }
  }
  ```

### 更新单位转换
- **URL**: `/api/unit-conversions/{id}`
- **方法**: PUT
- **参数**:
  ```json
  {
    "conversion_factor": 1000
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "单位转换更新成功",
    "data": {
      "id": 1,
      "from_unit_id": 1,
      "to_unit_id": 2,
      "conversion_factor": 1000,
      "created_at": "2023-08-01T00:00:00.000000Z",
      "updated_at": "2023-08-01T10:00:00.000000Z",
      "fromUnit": {
        "id": 1,
        "name": "kg",
        "display_name": "公斤"
      },
      "toUnit": {
        "id": 2,
        "name": "g",
        "display_name": "克"
      }
    }
  }
  ```

### 删除单位转换
- **URL**: `/api/unit-conversions/{id}`
- **方法**: DELETE
- **响应**:
  ```json
  {
    "code": 200,
    "message": "单位转换删除成功",
    "data": null
  }
  ```

## 供应商管理

### 获取供应商列表
- **URL**: `/api/suppliers`
- **方法**: GET
- **查询参数**:
  - `search`: 搜索关键词
  - `order_by`: 排序字段
  - `direction`: 排序方向(asc|desc)
  - `per_page`: 每页记录数
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "current_page": 1,
      "data": [
        {
          "id": 1,
          "name": "供应商A",
          "contact_person": "张三",
          "contact_phone": "13800138000",
          "email": "<EMAIL>",
          "address": "上海市浦东新区",
          "credit_limit": 50000,
          "current_debt": 0,
          "notes": null,
          "created_at": "2023-08-01T00:00:00.000000Z",
          "updated_at": "2023-08-01T00:00:00.000000Z"
        }
      ],
      "total": 1,
      "per_page": 15
    }
  }
  ```

### 创建供应商
- **URL**: `/api/suppliers`
- **方法**: POST
- **参数**:
  ```json
  {
    "name": "供应商A",
    "contact_person": "张三",
    "contact_phone": "13800138000",
    "email": "<EMAIL>",
    "address": "上海市浦东新区",
    "credit_limit": 50000,
    "notes": "主要供应食品原料"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "供应商创建成功",
    "data": {
      "id": 1,
      "name": "供应商A",
      "contact_person": "张三",
      "contact_phone": "13800138000",
      "email": "<EMAIL>",
      "address": "上海市浦东新区",
      "credit_limit": 50000,
      "current_debt": 0,
      "notes": "主要供应食品原料",
      "created_at": "2023-08-01T00:00:00.000000Z",
      "updated_at": "2023-08-01T00:00:00.000000Z"
    }
  }
  ```

### 获取供应商详情
- **URL**: `/api/suppliers/{id}`
- **方法**: GET
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": 1,
      "name": "供应商A",
      "contact_person": "张三",
      "contact_phone": "13800138000",
      "email": "<EMAIL>",
      "address": "上海市浦东新区",
      "credit_limit": 50000,
      "current_debt": 0,
      "notes": "主要供应食品原料",
      "created_at": "2023-08-01T00:00:00.000000Z",
      "updated_at": "2023-08-01T00:00:00.000000Z",
      "purchaseOrders": []
    }
  }
  ```

### 更新供应商
- **URL**: `/api/suppliers/{id}`
- **方法**: PUT
- **参数**:
  ```json
  {
    "contact_person": "李四",
    "contact_phone": "13900139000",
    "credit_limit": 100000
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "供应商更新成功",
    "data": {
      "id": 1,
      "name": "供应商A",
      "contact_person": "李四",
      "contact_phone": "13900139000",
      "email": "<EMAIL>",
      "address": "上海市浦东新区",
      "credit_limit": 100000,
      "current_debt": 0,
      "notes": "主要供应食品原料",
      "created_at": "2023-08-01T00:00:00.000000Z",
      "updated_at": "2023-08-01T10:00:00.000000Z"
    }
  }
  ```

### 删除供应商
- **URL**: `/api/suppliers/{id}`
- **方法**: DELETE
- **响应**:
  ```json
  {
    "code": 200,
    "message": "供应商删除成功",
    "data": null
  }
  ```

## 采购订单管理

### 获取采购订单列表
- **URL**: `/api/purchase-orders`
- **方法**: GET
- **查询参数**:
  - `search`: 搜索关键词
  - `status`: 订单状态
  - `date_from`: 起始日期
  - `date_to`: 结束日期
  - `order_by`: 排序字段
  - `direction`: 排序方向(asc|desc)
  - `per_page`: 每页记录数
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "current_page": 1,
      "data": [
        {
          "id": 1,
          "order_number": "PO-20230801-1234",
          "supplier_id": 1,
          "status": "draft",
          "order_date": "2023-08-01",
          "expected_delivery_date": "2023-08-10",
          "warehouse_id": 1,
          "total_amount": 5000,
          "paid_amount": 0,
          "notes": null,
          "created_by": 1,
          "approved_by": null,
          "approved_at": null,
          "created_at": "2023-08-01T00:00:00.000000Z",
          "updated_at": "2023-08-01T00:00:00.000000Z",
          "supplier": {
            "id": 1,
            "name": "供应商A"
          },
          "warehouse": {
            "id": 1,
            "location": "上海仓"
          }
        }
      ],
      "total": 1,
      "per_page": 15
    }
  }
  ```

### 创建采购订单
- **URL**: `/api/purchase-orders`
- **方法**: POST
- **参数**:
  ```json
  {
    "supplier_id": 1,
    "warehouse_id": 1,
    "order_date": "2023-08-01",
    "expected_delivery_date": "2023-08-10",
    "notes": "紧急订单",
    "items": [
      {
        "product_id": 1,
        "quantity": 10,
        "unit_id": 1,
        "unit_price": 500
      },
      {
        "product_id": 2,
        "quantity": 5,
        "unit_id": 2,
        "unit_price": 200
      }
    ]
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "采购订单创建成功",
    "data": {
      "id": 1,
      "order_number": "PO-20230801-1234",
      "supplier_id": 1,
      "status": "draft",
      "order_date": "2023-08-01",
      "expected_delivery_date": "2023-08-10",
      "warehouse_id": 1,
      "total_amount": 6000,
      "paid_amount": 0,
      "notes": "紧急订单",
      "created_by": 1,
      "approved_by": null,
      "approved_at": null,
      "created_at": "2023-08-01T00:00:00.000000Z",
      "updated_at": "2023-08-01T00:00:00.000000Z",
      "supplier": {
        "id": 1,
        "name": "供应商A"
      },
      "warehouse": {
        "id": 1,
        "location": "上海仓"
      },
      "items": [
        {
          "id": 1,
          "purchase_order_id": 1,
          "product_id": 1,
          "quantity": 10,
          "unit_id": 1,
          "unit_price": 500,
          "received_quantity": 0,
          "total_price": 5000,
          "product": {
            "id": 1,
            "name": "产品A"
          },
          "unit": {
            "id": 1,
            "name": "kg",
            "display_name": "公斤"
          }
        },
        {
          "id": 2,
          "purchase_order_id": 1,
          "product_id": 2,
          "quantity": 5,
          "unit_id": 2,
          "unit_price": 200,
          "received_quantity": 0,
          "total_price": 1000,
          "product": {
            "id": 2,
            "name": "产品B"
          },
          "unit": {
            "id": 2,
            "name": "box",
            "display_name": "箱"
          }
        }
      ]
    }
  }
  ```

### 获取采购订单详情
- **URL**: `/api/purchase-orders/{id}`
- **方法**: GET
- **响应**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "id": 1,
      "order_number": "PO-20230801-1234",
      "supplier_id": 1,
      "status": "draft",
      "order_date": "2023-08-01",
      "expected_delivery_date": "2023-08-10",
      "warehouse_id": 1,
      "total_amount": 6000,
      "paid_amount": 0,
      "notes": "紧急订单",
      "created_by": 1,
      "approved_by": null,
      "approved_at": null,
      "created_at": "2023-08-01T00:00:00.000000Z",
      "updated_at": "2023-08-01T00:00:00.000000Z",
      "supplier": {
        "id": 1,
        "name": "供应商A",
        "contact_person": "张三",
        "contact_phone": "13800138000"
      },
      "warehouse": {
        "id": 1,
        "location": "上海仓"
      },
      "items": [
        {
          "id": 1,
          "purchase_order_id": 1,
          "product_id": 1,
          "quantity": 10,
          "unit_id": 1,
          "unit_price": 500,
          "received_quantity": 0,
          "total_price": 5000,
          "product": {
            "id": 1,
            "name": "产品A"
          },
          "unit": {
            "id": 1,
            "name": "kg",
            "display_name": "公斤"
          }
        }
      ],
      "creator": {
        "id": 1,
        "name": "管理员"
      },
      "approver": null,
      "inventoryTransactions": []
    }
  }
  ```

### 更新采购订单
- **URL**: `/api/purchase-orders/{id}`
- **方法**: PUT
- **参数**:
  ```json
  {
    "expected_delivery_date": "2023-08-15",
    "notes": "更新为常规订单"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "采购订单更新成功",
    "data": {
      "id": 1,
      "order_number": "PO-20230801-1234",
      "supplier_id": 1,
      "status": "draft",
      "order_date": "2023-08-01",
      "expected_delivery_date": "2023-08-15",
      "warehouse_id": 1,
      "total_amount": 6000,
      "paid_amount": 0,
      "notes": "更新为常规订单",
      "created_by": 1,
      "approved_by": null,
      "approved_at": null,
      "created_at": "2023-08-01T00:00:00.000000Z",
      "updated_at": "2023-08-01T10:00:00.000000Z"
    }
  }
  ```

### 提交采购订单
- **URL**: `/api/purchase-orders/{id}/submit`
- **方法**: POST
- **响应**:
  ```json
  {
    "code": 200,
    "message": "采购订单已提交",
    "data": {
      "id": 1,
      "status": "submitted",
      "updated_at": "2023-08-01T11:00:00.000000Z"
    }
  }
  ```

### 审批采购订单
- **URL**: `/api/purchase-orders/{id}/approve`
- **方法**: POST
- **响应**:
  ```json
  {
    "code": 200,
    "message": "采购订单已审批",
    "data": {
      "id": 1,
      "status": "approved",
      "approved_by": 1,
      "approved_at": "2023-08-01T12:00:00.000000Z",
      "updated_at": "2023-08-01T12:00:00.000000Z"
    }
  }
  ```

### 取消采购订单
- **URL**: `/api/purchase-orders/{id}/cancel`
- **方法**: POST
- **响应**:
  ```json
  {
    "code": 200,
    "message": "采购订单已取消",
    "data": {
      "id": 1,
      "status": "canceled",
      "updated_at": "2023-08-01T13:00:00.000000Z"
    }
  }
  ```

### 处理采购收货
- **URL**: `/api/purchase-orders/{id}/receive`
- **方法**: POST
- **参数**:
  ```json
  {
    "items": [
      {
        "purchase_item_id": 1,
        "received_quantity": 5
      },
      {
        "purchase_item_id": 2,
        "received_quantity": 2
      }
    ],
    "notes": "部分收货"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "采购收货成功",
    "data": {
      "id": 1,
      "status": "partial_received",
      "items": [
        {
          "id": 1,
          "received_quantity": 5,
          "quantity": 10
        },
        {
          "id": 2,
          "received_quantity": 2,
          "quantity": 5
        }
      ],
      "inventoryTransactions": [
        {
          "id": 1,
          "transaction_type_id": 1,
          "product_id": 1,
          "warehouse_id": 1,
          "quantity": 5,
          "unit_id": 1,
          "status": "completed"
        },
        {
          "id": 2,
          "transaction_type_id": 1,
          "product_id": 2,
          "warehouse_id": 1,
          "quantity": 2,
          "unit_id": 2,
          "status": "completed"
        }
      ]
    }
  }
  ``` 