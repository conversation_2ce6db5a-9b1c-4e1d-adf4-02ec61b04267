<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        
        // 每天凌晨2点清理过期付款链接
        $schedule->command('payment-links:cleanup-expired')
                 ->dailyAt('02:00')
                 ->withoutOverlapping()
                 ->runInBackground();
                 
        // 每10分钟清理超时的支付处理状态
        $schedule->command('payment:cleanup-timeout')
                 ->everyTenMinutes()
                 ->withoutOverlapping()
                 ->runInBackground();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');
        
        require base_path('routes/console.php');
    }
}
