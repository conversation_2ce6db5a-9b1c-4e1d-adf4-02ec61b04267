<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // 添加配送相关字段
            $table->string('delivery_method')->nullable()->after('payment_no')->comment('配送方式：express(快递), self_pickup(自提), same_day(当日达), scheduled(定时达)');
            $table->date('delivery_date')->nullable()->after('delivery_method')->comment('期望配送日期');
            $table->string('delivery_time')->nullable()->after('delivery_date')->comment('期望配送时间段：morning(上午), afternoon(下午), evening(晚上), custom(自定义)');
            $table->string('delivery_time_custom')->nullable()->after('delivery_time')->comment('自定义配送时间段，如09:00-12:00');
            $table->text('delivery_notes')->nullable()->after('delivery_time_custom')->comment('配送备注信息');
            $table->timestamp('delivery_arranged_at')->nullable()->after('delivery_notes')->comment('配送安排时间');
            $table->unsignedBigInteger('delivery_arranged_by')->nullable()->after('delivery_arranged_at')->comment('配送安排人员ID');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // 移除配送相关字段
            $table->dropColumn([
                'delivery_method',
                'delivery_date',
                'delivery_time',
                'delivery_time_custom',
                'delivery_notes',
                'delivery_arranged_at',
                'delivery_arranged_by'
            ]);
        });
    }
};
