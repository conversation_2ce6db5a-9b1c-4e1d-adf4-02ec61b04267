<?php

namespace App\Models;

use App\Employee\Models\Employee as ModularEmployee;

/**
 * 员工模型类 - 兼容层
 * 
 * 这是一个兼容层，继承自模块化的Employee模型
 * 保持向后兼容性，允许现有代码继续使用App\Models\Employee命名空间
 * 
 * 在项目完全迁移到模块化结构后，这个类可以被移除
 */
class Employee extends ModularEmployee
{
    // 保持向后兼容，同时记录使用旧命名空间的情况
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        
        // 可以在开发环境中添加日志，以便跟踪对旧命名空间的使用
        if (config('app.env') === 'local' || config('app.env') === 'development') {
            \Illuminate\Support\Facades\Log::debug('使用了旧的Employee命名空间', [
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
            ]);
        }
    }
} 