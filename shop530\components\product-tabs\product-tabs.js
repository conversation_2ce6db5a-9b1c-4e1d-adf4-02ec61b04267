const api = require('../../utils/api');

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 标签页数据
    tabs: {
      type: Array,
      value: []
    },
    // 当前激活的标签索引
    activeTab: {
      type: Number,
      value: 0
    },
    // 吸顶偏移量（废弃，使用 stickyOffset）
    offsetTop: {
      type: Number,
      value: 180
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 是否处于吸顶状态（由父组件控制）
    isSticky: {
      type: Boolean,
      value: false
    },
    // 吸顶时的偏移量（由父组件传入）
    stickyOffset: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 当前显示的商品列表
    currentProducts: [],
    // 标签切换加载状态
    tabSwitching: false,
    // 数据缓存
    tabDataCache: {},
    // 性能优化相关
    isDataLoading: false,
    lastActiveTab: -1,
    // tabs 高度（用于占位）
    tabsHeight: 50
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 标签页切换事件 - 修复事件处理和错误处理
     */
    async onTabChange(event) {
      try {
        const eventDetail = event.detail || {};
        const newActiveTab = eventDetail.index;
        
        // 验证事件数据
        if (typeof newActiveTab !== 'number' || newActiveTab < 0) {
          console.warn('⚠️ 标签切换事件数据异常:', eventDetail);
          return;
        }
        
        const currentTab = this.data.tabs[newActiveTab];
        
        if (!currentTab || this.data.isDataLoading) {
          console.warn('⚠️ 标签不存在或正在加载中:', { currentTab, isDataLoading: this.data.isDataLoading });
          return;
        }

        console.log(`📋 切换到标签: ${currentTab.name} (${currentTab.type})`);

        // 防止重复切换
        if (newActiveTab === this.data.lastActiveTab) {
          console.log('📋 重复切换，忽略');
          return;
        }

        // 显示切换加载状态
        this.setData({
          tabSwitching: true,
          isDataLoading: true,
          lastActiveTab: newActiveTab
        });

        // 触发父组件的标签切换事件
        this.triggerEvent('tabChange', {
          index: newActiveTab,
          tab: currentTab
        });

        // 加载对应标签的数据
        await this.loadTabData(currentTab.type, newActiveTab);

      } catch (error) {
        console.error('❌ 标签切换失败:', error);
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      } finally {
        // 隐藏加载状态
        setTimeout(() => {
          this.setData({
            tabSwitching: false,
            isDataLoading: false
          });
        }, 300);
      }
    },

    /**
     * 加载标签页数据
     */
    async loadTabData(type, tabIndex) {
      try {
        // 检查缓存
        if (this.data.tabDataCache[type]) {
          console.log(`📋 使用缓存数据: ${type}`);
          this.setData({
            currentProducts: this.data.tabDataCache[type]
          });
          return;
        }

        let products = [];

        if (type === 'all') {
          // 加载全部商品
          products = await this.loadAllProducts();
        } else {
          // 加载特定标签商品
          products = await this.loadProductsByTag(type);
        }

        // 缓存数据
        this.setData({
          [`tabDataCache.${type}`]: products,
          currentProducts: products
        });

        console.log(`📋 ${type}标签数据加载完成: ${products.length}个商品`);

      } catch (error) {
        console.error(`❌ 加载${type}标签数据失败:`, error);
        this.setData({
          currentProducts: []
        });
      }
    },

    /**
     * 加载全部商品（合并所有标签）
     */
    async loadAllProducts() {
      try {
        const allTags = this.data.tabs.slice(1); // 跳过"全部"标签
        
        if (!allTags || allTags.length === 0) {
          // 没有其他标签，加载默认商品
          const result = await api.api.getProducts({
            per_page: 20,
            with: 'images,category,tags'
          });
          return result?.data?.data || [];
        }

        // 并行加载所有标签的商品
        const promises = allTags.map(async (tag) => {
          try {
            const result = await api.api.getProductsByTag(tag.type, {
              per_page: 8 // 每个标签取8个商品
            });
            return result?.data || [];
          } catch (error) {
            console.error(`❌ 加载${tag.type}标签商品失败:`, error);
            return [];
          }
        });

        const results = await Promise.all(promises);
        const allProducts = [];
        
        // 合并所有商品
        results.forEach(products => {
          allProducts.push(...products);
        });

        // 去重并随机排序
        const uniqueProducts = this.removeDuplicateProducts(allProducts);
        return this.shuffleArray(uniqueProducts);

      } catch (error) {
        console.error('❌ 加载全部商品失败:', error);
        return [];
      }
    },

    /**
     * 根据标签加载商品
     */
    async loadProductsByTag(tagType) {
      try {
        const result = await api.api.getProductsByTag(tagType, {
          per_page: 20
        });
        return result?.data || [];
      } catch (error) {
        console.error(`❌ 加载${tagType}标签商品失败:`, error);
        return [];
      }
    },

    /**
     * 去重商品（根据ID）
     */
    removeDuplicateProducts(products) {
      const seen = new Set();
      return products.filter(product => {
        if (seen.has(product.id)) {
          return false;
        }
        seen.add(product.id);
        return true;
      });
    },

    /**
     * 随机打乱数组
     */
    shuffleArray(array) {
      const shuffled = [...array];
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }
      return shuffled;
    },

    /**
     * 商品点击事件 - 修复事件传递
     */
    onProductTap(e) {
      try {
        const detail = e.detail || {};
        if (detail.product && detail.product.id) {
          this.triggerEvent('productTap', detail);
        } else {
          console.warn('⚠️ 商品点击事件数据异常:', detail);
        }
      } catch (error) {
        console.error('❌ 商品点击事件处理失败:', error);
      }
    },

    /**
     * 添加到购物车事件 - 修复事件传递
     */
    onAddToCart(e) {
      try {
        const detail = e.detail || {};
        if (detail.product) {
          this.triggerEvent('addToCart', detail);
        } else {
          console.warn('⚠️ 添加购物车事件数据异常:', detail);
        }
      } catch (error) {
        console.error('❌ 添加购物车事件处理失败:', error);
      }
    },

    /**
     * 从购物车移除事件 - 修复事件传递
     */
    onRemoveFromCart(e) {
      try {
        const detail = e.detail || {};
        if (detail.product) {
          this.triggerEvent('removeFromCart', detail);
        } else {
          console.warn('⚠️ 移除购物车事件数据异常:', detail);
        }
      } catch (error) {
        console.error('❌ 移除购物车事件处理失败:', error);
      }
    },

    /**
     * 商品图片加载事件 - 修复事件传递
     */
    onProductImageLoad(e) {
      try {
        this.triggerEvent('imageLoad', e.detail || {});
      } catch (error) {
        console.error('❌ 图片加载事件处理失败:', error);
      }
    },

    /**
     * 商品图片错误事件 - 修复事件传递
     */
    onProductImageError(e) {
      try {
        this.triggerEvent('imageError', e.detail || {});
      } catch (error) {
        console.error('❌ 图片错误事件处理失败:', error);
      }
    },

    /**
     * 购物车动画事件 - 修复事件传递
     */
    onCartAnimation(e) {
      try {
        this.triggerEvent('cartAnimation', e.detail || {});
      } catch (error) {
        console.error('❌ 购物车动画事件处理失败:', error);
      }
    },

    /**
     * 初始化加载数据
     */
    async initData() {
      if (this.data.tabs.length > 0) {
        const firstTab = this.data.tabs[0];
        await this.loadTabData(firstTab.type, 0);
      }
    },

    /**
     * 清理缓存
     */
    clearCache() {
      this.setData({
        tabDataCache: {}
      });
    },

    /**
     * 计算 tabs 高度
     */
    calculateTabsHeight() {
      setTimeout(() => {
        const query = this.createSelectorQuery();
        query.select('#tabs-wrapper').boundingClientRect();
        query.exec((res) => {
          if (res[0] && res[0].height) {
            this.setData({
              tabsHeight: res[0].height
            });
            console.log('📏 Tabs高度计算:', res[0].height + 'px');
          }
        });
      }, 100);
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('📋 product-tabs 组件已挂载');
      // 计算 tabs 高度
      this.calculateTabsHeight();
    },

    detached() {
      console.log('📋 product-tabs 组件已销毁');
      // 清理缓存
      this.clearCache();
      // 清理定时器
      if (this.stickyTimer) {
        clearTimeout(this.stickyTimer);
        this.stickyTimer = null;
      }
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面显示时可以刷新数据
    },

    hide() {
      // 页面隐藏时可以暂停一些操作
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'tabs': function(newTabs) {
      if (newTabs && newTabs.length > 0) {
        // 标签数据更新时，初始化加载数据
        this.initData();
      }
    },

    'activeTab': function(newActiveTab) {
      // 外部改变activeTab时，同步更新
      if (newActiveTab !== this.data.lastActiveTab && this.data.tabs[newActiveTab]) {
        const currentTab = this.data.tabs[newActiveTab];
        this.loadTabData(currentTab.type, newActiveTab);
      }
    }
  }
}); 