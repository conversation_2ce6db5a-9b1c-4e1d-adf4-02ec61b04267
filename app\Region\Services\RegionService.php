<?php

namespace App\Region\Services;

use App\Region\Models\Region;
use App\Region\Repositories\RegionRepository;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class RegionService
{
    /**
     * @var RegionRepository
     */
    protected $regionRepository;
    
    /**
     * 构造函数
     *
     * @param RegionRepository $regionRepository
     */
    public function __construct(RegionRepository $regionRepository)
    {
        $this->regionRepository = $regionRepository;
    }
    
    /**
     * 获取区域树结构
     *
     * @param int $parentId 父级ID
     * @param array $conditions 条件
     * @return Collection
     */
    public function getTree($parentId = 0, array $conditions = [])
    {
        return Region::getTree($parentId, $conditions);
    }
    
    /**
     * 获取区域列表（分页）
     *
     * @param array $filters 过滤条件
     * @param int $perPage 每页数量
     * @return LengthAwarePaginator
     */
    public function getRegions($filters = [], $perPage = 10)
    {
        return $this->regionRepository->getRegions($filters, $perPage);
    }
    
    /**
     * 创建区域
     *
     * @param array $data 区域数据
     * @return Region
     */
    public function createRegion(array $data)
    {
        // 处理全路径名称
        if (isset($data['parent_id']) && $data['parent_id'] > 0) {
            $parent = Region::find($data['parent_id']);
            if ($parent) {
                $data['full_name'] = $parent->full_name . ' > ' . $data['name'];
            } else {
                $data['full_name'] = $data['name'];
            }
        } else {
            $data['full_name'] = $data['name'];
        }
        
        return $this->regionRepository->create($data);
    }
    
    /**
     * 更新区域
     *
     * @param int $id 区域ID
     * @param array $data 区域数据
     * @return Region
     */
    public function updateRegion($id, array $data)
    {
        $region = $this->regionRepository->findOrFail($id);
        
        // 不允许将自己设为自己的父级
        if (isset($data['parent_id']) && $data['parent_id'] == $id) {
            throw new \InvalidArgumentException('区域不能成为自己的父级');
        }
        
        // 不允许将区域设为其后代的父级
        if (isset($data['parent_id']) && $data['parent_id'] > 0) {
            $descendantIds = $region->getAllDescendants()->pluck('id')->toArray();
            if (in_array($data['parent_id'], $descendantIds)) {
                throw new \InvalidArgumentException('区域不能成为其后代的父级');
            }
        }
        
        // 处理全路径名称
        if (isset($data['name']) || isset($data['parent_id'])) {
            $name = $data['name'] ?? $region->name;
            $parentId = $data['parent_id'] ?? $region->parent_id;
            
            if ($parentId > 0) {
                $parent = Region::find($parentId);
                if ($parent) {
                    $data['full_name'] = $parent->full_name . ' > ' . $name;
                } else {
                    $data['full_name'] = $name;
                }
            } else {
                $data['full_name'] = $name;
            }
            
            // 更新子区域的full_name
            if (isset($data['name']) && $region->name != $data['name']) {
                $this->updateDescendantsFullName($region);
            }
        }
        
        return $this->regionRepository->update($region, $data);
    }
    
    /**
     * 删除区域
     *
     * @param int $id 区域ID
     * @return bool
     */
    public function deleteRegion($id)
    {
        $region = $this->regionRepository->findOrFail($id);
        
        // 检查是否有子区域
        if ($region->hasChildren()) {
            throw new \InvalidArgumentException('不能删除有子区域的区域');
        }
        
        // 检查是否有关联的价格
        if ($region->prices()->count() > 0) {
            throw new \InvalidArgumentException('不能删除有关联价格的区域');
        }
        
        return $this->regionRepository->delete($region);
    }
    
    /**
     * 获取区域的祖先
     *
     * @param int $id 区域ID
     * @return Collection
     */
    public function getAncestors($id)
    {
        $region = $this->regionRepository->findOrFail($id);
        return $region->getAncestors();
    }
    
    /**
     * 获取区域的后代
     *
     * @param int $id 区域ID
     * @param bool $activeOnly 是否只获取激活的区域
     * @return Collection
     */
    public function getDescendants($id, $activeOnly = false)
    {
        $region = $this->regionRepository->findOrFail($id);
        return $region->getAllDescendants($activeOnly);
    }
    
    /**
     * 获取区域的面包屑
     *
     * @param int $id 区域ID
     * @return Collection
     */
    public function getBreadcrumb($id)
    {
        $region = $this->regionRepository->findOrFail($id);
        return $region->getBreadcrumb();
    }
    
    /**
     * 更新后代区域的全路径名称
     *
     * @param Region $region
     * @return void
     */
    protected function updateDescendantsFullName(Region $region)
    {
        $children = $region->children;
        
        foreach ($children as $child) {
            $child->full_name = $region->full_name . ' > ' . $child->name;
            $child->save();
            
            // 递归更新子区域
            $this->updateDescendantsFullName($child);
        }
    }
    
    /**
     * 根据坐标查找最近的区域
     *
     * @param float $latitude 纬度
     * @param float $longitude 经度
     * @param int|null $level 区域级别，可选
     * @param bool $onlyActive 是否只查询激活的区域
     * @param int $limit 返回记录数量限制
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function findNearestRegions($latitude, $longitude, $level = null, $onlyActive = true, $limit = 5)
    {
        $query = Region::query()
            ->whereNotNull('latitude')
            ->whereNotNull('longitude');
            
        if ($level !== null) {
            $query->where('level', $level);
        }
        
        if ($onlyActive) {
            $query->where('status', true);
        }
        
        // 使用Haversine公式计算距离（假设地球是个球体）
        $haversine = "(
            6371 * acos(
                cos(radians($latitude)) 
                * cos(radians(latitude)) 
                * cos(radians(longitude) - radians($longitude)) 
                + sin(radians($latitude)) 
                * sin(radians(latitude))
            )
        )";
        
        $query->selectRaw("*, $haversine as distance")
              ->orderByRaw('distance')
              ->limit($limit);
              
        return $query->get();
    }
    
    /**
     * 查找给定范围内的区域
     *
     * @param float $latitude 中心点纬度
     * @param float $longitude 中心点经度
     * @param float $radiusKm 半径（公里）
     * @param int|null $level 区域级别，可选
     * @param bool $onlyActive 是否只查询激活的区域
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function findRegionsWithinRadius($latitude, $longitude, $radiusKm, $level = null, $onlyActive = true)
    {
        $query = Region::query()
            ->whereNotNull('latitude')
            ->whereNotNull('longitude');
            
        if ($level !== null) {
            $query->where('level', $level);
        }
        
        if ($onlyActive) {
            $query->where('status', true);
        }
        
        // 使用Haversine公式计算距离（假设地球是个球体）
        $haversine = "(
            6371 * acos(
                cos(radians($latitude)) 
                * cos(radians(latitude)) 
                * cos(radians(longitude) - radians($longitude)) 
                + sin(radians($latitude)) 
                * sin(radians(latitude))
            )
        )";
        
        $query->selectRaw("*, $haversine as distance")
              ->havingRaw("distance < ?", [$radiusKm]);
              
        return $query->get();
    }
    
    /**
     * 检查点是否在区域边界内
     * 
     * @param float $latitude 点的纬度
     * @param float $longitude 点的经度
     * @param int $regionId 区域ID
     * @return bool
     */
    public function isPointInRegion($latitude, $longitude, $regionId)
    {
        $region = $this->regionRepository->findOrFail($regionId);
        
        // 如果区域没有边界信息，无法判断
        if (empty($region->boundary)) {
            return false;
        }
        
        // 实现射线法（Ray Casting Algorithm）检查点是否在多边形内
        // 这是一个简化实现，真实应用可能需要更复杂的地理空间库
        $vertices = $region->boundary;
        $intersections = 0;
        $vertexCount = count($vertices);
        
        for ($i = 0, $j = $vertexCount - 1; $i < $vertexCount; $j = $i++) {
            $vertexI = $vertices[$i];
            $vertexJ = $vertices[$j];
            
            if (
                (($vertexI['lat'] > $latitude) != ($vertexJ['lat'] > $latitude)) &&
                ($longitude < ($vertexJ['lng'] - $vertexI['lng']) * ($latitude - $vertexI['lat']) / ($vertexJ['lat'] - $vertexI['lat']) + $vertexI['lng'])
            ) {
                $intersections++;
            }
        }
        
        // 如果交点数为奇数，则点在多边形内
        return ($intersections % 2) === 1;
    }
} 