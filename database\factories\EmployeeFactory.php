<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Employee>
 */
class EmployeeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory()->merchant(),
            'employee_type' => $this->faker->randomElement(['warehouse_manager', 'delivery_person', 'crm_manager']),
        ];
    }
    
    /**
     * 设置员工类型为仓库管理员
     */
    public function warehouseManager(): static
    {
        return $this->state(fn (array $attributes) => [
            'employee_type' => 'warehouse_manager',
        ]);
    }
    
    /**
     * 设置员工类型为配送员
     */
    public function deliveryPerson(): static
    {
        return $this->state(fn (array $attributes) => [
            'employee_type' => 'delivery_person',
        ]);
    }
    
    /**
     * 设置员工类型为CRM管理员
     */
    public function crmManager(): static
    {
        return $this->state(fn (array $attributes) => [
            'employee_type' => 'crm_manager',
        ]);
    }
} 