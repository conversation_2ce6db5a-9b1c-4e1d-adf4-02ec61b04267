<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_discounts', function (Blueprint $table) {
            $table->id();
            $table->string('payment_method', 20)->comment('支付方式：wechat, alipay, cod, bank, cash');
            $table->enum('discount_type', ['fixed_amount', 'percentage', 'none'])->default('fixed_amount')->comment('优惠类型');
            $table->decimal('discount_value', 10, 2)->default(0)->comment('优惠值：固定金额或百分比');
            $table->decimal('min_amount', 10, 2)->default(0)->comment('最低消费金额');
            $table->decimal('max_discount', 10, 2)->nullable()->comment('最大优惠金额（百分比优惠时使用）');
            $table->boolean('status')->default(true)->comment('启用状态');
            $table->datetime('start_time')->nullable()->comment('开始时间');
            $table->datetime('end_time')->nullable()->comment('结束时间');
            $table->string('description')->nullable()->comment('优惠描述');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();
            
            // 索引
            $table->unique(['payment_method', 'start_time', 'end_time'], 'unique_payment_discount');
            $table->index(['payment_method', 'status']);
            $table->index(['start_time', 'end_time']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_discounts');
    }
};
