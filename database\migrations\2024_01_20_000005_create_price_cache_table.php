<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('price_cache', function (Blueprint $table) {
            $table->id();
            $table->string('cache_key', 255)->unique()->comment('缓存键');
            $table->unsignedBigInteger('product_id')->comment('商品ID');
            $table->unsignedBigInteger('user_id')->nullable()->comment('用户ID');
            $table->unsignedBigInteger('region_id')->nullable()->comment('区域ID');
            $table->json('price_data')->comment('价格数据');
            $table->timestamp('expires_at')->comment('过期时间');
            
            $table->timestamps();
            
            // 索引优化
            $table->index(['product_id', 'user_id', 'region_id']);
            $table->index('expires_at');
            
            // 外键约束
            $table->foreign('product_id')->references('id')->on('products');
            $table->foreign('user_id')->references('id')->on('users');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('price_cache');
    }
}; 