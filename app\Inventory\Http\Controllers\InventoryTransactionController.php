<?php

namespace App\Inventory\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Inventory\Models\Inventory;
use App\Inventory\Models\InventoryTransaction;
use App\Inventory\Models\InventoryTransactionType;
use App\Product\Models\Product;
use App\Warehouse\Models\Warehouse;
use App\Unit\Services\UnitService;
use App\Inventory\Services\CostPriceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;

class InventoryTransactionController extends Controller
{
    /**
     * 单位服务
     *
     * @var UnitService
     */
    protected $unitService;
    
    /**
     * 成本价服务
     *
     * @var CostPriceService
     */
    private $costPriceService;
    
    /**
     * 构造函数
     *
     * @param UnitService $unitService
     * @param CostPriceService $costPriceService
     */
    public function __construct(UnitService $unitService, CostPriceService $costPriceService)
    {
        $this->unitService = $unitService;
        $this->costPriceService = $costPriceService;
    }
    
    /**
     * 获取库存事务列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = InventoryTransaction::with(['transactionType', 'product', 'warehouse', 'unit']);
        
        // 事务类型筛选
        if ($request->has('transaction_type_id')) {
            $query->where('transaction_type_id', $request->transaction_type_id);
        }
        
        // 商品筛选
        if ($request->has('product_id')) {
            $query->where('product_id', $request->product_id);
        }
        
        // 仓库筛选
        if ($request->has('warehouse_id')) {
            $query->where('warehouse_id', $request->warehouse_id);
        }
        
        // 日期范围筛选
        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        
        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }
        
        // 状态筛选
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }
        
        // 排序
        $orderBy = $request->order_by ?? 'created_at';
        $direction = $request->direction ?? 'desc';
        $query->orderBy($orderBy, $direction);
        
        $transactions = $query->paginate($request->per_page ?? 15);
        
        return response()->json(ApiResponse::success($transactions));
    }

    /**
     * 创建库存事务
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'transaction_type_id' => 'required|exists:inventory_transaction_types,id',
            'product_id' => 'required|exists:products,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'quantity' => 'required|numeric|not_in:0',
            'unit_id' => 'required|exists:units,id',
            'unit_price' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            DB::beginTransaction();
            
            $transactionType = InventoryTransactionType::findOrFail($request->transaction_type_id);
            
            // 计算总金额
            $totalAmount = $request->has('unit_price')
                ? $request->quantity * $request->unit_price
                : null;
            
            // 创建库存事务
            $transaction = InventoryTransaction::create([
                'transaction_type_id' => $request->transaction_type_id,
                'product_id' => $request->product_id,
                'warehouse_id' => $request->warehouse_id,
                'quantity' => $request->quantity * $transactionType->effect_direction,
                'unit_id' => $request->unit_id,
                'unit_price' => $request->unit_price,
                'total_amount' => $totalAmount,
                'status' => $request->has('status') ? $request->status : 'completed',
                'notes' => $request->notes,
                'created_by' => auth()->id() ?: throw new \Exception('用户未登录，无法创建库存事务'),
                'updated_by' => auth()->id() ?: throw new \Exception('用户未登录，无法创建库存事务'),
            ]);
            
            // 如果事务状态为已完成，则应用到库存
            if ($transaction->status === 'completed' && $transactionType->affects_inventory) {
                $transaction->applyToInventory();
            }
            
            DB::commit();
            
            return response()->json(ApiResponse::success($transaction->load(['transactionType', 'product', 'warehouse', 'unit']), '库存事务创建成功'), 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('库存事务创建失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取库存事务详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $transaction = InventoryTransaction::with([
            'transactionType', 
            'product', 
            'warehouse', 
            'unit', 
            'creator', 
            'updater'
        ])->findOrFail($id);
        
        return response()->json(ApiResponse::success($transaction));
    }

    /**
     * 完成待处理的库存事务
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function complete(Request $request, $id)
    {
        $transaction = InventoryTransaction::findOrFail($id);
        
        if ($transaction->status !== 'pending') {
            return response()->json(ApiResponse::error('只有待处理的库存事务可以标记为完成', 422), 422);
        }
        
        try {
            DB::beginTransaction();
            
            $transaction->status = 'completed';
            $transaction->updated_by = auth()->id() ?: throw new \Exception('用户未登录，无法完成库存事务');
            $transaction->save();
            
            // 应用到库存
            if ($transaction->transactionType->affects_inventory) {
                $transaction->applyToInventory();
            }
            
            // 如果是采购入库事务，更新商品成本价
            $this->costPriceService->updateCostPriceOnInbound($transaction);
            
            DB::commit();
            
            return response()->json(ApiResponse::success($transaction, '库存事务已完成'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('完成库存事务失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 取消库存事务
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel(Request $request, $id)
    {
        $transaction = InventoryTransaction::findOrFail($id);
        
        if (!in_array($transaction->status, ['draft', 'pending'])) {
            return response()->json(ApiResponse::error('只有草稿或待处理的库存事务可以取消', 422), 422);
        }
        
        $transaction->update([
            'status' => 'canceled',
            'updated_by' => auth()->id() ?: throw new \Exception('用户未登录，无法取消库存事务'),
        ]);
        
        return response()->json(ApiResponse::success($transaction, '库存事务已取消'));
    }

    /**
     * 获取商品在指定仓库的库存
     *
     * @param Request $request
     * @param int $productId
     * @param int $warehouseId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductInventory(Request $request, $productId, $warehouseId)
    {
        try {
            $product = Product::with(['baseUnit', 'units'])->findOrFail($productId);
            $warehouse = Warehouse::findOrFail($warehouseId);
            
            // 查找库存记录，包含所有必要的关联
            $inventory = Inventory::where('product_id', $productId)
                ->where('warehouse_id', $warehouseId)
                ->with(['warehouse', 'unit', 'product.units', 'product.baseUnit'])
                ->first();
                
            if (!$inventory) {
                // 如果没有库存记录，创建一个默认的库存记录
                $baseUnitId = $product->base_unit_id;
                
                // 如果商品没有设置基本单位，尝试从商品的单位中找到基本单位
                if (!$baseUnitId) {
                    $allUnits = $product->getAllUnits();
                    if ($allUnits && is_array($allUnits)) {
                        foreach ($allUnits as $unit) {
                            if (isset($unit['roles']) && is_array($unit['roles']) && 
                                (in_array('base', $unit['roles']) || in_array('default', $unit['roles']))) {
                                $baseUnitId = $unit['id'];
                                break;
                            }
                        }
                    }
                }
                
                // 如果还是没有找到基本单位，使用第一个可用单位
                if (!$baseUnitId && $allUnits && is_array($allUnits) && count($allUnits) > 0) {
                    $baseUnitId = $allUnits[0]['id'];
                }
                
                // 创建并保存库存记录
                $inventory = Inventory::create([
                    'product_id' => $productId,
                    'warehouse_id' => $warehouseId,
                    'unit_id' => $baseUnitId,
                    'stock' => 0,
                ]);
                
                // 重新加载关联数据
                $inventory->load(['warehouse', 'unit', 'product.units', 'product.baseUnit']);
            } else {
                // 如果库存记录存在但unit_id为null，自动设置为商品的基本单位
                if (!$inventory->unit_id) {
                    $baseUnitId = $product->base_unit_id;
                    
                    if (!$baseUnitId) {
                        $allUnits = $product->getAllUnits();
                        if ($allUnits && is_array($allUnits)) {
                            foreach ($allUnits as $unit) {
                                if (isset($unit['roles']) && is_array($unit['roles']) && 
                                    (in_array('base', $unit['roles']) || in_array('default', $unit['roles']))) {
                                    $baseUnitId = $unit['id'];
                                    break;
                                }
                            }
                        }
                    }
                    
                    if ($baseUnitId) {
                        $inventory->unit_id = $baseUnitId;
                        $inventory->save();
                        $inventory->load('unit'); // 重新加载unit关联
                    }
                }
            }
            
            // 获取商品的所有可用单位
            $availableUnits = $product->getAllUnits() ?? [];
            
            // 获取最近的库存事务记录
            $recentTransactions = InventoryTransaction::where('product_id', $productId)
                ->where('warehouse_id', $warehouseId)
                ->where('status', 'completed')
                ->with(['transactionType', 'unit'])
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();
                
            // 准备返回的库存信息
            $inventoryData = [
                'id' => $inventory->id,
                'product_id' => $inventory->product_id,
                'warehouse_id' => $inventory->warehouse_id,
                'stock' => $inventory->stock,
                'unit_id' => $inventory->unit_id,
                'min_stock_level' => $inventory->min_stock_level,
                'warehouse' => $inventory->warehouse ? [
                    'id' => $inventory->warehouse->id,
                    'location' => $inventory->warehouse->location,
                    'name' => $inventory->warehouse->name ?? $inventory->warehouse->location,
                ] : null,
                'unit' => $inventory->unit ? [
                    'id' => $inventory->unit->id,
                    'name' => $inventory->unit->name,
                    'symbol' => $inventory->unit->symbol ?? $inventory->unit->name,
                ] : null,
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'code' => $product->code,
                    'base_unit_id' => $product->base_unit_id,
                    'units' => $availableUnits,
                    'baseUnit' => $product->baseUnit ? [
                        'id' => $product->baseUnit->id,
                        'name' => $product->baseUnit->name,
                        'symbol' => $product->baseUnit->symbol ?? $product->baseUnit->name,
                    ] : null
                ]
            ];
                
            return response()->json(ApiResponse::success([
                'inventory' => $inventoryData,
                'available_units' => $availableUnits,
                'recent_transactions' => $recentTransactions,
            ]));
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取商品库存失败', [
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取库存信息失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 库存调整
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function adjust(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'new_quantity' => 'required|numeric|min:0',
            'unit_id' => 'required|exists:units,id',
            'reason' => 'required|string',
            'unit_price' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            DB::beginTransaction();
            
            $product = Product::findOrFail($request->product_id);
            $warehouse = Warehouse::findOrFail($request->warehouse_id);
            
            // 获取或创建库存记录
            $inventory = $this->getOrCreateInventory($request->product_id, $request->warehouse_id, $request->unit_id);
            
            // 计算调整数量
            $currentStock = $inventory->stock;
            $newQuantity = $request->new_quantity;
            $adjustmentQuantity = $newQuantity - $currentStock;
            
            if ($adjustmentQuantity == 0) {
                return response()->json(ApiResponse::error('调整数量与当前库存相同，无需调整', 422), 422);
            }
            
            // 计算总金额
            $unitPrice = $request->unit_price ?? 0;
            $totalAmount = abs($adjustmentQuantity) * $unitPrice;
            
            // 根据reason参数选择正确的事务类型
            $transactionTypeCode = $this->getTransactionTypeByReason($request->reason, $adjustmentQuantity);
            $transactionType = InventoryTransactionType::where('code', $transactionTypeCode)->firstOrFail();
            
            // 创建库存调整事务
            $transaction = $this->createStockTransaction([
                'transaction_type_id' => $transactionType->id,
                'product_id' => $request->product_id,
                'warehouse_id' => $request->warehouse_id,
                'quantity' => $adjustmentQuantity,
                'unit_id' => $request->unit_id,
                'unit_price' => $unitPrice,
                'total_amount' => $totalAmount,
                'notes' => $request->notes ?: $request->reason,
            ]);
            
            // 应用库存调整
            $inventory->stock = $newQuantity;
            $inventory->save();
            
            // 更新相关统计
            $this->updateStockTotals($product, $warehouse);
            
            // 如果有价格信息且是入库调整，更新商品成本价
            if ($unitPrice > 0 && $adjustmentQuantity > 0) {
                // 标记事务为已完成，以便成本价服务处理
                $transaction->status = 'completed';
                $transaction->save();
                
                // 更新商品成本价
                $this->costPriceService->updateCostPriceOnInbound($transaction);
            } else {
                // 其他情况只清除缓存
                $this->costPriceService->clearCostPriceCacheForProduct(
                    $request->product_id,
                    $request->warehouse_id
                );
            }
            
            DB::commit();
            
            // 返回详细的调整信息
            $response = [
                'transaction' => $transaction->load(['transactionType', 'unit']),
                'inventory' => $inventory->load(['warehouse', 'unit']),
                'adjustment_summary' => [
                    'old_stock' => $currentStock,
                    'new_stock' => $newQuantity,
                    'adjustment_quantity' => $adjustmentQuantity,
                    'adjustment_type' => $adjustmentQuantity > 0 ? '入库' : '出库',
                    'unit_price' => $unitPrice,
                    'total_amount' => $totalAmount,
                    'unit_name' => $transaction->unit->name ?? '件',
                ]
            ];
            
            return response()->json(ApiResponse::success($response, '库存调整成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('库存调整失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取库存列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStockList(Request $request)
    {
        // 直接查询商品表，加载必要的关联数据
        $query = Product::with(['baseUnit', 'category', 'images']);
        
        // 商品筛选
        if ($request->has('product_id') && $request->product_id) {
            $query->where('id', $request->product_id);
        }
        
        // 关键词搜索
        if ($request->has('keyword') && $request->keyword) {
            $keyword = '%' . $request->keyword . '%';
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', $keyword)
                  ->orWhere('code', 'like', $keyword)
                  ->orWhere('description', 'like', $keyword);
            });
        }

        // 仓库筛选
        $warehouseId = $request->has('warehouse_id') && $request->warehouse_id ? $request->warehouse_id : null;
        
        // 库存状态筛选
        $stockStatus = $request->get('stock_status', 'all');
        
        // 排序
        $orderBy = $request->get('order_by', 'created_at');
        $direction = $request->get('direction', 'desc');
        $query->orderBy($orderBy, $direction);
        
        // 分页
        $products = $query->paginate($request->get('per_page', 20));
        
        // 准备返回数据
        $stockData = [];
        
        foreach ($products as $product) {
            // 获取库存记录
            $inventory = null;
            
            if ($warehouseId) {
                // 如果指定了仓库，获取指定仓库的库存
                $inventory = Inventory::where('product_id', $product->id)
                    ->where('warehouse_id', $warehouseId)
                    ->with(['warehouse', 'unit'])
                    ->first();
            } else {
                // 否则获取所有仓库的库存
                $inventories = Inventory::where('product_id', $product->id)
                    ->with(['warehouse', 'unit'])
                    ->get();
                
                // 如果有多个仓库的库存，处理每一个
                foreach ($inventories as $inv) {
                    $stockData[] = $this->formatStockItem($product, $inv);
                }
                
                // 如果没有找到库存记录，创建一个默认的
                if ($inventories->isEmpty()) {
                    $stockData[] = $this->formatStockItem($product, null);
                }
                
                // 继续下一个产品
                continue;
            }
            
            // 添加库存数据
            $stockData[] = $this->formatStockItem($product, $inventory);
        }
        
        // 根据库存状态筛选
        if ($stockStatus !== 'all') {
            $stockData = collect($stockData)->filter(function ($item) use ($stockStatus) {
                switch ($stockStatus) {
                    case 'normal':
                        return $item['stock_status'] === 'normal';
                    case 'low':
                        return $item['stock_status'] === 'low';
                    case 'out':
                        return $item['stock_status'] === 'out';
                    case 'in_stock':
                        return $item['stock_quantity'] > 0;
                    case 'out_of_stock':
                        return $item['stock_quantity'] <= 0;
                    case 'low_stock':
                        // 假设低库存阈值为10
                        return $item['stock_quantity'] > 0 && $item['stock_quantity'] <= 10;
                    default:
                        return true;
                }
            })->values()->all();
        }
        
        // 使用 ApiResponse 包装返回数据
        return response()->json(ApiResponse::success([
            'data' => $stockData,
            'total' => count($stockData),
            'per_page' => $request->get('per_page', 20),
            'current_page' => $request->get('page', 1),
        ]));
    }
    
    /**
     * 格式化库存项目
     * 
     * @param Product $product
     * @param Inventory|null $inventory
     * @return array
     */
    private function formatStockItem(Product $product, ?Inventory $inventory = null)
    {
        // 基本单位
        $baseUnit = $product->baseUnit;
        $baseUnitName = $baseUnit ? $baseUnit->name : '个';
        
        // 获取最近的入库和出库日期
        $lastInTransaction = null;
        $lastOutTransaction = null;
        
        if ($inventory) {
            $lastInTransaction = InventoryTransaction::where('product_id', $product->id)
                ->where('warehouse_id', $inventory->warehouse_id)
                ->where('quantity', '>', 0)
                ->where('status', 'completed')
                ->orderBy('created_at', 'desc')
                ->first();
                
            $lastOutTransaction = InventoryTransaction::where('product_id', $product->id)
                ->where('warehouse_id', $inventory->warehouse_id)
                ->where('quantity', '<', 0)
                ->where('status', 'completed')
                ->orderBy('created_at', 'desc')
                ->first();
        }
        
        $warehouse = $inventory ? $inventory->warehouse : null;
        
        // 获取单位名称，确保安全访问
        $unitName = $baseUnitName;
        if ($inventory) {
            // 优先使用unit关联，如果没有则使用unit_id加载或使用unit字段
            if ($inventory->relationLoaded('unit') && $inventory->unit) {
                $unitName = $inventory->unit->name;
            } elseif ($inventory->unit_id) {
                $unit = app(UnitService::class)->getUnitById($inventory->unit_id);
                $unitName = $unit ? $unit->name : $inventory->unit_name ?? $baseUnitName;
            } elseif (isset($inventory->unit) && is_string($inventory->unit)) {
                $unitName = $inventory->unit;
            }
        }
        
        // 获取商品的所有单位信息（包括销售单位）
        $availableUnits = [];
        try {
            Log::info('开始获取商品单位信息', [
                'product_id' => $product->id,
                'product_name' => $product->name
            ]);
            
            $allUnits = $product->getAllUnits();
            if ($allUnits && is_array($allUnits)) {
                $availableUnits = $allUnits;
                
                Log::info('成功获取商品单位信息', [
                    'product_id' => $product->id,
                    'units_count' => count($availableUnits),
                    'units' => $availableUnits
                ]);
                
                // 检查是否有销售单位
                $salesUnits = array_filter($availableUnits, function($unit) {
                    return isset($unit['roles']) && is_array($unit['roles']) && in_array('sales', $unit['roles']);
                });
                
                Log::info('销售单位检查结果', [
                    'product_id' => $product->id,
                    'sales_units_count' => count($salesUnits),
                    'sales_units' => array_values($salesUnits)
                ]);
            } else {
                Log::warning('getAllUnits返回空数据', [
                    'product_id' => $product->id,
                    'returned_data' => $allUnits
                ]);
            }
        } catch (\Exception $e) {
            // 如果获取失败，记录日志但不影响主要功能
            Log::warning('获取商品单位信息失败', [
                'product_id' => $product->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        // 计算库存价值
        $stockQuantity = $inventory ? $inventory->stock : 0;
        $stockValue = null;
        $costValue = null;
        $movingAvgCost = null;
        
        // 获取移动加权平均成本价
        if ($inventory) {
            try {
                $movingAvgCost = $this->costPriceService->calculateMovingAverageCostPrice($inventory->id);
            } catch (\Exception $e) {
                Log::warning('获取移动加权平均成本价失败', [
                    'inventory_id' => $inventory->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        if ($stockQuantity > 0) {
            // 使用sale_price作为销售价格
            if ($product->sale_price) {
                $stockValue = $stockQuantity * $product->sale_price;
            } elseif ($product->price) {
                // 如果没有sale_price，使用price作为备选
                $stockValue = $stockQuantity * $product->price;
            }
            
            if ($product->cost_price) {
                $costValue = $stockQuantity * $product->cost_price;
            }
        }
        
        // 获取商品图片
        $productImage = null;
        if ($product->cover_url) {
            $productImage = $product->cover_url;
        } elseif ($product->relationLoaded('images') && $product->images) {
            // 安全检查：确保images是Collection或数组且不为空
            $hasImages = false;
            if (is_object($product->images) && method_exists($product->images, 'isNotEmpty')) {
                $hasImages = $product->images->isNotEmpty();
            } elseif (is_array($product->images)) {
                $hasImages = count($product->images) > 0;
            }

            if ($hasImages) {
                $mainImage = null;
                if (is_object($product->images) && method_exists($product->images, 'where')) {
                    $mainImage = $product->images->where('is_main', true)->first();
                } elseif (is_array($product->images)) {
                    $mainImage = collect($product->images)->where('is_main', true)->first();
                }

                if ($mainImage) {
                    $productImage = $mainImage->url;
                } else {
                    if (is_object($product->images) && method_exists($product->images, 'first')) {
                        $productImage = $product->images->first()->url;
                    } elseif (is_array($product->images) && count($product->images) > 0) {
                        $productImage = $product->images[0]->url ?? null;
                    }
                }
            }
        }
        
        // 获取商品分类
        $productCategory = null;
        if ($product->relationLoaded('category') && $product->category) {
            $productCategory = $product->category->name;
        }
        
        // 计算库存周转天数（简化计算，基于最近30天的出库量）
        $turnoverDays = null;
        if ($inventory && $stockQuantity > 0) {
            $recentOutQuantity = InventoryTransaction::where('product_id', $product->id)
                ->where('warehouse_id', $inventory->warehouse_id)
                ->where('quantity', '<', 0)
                ->where('status', 'completed')
                ->where('created_at', '>=', now()->subDays(30))
                ->sum('quantity');
            
            if ($recentOutQuantity < 0) {
                $dailyOutQuantity = abs($recentOutQuantity) / 30;
                if ($dailyOutQuantity > 0) {
                    $turnoverDays = round($stockQuantity / $dailyOutQuantity);
                }
            }
        }
        
        // 生成预警信息
        $alerts = [];
        
        // 获取预警阈值（优先级：库存级 > 商品级 > 默认值）
        $alertThreshold = null;
        if ($inventory && $inventory->min_stock_level !== null) {
            // 优先使用库存记录的预警值（仓库级）
            $alertThreshold = $inventory->min_stock_level;
        } elseif ($product->min_stock_threshold !== null) {
            // 其次使用商品的预警值（商品级）
            $alertThreshold = $product->min_stock_threshold;
        }
        
        // 低库存预警
        if ($alertThreshold !== null && $stockQuantity <= $alertThreshold) {
            $thresholdSource = $inventory && $inventory->min_stock_level !== null ? '仓库' : '商品';
            $alerts[] = [
                'type' => 'low_stock',
                'message' => "库存低于{$thresholdSource}预警阈值 {$alertThreshold}{$unitName}",
                'threshold' => $alertThreshold,
                'source' => $thresholdSource
            ];
        }
        
        // 缺货预警
        if ($stockQuantity <= 0) {
            $alerts[] = [
                'type' => 'out_of_stock',
                'message' => '商品已缺货'
            ];
        }
        
        // 库存过多预警（如果库存是预警阈值的10倍以上）
        if ($alertThreshold !== null && $stockQuantity > ($alertThreshold * 10)) {
            $alerts[] = [
                'type' => 'overstock',
                'message' => '库存可能过多，建议检查'
            ];
        }
        
        // 确定库存状态
        $stockStatus = 'normal';
        if ($stockQuantity <= 0) {
            $stockStatus = 'out';
        } elseif ($alertThreshold !== null && $stockQuantity <= $alertThreshold) {
            $stockStatus = 'low';
        }
        
        // 计算可销售数量
        $saleableQuantity = $this->calculateSaleableQuantity($stockQuantity, $availableUnits, $unitName, $product);
        
        return [
            'id' => $inventory ? $inventory->id : $product->id,
            'product_id' => $product->id,
            'product_code' => $product->code,
            'product_name' => $product->name,
            'product_image' => $productImage,
            'product_category' => $productCategory,
            'product_price' => $product->sale_price ?? $product->price, // 优先使用sale_price
            'code' => $product->code, // 向后兼容
            'name' => $product->name, // 向后兼容
            'stock_quantity' => $stockQuantity,
            'stock' => $stockQuantity, // 向后兼容
            'base_stock' => $inventory ? $inventory->getStockInBaseUnit() : 0,
            'stock_unit' => $unitName,
            'unit' => $unitName, // 向后兼容
            'base_unit' => $baseUnitName,
            'stock_status' => $stockStatus,
            'conversion_rate' => 1, // 默认转换率
            'available_units' => $availableUnits, // 商品的所有单位信息
            'saleable_quantity' => $saleableQuantity, // 可销售数量信息
            'last_in_date' => $lastInTransaction ? $lastInTransaction->created_at->format('Y-m-d') : null,
            'last_out_date' => $lastOutTransaction ? $lastOutTransaction->created_at->format('Y-m-d') : null,
            'last_in_time' => $lastInTransaction ? $lastInTransaction->created_at->format('Y-m-d H:i:s') : null,
            'last_out_time' => $lastOutTransaction ? $lastOutTransaction->created_at->format('Y-m-d H:i:s') : null,
            'updated_at' => $inventory ? $inventory->updated_at->format('Y-m-d H:i:s') : null,
            'product' => [
                'id' => $product->id,
                'code' => $product->code,
                'name' => $product->name,
                'units' => $availableUnits // 也在product对象中包含单位信息
            ],
            'warehouse_id' => $warehouse ? $warehouse->id : null,
            'warehouse_name' => $warehouse ? ($warehouse->location ?? $warehouse->name) : null,
            'warehouse' => $warehouse ? [
                'id' => $warehouse->id,
                'name' => $warehouse->name,
                'location' => $warehouse->location
            ] : null,
            // 价格信息
            'sale_price' => $product->sale_price ?? null,
            'cost_price' => $product->cost_price ?? null,
            'moving_avg_cost' => $movingAvgCost,
            'stock_value' => $stockValue,
            'cost_value' => $costValue,
            // 库存管理信息
            'min_stock_level' => $alertThreshold, // 使用计算出的预警阈值
            'min_stock_threshold' => $product->min_stock_threshold, // 商品级预警阈值
            'inventory_min_stock_level' => $inventory ? $inventory->min_stock_level : null, // 库存级预警阈值
            'turnover_days' => $turnoverDays,
            'alerts' => $alerts,
            // 向后兼容的销售单位字段
            'sale_unit' => null, // 将通过available_units中的sales角色单位确定
            'sale_unit_conversion_rate' => 1,
            'min_sale_quantity' => $product->min_sale_quantity ?? 1
        ];
    }

    /**
     * 计算可销售数量
     * 
     * @param float $stockQuantity 库存数量
     * @param array $availableUnits 可用单位
     * @param string $stockUnit 库存单位
     * @param Product $product 商品对象
     * @return array
     */
    private function calculateSaleableQuantity(float $stockQuantity, array $availableUnits, string $stockUnit, Product $product): array
    {
        if ($stockQuantity <= 0) {
            return [
                'quantity' => 0,
                'unit' => $stockUnit,
                'canSale' => false,
                'remainder' => 0
            ];
        }
        
        // 从available_units中查找具有'sales'角色的单位
        $saleUnit = null;
        foreach ($availableUnits as $unit) {
            if (isset($unit['roles']) && is_array($unit['roles']) && in_array('sales', $unit['roles'])) {
                $saleUnit = $unit;
                break;
            }
        }
        
        if ($saleUnit && isset($saleUnit['conversion_factor']) && $saleUnit['conversion_factor'] > 0) {
            // 确保转换系数不为零，避免除零错误
            $conversionFactor = floatval($saleUnit['conversion_factor']);
            
            // 根据Unit模块的设计：conversion_factor表示1个销售单位等于多少个基本单位
            // 所以可销售数量 = 库存数量(基本单位) ÷ 销售单位转换系数
            $saleableQty = floor($stockQuantity / $conversionFactor);
            
            // 修复余数计算：余数 = 库存数量 - (可销售数量 × 转换系数)
            $soldQuantityInBaseUnit = $saleableQty * $conversionFactor;
            $remainder = $stockQuantity - $soldQuantityInBaseUnit;
            
            // 如果商品有价格信息，计算可销售价值
            $saleableValue = null;
            $salePrice = $product->sale_price ?? $product->price;
            if ($salePrice && $saleableQty > 0) {
                // 修复价格计算：价格基于基本单位，需要根据转换系数调整
                // 销售单位的单价 = 基本单位价格 × 转换系数
                $saleUnitPrice = $salePrice * $conversionFactor;
                $saleableValue = $saleableQty * $saleUnitPrice;
            }
            
            return [
                'quantity' => $saleableQty,
                'unit' => $saleUnit['name'],
                'canSale' => $saleableQty > 0,
                'remainder' => $remainder,
                'value' => $saleableValue,
                'conversionInfo' => "1{$saleUnit['name']} = {$conversionFactor}{$stockUnit}"
            ];
        }
        
        // 如果没有有效的销售单位，检查是否有基本单位可以直接销售
        $baseUnit = null;
        foreach ($availableUnits as $unit) {
            if (isset($unit['roles']) && is_array($unit['roles']) && 
                (in_array('base', $unit['roles']) || in_array('default', $unit['roles']))) {
                $baseUnit = $unit;
                break;
            }
        }
        
        if ($baseUnit) {
            $salePrice = $product->sale_price ?? $product->price;
            return [
                'quantity' => $stockQuantity,
                'unit' => $baseUnit['name'],
                'canSale' => true,
                'remainder' => 0,
                'value' => $salePrice ? $stockQuantity * $salePrice : null
            ];
        }
        
        // 如果都没有，返回不可销售
        return [
            'quantity' => 0,
            'unit' => $stockUnit,
            'canSale' => false,
            'remainder' => $stockQuantity
        ];
    }

    /**
     * 根据单位名称或商品基本单位获取单位ID
     *
     * @param Product $product
     * @param string $unitName
     * @return int|null
     */
    private function resolveUnitId(Product $product, string $unitName): ?int
    {
        // 如果商品有基本单位，优先使用
        if ($product->base_unit_id) {
            return $product->base_unit_id;
        }
        
        // 尝试根据名称查找单位
        $unit = \App\Unit\Models\Unit::where('name', $unitName)
            ->orWhere('display_name', $unitName)
            ->first();
            
        if ($unit) {
            return $unit->id;
        }
        
        // 尝试查找默认单位
        $defaultUnit = \App\Unit\Models\Unit::where('is_base', true)->first();
        return $defaultUnit ? $defaultUnit->id : null;
    }

    /**
     * 获取或创建库存记录
     *
     * @param int $productId
     * @param int $warehouseId
     * @param int $unitId
     * @return Inventory
     */
    private function getOrCreateInventory(int $productId, int $warehouseId, int $unitId): Inventory
    {
        $inventory = Inventory::firstOrCreate(
            [
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
            ],
            [
                'unit_id' => $unitId,
                'stock' => 0,
            ]
        );
        
        // 确保inventory有unit_id
        if (!$inventory->unit_id) {
            $inventory->unit_id = $unitId;
            $inventory->save();
        }
        
        return $inventory;
    }



    /**
     * 根据调整原因选择事务类型
     *
     * @param string $reason
     * @param float $adjustmentQuantity
     * @return string
     */
    private function getTransactionTypeByReason(string $reason, float $adjustmentQuantity): string
    {
        // 如果reason直接是数据库中的code，直接返回
        $transactionType = DB::table('inventory_transaction_types')
            ->where('code', $reason)
            ->first();
            
        if ($transactionType) {
            return $reason;
        }
        
        // 如果reason是中文名称，查找对应的code
        $transactionType = DB::table('inventory_transaction_types')
            ->where('name', $reason)
            ->first();
            
        if ($transactionType) {
            return $transactionType->code;
        }
        
        // 根据reason参数映射到正确的事务类型
        $reasonLower = strtolower(trim($reason));
        
        switch ($reasonLower) {
            // 损耗相关 - 对应"库存损耗"
            case 'loss':
            case 'damage':
            case 'damaged':
            case 'expired':
            case 'expire':
            case 'broken':
            case 'spoiled':
            case 'waste':
            case 'defective':
            case 'shrinkage':
            case '损耗':
            case '损坏':
            case '过期':
            case '报废':
            case '破损':
            case '变质':
                return 'inventory_loss';
            
            // 采购入库相关 - 对应"采购入库"
            case 'purchase':
            case 'purchase_in':
            case 'buy':
            case 'receive':
            case 'received':
            case 'procurement':
            case 'restock':
            case 'replenish':
            case '采购':
            case '进货':
            case '补货':
            case '入库':
                return 'purchase_in';
                
            // 销售出库相关 - 对应"销售出库"
            case 'sale':
            case 'sales_out':
            case 'sell':
            case 'sold':
            case 'delivery':
            case 'ship':
            case 'dispatch':
            case 'order':
            case '销售':
            case '出库':
            case '发货':
            case '配送':
                return 'sales_out';
                
            // 销售退货入库 - 对应"销售退货入库"
            case 'return_in':
            case 'customer_return':
            case 'sales_return':
            case 'refund':
            case 'return_from_customer':
            case '退货':
            case '客户退货':
            case '销售退货':
                return 'return_in';
                
            // 采购退货出库 - 对应"采购退货出库"
            case 'return_out':
            case 'purchase_return':
            case 'supplier_return':
            case 'return_to_supplier':
            case '退货给供应商':
            case '采购退货':
                return 'return_out';
                
            // 调拨相关 - 对应"调拨入库/出库"
            case 'transfer':
            case 'transfer_in':
            case 'transfer_out':
            case 'move':
            case 'relocate':
            case '调拨':
            case '转移':
                return $adjustmentQuantity > 0 ? 'transfer_in' : 'transfer_out';
                
            // 期初入库相关
            case 'initial':
            case 'initial_stock':
            case 'opening':
            case 'opening_stock':
            case '期初':
            case '期初库存':
            case '初始库存':
                return 'initial_stock_in';
                
            // 盘点调整相关 - 对应"库存调整"
            case 'inventory':
            case 'adjustment':
            case 'count':
            case 'stocktaking':
            case 'audit':
            case 'reconciliation':
            case 'correction':
            case 'manual':
            case 'recount':
            case '盘点':
            case '调整':
            case '手动调整':
            case '库存盘点':
            case '人工调整':
            case '修正':
            default:
                return 'inventory_adjustment';
        }
    }

    /**
     * 创建库存事务
     *
     * @param array $data
     * @return InventoryTransaction
     */
    private function createStockTransaction(array $data): InventoryTransaction
    {
        // 检查用户认证
        $userId = auth()->id();
        if (!$userId) {
            throw new \Exception('用户未登录，无法创建库存事务');
        }
        
        return InventoryTransaction::create([
            'transaction_type_id' => $data['transaction_type_id'],
            'product_id' => $data['product_id'],
            'warehouse_id' => $data['warehouse_id'],
            'quantity' => $data['quantity'],
            'unit_id' => $data['unit_id'],
            'unit_price' => $data['unit_price'] ?? 0,
            'total_amount' => $data['total_amount'] ?? 0,
            'status' => 'completed',
            'notes' => $data['notes'] ?? '',
            'created_by' => $userId,
            'updated_by' => $userId,
        ]);
    }

    /**
     * 更新相关库存统计
     *
     * @param Product $product
     * @param Warehouse $warehouse
     * @return void
     */
    private function updateStockTotals(Product $product, Warehouse $warehouse): void
    {
        // 更新商品总库存
        if (method_exists($product, 'updateTotalStock')) {
            $product->updateTotalStock();
        }
        
        // 更新仓库总库存
        if (method_exists($warehouse, 'updateTotalStock')) {
            $warehouse->updateTotalStock();
        }
    }

    /**
     * 增加库存
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addStock(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'quantity' => 'required|numeric|min:0.01',
            'unit_id' => 'required|exists:units,id',
            'batch_number' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            DB::beginTransaction();
            
            $product = Product::findOrFail($request->product_id);
            $warehouse = Warehouse::findOrFail($request->warehouse_id);
            
            // 验证单位是否属于该商品
            $productUnits = $product->getAllUnits();
            $validUnit = collect($productUnits)->firstWhere('id', $request->unit_id);
            if (!$validUnit) {
                DB::rollBack();
                return response()->json(ApiResponse::error('所选单位不适用于该商品', 422), 422);
            }
            
            // 计算基本单位数量（用于库存存储）
            $conversionFactor = floatval($validUnit['conversion_factor'] ?? 1);
            $baseUnitQuantity = $request->quantity * $conversionFactor;
            
            // 获取基本单位ID
            $baseUnitId = $product->base_unit_id;
            if (!$baseUnitId) {
                // 如果没有基本单位，查找base角色的单位
                $baseUnit = collect($productUnits)->first(function($unit) {
                    return in_array('base', $unit['roles'] ?? []);
                });
                $baseUnitId = $baseUnit ? $baseUnit['id'] : $request->unit_id;
            }
            
            // 获取或创建库存记录（使用基本单位）
            $inventory = $this->getOrCreateInventory($request->product_id, $request->warehouse_id, $baseUnitId);
            
            // 获取入库事务类型
            $transactionType = InventoryTransactionType::where('code', 'purchase_in')->firstOrFail();
            
            // 创建库存入库事务
            $transaction = $this->createStockTransaction([
                'transaction_type_id' => $transactionType->id,
                'product_id' => $request->product_id,
                'warehouse_id' => $request->warehouse_id,
                'quantity' => $baseUnitQuantity,
                'unit_id' => $request->unit_id,
                'notes' => $request->notes ?? '手动增加库存',
            ]);
            
            // 更新库存
            $inventory->stock += $baseUnitQuantity;
            $inventory->save();
            
            // 更新相关统计
            $this->updateStockTotals($product, $warehouse);
            
            DB::commit();
            
            // 准备响应数据
            $responseData = [
                'transaction' => $transaction,
                'inventory' => $inventory,
                'conversion_info' => [
                    'input_quantity' => $request->quantity,
                    'input_unit' => $validUnit['name'],
                    'base_quantity' => $baseUnitQuantity,
                    'base_unit' => $inventory->unit ? $inventory->unit->name : '基本单位',
                    'conversion_factor' => $conversionFactor,
                    'conversion_note' => $conversionFactor != 1 ? 
                        "入库{$request->quantity}{$validUnit['name']} = {$baseUnitQuantity}基本单位" : 
                        "直接以基本单位入库"
                ],
                'message' => '入库成功'
            ];
            
            return response()->json(ApiResponse::success($responseData, '库存增加成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('增加库存失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 减少库存
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reduceStock(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'quantity' => 'required|numeric|min:0.01',
            'unit' => 'required|string',
            'reason' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            DB::beginTransaction();
            
            $product = Product::findOrFail($request->product_id);
            $warehouse = Warehouse::findOrFail($request->warehouse_id);
            
            // 解析单位ID
            $unitId = $this->resolveUnitId($product, $request->unit);
            if (!$unitId) {
                DB::rollBack();
                return response()->json(ApiResponse::error('无法减少库存：未找到有效的单位，请先为商品设置基本单位', 422), 422);
            }
            
            // 获取库存记录
            $inventory = Inventory::where('product_id', $request->product_id)
                ->where('warehouse_id', $request->warehouse_id)
                ->first();
                
            if (!$inventory) {
                return response()->json(ApiResponse::error('该商品在指定仓库中没有库存记录', 404), 404);
            }
            
            // 确保inventory有unit_id
            if (!$inventory->unit_id) {
                $inventory->unit_id = $unitId;
                $inventory->save();
            }
            
            // 检查库存是否充足
            if ($inventory->stock < $request->quantity) {
                return response()->json(ApiResponse::error('库存不足，无法减少', 422), 422);
            }
            
            // 获取出库事务类型
            $transactionType = InventoryTransactionType::where('code', 'sales_out')->firstOrFail();
            
            // 创建库存出库事务
            $transaction = $this->createStockTransaction([
                'transaction_type_id' => $transactionType->id,
                'product_id' => $request->product_id,
                'warehouse_id' => $request->warehouse_id,
                'quantity' => -$request->quantity, // 负数表示减少
                'unit_id' => $unitId,
                'notes' => $request->reason ?? '手动减少库存',
            ]);
            
            // 更新库存
            $inventory->stock -= $request->quantity;
            $inventory->save();
            
            // 更新相关统计
            $this->updateStockTotals($product, $warehouse);
            
            DB::commit();
            
            return response()->json(ApiResponse::success($transaction, '库存减少成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('减少库存失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取库存统计数据
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats()
    {
        try {
            // 商品种类统计
            $totalProducts = Inventory::distinct('product_id')->count('product_id');
            
            // 低库存商品统计（这里需要根据实际业务逻辑定义低库存阈值）
            $lowStockProducts = Inventory::where('stock', '<', 10)->count();
            
            // 即将过期批次统计
            $nearExpiryBatches = \App\Inventory\Models\InventoryBatch::where('expiry_date', '>=', now())
                ->where('expiry_date', '<=', now()->addDays(7))
                ->where('quantity', '>', 0)
                ->count();
            
            // 库存总价值计算（需要结合商品价格）
            $totalValue = Inventory::join('products', 'inventory.product_id', '=', 'products.id')
                ->selectRaw('SUM(inventory.stock * COALESCE(products.cost_price, products.sale_price, 0)) as total_value')
                ->value('total_value') ?? 0;
            
            return response()->json(ApiResponse::success([
                'totalProducts' => $totalProducts,
                'lowStockProducts' => $lowStockProducts,
                'nearExpiryBatches' => $nearExpiryBatches,
                'totalValue' => round($totalValue, 2)
            ]));
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取库存统计失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取统计数据失败'), 500);
        }
    }

    /**
     * 测试API连接
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function test()
    {
        return response()->json(ApiResponse::success([
            'message' => 'API连接正常',
            'timestamp' => now()->toISOString(),
            'user' => auth()->user() ? auth()->user()->id : 'guest'
        ]));
    }

    /**
     * 获取商品的所有库存信息（按仓库分组）
     *
     * @param Request $request
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductInventories(Request $request, $productId)
    {
        try {
            $product = Product::with(['baseUnit', 'category', 'images'])->findOrFail($productId);
            
            // 获取商品的所有可用单位
            $availableUnits = $product->getAllUnits() ?? [];
            
            // 获取商品在所有仓库的库存记录
            $inventories = Inventory::where('product_id', $productId)
                ->with(['warehouse', 'unit'])
                ->get();
            
            // 使用formatStockItem方法格式化库存数据，包含移动加权平均成本价等完整信息
            $inventoryData = [];
            foreach ($inventories as $inventory) {
                // 确保库存记录有正确的unit_id
                if (!$inventory->unit_id && $product->base_unit_id) {
                    $inventory->unit_id = $product->base_unit_id;
                    $inventory->save();
                    $inventory->load('unit');
                }
                
                // 使用formatStockItem方法格式化数据
                $formattedItem = $this->formatStockItem($product, $inventory);
                
                // 转换为前端期望的格式
                $inventoryData[] = [
                    'id' => $inventory->id,
                    'warehouse_id' => $inventory->warehouse_id,
                    'stock' => $inventory->stock,
                    'min_stock_level' => $inventory->min_stock_level,
                    'stock_status' => $formattedItem['stock_status'],
                    'updated_at' => $inventory->updated_at,
                    'warehouse' => [
                        'id' => $inventory->warehouse->id,
                        'name' => $inventory->warehouse->name,
                        'code' => $inventory->warehouse->code ?? '',
                        'location' => $inventory->warehouse->location,
                    ],
                    'unit' => $inventory->unit ? [
                        'id' => $inventory->unit->id,
                        'name' => $inventory->unit->name,
                        'symbol' => $inventory->unit->symbol ?? $inventory->unit->name,
                    ] : null,
                    'inventory_min_stock_level' => $inventory->min_stock_level,
                                    // 从formatStockItem添加移动加权平均成本价和时间信息
                'moving_avg_cost' => $formattedItem['moving_avg_cost'],
                    'last_in_time' => $formattedItem['last_in_time'],
                    'last_out_time' => $formattedItem['last_out_time'],
                    'last_in_date' => $formattedItem['last_in_date'],
                    'last_out_date' => $formattedItem['last_out_date'],
                    // 其他有用的信息
                    'cost_price' => $formattedItem['cost_price'],
                    'stock_value' => $formattedItem['stock_value'],
                    'cost_value' => $formattedItem['cost_value'],
                    'turnover_days' => $formattedItem['turnover_days'],
                    'alerts' => $formattedItem['alerts'],
                ];
            }
            
            // 提取销售单位和采购单位
            $saleUnit = null;
            $purchaseUnit = null;
            $saleConversionFactor = 1;
            $purchaseConversionFactor = 1;
            
            foreach ($availableUnits as $unit) {
                if (isset($unit['roles']) && is_array($unit['roles'])) {
                    if (in_array('sales', $unit['roles'])) {
                        $saleUnit = [
                            'id' => $unit['id'],
                            'name' => $unit['name'],
                            'symbol' => $unit['symbol'] ?? $unit['name'],
                        ];
                        $saleConversionFactor = $unit['conversion_factor'] ?? 1;
                    }
                    if (in_array('purchase', $unit['roles'])) {
                        $purchaseUnit = [
                            'id' => $unit['id'],
                            'name' => $unit['name'],
                            'symbol' => $unit['symbol'] ?? $unit['name'],
                        ];
                        $purchaseConversionFactor = $unit['conversion_factor'] ?? 1;
                    }
                }
            }

            // 商品基本信息
            $productData = [
                'id' => $product->id,
                'code' => $product->code,
                'name' => $product->name,
                'category' => $product->category ? [
                    'id' => $product->category->id,
                    'name' => $product->category->name,
                ] : null,
                'base_unit' => $product->baseUnit ? [
                    'id' => $product->baseUnit->id,
                    'name' => $product->baseUnit->name,
                    'symbol' => $product->baseUnit->symbol ?? $product->baseUnit->name,
                ] : null,
                'sale_unit' => $saleUnit,
                'purchase_unit' => $purchaseUnit,
                'sale_conversion_factor' => $saleConversionFactor,
                'purchase_conversion_factor' => $purchaseConversionFactor,
                'price' => $product->sale_price ?? $product->price,
                'cost_price' => $product->cost_price,
                'total_stock' => $product->stock,
                'cover_url' => $this->getProductImageSafe($product),
                'image' => $this->getProductImageSafe($product),
                'min_stock_threshold' => $product->min_stock_threshold, // 添加商品级预警阈值
            ];
            
            // 计算统计信息
            $stats = [
                'total_stock' => $inventories->sum('stock'),
                'warehouse_count' => $inventories->count(),
                'low_stock_warehouses' => collect($inventoryData)->where('stock_status', 'low')->count(),
                'out_stock_warehouses' => collect($inventoryData)->where('stock_status', 'out')->count(),
                'stock_status' => $this->calculateOverallStockStatus($inventoryData),
            ];
            
            return response()->json(ApiResponse::success([
                'product' => $productData,
                'inventories' => $inventoryData,
                'available_units' => $availableUnits,
                'stats' => $stats,
            ]));
            
        } catch (\Exception $e) {
            Log::error('获取商品库存信息失败', [
                'product_id' => $productId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(ApiResponse::error('获取商品库存信息失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 计算整体库存状态
     */
    private function calculateOverallStockStatus($inventoryData)
    {
        if (empty($inventoryData)) {
            return 'out';
        }
        
        $hasStock = false;
        $hasLowStock = false;
        
        foreach ($inventoryData as $item) {
            if ($item['stock'] > 0) {
                $hasStock = true;
            }
            if ($item['stock_status'] === 'low') {
                $hasLowStock = true;
            }
        }
        
        if (!$hasStock) {
            return 'out';
        } elseif ($hasLowStock) {
            return 'low';
        } else {
            return 'normal';
        }
    }

    /**
     * 安全获取商品图片
     */
    private function getProductImageSafe($product)
    {
        if ($product->cover_url) {
            return $product->cover_url;
        }

        if ($product->images) {
            // 安全检查：确保images是Collection或数组且不为空
            if (is_object($product->images) && method_exists($product->images, 'isNotEmpty')) {
                if ($product->images->isNotEmpty()) {
                    return $product->images->first()->url ?? null;
                }
            } elseif (is_array($product->images) && count($product->images) > 0) {
                return $product->images[0]->url ?? null;
            }
        }

        return null;
    }

    /**
     * 获取库存历史记录
     *
     * @param Request $request
     * @param int $inventoryId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInventoryHistory(Request $request, $inventoryId)
    {
        try {
            // 验证库存记录是否存在
            $inventory = Inventory::with(['product', 'warehouse'])->findOrFail($inventoryId);
            
            // 构建查询
            $query = InventoryTransaction::with([
                'transactionType', 
                'product', 
                'warehouse', 
                'unit', 
                'creator'
            ])
            ->where('product_id', $inventory->product_id)
            ->where('warehouse_id', $inventory->warehouse_id);
            
            // 日期范围筛选
            if ($request->has('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }
            
            if ($request->has('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }
            
            // 事务类型筛选
            if ($request->has('transaction_type_id')) {
                $query->where('transaction_type_id', $request->transaction_type_id);
            }
            
            // 排序
            $query->orderBy('created_at', 'desc');
            
            // 分页
            $perPage = $request->per_page ?? 20;
            $transactions = $query->paginate($perPage);
            
            // 格式化数据
            $formattedTransactions = $transactions->items();
            $formattedData = array_map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'transaction_type' => [
                        'id' => $transaction->transactionType->id,
                        'name' => $transaction->transactionType->name,
                        'code' => $transaction->transactionType->code,
                    ],
                    'quantity' => $transaction->quantity,
                    'unit' => [
                        'id' => $transaction->unit->id,
                        'name' => $transaction->unit->name,
                        'symbol' => $transaction->unit->symbol ?? $transaction->unit->name,
                    ],
                    'unit_price' => $transaction->unit_price,
                    'total_amount' => $transaction->total_amount,
                    'status' => $transaction->status,
                    'notes' => $transaction->notes,
                    'reference_type' => $transaction->reference_type,
                    'reference_id' => $transaction->reference_id,
                    'creator' => $transaction->creator ? [
                        'id' => $transaction->creator->id,
                        'name' => $transaction->creator->name,
                    ] : null,
                    'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
                ];
            }, $formattedTransactions);
            
            // 重新组装分页数据
            $paginationData = [
                'data' => $formattedData,
                'current_page' => $transactions->currentPage(),
                'per_page' => $transactions->perPage(),
                'total' => $transactions->total(),
                'last_page' => $transactions->lastPage(),
                'from' => $transactions->firstItem(),
                'to' => $transactions->lastItem(),
            ];
            
            return response()->json(ApiResponse::success([
                'inventory' => [
                    'id' => $inventory->id,
                    'product' => [
                        'id' => $inventory->product->id,
                        'name' => $inventory->product->name,
                        'code' => $inventory->product->code,
                    ],
                    'warehouse' => [
                        'id' => $inventory->warehouse->id,
                        'name' => $inventory->warehouse->name,
                        'location' => $inventory->warehouse->location,
                    ],
                    'current_stock' => $inventory->stock,
                ],
                'transactions' => $paginationData,
            ]));
            
        } catch (\Exception $e) {
            Log::error('获取库存历史失败', [
                'inventory_id' => $inventoryId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取库存历史失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取商品在所有仓库的库存历史
     *
     * @param Request $request
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductStockHistory(Request $request, $productId)
    {
        try {
            // 验证商品是否存在
            $product = Product::findOrFail($productId);
            
            // 构建查询
            $query = InventoryTransaction::with([
                'transactionType', 
                'warehouse', 
                'unit', 
                'creator'
            ])
            ->where('product_id', $productId);
            
            // 仓库筛选
            if ($request->has('warehouse_id')) {
                $query->where('warehouse_id', $request->warehouse_id);
            }
            
            // 日期范围筛选
            if ($request->has('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }
            
            if ($request->has('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }
            
            // 事务类型筛选
            if ($request->has('transaction_type_id')) {
                $query->where('transaction_type_id', $request->transaction_type_id);
            }
            
            // 排序
            $query->orderBy('created_at', 'desc');
            
            // 分页
            $perPage = $request->per_page ?? 20;
            $transactions = $query->paginate($perPage);
            
            // 格式化数据
            $formattedTransactions = $transactions->items();
            $formattedData = array_map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'transaction_type' => [
                        'id' => $transaction->transactionType->id,
                        'name' => $transaction->transactionType->name,
                        'code' => $transaction->transactionType->code,
                    ],
                    'warehouse' => [
                        'id' => $transaction->warehouse->id,
                        'name' => $transaction->warehouse->name,
                        'location' => $transaction->warehouse->location,
                    ],
                    'quantity' => $transaction->quantity,
                    'unit' => [
                        'id' => $transaction->unit->id,
                        'name' => $transaction->unit->name,
                        'symbol' => $transaction->unit->symbol ?? $transaction->unit->name,
                    ],
                    'unit_price' => $transaction->unit_price,
                    'total_amount' => $transaction->total_amount,
                    'status' => $transaction->status,
                    'notes' => $transaction->notes,
                    'reference_type' => $transaction->reference_type,
                    'reference_id' => $transaction->reference_id,
                    'creator' => $transaction->creator ? [
                        'id' => $transaction->creator->id,
                        'name' => $transaction->creator->name,
                    ] : null,
                    'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
                ];
            }, $formattedTransactions);
            
            // 重新组装分页数据
            $paginationData = [
                'data' => $formattedData,
                'current_page' => $transactions->currentPage(),
                'per_page' => $transactions->perPage(),
                'total' => $transactions->total(),
                'last_page' => $transactions->lastPage(),
                'from' => $transactions->firstItem(),
                'to' => $transactions->lastItem(),
            ];
            
            return response()->json(ApiResponse::success([
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'code' => $product->code,
                ],
                'transactions' => $paginationData,
            ]));
            
        } catch (\Exception $e) {
            Log::error('获取商品库存历史失败', [
                'product_id' => $productId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取商品库存历史失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取支持的调整原因选项
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAdjustmentReasons()
    {
        try {
            // 从数据库获取所有事务类型
            $transactionTypes = \DB::table('inventory_transaction_types')
                ->select('code', 'name', 'effect_direction', 'description')
                ->orderBy('name')
                ->get();

            // 按类型分组整理数据
            $reasons = [];
            
            foreach ($transactionTypes as $type) {
                $reasons[$type->code] = [
                    'code' => $type->code,
                    'name' => $type->name,
                    'effect_direction' => $type->effect_direction,
                    'description' => $type->description
                ];
            }

            return response()->json(ApiResponse::success($reasons, '获取调整原因选项成功'));
            
        } catch (\Exception $e) {
            Log::error('获取调整原因失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(ApiResponse::error('获取调整原因失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 设置库存预警阈值
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function setStockAlert(Request $request)
    {
        try {
            $validated = $request->validate([
                'inventory_id' => 'required|integer|exists:inventory,id',
                'min_stock_level' => 'required|numeric|min:0'
            ]);

            // 获取库存记录
            $inventory = Inventory::findOrFail($validated['inventory_id']);
            
            // 更新安全库存阈值
            $inventory->update([
                'min_stock_level' => $validated['min_stock_level']
            ]);

            Log::info('库存预警设置成功', [
                'inventory_id' => $inventory->id,
                'product_id' => $inventory->product_id,
                'warehouse_id' => $inventory->warehouse_id,
                'min_stock_level' => $validated['min_stock_level'],
                'user_id' => auth()->id()
            ]);

            return response()->json(ApiResponse::success([
                'message' => '库存预警设置成功',
                'inventory' => [
                    'id' => $inventory->id,
                    'min_stock_level' => $inventory->min_stock_level,
                    'current_stock' => $inventory->stock,
                    'product' => [
                        'id' => $inventory->product->id,
                        'name' => $inventory->product->name,
                    ],
                    'warehouse' => [
                        'id' => $inventory->warehouse->id,
                        'name' => $inventory->warehouse->name,
                    ]
                ]
            ]));

        } catch (\Exception $e) {
            Log::error('设置库存预警失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(ApiResponse::error('设置库存预警失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取库存成本价信息
     *
     * @param Request $request
     * @param int $inventoryId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCostPrice(Request $request, $inventoryId)
    {
        try {
            $costPriceService = app(CostPriceService::class);
            $costPrice = $costPriceService->calculateMovingAverageCostPrice($inventoryId);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'cost_price' => $costPrice,
                    'method' => 'moving_average',
                    'calculation_time' => now()
                ],
                'message' => '成本价获取成功'
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取库存成本价失败', [
                'inventory_id' => $inventoryId,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '获取成本价失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 刷新成本价缓存
     */
    public function refreshCostPrice($inventoryId)
    {
        try {
            $costPriceService = app(CostPriceService::class);
            $costPriceInfo = $costPriceService->refreshCostPriceCache($inventoryId);
            
            return response()->json([
                'success' => true,
                'data' => $costPriceInfo,
                'message' => '成本价缓存刷新成功'
            ]);
            
        } catch (\Exception $e) {
            Log::error('刷新成本价缓存失败', [
                'inventory_id' => $inventoryId,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '刷新缓存失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 测试成本价功能（临时方法）
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function testCostPrice()
    {
        try {
            $results = [];
            
            // 获取前5个库存记录进行测试
            $inventories = Inventory::with(['product', 'warehouse'])->take(5)->get();
            
            foreach ($inventories as $inventory) {
                $costPrice30d = null;
                $error = null;
                
                try {
                    // 测试移动加权平均成本计算
                    $costPrice30d = $this->costPriceService->calculateMovingAverageCostPrice($inventory->id);
                    
                    // 获取成本价信息
                    $costPriceInfo = $this->costPriceService->getCostPriceInfo($inventory->id);
                    
                    $results[] = [
                        'inventory_id' => $inventory->id,
                        'product_name' => $inventory->product->name,
                        'warehouse_name' => $inventory->warehouse->name,
                        'current_stock' => $inventory->stock,
                        'moving_avg_cost' => $costPrice30d,
                        'cost_price_info' => $costPriceInfo,
                        'has_transactions' => $this->hasRecentTransactions($inventory),
                        'updated_at' => $inventory->updated_at->format('Y-m-d H:i:s')
                    ];
                } catch (\Exception $e) {
                    $results[] = [
                        'inventory_id' => $inventory->id,
                        'product_name' => $inventory->product->name,
                        'warehouse_name' => $inventory->warehouse->name,
                        'error' => $e->getMessage()
                    ];
                }
            }
            
            return response()->json(ApiResponse::success([
                'test_results' => $results,
                'total_tested' => count($results)
            ]));
            
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('测试失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 检查库存是否有最近的事务记录
     *
     * @param Inventory $inventory
     * @return array
     */
    private function hasRecentTransactions(Inventory $inventory): array
    {
        $recentTransactions = InventoryTransaction::where('product_id', $inventory->product_id)
            ->where('warehouse_id', $inventory->warehouse_id)
            ->where('created_at', '>=', now()->subDays(30))
            ->whereNotNull('unit_price')
            ->where('unit_price', '>', 0)
            ->count();
            
        $totalTransactions = InventoryTransaction::where('product_id', $inventory->product_id)
            ->where('warehouse_id', $inventory->warehouse_id)
            ->count();
            
        return [
            'recent_with_price' => $recentTransactions,
            'total' => $totalTransactions
        ];
    }

    /**
     * 获取库存成本价详细信息（包含多种计算方法）
     */
    public function getCostPriceInfo($inventoryId)
    {
        try {
            $costPriceService = app(CostPriceService::class);
            $costPriceInfo = $costPriceService->getCostPriceInfo($inventoryId);
            
            if (empty($costPriceInfo)) {
                return response()->json([
                    'success' => false,
                    'message' => '库存记录不存在'
                ], 404);
            }
            
            return response()->json([
                'success' => true,
                'data' => $costPriceInfo,
                'message' => '成本价信息获取成功'
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取库存成本价信息失败', [
                'inventory_id' => $inventoryId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '获取成本价信息失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取成本价计算方法列表
     */
    public function getCostMethods()
    {
        return response()->json([
            'success' => true,
            'data' => [
                [
                    'code' => 'moving_average',
                    'name' => '移动加权平均法',
                    'description' => 'ERP标准方法，每次入库后重新计算平均成本',
                    'recommended' => true
                ],
                [
                    'code' => 'fifo',
                    'name' => '先进先出法',
                    'description' => '最早入库的商品先出库，适用于有保质期的商品',
                    'recommended' => false
                ],
                [
                    'code' => 'standard',
                    'name' => '标准成本法',
                    'description' => '基于最近采购价格，适用于价格相对稳定的商品',
                    'recommended' => false
                ]
            ],
            'message' => '成本价计算方法获取成功'
        ]);
    }
}