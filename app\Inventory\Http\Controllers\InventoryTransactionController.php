<?php

namespace App\Inventory\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Inventory\Models\Inventory;
use App\Inventory\Models\InventoryTransaction;
use App\Inventory\Models\InventoryTransactionType;
use App\Product\Models\Product;
use App\Warehouse\Models\Warehouse;
use App\Unit\Services\UnitService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class InventoryTransactionController extends Controller
{
    protected $unitService;
    
    public function __construct(UnitService $unitService)
    {
        $this->unitService = $unitService;
    }
    
    /**
     * 获取库存事务列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = InventoryTransaction::with(['transactionType', 'product', 'warehouse', 'unit']);
        
        // 事务类型筛选
        if ($request->has('transaction_type_id')) {
            $query->where('transaction_type_id', $request->transaction_type_id);
        }
        
        // 商品筛选
        if ($request->has('product_id')) {
            $query->where('product_id', $request->product_id);
        }
        
        // 仓库筛选
        if ($request->has('warehouse_id')) {
            $query->where('warehouse_id', $request->warehouse_id);
        }
        
        // 日期范围筛选
        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        
        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }
        
        // 状态筛选
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }
        
        // 排序
        $orderBy = $request->order_by ?? 'created_at';
        $direction = $request->direction ?? 'desc';
        $query->orderBy($orderBy, $direction);
        
        $transactions = $query->paginate($request->per_page ?? 15);
        
        return response()->json(ApiResponse::success($transactions));
    }

    /**
     * 创建库存事务
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'transaction_type_id' => 'required|exists:inventory_transaction_types,id',
            'product_id' => 'required|exists:products,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'quantity' => 'required|numeric|not_in:0',
            'unit_id' => 'required|exists:units,id',
            'unit_price' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            DB::beginTransaction();
            
            $transactionType = InventoryTransactionType::findOrFail($request->transaction_type_id);
            
            // 计算总金额
            $totalAmount = $request->has('unit_price')
                ? $request->quantity * $request->unit_price
                : null;
            
            // 创建库存事务
            $transaction = InventoryTransaction::create([
                'transaction_type_id' => $request->transaction_type_id,
                'product_id' => $request->product_id,
                'warehouse_id' => $request->warehouse_id,
                'quantity' => $request->quantity * $transactionType->effect_direction,
                'unit_id' => $request->unit_id,
                'unit_price' => $request->unit_price,
                'total_amount' => $totalAmount,
                'status' => $request->has('status') ? $request->status : 'completed',
                'notes' => $request->notes,
                'created_by' => auth()->id() ?? 1,
                'updated_by' => auth()->id() ?? 1,
            ]);
            
            // 如果事务状态为已完成，则应用到库存
            if ($transaction->status === 'completed' && $transactionType->affects_inventory) {
                $transaction->applyToInventory();
            }
            
            DB::commit();
            
            return response()->json(ApiResponse::success($transaction->load(['transactionType', 'product', 'warehouse', 'unit']), '库存事务创建成功'), 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('库存事务创建失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取库存事务详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $transaction = InventoryTransaction::with([
            'transactionType', 
            'product', 
            'warehouse', 
            'unit', 
            'creator', 
            'updater'
        ])->findOrFail($id);
        
        return response()->json(ApiResponse::success($transaction));
    }

    /**
     * 完成待处理的库存事务
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function complete(Request $request, $id)
    {
        $transaction = InventoryTransaction::findOrFail($id);
        
        if ($transaction->status !== 'pending') {
            return response()->json(ApiResponse::error('只有待处理的库存事务可以标记为完成', 422), 422);
        }
        
        try {
            DB::beginTransaction();
            
            $transaction->status = 'completed';
            $transaction->updated_by = auth()->id() ?? 1;
            $transaction->save();
            
            // 应用到库存
            if ($transaction->transactionType->affects_inventory) {
                $transaction->applyToInventory();
            }
            
            DB::commit();
            
            return response()->json(ApiResponse::success($transaction, '库存事务已完成'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('完成库存事务失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 取消库存事务
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel(Request $request, $id)
    {
        $transaction = InventoryTransaction::findOrFail($id);
        
        if (!in_array($transaction->status, ['draft', 'pending'])) {
            return response()->json(ApiResponse::error('只有草稿或待处理的库存事务可以取消', 422), 422);
        }
        
        $transaction->update([
            'status' => 'canceled',
            'updated_by' => auth()->id() ?? 1,
        ]);
        
        return response()->json(ApiResponse::success($transaction, '库存事务已取消'));
    }

    /**
     * 获取商品在指定仓库的库存
     *
     * @param Request $request
     * @param int $productId
     * @param int $warehouseId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductInventory(Request $request, $productId, $warehouseId)
    {
        try {
            $product = Product::with(['baseUnit', 'units'])->findOrFail($productId);
            $warehouse = Warehouse::findOrFail($warehouseId);
            
            // 查找库存记录，包含所有必要的关联
            $inventory = Inventory::where('product_id', $productId)
                ->where('warehouse_id', $warehouseId)
                ->with(['warehouse', 'unit', 'product.units', 'product.baseUnit'])
                ->first();
                
            if (!$inventory) {
                // 如果没有库存记录，创建一个默认的库存记录
                $baseUnitId = $product->base_unit_id;
                
                // 如果商品没有设置基本单位，尝试从商品的单位中找到基本单位
                if (!$baseUnitId) {
                    $allUnits = $product->getAllUnits();
                    if ($allUnits && is_array($allUnits)) {
                        foreach ($allUnits as $unit) {
                            if (isset($unit['roles']) && is_array($unit['roles']) && 
                                (in_array('base', $unit['roles']) || in_array('default', $unit['roles']))) {
                                $baseUnitId = $unit['id'];
                                break;
                            }
                        }
                    }
                }
                
                // 如果还是没有找到基本单位，使用第一个可用单位
                if (!$baseUnitId && $allUnits && is_array($allUnits) && count($allUnits) > 0) {
                    $baseUnitId = $allUnits[0]['id'];
                }
                
                // 创建并保存库存记录
                $inventory = Inventory::create([
                    'product_id' => $productId,
                    'warehouse_id' => $warehouseId,
                    'unit_id' => $baseUnitId,
                    'stock' => 0,
                ]);
                
                // 重新加载关联数据
                $inventory->load(['warehouse', 'unit', 'product.units', 'product.baseUnit']);
            } else {
                // 如果库存记录存在但unit_id为null，自动设置为商品的基本单位
                if (!$inventory->unit_id) {
                    $baseUnitId = $product->base_unit_id;
                    
                    if (!$baseUnitId) {
                        $allUnits = $product->getAllUnits();
                        if ($allUnits && is_array($allUnits)) {
                            foreach ($allUnits as $unit) {
                                if (isset($unit['roles']) && is_array($unit['roles']) && 
                                    (in_array('base', $unit['roles']) || in_array('default', $unit['roles']))) {
                                    $baseUnitId = $unit['id'];
                                    break;
                                }
                            }
                        }
                    }
                    
                    if ($baseUnitId) {
                        $inventory->unit_id = $baseUnitId;
                        $inventory->save();
                        $inventory->load('unit'); // 重新加载unit关联
                    }
                }
            }
            
            // 获取商品的所有可用单位
            $availableUnits = $product->getAllUnits() ?? [];
            
            // 获取最近的库存事务记录
            $recentTransactions = InventoryTransaction::where('product_id', $productId)
                ->where('warehouse_id', $warehouseId)
                ->where('status', 'completed')
                ->with(['transactionType', 'unit'])
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();
                
            // 准备返回的库存信息
            $inventoryData = [
                'id' => $inventory->id,
                'product_id' => $inventory->product_id,
                'warehouse_id' => $inventory->warehouse_id,
                'stock' => $inventory->stock,
                'unit_id' => $inventory->unit_id,
                'min_stock_level' => $inventory->min_stock_level,
                'warehouse' => $inventory->warehouse ? [
                    'id' => $inventory->warehouse->id,
                    'location' => $inventory->warehouse->location,
                    'name' => $inventory->warehouse->name ?? $inventory->warehouse->location,
                ] : null,
                'unit' => $inventory->unit ? [
                    'id' => $inventory->unit->id,
                    'name' => $inventory->unit->name,
                    'symbol' => $inventory->unit->symbol ?? $inventory->unit->name,
                ] : null,
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'code' => $product->code,
                    'base_unit_id' => $product->base_unit_id,
                    'units' => $availableUnits,
                    'baseUnit' => $product->baseUnit ? [
                        'id' => $product->baseUnit->id,
                        'name' => $product->baseUnit->name,
                        'symbol' => $product->baseUnit->symbol ?? $product->baseUnit->name,
                    ] : null
                ]
            ];
                
            return response()->json(ApiResponse::success([
                'inventory' => $inventoryData,
                'available_units' => $availableUnits,
                'recent_transactions' => $recentTransactions,
            ]));
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取商品库存失败', [
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取库存信息失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 库存调整
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function adjust(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'new_quantity' => 'required|numeric|min:0',
            'unit_id' => 'required|exists:units,id',
            'reason' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            DB::beginTransaction();
            
            $product = Product::findOrFail($request->product_id);
            $warehouse = Warehouse::findOrFail($request->warehouse_id);
            
            // 获取或创建库存记录
            $inventory = $this->getOrCreateInventory($request->product_id, $request->warehouse_id, $request->unit_id);
            
            // 计算调整数量
            $currentStock = $inventory->stock;
            $newQuantity = $request->new_quantity;
            $adjustmentQuantity = $newQuantity - $currentStock;
            
            if ($adjustmentQuantity == 0) {
                return response()->json(ApiResponse::error('调整数量与当前库存相同，无需调整', 422), 422);
            }
            
            // 获取库存调整事务类型
            $transactionType = $this->getOrCreateTransactionType('inventory_adjustment', '库存调整', $adjustmentQuantity > 0 ? 1 : -1);
            
            // 创建库存调整事务
            $transaction = $this->createStockTransaction([
                'transaction_type_id' => $transactionType->id,
                'product_id' => $request->product_id,
                'warehouse_id' => $request->warehouse_id,
                'quantity' => $adjustmentQuantity,
                'unit_id' => $request->unit_id,
                'notes' => $request->reason,
            ]);
            
            // 应用库存调整
            $inventory->stock = $newQuantity;
            $inventory->save();
            
            // 更新相关统计
            $this->updateStockTotals($product, $warehouse);
            
            DB::commit();
            
            return response()->json(ApiResponse::success($transaction, '库存调整成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('库存调整失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取库存列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStockList(Request $request)
    {
        // 直接查询商品表，加载必要的关联数据
        $query = Product::with(['baseUnit', 'category', 'images']);
        
        // 商品筛选
        if ($request->has('product_id') && $request->product_id) {
            $query->where('id', $request->product_id);
        }
        
        // 关键词搜索
        if ($request->has('keyword') && $request->keyword) {
            $keyword = '%' . $request->keyword . '%';
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', $keyword)
                  ->orWhere('code', 'like', $keyword)
                  ->orWhere('description', 'like', $keyword);
            });
        }

        // 仓库筛选
        $warehouseId = $request->has('warehouse_id') && $request->warehouse_id ? $request->warehouse_id : null;
        
        // 库存状态筛选
        $stockStatus = $request->get('stock_status', 'all');
        
        // 排序
        $orderBy = $request->get('order_by', 'created_at');
        $direction = $request->get('direction', 'desc');
        $query->orderBy($orderBy, $direction);
        
        // 分页
        $products = $query->paginate($request->get('per_page', 20));
        
        // 准备返回数据
        $stockData = [];
        
        foreach ($products as $product) {
            // 获取库存记录
            $inventory = null;
            
            if ($warehouseId) {
                // 如果指定了仓库，获取指定仓库的库存
                $inventory = Inventory::where('product_id', $product->id)
                    ->where('warehouse_id', $warehouseId)
                    ->with(['warehouse', 'unit'])
                    ->first();
            } else {
                // 否则获取所有仓库的库存
                $inventories = Inventory::where('product_id', $product->id)
                    ->with(['warehouse', 'unit'])
                    ->get();
                
                // 如果有多个仓库的库存，处理每一个
                foreach ($inventories as $inv) {
                    $stockData[] = $this->formatStockItem($product, $inv);
                }
                
                // 如果没有找到库存记录，创建一个默认的
                if ($inventories->isEmpty()) {
                    $stockData[] = $this->formatStockItem($product, null);
                }
                
                // 继续下一个产品
                continue;
            }
            
            // 添加库存数据
            $stockData[] = $this->formatStockItem($product, $inventory);
        }
        
        // 根据库存状态筛选
        if ($stockStatus !== 'all') {
            $stockData = collect($stockData)->filter(function ($item) use ($stockStatus) {
                switch ($stockStatus) {
                    case 'normal':
                        return $item['stock_status'] === 'normal';
                    case 'low':
                        return $item['stock_status'] === 'low';
                    case 'out':
                        return $item['stock_status'] === 'out';
                    case 'in_stock':
                        return $item['stock_quantity'] > 0;
                    case 'out_of_stock':
                        return $item['stock_quantity'] <= 0;
                    case 'low_stock':
                        // 假设低库存阈值为10
                        return $item['stock_quantity'] > 0 && $item['stock_quantity'] <= 10;
                    default:
                        return true;
                }
            })->values()->all();
        }
        
        // 使用 ApiResponse 包装返回数据
        return response()->json(ApiResponse::success([
            'data' => $stockData,
            'total' => count($stockData),
            'per_page' => $request->get('per_page', 20),
            'current_page' => $request->get('page', 1),
        ]));
    }
    
    /**
     * 格式化库存项目
     * 
     * @param Product $product
     * @param Inventory|null $inventory
     * @return array
     */
    private function formatStockItem(Product $product, ?Inventory $inventory = null)
    {
        // 基本单位
        $baseUnit = $product->baseUnit;
        $baseUnitName = $baseUnit ? $baseUnit->name : '个';
        
        // 获取最近的入库和出库日期
        $lastInTransaction = null;
        $lastOutTransaction = null;
        
        if ($inventory) {
            $lastInTransaction = InventoryTransaction::where('product_id', $product->id)
                ->where('warehouse_id', $inventory->warehouse_id)
                ->where('quantity', '>', 0)
                ->where('status', 'completed')
                ->orderBy('created_at', 'desc')
                ->first();
                
            $lastOutTransaction = InventoryTransaction::where('product_id', $product->id)
                ->where('warehouse_id', $inventory->warehouse_id)
                ->where('quantity', '<', 0)
                ->where('status', 'completed')
                ->orderBy('created_at', 'desc')
                ->first();
        }
        
        $warehouse = $inventory ? $inventory->warehouse : null;
        
        // 获取单位名称，确保安全访问
        $unitName = $baseUnitName;
        if ($inventory) {
            // 优先使用unit关联，如果没有则使用unit_id加载或使用unit字段
            if ($inventory->relationLoaded('unit') && $inventory->unit) {
                $unitName = $inventory->unit->name;
            } elseif ($inventory->unit_id) {
                $unit = app(UnitService::class)->getUnitById($inventory->unit_id);
                $unitName = $unit ? $unit->name : $inventory->unit_name ?? $baseUnitName;
            } elseif (isset($inventory->unit) && is_string($inventory->unit)) {
                $unitName = $inventory->unit;
            }
        }
        
        // 获取商品的所有单位信息（包括销售单位）
        $availableUnits = [];
        try {
            Log::info('开始获取商品单位信息', [
                'product_id' => $product->id,
                'product_name' => $product->name
            ]);
            
            $allUnits = $product->getAllUnits();
            if ($allUnits && is_array($allUnits)) {
                $availableUnits = $allUnits;
                
                Log::info('成功获取商品单位信息', [
                    'product_id' => $product->id,
                    'units_count' => count($availableUnits),
                    'units' => $availableUnits
                ]);
                
                // 检查是否有销售单位
                $salesUnits = array_filter($availableUnits, function($unit) {
                    return isset($unit['roles']) && is_array($unit['roles']) && in_array('sales', $unit['roles']);
                });
                
                Log::info('销售单位检查结果', [
                    'product_id' => $product->id,
                    'sales_units_count' => count($salesUnits),
                    'sales_units' => array_values($salesUnits)
                ]);
            } else {
                Log::warning('getAllUnits返回空数据', [
                    'product_id' => $product->id,
                    'returned_data' => $allUnits
                ]);
            }
        } catch (\Exception $e) {
            // 如果获取失败，记录日志但不影响主要功能
            Log::warning('获取商品单位信息失败', [
                'product_id' => $product->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        // 计算库存价值
        $stockQuantity = $inventory ? $inventory->stock : 0;
        $stockValue = null;
        $costValue = null;
        
        if ($stockQuantity > 0) {
            // 使用sale_price作为销售价格
            if ($product->sale_price) {
                $stockValue = $stockQuantity * $product->sale_price;
            } elseif ($product->price) {
                // 如果没有sale_price，使用price作为备选
                $stockValue = $stockQuantity * $product->price;
            }
            
            if ($product->cost_price) {
                $costValue = $stockQuantity * $product->cost_price;
            }
        }
        
        // 获取商品图片
        $productImage = null;
        if ($product->cover_url) {
            $productImage = $product->cover_url;
        } elseif ($product->relationLoaded('images') && $product->images) {
            // 安全检查：确保images是Collection或数组且不为空
            $hasImages = false;
            if (is_object($product->images) && method_exists($product->images, 'isNotEmpty')) {
                $hasImages = $product->images->isNotEmpty();
            } elseif (is_array($product->images)) {
                $hasImages = count($product->images) > 0;
            }

            if ($hasImages) {
                $mainImage = null;
                if (is_object($product->images) && method_exists($product->images, 'where')) {
                    $mainImage = $product->images->where('is_main', true)->first();
                } elseif (is_array($product->images)) {
                    $mainImage = collect($product->images)->where('is_main', true)->first();
                }

                if ($mainImage) {
                    $productImage = $mainImage->url;
                } else {
                    if (is_object($product->images) && method_exists($product->images, 'first')) {
                        $productImage = $product->images->first()->url;
                    } elseif (is_array($product->images) && count($product->images) > 0) {
                        $productImage = $product->images[0]->url ?? null;
                    }
                }
            }
        }
        
        // 获取商品分类
        $productCategory = null;
        if ($product->relationLoaded('category') && $product->category) {
            $productCategory = $product->category->name;
        }
        
        // 计算库存周转天数（简化计算，基于最近30天的出库量）
        $turnoverDays = null;
        if ($inventory && $stockQuantity > 0) {
            $recentOutQuantity = InventoryTransaction::where('product_id', $product->id)
                ->where('warehouse_id', $inventory->warehouse_id)
                ->where('quantity', '<', 0)
                ->where('status', 'completed')
                ->where('created_at', '>=', now()->subDays(30))
                ->sum('quantity');
            
            if ($recentOutQuantity < 0) {
                $dailyOutQuantity = abs($recentOutQuantity) / 30;
                if ($dailyOutQuantity > 0) {
                    $turnoverDays = round($stockQuantity / $dailyOutQuantity);
                }
            }
        }
        
        // 生成预警信息
        $alerts = [];
        
        // 低库存预警
        if ($product->min_stock_level && $stockQuantity <= $product->min_stock_level) {
            $alerts[] = [
                'type' => 'low_stock',
                'message' => "库存低于最低库存量 {$product->min_stock_level}{$unitName}"
            ];
        }
        
        // 缺货预警
        if ($stockQuantity <= 0) {
            $alerts[] = [
                'type' => 'out_of_stock',
                'message' => '商品已缺货'
            ];
        }
        
        // 库存过多预警（如果库存是最低库存的10倍以上）
        if ($product->min_stock_level && $stockQuantity > ($product->min_stock_level * 10)) {
            $alerts[] = [
                'type' => 'overstock',
                'message' => '库存可能过多，建议检查'
            ];
        }
        
        // 确定库存状态
        $stockStatus = 'normal';
        if ($stockQuantity <= 0) {
            $stockStatus = 'out';
        } elseif ($product->min_stock_level && $stockQuantity <= $product->min_stock_level) {
            $stockStatus = 'low';
        }
        
        // 计算可销售数量
        $saleableQuantity = $this->calculateSaleableQuantity($stockQuantity, $availableUnits, $unitName, $product);
        
        return [
            'id' => $inventory ? $inventory->id : $product->id,
            'product_id' => $product->id,
            'product_code' => $product->code,
            'product_name' => $product->name,
            'product_image' => $productImage,
            'product_category' => $productCategory,
            'product_price' => $product->sale_price ?? $product->price, // 优先使用sale_price
            'code' => $product->code, // 向后兼容
            'name' => $product->name, // 向后兼容
            'stock_quantity' => $stockQuantity,
            'stock' => $stockQuantity, // 向后兼容
            'base_stock' => $inventory ? $inventory->getStockInBaseUnit() : 0,
            'stock_unit' => $unitName,
            'unit' => $unitName, // 向后兼容
            'base_unit' => $baseUnitName,
            'stock_status' => $stockStatus,
            'conversion_rate' => 1, // 默认转换率
            'available_units' => $availableUnits, // 商品的所有单位信息
            'saleable_quantity' => $saleableQuantity, // 可销售数量信息
            'last_in_date' => $lastInTransaction ? $lastInTransaction->created_at->format('Y-m-d') : null,
            'last_out_date' => $lastOutTransaction ? $lastOutTransaction->created_at->format('Y-m-d') : null,
            'product' => [
                'id' => $product->id,
                'code' => $product->code,
                'name' => $product->name,
                'units' => $availableUnits // 也在product对象中包含单位信息
            ],
            'warehouse_id' => $warehouse ? $warehouse->id : null,
            'warehouse_name' => $warehouse ? ($warehouse->location ?? $warehouse->name) : null,
            'warehouse' => $warehouse ? [
                'id' => $warehouse->id,
                'name' => $warehouse->name,
                'location' => $warehouse->location
            ] : null,
            // 价格信息
            'sale_price' => $product->sale_price ?? null,
            'cost_price' => $product->cost_price ?? null,
            'stock_value' => $stockValue,
            'cost_value' => $costValue,
            // 库存管理信息
            'min_stock_level' => $product->min_stock_level ?? null,
            'turnover_days' => $turnoverDays,
            'alerts' => $alerts,
            // 向后兼容的销售单位字段
            'sale_unit' => null, // 将通过available_units中的sales角色单位确定
            'sale_unit_conversion_rate' => 1,
            'min_sale_quantity' => $product->min_sale_quantity ?? 1
        ];
    }

    /**
     * 计算可销售数量
     * 
     * @param float $stockQuantity 库存数量
     * @param array $availableUnits 可用单位
     * @param string $stockUnit 库存单位
     * @param Product $product 商品对象
     * @return array
     */
    private function calculateSaleableQuantity(float $stockQuantity, array $availableUnits, string $stockUnit, Product $product): array
    {
        if ($stockQuantity <= 0) {
            return [
                'quantity' => 0,
                'unit' => $stockUnit,
                'canSale' => false,
                'remainder' => 0
            ];
        }
        
        // 从available_units中查找具有'sales'角色的单位
        $saleUnit = null;
        foreach ($availableUnits as $unit) {
            if (isset($unit['roles']) && is_array($unit['roles']) && in_array('sales', $unit['roles'])) {
                $saleUnit = $unit;
                break;
            }
        }
        
        if ($saleUnit && isset($saleUnit['conversion_factor']) && $saleUnit['conversion_factor'] > 0) {
            // 确保转换系数不为零，避免除零错误
            $conversionFactor = floatval($saleUnit['conversion_factor']);
            
            // 根据Unit模块的设计：conversion_factor表示1个销售单位等于多少个基本单位
            // 所以可销售数量 = 库存数量(基本单位) ÷ 销售单位转换系数
            $saleableQty = floor($stockQuantity / $conversionFactor);
            
            // 修复余数计算：余数 = 库存数量 - (可销售数量 × 转换系数)
            $soldQuantityInBaseUnit = $saleableQty * $conversionFactor;
            $remainder = $stockQuantity - $soldQuantityInBaseUnit;
            
            // 如果商品有价格信息，计算可销售价值
            $saleableValue = null;
            $salePrice = $product->sale_price ?? $product->price;
            if ($salePrice && $saleableQty > 0) {
                // 修复价格计算：价格基于基本单位，需要根据转换系数调整
                // 销售单位的单价 = 基本单位价格 × 转换系数
                $saleUnitPrice = $salePrice * $conversionFactor;
                $saleableValue = $saleableQty * $saleUnitPrice;
            }
            
            return [
                'quantity' => $saleableQty,
                'unit' => $saleUnit['name'],
                'canSale' => $saleableQty > 0,
                'remainder' => $remainder,
                'value' => $saleableValue,
                'conversionInfo' => "1{$saleUnit['name']} = {$conversionFactor}{$stockUnit}"
            ];
        }
        
        // 如果没有有效的销售单位，检查是否有基本单位可以直接销售
        $baseUnit = null;
        foreach ($availableUnits as $unit) {
            if (isset($unit['roles']) && is_array($unit['roles']) && 
                (in_array('base', $unit['roles']) || in_array('default', $unit['roles']))) {
                $baseUnit = $unit;
                break;
            }
        }
        
        if ($baseUnit) {
            $salePrice = $product->sale_price ?? $product->price;
            return [
                'quantity' => $stockQuantity,
                'unit' => $baseUnit['name'],
                'canSale' => true,
                'remainder' => 0,
                'value' => $salePrice ? $stockQuantity * $salePrice : null
            ];
        }
        
        // 如果都没有，返回不可销售
        return [
            'quantity' => 0,
            'unit' => $stockUnit,
            'canSale' => false,
            'remainder' => $stockQuantity
        ];
    }

    /**
     * 根据单位名称或商品基本单位获取单位ID
     *
     * @param Product $product
     * @param string $unitName
     * @return int|null
     */
    private function resolveUnitId(Product $product, string $unitName): ?int
    {
        // 如果商品有基本单位，优先使用
        if ($product->base_unit_id) {
            return $product->base_unit_id;
        }
        
        // 尝试根据名称查找单位
        $unit = \App\Unit\Models\Unit::where('name', $unitName)
            ->orWhere('display_name', $unitName)
            ->first();
            
        if ($unit) {
            return $unit->id;
        }
        
        // 尝试查找默认单位
        $defaultUnit = \App\Unit\Models\Unit::where('is_base', true)->first();
        return $defaultUnit ? $defaultUnit->id : null;
    }

    /**
     * 获取或创建库存记录
     *
     * @param int $productId
     * @param int $warehouseId
     * @param int $unitId
     * @return Inventory
     */
    private function getOrCreateInventory(int $productId, int $warehouseId, int $unitId): Inventory
    {
        $inventory = Inventory::firstOrCreate(
            [
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
            ],
            [
                'unit_id' => $unitId,
                'stock' => 0,
            ]
        );
        
        // 确保inventory有unit_id
        if (!$inventory->unit_id) {
            $inventory->unit_id = $unitId;
            $inventory->save();
        }
        
        return $inventory;
    }

    /**
     * 获取或创建事务类型
     *
     * @param string $code
     * @param string $name
     * @param int $effectDirection
     * @return InventoryTransactionType
     */
    private function getOrCreateTransactionType(string $code, string $name, int $effectDirection): InventoryTransactionType
    {
        return InventoryTransactionType::firstOrCreate(
            ['code' => $code],
            [
                'name' => $name,
                'effect_direction' => $effectDirection,
                'affects_inventory' => true,
            ]
        );
    }

    /**
     * 创建库存事务
     *
     * @param array $data
     * @return InventoryTransaction
     */
    private function createStockTransaction(array $data): InventoryTransaction
    {
        return InventoryTransaction::create([
            'transaction_type_id' => $data['transaction_type_id'],
            'product_id' => $data['product_id'],
            'warehouse_id' => $data['warehouse_id'],
            'quantity' => $data['quantity'],
            'unit_id' => $data['unit_id'],
            'status' => 'completed',
            'notes' => $data['notes'] ?? '',
            'created_by' => auth()->id() ?? 1,
            'updated_by' => auth()->id() ?? 1,
        ]);
    }

    /**
     * 更新相关库存统计
     *
     * @param Product $product
     * @param Warehouse $warehouse
     * @return void
     */
    private function updateStockTotals(Product $product, Warehouse $warehouse): void
    {
        // 更新商品总库存
        if (method_exists($product, 'updateTotalStock')) {
            $product->updateTotalStock();
        }
        
        // 更新仓库总库存
        if (method_exists($warehouse, 'updateTotalStock')) {
            $warehouse->updateTotalStock();
        }
    }

    /**
     * 增加库存
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addStock(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'quantity' => 'required|numeric|min:0.01',
            'unit_id' => 'required|exists:units,id',
            'batch_number' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            DB::beginTransaction();
            
            $product = Product::findOrFail($request->product_id);
            $warehouse = Warehouse::findOrFail($request->warehouse_id);
            
            // 验证单位是否属于该商品
            $productUnits = $product->getAllUnits();
            $validUnit = collect($productUnits)->firstWhere('id', $request->unit_id);
            if (!$validUnit) {
                DB::rollBack();
                return response()->json(ApiResponse::error('所选单位不适用于该商品', 422), 422);
            }
            
            // 计算基本单位数量（用于库存存储）
            $conversionFactor = floatval($validUnit['conversion_factor'] ?? 1);
            $baseUnitQuantity = $request->quantity * $conversionFactor;
            
            // 获取基本单位ID
            $baseUnitId = $product->base_unit_id;
            if (!$baseUnitId) {
                // 如果没有基本单位，查找base角色的单位
                $baseUnit = collect($productUnits)->first(function($unit) {
                    return in_array('base', $unit['roles'] ?? []);
                });
                $baseUnitId = $baseUnit ? $baseUnit['id'] : $request->unit_id;
            }
            
            // 获取或创建库存记录（使用基本单位）
            $inventory = $this->getOrCreateInventory($request->product_id, $request->warehouse_id, $baseUnitId);
            
            // 获取入库事务类型
            $transactionType = $this->getOrCreateTransactionType('stock_in', '入库', 1);
            
            // 创建库存入库事务
            $transaction = $this->createStockTransaction([
                'transaction_type_id' => $transactionType->id,
                'product_id' => $request->product_id,
                'warehouse_id' => $request->warehouse_id,
                'quantity' => $baseUnitQuantity,
                'unit_id' => $request->unit_id,
                'notes' => $request->notes ?? '手动增加库存',
            ]);
            
            // 更新库存
            $inventory->stock += $baseUnitQuantity;
            $inventory->save();
            
            // 更新相关统计
            $this->updateStockTotals($product, $warehouse);
            
            DB::commit();
            
            // 准备响应数据
            $responseData = [
                'transaction' => $transaction,
                'inventory' => $inventory,
                'conversion_info' => [
                    'input_quantity' => $request->quantity,
                    'input_unit' => $validUnit['name'],
                    'base_quantity' => $baseUnitQuantity,
                    'base_unit' => $inventory->unit ? $inventory->unit->name : '基本单位',
                    'conversion_factor' => $conversionFactor,
                    'conversion_note' => $conversionFactor != 1 ? 
                        "入库{$request->quantity}{$validUnit['name']} = {$baseUnitQuantity}基本单位" : 
                        "直接以基本单位入库"
                ],
                'message' => '入库成功'
            ];
            
            return response()->json(ApiResponse::success($responseData, '库存增加成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('增加库存失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 减少库存
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reduceStock(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'quantity' => 'required|numeric|min:0.01',
            'unit' => 'required|string',
            'reason' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            DB::beginTransaction();
            
            $product = Product::findOrFail($request->product_id);
            $warehouse = Warehouse::findOrFail($request->warehouse_id);
            
            // 解析单位ID
            $unitId = $this->resolveUnitId($product, $request->unit);
            if (!$unitId) {
                DB::rollBack();
                return response()->json(ApiResponse::error('无法减少库存：未找到有效的单位，请先为商品设置基本单位', 422), 422);
            }
            
            // 获取库存记录
            $inventory = Inventory::where('product_id', $request->product_id)
                ->where('warehouse_id', $request->warehouse_id)
                ->first();
                
            if (!$inventory) {
                return response()->json(ApiResponse::error('该商品在指定仓库中没有库存记录', 404), 404);
            }
            
            // 确保inventory有unit_id
            if (!$inventory->unit_id) {
                $inventory->unit_id = $unitId;
                $inventory->save();
            }
            
            // 检查库存是否充足
            if ($inventory->stock < $request->quantity) {
                return response()->json(ApiResponse::error('库存不足，无法减少', 422), 422);
            }
            
            // 获取出库事务类型
            $transactionType = $this->getOrCreateTransactionType('stock_out', '出库', -1);
            
            // 创建库存出库事务
            $transaction = $this->createStockTransaction([
                'transaction_type_id' => $transactionType->id,
                'product_id' => $request->product_id,
                'warehouse_id' => $request->warehouse_id,
                'quantity' => -$request->quantity, // 负数表示减少
                'unit_id' => $unitId,
                'notes' => $request->reason ?? '手动减少库存',
            ]);
            
            // 更新库存
            $inventory->stock -= $request->quantity;
            $inventory->save();
            
            // 更新相关统计
            $this->updateStockTotals($product, $warehouse);
            
            DB::commit();
            
            return response()->json(ApiResponse::success($transaction, '库存减少成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('减少库存失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取库存统计数据
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats()
    {
        try {
            // 商品种类统计
            $totalProducts = Inventory::distinct('product_id')->count('product_id');
            
            // 低库存商品统计（这里需要根据实际业务逻辑定义低库存阈值）
            $lowStockProducts = Inventory::where('stock', '<', 10)->count();
            
            // 即将过期批次统计
            $nearExpiryBatches = \App\Inventory\Models\InventoryBatch::where('expiry_date', '>=', now())
                ->where('expiry_date', '<=', now()->addDays(7))
                ->where('quantity', '>', 0)
                ->count();
            
            // 库存总价值计算（需要结合商品价格）
            $totalValue = Inventory::join('products', 'inventory.product_id', '=', 'products.id')
                ->selectRaw('SUM(inventory.stock * COALESCE(products.cost_price, products.sale_price, 0)) as total_value')
                ->value('total_value') ?? 0;
            
            return response()->json(ApiResponse::success([
                'totalProducts' => $totalProducts,
                'lowStockProducts' => $lowStockProducts,
                'nearExpiryBatches' => $nearExpiryBatches,
                'totalValue' => round($totalValue, 2)
            ]));
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取库存统计失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取统计数据失败'), 500);
        }
    }

    /**
     * 测试API连接
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function test()
    {
        return response()->json(ApiResponse::success([
            'message' => 'API连接正常',
            'timestamp' => now()->toISOString(),
            'user' => auth()->user() ? auth()->user()->id : 'guest'
        ]));
    }

    /**
     * 获取商品的所有库存信息（按仓库分组）
     *
     * @param Request $request
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductInventories(Request $request, $productId)
    {
        try {
            $product = Product::with(['baseUnit', 'category', 'images'])->findOrFail($productId);
            
            // 获取商品的所有可用单位
            $availableUnits = $product->getAllUnits() ?? [];
            
            // 获取商品在所有仓库的库存记录
            $inventories = Inventory::where('product_id', $productId)
                ->with(['warehouse', 'unit'])
                ->get();
            
            // 格式化库存数据
            $inventoryData = [];
            foreach ($inventories as $inventory) {
                // 确保库存记录有正确的unit_id
                if (!$inventory->unit_id && $product->base_unit_id) {
                    $inventory->unit_id = $product->base_unit_id;
                    $inventory->save();
                    $inventory->load('unit');
                }
                
                // 获取库存单位和销售单位
                $stockUnit = $inventory->unit ? [
                    'id' => $inventory->unit->id,
                    'name' => $inventory->unit->name,
                    'symbol' => $inventory->unit->symbol ?? $inventory->unit->name,
                ] : null;
                
                // 查找销售单位
                $salesUnit = null;
                foreach ($availableUnits as $unit) {
                    if (isset($unit['roles']) && is_array($unit['roles']) && in_array('sales', $unit['roles'])) {
                        $salesUnit = [
                            'id' => $unit['id'],
                            'name' => $unit['name'],
                            'symbol' => $unit['symbol'] ?? $unit['name'],
                            'conversion_factor' => $unit['conversion_factor'] ?? 1,
                        ];
                        break;
                    }
                }
                
                // 如果没有销售单位，使用基本单位
                if (!$salesUnit && $product->baseUnit) {
                    $salesUnit = [
                        'id' => $product->baseUnit->id,
                        'name' => $product->baseUnit->name,
                        'symbol' => $product->baseUnit->symbol ?? $product->baseUnit->name,
                        'conversion_factor' => 1,
                    ];
                }
                
                $inventoryData[] = [
                    'id' => $inventory->id,
                    'warehouse' => [
                        'id' => $inventory->warehouse->id,
                        'name' => $inventory->warehouse->location,
                        'location' => $inventory->warehouse->location,
                    ],
                    'stock' => $inventory->stock,
                    'stock_unit' => $stockUnit,
                    'sales_unit' => $salesUnit,
                    'min_stock_level' => $inventory->min_stock_level,
                ];
            }
            
            // 商品基本信息
            $productData = [
                'id' => $product->id,
                'code' => $product->code,
                'name' => $product->name,
                'category' => $product->category ? [
                    'id' => $product->category->id,
                    'name' => $product->category->name,
                ] : null,
                'base_unit' => $product->baseUnit ? [
                    'id' => $product->baseUnit->id,
                    'name' => $product->baseUnit->name,
                    'symbol' => $product->baseUnit->symbol ?? $product->baseUnit->name,
                ] : null,
                'price' => $product->sale_price ?? $product->price,
                'cost_price' => $product->cost_price,
                'total_stock' => $product->stock,
                'image' => $this->getProductImageSafe($product),
            ];
            
            return response()->json(ApiResponse::success([
                'product' => $productData,
                'inventories' => $inventoryData,
                'available_units' => $availableUnits,
            ]));
            
        } catch (\Exception $e) {
            Log::error('获取商品库存信息失败', [
                'product_id' => $productId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(ApiResponse::error('获取商品库存信息失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 安全获取商品图片
     */
    private function getProductImageSafe($product)
    {
        if ($product->cover_url) {
            return $product->cover_url;
        }

        if ($product->images) {
            // 安全检查：确保images是Collection或数组且不为空
            if (is_object($product->images) && method_exists($product->images, 'isNotEmpty')) {
                if ($product->images->isNotEmpty()) {
                    return $product->images->first()->url ?? null;
                }
            } elseif (is_array($product->images) && count($product->images) > 0) {
                return $product->images[0]->url ?? null;
            }
        }

        return null;
    }
}