# Laravel 10 部署检查清单

## 部署前检查
- [ ] 代码已提交到Git仓库
- [ ] .env.example 文件已更新
- [ ] 数据库迁移文件已创建
- [ ] 前端资源已编译（如果有）
- [ ] 测试用例通过

## 服务器环境
- [ ] PHP 8.1+ 已安装
- [ ] Composer 已安装
- [ ] MySQL 5.7+ 已安装
- [ ] Redis 已安装（如需要）
- [ ] Nginx/Apache 已配置

## 部署步骤
- [ ] 拉取最新代码
- [ ] 安装Composer依赖
- [ ] 复制并配置.env文件
- [ ] 生成应用密钥
- [ ] 运行数据库迁移
- [ ] 创建存储链接
- [ ] 设置目录权限
- [ ] 配置Web服务器
- [ ] 配置SSL证书

## 优化步骤
- [ ] 执行 composer install --optimize-autoloader --no-dev
- [ ] 执行 php artisan config:cache
- [ ] 执行 php artisan route:cache
- [ ] 执行 php artisan view:cache
- [ ] 启用OPcache
- [ ] 配置Redis缓存

## 安全检查
- [ ] APP_DEBUG=false
- [ ] 数据库密码已更改
- [ ] 删除安装脚本
- [ ] 配置防火墙规则
- [ ] 限制上传文件类型
- [ ] 配置HTTPS

## 监控配置
- [ ] 错误日志配置
- [ ] 访问日志配置
- [ ] 性能监控工具
- [ ] 备份策略配置

## 测试验证
- [ ] 首页可以访问
- [ ] API接口正常
- [ ] 数据库连接正常
- [ ] 文件上传功能正常
- [ ] 邮件发送功能正常（如有）
- [ ] 队列任务正常（如有） 