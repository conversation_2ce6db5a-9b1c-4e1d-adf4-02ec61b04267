<?php

namespace App\Purchase\Models;

use App\Supplier\Models\Supplier;
use App\Warehouse\Models\Warehouse;
use App\Models\User;
use App\Inventory\Models\InventoryTransaction;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseOrder extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_number',
        'supplier_id',
        'status',
        'order_date',
        'expected_delivery_date',
        'warehouse_id',
        'total_amount',
        'subtotal_amount',
        'paid_amount',
        'notes',
        'payment_method',
        'priority',
        'shipping_fee',
        'tax_fee',
        'other_fee',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'order_date' => 'date',
        'expected_delivery_date' => 'date',
        'total_amount' => 'decimal:2',
        'subtotal_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'shipping_fee' => 'decimal:2',
        'tax_fee' => 'decimal:2',
        'other_fee' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    /**
     * 获取供应商
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * 获取收货仓库
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * 获取采购明细
     */
    public function items()
    {
        return $this->hasMany(PurchaseItem::class);
    }

    /**
     * 获取创建人
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取审批人
     */
    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * 获取应付金额
     */
    public function getDueAmountAttribute()
    {
        return $this->total_amount - $this->paid_amount;
    }

    /**
     * 获取与此采购单关联的库存事务
     */
    public function inventoryTransactions()
    {
        return $this->morphMany(InventoryTransaction::class, 'reference');
    }
} 