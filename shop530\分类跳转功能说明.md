# 分类跳转功能实现说明

## 📋 功能概述

实现从主页点击分类跳转到分类页面，并自动选中对应分类的功能。支持三级分类的智能展开和选择。

## ✅ 已实现功能

1. **主页分类点击跳转**：使用 `wx.switchTab` + 全局数据传递方式
2. **模块化组件跳转**：使用 `wx.navigateTo` + URL参数方式
3. **智能分类选择**：根据分类层级自动展开父分类和祖父分类
4. **三级分类支持**：完整支持一级、二级、三级分类的跳转和选择
5. **时间戳验证**：防止重复处理和过期数据干扰

## 🔄 实现方案

### 方案1: 主页分类网格跳转

**流程：**
```
用户点击主页分类 
→ 保存分类信息到全局数据 
→ wx.switchTab跳转 
→ 分类页面onShow检查全局数据 
→ 自动选中目标分类
```

**关键代码：**
```javascript
// 主页分类点击 (shop530/pages/index/index.js)
onCategoryTap(e) {
  const category = e.currentTarget.dataset.category;
  
  // 保存到全局数据
  const app = getApp();
  app.globalData.selectedCategoryFromHome = {
    id: category.id,
    name: category.name,
    icon: category.icon,
    timestamp: Date.now()
  };
  
  // 跳转到分类页面
  wx.switchTab({ url: '/pages/category/category' });
}
```

### 方案2: 模块化组件跳转

**流程：**
```
组件触发分类点击 
→ wx.navigateTo + URL参数 
→ 分类页面onLoad接收参数 
→ initData完成后自动选中
```

**关键代码：**
```javascript
// 模块化组件跳转 (shop530/pages/index/modules/event-handlers.js)
onCategoryTap(e) {
  const { category } = e.detail;
  
  wx.navigateTo({
    url: `/pages/category/category?id=${category.id}&name=${encodeURIComponent(category.name)}`
  });
}
```

## 🎯 核心实现方法

### 1. 分类页面跳转检测

```javascript
// 检查主页跳转 (onShow)
checkAndSelectCategoryFromHome() {
  const app = getApp();
  const selectedCategory = app.globalData?.selectedCategoryFromHome;
  
  if (selectedCategory && selectedCategory.timestamp) {
    // 检查时间戳（5秒内有效）
    const timeDiff = Date.now() - selectedCategory.timestamp;
    if (timeDiff < 5000) {
      // 清除全局数据，避免重复处理
      app.globalData.selectedCategoryFromHome = null;
      // 选中目标分类
      this.selectCategoryWhenReady(selectedCategory.id);
    }
  }
}

// 检查URL参数 (onLoad)
onLoad(options) {
  if (options.categoryId || options.id) {
    const categoryId = parseInt(options.categoryId || options.id);
    this.setData({ targetCategoryId: categoryId });
  }
}
```

### 2. 智能分类选择

```javascript
// 根据ID选中分类
selectCategoryById(categoryId) {
  const targetCategory = this.findCategoryById(categoryId);
  
  if (targetCategory.level === 1) {
    this.selectMainCategory(targetCategory);
  } else if (targetCategory.level === 2) {
    this.selectSubCategory(targetCategory);
  } else if (targetCategory.level === 3) {
    this.selectThirdCategory(targetCategory);
  }
}

// 在分类树中查找分类
findCategoryById(categoryId) {
  // 查找一级分类
  for (const category of this.data.categories) {
    if (category.id === categoryId) {
      return { ...category, level: 1 };
    }
    
    // 查找二级分类
    if (category.children_data) {
      for (const subCategory of category.children_data) {
        if (subCategory.id === categoryId) {
          return { 
            ...subCategory, 
            level: 2, 
            parentId: category.id, 
            parentCategory: category 
          };
        }
        
        // 查找三级分类
        if (subCategory.children_data) {
          for (const thirdCategory of subCategory.children_data) {
            if (thirdCategory.id === categoryId) {
              return { 
                ...thirdCategory, 
                level: 3, 
                parentId: subCategory.id, 
                grandParentId: category.id,
                parentCategory: subCategory,
                grandParentCategory: category
              };
            }
          }
        }
      }
    }
  }
  return null;
}
```

### 3. 分层级选择处理

```javascript
// 选中一级分类
selectMainCategory(category) {
  this.handleCategoryTap({
    currentTarget: {
      dataset: { id: category.id, type: 'main' }
    }
  });
}

// 选中二级分类
selectSubCategory(category) {
  // 先展开父分类
  this.expandCategory(category.parentId);
  
  // 延迟选中子分类
  setTimeout(() => {
    this.handleCategoryTap({
      currentTarget: {
        dataset: {
          id: category.id,
          type: 'sub',
          parentId: category.parentId
        }
      }
    });
  }, 300);
}

// 选中三级分类
selectThirdCategory(category) {
  // 先展开祖父分类
  this.expandCategory(category.grandParentId);
  
  // 延迟选中父分类
  setTimeout(() => {
    this.handleCategoryTap({
      currentTarget: {
        dataset: {
          id: category.parentId,
          type: 'sub',
          parentId: category.grandParentId
        }
      }
    });
    
    // 再延迟选中三级分类
    setTimeout(() => {
      this.handleThirdCategoryTap({
        currentTarget: {
          dataset: { id: category.id }
        }
      });
    }, 300);
  }, 300);
}
```

## 📁 修改的文件

### 1. `shop530/pages/index/index.js`
**修改内容：**
- 优化 `onCategoryTap` 方法，添加全局数据保存和时间戳

### 2. `shop530/pages/category/category.js`
**新增方法：**
- `checkAndSelectCategoryFromHome()`: 检查主页跳转
- `selectCategoryWhenReady()`: 等待数据准备后选中
- `selectCategoryById()`: 根据ID智能选中分类
- `findCategoryById()`: 在分类树中查找分类
- `selectMainCategory()`: 选中一级分类
- `selectSubCategory()`: 选中二级分类
- `selectThirdCategory()`: 选中三级分类

**修改方法：**
- `onLoad()`: 处理URL参数 `id` 和 `categoryId`
- `onShow()`: 添加主页跳转检查
- `initData()`: 在数据加载完成后检查目标分类

### 3. `shop530/pages/index/modules/event-handlers.js`
**已存在功能：**
- URL参数跳转方式已实现

## 🧪 测试场景

### 测试1: 一级分类跳转
- **操作：** 从主页点击一级分类（如"新鲜蔬菜"）
- **期望：** 跳转到分类页面，自动选中该一级分类，展开子分类，加载所有子分类商品

### 测试2: 二级分类跳转
- **操作：** 从主页点击二级分类（如"叶菜类"）
- **期望：** 跳转到分类页面，展开父分类，选中该二级分类，显示三级分类

### 测试3: 三级分类跳转
- **操作：** 从主页点击三级分类（如"菠菜"）
- **期望：** 跳转到分类页面，展开祖父分类和父分类，选中该三级分类

### 测试4: URL参数跳转
- **操作：** 通过URL参数跳转 `/pages/category/category?id=123`
- **期望：** 自动选中ID为123的分类

## ⚡ 性能优化

1. **时间戳验证**：防止过期数据处理，5秒内有效
2. **延迟选择**：使用300ms延迟确保展开动画完成
3. **数据清理**：处理完成后立即清除全局数据
4. **重复检查**：避免重复处理同一个跳转请求

## 🔍 调试信息

关键日志输出：
- `🎯 检测到主页分类跳转:` - 检测到主页跳转
- `🎯 设置目标分类ID:` - URL参数设置
- `🎯 尝试选中分类ID:` - 开始选中分类
- `✅ 找到目标分类:` - 成功找到分类
- `🎯 选中X级分类:` - 选中不同层级分类

## 📝 使用说明

1. 确保主页分类数据包含正确的 `id` 字段
2. 分类页面会自动处理跳转，无需额外配置
3. 支持两种跳转方式，可根据需要选择使用
4. 三级分类结构需要正确的 `children_data` 层级关系
