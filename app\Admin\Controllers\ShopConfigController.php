<?php

namespace App\Admin\Controllers;

use App\Admin\Http\Controllers\ShopConfigController as HttpShopConfigController;
use App\shop\Services\ConfigService;
use Illuminate\Http\Request;

/**
 * 商店配置控制器（代理类）
 * 
 * 该类作为App\Admin\Http\Controllers\ShopConfigController的代理
 * 用于保持向后兼容性
 */
class ShopConfigController extends HttpShopConfigController
{
    /**
     * 构造函数
     */
    public function __construct(ConfigService $configService)
    {
        parent::__construct($configService);
    }
} 