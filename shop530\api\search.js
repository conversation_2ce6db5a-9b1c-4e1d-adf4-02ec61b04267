/**
 * 搜索相关API
 */
const request = require('../utils/request');
const { cacheManager } = require('../utils/search');

// 请求超时时间（毫秒）
const REQUEST_TIMEOUT = 10000;

/**
 * 创建带超时的请求
 * @param {Promise} requestPromise - 原始请求Promise
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise} - 带超时的Promise
 */
function withTimeout(requestPromise, timeout = REQUEST_TIMEOUT) {
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error('请求超时'));
    }, timeout);
  });
  
  return Promise.race([requestPromise, timeoutPromise]);
}

/**
 * 获取热门搜索词
 * @returns {Promise} - 请求Promise
 */
function getHotKeywords() {
  // 先尝试从缓存获取
  const cached = cacheManager.get('hotKeywords');
  if (cached) {
    return Promise.resolve({ data: cached });
  }
  
  return withTimeout(
    request.get('/search/hot-keywords')
      .then(res => {
        // 缓存结果，30分钟过期
        if (res.data && Array.isArray(res.data)) {
          cacheManager.set('hotKeywords', res.data, 30 * 60 * 1000);
        }
        return res;
      })
      .catch(err => {
        console.error('获取热门搜索词失败', err);
        // 返回空数组而不是拒绝Promise
        return { data: [] };
      })
  );
}

/**
 * 获取搜索建议
 * @param {string} keyword - 搜索关键词
 * @returns {Promise} - 请求Promise
 */
function getSuggestions(keyword) {
  if (!keyword) {
    return Promise.resolve({ data: [] });
  }
  
  // 先尝试从缓存获取
  const cacheKey = `suggestions_${keyword}`;
  const cached = cacheManager.get(cacheKey);
  if (cached) {
    return Promise.resolve({ data: cached });
  }
  
  return withTimeout(
    request.get('/search/suggestions', { keyword })
      .then(res => {
        // 缓存结果，5分钟过期
        if (res.data && Array.isArray(res.data)) {
          cacheManager.set(cacheKey, res.data, 5 * 60 * 1000);
        }
        return res;
      })
      .catch(err => {
        console.error('获取搜索建议失败', err);
        // 返回空数组而不是拒绝Promise
        return { data: [] };
      })
  );
}

/**
 * 搜索商品
 * @param {Object} params - 搜索参数
 * @returns {Promise} - 请求Promise
 */
function searchProducts(params) {
  const { keyword } = params;
  
  if (!keyword) {
    return Promise.resolve({ data: { list: [], total: 0 } });
  }
  
  // 确保参数类型正确
  const cleanParams = {
    keyword: String(keyword || ''),
    page: Number(params.page) || 1,
    pageSize: Number(params.pageSize) || 10,
    sort: String(params.sort || 'default'),
    order: String(params.order || 'desc'),
    platform: String(params.platform || 'miniprogram')
  };
  
  // 添加可选参数，确保类型正确
  if (params.categoryId !== undefined && params.categoryId !== null) {
    const categoryId = parseInt(params.categoryId, 10);
    if (!isNaN(categoryId)) {
      cleanParams.categoryId = categoryId;
    }
  }
  
  if (params.minPrice !== undefined) {
    const minPrice = parseFloat(params.minPrice);
    if (!isNaN(minPrice)) {
      cleanParams.minPrice = minPrice;
    }
  }
  
  if (params.maxPrice !== undefined) {
    const maxPrice = parseFloat(params.maxPrice);
    if (!isNaN(maxPrice)) {
      cleanParams.maxPrice = maxPrice;
    }
  }
  
  if (Array.isArray(params.tags) && params.tags.length > 0) {
    cleanParams.tags = params.tags;
  }
  
  // 生成缓存键
  const cacheKey = `search_${JSON.stringify(cleanParams)}`;
  const cachedResult = cacheManager.get(cacheKey);
  
  // 如果有缓存且不是第一页，直接返回缓存结果
  if (cachedResult && (cleanParams.page > 1 || params.useCache)) {
    return Promise.resolve({ data: cachedResult });
  }
  
  return new Promise((resolve, reject) => {
    // 设置超时
    const timeout = setTimeout(() => {
      reject(new Error('请求超时'));
    }, 10000);
    
    // 添加性能优化参数
    cleanParams._t = Date.now(); // 防止缓存
    cleanParams.optimize = true; // 告诉服务器优化响应
    
    request.get('/search/products', cleanParams)
      .then(res => {
        clearTimeout(timeout);
        
        // 缓存结果
        if (res && res.data) {
          cacheManager.set(cacheKey, res.data, 60 * 1000); // 缓存1分钟
        }
        
        resolve(res);
      })
      .catch(err => {
        clearTimeout(timeout);
        reject(err);
      });
  });
}

/**
 * 获取搜索历史记录
 * @returns {Promise} - 请求Promise
 */
function getSearchHistory() {
  try {
    const history = wx.getStorageSync('searchHistory') || [];
    return Promise.resolve({ data: Array.isArray(history) ? history : [] });
  } catch (e) {
    console.error('获取搜索历史失败', e);
    return Promise.resolve({ data: [] });
  }
}

/**
 * 清除搜索历史记录
 * @returns {Promise} - 请求Promise
 */
function clearSearchHistory() {
  try {
    wx.removeStorageSync('searchHistory');
    return Promise.resolve({ success: true });
  } catch (e) {
    console.error('清除搜索历史失败', e);
    return Promise.reject({ success: false, message: '清除失败' });
  }
}

module.exports = {
  getHotKeywords,
  getSuggestions,
  searchProducts,
  getSearchHistory,
  clearSearchHistory
}; 