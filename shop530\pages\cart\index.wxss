/* 购物车页面 - 统一设计风格 */

/* ==================== 基础样式 ==================== */
page {
  --primary-color: #4CAF50;
  --secondary-color: #ff6b35;
  --text-color: #333;
  --text-secondary: #666;
  --text-light: #999;
  --border-color: #eee;
  --bg-color: #f8f8f8;
  --card-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  --success-color: #4CAF50;
  --danger-color: #ff4444;

  background-color: var(--bg-color);
  color: var(--text-color);
}

.cart-container {
  min-height: 100vh;
  background-color: var(--bg-color);
  padding-bottom: 120rpx;
}

/* ==================== 骨架屏 ==================== */
.skeleton-container {
  padding: 24rpx;
}

.skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background: #fff;
  border-radius: 12rpx;
}

.skeleton-title {
  width: 200rpx;
  height: 36rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
}

.skeleton-action {
  width: 80rpx;
  height: 36rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
}

.skeleton-items {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.skeleton-item {
  display: flex;
  padding: 24rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: var(--card-shadow);
}

.skeleton-checkbox {
  width: 36rpx;
  height: 36rpx;
  background: #f0f0f0;
  border-radius: 50%;
  margin-right: 16rpx;
}

.skeleton-image {
  width: 140rpx;
  height: 140rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  margin-right: 16rpx;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.skeleton-name {
  height: 32rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 12rpx;
  width: 80%;
}

.skeleton-spec {
  height: 24rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 24rpx;
  width: 60%;
}

.skeleton-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skeleton-price {
  height: 32rpx;
  width: 100rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
}

.skeleton-stepper {
  height: 56rpx;
  width: 140rpx;
  background: #f0f0f0;
  border-radius: 28rpx;
}

/* ==================== 空状态 ==================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
  background: #fff;
  margin: 24rpx;
  border-radius: 12rpx;
  box-shadow: var(--card-shadow);
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.8;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: var(--text-light);
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.primary-button {
  background: var(--primary-color);
  color: #fff;
  border: none;
  border-radius: 48rpx;
  padding: 24rpx 64rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.primary-button::after {
  border: none;
}

.primary-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}

/* ==================== 购物车头部 ==================== */
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: #fff;
  margin: 24rpx 24rpx 0;
  border-radius: 12rpx;
  box-shadow: var(--card-shadow);
}

.cart-title {
  display: flex;
  align-items: center;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
}

.title-count {
  font-size: 24rpx;
  color: var(--primary-color);
  margin-left: 12rpx;
  background: rgba(76, 175, 80, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  min-width: 32rpx;
  text-align: center;
}

.cart-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.refresh-btn,
.fix-image-btn,
.edit-btn {
  padding: 12rpx;
  border-radius: 8rpx;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.refresh-btn:active,
.fix-image-btn:active,
.edit-btn:active {
  background: rgba(0, 0, 0, 0.05);
}

.edit-btn {
  color: var(--primary-color);
  font-size: 26rpx;
  font-weight: 500;
}

/* ==================== 配送信息 ==================== */
.delivery-card {
  margin: 16rpx 24rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: var(--card-shadow);
}

.delivery-icon {
  margin-right: 16rpx;
  color: var(--primary-color);
}

.delivery-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.delivery-badge {
  background: linear-gradient(135deg, var(--primary-color), #66BB6A);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 22rpx;
  font-weight: 500;
  margin-right: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(76, 175, 80, 0.3);
}

.delivery-date {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* ==================== 购物车列表 ==================== */
.cart-list {
  padding: 0 24rpx;
}

.cart-item {
  display: flex;
  align-items: flex-start;
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: var(--card-shadow);
  position: relative;
  transition: all 0.3s ease;
}

.cart-item.updating {
  opacity: 0.7;
}

.item-select {
  margin-right: 16rpx;
  padding-top: 8rpx;
  flex-shrink: 0;
}

.custom-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.custom-checkbox.checked {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.item-image {
  width: 140rpx;
  height: 140rpx;
  margin-right: 16rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background: #f9f9f9;
  flex-shrink: 0;
}

.item-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 140rpx;
}

.item-header {
  flex: 1;
}

.item-name {
  font-size: 28rpx;
  color: var(--text-color);
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-spec {
  font-size: 24rpx;
  color: var(--text-light);
  margin-bottom: 16rpx;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-price {
  font-size: 30rpx;
  color: var(--secondary-color);
  font-weight: 600;
}

.price-unit {
  color: var(--text-secondary);
  font-size: 24rpx;
  font-weight: normal;
  margin-left: 4rpx;
}

.item-controls {
  display: flex;
  align-items: center;
  border: 1rpx solid var(--border-color);
  border-radius: 24rpx;
  overflow: hidden;
  background: #f8f8f8;
  width: 180rpx; /* 固定宽度，不自适应 */
  flex-shrink: 0;
}

.control-btn {
  width: 50rpx; /* 固定宽度 */
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--text-secondary);
  background: #fff;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.control-btn:active {
  background: #f0f0f0;
}

.control-btn.disabled {
  color: #ccc;
  background: #f8f8f8;
}

.quantity-input {
  width: 80rpx; /* 固定宽度 */
  height: 56rpx;
  text-align: center;
  font-size: 26rpx;
  color: var(--text-color);
  background: #fff;
  border: none;
  border-left: 1rpx solid var(--border-color);
  border-right: 1rpx solid var(--border-color);
  outline: none;
  padding: 0;
  margin: 0;
  flex-shrink: 0;
}

/* ==================== 推荐商品 ==================== */
.recommend-section {
  background: #fff;
  margin: 24rpx;
  border-radius: 12rpx;
  padding: 32rpx 24rpx;
  box-shadow: var(--card-shadow);
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background: var(--primary-color);
  border-radius: 3rpx;
}

.recommend-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.recommend-item {
  background: #f8f8f8;
  border-radius: 12rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.recommend-item:active {
  transform: scale(0.98);
}

.recommend-image {
  width: 100%;
  height: 280rpx;
  background: #f0f0f0;
}

.recommend-name {
  font-size: 26rpx;
  color: var(--text-color);
  padding: 16rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 72rpx;
}

.recommend-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16rpx 16rpx;
}

.recommend-price {
  font-size: 28rpx;
  color: var(--secondary-color);
  font-weight: 600;
}

.add-cart-btn {
  width: 56rpx;
  height: 56rpx;
  background: rgba(76, 175, 80, 0.1);
  color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-cart-btn:active {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(0.9);
}

/* ==================== 底部结算栏 ==================== */
.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-top: 1rpx solid var(--border-color);
}

.footer-select {
  display: flex;
  align-items: center;
}

.select-all {
  display: flex;
  align-items: center;
  padding: 12rpx;
}

.select-text {
  margin-left: 12rpx;
  font-size: 26rpx;
  color: var(--text-color);
}

.footer-info {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.total-price {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.price-symbol {
  color: var(--secondary-color);
  font-size: 24rpx;
  margin: 0 4rpx;
}

.price-value {
  color: var(--secondary-color);
  font-size: 32rpx;
  font-weight: 600;
}

.footer-action {
  flex-shrink: 0;
}

.action-button {
  min-width: 160rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.action-button::after {
  border: none;
}

.action-button.checkout {
  background: var(--primary-color);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.action-button.checkout:disabled {
  background: #ccc;
  box-shadow: none;
}

.action-button.delete {
  background: var(--danger-color);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 68, 68, 0.3);
}

.action-button.delete:disabled {
  background: #ccc;
  box-shadow: none;
}

.action-button:active:not(:disabled) {
  transform: translateY(2rpx);
}

/* ==================== 编辑模式 ==================== */
.edit-mode .cart-item {
  background: rgba(255, 255, 255, 0.95);
}

/* ==================== 动画效果 ==================== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cart-item {
  animation: fadeIn 0.4s ease-out;
}

/* ==================== 响应式适配 ==================== */
@media (min-width: 768px) {
  .recommend-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .cart-container {
    max-width: 1200rpx;
    margin: 0 auto;
  }
}

/* ==================== 调试模式 ==================== */
.debug-btn {
  position: fixed;
  bottom: 140rpx;
  right: 24rpx;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.debug-btn:active {
  transform: scale(0.9);
}

.debug-indicator {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  font-size: 20rpx;
  background: var(--danger-color);
  color: #fff;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 32rpx;
  text-align: center;
}