<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_units', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade')->comment('商品ID');
            $table->foreignId('unit_id')->constrained('units')->onDelete('cascade')->comment('单位ID');
            $table->decimal('conversion_factor', 15, 6)->comment('相对于基本单位的转换系数');
            $table->boolean('is_purchase_unit')->default(false)->comment('是否为采购单位');
            $table->boolean('is_sale_unit')->default(false)->comment('是否为销售单位');
            $table->boolean('is_inventory_unit')->default(false)->comment('是否为库存单位');
            $table->timestamps();
            
            // 添加唯一约束
            $table->unique(['product_id', 'unit_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_units');
    }
}; 