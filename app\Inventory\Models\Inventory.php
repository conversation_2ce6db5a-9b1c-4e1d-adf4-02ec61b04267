<?php

namespace App\Inventory\Models;

use App\Warehouse\Models\Warehouse;
use App\Product\Models\Product;
use App\Unit\Models\Unit;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Inventory extends Model
{
    use HasFactory;

    /**
     * 关联的数据表
     *
     * @var string
     */
    protected $table = 'inventory';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'warehouse_id',
        'product_id',
        'stock',
        'unit_id',
        'min_stock_level',
    ];

    /**
     * 自定义属性
     */
    protected $appends = [
        'unit_name',
        'unit_symbol',
        'stock_in_display_unit'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'stock' => 'decimal:2',
        'min_stock_level' => 'decimal:2',
    ];

    /**
     * 获取对应的仓库
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * 获取对应的产品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 获取对应的单位
     */
    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * 获取该库存的所有批次
     */
    public function batches()
    {
        return $this->hasMany(InventoryBatch::class);
    }

    /**
     * 获取有效批次（未过期的）
     */
    public function validBatches()
    {
        return $this->hasMany(InventoryBatch::class)
            ->where(function($query) {
                $query->whereNull('expiry_date')
                      ->orWhere('expiry_date', '>=', now());
            })
            ->where('quantity', '>', 0);
    }

    /**
     * 获取即将过期的批次（在指定天数内过期）
     * 
     * @param int $days 天数
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function nearExpiryBatches($days = 7)
    {
        return $this->hasMany(InventoryBatch::class)
            ->whereNotNull('expiry_date')
            ->where('expiry_date', '>=', now())
            ->where('expiry_date', '<=', now()->addDays($days))
            ->where('quantity', '>', 0);
    }

    /**
     * 获取商品在某个仓库中的库存（转换为基础单位）
     * 
     * @return float 基础单位的库存量
     */
    public function getStockInBaseUnit()
    {
        $product = $this->product;
        
        // 修复：如果unit_id为null，自动设置为商品的基本单位
        if (!$this->unit_id && $product && $product->base_unit_id) {
            $this->unit_id = $product->base_unit_id;
            $this->save();
            
            Log::info('修复库存记录的unit_id', [
                'inventory_id' => $this->id,
                'product_id' => $this->product_id,
                'set_unit_id' => $product->base_unit_id
            ]);
        }
        
        // 如果库存单位与产品基本单位相同，则无需转换
        if ($this->unit_id === $product->base_unit_id) {
            return $this->stock;
        }
        
        // 使用商品模型中的单位转换方法
        $convertedStock = $product->convertQuantity($this->stock, $this->unit_id, $product->base_unit_id);
        if ($convertedStock === null) {
            Log::warning('单位转换失败', [
                'inventory_id' => $this->id,
                'product_id' => $this->product_id,
                'from_unit_id' => $this->unit_id,
                'to_unit_id' => $product->base_unit_id
            ]);
            return $this->stock; // 无法转换时返回原值
        }
        
        return $convertedStock;
    }

    /**
     * 设置商品在某个仓库中的库存
     * 
     * @param float $stock 库存量
     * @param int|null $unitId 单位ID（如果为null则使用当前单位）
     * @return bool 操作是否成功
     */
    public function setStockInUnit($stock, $unitId = null)
    {
        $unitId = $unitId ?? $this->unit_id;
        $product = $this->product;
        
        // 确保产品和单位存在
        if (!$product || !$unitId) {
            Log::error('设置库存失败：产品或单位不存在', [
                'inventory_id' => $this->id,
                'product_id' => $this->product_id,
                'unit_id' => $unitId
            ]);
            return false;
        }
        
        // 始终将库存转换为并存储为基本单位
        $baseUnitId = $product->base_unit_id;
        $stockInBaseUnit = $stock;
        
        // 如果不是基本单位，需要转换
        if ($unitId != $baseUnitId) {
            $stockInBaseUnit = $product->convertQuantity($stock, $unitId, $baseUnitId);
            if ($stockInBaseUnit === null) {
                Log::error('设置库存失败：无法转换单位', [
                    'inventory_id' => $this->id,
                    'product_id' => $this->product_id,
                    'from_unit_id' => $unitId,
                    'to_unit_id' => $baseUnitId
                ]);
                return false;
            }
        }
        
        // 更新库存为基本单位的数量
        $this->stock = $stockInBaseUnit;
        $this->unit_id = $baseUnitId; // 确保单位始终是基本单位
        $success = $this->save();
        
        if ($success) {
            // 更新产品总库存
            $product->updateTotalStock();
        }
        
        return $success;
    }



    /**
     * 获取指定单位下的库存量
     * 
     * @param int|null $unitId 单位ID
     * @return float|null 转换后的库存量
     */
    public function getStockInUnit($unitId = null)
    {
        if (!$unitId) {
            return $this->stock;
        }
        
        $product = $this->product;
        
        // 如果请求的单位就是当前单位，直接返回
        if ($this->unit_id == $unitId) {
            return $this->stock;
        }
        
        // 使用产品模型的单位转换方法
        return $product->convertQuantity($this->stock, $this->unit_id, $unitId);
    }

    /**
     * 获取用于显示的单位下的库存量（优先使用产品默认显示单位）
     * 
     * @return float
     */
    public function getStockInDisplayUnitAttribute()
    {
        $product = $this->product;
        
        // 尝试获取销售默认单位作为显示单位
        $displayUnit = $product->getSaleDefaultUnit();
        if (!$displayUnit) {
            return $this->stock;
        }
        
        return $this->getStockInUnit($displayUnit->id) ?? $this->stock;
    }

    /**
     * 获取单位名称
     *
     * @return string
     */
    public function getUnitNameAttribute()
    {
        return $this->unit ? $this->unit->name : '';
    }

    /**
     * 获取单位符号
     *
     * @return string
     */
    public function getUnitSymbolAttribute()
    {
        return $this->unit ? $this->unit->symbol : '';
    }

    /**
     * 模型启动时的事件监听
     */
    protected static function boot()
    {
        parent::boot();
        
        // 监听库存变更事件，自动清除缓存
        static::updated(function ($inventory) {
            if ($inventory->product) {
                $inventory->product->clearStockCache();
            }
        });
        
        static::created(function ($inventory) {
            if ($inventory->product) {
                $inventory->product->clearStockCache();
            }
        });
        
        static::deleted(function ($inventory) {
            if ($inventory->product) {
                $inventory->product->clearStockCache();
            }
        });
    }
} 