<?php

namespace App\Printing\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Printing\Services\PrintingService;
use App\Printing\Models\PrintRecord;
use App\Order\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PrintController extends Controller
{
    protected PrintingService $printingService;

    public function __construct(PrintingService $printingService)
    {
        $this->printingService = $printingService;
    }

    /**
     * 打印订单
     */
    public function printOrder(Request $request, $orderId): JsonResponse
    {
        try {
            $order = Order::with(['items.product'])->findOrFail($orderId);
            
            $options = [
                'type' => $request->input('type', 'normal'), // normal, receipt, delivery
                'printer_name' => $request->input('printer_name', ''),
                'copies' => $request->input('copies', 1),
                'paper_size' => $request->input('paper_size', 'A4'),
                'paper_width' => $request->input('paper_width', 80), // for receipt
            ];

            // 设置打印驱动（默认使用CLodop，兼容原有功能）
            $driver = $request->input('driver', 'clodop');
            $this->printingService->setDriver($driver);

            $result = $this->printingService->printOrder($order, $options);

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 打印配送单
     */
    public function printDelivery(Request $request, $orderId): JsonResponse
    {
        try {
            $order = Order::with(['items.product'])->findOrFail($orderId);
            
            $options = [
                'type' => 'delivery', // 强制使用配送单模板
                'printer_name' => $request->input('printer_name', ''),
                'copies' => $request->input('copies', 1),
                'paper_size' => $request->input('paper_size', 'A4'),
            ];

            // 设置打印驱动（默认使用CLodop）
            $driver = $request->input('driver', 'clodop');
            $this->printingService->setDriver($driver);

            $result = $this->printingService->printOrder($order, $options);

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量打印订单
     */
    public function batchPrintOrders(Request $request): JsonResponse
    {
        try {
            $orderIds = $request->input('order_ids', []);
            $options = [
                'type' => $request->input('type', 'normal'),
                'printer_name' => $request->input('printer_name', ''),
                'copies' => $request->input('copies', 1),
                'paper_size' => $request->input('paper_size', 'A4'),
                'paper_width' => $request->input('paper_width', 80),
            ];

            // 设置打印驱动（默认使用CLodop，兼容原有功能）
            $driver = $request->input('driver', 'clodop');
            $this->printingService->setDriver($driver);

            $results = [];
            foreach ($orderIds as $orderId) {
                $order = Order::with(['items.product'])->find($orderId);
                if ($order) {
                    $result = $this->printingService->printOrder($order, $options);
                    $results[] = [
                        'order_id' => $orderId,
                        'order_no' => $order->order_no,
                        'result' => $result
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'results' => $results
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取打印机列表
     */
    public function printers(Request $request): JsonResponse
    {
        try {
            $driver = $request->input('driver', 'clodop');
            $this->printingService->setDriver($driver);
            
            $printers = $this->printingService->getPrinters();

            return response()->json([
                'success' => true,
                'data' => $printers
            ]);

        } catch (\Exception $e) {
            Log::error('Get printers API error', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取打印机列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 设置默认打印机
     */
    public function setDefaultPrinter(Request $request): JsonResponse
    {
        try {
            $printerName = $request->input('printer_name');
            $result = $this->printingService->setDefaultPrinter($printerName);
            
            return response()->json([
                'success' => $result,
                'message' => $result ? '设置成功' : '设置失败'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取打印机状态
     */
    public function printerStatus(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'printer_sn' => 'required|string',
                'driver' => 'nullable|string|in:browser,lodop'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $printerSn = $request->input('printer_sn');
            $driver = $request->input('driver', 'clodop');
            
            $this->printingService->setDriver($driver);
            $status = $this->printingService->getPrinterStatus($printerSn);

            return response()->json([
                'success' => true,
                'data' => $status
            ]);

        } catch (\Exception $e) {
            Log::error('Get printer status API error', [
                'error' => $e->getMessage(),
                'printer_sn' => $request->input('printer_sn')
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取打印机状态失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 预览打印内容
     */
    public function preview(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'content' => 'required|string',
                'driver' => 'nullable|string|in:browser,lodop'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $content = $request->input('content');
            $driver = $request->input('driver', 'clodop');
            
            $this->printingService->setDriver($driver);
            $preview = $this->printingService->preview($content);

            return response()->json([
                'success' => true,
                'data' => [
                    'preview' => $preview,
                    'driver' => $driver
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Print preview API error', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '预览生成失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 生成打印脚本
     */
    public function script(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'content' => 'required|string',
                'driver' => 'nullable|string|in:browser,lodop',
                'type' => 'nullable|string|in:normal,receipt'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $content = $request->input('content');
            $driver = $request->input('driver', 'clodop');
            $type = $request->input('type', 'normal');
            $options = $request->input('options', []);
            
            $this->printingService->setDriver($driver);
            
            if ($type === 'receipt') {
                $script = $this->printingService->generateReceiptScript($content, $options);
            } else {
                $script = $this->printingService->generatePrintScript($content, $options);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'script' => $script,
                    'driver' => $driver,
                    'type' => $type
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Generate print script API error', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '生成打印脚本失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取C-Lodop配置信息
     */
    public function getConfig(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'service_url' => env('CLODOP_SERVICE_URL', 'http://localhost:8000/CLodopfuncs.js'),
                'license' => env('CLODOP_LICENSE', ''),
                'default_printer' => env('CLODOP_DEFAULT_PRINTER', ''),
                'debug' => env('PRINTING_DEBUG', false)
            ]
        ]);
    }

    /**
     * 打印完成回调
     */
    public function printCompleted(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'print_record_id' => 'required|integer|exists:print_records,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $printRecordId = $request->input('print_record_id');
            $result = $this->printingService->markPrintCompleted($printRecordId);

            return response()->json([
                'success' => $result,
                'message' => $result ? '打印状态更新成功' : '打印状态更新失败'
            ]);

        } catch (\Exception $e) {
            Log::error('Print completed callback error', [
                'error' => $e->getMessage(),
                'print_record_id' => $request->input('print_record_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => '打印完成回调失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 打印失败回调
     */
    public function printFailed(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'print_record_id' => 'required|integer|exists:print_records,id',
                'error_message' => 'nullable|string|max:1000'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $printRecordId = $request->input('print_record_id');
            $errorMessage = $request->input('error_message', '');
            
            $result = $this->printingService->markPrintFailed($printRecordId, $errorMessage);

            return response()->json([
                'success' => $result,
                'message' => $result ? '打印失败状态记录成功' : '打印失败状态记录失败'
            ]);

        } catch (\Exception $e) {
            Log::error('Print failed callback error', [
                'error' => $e->getMessage(),
                'print_record_id' => $request->input('print_record_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => '打印失败回调失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 查询订单打印状态
     */
    public function orderPrintStatus(Request $request, $orderId): JsonResponse
    {
        try {
            $order = Order::findOrFail($orderId);
            $printType = $request->input('print_type'); // 可选，指定打印类型
            
            $stats = $this->printingService->getPrintStats($order, $printType);
            $history = $this->printingService->getPrintHistory($order, $printType);

            return response()->json([
                'success' => true,
                'data' => [
                    'order_id' => $orderId,
                    'order_no' => $order->order_no,
                    'print_stats' => $stats,
                    'print_history' => $history->map(function ($record) {
                        return [
                            'id' => $record->id,
                            'print_type' => $record->print_type,
                            'driver' => $record->driver,
                            'printer_name' => $record->printer_name,
                            'status' => $record->status,
                            'copies' => $record->copies,
                            'printed_at' => $record->printed_at,
                            'printed_by' => $record->printedBy ? $record->printedBy->name : null,
                            'error_message' => $record->error_message,
                            'created_at' => $record->created_at
                        ];
                    })
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 重新打印订单
     */
    public function reprintOrder(Request $request, $orderId): JsonResponse
    {
        try {
            $order = Order::with(['items.product'])->findOrFail($orderId);
            
            $options = [
                'type' => $request->input('type', 'normal'),
                'printer_name' => $request->input('printer_name', ''),
                'copies' => $request->input('copies', 1),
                'paper_size' => $request->input('paper_size', 'A4'),
                'paper_width' => $request->input('paper_width', 80),
                'allow_reprint' => $request->input('allow_reprint', true)
            ];

            // 设置打印驱动
            $driver = $request->input('driver', 'clodop');
            $this->printingService->setDriver($driver);

            $result = $this->printingService->reprintOrder($order, $options);

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取待打印列表
     */
    public function pendingPrints(Request $request): JsonResponse
    {
        try {
            $pendingPrints = $this->printingService->getPendingPrints();

            return response()->json([
                'success' => true,
                'data' => $pendingPrints->map(function ($record) {
                    return [
                        'id' => $record->id,
                        'printable_type' => $record->printable_type,
                        'printable_id' => $record->printable_id,
                        'print_type' => $record->print_type,
                        'driver' => $record->driver,
                        'printer_name' => $record->printer_name,
                        'copies' => $record->copies,
                        'created_at' => $record->created_at,
                        'printed_by' => $record->printedBy ? $record->printedBy->name : null,
                        'printable_info' => $this->getPrintableInfo($record->printable)
                    ];
                })
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取打印记录详情
     */
    public function printRecord(Request $request, $recordId): JsonResponse
    {
        try {
            $record = PrintRecord::with(['printable', 'printedBy'])->findOrFail($recordId);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $record->id,
                    'printable_type' => $record->printable_type,
                    'printable_id' => $record->printable_id,
                    'print_type' => $record->print_type,
                    'driver' => $record->driver,
                    'printer_name' => $record->printer_name,
                    'status' => $record->status,
                    'copies' => $record->copies,
                    'print_options' => $record->print_options,
                    'print_content' => $record->print_content,
                    'printed_at' => $record->printed_at,
                    'error_message' => $record->error_message,
                    'created_at' => $record->created_at,
                    'updated_at' => $record->updated_at,
                    'printed_by' => $record->printedBy ? [
                        'id' => $record->printedBy->id,
                        'name' => $record->printedBy->name
                    ] : null,
                    'printable_info' => $this->getPrintableInfo($record->printable)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量打印状态回调
     */
    public function batchPrintCallback(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'callbacks' => 'required|array',
                'callbacks.*.print_record_id' => 'required|integer|exists:print_records,id',
                'callbacks.*.status' => 'required|string|in:completed,failed',
                'callbacks.*.error_message' => 'nullable|string|max:1000'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $callbacks = $request->input('callbacks');
            $results = [];

            foreach ($callbacks as $callback) {
                $printRecordId = $callback['print_record_id'];
                $status = $callback['status'];
                $errorMessage = $callback['error_message'] ?? '';

                if ($status === 'completed') {
                    $result = $this->printingService->markPrintCompleted($printRecordId);
                } else {
                    $result = $this->printingService->markPrintFailed($printRecordId, $errorMessage);
                }

                $results[] = [
                    'print_record_id' => $printRecordId,
                    'success' => $result
                ];
            }

            return response()->json([
                'success' => true,
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('Batch print callback error', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '批量打印回调失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取可打印对象信息
     */
    private function getPrintableInfo($printable): ?array
    {
        if (!$printable) {
            return null;
        }

        // 根据不同的模型返回不同的信息
        if ($printable instanceof Order) {
            return [
                'type' => 'order',
                'order_no' => $printable->order_no,
                'contact_name' => $printable->contact_name,
                'total' => $printable->total,
                'status' => $printable->status
            ];
        }

        // 可以继续添加其他模型的处理
        return [
            'type' => 'unknown',
            'id' => $printable->id
        ];
    }

    /**
     * 批量获取订单打印状态
     */
    public function batchGetOrderPrintStatus(Request $request)
    {
        $request->validate([
            'order_ids' => 'required|array',
            'order_ids.*' => 'integer',
            'print_type' => 'nullable|string|in:receipt,delivery'
        ]);

        try {
            $orderIds = $request->input('order_ids');
            $printType = $request->input('print_type');
            $results = [];

            // 批量获取订单
            $orders = Order::whereIn('id', $orderIds)->get()->keyBy('id');

            foreach ($orderIds as $orderId) {
                if (!isset($orders[$orderId])) {
                    $results[$orderId] = [
                        'success' => false,
                        'error' => 'Order not found'
                    ];
                    continue;
                }

                $order = $orders[$orderId];
                
                try {
                    // 获取打印记录统计
                    $printStats = $this->getPrintStatistics($order, $printType);
                    
                    $results[$orderId] = [
                        'success' => true,
                        'data' => [
                            'order_id' => $order->id,
                            'print_stats' => $printStats
                        ]
                    ];
                } catch (\Exception $e) {
                    $results[$orderId] = [
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'data' => $results
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量获取打印状态失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取打印统计信息
     */
    private function getPrintStatistics($order, $printType = null)
    {
        $query = $order->printRecords()->with('printedBy');
        
        if ($printType) {
            $query->where('print_type', $printType);
        }
        
        $records = $query->orderBy('created_at', 'desc')->get();
        
        if ($printType) {
            // 单一类型统计
            $completedRecords = $records->where('status', 'completed');
            $lastCompletedRecord = $completedRecords->sortByDesc('printed_at')->first();
            
            return [
                'is_printed' => $completedRecords->count() > 0,
                'total_prints' => $records->count(), // 统计所有打印记录
                'completed_prints' => $completedRecords->count(), // 成功打印次数
                'failed_prints' => $records->where('status', 'failed')->count(), // 失败打印次数
                'last_printed_at' => $lastCompletedRecord?->printed_at,
                'last_printed_by' => $lastCompletedRecord && $lastCompletedRecord->printedBy ? $lastCompletedRecord->printedBy->name : null,
                'print_type' => $printType
            ];
        } else {
            // 所有类型统计
            $stats = [];
            $types = ['receipt', 'delivery'];
            
            foreach ($types as $type) {
                $typeRecords = $records->where('print_type', $type);
                $completedRecords = $typeRecords->where('status', 'completed');
                $lastCompletedRecord = $completedRecords->sortByDesc('printed_at')->first();
                
                $stats[$type] = [
                    'is_printed' => $completedRecords->count() > 0,
                    'total_prints' => $typeRecords->count(), // 统计所有打印记录
                    'completed_prints' => $completedRecords->count(), // 成功打印次数
                    'failed_prints' => $typeRecords->where('status', 'failed')->count(), // 失败打印次数
                    'last_printed_at' => $lastCompletedRecord?->printed_at,
                    'last_printed_by' => $lastCompletedRecord && $lastCompletedRecord->printedBy ? $lastCompletedRecord->printedBy->name : null
                ];
            }
            
            return $stats;
        }
    }

    /**
     * 测试当前员工认证状态
     */
    public function testAuth(): JsonResponse
    {
        $user = auth()->user();
        
        return response()->json([
            'success' => true,
            'data' => [
                'authenticated' => auth()->check(),
                'user_id' => auth()->id(),
                'user_name' => $user ? $user->name : null,
                'user_type' => $user ? get_class($user) : null,
                'guard' => 'default'
            ]
        ]);
    }
} 