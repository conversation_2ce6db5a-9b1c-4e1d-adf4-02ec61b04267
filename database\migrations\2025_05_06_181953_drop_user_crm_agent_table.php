<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 删除中间表，因为我们改为直接在users表中添加crm_agent_id字段
        Schema::dropIfExists('user_crm_agent');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 重新创建中间表
        Schema::create('user_crm_agent', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->comment('客户ID');
            $table->foreignId('agent_id')->constrained('users')->comment('CRM专员ID');
            $table->timestamp('assigned_at')->nullable()->comment('分配时间');
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('状态：活跃、非活跃');
            $table->text('notes')->nullable()->comment('备注');
            $table->timestamps();
            
            // 添加唯一索引确保一个客户不会被分配给同一个CRM专员多次
            $table->unique(['user_id', 'agent_id']);
        });
    }
};
