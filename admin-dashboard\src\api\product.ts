import { get, post } from '@/utils/request'

// 获取库存成本价详细信息（包含多种计算方法）
export const getCostPriceInfo = (inventoryId: number) => {
  return get(`/inventory/${inventoryId}/cost-price-info`)
}

// 刷新库存成本价缓存
export const refreshCostPrice = (inventoryId: number) => {
  return post(`/inventory/${inventoryId}/refresh-cost-price`)
}

// 获取成本价计算方法列表
export const getCostMethods = () => {
  return get('/inventory/cost-methods')
}

// 兼容旧的成本价接口
export const getCostPrice = (inventoryId: number) => {
  return get(`/inventory/${inventoryId}/cost-price`)
} 