<?php

use App\Delivery\Http\Controllers\DeliveryController;
use App\Delivery\Http\Controllers\DelivererController;
use App\Delivery\Http\Controllers\DeliveryRouteController;
use App\Delivery\Http\Controllers\DeliveryStatisticsController;
use Illuminate\Support\Facades\Route;

// 配送管理Web路由
Route::group(['prefix' => 'admin/delivery', 'middleware' => ['web', 'auth']], function () {
    // 配送管理
    Route::get('/', [DeliveryController::class, 'index'])->name('delivery.index');
    Route::get('/{id}', [DeliveryController::class, 'show'])->name('delivery.show');
    
    // 配送员管理
    Route::get('/deliverers', [DelivererController::class, 'index'])->name('deliverers.index');
    Route::get('/deliverers/{id}', [DelivererController::class, 'show'])->name('deliverers.show');
    
    // 配送路线管理
    Route::get('/routes', [DeliveryRouteController::class, 'index'])->name('delivery.routes.index');
    Route::get('/routes/{id}', [DeliveryRouteController::class, 'show'])->name('delivery.routes.show');
    
    // 配送统计
    Route::get('/statistics', [DeliveryStatisticsController::class, 'overview'])->name('delivery.statistics');
}); 