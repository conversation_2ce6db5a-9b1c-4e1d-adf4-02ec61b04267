<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // 添加user_address_id字段并建立外键关联
            $table->foreignId('user_address_id')->nullable()->after('user_id')
                ->comment('用户地址ID')->constrained('user_addresses')->nullOnDelete();
            
            // 修改现有字段为可空
            $table->string('shipping_address')->nullable()->change();
            $table->string('contact_name')->nullable()->change();
            $table->string('contact_phone')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // 删除外键约束和字段
            $table->dropConstrainedForeignId('user_address_id');
            
            // 恢复字段为非空
            $table->string('shipping_address')->nullable(false)->change();
            $table->string('contact_name')->nullable(false)->change();
            $table->string('contact_phone')->nullable(false)->change();
        });
    }
};
