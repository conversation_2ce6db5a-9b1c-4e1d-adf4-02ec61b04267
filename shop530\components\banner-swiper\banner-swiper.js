Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 轮播图数据
    bannerList: {
      type: Array,
      value: []
    },
    // 自动播放
    autoplay: {
      type: Boolean,
      value: true
    },
    // 播放间隔
    interval: {
      type: Number,
      value: 6000
    },
    // 切换动画时长
    duration: {
      type: Number,
      value: 800
    },
    // 是否显示指示器
    indicatorDots: {
      type: Boolean,
      value: true
    },
    // 是否启用三层切割效果
    enableLayered: {
      type: Boolean,
      value: true
    },
    // 轮播图高度
    height: {
      type: String,
      value: '300rpx'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentIndex: 0,
    bannerChangeTimer: null
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 轮播图切换事件（优化版本）- 修复事件处理
     */
    onBannerChange(event) {
      try {
        const detail = event.detail || {};
        const current = detail.current;
        
        // 验证事件数据
        if (typeof current !== 'number' || current < 0 || current >= this.data.bannerList.length) {
          return;
        }
        
        // 防抖处理，避免频繁切换
        if (this.data.bannerChangeTimer) {
          clearTimeout(this.data.bannerChangeTimer);
        }
        
        const timer = setTimeout(() => {
          try {
            // 更新当前轮播图索引
            this.setData({
              currentIndex: current
            });
            
            // 触发父组件事件
            this.triggerEvent('change', {
              current: current,
              item: this.data.bannerList[current] || null
            });
            
            // 清理定时器
            this.setData({
              bannerChangeTimer: null
            });
          } catch (error) {
            // 静默处理错误
          }
        }, 150); // 防抖处理
        
        this.setData({
          bannerChangeTimer: timer
        });
      } catch (error) {
        // 静默处理错误
      }
    },

    /**
     * 轮播图点击事件 - 修复事件处理
     */
    onBannerTap(event) {
      try {
        const currentTarget = event.currentTarget || {};
        const dataset = currentTarget.dataset || {};
        const item = dataset.item;
        
        if (!item) {
          return;
        }
        
        this.triggerEvent('tap', {
          item: item,
          index: this.data.currentIndex
        });
      } catch (error) {
        // 静默处理错误
      }
    },

    /**
     * 控制轮播图播放状态
     */
    controlAutoplay(autoplay) {
      this.setData({
        autoplay: autoplay
      });
    },

    /**
     * 跳转到指定轮播图
     */
    goToSlide(index) {
      if (index >= 0 && index < this.data.bannerList.length) {
        this.setData({
          currentIndex: index
        });
      }
    },

    /**
     * 预加载轮播图图片
     */
    preloadImages() {
      if (!this.data.bannerList || this.data.bannerList.length === 0) return;
      
      const imageUrls = this.data.bannerList
        .map(item => item.image)
        .filter(Boolean);
      
      if (imageUrls.length > 0) {
        // 预加载图片
        imageUrls.forEach(url => {
          wx.getImageInfo({
            src: url,
            success: () => {
              // 静默处理成功
            },
            fail: () => {
              // 静默处理失败
            }
          });
        });
      }
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件已挂载
    },

    detached() {
      // 清理定时器
      if (this.data.bannerChangeTimer) {
        clearTimeout(this.data.bannerChangeTimer);
      }
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面显示时恢复自动播放
      if (this.properties.autoplay) {
        this.controlAutoplay(true);
      }
    },

    hide() {
      // 页面隐藏时暂停自动播放
      this.controlAutoplay(false);
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'bannerList': function(newBannerList) {
      if (newBannerList && newBannerList.length > 0) {
        // 轮播图数据更新时，预加载图片
        this.preloadImages();
        
        // 重置当前索引
        this.setData({
          currentIndex: 0
        });
      }
    }
  }
}); 