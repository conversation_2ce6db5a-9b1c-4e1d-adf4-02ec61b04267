<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. 用户行为记录表
        Schema::create('customer_behavior_analytics', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->comment('用户ID，可为空（匿名用户）');
            $table->string('session_id', 100)->comment('会话ID');
            $table->string('event_type', 50)->comment('事件类型：page_view,product_view,cart_operation,search,order_behavior');
            $table->json('event_data')->comment('事件详细数据');
            $table->json('device_info')->nullable()->comment('设备信息');
            $table->string('ip_address', 45)->nullable()->comment('IP地址');
            $table->string('user_agent')->nullable()->comment('用户代理');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            
            // 索引优化
            $table->index(['user_id', 'created_at'], 'idx_user_time');
            $table->index('session_id', 'idx_session');
            $table->index('event_type', 'idx_event_type');
            $table->index('created_at', 'idx_created_at');
            $table->index(['event_type', 'created_at'], 'idx_event_time');
            
            // 外键约束
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });

        // 2. 用户会话表
        Schema::create('user_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('session_id', 100)->unique()->comment('会话ID');
            $table->unsignedBigInteger('user_id')->nullable()->comment('用户ID');
            $table->timestamp('start_time')->comment('会话开始时间');
            $table->timestamp('end_time')->nullable()->comment('会话结束时间');
            $table->integer('page_count')->default(0)->comment('访问页面数');
            $table->integer('event_count')->default(0)->comment('事件总数');
            $table->integer('duration')->default(0)->comment('会话时长（秒）');
            $table->json('device_info')->nullable()->comment('设备信息');
            $table->string('ip_address', 45)->nullable()->comment('IP地址');
            $table->string('referrer')->nullable()->comment('来源页面');
            $table->timestamps();
            
            // 索引
            $table->index('user_id', 'idx_user_id');
            $table->index('start_time', 'idx_start_time');
            $table->index(['user_id', 'start_time'], 'idx_user_start');
            
            // 外键约束
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });

        // 3. 行为统计汇总表（按日汇总）
        Schema::create('behavior_statistics', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->date('stat_date')->comment('统计日期');
            $table->integer('page_views')->default(0)->comment('页面访问次数');
            $table->integer('product_views')->default(0)->comment('商品浏览次数');
            $table->integer('cart_operations')->default(0)->comment('购物车操作次数');
            $table->integer('search_count')->default(0)->comment('搜索次数');
            $table->integer('order_count')->default(0)->comment('订单数量');
            $table->integer('session_count')->default(0)->comment('会话数量');
            $table->integer('total_duration')->default(0)->comment('总停留时间(秒)');
            $table->decimal('total_amount', 10, 2)->default(0)->comment('当日消费金额');
            $table->timestamps();
            
            // 唯一约束和索引
            $table->unique(['user_id', 'stat_date'], 'uk_user_date');
            $table->index('stat_date', 'idx_stat_date');
            $table->index(['user_id', 'stat_date'], 'idx_user_date');
            
            // 外键约束
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });

        // 4. 商品浏览记录表（用于商品偏好分析）
        Schema::create('product_view_analytics', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->comment('用户ID');
            $table->unsignedBigInteger('product_id')->comment('商品ID');
            $table->string('session_id', 100)->comment('会话ID');
            $table->string('source', 50)->nullable()->comment('来源：list,search,recommendation,banner');
            $table->integer('view_duration')->default(0)->comment('浏览时长（秒）');
            $table->integer('scroll_depth')->default(0)->comment('滚动深度（百分比）');
            $table->integer('image_views')->default(0)->comment('图片查看次数');
            $table->boolean('added_to_cart')->default(false)->comment('是否加入购物车');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            
            // 索引
            $table->index(['user_id', 'created_at'], 'idx_user_time');
            $table->index(['product_id', 'created_at'], 'idx_product_time');
            $table->index('session_id', 'idx_session');
            $table->index(['user_id', 'product_id'], 'idx_user_product');
            
            // 外键约束
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });

        // 5. 搜索行为记录表
        Schema::create('search_analytics', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->comment('用户ID');
            $table->string('session_id', 100)->comment('会话ID');
            $table->string('keyword')->comment('搜索关键词');
            $table->json('filters')->nullable()->comment('筛选条件');
            $table->integer('result_count')->default(0)->comment('搜索结果数量');
            $table->integer('click_position')->nullable()->comment('点击位置');
            $table->unsignedBigInteger('clicked_product_id')->nullable()->comment('点击的商品ID');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            
            // 索引
            $table->index(['user_id', 'created_at'], 'idx_user_time');
            $table->index('keyword', 'idx_keyword');
            $table->index('session_id', 'idx_session');
            $table->index('created_at', 'idx_created_at');
            
            // 外键约束
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('clicked_product_id')->references('id')->on('products')->onDelete('set null');
        });

        // 6. 购物车行为记录表
        Schema::create('cart_analytics', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->comment('用户ID');
            $table->string('session_id', 100)->comment('会话ID');
            $table->string('operation', 20)->comment('操作类型：add,remove,update,clear');
            $table->unsignedBigInteger('product_id')->nullable()->comment('商品ID');
            $table->integer('quantity')->default(0)->comment('数量');
            $table->decimal('price', 10, 2)->default(0)->comment('商品价格');
            $table->decimal('total_cart_value', 10, 2)->default(0)->comment('购物车总价值');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            
            // 索引
            $table->index(['user_id', 'created_at'], 'idx_user_time');
            $table->index('session_id', 'idx_session');
            $table->index('operation', 'idx_operation');
            $table->index(['product_id', 'created_at'], 'idx_product_time');
            
            // 外键约束
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cart_analytics');
        Schema::dropIfExists('search_analytics');
        Schema::dropIfExists('product_view_analytics');
        Schema::dropIfExists('behavior_statistics');
        Schema::dropIfExists('user_sessions');
        Schema::dropIfExists('customer_behavior_analytics');
    }
}; 