<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TabBar图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 2px solid #eee;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon-preview {
            width: 81px;
            height: 81px;
            margin: 0 auto 10px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .icon-preview:hover {
            transform: scale(1.1);
        }
        .icon-normal {
            background: #999;
            color: white;
        }
        .icon-active {
            background: #007AFF;
            color: white;
        }
        .icon-name {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .download-btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .step {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }
        .step::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #1976d2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 CRM TabBar图标生成器</h1>
        
        <div class="icon-grid">
            <div class="icon-item">
                <div class="icon-name">首页</div>
                <div class="icon-preview icon-normal" onclick="downloadIcon('home', '🏠', '#999')">🏠</div>
                <div class="icon-preview icon-active" onclick="downloadIcon('home-active', '🏠', '#007AFF')">🏠</div>
                <button class="download-btn" onclick="downloadBothIcons('home', '🏠')">下载图标</button>
            </div>
            
            <div class="icon-item">
                <div class="icon-name">代客下单</div>
                <div class="icon-preview icon-normal" onclick="downloadIcon('order', '📝', '#999')">📝</div>
                <div class="icon-preview icon-active" onclick="downloadIcon('order-active', '📝', '#007AFF')">📝</div>
                <button class="download-btn" onclick="downloadBothIcons('order', '📝')">下载图标</button>
            </div>
            
            <div class="icon-item">
                <div class="icon-name">客户</div>
                <div class="icon-preview icon-normal" onclick="downloadIcon('client', '👥', '#999')">👥</div>
                <div class="icon-preview icon-active" onclick="downloadIcon('client-active', '👥', '#007AFF')">👥</div>
                <button class="download-btn" onclick="downloadBothIcons('client', '👥')">下载图标</button>
            </div>
            
            <div class="icon-item">
                <div class="icon-name">订单</div>
                <div class="icon-preview icon-normal" onclick="downloadIcon('orders', '📋', '#999')">📋</div>
                <div class="icon-preview icon-active" onclick="downloadIcon('orders-active', '📋', '#007AFF')">📋</div>
                <button class="download-btn" onclick="downloadBothIcons('orders', '📋')">下载图标</button>
            </div>
            
            <div class="icon-item">
                <div class="icon-name">我的</div>
                <div class="icon-preview icon-normal" onclick="downloadIcon('profile', '👤', '#999')">👤</div>
                <div class="icon-preview icon-active" onclick="downloadIcon('profile-active', '👤', '#007AFF')">👤</div>
                <button class="download-btn" onclick="downloadBothIcons('profile', '👤')">下载图标</button>
            </div>
        </div>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <div class="step">点击上方的图标预览或"下载图标"按钮</div>
            <div class="step">图标会自动下载到您的电脑</div>
            <div class="step">将下载的PNG文件放到项目的 <code>static/tabbar/</code> 目录下</div>
            <div class="step">重新运行项目，底部导航栏就会显示图标了</div>
            <div class="step">如果需要自定义图标，可以使用设计软件制作81x81px的PNG图标</div>
        </div>
    </div>

    <script>
        function createIcon(emoji, bgColor, size = 81) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // 绘制背景
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, size, size);
            
            // 绘制emoji
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = 'white';
            ctx.fillText(emoji, size / 2, size / 2);
            
            return canvas;
        }
        
        function downloadIcon(filename, emoji, bgColor) {
            const canvas = createIcon(emoji, bgColor);
            const link = document.createElement('a');
            link.download = filename + '.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function downloadBothIcons(baseName, emoji) {
            // 下载普通状态图标
            downloadIcon(baseName, emoji, '#999999');
            
            // 延迟下载选中状态图标
            setTimeout(() => {
                downloadIcon(baseName + '-active', emoji, '#007AFF');
            }, 100);
        }
        
        // 页面加载完成后的提示
        window.onload = function() {
            console.log('TabBar图标生成器已加载完成！');
            console.log('点击图标或按钮即可下载对应的PNG文件');
        }
    </script>
</body>
</html> 