<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Employee\Models\Employee;
use App\Delivery\Models\Deliverer;
use App\Order\Models\Order;
use App\Delivery\Models\Delivery;
use Carbon\Carbon;

class DeliveryTestDataSeeder extends Seeder
{
    private $addresses = [
        '北京市朝阳区建国门外大街1号',
        '北京市海淀区中关村大街27号',
        '北京市西城区西单北大街133号',
        '北京市东城区王府井大街255号',
        '北京市丰台区南三环西路16号',
        '北京市朝阳区三里屯路19号',
        '北京市海淀区学院路30号',
        '北京市西城区金融街35号',
        '北京市东城区东直门内大街5号',
        '北京市丰台区草桥东路8号',
        '北京市朝阳区国贸建外SOHO',
        '北京市海淀区五道口华清嘉园',
        '北京市西城区复兴门内大街45号',
        '北京市东城区雍和宫大街52号',
        '北京市丰台区宋家庄地铁站附近'
    ];

    private $customerNames = [
        '陈先生', '刘女士', '杨先生', '黄女士', '周先生',
        '吴女士', '徐先生', '孙女士', '马先生', '朱女士',
        '胡先生', '郭女士', '林先生', '何女士', '高先生'
    ];

    private $phones = [
        '13912345678', '13823456789', '13734567890', '13645678901', '13556789012',
        '13467890123', '13378901234', '13289012345', '13190123456', '13001234567',
        '15912345678', '15823456789', '15734567890', '15645678901', '15556789012'
    ];

    /**
     * 运行数据库种子
     */
    public function run(): void
    {
        // 清空相关表
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Delivery::truncate();
        Order::truncate();
        Deliverer::truncate();
        Employee::truncate();
        User::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // 创建测试用户和配送员
        $this->createDeliverers();
        
        // 创建测试订单
        $this->createOrders();
        
        // 创建配送记录
        $this->createDeliveries();
        
        $this->command->info('配送系统测试数据创建完成！');
    }

    /**
     * 创建配送员数据
     */
    private function createDeliverers()
    {
        $deliverers = [
            [
                'name' => '张三',
                'phone' => '13800138001',
                'password' => '123456',
                'delivery_area' => '朝阳区',
                'transportation' => '电动车',
                'status' => 'available'
            ],
            [
                'name' => '李四',
                'phone' => '13800138002',
                'password' => '123456',
                'delivery_area' => '海淀区',
                'transportation' => '摩托车',
                'status' => 'busy'
            ],
            [
                'name' => '王五',
                'phone' => '13800138003',
                'password' => '123456',
                'delivery_area' => '西城区',
                'transportation' => '电动车',
                'status' => 'available'
            ],
            [
                'name' => '赵六',
                'phone' => '13800138004',
                'password' => '123456',
                'delivery_area' => '东城区',
                'transportation' => '自行车',
                'status' => 'offline'
            ],
            [
                'name' => '钱七',
                'phone' => '13800138005',
                'password' => '123456',
                'delivery_area' => '丰台区',
                'transportation' => '电动车',
                'status' => 'available'
            ]
        ];

        foreach ($deliverers as $index => $delivererData) {
            // 创建用户
            $user = User::create([
                'name' => $delivererData['name'],
                'phone' => $delivererData['phone'],
                'password' => Hash::make($delivererData['password']),
            ]);

            // 创建员工
            $employee = Employee::create([
                'user_id' => $user->id,
                'name' => $delivererData['name'],
                'phone' => $delivererData['phone'],
                'role' => 'delivery',
                'department' => '配送部',
                'position' => '配送员',
                'hire_date' => Carbon::now()->subDays(rand(30, 365)),
                'status' => 'active',
                'salary' => rand(4000, 8000),
            ]);

            // 创建配送员
            Deliverer::create([
                'user_id' => $user->id,
                'employee_id' => $employee->id,
                'type' => 'employee',
                'delivery_area' => $delivererData['delivery_area'],
                'max_orders' => rand(8, 15),
                'rating' => round(rand(40, 50) / 10, 1),
                'status' => $delivererData['status'],
                'working_hours' => '09:00-18:00',
                'transportation' => $delivererData['transportation'],
                'last_location_lat' => 39.9087 + (rand(-100, 100) / 1000),
                'last_location_lng' => 116.3974 + (rand(-100, 100) / 1000),
                'last_active_at' => Carbon::now()->subMinutes(rand(1, 60)),
            ]);
        }

        $this->command->info('已创建 ' . count($deliverers) . ' 个配送员');
    }

    /**
     * 创建订单数据
     */
    private function createOrders()
    {
        $statuses = ['pending', 'confirmed', 'preparing', 'ready', 'delivering', 'delivered', 'cancelled'];
        $orderStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'delivering', 'delivered'];

        // 创建最近30天的订单
        for ($i = 0; $i < 150; $i++) {
            $createdAt = Carbon::now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59));
            $status = $orderStatuses[array_rand($orderStatuses)];
            
            // 根据状态设置相应的时间
            $confirmedAt = null;
            $deliveredAt = null;
            
            if (in_array($status, ['confirmed', 'preparing', 'ready', 'delivering', 'delivered'])) {
                $confirmedAt = $createdAt->copy()->addMinutes(rand(5, 30));
            }
            
            if ($status === 'delivered') {
                $deliveredAt = $confirmedAt ? $confirmedAt->copy()->addMinutes(rand(30, 120)) : $createdAt->copy()->addMinutes(rand(35, 150));
            }

            Order::create([
                'order_number' => 'ORD' . date('Ymd') . str_pad($i + 1, 4, '0', STR_PAD_LEFT),
                'customer_name' => $this->customerNames[array_rand($this->customerNames)],
                'customer_phone' => $this->phones[array_rand($this->phones)],
                'receiver_name' => $this->customerNames[array_rand($this->customerNames)],
                'receiver_phone' => $this->phones[array_rand($this->phones)],
                'delivery_address' => $this->addresses[array_rand($this->addresses)],
                'total_amount' => rand(50, 500) + (rand(0, 99) / 100),
                'status' => $status,
                'remark' => rand(0, 3) == 0 ? '请尽快配送，谢谢！' : null,
                'created_at' => $createdAt,
                'confirmed_at' => $confirmedAt,
                'delivered_at' => $deliveredAt,
                'updated_at' => $createdAt,
            ]);
        }

        $this->command->info('已创建 150 个测试订单');
    }

    /**
     * 创建配送记录
     */
    private function createDeliveries()
    {
        $deliverers = Deliverer::all();
        $orders = Order::whereIn('status', ['ready', 'delivering', 'delivered'])->get();

        foreach ($orders as $order) {
            // 随机分配配送员
            $deliverer = $deliverers->random();
            
            // 根据订单状态确定配送状态
            $deliveryStatus = 'pending';
            if ($order->status === 'delivering') {
                $deliveryStatus = 'in_progress';
            } elseif ($order->status === 'delivered') {
                $deliveryStatus = 'completed';
            }

            $delivery = Delivery::create([
                'order_id' => $order->id,
                'deliverer_id' => $deliverer->id,
                'status' => $deliveryStatus,
                'created_at' => $order->confirmed_at ?: $order->created_at,
                'updated_at' => $order->delivered_at ?: ($order->confirmed_at ?: $order->created_at),
            ]);
        }

        // 为当前配送员创建一些特定的配送记录
        $currentDeliverer = Deliverer::where('status', 'busy')->first();
        if ($currentDeliverer) {
            // 创建一些待处理的订单
            for ($i = 0; $i < 3; $i++) {
                $order = Order::create([
                    'order_number' => 'ORD' . date('Ymd') . str_pad(200 + $i, 4, '0', STR_PAD_LEFT),
                    'customer_name' => $this->customerNames[array_rand($this->customerNames)],
                    'customer_phone' => $this->phones[array_rand($this->phones)],
                    'receiver_name' => $this->customerNames[array_rand($this->customerNames)],
                    'receiver_phone' => $this->phones[array_rand($this->phones)],
                    'delivery_address' => $this->addresses[array_rand($this->addresses)],
                    'total_amount' => rand(50, 300) + (rand(0, 99) / 100),
                    'status' => 'ready',
                    'created_at' => Carbon::now()->subMinutes(rand(10, 60)),
                    'confirmed_at' => Carbon::now()->subMinutes(rand(5, 30)),
                ]);

                Delivery::create([
                    'order_id' => $order->id,
                    'deliverer_id' => $currentDeliverer->id,
                    'status' => 'pending',
                    'created_at' => $order->confirmed_at,
                    'updated_at' => $order->confirmed_at,
                ]);
            }

            // 创建一个正在配送的订单
            $order = Order::create([
                'order_number' => 'ORD' . date('Ymd') . '0250',
                'customer_name' => '测试客户',
                'customer_phone' => '13800138888',
                'receiver_name' => '测试收货人',
                'receiver_phone' => '13800138888',
                'delivery_address' => '北京市朝阳区测试地址123号',
                'total_amount' => 128.50,
                'status' => 'delivering',
                'created_at' => Carbon::now()->subHour(),
                'confirmed_at' => Carbon::now()->subMinutes(45),
            ]);

            Delivery::create([
                'order_id' => $order->id,
                'deliverer_id' => $currentDeliverer->id,
                'status' => 'in_progress',
                'created_at' => $order->confirmed_at,
                'updated_at' => Carbon::now()->subMinutes(15),
            ]);
        }

        $this->command->info('已创建配送记录');
    }
} 