<?php

namespace App\Region\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class RegionCollection extends ResourceCollection
{
    /**
     * 资源集合的资源类
     *
     * @var string
     */
    public $collects = RegionResource::class;
    
    /**
     * 将资源集合转换为数组
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total_count' => $this->collection->count(),
            ],
        ];
    }
    
    /**
     * 获取应该随资源一起返回的其他数据
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function with($request)
    {
        return [
            'success' => true,
            'message' => '获取区域列表成功',
        ];
    }
} 