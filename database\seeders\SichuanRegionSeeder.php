<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Region\Models\Region;
use Illuminate\Support\Facades\DB;

class SichuanRegionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::beginTransaction();
        
        try {
            // 清空现有数据
            $this->command->info('正在清空现有区域数据...');
            
            // 禁用外键约束检查
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
            
            // 清空相关表数据
            DB::table('region_prices')->delete();
            DB::table('regions')->delete();
            
            // 重新启用外键约束检查
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
            
            $this->command->info('开始导入四川省区域数据...');
            
            // 创建四川省
            $sichuan = Region::create([
                'name' => '四川省',
                'code' => 'sichuan',
                'level' => 1,
                'parent_id' => 0,
                'sort' => 1,
                'status' => true
            ]);

            $this->command->info("已创建省级: {$sichuan->name}");

            // 创建所有地级市和自治州
            $cities = $this->getAllCities($sichuan->id);
            $cityMap = [];
            
            foreach ($cities as $cityData) {
                $city = Region::create($cityData);
                $cityMap[$cityData['code']] = $city;
                $this->command->info("已创建市级: {$city->name}");
            }

            // 创建所有区县
            $counties = $this->getAllCounties();
            $countyMap = [];
            
            foreach ($counties as $countyData) {
                if (isset($cityMap[$countyData['parent_code']])) {
                    $countyData['parent_id'] = $cityMap[$countyData['parent_code']]->id;
                    unset($countyData['parent_code']);
                    
                    $county = Region::create($countyData);
                    $countyMap[$countyData['code']] = $county;
                    $this->command->info("已创建区县: {$county->name}");
                }
            }

            // 创建部分街道/乡镇（示例）
            $streets = $this->getSampleStreets();
            
            foreach ($streets as $streetData) {
                if (isset($countyMap[$streetData['parent_code']])) {
                    $streetData['parent_id'] = $countyMap[$streetData['parent_code']]->id;
                    unset($streetData['parent_code']);
                    
                    $street = Region::create($streetData);
                    $this->command->info("已创建街道: {$street->name}");
                }
            }

            DB::commit();
            
            $this->command->info('✅ 四川省区域数据导入完成！');
            $this->command->info('📊 统计信息:');
            $this->command->info('省级: ' . Region::where('level', 1)->count() . ' 个');
            $this->command->info('市级: ' . Region::where('level', 2)->count() . ' 个');
            $this->command->info('区县级: ' . Region::where('level', 3)->count() . ' 个');
            $this->command->info('街道级: ' . Region::where('level', 4)->count() . ' 个');
            $this->command->info('总计: ' . Region::count() . ' 个');
            
        } catch (\Exception $e) {
            DB::rollback();
            // 确保重新启用外键约束检查
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
            $this->command->error('导入失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取所有地级市数据
     */
    private function getAllCities($provinceId): array
    {
        return [
            ['name' => '成都市', 'code' => 'chengdu', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 1, 'status' => true],
            ['name' => '自贡市', 'code' => 'zigong', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 2, 'status' => true],
            ['name' => '攀枝花市', 'code' => 'panzhihua', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 3, 'status' => true],
            ['name' => '泸州市', 'code' => 'luzhou', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 4, 'status' => true],
            ['name' => '德阳市', 'code' => 'deyang', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 5, 'status' => true],
            ['name' => '绵阳市', 'code' => 'mianyang', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 6, 'status' => true],
            ['name' => '广元市', 'code' => 'guangyuan', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 7, 'status' => true],
            ['name' => '遂宁市', 'code' => 'suining', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 8, 'status' => true],
            ['name' => '内江市', 'code' => 'neijiang', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 9, 'status' => true],
            ['name' => '乐山市', 'code' => 'leshan', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 10, 'status' => true],
            ['name' => '南充市', 'code' => 'nanchong', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 11, 'status' => true],
            ['name' => '眉山市', 'code' => 'meishan', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 12, 'status' => true],
            ['name' => '宜宾市', 'code' => 'yibin', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 13, 'status' => true],
            ['name' => '广安市', 'code' => 'guangan', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 14, 'status' => true],
            ['name' => '达州市', 'code' => 'dazhou', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 15, 'status' => true],
            ['name' => '雅安市', 'code' => 'yaan', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 16, 'status' => true],
            ['name' => '巴中市', 'code' => 'bazhong', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 17, 'status' => true],
            ['name' => '资阳市', 'code' => 'ziyang', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 18, 'status' => true],
            ['name' => '阿坝藏族羌族自治州', 'code' => 'aba', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 19, 'status' => true],
            ['name' => '甘孜藏族自治州', 'code' => 'ganzi', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 20, 'status' => true],
            ['name' => '凉山彝族自治州', 'code' => 'liangshan', 'level' => 2, 'parent_id' => $provinceId, 'sort' => 21, 'status' => true],
        ];
    }

    /**
     * 获取所有区县数据
     */
    private function getAllCounties(): array
    {
        return [
            // 成都市（20个区县）
            ['name' => '锦江区', 'code' => 'jinjiang', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '青羊区', 'code' => 'qingyang', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '金牛区', 'code' => 'jinniu', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '武侯区', 'code' => 'wuhou', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '成华区', 'code' => 'chenghua', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '龙泉驿区', 'code' => 'longquanyi', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 6, 'status' => true],
            ['name' => '青白江区', 'code' => 'qingbaijiang', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 7, 'status' => true],
            ['name' => '新都区', 'code' => 'xindu', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 8, 'status' => true],
            ['name' => '温江区', 'code' => 'wenjiang', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 9, 'status' => true],
            ['name' => '双流区', 'code' => 'shuangliu', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 10, 'status' => true],
            ['name' => '郫都区', 'code' => 'pidu', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 11, 'status' => true],
            ['name' => '新津区', 'code' => 'xinjin', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 12, 'status' => true],
            ['name' => '都江堰市', 'code' => 'dujiangyan', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 13, 'status' => true],
            ['name' => '彭州市', 'code' => 'pengzhou', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 14, 'status' => true],
            ['name' => '邛崃市', 'code' => 'qionglai', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 15, 'status' => true],
            ['name' => '崇州市', 'code' => 'chongzhou', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 16, 'status' => true],
            ['name' => '简阳市', 'code' => 'jianyang', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 17, 'status' => true],
            ['name' => '金堂县', 'code' => 'jintang', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 18, 'status' => true],
            ['name' => '大邑县', 'code' => 'dayi', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 19, 'status' => true],
            ['name' => '蒲江县', 'code' => 'pujiang', 'parent_code' => 'chengdu', 'level' => 3, 'sort' => 20, 'status' => true],

            // 自贡市（6个区县）
            ['name' => '自流井区', 'code' => 'ziliujing', 'parent_code' => 'zigong', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '贡井区', 'code' => 'gongjing', 'parent_code' => 'zigong', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '大安区', 'code' => 'daan', 'parent_code' => 'zigong', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '沿滩区', 'code' => 'yantan', 'parent_code' => 'zigong', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '荣县', 'code' => 'rongxian', 'parent_code' => 'zigong', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '富顺县', 'code' => 'fushun', 'parent_code' => 'zigong', 'level' => 3, 'sort' => 6, 'status' => true],

            // 攀枝花市（5个区县）
            ['name' => '东区', 'code' => 'dongqu', 'parent_code' => 'panzhihua', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '西区', 'code' => 'xiqu', 'parent_code' => 'panzhihua', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '仁和区', 'code' => 'renhe', 'parent_code' => 'panzhihua', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '米易县', 'code' => 'miyi', 'parent_code' => 'panzhihua', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '盐边县', 'code' => 'yanbian', 'parent_code' => 'panzhihua', 'level' => 3, 'sort' => 5, 'status' => true],

            // 泸州市（7个区县）
            ['name' => '江阳区', 'code' => 'jiangyang', 'parent_code' => 'luzhou', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '纳溪区', 'code' => 'naxi', 'parent_code' => 'luzhou', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '龙马潭区', 'code' => 'longmatan', 'parent_code' => 'luzhou', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '泸县', 'code' => 'luxian', 'parent_code' => 'luzhou', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '合江县', 'code' => 'hejiang', 'parent_code' => 'luzhou', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '叙永县', 'code' => 'xuyong', 'parent_code' => 'luzhou', 'level' => 3, 'sort' => 6, 'status' => true],
            ['name' => '古蔺县', 'code' => 'gulin', 'parent_code' => 'luzhou', 'level' => 3, 'sort' => 7, 'status' => true],

            // 德阳市（6个区县）
            ['name' => '旌阳区', 'code' => 'jingyang', 'parent_code' => 'deyang', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '罗江区', 'code' => 'luojiang', 'parent_code' => 'deyang', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '广汉市', 'code' => 'guanghan', 'parent_code' => 'deyang', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '什邡市', 'code' => 'shifang', 'parent_code' => 'deyang', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '绵竹市', 'code' => 'mianzhu', 'parent_code' => 'deyang', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '中江县', 'code' => 'zhongjiang', 'parent_code' => 'deyang', 'level' => 3, 'sort' => 6, 'status' => true],

            // 绵阳市（9个区县）
            ['name' => '涪城区', 'code' => 'fucheng', 'parent_code' => 'mianyang', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '游仙区', 'code' => 'youxian', 'parent_code' => 'mianyang', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '安州区', 'code' => 'anzhou', 'parent_code' => 'mianyang', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '江油市', 'code' => 'jiangyou', 'parent_code' => 'mianyang', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '三台县', 'code' => 'santai', 'parent_code' => 'mianyang', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '盐亭县', 'code' => 'yanting', 'parent_code' => 'mianyang', 'level' => 3, 'sort' => 6, 'status' => true],
            ['name' => '梓潼县', 'code' => 'zitong', 'parent_code' => 'mianyang', 'level' => 3, 'sort' => 7, 'status' => true],
            ['name' => '北川羌族自治县', 'code' => 'beichuan', 'parent_code' => 'mianyang', 'level' => 3, 'sort' => 8, 'status' => true],
            ['name' => '平武县', 'code' => 'pingwu', 'parent_code' => 'mianyang', 'level' => 3, 'sort' => 9, 'status' => true],

            // 广元市（7个区县）
            ['name' => '利州区', 'code' => 'lizhou', 'parent_code' => 'guangyuan', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '昭化区', 'code' => 'zhaohua', 'parent_code' => 'guangyuan', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '朝天区', 'code' => 'chaotian', 'parent_code' => 'guangyuan', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '旺苍县', 'code' => 'wangcang', 'parent_code' => 'guangyuan', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '青川县', 'code' => 'qingchuan', 'parent_code' => 'guangyuan', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '剑阁县', 'code' => 'jiange', 'parent_code' => 'guangyuan', 'level' => 3, 'sort' => 6, 'status' => true],
            ['name' => '苍溪县', 'code' => 'cangxi', 'parent_code' => 'guangyuan', 'level' => 3, 'sort' => 7, 'status' => true],

            // 遂宁市（5个区县）
            ['name' => '船山区', 'code' => 'chuanshan', 'parent_code' => 'suining', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '安居区', 'code' => 'anju', 'parent_code' => 'suining', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '蓬溪县', 'code' => 'pengxi', 'parent_code' => 'suining', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '大英县', 'code' => 'daying', 'parent_code' => 'suining', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '射洪市', 'code' => 'shehong', 'parent_code' => 'suining', 'level' => 3, 'sort' => 5, 'status' => true],

            // 内江市（5个区县）
            ['name' => '市中区', 'code' => 'shizhong_nj', 'parent_code' => 'neijiang', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '东兴区', 'code' => 'dongxing', 'parent_code' => 'neijiang', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '威远县', 'code' => 'weiyuan', 'parent_code' => 'neijiang', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '资中县', 'code' => 'zizhong', 'parent_code' => 'neijiang', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '隆昌市', 'code' => 'longchang', 'parent_code' => 'neijiang', 'level' => 3, 'sort' => 5, 'status' => true],

            // 乐山市（11个区县）
            ['name' => '市中区', 'code' => 'shizhong_ls', 'parent_code' => 'leshan', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '沙湾区', 'code' => 'shawan', 'parent_code' => 'leshan', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '五通桥区', 'code' => 'wutongqiao', 'parent_code' => 'leshan', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '金口河区', 'code' => 'jinkouhe', 'parent_code' => 'leshan', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '犍为县', 'code' => 'jianwei', 'parent_code' => 'leshan', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '井研县', 'code' => 'jingyan', 'parent_code' => 'leshan', 'level' => 3, 'sort' => 6, 'status' => true],
            ['name' => '夹江县', 'code' => 'jiajiang', 'parent_code' => 'leshan', 'level' => 3, 'sort' => 7, 'status' => true],
            ['name' => '沐川县', 'code' => 'muchuan', 'parent_code' => 'leshan', 'level' => 3, 'sort' => 8, 'status' => true],
            ['name' => '峨边彝族自治县', 'code' => 'ebian', 'parent_code' => 'leshan', 'level' => 3, 'sort' => 9, 'status' => true],
            ['name' => '马边彝族自治县', 'code' => 'mabian', 'parent_code' => 'leshan', 'level' => 3, 'sort' => 10, 'status' => true],
            ['name' => '峨眉山市', 'code' => 'emeishan', 'parent_code' => 'leshan', 'level' => 3, 'sort' => 11, 'status' => true],

            // 南充市（9个区县）
            ['name' => '顺庆区', 'code' => 'shunqing', 'parent_code' => 'nanchong', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '高坪区', 'code' => 'gaoping', 'parent_code' => 'nanchong', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '嘉陵区', 'code' => 'jialing', 'parent_code' => 'nanchong', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '南部县', 'code' => 'nanbu', 'parent_code' => 'nanchong', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '营山县', 'code' => 'yingshan', 'parent_code' => 'nanchong', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '蓬安县', 'code' => 'pengan', 'parent_code' => 'nanchong', 'level' => 3, 'sort' => 6, 'status' => true],
            ['name' => '仪陇县', 'code' => 'yilong', 'parent_code' => 'nanchong', 'level' => 3, 'sort' => 7, 'status' => true],
            ['name' => '西充县', 'code' => 'xichong', 'parent_code' => 'nanchong', 'level' => 3, 'sort' => 8, 'status' => true],
            ['name' => '阆中市', 'code' => 'langzhong', 'parent_code' => 'nanchong', 'level' => 3, 'sort' => 9, 'status' => true],

            // 眉山市（6个区县）
            ['name' => '东坡区', 'code' => 'dongpo', 'parent_code' => 'meishan', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '彭山区', 'code' => 'pengshan', 'parent_code' => 'meishan', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '仁寿县', 'code' => 'renshou', 'parent_code' => 'meishan', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '洪雅县', 'code' => 'hongya', 'parent_code' => 'meishan', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '丹棱县', 'code' => 'danling', 'parent_code' => 'meishan', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '青神县', 'code' => 'qingshen', 'parent_code' => 'meishan', 'level' => 3, 'sort' => 6, 'status' => true],

            // 宜宾市（10个区县）
            ['name' => '翠屏区', 'code' => 'cuiping', 'parent_code' => 'yibin', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '南溪区', 'code' => 'nanxi', 'parent_code' => 'yibin', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '叙州区', 'code' => 'xuzhou', 'parent_code' => 'yibin', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '江安县', 'code' => 'jiangan', 'parent_code' => 'yibin', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '长宁县', 'code' => 'changning', 'parent_code' => 'yibin', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '高县', 'code' => 'gaoxian', 'parent_code' => 'yibin', 'level' => 3, 'sort' => 6, 'status' => true],
            ['name' => '珙县', 'code' => 'gongxian', 'parent_code' => 'yibin', 'level' => 3, 'sort' => 7, 'status' => true],
            ['name' => '筠连县', 'code' => 'junlian', 'parent_code' => 'yibin', 'level' => 3, 'sort' => 8, 'status' => true],
            ['name' => '兴文县', 'code' => 'xingwen', 'parent_code' => 'yibin', 'level' => 3, 'sort' => 9, 'status' => true],
            ['name' => '屏山县', 'code' => 'pingshan', 'parent_code' => 'yibin', 'level' => 3, 'sort' => 10, 'status' => true],

            // 广安市（6个区县）
            ['name' => '广安区', 'code' => 'guangan_qu', 'parent_code' => 'guangan', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '前锋区', 'code' => 'qianfeng', 'parent_code' => 'guangan', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '岳池县', 'code' => 'yuechi', 'parent_code' => 'guangan', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '武胜县', 'code' => 'wusheng', 'parent_code' => 'guangan', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '邻水县', 'code' => 'linshui', 'parent_code' => 'guangan', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '华蓥市', 'code' => 'huaying', 'parent_code' => 'guangan', 'level' => 3, 'sort' => 6, 'status' => true],

            // 达州市（7个区县）
            ['name' => '通川区', 'code' => 'tongchuan', 'parent_code' => 'dazhou', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '达川区', 'code' => 'dachuan', 'parent_code' => 'dazhou', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '宣汉县', 'code' => 'xuanhan', 'parent_code' => 'dazhou', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '开江县', 'code' => 'kaijiang', 'parent_code' => 'dazhou', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '大竹县', 'code' => 'dazhu', 'parent_code' => 'dazhou', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '渠县', 'code' => 'quxian', 'parent_code' => 'dazhou', 'level' => 3, 'sort' => 6, 'status' => true],
            ['name' => '万源市', 'code' => 'wanyuan', 'parent_code' => 'dazhou', 'level' => 3, 'sort' => 7, 'status' => true],

            // 雅安市（8个区县）
            ['name' => '雨城区', 'code' => 'yucheng', 'parent_code' => 'yaan', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '名山区', 'code' => 'mingshan', 'parent_code' => 'yaan', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '荥经县', 'code' => 'yingjing', 'parent_code' => 'yaan', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '汉源县', 'code' => 'hanyuan', 'parent_code' => 'yaan', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '石棉县', 'code' => 'shimian', 'parent_code' => 'yaan', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '天全县', 'code' => 'tianquan', 'parent_code' => 'yaan', 'level' => 3, 'sort' => 6, 'status' => true],
            ['name' => '芦山县', 'code' => 'lushan', 'parent_code' => 'yaan', 'level' => 3, 'sort' => 7, 'status' => true],
            ['name' => '宝兴县', 'code' => 'baoxing', 'parent_code' => 'yaan', 'level' => 3, 'sort' => 8, 'status' => true],

            // 巴中市（5个区县）
            ['name' => '巴州区', 'code' => 'bazhou', 'parent_code' => 'bazhong', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '恩阳区', 'code' => 'enyang', 'parent_code' => 'bazhong', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '通江县', 'code' => 'tongjiang', 'parent_code' => 'bazhong', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '南江县', 'code' => 'nanjiang', 'parent_code' => 'bazhong', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '平昌县', 'code' => 'pingchang', 'parent_code' => 'bazhong', 'level' => 3, 'sort' => 5, 'status' => true],

            // 资阳市（3个区县）
            ['name' => '雁江区', 'code' => 'yanjiang', 'parent_code' => 'ziyang', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '安岳县', 'code' => 'anyue', 'parent_code' => 'ziyang', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '乐至县', 'code' => 'lezhi', 'parent_code' => 'ziyang', 'level' => 3, 'sort' => 3, 'status' => true],

            // 阿坝藏族羌族自治州（13个县）
            ['name' => '马尔康市', 'code' => 'maerkang', 'parent_code' => 'aba', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '汶川县', 'code' => 'wenchuan', 'parent_code' => 'aba', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '理县', 'code' => 'lixian', 'parent_code' => 'aba', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '茂县', 'code' => 'maoxian', 'parent_code' => 'aba', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '松潘县', 'code' => 'songpan', 'parent_code' => 'aba', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '九寨沟县', 'code' => 'jiuzhaigou', 'parent_code' => 'aba', 'level' => 3, 'sort' => 6, 'status' => true],
            ['name' => '金川县', 'code' => 'jinchuan', 'parent_code' => 'aba', 'level' => 3, 'sort' => 7, 'status' => true],
            ['name' => '小金县', 'code' => 'xiaojin', 'parent_code' => 'aba', 'level' => 3, 'sort' => 8, 'status' => true],
            ['name' => '黑水县', 'code' => 'heishui', 'parent_code' => 'aba', 'level' => 3, 'sort' => 9, 'status' => true],
            ['name' => '壤塘县', 'code' => 'rangtang', 'parent_code' => 'aba', 'level' => 3, 'sort' => 10, 'status' => true],
            ['name' => '阿坝县', 'code' => 'aba_xian', 'parent_code' => 'aba', 'level' => 3, 'sort' => 11, 'status' => true],
            ['name' => '若尔盖县', 'code' => 'ruoergai', 'parent_code' => 'aba', 'level' => 3, 'sort' => 12, 'status' => true],
            ['name' => '红原县', 'code' => 'hongyuan', 'parent_code' => 'aba', 'level' => 3, 'sort' => 13, 'status' => true],

            // 甘孜藏族自治州（18个县）
            ['name' => '康定市', 'code' => 'kangding', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '泸定县', 'code' => 'luding', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '丹巴县', 'code' => 'danba', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '九龙县', 'code' => 'jiulong', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '雅江县', 'code' => 'yajiang', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '道孚县', 'code' => 'daofu', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 6, 'status' => true],
            ['name' => '炉霍县', 'code' => 'luhuo', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 7, 'status' => true],
            ['name' => '甘孜县', 'code' => 'ganzi_xian', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 8, 'status' => true],
            ['name' => '新龙县', 'code' => 'xinlong', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 9, 'status' => true],
            ['name' => '德格县', 'code' => 'dege', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 10, 'status' => true],
            ['name' => '白玉县', 'code' => 'baiyu', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 11, 'status' => true],
            ['name' => '石渠县', 'code' => 'shiqu', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 12, 'status' => true],
            ['name' => '色达县', 'code' => 'seda', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 13, 'status' => true],
            ['name' => '理塘县', 'code' => 'litang', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 14, 'status' => true],
            ['name' => '巴塘县', 'code' => 'batang', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 15, 'status' => true],
            ['name' => '乡城县', 'code' => 'xiangcheng', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 16, 'status' => true],
            ['name' => '稻城县', 'code' => 'daocheng', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 17, 'status' => true],
            ['name' => '得荣县', 'code' => 'derong', 'parent_code' => 'ganzi', 'level' => 3, 'sort' => 18, 'status' => true],

            // 凉山彝族自治州（17个县市）
            ['name' => '西昌市', 'code' => 'xichang', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 1, 'status' => true],
            ['name' => '木里藏族自治县', 'code' => 'muli', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 2, 'status' => true],
            ['name' => '盐源县', 'code' => 'yanyuan', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 3, 'status' => true],
            ['name' => '德昌县', 'code' => 'dechang', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 4, 'status' => true],
            ['name' => '会理市', 'code' => 'huili', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 5, 'status' => true],
            ['name' => '会东县', 'code' => 'huidong', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 6, 'status' => true],
            ['name' => '宁南县', 'code' => 'ningnan', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 7, 'status' => true],
            ['name' => '普格县', 'code' => 'puge', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 8, 'status' => true],
            ['name' => '布拖县', 'code' => 'butuo', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 9, 'status' => true],
            ['name' => '金阳县', 'code' => 'jinyang', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 10, 'status' => true],
            ['name' => '昭觉县', 'code' => 'zhaojue', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 11, 'status' => true],
            ['name' => '喜德县', 'code' => 'xide', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 12, 'status' => true],
            ['name' => '冕宁县', 'code' => 'mianning', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 13, 'status' => true],
            ['name' => '越西县', 'code' => 'yuexi', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 14, 'status' => true],
            ['name' => '甘洛县', 'code' => 'ganluo', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 15, 'status' => true],
            ['name' => '美姑县', 'code' => 'meigu', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 16, 'status' => true],
            ['name' => '雷波县', 'code' => 'leibo', 'parent_code' => 'liangshan', 'level' => 3, 'sort' => 17, 'status' => true],
        ];
    }

    /**
     * 获取示例街道数据
     */
    private function getSampleStreets(): array
    {
        return [
            // 成都市锦江区街道
            ['name' => '督院街街道', 'code' => 'duyuanjie', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 1, 'status' => true],
            ['name' => '盐市口街道', 'code' => 'yanshikou', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 2, 'status' => true],
            ['name' => '春熙路街道', 'code' => 'chunxilu', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 3, 'status' => true],
            ['name' => '书院街街道', 'code' => 'shuyuanjie', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 4, 'status' => true],
            ['name' => '合江亭街道', 'code' => 'hejiangting', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 5, 'status' => true],
            ['name' => '水井坊街道', 'code' => 'shuijingfang', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 6, 'status' => true],
            ['name' => '牛市口街道', 'code' => 'niushikou', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 7, 'status' => true],
            ['name' => '龙舟路街道', 'code' => 'longzhoulu', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 8, 'status' => true],
            ['name' => '双桂路街道', 'code' => 'shuangguilu', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 9, 'status' => true],
            ['name' => '莲新街道', 'code' => 'lianxin', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 10, 'status' => true],
            ['name' => '沙河街道', 'code' => 'shahe', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 11, 'status' => true],
            ['name' => '东光街道', 'code' => 'dongguang', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 12, 'status' => true],
            ['name' => '狮子山街道', 'code' => 'shizishan', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 13, 'status' => true],
            ['name' => '成龙路街道', 'code' => 'chenglonglu', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 14, 'status' => true],
            ['name' => '柳江街道', 'code' => 'liujiang', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 15, 'status' => true],
            ['name' => '三圣街道', 'code' => 'sansheng', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 16, 'status' => true],
            ['name' => '琉璃场街道', 'code' => 'liulichang', 'parent_code' => 'jinjiang', 'level' => 4, 'sort' => 17, 'status' => true],

            // 成都市青羊区街道
            ['name' => '太升路街道', 'code' => 'taishenglu', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 1, 'status' => true],
            ['name' => '草市街街道', 'code' => 'caoshijie', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 2, 'status' => true],
            ['name' => '西御河街道', 'code' => 'xiyuhe', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 3, 'status' => true],
            ['name' => '汪家拐街道', 'code' => 'wangjiaguai', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 4, 'status' => true],
            ['name' => '少城街道', 'code' => 'shaocheng', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 5, 'status' => true],
            ['name' => '新华西路街道', 'code' => 'xinhuaxilu', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 6, 'status' => true],
            ['name' => '草堂路街道', 'code' => 'caotanglu', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 7, 'status' => true],
            ['name' => '府南街道', 'code' => 'funan', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 8, 'status' => true],
            ['name' => '光华街道', 'code' => 'guanghua', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 9, 'status' => true],
            ['name' => '东坡街道', 'code' => 'dongpo_jd', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 10, 'status' => true],
            ['name' => '金沙街道', 'code' => 'jinsha', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 11, 'status' => true],
            ['name' => '黄田坝街道', 'code' => 'huangtianba', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 12, 'status' => true],
            ['name' => '苏坡街道', 'code' => 'supo', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 13, 'status' => true],
            ['name' => '文家街道', 'code' => 'wenjia', 'parent_code' => 'qingyang', 'level' => 4, 'sort' => 14, 'status' => true],

            // 成都市金牛区街道
            ['name' => '西安路街道', 'code' => 'xianlu', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 1, 'status' => true],
            ['name' => '西华街道', 'code' => 'xihua', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 2, 'status' => true],
            ['name' => '人民北路街道', 'code' => 'renminbeilu', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 3, 'status' => true],
            ['name' => '荷花池街道', 'code' => 'hehuachi', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 4, 'status' => true],
            ['name' => '驷马桥街道', 'code' => 'simaqiao', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 5, 'status' => true],
            ['name' => '茶店子街道', 'code' => 'chadianzi', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 6, 'status' => true],
            ['name' => '抚琴街道', 'code' => 'fuqin', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 7, 'status' => true],
            ['name' => '九里堤街道', 'code' => 'jiulidi', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 8, 'status' => true],
            ['name' => '五块石街道', 'code' => 'wukuaishi', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 9, 'status' => true],
            ['name' => '黄忠街道', 'code' => 'huangzhong', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 10, 'status' => true],
            ['name' => '营门口街道', 'code' => 'yingmenkou', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 11, 'status' => true],
            ['name' => '金泉街道', 'code' => 'jinquan', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 12, 'status' => true],
            ['name' => '沙河源街道', 'code' => 'shaheyuan', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 13, 'status' => true],
            ['name' => '天回镇街道', 'code' => 'tianhuizhen', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 14, 'status' => true],
            ['name' => '凤凰山街道', 'code' => 'fenghuangshan', 'parent_code' => 'jinniu', 'level' => 4, 'sort' => 15, 'status' => true],
        ];
    }
} 