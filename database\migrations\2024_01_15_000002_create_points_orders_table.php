<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('points_orders', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->string('order_no', 32)->unique()->comment('订单号');
            $table->unsignedInteger('total_points')->default(0)->comment('总积分');
            $table->decimal('cash_amount', 10, 2)->default(0)->comment('现金金额');
            $table->enum('status', ['pending', 'paid', 'shipped', 'delivered', 'cancelled', 'completed'])->default('pending')->comment('订单状态');
            $table->string('payment_method', 20)->nullable()->comment('支付方式');
            $table->string('payment_no', 100)->nullable()->comment('支付单号');
            $table->json('shipping_address')->nullable()->comment('配送地址');
            $table->string('contact_name', 50)->nullable()->comment('联系人姓名');
            $table->string('contact_phone', 20)->nullable()->comment('联系人电话');
            $table->enum('delivery_method', ['express', 'pickup', 'virtual'])->default('express')->comment('配送方式');
            $table->date('delivery_date')->nullable()->comment('配送日期');
            $table->string('delivery_time', 50)->nullable()->comment('配送时间');
            $table->text('delivery_notes')->nullable()->comment('配送备注');
            $table->text('notes')->nullable()->comment('订单备注');
            $table->timestamp('paid_at')->nullable()->comment('支付时间');
            $table->timestamp('shipped_at')->nullable()->comment('发货时间');
            $table->timestamp('delivered_at')->nullable()->comment('送达时间');
            $table->timestamp('cancelled_at')->nullable()->comment('取消时间');
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['status', 'created_at']);
            $table->index('order_no');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('points_orders');
    }
}; 