<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('crm_agent_id')->nullable()->constrained('employees')->after('default_deliverer_id')
                ->comment('关联的CRM专员ID(员工)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['crm_agent_id']);
            $table->dropColumn('crm_agent_id');
        });
    }
}; 