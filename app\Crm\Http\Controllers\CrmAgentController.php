<?php

namespace App\Crm\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Crm\Models\CrmAgent;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class CrmAgentController extends Controller
{
    /**
     * 获取CRM专员列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // 验证权限
        $currentUser = $request->user();
        if (!in_array($currentUser->role, ['admin', 'manager'])) {
            return response()->json(ApiResponse::error('没有权限查看CRM专员列表', 403), 403);
        }
        
        // 查询参数
        $keyword = $request->input('keyword');
        $status = $request->input('status');
        $specialty = $request->input('specialty');
        $perPage = $request->input('per_page', 10);
        
        // 构建查询
        $query = CrmAgent::with('employee');
        
        // 按状态筛选
        if ($status) {
            $query->where('status', $status);
        }
        
        // 按专长筛选
        if ($specialty) {
            $query->where('specialty', 'like', "%{$specialty}%");
        }
        
        // 关键字搜索
        if ($keyword) {
            $query->whereHas('employee', function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('phone', 'like', "%{$keyword}%");
            });
        }
        
        $agents = $query->paginate($perPage);
        
        return response()->json(ApiResponse::success($agents));
    }
    
    /**
     * 创建CRM专员
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // 验证权限
        $currentUser = $request->user();
        if (!in_array($currentUser->role, ['admin', 'manager'])) {
            return response()->json(ApiResponse::error('没有权限创建CRM专员', 403), 403);
        }
        
        $validator = Validator::make($request->all(), [
            'employee_id' => 'required|exists:employees,id',
            'service_area' => 'nullable|array',
            'service_area.*' => 'string|max:255',
            'max_clients' => 'nullable|integer|min:1|max:1000',
            'specialty' => 'nullable|string|max:100',
            'monthly_target' => 'nullable|integer|min:0',
            'performance_rating' => 'nullable|numeric|min:0|max:10',
            'status' => 'nullable|in:available,busy,offline',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        DB::beginTransaction();
        
        try {
            // 查找员工
            $employee = \App\Employee\Models\Employee::findOrFail($request->employee_id);
            
            // 检查员工是否已经是CRM专员
            $existingAgent = CrmAgent::where('employee_id', $employee->id)->first();
            if ($existingAgent) {
                return response()->json(ApiResponse::error('该员工已经是CRM专员', 400), 400);
            }
            
            // 创建CRM专员记录
            $agent = CrmAgent::create([
                'employee_id' => $employee->id,
                'service_area' => $request->service_area,
                'max_clients' => $request->max_clients ?? 50,
                'specialty' => $request->specialty,
                'monthly_target' => $request->monthly_target ?? 0,
                'performance_rating' => $request->performance_rating ?? 5.0,
                'status' => $request->status ?? 'available',
                'clients_count' => 0,
            ]);
            
            // 更新员工角色为CRM专员（如果还不是的话）
            if ($employee->role !== 'crm_agent') {
                $employee->role = 'crm_agent';
                $employee->save();
            }
            
            DB::commit();
            
            // 加载员工关联信息
            $agent->load('employee');
            
            return response()->json(ApiResponse::success($agent, 'CRM专员创建成功'), 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('创建CRM专员失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取CRM专员详情
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        // 验证权限
        $currentUser = $request->user();
        $agent = CrmAgent::with('employee')->findOrFail($id);
        
        // 检查权限：管理员、经理或专员本人可以查看
        if (!in_array($currentUser->role, ['admin', 'manager']) && $currentUser->id !== $agent->employee_id) {
            return response()->json(ApiResponse::error('没有权限查看此CRM专员信息', 403), 403);
        }
        
        return response()->json(ApiResponse::success($agent));
    }
    
    /**
     * 更新CRM专员信息
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // 验证权限
        $currentUser = $request->user();
        $agent = CrmAgent::findOrFail($id);
        
        // 检查权限：管理员、经理或专员本人可以修改
        if (!in_array($currentUser->role, ['admin', 'manager']) && $currentUser->id !== $agent->employee_id) {
            return response()->json(ApiResponse::error('没有权限更新此CRM专员信息', 403), 403);
        }
        
        $validator = Validator::make($request->all(), [
            'employee_id' => 'nullable|exists:employees,id',
            'service_area' => 'nullable|array',
            'service_area.*' => 'string|max:255',
            'max_clients' => 'nullable|integer|min:1|max:1000',
            'specialty' => 'nullable|string|max:100',
            'monthly_target' => 'nullable|integer|min:0',
            'performance_rating' => 'nullable|numeric|min:0|max:10',
            'status' => 'nullable|in:available,busy,offline',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        DB::beginTransaction();
        
        try {
            // 如果要更换员工，需要检查新员工是否已经是CRM专员
            if ($request->has('employee_id') && $request->employee_id != $agent->employee_id) {
                $existingAgent = CrmAgent::where('employee_id', $request->employee_id)
                                         ->where('id', '!=', $agent->id)
                                         ->first();
                if ($existingAgent) {
                    return response()->json(ApiResponse::error('目标员工已经是CRM专员', 400), 400);
                }
            }
            
            // 更新CRM专员记录
            $agent->update($request->only([
                'employee_id',
                'service_area',
                'max_clients',
                'specialty',
                'monthly_target',
                'performance_rating',
                'status',
            ]));
            
            DB::commit();
            
            // 重新加载关联数据
            $agent->load('employee');
            
            return response()->json(ApiResponse::success($agent, 'CRM专员信息更新成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('更新CRM专员信息失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 删除CRM专员
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        // 验证权限
        $currentUser = $request->user();
        if ($currentUser->role !== 'admin') {
            return response()->json(ApiResponse::error('没有权限删除CRM专员', 403), 403);
        }
        
        $agent = CrmAgent::findOrFail($id);
        
        DB::beginTransaction();
        
        try {
            // 删除CRM专员记录
            $agent->delete();
            
            // 注意：这里并没有删除关联的用户账号，而是保留用户账号
            // 如果需要彻底删除，取消下面的注释
            // $agent->employee->delete();
            
            DB::commit();
            
            return response()->json(ApiResponse::success(null, 'CRM专员删除成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('删除CRM专员失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取CRM专员的客户列表
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function clients(Request $request, $id)
    {
        // 验证权限
        $currentUser = $request->user();
        $agent = CrmAgent::findOrFail($id);
        
        // 检查权限：管理员、经理或专员本人可以查看
        if (!in_array($currentUser->role, ['admin', 'manager']) && $currentUser->id !== $agent->employee_id) {
            return response()->json(ApiResponse::error('没有权限查看此CRM专员的客户列表', 403), 403);
        }
        
        // 获取该CRM专员管理的客户
        $query = \App\Models\User::where('crm_agent_id', $agent->employee_id);
        
        // 状态筛选
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }
        
        // 关键词搜索
        if ($request->has('keyword') && $request->keyword) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('phone', 'like', "%{$keyword}%")
                  ->orWhere('nickname', 'like', "%{$keyword}%");
            });
        }
        
        $clients = $query->paginate($request->input('per_page', 10));
        
        return response()->json(ApiResponse::success($clients));
    }
    
    /**
     * 更新CRM专员状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        // 验证权限
        $currentUser = $request->user();
        $agent = CrmAgent::findOrFail($id);
        
        // 检查权限：管理员、经理或专员本人可以修改状态
        if (!in_array($currentUser->role, ['admin', 'manager']) && $currentUser->id !== $agent->employee_id) {
            return response()->json(ApiResponse::error('没有权限更新此CRM专员状态', 403), 403);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:available,busy,offline',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        try {
            $agent->update([
                'status' => $request->status,
            ]);
            
            // 重新加载关联数据
            $agent->load('employee');
            
            return response()->json(ApiResponse::success($agent, 'CRM专员状态更新成功'));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('更新CRM专员状态失败: ' . $e->getMessage(), 500), 500);
        }
    }
} 