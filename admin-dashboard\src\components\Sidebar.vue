<template>
  <div class="sidebar">
    <!-- 左侧一级菜单 -->
    <div class="primary-menu">
      <!-- Logo区域 -->
      <div class="logo">
        <span class="logo-icon">🍃</span>
        <span class="logo-text">天心</span>
      </div>
      
      <!-- 一级导航菜单 -->
      <div class="menu-items">
        <!-- 概况 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'dashboard' }"
          @click="setActiveMenu('dashboard')"
        >
          <el-icon><Monitor /></el-icon>
          <span>概况</span>
        </div>
        
        <!-- 商品 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'products' }"
          @click="setActiveMenu('products')"
        >
          <el-icon><ShoppingCart /></el-icon>
          <span>商品</span>
        </div>
        
        <!-- 用户 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'users' }"
          @click="setActiveMenu('users')"
        >
          <el-icon><User /></el-icon>
          <span>用户</span>
        </div>
        
        <!-- 订单 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'orders' }"
          @click="setActiveMenu('orders')"
        >
          <el-icon><Document /></el-icon>
          <span>订单</span>
        </div>
        
        <!-- 采购 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'purchase' }"
          @click="setActiveMenu('purchase')"
        >
          <el-icon><Van /></el-icon>
          <span>采购</span>
        </div>
        
        <!-- 库房 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'warehouse' }"
          @click="setActiveMenu('warehouse')"
        >
          <el-icon><House /></el-icon>
          <span>库房</span>
        </div>
        
        <!-- 分拣 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'sorting' }"
          @click="setActiveMenu('sorting')"
        >
          <el-icon><Grid /></el-icon>
          <span>分拣</span>
        </div>
        
        <!-- 配送 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'delivery' }"
          @click="setActiveMenu('delivery')"
        >
          <el-icon><Van /></el-icon>
          <span>配送</span>
        </div>
        
        <!-- 财务 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'finance' }"
          @click="setActiveMenu('finance')"
        >
          <el-icon><Coin /></el-icon>
          <span>财务</span>
        </div>
        
        <!-- 报表 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'reports' }"
          @click="setActiveMenu('reports')"
        >
          <el-icon><PieChart /></el-icon>
          <span>报表</span>
        </div>
        
        <!-- 应用 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'apps' }"
          @click="setActiveMenu('apps')"
        >
          <el-icon><Grid /></el-icon>
          <span>应用</span>
        </div>
        
        <!-- 渠道 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'channels' }"
          @click="setActiveMenu('channels')"
        >
          <el-icon><Connection /></el-icon>
          <span>渠道</span>
        </div>
        
        <!-- 硬件 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'hardware' }"
          @click="setActiveMenu('hardware')"
        >
          <el-icon><Monitor /></el-icon>
          <span>硬件</span>
        </div>
        
        <!-- 设置 -->
        <div 
          class="menu-item" 
          :class="{ active: activeMenu === 'settings' }"
          @click="setActiveMenu('settings')"
        >
          <el-icon><Setting /></el-icon>
          <span>设置</span>
        </div>
      </div>
    </div>
    
    <!-- 右侧二级菜单 -->
    <div class="secondary-menu" v-if="hasSubMenu">
      <div class="submenu-header">
        <el-icon>
          <component :is="currentMenuIcon" />
        </el-icon>
        <span>{{ currentMenuTitle }}</span>
      </div>
      
      <div class="submenu-content">
        <!-- 商品子菜单 -->
        <template v-if="activeMenu === 'products'">
          <router-link to="/products" class="submenu-item">
            <span>商品列表</span>
          </router-link>
          <router-link to="/products/group-price" class="submenu-item">
            <span>分组报价</span>
          </router-link>
          <router-link to="/products/smart-price" class="submenu-item">
            <span>智能定价</span>
          </router-link>
          <router-link to="/products/tags" class="submenu-item">
            <span>商品标签</span>
          </router-link>
          <router-link to="/products/specs" class="submenu-item">
            <span>商品规格</span>
          </router-link>
          <router-link to="/products/ads" class="submenu-item">
            <span>商品广告</span>
          </router-link>
          <router-link to="/products/tags-top" class="submenu-item">
            <span>商品标签左上</span>
          </router-link>
          <router-link to="/products/reviews" class="submenu-item">
            <span>商品评价</span>
          </router-link>
          <router-link to="/products/service" class="submenu-item">
            <span>商品服务</span>
          </router-link>
          
          <!-- 商品分类分组 -->
          <div class="submenu-group">
            <div class="submenu-group-title">
              <el-icon><Folder /></el-icon>
              <span>商品分类</span>
            </div>
            <router-link to="/categories" class="submenu-item submenu-sub-item">
              <span>分类列表</span>
            </router-link>
            <router-link to="/categories/slides" class="submenu-item submenu-sub-item">
              <span>分类幻灯片</span>
            </router-link>
            <router-link to="/categories/settings" class="submenu-item submenu-sub-item">
              <span>分类设置</span>
            </router-link>
          </div>
        </template>
        
        <!-- 其他菜单的子菜单可以在这里添加 -->
        <template v-else-if="activeMenu === 'users'">
          <router-link to="/users" class="submenu-item">
            <span>用户列表</span>
          </router-link>
          <router-link to="/users/groups" class="submenu-item">
            <span>用户分组</span>
          </router-link>
          <router-link to="/users/permissions" class="submenu-item">
            <span>权限管理</span>
          </router-link>
        </template>
        
        <template v-else-if="activeMenu === 'orders'">
          <router-link to="/orders" class="submenu-item">
            <span>订单列表</span>
          </router-link>
          <router-link to="/orders/pending" class="submenu-item">
            <span>待处理订单</span>
          </router-link>
          <router-link to="/orders/completed" class="submenu-item">
            <span>已完成订单</span>
          </router-link>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  Monitor, ShoppingCart, User, Document, 
  Van, House, Coin, 
  PieChart, Grid, Connection, Setting,
  Folder
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const activeMenu = ref('dashboard')

// 有子菜单的菜单项
const menusWithSubMenu = ['products', 'users', 'orders']

const hasSubMenu = computed(() => {
  return menusWithSubMenu.includes(activeMenu.value)
})

const currentMenuIcon = computed(() => {
  const iconMap: Record<string, any> = {
    products: ShoppingCart,
    users: User,
    orders: Document,
    dashboard: Monitor,
    purchase: Van,
    warehouse: House,
    sorting: Grid,
    delivery: Van,
    finance: Coin,
    reports: PieChart,
    apps: Grid,
    channels: Connection,
    hardware: Monitor,
    settings: Setting
  }
  return iconMap[activeMenu.value] || Monitor
})

const currentMenuTitle = computed(() => {
  const titleMap: Record<string, string> = {
    products: '商品',
    users: '用户',
    orders: '订单',
    dashboard: '概况',
    purchase: '采购',
    warehouse: '库房',
    sorting: '分拣',
    delivery: '配送',
    finance: '财务',
    reports: '报表',
    apps: '应用',
    channels: '渠道',
    hardware: '硬件',
    settings: '设置'
  }
  return titleMap[activeMenu.value] || '菜单'
})

// 监听路由变化，更新activeMenu
watch(() => route.path, (newPath) => {
  if (newPath.startsWith('/products')) {
    activeMenu.value = 'products'
  } else if (newPath.startsWith('/users')) {
    activeMenu.value = 'users'
  } else if (newPath.startsWith('/orders')) {
    activeMenu.value = 'orders'
  } else if (newPath === '/dashboard') {
    activeMenu.value = 'dashboard'
  } else {
    // 其他路由的处理
    const pathSegments = newPath.split('/').filter(Boolean)
    if (pathSegments.length > 0) {
      activeMenu.value = pathSegments[0]
    }
  }
}, { immediate: true })

const setActiveMenu = (menuKey: string) => {
  if (menusWithSubMenu.includes(menuKey)) {
    // 有子菜单的项目：只切换activeMenu，显示对应的子菜单，不跳转路由
    activeMenu.value = menuKey
  } else {
    // 没有子菜单的项目：直接跳转路由
    router.push(`/${menuKey}`)
  }
}
</script>

<style scoped>
.sidebar {
  display: flex;
  height: 100vh;
  background-color: #2c3e50;
}

/* 左侧一级菜单 */
.primary-menu {
  width: 150px;
  background-color: #2c3e50;
  border-right: 1px solid #34495e;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #34495e;
  color: white;
  font-size: 14px;
  font-weight: bold;
  gap: 8px;
}

.logo-icon {
  font-size: 20px;
}

.logo-text {
  font-size: 10px;
}

.menu-items {
  padding: 10px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 45px;
  color: #bfcbd9;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
  gap: 12px;
  margin: 2px 8px;
  border-radius: 6px;
  padding: 0 15px;
}

.menu-item:hover {
  background-color: #34495e;
  color: white;
}

.menu-item.active {
  background-color: #28a745;
  color: white;
}

.menu-item .el-icon {
  font-size: 18px;
  flex-shrink: 0;
}

/* 右侧二级菜单 */
.secondary-menu {
  width: 120px;
  background-color: white;
  border-right: 1px solid #e4e7ed;
}

.submenu-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  color: #333333;
  font-weight: bold;
  gap: 8px;
  font-size: 14px;
  border-bottom: 1px solid #e4e7ed;
}

.submenu-content {
  padding: 8px 0;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.submenu-item {
  display: block;
  padding: 8px 12px;
  color: #333333;
  text-decoration: none;
  font-size: 12px;
  transition: all 0.3s;
  border-left: 3px solid transparent;
}

.submenu-item:hover {
  background-color: #28a745;
  color: white;
  border-left-color: #28a745;
}

.submenu-item.router-link-active {
  background-color: #28a745;
  color: white;
  border-left-color: #28a745;
  font-weight: 500;
}

/* 子菜单分组 */
.submenu-group {
  margin: 10px 0;
}

.submenu-group-title {
  padding: 6px 12px;
  background-color: #f8f9fa;
  color: #6c757d;
  font-size: 11px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.submenu-sub-item {
  padding-left: 20px;
  font-size: 11px;
}

.submenu-sub-item:hover {
  background-color: #28a745;
  color: white;
}
</style> 