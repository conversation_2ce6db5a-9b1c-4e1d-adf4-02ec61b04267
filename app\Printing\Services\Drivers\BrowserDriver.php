<?php

namespace App\Printing\Services\Drivers;

use App\Printing\Contracts\PrintDriverInterface;
use Illuminate\Support\Facades\Log;

class BrowserDriver implements PrintDriverInterface
{
    protected array $config = [];

    public function initialize(array $config = []): void
    {
        $this->config = array_merge([
            'auto_close' => true,
            'page_size' => 'A4',
            'orientation' => 'portrait',
            'debug' => env('PRINTING_DEBUG', false)
        ], $config);

        if ($this->config['debug']) {
            Log::info('Browser Driver initialized', $this->config);
        }
    }

    public function printText(string $content, array $options = []): bool
    {
        // 浏览器打印通过前端JavaScript实现
        return true;
    }

    public function printHtml(string $html, array $options = []): bool
    {
        // 浏览器打印通过前端JavaScript实现
        return true;
    }

    public function getPrinters(): array
    {
        // 浏览器打印无法获取打印机列表，由用户在打印对话框中选择
        return [];
    }

    public function getPrinterStatus(string $printerName): array
    {
        return ['status' => 'unknown', 'message' => 'Browser printing does not support printer status check'];
    }

    public function setDefaultPrinter(string $printerName): bool
    {
        // 浏览器打印无法设置默认打印机
        return false;
    }

    public function preview(string $content, array $options = []): string
    {
        return $this->prepareHtmlContent($content);
    }

    public function generatePrintScript(string $content, array $options = []): string
    {
        $options = array_merge([
            'auto_close' => $this->config['auto_close'],
            'page_size' => $this->config['page_size'],
            'orientation' => $this->config['orientation']
        ], $options);

        $escapedContent = $this->escapePrintContent($content);
        $autoClose = $options['auto_close'] ? 'true' : 'false';

        $script = "
function doPrint() {
    try {
        // 创建新窗口用于打印
        var printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
        
        if (!printWindow) {
            alert('打印失败：浏览器阻止了弹出窗口，请允许弹出窗口后重试');
            return false;
        }

        // 写入打印内容
        printWindow.document.write(`
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>打印预览</title>
    <style>
        @media print {
            body { margin: 0; padding: 20px; }
            .no-print { display: none !important; }
        }
        @media screen {
            body { margin: 20px; font-family: Arial, sans-serif; }
            .print-button { 
                position: fixed; 
                top: 10px; 
                right: 10px; 
                z-index: 1000;
                padding: 10px 20px;
                background: #007cba;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            .print-button:hover { background: #005a87; }
        }
        
        /* 打印样式 */
        .receipt {
            max-width: 800px;
            margin: 0 auto;
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .store-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .store-info {
            font-size: 16px;
            color: #666;
        }
        
        .divider {
            border-top: 1px solid #ddd;
            margin: 15px 0;
        }
        
        .info {
            margin-bottom: 15px;
        }
        
        .info-item {
            margin-bottom: 5px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        
        .total {
            text-align: right;
            margin-bottom: 15px;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <button class='print-button no-print' onclick='window.print()'>打印</button>
    {$escapedContent}
    
    <script>
        // 自动打印
        window.onload = function() {
            setTimeout(function() {
                window.print();
                " . ($autoClose ? "
                // 打印后自动关闭窗口
                setTimeout(function() {
                    window.close();
                }, 1000);
                " : "") . "
            }, 500);
        };
        
        // 监听打印事件
        window.onbeforeprint = function() {
            console.log('开始打印...');
        };
        
        window.onafterprint = function() {
            console.log('打印完成');
            " . ($autoClose ? "
            setTimeout(function() {
                window.close();
            }, 500);
            " : "") . "
        };
    </script>
</body>
</html>
        `);
        
        printWindow.document.close();
        printWindow.focus();
        
        return true;
    } catch (error) {
        console.error('浏览器打印失败:', error);
        alert('浏览器打印失败: ' + error.message);
        return false;
    }
}

function doPreview() {
    try {
        // 创建预览窗口
        var previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
        
        if (!previewWindow) {
            alert('预览失败：浏览器阻止了弹出窗口，请允许弹出窗口后重试');
            return false;
        }

        previewWindow.document.write(`
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>打印预览</title>
    <style>
        body { margin: 20px; font-family: Arial, sans-serif; }
        .preview-toolbar { 
            position: fixed; 
            top: 10px; 
            right: 10px; 
            z-index: 1000;
            background: white;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .preview-toolbar button {
            margin-left: 10px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .print-btn { background: #007cba; color: white; }
        .print-btn:hover { background: #005a87; }
        .close-btn { background: #666; color: white; }
        .close-btn:hover { background: #444; }
        
        /* 打印样式 */
        .receipt {
            max-width: 800px;
            margin: 0 auto;
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .store-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .store-info {
            font-size: 16px;
            color: #666;
        }
        
        .divider {
            border-top: 1px solid #ddd;
            margin: 15px 0;
        }
        
        .info {
            margin-bottom: 15px;
        }
        
        .info-item {
            margin-bottom: 5px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        
        .total {
            text-align: right;
            margin-bottom: 15px;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class='preview-toolbar'>
        <button class='print-btn' onclick='window.print()'>打印</button>
        <button class='close-btn' onclick='window.close()'>关闭</button>
    </div>
    
    {$escapedContent}
</body>
</html>
        `);
        
        previewWindow.document.close();
        previewWindow.focus();
        
        return true;
    } catch (error) {
        console.error('预览失败:', error);
        alert('预览失败: ' + error.message);
        return false;
    }
}
";

        return $script;
    }

    public function generateReceiptScript(string $content, array $options = []): string
    {
        // 小票打印使用更小的窗口和特殊样式
        $options = array_merge([
            'auto_close' => $this->config['auto_close'],
            'width' => 80 // 小票宽度（毫米）
        ], $options);

        $escapedContent = $this->escapePrintContent($content);
        $autoClose = $options['auto_close'] ? 'true' : 'false';

        $script = "
function doPrintReceipt() {
    try {
        var printWindow = window.open('', '_blank', 'width=400,height=600,scrollbars=yes,resizable=yes');
        
        if (!printWindow) {
            alert('打印失败：浏览器阻止了弹出窗口，请允许弹出窗口后重试');
            return false;
        }

        printWindow.document.write(`
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>小票打印</title>
    <style>
        @media print {
            body { margin: 0; padding: 5px; }
            .no-print { display: none !important; }
            @page { size: 80mm auto; margin: 0; }
        }
        @media screen {
            body { margin: 10px; font-family: Arial, sans-serif; }
            .print-button { 
                position: fixed; 
                top: 10px; 
                right: 10px; 
                z-index: 1000;
                padding: 8px 16px;
                background: #007cba;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            }
        }
        
        /* 小票样式 */
        .receipt {
            width: 80mm;
            margin: 0 auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.2;
        }
        
        .header {
            text-align: center;
            margin-bottom: 10px;
        }
        
        .store-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .store-info {
            font-size: 12px;
        }
        
        .divider {
            border-top: 1px dashed #333;
            margin: 8px 0;
        }
        
        .info {
            margin-bottom: 8px;
        }
        
        .info-item {
            margin-bottom: 2px;
            font-size: 11px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
            font-size: 11px;
        }
        
        th, td {
            padding: 2px;
            text-align: left;
        }
        
        .total {
            text-align: right;
            margin-bottom: 8px;
            font-size: 12px;
        }
        
        .footer {
            text-align: center;
            margin-top: 10px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <button class='print-button no-print' onclick='window.print()'>打印小票</button>
    {$escapedContent}
    
    <script>
        window.onload = function() {
            setTimeout(function() {
                window.print();
                " . ($autoClose ? "
                setTimeout(function() {
                    window.close();
                }, 1000);
                " : "") . "
            }, 500);
        };
        
        window.onafterprint = function() {
            " . ($autoClose ? "
            setTimeout(function() {
                window.close();
            }, 500);
            " : "") . "
        };
    </script>
</body>
</html>
        `);
        
        printWindow.document.close();
        printWindow.focus();
        
        return true;
    } catch (error) {
        console.error('小票打印失败:', error);
        alert('小票打印失败: ' + error.message);
        return false;
    }
}
";

        return $script;
    }

    /**
     * 转义打印内容中的特殊字符
     */
    protected function escapePrintContent(string $content): string
    {
        // 转义反引号和反斜杠
        $content = str_replace('\\', '\\\\', $content);
        $content = str_replace('`', '\\`', $content);
        $content = str_replace('${', '\\${', $content);
        
        return $content;
    }

    /**
     * 准备HTML内容
     */
    protected function prepareHtmlContent(string $html): string
    {
        // 确保HTML格式正确
        if (strpos($html, '<html>') === false) {
            $html = "
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>打印内容</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .receipt { max-width: 800px; margin: 0 auto; }
    </style>
</head>
<body>
    {$html}
</body>
</html>";
        }
        
        return $html;
    }
} 