<?php

namespace App\Order\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Order\Models\Order;
use App\Order\Models\OrderItem;
use App\Order\Models\OrderCorrection;
use App\Product\Models\Product;
use App\Delivery\Models\Delivery;
use App\Models\User;
use App\Order\Services\OrderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderController extends Controller
{
    /**
     * 订单服务
     */
    protected $orderService;
    
    /**
     * 构造函数
     */
    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }
    
    /**
     * 获取订单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            Log::info('开始获取订单列表', [
                'user_id' => $request->user()?->id,
                'filters' => $request->only(['status', 'keyword', 'start_date', 'end_date', 'date_range', 'sort']),
                'per_page' => $request->input('per_page', 20)
            ]);
            
            $filters = [
                'status' => $request->input('status'),
                'keyword' => $request->input('keyword'),
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
                'date_range' => $request->input('date_range'),
                'sort' => $request->input('sort'),
                // 新增的筛选参数
                'delivery_date' => $request->input('delivery_date'),
                'correction_status' => $request->input('correction_status'),
                'correction_type' => $request->input('correction_type'),
                'correctable' => $request->input('correctable'),
                'exclude_cancelled' => $request->input('exclude_cancelled'),
                // 合并订单相关筛选参数
                'show_merged_orders' => $request->input('show_merged_orders'),
                'only_mergeable' => $request->input('only_mergeable'),
                // 前端请求的特殊参数
                'with_corrections' => $request->input('with_corrections'),
                'with_payment_links' => $request->input('with_payment_links'),
                'calculate_correction_status' => $request->input('calculate_correction_status'),
            ];
            
            $orders = $this->orderService->getOrders(
                $filters,
                $request->user(),
                $request->input('per_page', 20)
            );
            
            // 处理合并订单的原订单信息
            $orders->getCollection()->transform(function ($order) {
                if ($order->is_merged && $order->orderMerge) {
                    // 获取原订单信息
                    $originalOrderIds = $order->orderMerge->original_order_ids;
                    if (!empty($originalOrderIds)) {
                        $originalOrders = \App\Order\Models\Order::whereIn('id', $originalOrderIds)
                            ->select('id', 'order_no')
                            ->get();
                        
                        // 添加到 orderMerge 关联中
                        $order->orderMerge->setRelation('original_orders', $originalOrders);
                    }
                }
                return $order;
            });
            
            Log::info('订单列表获取成功', [
                'total' => $orders->total(),
                'current_page' => $orders->currentPage(),
                'per_page' => $orders->perPage()
            ]);
            
            return response()->json(ApiResponse::success($orders));
        } catch (\Illuminate\Database\QueryException $e) {
            // 数据库查询异常的特殊处理
            Log::error('订单列表数据库查询失败', [
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'sql' => $e->getSql() ?? 'N/A',
                'bindings' => $e->getBindings() ?? [],
                'user_id' => $request->user()?->id,
                'filters' => $request->only(['status', 'keyword', 'start_date', 'end_date', 'date_range', 'sort'])
            ]);
            
            // 检查是否是连接问题
            if (strpos($e->getMessage(), 'mysql_native_password') !== false || 
                strpos($e->getMessage(), 'Connection refused') !== false ||
                strpos($e->getMessage(), 'server has gone away') !== false) {
                return response()->json(ApiResponse::error('数据库连接异常，请稍后重试', 503), 503);
            }
            
            return response()->json(ApiResponse::error('获取订单列表失败: 数据库查询错误', 500), 500);
        } catch (\Exception $e) {
            Log::error('订单列表获取失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user()?->id,
                'filters' => $request->only(['status', 'keyword', 'start_date', 'end_date', 'date_range', 'sort'])
            ]);
            
            return response()->json(ApiResponse::error('获取订单列表失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 创建订单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|string|in:wechat,alipay,bank,cash,cod', // 修复：使用数据库支持的支付方式
            'user_address_id' => 'required|integer|exists:user_addresses,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|integer|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:500',
            'region_id' => 'nullable|integer|exists:regions,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
        }
        
        try {
            // 订单数据
            $orderData = [
                'payment_method' => $request->input('payment_method'),
                'user_address_id' => $request->input('user_address_id'),
                'items' => $request->input('items'),
                'notes' => $request->input('notes'),
                'region_id' => $request->input('region_id'),
            ];
            
            // 创建订单
            $order = $this->orderService->createOrder($orderData, $request->user());
            
            return response()->json(ApiResponse::success($order, '订单创建成功', 200), 201);
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('创建订单失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 创建订单（别名方法，用于API调用）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        return $this->create($request);
    }
    
    /**
     * 查看订单详情
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        try {
            // 查找订单，预加载必要关联，包括配送信息
            $order = Order::with([
                'items.product',
                'user',
                'delivery.deliverer.employee'  // 加载配送信息和配送员信息
            ])->findOrFail($id);
            
            // 检查权限
            $user = $request->user();
            
            // 仅当用户对象存在时检查权限
            if ($user) {
                // 获取用户角色，如果没有role属性，则视为客户
                $userRole = $user->role ?? 'customer';
                
                // 如果是普通客户，只能查看自己的订单
                if ($userRole === 'customer' && $order->user_id !== $user->id) {
                    return response()->json(ApiResponse::error('没有权限查看此订单', 403), 403);
                }
            }
            
            // 预处理订单数据
            $orderData = $order->toArray();
            
            // 格式化金额字段（避免前端显示问题）
            $orderData['total'] = (float)$order->total;
            $orderData['subtotal'] = (float)($order->subtotal ?? $order->total);
            $orderData['discount'] = (float)($order->discount ?? 0);
            $orderData['original_total'] = (float)($order->original_total ?? $order->total);
            
            // 添加完整的优惠信息
            $orderData['discount_info'] = $order->getDiscountInfo();
            $orderData['payment_discount_info'] = $order->getPaymentDiscountInfo();
            
            // 添加优惠明细（用于管理后台显示）
            $orderData['discount_details'] = $this->formatDiscountDetails($order);
            
            // 添加状态文本
            $orderData['status_text'] = $order->getStatusTextAttribute();
            
            // 如果是货到付款订单，添加相关信息
            if ($order->isCashOnDelivery()) {
                $orderData['is_cod'] = true;
                $orderData['cod_status_text'] = $order->getCodStatusTextAttribute();
            }
            
            // 如果有配送信息，添加配送相关文本
            if ($order->delivery_method) {
                $orderData['delivery_method_text'] = $order->getDeliveryMethodTextAttribute();
                $orderData['delivery_time_text'] = $order->getDeliveryTimeTextAttribute();
            }
            
            // 添加配送员信息（如果有配送记录）
            if ($order->delivery && $order->delivery->deliverer && $order->delivery->deliverer->employee) {
                $orderData['deliverer_info'] = [
                    'id' => $order->delivery->deliverer->employee->id,
                    'name' => $order->delivery->deliverer->employee->name,
                    'phone' => $order->delivery->deliverer->employee->phone,
                    'delivery_status' => $order->delivery->status,
                ];
            }
            
            return response()->json(ApiResponse::success($orderData));
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(ApiResponse::error('订单不存在', 404), 404);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取订单详情失败', [
                'order_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(ApiResponse::error('获取订单详情失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 格式化优惠明细（用于管理后台显示）
     *
     * @param Order $order
     * @return array
     */
    private function formatDiscountDetails(Order $order): array
    {
        $details = [
            'has_discount' => false,
            'original_total' => (float)($order->original_total ?? $order->total),
            'final_total' => (float)$order->total,
            'total_discount_amount' => 0,
            'discount_breakdown' => [],
            'discount_summary' => []
        ];
        
        // 计算总优惠金额
        $totalDiscount = ($order->discount ?? 0) + ($order->payment_discount ?? 0);
        if ($order->original_total && $order->original_total > $order->total) {
            $totalDiscount = $order->original_total - $order->total;
        }
        
        $details['total_discount_amount'] = (float)$totalDiscount;
        $details['has_discount'] = $totalDiscount > 0;
        
        // 解析价格计算详情
        if ($order->pricing_info && is_array($order->pricing_info)) {
            foreach ($order->pricing_info as $index => $detail) {
                $discountType = $detail['type'] ?? 'unknown';
                $discountName = $detail['name'] ?? '未知优惠';
                $discountAmount = (float)($detail['discount_amount'] ?? 0);
                
                if ($discountAmount > 0) {
                    $formattedDetail = [
                        'id' => $index + 1,
                        'type' => $discountType,
                        'name' => $discountName,
                        'amount' => $discountAmount,
                        'description' => $this->getDiscountDescription($detail),
                        'color' => $this->getDiscountColor($discountType),
                        'icon' => $this->getDiscountIcon($discountType)
                    ];
                    
                    $details['discount_breakdown'][] = $formattedDetail;
                    
                    // 添加到汇总中
                    $categoryKey = $this->getDiscountCategory($discountType);
                    if (!isset($details['discount_summary'][$categoryKey])) {
                        $details['discount_summary'][$categoryKey] = [
                            'category' => $categoryKey,
                            'name' => $this->getDiscountCategoryName($categoryKey),
                            'total_amount' => 0,
                            'count' => 0,
                            'color' => $this->getDiscountColor($discountType)
                        ];
                    }
                    $details['discount_summary'][$categoryKey]['total_amount'] += $discountAmount;
                    $details['discount_summary'][$categoryKey]['count']++;
                }
            }
        }
        
        // 添加支付优惠
        if ($order->payment_discount > 0) {
            $paymentDiscountDetail = [
                'id' => count($details['discount_breakdown']) + 1,
                'type' => 'payment_discount',
                'name' => '支付优惠',
                'amount' => (float)$order->payment_discount,
                'description' => $this->getPaymentDiscountDescription($order),
                'color' => 'orange',
                'icon' => 'payment'
            ];
            
            $details['discount_breakdown'][] = $paymentDiscountDetail;
            
            // 添加到汇总
            $details['discount_summary']['payment'] = [
                'category' => 'payment',
                'name' => '支付优惠',
                'total_amount' => (float)$order->payment_discount,
                'count' => 1,
                'color' => 'orange'
            ];
        }
        
        // 转换汇总为数组
        $details['discount_summary'] = array_values($details['discount_summary']);
        
        return $details;
    }
    
    /**
     * 获取优惠描述
     */
    private function getDiscountDescription(array $detail): string
    {
        $type = $detail['type'] ?? '';
        $amount = $detail['discount_amount'] ?? 0;
        
        switch ($type) {
            case 'region_price':
                return "区域价格优惠 ¥{$amount}";
            case 'product_member_discount':
                return "商品会员折扣 ¥{$amount}";
            case 'category_member_discount':
                return "分类会员折扣 ¥{$amount}";
            case 'global_member_discount':
                $levelName = $detail['name'] ?? '会员';
                return "{$levelName} ¥{$amount}";
            case 'category_region_discount':
                return "区域优惠 ¥{$amount}";
            default:
                return "优惠 ¥{$amount}";
        }
    }
    
    /**
     * 获取优惠颜色
     */
    private function getDiscountColor(string $type): string
    {
        $colorMap = [
            'region_price' => 'blue',
            'product_member_discount' => 'gold',
            'category_member_discount' => 'gold',
            'global_member_discount' => 'gold',
            'category_region_discount' => 'green',
            'payment_discount' => 'orange'
        ];
        
        return $colorMap[$type] ?? 'default';
    }
    
    /**
     * 获取优惠图标
     */
    private function getDiscountIcon(string $type): string
    {
        $iconMap = [
            'region_price' => 'location',
            'product_member_discount' => 'crown',
            'category_member_discount' => 'crown',
            'global_member_discount' => 'crown',
            'category_region_discount' => 'tag',
            'payment_discount' => 'payment'
        ];
        
        return $iconMap[$type] ?? 'discount';
    }
    
    /**
     * 获取优惠分类
     */
    private function getDiscountCategory(string $type): string
    {
        if (in_array($type, ['product_member_discount', 'category_member_discount', 'global_member_discount'])) {
            return 'member';
        }
        if (in_array($type, ['region_price', 'category_region_discount'])) {
            return 'region';
        }
        return 'other';
    }
    
    /**
     * 获取优惠分类名称
     */
    private function getDiscountCategoryName(string $category): string
    {
        $nameMap = [
            'member' => '会员优惠',
            'region' => '区域优惠',
            'payment' => '支付优惠',
            'other' => '其他优惠'
        ];
        
        return $nameMap[$category] ?? '未知优惠';
    }
    
    /**
     * 获取支付优惠描述
     */
    private function getPaymentDiscountDescription(Order $order): string
    {
        $paymentMethod = $order->payment_method;
        $amount = $order->payment_discount;
        
        $methodNames = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'bank' => '银行转账',
            'cash' => '现金支付'
        ];
        
        $methodName = $methodNames[$paymentMethod] ?? '支付';
        return "{$methodName}优惠 ¥{$amount}";
    }
    
    /**
     * 更新订单状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,paid,shipped,delivered,cancelled',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $user = $request->user();
        $order = Order::findOrFail($id);
        
        // 检查权限：普通用户只能更新自己的订单，且只有某些状态可以更新
        if ($user->role === 'customer') {
            if ($order->user_id !== $user->id) {
                return response()->json(ApiResponse::error('没有权限更新此订单', 403), 403);
            }
            
            // 普通用户只能将订单从 pending 更新为 paid
            if ($order->status !== 'pending' || $request->status !== 'paid') {
                return response()->json(ApiResponse::error('无法更新到此状态', 400), 400);
            }
        }
        
        // 商户和管理员的权限检查
        if ($user->role === 'merchant' && !in_array($request->status, ['shipped', 'delivered'])) {
            return response()->json(ApiResponse::error('商户只能更新订单为已发货或已送达状态', 400), 400);
        }
        
        DB::beginTransaction();
        try {
            // 更新订单状态
            $oldStatus = $order->status;
            $newStatus = $request->status;
            $order->status = $newStatus;
            
            // 根据状态变更设置相应的时间戳
            if ($oldStatus !== $newStatus) {
                switch ($newStatus) {
                    case 'paid':
                        $order->paid_at = now();
                        break;
                    case 'shipped':
                        $order->shipped_at = now();
                        break;
                    case 'delivered':
                        $order->delivered_at = now();
                        break;
                    case 'cancelled':
                        $order->cancelled_at = now();
                        // 如果取消订单，恢复库存
                        if ($oldStatus !== 'delivered') {
                            foreach ($order->items as $item) {
                                $product = $item->product;
                                if ($product) {
                                    $product->addStock($item->quantity);
                                }
                            }
                        }
                        break;
                }
            }
            
            $order->save();
            
            // 如果订单状态变为已付款，创建配送记录
            if ($oldStatus !== 'paid' && $newStatus === 'paid') {
                // 检查是否已存在配送记录
                $existingDelivery = Delivery::where('order_id', $order->id)->first();
                if (!$existingDelivery) {
                    // 获取用户的默认配送员
                    $user = User::find($order->user_id);
                    $delivererId = null;
                    
                    // 如果用户设置了默认配送员，则自动分配
                    if ($user && $user->default_employee_deliverer_id) {
                        // 查找对应的deliverer记录
                        $deliverer = \App\Delivery\Models\Deliverer::where('employee_id', $user->default_employee_deliverer_id)->first();
                        if ($deliverer) {
                            $delivererId = $deliverer->id;
                            Log::info('订单自动分配给默认配送员', [
                                'order_id' => $order->id,
                                'order_no' => $order->order_no,
                                'user_id' => $user->id,
                                'employee_id' => $user->default_employee_deliverer_id,
                                'deliverer_id' => $delivererId
                            ]);
                        }
                    }
                    
                    Delivery::create([
                        'order_id' => $order->id,
                        'status' => 'pending', // 待分配配送员
                        'deliverer_id' => $delivererId, // 自动分配用户的默认配送员
                    ]);
                }
            }
            
            DB::commit();
            return response()->json(ApiResponse::success($order, '订单状态更新成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('更新订单状态失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 取消订单
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel(Request $request, $id)
    {
        $user = $request->user();
        $order = Order::with('items.product')->findOrFail($id);
        
        // 普通用户只能取消自己的订单
        if ($user->role === 'customer' && $order->user_id !== $user->id) {
            return response()->json(ApiResponse::error('没有权限取消此订单', 403), 403);
        }
        
        // 只有待付款或已付款的订单可以取消
        if (!in_array($order->status, ['pending', 'paid'])) {
            return response()->json(ApiResponse::error('只有待付款或已付款的订单可以取消', 400), 400);
        }
        
        DB::beginTransaction();
        try {
            $order->status = 'cancelled';
            $order->cancelled_at = now();
            $order->save();
            
            // 恢复商品库存
            foreach ($order->items as $item) {
                $product = $item->product;
                if ($product) {
                    $product->addStock($item->quantity);
                }
            }
            
            DB::commit();
            return response()->json(ApiResponse::success(null, '订单已取消'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('取消订单失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 代客下单 - 管理员或CRM专员为客户创建订单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createForClient(Request $request)
    {
        // 获取当前登录的员工（通过employee.role中间件已经验证过权限）
        $currentEmployee = $request->user();
        
        // 添加详细的请求数据日志
        Log::info('代客下单请求开始', [
            'employee_id' => $currentEmployee->id ?? null,
            'employee_name' => $currentEmployee->name ?? null,
            'request_method' => $request->method(),
            'request_url' => $request->fullUrl(),
            'request_headers' => $request->headers->all(),
            'request_data' => $request->all(),
            'content_type' => $request->header('Content-Type'),
            'request_size' => strlen($request->getContent())
        ]);
        
        // 添加权限检查日志
        Log::info('代客下单权限检查', [
            'employee_id' => $currentEmployee->id ?? null,
            'employee_name' => $currentEmployee->name ?? null,
            'employee_class' => get_class($currentEmployee),
            'employee_role' => $currentEmployee->role ?? null,
            'is_employee_instance' => $currentEmployee instanceof \App\Employee\Models\Employee,
            'request_data_keys' => array_keys($request->all()),
            'request_client_id' => $request->input('client_id'),
            'request_items_count' => count($request->input('items', [])),
            'middleware_passed' => true, // 如果到这里说明中间件已经通过
            'auth_guard' => $request->user() ? 'sanctum' : 'none',
            'bearer_token_exists' => $request->bearerToken() ? true : false
        ]);
        
        // 验证请求参数
        $validator = Validator::make($request->all(), [
            'client_id' => 'required|exists:users,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.price' => 'nullable|numeric|min:0', // 允许指定价格（如折扣价）
            'items.*.unit' => 'nullable|string', // 允许指定商品单位
            'user_address_id' => 'nullable|exists:user_addresses,id',
            'shipping_address' => 'required|string',
            'contact_name' => 'required|string',
            'contact_phone' => 'required|string',
            'payment_method' => 'required|in:wechat,alipay,bank,cash,cod',
            'delivery_method' => 'nullable|string',
            'delivery_date' => 'nullable|date',
            'delivery_time' => 'nullable|string',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'notes' => 'nullable|string',
            'region_id' => 'nullable|integer', // 添加区域ID验证
        ]);
        
        // 添加验证结果日志
        Log::info('代客下单验证结果', [
            'validation_passed' => !$validator->fails(),
            'validation_errors' => $validator->fails() ? $validator->errors()->toArray() : null,
            'employee_id' => $currentEmployee->id ?? null
        ]);
        
        if ($validator->fails()) {
            // 添加详细的验证错误日志
            Log::error('代客下单验证失败', [
                'request_data' => $request->all(),
                'validation_errors' => $validator->errors()->toArray(),
                'employee_id' => $currentEmployee->id ?? null,
                'first_error' => $validator->errors()->first()
            ]);
            return response()->json(ApiResponse::error($validator->errors()->first(), 422, $validator->errors()->toArray()), 422);
        }
        
        // 查找客户
        $client = User::findOrFail($request->client_id);
        
        // CRM专员权限检查：只能为分配给自己的客户代下单
        if ($currentEmployee->role === 'crm_agent') {
            if ($client->crm_agent_id !== $currentEmployee->id) {
                return response()->json(ApiResponse::error('您只能为分配给您的客户代下单', 403), 403);
            }
        }
        
        DB::beginTransaction();
        try {
            // 计算订单总金额并验证商品库存
            $subtotal = 0;
            $items = [];
            
            Log::info('开始处理订单商品', [
                'items_count' => count($request->items),
                'employee_id' => $currentEmployee->id
            ]);
            
            foreach ($request->items as $index => $item) {
                Log::info("处理商品 #{$index}", [
                    'item_data' => $item,
                    'employee_id' => $currentEmployee->id
                ]);
                
                $product = Product::findOrFail($item['product_id']);
                
                Log::info("商品信息", [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_stock' => $product->stock ?? 'N/A',
                    'requested_quantity' => $item['quantity'],
                    'inventory_policy' => $product->inventory_policy ?? 'strict',
                    'track_inventory' => $product->track_inventory ?? true,
                    'employee_id' => $currentEmployee->id
                ]);
                
                // 使用新的库存策略检查
                $stockCheckResult = $product->checkStockWithPolicy($item['quantity']);
                Log::info("库存策略检查结果", [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'requested_quantity' => $item['quantity'],
                    'check_result' => $stockCheckResult,
                    'employee_id' => $currentEmployee->id
                ]);
                
                if (!$stockCheckResult['allowed']) {
                    Log::error("库存策略检查失败", [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'requested_quantity' => $item['quantity'],
                        'current_stock' => $product->stock ?? 'N/A',
                        'check_message' => $stockCheckResult['message'],
                        'employee_id' => $currentEmployee->id
                    ]);
                    return response()->json(ApiResponse::error($stockCheckResult['message'], 422), 422);
                }
                
                // 如果有警告，记录到日志
                if ($stockCheckResult['warning']) {
                    Log::warning("库存策略警告", [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'warning' => $stockCheckResult['warning'],
                        'employee_id' => $currentEmployee->id
                    ]);
                }
                
                // 使用指定价格或商品默认价格
                $price = isset($item['price']) ? $item['price'] : $product->price;
                $itemTotal = $price * $item['quantity'];
                $subtotal += $itemTotal;
                
                $items[] = [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku,
                    'quantity' => $item['quantity'],
                    'price' => $price,
                    'unit' => $item['unit'] ?? $product->getSaleDefaultUnit()?->name ?? '',
                    'total' => $itemTotal,
                ];
                
                Log::info("商品处理完成", [
                    'product_id' => $product->id,
                    'item_total' => $itemTotal,
                    'running_subtotal' => $subtotal,
                    'stock_warning' => $stockCheckResult['warning'],
                    'employee_id' => $currentEmployee->id
                ]);
            }
            
            // 应用折扣（如果有）
            $discount = 0;
            if ($request->has('discount_percentage') && $request->discount_percentage > 0) {
                $discount = $subtotal * ($request->discount_percentage / 100);
            }
            
            $total = $subtotal - $discount;
            
            // 创建订单数据
            $orderData = [
                'user_id' => $client->id,
                'order_no' => Order::generateOrderNo(),
                'subtotal' => $subtotal,
                'discount' => $discount,
                'total' => $total,
                'status' => 'pending',
                'source' => 'proxy', // 代客下单
                'payment_method' => $request->payment_method,
                'delivery_method' => $request->delivery_method ?? null,
                'delivery_date' => $request->delivery_date ?? null,
                'delivery_time' => $request->delivery_time ?? null,
                'notes' => $request->notes,
                'created_by_id' => $currentEmployee->id, // 记录代下单员工ID
            ];
            
            // 处理地址信息
            if ($request->has('user_address_id')) {
                // 通过用户地址ID关联
                $orderData['user_address_id'] = $request->user_address_id;
                
                // 查找地址并填充其他地址字段（冗余，方便查询）
                $userAddress = \App\Crm\Models\UserAddress::findOrFail($request->user_address_id);
                $orderData['shipping_address'] = $userAddress->getFullAddressAttribute();
                $orderData['contact_name'] = $userAddress->contact_name;
                $orderData['contact_phone'] = $userAddress->contact_phone;
            } else {
                // 直接使用提交的地址信息
                $orderData['shipping_address'] = $request->shipping_address;
                $orderData['contact_name'] = $request->contact_name;
                $orderData['contact_phone'] = $request->contact_phone;
            }
            
            // 创建订单
            $order = Order::create($orderData);
            
            // 记录库存策略使用情况
            $inventoryPolicies = [];
            $stockWarnings = [];
            
            // 如果是货到付款订单，记录特殊标记
            if ($request->payment_method === 'cod') {
                // 添加货到付款特殊标记
                $order->is_cod = true;
                $order->cod_status = 'unpaid'; // 初始状态：未支付
                $order->save();
                
                // 记录货到付款日志
                \Illuminate\Support\Facades\Log::info('货到付款订单创建', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'client_id' => $client->id,
                    'client_name' => $client->name,
                    'total' => $order->total
                ]);
            }
            
            // 创建订单明细
            foreach ($items as $item) {
                // 只使用OrderItem模型中fillable定义的字段
                $orderItemData = [
                    'order_id' => $order->id,
                    'product_id' => $item['product_id'],
                    'product_name' => $item['product_name'],
                    'product_sku' => $item['product_sku'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'unit' => $item['unit'],
                    'total' => $item['total'],
                ];
                
                $order->items()->create($orderItemData);
                
                // 使用新的库存策略减少库存
                $product = Product::find($item['product_id']);
                $reduceResult = $product->reduceStockWithPolicy($item['quantity']);
                
                // 记录库存策略使用情况
                $inventoryPolicies[$product->id] = [
                    'product_name' => $product->name,
                    'policy' => $product->inventory_policy ?? 'strict',
                    'track_inventory' => $product->track_inventory ?? true,
                    'quantity_reduced' => $item['quantity'],
                    'stock_before' => $product->stock + $item['quantity'], // 减少前的库存
                    'stock_after' => $product->stock, // 减少后的库存
                    'warning' => $reduceResult['warning'] ?? null,
                    'success' => $reduceResult['success']
                ];
                
                Log::info('库存减少结果', [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'quantity_reduced' => $item['quantity'],
                    'reduce_result' => $reduceResult,
                    'order_id' => $order->id,
                    'employee_id' => $currentEmployee->id
                ]);
                
                // 如果库存减少失败，记录错误但不回滚（因为前面已经检查过了）
                if (!$reduceResult['success']) {
                    Log::error('库存减少失败', [
                        'product_id' => $product->id,
                        'error_message' => $reduceResult['message'],
                        'order_id' => $order->id,
                        'employee_id' => $currentEmployee->id
                    ]);
                }
                
                // 如果有警告，记录到日志
                if ($reduceResult['warning']) {
                    Log::warning('库存减少警告', [
                        'product_id' => $product->id,
                        'warning' => $reduceResult['warning'],
                        'order_id' => $order->id,
                        'employee_id' => $currentEmployee->id
                    ]);
                }
            }
            
            // 记录订单的库存策略使用情况
            $order->recordInventoryPolicies($inventoryPolicies);
            
            // 如果有负库存且当前员工是管理员，自动批准
            if ($order->has_negative_stock_items && $currentEmployee->role === 'admin') {
                $order->approveNegativeStock($currentEmployee->id);
                Log::info('管理员自动批准负库存', [
                    'order_id' => $order->id,
                    'employee_id' => $currentEmployee->id,
                    'employee_role' => $currentEmployee->role
                ]);
            }
            
            // 添加代客下单日志
            \Illuminate\Support\Facades\Log::info('代客下单', [
                'admin_id' => $currentEmployee->id,
                'admin_name' => $currentEmployee->name,
                'client_id' => $client->id,
                'client_name' => $client->name,
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'total' => $order->total,
                'inventory_summary' => $order->getInventoryPolicyDisplayText(),
                'has_negative_stock' => $order->has_negative_stock_items,
                'negative_stock_approved' => $order->negative_stock_approved
            ]);
            
            DB::commit();
            
            // 返回包含订单明细的订单信息
            $order->load(['items', 'userAddress']);
            return response()->json(ApiResponse::success($order, '代客下单成功', 200), 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('代客下单失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取订单统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats(Request $request)
    {
        try {
            $user = $request->user();
            $query = Order::query();
            
            // 权限控制：根据员工角色限制数据访问范围
            if ($user) {
                // 检查是否是Employee实例（员工系统）
                if ($user instanceof \App\Employee\Models\Employee) {
                    // 员工系统的权限控制
                    switch ($user->role) {
                        case 'admin':
                        case 'manager':
                            // 管理员和经理可以查看所有订单统计
                            break;
                            
                        case 'crm_agent':
                        case 'crm':
                            // CRM专员只能查看分配给自己的客户的订单
                            $query->whereHas('user', function($q) use ($user) {
                                $q->where('crm_agent_id', $user->id);
                            });
                            break;
                            
                        case 'delivery':
                            // 配送员只能查看自己配送的订单
                            $query->whereHas('delivery', function($q) use ($user) {
                                $q->whereHas('deliverer', function($dq) use ($user) {
                                    $dq->where('employee_id', $user->id);
                                });
                            });
                            break;
                            
                        default:
                            // 其他员工角色暂时不允许查看统计
                            return response()->json(ApiResponse::error('没有权限查看订单统计', 403), 403);
                    }
                } else {
                    // 普通用户（客户）只能查看自己的订单统计
                    $query->where('user_id', $user->id);
                }
            } else {
                // 未登录用户不允许访问
                return response()->json(ApiResponse::error('未授权访问', 401), 401);
            }
            
            // 总订单数
            $total = $query->count();
            
            // 待处理订单数（待付款 + 已付款）
            $pending = $query->whereIn('status', ['pending', 'paid'])->count();
            
            // 总金额
            $totalAmount = $query->sum('total');
            
            // 今日订单数
            $today = $query->whereDate('created_at', today())->count();
            
            // 各状态订单数
            $statusStats = $query->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();
            
            // 本月订单趋势（最近7天）
            $weeklyTrend = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = now()->subDays($i);
                $dayQuery = clone $query;
                $count = $dayQuery->whereDate('created_at', $date->toDateString())->count();
                $weeklyTrend[] = [
                    'date' => $date->format('m-d'),
                    'count' => $count
                ];
            }
            
            $stats = [
                'total' => $total,
                'pending' => $pending,
                'total_amount' => $totalAmount,
                'today' => $today,
                'status_stats' => $statusStats,
                'weekly_trend' => $weeklyTrend
            ];
            
            return response()->json(ApiResponse::success($stats));
        } catch (\Exception $e) {
            Log::error('获取订单统计失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user()?->id
            ]);
            
            return response()->json(ApiResponse::error('获取统计数据失败: ' . $e->getMessage(), 500), 500);
        }
    }
} 