<?php

namespace App\Warehouse\Providers;

use Illuminate\Support\ServiceProvider;
use App\Warehouse\Services\InventoryService;
use Illuminate\Support\Facades\Route;

class WarehouseServiceProvider extends ServiceProvider
{
    /**
     * 注册应用服务
     *
     * @return void
     */
    public function register()
    {
        // 绑定仓库服务到容器
        $this->app->bind('warehouse.inventory.service', function ($app) {
            return new InventoryService();
        });
        
        // 直接绑定InventoryService类，以便在控制器等地方可以直接注入
        $this->app->bind(InventoryService::class, function ($app) {
            return new InventoryService();
        });
    }

    /**
     * 引导应用服务
     *
     * @return void
     */
    public function boot()
    {
        // 注册API路由，并添加api前缀
        Route::prefix('api')->group(function () {
            $this->loadRoutesFrom(__DIR__.'/../routes/api.php');
        });
        
        // 加载Web路由
        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');
    }
} 