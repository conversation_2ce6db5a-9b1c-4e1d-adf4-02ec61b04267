<?php

namespace App\Inventory\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Product\Models\Product;
use App\Warehouse\Models\Warehouse;
use App\Supplier\Models\Supplier;
use App\Employee\Models\Employee;
use Illuminate\Support\Facades\Log;

class ReorderRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'reorder_rule_id',
        'product_id',
        'warehouse_id',
        'supplier_id',
        'reorder_no',
        'status',
        'trigger_reason',
        'trigger_data',
        'stock_before',
        'reorder_point',
        'requested_quantity',
        'approved_quantity',
        'actual_quantity',
        'unit_cost',
        'total_cost',
        'estimated_cost',
        'triggered_at',
        'approved_at',
        'ordered_at',
        'expected_delivery_at',
        'received_at',
        'completed_at',
        'triggered_by',
        'approved_by',
        'ordered_by',
        'received_by',
        'purchase_order_no',
        'purchase_order_id',
        'notes',
        'rejection_reason',
        'additional_data',
        'lead_time_actual',
        'cost_variance',
        'on_time_delivery',
    ];

    protected $casts = [
        'trigger_data' => 'array',
        'additional_data' => 'array',
        'triggered_at' => 'datetime',
        'approved_at' => 'datetime',
        'ordered_at' => 'datetime',
        'expected_delivery_at' => 'datetime',
        'received_at' => 'datetime',
        'completed_at' => 'datetime',
        'stock_before' => 'decimal:2',
        'reorder_point' => 'decimal:2',
        'requested_quantity' => 'decimal:2',
        'approved_quantity' => 'decimal:2',
        'actual_quantity' => 'decimal:2',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'estimated_cost' => 'decimal:2',
        'cost_variance' => 'decimal:2',
        'lead_time_actual' => 'integer',
        'on_time_delivery' => 'boolean',
    ];

    /**
     * 关联补货规则
     */
    public function reorderRule()
    {
        return $this->belongsTo(AutoReorderRule::class, 'reorder_rule_id');
    }

    /**
     * 关联商品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 关联仓库
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * 关联供应商
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * 触发人员
     */
    public function triggeredBy()
    {
        return $this->belongsTo(Employee::class, 'triggered_by');
    }

    /**
     * 批准人员
     */
    public function approvedBy()
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }

    /**
     * 下单人员
     */
    public function orderedBy()
    {
        return $this->belongsTo(Employee::class, 'ordered_by');
    }

    /**
     * 收货人员
     */
    public function receivedBy()
    {
        return $this->belongsTo(Employee::class, 'received_by');
    }

    /**
     * 生成补货单号
     * 
     * @return string
     */
    public static function generateReorderNo()
    {
        do {
            $no = 'RO' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (self::where('reorder_no', $no)->exists());
        
        return $no;
    }

    /**
     * 批准补货
     * 
     * @param int $employeeId 批准人员ID
     * @param float|null $approvedQuantity 批准数量
     * @param string|null $notes 备注
     * @return bool
     */
    public function approve($employeeId, $approvedQuantity = null, $notes = null)
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => $employeeId,
            'approved_quantity' => $approvedQuantity ?? $this->requested_quantity,
            'notes' => $notes,
        ]);

        Log::info('补货申请已批准', [
            'record_id' => $this->id,
            'approved_by' => $employeeId,
            'approved_quantity' => $this->approved_quantity
        ]);

        return true;
    }

    /**
     * 拒绝补货
     * 
     * @param int $employeeId 拒绝人员ID
     * @param string $reason 拒绝原因
     * @return bool
     */
    public function reject($employeeId, $reason)
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->update([
            'status' => 'rejected',
            'approved_at' => now(),
            'approved_by' => $employeeId,
            'rejection_reason' => $reason,
        ]);

        Log::info('补货申请已拒绝', [
            'record_id' => $this->id,
            'rejected_by' => $employeeId,
            'reason' => $reason
        ]);

        return true;
    }

    /**
     * 标记为已下单
     * 
     * @param int $employeeId 下单人员ID
     * @param string|null $purchaseOrderNo 采购订单号
     * @param int|null $purchaseOrderId 采购订单ID
     * @return bool
     */
    public function markAsOrdered($employeeId, $purchaseOrderNo = null, $purchaseOrderId = null)
    {
        if ($this->status !== 'approved') {
            return false;
        }

        $this->update([
            'status' => 'ordered',
            'ordered_at' => now(),
            'ordered_by' => $employeeId,
            'purchase_order_no' => $purchaseOrderNo,
            'purchase_order_id' => $purchaseOrderId,
        ]);

        Log::info('补货已下单', [
            'record_id' => $this->id,
            'ordered_by' => $employeeId,
            'purchase_order_no' => $purchaseOrderNo
        ]);

        return true;
    }

    /**
     * 标记为已收货
     * 
     * @param int $employeeId 收货人员ID
     * @param float $actualQuantity 实际收货数量
     * @param float|null $actualCost 实际成本
     * @return bool
     */
    public function markAsReceived($employeeId, $actualQuantity, $actualCost = null)
    {
        if ($this->status !== 'ordered') {
            return false;
        }

        $receivedAt = now();
        $leadTimeActual = $this->ordered_at ? $this->ordered_at->diffInDays($receivedAt) : null;
        $onTimeDelivery = $this->expected_delivery_at ? $receivedAt <= $this->expected_delivery_at : null;
        $costVariance = $actualCost && $this->estimated_cost ? $actualCost - $this->estimated_cost : null;

        $this->update([
            'status' => 'received',
            'received_at' => $receivedAt,
            'received_by' => $employeeId,
            'actual_quantity' => $actualQuantity,
            'total_cost' => $actualCost,
            'lead_time_actual' => $leadTimeActual,
            'cost_variance' => $costVariance,
            'on_time_delivery' => $onTimeDelivery,
        ]);

        // 更新商品库存
        if ($this->product) {
            $this->product->addStock($actualQuantity);
        }

        Log::info('补货已收货', [
            'record_id' => $this->id,
            'received_by' => $employeeId,
            'actual_quantity' => $actualQuantity,
            'on_time' => $onTimeDelivery
        ]);

        return true;
    }

    /**
     * 标记为已完成
     * 
     * @param string|null $notes 完成备注
     * @return bool
     */
    public function markAsCompleted($notes = null)
    {
        if ($this->status !== 'received') {
            return false;
        }

        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'notes' => $notes,
        ]);

        // 更新补货规则成功统计
        if ($this->reorderRule) {
            $this->reorderRule->update([
                'success_count' => $this->reorderRule->success_count + 1,
                'last_success_at' => now(),
            ]);
        }

        Log::info('补货已完成', [
            'record_id' => $this->id,
            'notes' => $notes
        ]);

        return true;
    }

    /**
     * 取消补货
     * 
     * @param int $employeeId 取消人员ID
     * @param string $reason 取消原因
     * @return bool
     */
    public function cancel($employeeId, $reason)
    {
        if (in_array($this->status, ['completed', 'cancelled'])) {
            return false;
        }

        $this->update([
            'status' => 'cancelled',
            'notes' => $reason,
            'updated_at' => now(),
        ]);

        Log::info('补货已取消', [
            'record_id' => $this->id,
            'cancelled_by' => $employeeId,
            'reason' => $reason
        ]);

        return true;
    }

    /**
     * 获取状态颜色
     * 
     * @return string
     */
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'pending' => 'yellow',
            'approved' => 'blue',
            'rejected' => 'red',
            'ordered' => 'purple',
            'received' => 'green',
            'completed' => 'green',
            'cancelled' => 'gray',
            default => 'gray'
        };
    }

    /**
     * 获取状态文本
     * 
     * @return string
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'pending' => '待处理',
            'approved' => '已批准',
            'rejected' => '已拒绝',
            'ordered' => '已下单',
            'received' => '已收货',
            'completed' => '已完成',
            'cancelled' => '已取消',
            default => '未知'
        };
    }

    /**
     * 获取触发原因文本
     * 
     * @return string
     */
    public function getTriggerReasonTextAttribute()
    {
        return match($this->trigger_reason) {
            'low_stock' => '低库存',
            'out_of_stock' => '缺货',
            'sales_velocity' => '销售速度',
            'scheduled' => '定时触发',
            'manual' => '手动触发',
            default => '未知'
        };
    }

    /**
     * 获取进度百分比
     * 
     * @return int
     */
    public function getProgressPercentageAttribute()
    {
        return match($this->status) {
            'pending' => 10,
            'approved' => 25,
            'rejected' => 0,
            'ordered' => 50,
            'received' => 80,
            'completed' => 100,
            'cancelled' => 0,
            default => 0
        };
    }

    /**
     * 检查是否逾期
     * 
     * @return bool
     */
    public function isOverdue()
    {
        if (!$this->expected_delivery_at || in_array($this->status, ['received', 'completed', 'cancelled'])) {
            return false;
        }

        return now() > $this->expected_delivery_at;
    }

    /**
     * 获取逾期天数
     * 
     * @return int|null
     */
    public function getOverdueDays()
    {
        if (!$this->isOverdue()) {
            return null;
        }

        return now()->diffInDays($this->expected_delivery_at);
    }

    /**
     * 作用域：待处理
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * 作用域：已批准
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * 作用域：进行中
     */
    public function scopeInProgress($query)
    {
        return $query->whereIn('status', ['pending', 'approved', 'ordered']);
    }

    /**
     * 作用域：已完成
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * 作用域：逾期
     */
    public function scopeOverdue($query)
    {
        return $query->where('expected_delivery_at', '<', now())
            ->whereNotIn('status', ['received', 'completed', 'cancelled']);
    }
} 