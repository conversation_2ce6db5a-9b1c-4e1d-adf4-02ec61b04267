<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 创建配送路线表
        Schema::create('delivery_routes', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('路线名称');
            $table->text('coverage_area')->comment('覆盖区域');
            $table->string('starting_point')->comment('起始点');
            $table->integer('estimated_delivery_time')->default(30)->comment('预计配送时间(分钟)');
            $table->integer('max_orders')->default(20)->comment('最大订单数量');
            $table->text('description')->nullable()->comment('路线描述');
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('状态');
            $table->timestamps();
        });
        
        // 创建配送路线与配送员关联表
        Schema::create('delivery_route_employee', function (Blueprint $table) {
            $table->id();
            $table->foreignId('route_id')->constrained('delivery_routes')->onDelete('cascade');
            $table->foreignId('employee_id')->constrained('employees')->onDelete('cascade');
            $table->timestamps();
            
            // 唯一索引确保一个配送员在一个路线中只分配一次
            $table->unique(['route_id', 'employee_id']);
        });
        
        // 添加路线ID到配送表
        Schema::table('deliveries', function (Blueprint $table) {
            if (!Schema::hasColumn('deliveries', 'route_id')) {
                $table->foreignId('route_id')->nullable()->after('order_id')->constrained('delivery_routes')->nullOnDelete();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 先移除外键
        Schema::table('deliveries', function (Blueprint $table) {
            if (Schema::hasColumn('deliveries', 'route_id')) {
                $table->dropForeign(['route_id']);
                $table->dropColumn('route_id');
            }
        });
        
        // 删除关联表和主表
        Schema::dropIfExists('delivery_route_employee');
        Schema::dropIfExists('delivery_routes');
    }
}; 