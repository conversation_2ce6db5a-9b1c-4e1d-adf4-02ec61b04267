<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('region_prices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id')->comment('商品ID');
            $table->unsignedBigInteger('region_id')->comment('区域ID');
            $table->decimal('price', 10, 2)->comment('价格');
            $table->decimal('original_price', 10, 2)->nullable()->comment('原价');
            $table->integer('stock')->nullable()->comment('库存');
            $table->tinyInteger('status')->default(1)->comment('状态');
            $table->datetime('start_date')->nullable()->comment('开始日期');
            $table->datetime('end_date')->nullable()->comment('结束日期');
            $table->json('special_conditions')->nullable()->comment('特殊条件');
            
            $table->timestamps();
            
            // 索引
            $table->unique(['product_id', 'region_id'], 'region_prices_product_id_region_id_unique');
            $table->index('product_id', 'region_prices_product_id_index');
            $table->index('region_id', 'region_prices_region_id_index');
            $table->index('status', 'region_prices_status_index');
            $table->index('price', 'region_prices_price_index');
            
            // 外键约束
            $table->foreign('product_id', 'region_prices_product_id_foreign')
                  ->references('id')->on('products')->onDelete('cascade');
            $table->foreign('region_id', 'region_prices_region_id_foreign')
                  ->references('id')->on('regions')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('region_prices');
    }
};
