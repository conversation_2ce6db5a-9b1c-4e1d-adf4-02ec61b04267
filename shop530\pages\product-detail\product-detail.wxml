<!-- 商品详情页 - 1:1还原设计图 -->
<view class="product-detail-page">
  
  <!-- 自定义导航栏 -->
  <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
    <view class="navbar-content">
      <view class="navbar-left" catchtap="onBack">
        <van-icon name="arrow-left" size="20px" color="#333" />
      </view>
      <view class="navbar-title">商品详情</view>
      <view class="navbar-right">
        <view class="navbar-actions">
          <van-icon name="ellipsis" size="4px" color="#333" />
          <van-icon name="minus" size="16px" color="#333" />
          <van-icon name="aim" size="20px" color="#333" />
        </view>
      </view>
    </view>
  </view>
  
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <van-loading size="24px" type="spinner" color="#07c160">加载中...</van-loading>
  </view>

  <!-- 主内容 -->
  <view 
    class="main-content" 
    wx:else
  >
    
    <!-- 商品图片轮播 -->
    <view class="product-images">
      <swiper 
        class="images-swiper"
        indicator-dots="{{false}}"
        autoplay="{{false}}"
        circular="{{images.length > 1}}"
        duration="{{300}}"
        bindchange="onImageSwiper"
        current="{{currentImageIndex}}"
      >
        <swiper-item wx:for="{{images}}" wx:key="index" wx:for-index="index">
          <image 
            src="{{item}}"
            mode="aspectFill"
            class="product-image"
            catch:load="onImageLoad"
            catch:error="onImageError"
            catchtap="onImageTap"
            data-index="{{index}}"
            data-src="{{item}}"
          />
          
          <!-- 图片加载失败占位 -->
          <view class="image-error-placeholder" wx:if="{{item.indexOf('placeholder') !== -1}}">
            <van-icon name="photo-o" size="48px" color="#ccc" />
            <text class="error-text">图片加载失败</text>
          </view>
        </swiper-item>
      </swiper>
      
      <!-- 自定义指示器 -->
      <view class="custom-indicators" wx:if="{{images.length > 1}}">
        <view 
          class="indicator-dot {{currentImageIndex === index ? 'active' : ''}}"
          wx:for="{{images}}" 
          wx:key="index"
          data-index="{{index}}"
          catchtap="onIndicatorTap"
        ></view>
      </view>
      
      <!-- 图片数量显示 -->
      <view class="image-counter" wx:if="{{images.length > 1}}">
        {{currentImageIndex + 1}} / {{images.length}}
      </view>
      
      <!-- 收藏按钮 -->
      <view class="favorite-btn-overlay" catchtap="onToggleFavorite">
        <van-icon 
          name="{{isFavorite ? 'star' : 'star-o'}}" 
          size="20px" 
          color="{{isFavorite ? '#ff4444' : '#333'}}"
        />
        <text class="favorite-text">收藏</text>
      </view>
      
      <!-- 分享按钮 -->
      <view class="share-btn-overlay" catchtap="onShare">
        <van-icon name="share" size="20px" color="#333" />
        <text class="share-text">分享</text>
      </view>
    </view>

    <!-- 商品信息卡片 -->
    <view class="product-info-card">
      
      <!-- 商品标签 -->
      <view class="product-tags" wx:if="{{product.tags && product.tags.length > 0}}">
        <van-tag 
          wx:for="{{product.tags}}" 
          wx:key="index"
          wx:for-item="tag"
          wx:for-index="index"
          size="small"
          round
          custom-class="product-tag {{tag.cssClass || ''}}"
        >
          {{tag.name || tag}}
        </van-tag>
      </view>

      <!-- 商品标题 -->
      <view class="product-title">{{product.name}}</view>

      <!-- 商品副标题 -->
      <view class="product-subtitle" wx:if="{{product.subtitle}}">
        {{product.subtitle}}
      </view>

      

      <!-- 价格信息 -->
      <view class="product-price-section">
        <price-display 
          productId="{{product.id || 0}}"
          price="{{product.price || '0.00'}}"
          unit="{{product.unit || ''}}"
          className="product-detail-price"
          loginText="登录查看价格"
          showLoginIcon="{{false}}"
          showPriceLabels="{{true}}"
          showDiscountInfo="{{true}}"
          bind:priceLoaded="onProductPriceLoaded"
          bind:priceError="onProductPriceError"
        />
        <view class="sales-stock-info">
          <text class="sales-text">销量{{product.sales || 0}}</text>
          <text class="stock-text">库存{{product.stock || 0}}</text>
          <text class="unit-text" wx:if="{{product.unit}}">单位：{{product.unit}}</text>
        </view>
      </view>
    </view>

    <!-- 商品规格信息 -->
    <view class="product-specs-section" wx:if="{{product.specifications && product.specifications.length > 0}}">
      <view class="section-title">商品规格</view>
      <view class="specs-list">
        <view class="spec-item" wx:for="{{product.specifications}}" wx:key="name">
          <text class="spec-name">{{item.name}}：</text>
          <text class="spec-value">{{item.value}}</text>
        </view>
      </view>
    </view>

    <!-- 配送信息 -->
    <view class="delivery-section">
      <view class="section-title">配送信息</view>
      <view class="delivery-item">
        <van-icon name="logistics" size="16px" color="#07c160" />
        <text class="delivery-text">{{deliveryInfo.time}} 送达</text>
      </view>
      <view class="delivery-item">
        <van-icon name="gift-o" size="16px" color="#ff6b35" />
        <text class="delivery-text">{{deliveryInfo.fee}}</text>
      </view>
    </view>

    <!-- 推荐商品 -->
    <view class="recommend-section">
      <view class="section-title">推荐商品 ({{recommendProducts.length}})</view>
      
      <!-- 调试信息 -->
      <view wx:if="{{recommendProducts.length === 0}}" style="padding: 32rpx; text-align: center; color: #999;">
        暂无推荐商品数据
      </view>
      
      <scroll-view class="recommend-scroll" scroll-x wx:if="{{recommendProducts.length > 0}}">
        <view class="recommend-list">
          <view 
            class="recommend-item" 
            wx:for="{{recommendProducts}}" 
            wx:key="id"
            data-product="{{item}}"
            catchtap="onRecommendProductTap"
          >
            <image 
              class="recommend-image" 
              src="{{item.image}}" 
              mode="aspectFill"
              catch:error="onRecommendImageError"
              data-product-id="{{item.id}}"
            />
            
            <!-- 推荐商品图片加载失败占位 -->
            <view class="recommend-image-error" wx:if="{{item.image.indexOf('placeholder') !== -1}}">
              <van-icon name="photo-o" size="24px" color="#ccc" />
            </view>
            
            <!-- 推荐商品标签 -->
            <view class="recommend-tags" wx:if="{{item.tags && item.tags.length > 0}}">
              <van-tag 
                wx:for="{{item.tags}}" 
                wx:key="index"
                wx:for-item="tag"
                wx:for-index="tagIndex"
                size="mini"
                round
                custom-class="recommend-tag {{tag.cssClass || ''}}"
              >
                {{tag.name || tag}}
              </van-tag>
            </view>
            
            <view class="recommend-info">
              <view class="recommend-name">{{item.name}}</view>
              <price-display 
                price="{{item.price || '0.00'}}"
                unit="{{item.unit || ''}}"
                className="recommend-price"
                loginText="登录查看"
                showLoginIcon="{{false}}"
                bind:priceLoaded="onRecommendPriceLoaded"
                bind:priceError="onRecommendPriceError"
              />
            </view>
            
            <!-- 推荐商品加购按钮 -->
            <view 
              class="recommend-add-btn"
              data-product="{{item}}"
              catchtap="onRecommendAddCart"
            >
              <van-icon name="plus" size="16px" color="#fff" />
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 详情标签页 -->
    <view class="detail-tabs">
      <view class="tab-headers">
        <view 
          class="tab-header {{activeTab === 0 ? 'active' : ''}}"
          data-index="0"
          catchtap="onTabChange"
        >
          商品详情
        </view>
        <view 
          class="tab-header {{activeTab === 1 ? 'active' : ''}}"
          data-index="1"
          catchtap="onTabChange"
        >
          购买记录
        </view>
      </view>
      
      <view class="tab-content">
        <!-- 商品详情内容 -->
        <view class="detail-content" wx:if="{{activeTab === 0}}">
          <!-- 商品详细描述 -->
          <view class="product-detail-info" wx:if="{{processedRichContent}}">
            <view class="detail-section">
              <view class="detail-text">
                <!-- 直接使用rich-text组件显示所有内容 -->
                <rich-text 
                  nodes="{{processedRichContent}}"
                  class="rich-content"
                  space="nbsp"
                  selectable="{{false}}"
                />
              </view>
            </view>
            
            <!-- 产品特点 -->
            <view class="detail-section" wx:if="{{product.features && product.features.length > 0}}">
              <view class="detail-section-title">产品特点</view>
              <view class="feature-list">
                <view class="feature-item" wx:for="{{product.features}}" wx:key="index">
                  <view class="feature-dot"></view>
                  <text class="feature-text">{{item}}</text>
                </view>
              </view>
            </view>
            
            <!-- 使用说明 -->
            <view class="detail-section" wx:if="{{product.usage}}">
              <view class="detail-section-title">使用说明</view>
              <view class="detail-text">{{product.usage}}</view>
            </view>
            
            <!-- 保存方式 -->
            <view class="detail-section" wx:if="{{product.storage}}">
              <view class="detail-section-title">保存方式</view>
              <view class="detail-text">{{product.storage}}</view>
            </view>
          </view>
          
          <!-- 无详情时的占位 -->
          <view class="detail-placeholder" wx:else>
            <van-icon name="description" size="48px" color="#ccc" />
            <text class="detail-placeholder-text">暂无详细介绍</text>
            <text class="detail-placeholder-desc">商品详情正在完善中</text>
          </view>
        </view>
        
        <!-- 购买记录内容 -->
        <view class="purchase-content" wx:else>
          <van-empty description="暂无购买记录" />
        </view>
      </view>
    </view>
    
    <!-- 底部占位区域，防止内容被底部操作栏遮挡 -->
    <view style="height: 120rpx;"></view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <!-- 底部导航 -->
    <view class="bottom-nav">
      <view class="nav-item" catchtap="onGoHome">
        <van-icon name="home-o" size="20px" color="#999" />
        <text class="nav-text">首页</text>
      </view>
      <view class="nav-item" catchtap="onContactService">
        <van-icon name="chat-o" size="20px" color="#999" />
        <text class="nav-text">客服</text>
      </view>
      <view class="nav-item cart-item" catchtap="onGoCart">
        <van-icon name="shopping-cart-o" size="20px" color="#999" />
        <view class="cart-badge" wx:if="{{cartCount > 0}}">{{cartCount}}</view>
        <text class="nav-text">购物车</text>
      </view>
    </view>
    
    <!-- 操作按钮区域 -->
    <view class="action-buttons">
      <!-- 数量选择器 -->
      <view class="quantity-selector">
        <view class="quantity-btn" catchtap="onQuantityDecrease">
          <van-icon name="minus" size="14px" color="#666" />
        </view>
        <input class="quantity-input" type="number" value="{{quantity}}" disabled />
        <view class="quantity-btn" catchtap="onQuantityIncrease">
          <van-icon name="plus" size="14px" color="#666" />
        </view>
      </view>
      
      <!-- 加购物车按钮 -->
      <view class="add-cart-btn" catchtap="onAddToCart">
        <van-icon name="shopping-cart-o" size="16px" color="#fff" />
        <text class="add-cart-text">加入购物车</text>
      </view>
    </view>
  </view>

</view> 