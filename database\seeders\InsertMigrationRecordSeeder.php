<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class InsertMigrationRecordSeeder extends Seeder
{
    /**
     * 手动将wechat_sub_merchants迁移标记为已完成
     */
    public function run(): void
    {
        // 获取当前最大批次号
        $maxBatch = DB::table('migrations')->max('batch');
        
        // 检查记录是否已存在
        $exists = DB::table('migrations')
            ->where('migration', '2025_05_13_064534_create_wechat_sub_merchants_table')
            ->exists();
            
        if (!$exists) {
            // 插入迁移记录
            DB::table('migrations')->insert([
                'migration' => '2025_05_13_064534_create_wechat_sub_merchants_table',
                'batch' => $maxBatch + 1,
            ]);
            
            $this->command->info('已成功标记 wechat_sub_merchants 表迁移为已完成');
        } else {
            $this->command->info('wechat_sub_merchants 表迁移记录已存在');
        }
    }
} 