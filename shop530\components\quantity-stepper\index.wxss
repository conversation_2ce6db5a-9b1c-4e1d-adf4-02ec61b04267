/* 自定义数量控制组件样式 */
.quantity-stepper {
  display: flex;
  align-items: center;
  background: transparent;
}

.stepper-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  transition: all 0.2s ease;
  user-select: none;
}

.stepper-btn:active {
  background: #00C853;
  border-color: #00C853;
  transform: scale(0.95);
}

.stepper-btn:active .stepper-icon {
  color: #fff;
}

.stepper-btn.disabled {
  background: #f5f5f5 !important;
  border-color: #f0f0f0 !important;
  opacity: 0.6;
}

.stepper-btn.disabled .stepper-icon {
  color: #ccc !important;
}

.stepper-icon {
  font-size: 32rpx;
  font-weight: 600;
  color: #00C853;
  line-height: 1;
}

.stepper-input {
  width: 120rpx;
  height: 60rpx;
  margin: 0 8rpx;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #1a1a1a;
  text-align: center;
  line-height: 60rpx;
  transition: all 0.2s ease;
}

.stepper-input-focus {
  border-color: #00C853;
  background: #fff;
  box-shadow: 0 0 0 2rpx rgba(0, 200, 83, 0.1);
}

.stepper-input-disabled {
  background: #f5f5f5;
  color: #999;
  border-color: #f0f0f0;
} 