<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 库存跟踪标志
            $table->boolean('track_inventory')->default(true)->comment('是否跟踪库存');
            
            // 库存策略：strict(严格), allow_negative(允许负库存), unlimited(无限库存), inherit(继承仓库设置)
            $table->enum('inventory_policy', ['inherit', 'strict', 'allow_negative', 'unlimited'])
                  ->default('inherit')
                  ->comment('库存管理策略');
            
            // 最小库存预警阈值
            $table->decimal('min_stock_threshold', 10, 2)
                  ->nullable()
                  ->comment('最小库存预警阈值');
            
            // 最大负库存限制（仅当inventory_policy为allow_negative时有效）
            $table->decimal('max_negative_stock', 10, 2)
                  ->nullable()
                  ->comment('最大负库存限制（负数）');
            
            // 库存管理类型
            $table->enum('inventory_type', ['physical', 'virtual', 'service'])
                  ->default('physical')
                  ->comment('库存类型：physical=实物，virtual=虚拟，service=服务');
            
            // 自动补货设置
            $table->boolean('auto_reorder')
                  ->default(false)
                  ->comment('是否启用自动补货');
            
            $table->decimal('reorder_point', 10, 2)
                  ->nullable()
                  ->comment('补货点');
            
            $table->decimal('reorder_quantity', 10, 2)
                  ->nullable()
                  ->comment('补货数量');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'track_inventory',
                'inventory_policy',
                'min_stock_threshold',
                'max_negative_stock',
                'inventory_type',
                'auto_reorder',
                'reorder_point',
                'reorder_quantity'
            ]);
        });
    }
}; 