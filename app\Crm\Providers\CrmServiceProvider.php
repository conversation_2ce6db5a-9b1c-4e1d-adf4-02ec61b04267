<?php

namespace App\Crm\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;

class CrmServiceProvider extends ServiceProvider
{
    /**
     * 注册应用服务
     *
     * @return void
     */
    public function register()
    {
        // 注册会员等级服务
        $this->app->singleton('membership.level.service', function ($app) {
            return new \App\Crm\Services\MembershipLevelService();
        });
        
        // 注册CRM专员服务
        $this->app->singleton('crm.agent.service', function ($app) {
            return new \App\Crm\Services\CrmAgentService();
        });
        
        // 注册客户跟进服务
        $this->app->singleton('client.follow-up.service', function ($app) {
            return new \App\Crm\Services\ClientFollowUpService();
        });
        
        // 注册客户分配服务
        $this->app->singleton('client.assignment.service', function ($app) {
            return new \App\Crm\Services\ClientAssignmentService();
        });
        
        // 注册反馈服务
        $this->app->singleton('feedback.service', function ($app) {
            return new \App\Crm\Services\FeedbackService();
        });
        
        // 注册用户地址服务
        $this->app->singleton('user.address.service', function ($app) {
            return new \App\Crm\Services\UserAddressService();
        });
        
        // 注册行为分析服务
        $this->app->singleton('behavior.analytics.service', function ($app) {
            return new \App\Crm\Services\BehaviorAnalyticsService();
        });
    }

    /**
     * 引导应用服务
     *
     * @return void
     */
    public function boot()
    {
        // 加载API路由 - 添加正确的前缀和中间件
        Route::group([
            'prefix' => 'api/crm',
            'middleware' => ['api', 'auth:sanctum']
        ], function () {
            $this->loadRoutesFrom(__DIR__.'/../routes/api.php');
        });
        
        // 加载Web路由
        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');
    }
} 