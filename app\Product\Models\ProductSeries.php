<?php

namespace App\Product\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductSeries extends Model
{
    use HasFactory;

    /**
     * 表名
     */
    protected $table = 'product_series';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'name',
        'description',
        'image_url',
        'sort',
        'status',
    ];

    /**
     * 类型转换
     */
    protected $casts = [
        'sort' => 'integer',
        'status' => 'boolean',
    ];

    /**
     * 获取该系列下的所有商品
     */
    public function products()
    {
        return $this->hasMany(Product::class, 'series_id');
    }

    /**
     * 获取该系列下的启用商品
     */
    public function activeProducts()
    {
        return $this->hasMany(Product::class, 'series_id')
            ->where('status', 1);
    }

    /**
     * 作用域：仅获取启用的系列
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * 作用域：按排序获取
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 获取系列商品数量
     */
    public function getProductCountAttribute()
    {
        return $this->products()->count();
    }

    /**
     * 获取系列启用商品数量
     */
    public function getActiveProductCountAttribute()
    {
        return $this->activeProducts()->count();
    }
} 