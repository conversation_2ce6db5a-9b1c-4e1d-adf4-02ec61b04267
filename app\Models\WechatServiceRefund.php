<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WechatServiceRefund extends Model
{
    use HasFactory;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'wechat_service_refunds';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'payment_id',
        'order_id',
        'out_refund_no',
        'refund_id',
        'transaction_id',
        'out_trade_no',
        'total_fee',
        'refund_fee',
        'service_refund_fee',
        'refund_status',
        'refund_channel',
        'refund_reason',
        'refund_time',
        'notify_data',
        'error_message',
    ];

    /**
     * 类型转换
     *
     * @var array
     */
    protected $casts = [
        'total_fee' => 'float',
        'refund_fee' => 'float',
        'service_refund_fee' => 'float',
        'refund_time' => 'datetime',
        'notify_data' => 'array',
    ];

    /**
     * 获取该退款记录所属的支付记录
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(WechatServicePayment::class, 'payment_id');
    }

    /**
     * 获取该退款记录关联的订单
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }
} 