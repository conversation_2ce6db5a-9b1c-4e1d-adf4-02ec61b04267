<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            // 先删除外键约束
            $table->dropForeign(['supplier_id']);
            $table->dropForeign(['warehouse_id']);
            
            // 修改字段为可null
            $table->foreignId('supplier_id')->nullable()->change();
            $table->foreignId('warehouse_id')->nullable()->change();
            
            // 重新添加外键约束（但允许null值）
            $table->foreign('supplier_id')->references('id')->on('suppliers')->onDelete('set null');
            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchase_orders', function (Blueprint $table) {
            // 删除可null的外键约束
            $table->dropForeign(['supplier_id']);
            $table->dropForeign(['warehouse_id']);
            
            // 恢复为不可null
            $table->foreignId('supplier_id')->nullable(false)->change();
            $table->foreignId('warehouse_id')->nullable(false)->change();
            
            // 重新添加严格的外键约束
            $table->foreign('supplier_id')->references('id')->on('suppliers');
            $table->foreign('warehouse_id')->references('id')->on('warehouses');
        });
    }
};
