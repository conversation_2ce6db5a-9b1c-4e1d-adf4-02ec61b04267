// API配置
const config = {
	// 开发环境API地址
	baseUrl: 'http://192.168.0.107/api',
	
	// 生产环境API地址（根据实际情况修改）
	// baseUrl: 'https://your-domain.com/api',
	
	// 请求超时时间
	timeout: 10000,
	
	// 存储键名
	storageKeys: {
		token: 'crm_token',
		userInfo: 'crm_user_info',
		employeeInfo: 'crm_employee_info'
	},
	
	// 员工角色映射
	roles: {
		admin: '管理员',
		manager: '经理',
		crm_agent: 'CRM专员',
		staff: '员工',
		delivery: '配送员',
		warehouse_manager: '仓库管理员'
	},
	
	// 订单状态映射
	orderStatus: {
		pending: '待付款',
		paid: '已付款',
		processing: '处理中',
		shipped: '已发货',
		delivered: '已送达',
		cancelled: '已取消',
		refunded: '已退款'
	},
	
	// 支付方式映射
	paymentMethods: {
		wechat: '微信支付',
		alipay: '支付宝',
		bank: '银行转账',
		cash: '现金支付',
		cod: '货到付款'
	}
}

export default config 