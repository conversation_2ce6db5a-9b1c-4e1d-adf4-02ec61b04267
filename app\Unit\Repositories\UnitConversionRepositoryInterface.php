<?php

namespace App\Unit\Repositories;

use App\Core\Repositories\RepositoryInterface;

interface UnitConversionRepositoryInterface extends RepositoryInterface
{
    /**
     * 获取单位的所有转换关系
     *
     * @param int $unitId 单位ID
     * @return mixed
     */
    public function getByUnit(int $unitId);
    
    /**
     * 获取两个单位之间的直接转换关系
     *
     * @param int $fromUnitId 源单位ID
     * @param int $toUnitId 目标单位ID
     * @return mixed
     */
    public function getDirectConversion(int $fromUnitId, int $toUnitId);
    
    /**
     * 批量创建转换关系
     *
     * @param array $edges 转换关系数组
     * @return mixed
     */
    public function createBatch(array $edges);
    
    /**
     * 获取转换路径
     *
     * @param int $fromUnitId 源单位ID
     * @param int $toUnitId 目标单位ID
     * @param string|null $role 角色名称
     * @return array
     */
    public function findConversionPath(int $fromUnitId, int $toUnitId, ?string $role = null): array;
    
    /**
     * 获取单位转换图
     *
     * @param string|null $role 角色名称
     * @return array
     */
    public function getConversionGraph(?string $role = null): array;
    
    /**
     * 检查是否存在循环引用
     *
     * @param int $fromUnitId 源单位ID
     * @param int $toUnitId 目标单位ID
     * @return bool
     */
    public function hasCircularReference(int $fromUnitId, int $toUnitId): bool;
} 