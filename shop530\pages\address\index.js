// pages/address/index.js - 地址选择页面
const api = require('../../utils/api');

Page({
  data: {
    // 地址列表
    addressList: [],
    
    // 当前选中的地址ID
    selectedAddressId: null,
    
    // 来源页面（用于返回时传递数据）
    fromPage: '',
    
    // 加载状态
    loading: true,
    
    // 系统状态栏高度
    statusBarHeight: 44
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 44;
    
    this.setData({
      statusBarHeight: statusBarHeight,
      fromPage: options.from || '',
      selectedAddressId: options.selectedId || null
    });
    
    console.log('地址页面状态栏高度:', statusBarHeight);
    
    // 加载地址列表
    this.loadAddressList();
  },

  /**
   * 页面显示时刷新数据
   */
  onShow() {
    // 从新建地址页面回来时刷新列表
    this.loadAddressList();
  },

  /**
   * 加载地址列表
   */
  async loadAddressList() {
    try {
      wx.showLoading({ title: '加载中...' });
      
      const result = await api.api.getAddressList();
      
      wx.hideLoading();
      
      if (result && result.data) {
        this.setData({
          addressList: result.data,
          hasAddress: result.data.length > 0
        });
      } else {
        this.setData({
          addressList: [],
          hasAddress: false
        });
      }
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '加载地址失败',
        icon: 'none'
      });
      
      this.setData({
        addressList: [],
        hasAddress: false
      });
    }
  },

  /**
   * 选择地址
   */
  async selectAddress(e) {
    const { address } = e.currentTarget.dataset;
    
    if (!address) {
      wx.showToast({
        title: '地址信息错误',
        icon: 'none'
      });
      return;
    }
    
    // 判断是否来自选择页面
    if (this.data.from === 'select') {
      // 返回上一页并传递选中的地址
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      
      // 设置上一页的地址数据
      if (prevPage && prevPage.setData) {
        prevPage.setData({
          selectedAddress: address
        });
        
        // 如果上一页有回调函数，则调用
        if (typeof prevPage.onAddressSelected === 'function') {
          prevPage.onAddressSelected(address);
        }
      }
      
      // 返回上一页
      wx.navigateBack();
    }
  },

  /**
   * 编辑地址
   */
  onEditAddress(e) {
    const { address } = e.currentTarget.dataset;
    
    if (!address) return;
    
    wx.navigateTo({
      url: `/pages/address-add/index?mode=edit&id=${address.id}&data=${encodeURIComponent(JSON.stringify(address))}`
    });
  },

  /**
   * 删除地址
   */
  async deleteAddress(addressId) {
    try {
      wx.showLoading({ title: '删除中...' });
      
      const result = await api.api.deleteAddress(addressId);
      
      wx.hideLoading();
      
      if (result) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        // 重新加载地址列表
        this.loadAddressList();
      } else {
        wx.showToast({
          title: '删除失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 设置默认地址
   */
  async onSetDefault(e) {
    const { address } = e.currentTarget.dataset;
    
    if (!address) {
      wx.showToast({
        title: '地址信息错误',
        icon: 'none'
      });
      return;
    }
    
    try {
      wx.showLoading({ title: '设置中...' });
      
      const result = await api.api.setDefaultAddress(address.id);
      
      wx.hideLoading();
      
      if (result) {
        wx.showToast({
          title: '设置成功',
          icon: 'success'
        });
        
        // 重新加载地址列表
        this.loadAddressList();
      } else {
        wx.showToast({
          title: '设置失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '设置失败',
        icon: 'none'
      });
    }
  },

  /**
   * 新增地址
   */
  onAddAddress() {
    wx.navigateTo({
      url: '/pages/address-add/index?mode=add'
    });
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 刷新地址列表
   */
  async refreshAddressList() {
    try {
      wx.showLoading({ title: '刷新中...' });
      
      const result = await api.api.getAddressList();
      
      wx.hideLoading();
      wx.stopPullDownRefresh();
      
      if (result && result.data) {
        this.setData({
          addressList: result.data,
          hasAddress: result.data.length > 0
        });
      } else {
        this.setData({
          addressList: [],
          hasAddress: false
        });
      }
    } catch (error) {
      wx.hideLoading();
      wx.stopPullDownRefresh();
      
      wx.showToast({
        title: '刷新失败',
        icon: 'none'
      });
    }
  },

  // ==================== 测试方法 ====================
  
  async testAPIPath() {
    console.log('🧪 开始测试API路径...');
    
    try {
      // 1. 测试正确的微信小程序API路径
      const correctPath = '/wechat/mp/user/addresses';
      console.log('📋 正确的微信小程序API路径:', correctPath);
      
      // 2. 检查api.js中的getAddressList方法
      console.log('📋 测试api.getAddressList()方法...');
      const result = await api.api.getAddressList();
      console.log('✅ api.getAddressList()调用成功:', result);
      
      // 3. 直接调用request来测试正确路径
      const request = require('../../utils/request');
      console.log('📡 直接使用request测试正确路径...');
      console.log('📡 请求路径:', correctPath);
      
      const directResult = await request.get(correctPath);
      console.log('✅ 直接request调用成功:', directResult);
      
    } catch (error) {
      console.error('❌ 测试失败:', error);
      console.error('❌ 错误详情:', {
        message: error.message,
        stack: error.stack
      });
    }
    
    // 显示测试结果
    wx.showModal({
      title: '测试完成',
      content: '请查看控制台输出',
      showCancel: false
    });
  },

  // ==================== 调试方法 ====================
}); 