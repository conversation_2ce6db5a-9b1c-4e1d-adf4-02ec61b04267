// 万象生鲜商品卡片组件 - 根据设计图重新构建
const { isLoggedIn } = require('../../utils/login-state-manager');
const ImageLoadingManager = require('../../utils/image-loading-manager');

Component({
  /**
   * 组件属性
   */
  properties: {
    // 商品数据
    product: {
      type: Object,
      value: {},
      observer: 'onProductChange'
    },
    
    // 图片高度
    imageHeight: {
      type: String,
      value: '240rpx'
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    
    // 卡片尺寸 small/normal/large
    cardSize: {
      type: String,
      value: 'normal'
    },
    
    // 是否显示供应商信息
    showSupplier: {
      type: Boolean,
      value: true
    },
    
    // 是否显示标签
    showTags: {
      type: Boolean,
      value: true
    },
    
    // 是否显示加购按钮
    showAddCart: {
      type: Boolean,
      value: true
    },
    
    // 布局模式 normal/compact
    layoutMode: {
      type: String,
      value: 'normal'
    },
    
    // 是否启用数字键盘功能
    enableNumberKeyboard: {
      type: Boolean,
      value: true
    },
    
    // 最大购买数量
    maxQuantity: {
      type: Number,
      value: 999
    },
    
    // 最小购买数量
    minQuantity: {
      type: Number,
      value: 1
    },
    
    // 是否强制刷新价格
    forceRefreshPrice: {
      type: Boolean,
      value: false,
      observer: 'onForceRefreshPriceChange'
    }
  },

  /**
   * 组件数据
   */
  data: {
    // 内部状态
    loading: false,
    error: false,
    
    // 数字键盘相关
    showNumberKeyboard: false,
    currentProduct: {},
    
    // 购物车数量
    cartQuantity: 0,
    adding: false,
    
    // 价格状态
    priceLoading: false,
    priceError: false
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    /**
     * 组件挂载时
     */
    attached() {
      // 验证初始商品数据
      const { product } = this.properties;
      if (!product || typeof product !== 'object' || !product.id) {
        console.warn('⚠️ 商品卡片组件初始化时商品数据无效:', product);
        return;
      }
      
      this.initComponent();
      this.initCartListener();
      this.initPriceListener();
      this.updateCartQuantity();
    },

    /**
     * 组件卸载时
     */
    detached() {
      this.removeCartListener();
      this.removePriceListener();
    }
  },

  /**
   * 组件方法
   */
  methods: {
    /**
     * 初始化组件
     */
    initComponent() {
      // 根据卡片尺寸设置样式类
      this.updateCardClass();
    },

    /**
     * 初始化价格监听器
     */
    initPriceListener() {
      // 监听登录状态变化，自动刷新价格
      const { addLoginStateListener } = require('../../utils/login-state-manager');
      
      this.priceListenerId = `product-card-price-${this.data.product?.id || 'unknown'}-${Date.now()}`;
      
      addLoginStateListener(this.priceListenerId, (event) => {
        console.log('💰 商品卡片收到登录状态变化:', event.type);
        
        if (event.type === 'login-status-change') {
          // 登录状态变化时，通知price-display组件刷新价格
          this.refreshProductPrice();
        }
      });
      
      console.log('💰 商品卡片价格监听器初始化完成:', this.priceListenerId);
    },

    /**
     * 移除价格监听器
     */
    removePriceListener() {
      if (this.priceListenerId) {
        const { removeLoginStateListener } = require('../../utils/login-state-manager');
        removeLoginStateListener(this.priceListenerId);
        console.log('💰 商品卡片价格监听器已移除:', this.priceListenerId);
      }
    },

    /**
     * 刷新商品价格
     */
    refreshProductPrice() {
      const { product } = this.properties;
      if (!product || !product.id) {
        console.warn('💰 商品信息无效，无法刷新价格');
        return;
      }

      console.log('💰 刷新商品价格:', product.id);
      
      // 通过选择器找到price-display组件并调用刷新方法
      const priceDisplay = this.selectComponent('price-display');
      if (priceDisplay && typeof priceDisplay.refreshPrice === 'function') {
        priceDisplay.refreshPrice();
      } else {
        console.warn('💰 未找到price-display组件或刷新方法');
      }
    },

    /**
     * 强制刷新价格属性变化观察者
     */
    onForceRefreshPriceChange(newVal, oldVal) {
      if (newVal && newVal !== oldVal) {
        console.log('💰 收到强制刷新价格指令');
        this.refreshProductPrice();
        
        // 重置标志
        setTimeout(() => {
          this.setData({ forceRefreshPrice: false });
        }, 100);
      }
    },

    /**
     * 获取会员价格信息
     */
    async getMemberPriceInfo() {
      const { product } = this.properties;
      if (!product || !product.id) {
        return null;
      }

      try {
        this.setData({ priceLoading: true, priceError: false });
        
        const { getProductPrice } = require('../../utils/price-manager');
        
        // 检查商品是否有错误标记，避免请求不存在的商品
        if (product._error_checked) {
          console.log('💰 商品已进行错误检查，跳过价格请求');
          this.setData({ priceLoading: false });
          return {
            shouldShow: true,
            base_price: product.price,
            final_price: product.price
          };
        }
        
        const priceData = await getProductPrice(product.id, 1, true); // 强制刷新
        
        this.setData({ priceLoading: false });
        
        if (priceData && priceData.shouldShow) {
          console.log('💰 获取到会员价格信息:', priceData);
          return priceData;
        } else {
          console.log('💰 无法显示价格信息:', priceData?.message || '未知原因');
          return null;
        }
        
      } catch (error) {
        console.error('💰 获取会员价格失败:', error);
        this.setData({ priceLoading: false, priceError: true });
        return null;
      }
    },

    /**
     * 检查会员价格状态
     */
    checkMemberPriceStatus() {
      const { isLoggedIn } = require('../../utils/login-state-manager');
      const loginStatus = isLoggedIn();
      
      console.log('💰 检查会员价格状态:', {
        isLoggedIn: loginStatus,
        productId: this.properties.product?.id
      });
      
      return {
        canShowMemberPrice: loginStatus,
        needLogin: !loginStatus
      };
    },

    /**
     * 初始化购物车监听器
     */
    initCartListener() {
      // 检查登录状态
      const { isLoggedIn } = require('../../utils/login-state-manager');
      if (!isLoggedIn()) {
        console.log('👤 用户未登录，跳过购物车监听器初始化');
        this.setData({
          cartQuantity: 0
        });
        return;
      }

      // 添加购物车状态监听器
      const { addListener, CartEvents } = require('../../utils/cart-unified');

      // 监听商品添加
      this.addListener = addListener(CartEvents.ADD, (data) => {
        if (data.product_id === this.properties.product?.id) {
          console.log('🛒 商品卡片收到商品添加:', data);
          this.updateCartQuantity();
        }
      });

      // 监听商品更新
      this.updateListener = addListener(CartEvents.UPDATE, (data) => {
        if (data.product_id === this.properties.product?.id) {
          console.log('🛒 商品卡片收到商品更新:', data);
          this.updateCartQuantity();
        }
      });

      // 监听商品移除
      this.removeListener = addListener(CartEvents.REMOVE, (data) => {
        if (data.product_id === this.properties.product?.id) {
          console.log('🛒 商品卡片收到商品移除:', data);
          this.setData({ cartQuantity: 0 });
        }
      });

      console.log('✅ 商品卡片购物车监听器初始化完成');
      this.updateCartQuantity();
    },

    /**
     * 移除购物车监听器
     */
    removeCartListener() {
      const { removeListener } = require('../../utils/cart-unified');

      if (this.addListener) {
        removeListener(this.addListener);
        this.addListener = null;
      }

      if (this.updateListener) {
        removeListener(this.updateListener);
        this.updateListener = null;
      }

      if (this.removeListener) {
        removeListener(this.removeListener);
        this.removeListener = null;
      }

      console.log('✅ 商品卡片购物车监听器移除完成');
    },

    /**
     * 更新购物车数量显示
     */
    async updateCartQuantity() {
      const { product } = this.properties;

      if (!product || !product.id) {
        console.warn('⚠️ 商品信息无效，无法更新购物车数量');
        return;
      }

      // 检查登录状态
      const { isLoggedIn } = require('../../utils/login-state-manager');
      if (!isLoggedIn()) {
        console.log('👤 用户未登录，不处理购物车状态');
        this.setData({
          cartQuantity: 0
        });
        return;
      }

      try {
        // 🔥 使用统一购物车管理器接口
        const { getCartList } = require('../../utils/cart-unified');
        const result = await getCartList();
        
        if (result.success && result.data && result.data.items) {
          const cartItem = result.data.items.find(item => item.product_id === product.id);
          const quantity = cartItem ? cartItem.quantity : 0;

          // 只有当数量发生变化时才更新
          if (quantity !== this.data.cartQuantity) {
            console.log('🔄 更新商品卡片数量显示:', product.id, '从', this.data.cartQuantity, '到', quantity);
            this.setData({
              cartQuantity: quantity
            });
          } else {
            console.log('💭 商品卡片数量无变化:', product.id, '当前数量:', quantity);
          }
        } else {
          // API调用失败或返回空数据，设为0
          if (this.data.cartQuantity !== 0) {
            this.setData({
              cartQuantity: 0
            });
          }
        }
      } catch (error) {
        console.warn('更新购物车数量失败:', error);
        this.setData({
          cartQuantity: 0
        });
      }
    },

    /**
     * 增加商品数量
     */
    onIncreaseQuantity(e) {
      const { product } = this.properties;
      const { cartQuantity } = this.data;

      // 参数验证
      if (!product || !product.id) {
        console.error('❌ 商品信息无效，无法增加数量:', product);
        wx.showToast({
          title: '商品信息错误',
          icon: 'none'
        });
        return;
      }

      // 检查登录状态
      const { isLoggedIn } = require('../../utils/login-state-manager');
      if (!isLoggedIn()) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      // 防止重复点击
      if (this.data.adding) {
        return;
      }

      this.setData({ adding: true });

      // 计算新数量
      const newQuantity = cartQuantity + 1;
      const maxQuantity = this.properties.maxQuantity || 999;

      // 检查最大数量限制
      if (newQuantity > maxQuantity) {
        wx.showToast({
          title: `最多购买${maxQuantity}件`,
          icon: 'none'
        });
        this.setData({ adding: false });
        return;
      }

      console.log('🔼 商品卡片增加数量:', {
        product_id: product?.id,
        product_name: product?.name || '未知商品',
        currentQuantity: cartQuantity,
        newQuantity
      });

      // 直接更新购物车数量
      this.updateCartItemQuantity(product, newQuantity);

      setTimeout(() => {
        this.setData({ adding: false });
      }, 300);
    },

    /**
     * 减少商品数量
     * 🔥 修改：当数量低于最小起购数量时，直接删除商品
     */
    onDecreaseQuantity(e) {
      const { product } = this.properties;
      const { cartQuantity } = this.data;

      // 参数验证
      if (!product || !product.id) {
        console.error('❌ 商品信息无效，无法减少数量:', product);
        wx.showToast({
          title: '商品信息错误',
          icon: 'none'
        });
        return;
      }

      // 检查登录状态
      const { isLoggedIn } = require('../../utils/login-state-manager');
      if (!isLoggedIn()) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }
      
      // 防止重复点击
      if (this.data.adding) {
        return;
      }
      
      // 检查数量是否大于0
      if (cartQuantity <= 0) {
        return;
      }
      
      this.setData({ adding: true });
      
      // 🔥 新增：检查减少后的数量是否低于最小起购数量
      const minSaleQuantity = product?.min_sale_quantity || 1;
      const newQuantity = cartQuantity - 1;
      
      console.log('🔧 商品卡片减少数量:', {
        product_id: product?.id,
        product_name: product?.name || '未知商品',
        current_quantity: cartQuantity,
        new_quantity: newQuantity,
        min_sale_quantity: minSaleQuantity
      });
      
      if (newQuantity < minSaleQuantity) {
        // 数量低于最小起购数量，询问用户是否删除
        console.log('🔧 数量低于最小起购数量，询问用户是否删除商品');

        wx.showModal({
          title: '提示',
          content: `该商品最少购买${minSaleQuantity}${product.unit || '件'}，是否删除该商品？`,
          confirmText: '删除',
          cancelText: '保留',
          success: (res) => {
            if (res.confirm) {
              // 用户确认删除
              this.removeFromCart(product);
            } else {
              // 用户选择保留，恢复到最小起购数量
              this.updateCartItemQuantity(product, minSaleQuantity);
            }
          }
        });
      } else {
        // 正常减少数量
        console.log('🔽 商品卡片减少数量:', {
          product_id: product?.id,
          product_name: product?.name || '未知商品',
          newQuantity
        });

        this.updateCartItemQuantity(product, newQuantity);
      }
      
      setTimeout(() => {
        this.setData({ adding: false });
      }, 300);
    },

    /**
     * 更新卡片样式类
     */
    updateCardClass() {
      const { cardSize, layoutMode } = this.properties;
      let classNames = [];
      
      if (cardSize && cardSize !== 'normal') {
        classNames.push(cardSize);
      }
      
      if (layoutMode && layoutMode !== 'normal') {
        classNames.push(layoutMode);
      }
      
      // 检查商品状态
      const { product } = this.properties;
      if (product) {
        // 只有真正缺货时才添加out-of-stock样式（严格库存策略下的缺货）
        if (product.out_of_stock) {
          classNames.push('out-of-stock');
        }
        if (product.isPromotion || (product.tags && product.tags.some(tag => 
          (tag.name || tag).includes('促销') || 
          (tag.name || tag).includes('特价')
        ))) {
          classNames.push('promotion');
        }
      }
      
      this.setData({
        cardClassNames: classNames.join(' ')
      });
    },

    /**
     * 商品数据变化监听
     */
    onProductChange(newProduct, oldProduct) {
      // 验证新商品数据的有效性
      if (!newProduct || typeof newProduct !== 'object' || !newProduct.id) {
        console.warn('⚠️ 商品数据无效:', newProduct);
        this.setData({
          cartQuantity: 0
        });
        return;
      }
      
      if (newProduct !== oldProduct) {
        // 🔥 通知图片加载管理器开始跟踪新商品图片
        if (newProduct.image && newProduct.id !== oldProduct?.id) {
          const imageKey = `product-${newProduct.id}`;
          ImageLoadingManager.startImageLoading(imageKey, 'category-page', {
            silent: true
          });
        }
        
        this.updateCardClass();
        
        // 🔥 新增：根据商品的最小起购数量更新组件的minQuantity属性
        const minSaleQuantity = newProduct.min_sale_quantity || 1;
        console.log('🔧 商品卡片更新最小起购数量:', {
          product_id: newProduct.id,
          product_name: newProduct.name,
          min_sale_quantity: minSaleQuantity
        });
        
        // 更新组件的minQuantity属性
        this.setData({
          minQuantity: minSaleQuantity
        });
        
        // 只有在登录状态下才更新购物车数量
        const { isLoggedIn } = require('../../utils/login-state-manager');
        if (isLoggedIn()) {
          this.updateCartQuantity();
        } else {
          this.setData({
            cartQuantity: 0
          });
        }
      }
    },

    /**
     * 商品卡片点击
     */
    onProductTap(e) {
      const { product } = this.properties;
      
      // 触发商品点击事件
      this.triggerEvent('productTap', {
        product: product,
        event: e
      });
      
      // 埋点统计
      this.trackProductClick(product);
    },

    /**
     * 商品图片加载成功
     */
    onImageLoad(e) {
      this.setData({ loading: false, error: false });
      
      // 🔥 通知图片加载管理器
      const product = this.properties.product;
      if (product?.id) {
        const imageKey = `product-${product.id}`;
        ImageLoadingManager.onImageLoaded(imageKey, e.detail);
      }
      
      // 触发图片加载成功事件
      this.triggerEvent('imageLoad', {
        product: product,
        detail: e.detail
      });
    },

    /**
     * 商品图片加载失败
     */
    onImageError(e) {
      this.setData({ loading: false, error: true });
      
      // 🔥 通知图片加载管理器
      const product = this.properties.product;
      if (product?.id) {
        const imageKey = `product-${product.id}`;
        ImageLoadingManager.onImageError(imageKey, e.detail);
      }
      
      // 触发图片加载失败事件
      this.triggerEvent('imageError', {
        product: product,
        detail: e.detail
      });
    },

    /**
     * 商品图片点击
     */
    onImageTap(e) {
      const { product } = this.properties;
      
      // 触发图片点击事件
      this.triggerEvent('imageTap', {
        product: product,
        event: e
      });
    },

    /**
     * 添加到购物车
     */
    onAddToCart(e) {
      const { product, enableNumberKeyboard } = this.properties;
      
      // 防止重复点击
      if (this.data.adding) {
        return;
      }
      
      // 检查登录状态，未登录时跳转到登录页（最优先检查）
      const userLoggedIn = isLoggedIn();
      if (!userLoggedIn) {
        wx.navigateTo({
          url: '/pages/login/index'
        });
        return;
      }
      
      // 检查商品缺货状态 - 只检查真正的缺货状态，不检查can_purchase
      if (product.out_of_stock) {
        wx.showToast({
          title: product.purchase_message || '商品已售罄',
          icon: 'none'
        });
        return;
      }
      
      // 如果启用了数字键盘，点击加购按钮直接显示数字键盘（登录后才执行）
      if (enableNumberKeyboard) {
        this.onShowNumberKeyboard(e);
        return;
      }
      
      this.setData({ adding: true });
      
      // 触发加购事件
      this.triggerEvent('addToCart', {
        product: product,
        quantity: 1,
        event: e
      });

      // 延迟更新购物车数量显示
      setTimeout(() => {
        this.updateCartQuantity();
      }, 500);
      
      // 显示加购动画
      this.showAddCartAnimation(e);
      
      // 埋点统计
      this.trackAddToCart(product);
      
      // 重置状态
      setTimeout(() => {
        this.setData({ adding: false });
      }, 500);
    },

    /**
     * 数量输入处理
     */
    onQuantityInput(e) {
      // 检查登录状态
      const { isLoggedIn } = require('../../utils/login-state-manager');
      if (!isLoggedIn()) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      const inputValue = e.detail.value;
      const product = e.currentTarget.dataset.product;

      if (!product || !product.id) {
        console.error('❌ 商品信息无效');
        return;
      }

      // 验证输入值
      let quantity = parseInt(inputValue) || 0;
      const minQuantity = this.data.minQuantity || product.min_sale_quantity || 1;
      const maxQuantity = this.properties.maxQuantity || 999;

      console.log('📝 商品卡片数量输入:', {
        product_id: product?.id,
        product_name: product?.name || '未知商品',
        inputValue,
        quantity,
        minQuantity,
        maxQuantity
      });

      // 验证数量范围
      if (quantity < minQuantity) {
        if (quantity === 0) {
          // 输入0表示要删除商品
          this.removeFromCart(product);
          return;
        } else {
          // 小于最小起购数量，提示用户
          wx.showModal({
            title: '提示',
            content: `该商品最少购买${minQuantity}${product.unit || '件'}，是否删除该商品？`,
            confirmText: '删除',
            cancelText: '保留',
            success: (res) => {
              if (res.confirm) {
                this.removeFromCart(product);
              } else {
                // 恢复到最小起购数量
                this.updateCartItemQuantity(product, minQuantity);
              }
            }
          });
          return;
        }
      }

      if (quantity > maxQuantity) {
        quantity = maxQuantity;
        wx.showToast({
          title: `最多购买${maxQuantity}件`,
          icon: 'none'
        });
      }

      // 更新购物车数量
      this.updateCartItemQuantity(product, quantity);
    },

    /**
     * 数量输入框获得焦点
     */
    onQuantityFocus(e) {
      const product = e.currentTarget.dataset.product;
      console.log('🎯 商品卡片数量输入框获得焦点:', product?.name);
    },

    /**
     * 长按显示数字键盘
     */
    onShowNumberKeyboard(e) {
      const { product, enableNumberKeyboard } = this.properties;
      
      // 检查是否启用数字键盘功能
      if (!enableNumberKeyboard) {
        return;
      }
      
      // 检查登录状态，未登录时跳转到登录页
      const userLoggedIn = isLoggedIn();
      if (!userLoggedIn) {
        wx.navigateTo({
          url: '/pages/login/index'
        });
        return;
      }
      
      // 检查商品缺货状态 - 只检查真正的缺货状态
      if (product.out_of_stock) {
        wx.showToast({
          title: product.purchase_message || '商品已售罄',
          icon: 'none'
        });
        return;
      }
      
      // 设置当前商品并显示数字键盘
      this.setData({
        currentProduct: product,
        showNumberKeyboard: true
      });
      
      // 触发数字键盘显示事件
      this.triggerEvent('showNumberKeyboard', {
        product: product,
        event: e
      });
    },

    /**
     * 数字键盘确认事件
     */
    onNumberKeyboardConfirm(e) {
      const { product, quantity } = e.detail;

      // 确保商品对象有效
      if (!product || !product.id) {
        console.error('❌ 无效的商品信息:', product);
        wx.showToast({
          title: '商品信息不完整',
          icon: 'none'
        });
        return;
      }

      // 计算总的加购数量（当前购物车数量 + 新增数量）
      const currentCartQuantity = this.data.cartQuantity || 0;
      const totalQuantity = currentCartQuantity + quantity;

      console.log('🔢 数字键盘确认 - 总数量计算:', {
        product_id: product?.id,
        product_name: product?.name || '未知商品',
        currentCartQuantity,
        addQuantity: quantity,
        totalQuantity
      });

      // 触发加购事件，传递总数量
      this.triggerEvent('addToCart', {
        product: product,
        quantity: totalQuantity, // 传递总数量而不是增量
        fromKeyboard: true,
        isTotal: true // 标记这是总数量
      });

      // 埋点统计
      this.trackAddToCart(product, quantity);

      // 隐藏数字键盘
      this.setData({
        showNumberKeyboard: false
      });
    },

    /**
     * 数字键盘隐藏事件
     */
    onNumberKeyboardHide(e) {
      this.setData({
        showNumberKeyboard: false
      });
      
      // 触发数字键盘隐藏事件
      this.triggerEvent('hideNumberKeyboard', {
        product: this.data.currentProduct
      });
    },

    /**
     * 数字键盘显示事件
     */
    onNumberKeyboardShow(e) {
      // 触发数字键盘显示事件
      this.triggerEvent('numberKeyboardShow', {
        product: this.data.currentProduct
      });
    },

    /**
     * 更新购物车商品数量（API调用）
     */
    async updateCartItemQuantity(product, quantity) {
      try {
        // 参数验证
        if (!product || !product.id) {
          console.error('❌ 商品信息无效:', product);
          wx.showToast({
            title: '商品信息错误',
            icon: 'none'
          });
          return;
        }

        if (!quantity || quantity < 0) {
          console.error('❌ 数量无效:', quantity);
          wx.showToast({
            title: '数量无效',
            icon: 'none'
          });
          return;
        }

        const { updateCartItem } = require('../../utils/cart-unified');

        console.log('🔄 商品卡片更新购物车数量:', {
          product_id: product.id,
          product_name: product?.name || '未知商品',
          quantity
        });

        const result = await updateCartItem(product.id, quantity);

        if (result.success) {
          this.setData({
            cartQuantity: quantity
          });

          wx.showToast({
            title: '更新成功',
            icon: 'success',
            duration: 1000
          });
        } else {
          throw new Error(result.message || '更新失败');
        }
      } catch (error) {
        console.error('❌ 更新购物车数量失败:', error);
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        });

        // 恢复原数量显示
        this.updateCartQuantity();
      }
    },

    /**
     * 从购物车删除商品
     */
    async removeFromCart(product) {
      try {
        // 参数验证
        if (!product || !product.id) {
          console.error('❌ 商品信息无效，无法删除:', product);
          wx.showToast({
            title: '商品信息错误',
            icon: 'none'
          });
          return;
        }

        const { removeCartItem } = require('../../utils/cart-unified');

        console.log('🗑️ 商品卡片删除商品:', {
          product_id: product.id,
          product_name: product.name || '未知商品'
        });

        const result = await removeCartItem(product.id);

        if (result.success) {
          this.setData({
            cartQuantity: 0
          });

          wx.showToast({
            title: '已删除',
            icon: 'success',
            duration: 1000
          });
        } else {
          throw new Error(result.message || '删除失败');
        }
      } catch (error) {
        console.error('❌ 删除商品失败:', error);
        wx.showToast({
          title: '删除失败',
          icon: 'none'
        });
      }
    },

    /**
     * 显示加购动画
     */
    showAddCartAnimation(e) {
      // 获取按钮位置信息
      const query = this.createSelectorQuery();
      query.select('.add-cart-button').boundingClientRect();
      
      query.exec((res) => {
        if (res && res[0]) {
          const buttonRect = res[0];
          
          // 触发购物车动画事件
          this.triggerEvent('cartAnimation', {
            buttonRect: buttonRect,
            product: this.properties.product
          });
        }
      });
    },

    /**
     * 埋点统计 - 商品点击
     */
    trackProductClick(product) {
      // 这里可以接入埋点统计
      try {
        // 示例：上报商品点击事件
        // analytics.track('product_click', {
        //   product_id: product.id,
        //   product_name: product.name,
        //   price: product.price,
        //   category: product.category
        // });
      } catch (error) {
        console.error('商品点击统计失败:', error);
      }
    },

    /**
     * 埋点统计 - 加购行为
     */
    trackAddToCart(product, quantity = 1) {
      // 这里可以接入埋点统计
      try {
        // 示例：上报加购事件
        // analytics.track('add_to_cart', {
        //   product_id: product.id,
        //   product_name: product.name,
        //   price: product.price,
        //   quantity: quantity
        // });
      } catch (error) {
        console.error('加购统计失败:', error);
      }
    },

    /**
     * 格式化价格显示
     */
    formatPrice(price) {
      if (typeof price === 'number') {
        return price.toFixed(2);
      }
      return price || '0.00';
    },

    /**
     * 获取标签显示文本
     */
    getTagText(tag) {
      return tag.name || tag || '';
    },

    /**
     * 获取标签CSS类名
     */
    getTagClass(tag) {
      if (!tag) return '';
      
      const type = tag.type || '';
      const cssClasses = [];
      
      // 根据标签类型添加对应的CSS类
      switch (type) {
        case 'promotion':
          cssClasses.push('promotion-tag');
          break;
        case 'new':
          cssClasses.push('new-tag');
          break;
        case 'discount':
          cssClasses.push('discount-tag');
          break;
        case 'hot':
          cssClasses.push('hot-tag');
          break;
        case 'recommend':
          cssClasses.push('recommend-tag');
          break;
        case 'default':
          cssClasses.push('default-tag');
          break;
        default:
          // 默认样式
          cssClasses.push('default-tag');
          break;
      }
      
      // 如果标签对象本身有cssClass属性，也添加进去
      if (tag.cssClass) {
        cssClasses.push(tag.cssClass);
      }
      
      return cssClasses.join(' ');
    },

    /**
     * 检查是否显示元素
     */
    shouldShow(condition, content) {
      return condition && content;
    },

    /**
     * 价格加载完成事件处理
     */
    onPriceLoaded(e) {
      const priceInfo = e.detail;
      console.log('💰 商品卡片收到价格加载完成事件:', priceInfo);
      
      // 更新价格状态
      this.setData({
        priceLoading: false,
        priceError: false
      });
      
      // 触发价格加载完成事件给父组件
      this.triggerEvent('priceLoaded', {
        product: this.properties.product,
        priceInfo: priceInfo
      });
    },

    /**
     * 价格加载错误事件处理
     */
    onPriceError(e) {
      const error = e.detail;
      console.warn('💰 商品卡片收到价格加载错误事件:', error);
      
      // 更新价格状态
      this.setData({
        priceLoading: false,
        priceError: true
      });
      
      // 触发价格错误事件给父组件
      this.triggerEvent('priceError', {
        product: this.properties.product,
        error: error
      });
    },

    /**
     * 手动刷新价格
     */
    refreshPrice() {
      console.log('💰 手动刷新商品价格:', this.properties.product?.id);
      this.refreshProductPrice();
    },

    /**
     * 获取当前价格信息
     */
    getCurrentPriceInfo() {
      const priceDisplay = this.selectComponent('price-display');
      if (priceDisplay && typeof priceDisplay.getPriceInfo === 'function') {
        return priceDisplay.getPriceInfo();
      }
      return null;
    }
  }
}); 