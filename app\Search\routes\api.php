<?php

use App\Search\Http\Controllers\SearchController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Search API Routes
|--------------------------------------------------------------------------
|
| 搜索模块的API路由定义
|
*/

// 公共API - 无需认证
Route::prefix('search')->group(function () {
    // 搜索商品
    Route::get('/products', [SearchController::class, 'searchProducts']);
    
    // 获取热门搜索关键词
    Route::get('/hot-keywords', [SearchController::class, 'getHotKeywords']);
    
    // 获取搜索建议
    Route::get('/suggestions', [SearchController::class, 'getSuggestions']);
});

// 添加带有api前缀的路由，匹配前端请求
Route::prefix('api/search')->group(function () {
    // 搜索商品
    Route::get('/products', [SearchController::class, 'searchProducts']);
    
    // 获取热门搜索关键词
    Route::get('/hot-keywords', [SearchController::class, 'getHotKeywords']);
    
    // 获取搜索建议
    Route::get('/suggestions', [SearchController::class, 'getSuggestions']);
});

// 微信小程序专用API
Route::prefix('wechat/search')->group(function () {
    // 搜索商品
    Route::get('/products', [SearchController::class, 'searchProducts']);
    
    // 获取热门搜索关键词
    Route::get('/hot-keywords', [SearchController::class, 'getHotKeywords']);
    
    // 获取搜索建议
    Route::get('/suggestions', [SearchController::class, 'getSuggestions']);
});

// 后台管理API - 需要认证
Route::prefix('admin/search')->middleware(['api', 'auth:sanctum'])->group(function () {
    // 搜索统计
    Route::get('/stats', [SearchController::class, 'getSearchStats']);
    
    // 热门搜索词管理
    Route::get('/hot-keywords', [SearchController::class, 'getHotKeywords']);
    Route::post('/hot-keywords', [SearchController::class, 'setHotKeywords']);
}); 