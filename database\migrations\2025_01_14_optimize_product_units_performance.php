<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_units', function (Blueprint $table) {
            // 1. 为JSON角色字段添加虚拟列和索引（MySQL 5.7+）
            if (DB::connection()->getDriverName() === 'mysql') {
                // 检查MySQL版本是否支持JSON索引
                $version = DB::select('SELECT VERSION() as version')[0]->version;
                $majorVersion = (int) explode('.', $version)[0];
                $minorVersion = (int) explode('.', explode('.', $version)[1])[0];
                
                if ($majorVersion > 5 || ($majorVersion == 5 && $minorVersion >= 7)) {
                    // 为销售单位查询添加虚拟列索引
                    DB::statement('ALTER TABLE product_units ADD COLUMN has_sales_role TINYINT(1) 
                        GENERATED ALWAYS AS (JSON_CONTAINS(roles, \'"sales"\')) STORED');
                    
                    // 为采购单位查询添加虚拟列索引  
                    DB::statement('ALTER TABLE product_units ADD COLUMN has_purchase_role TINYINT(1) 
                        GENERATED ALWAYS AS (JSON_CONTAINS(roles, \'"purchase"\')) STORED');
                    
                    // 为库存单位查询添加虚拟列索引
                    DB::statement('ALTER TABLE product_units ADD COLUMN has_inventory_role TINYINT(1) 
                        GENERATED ALWAYS AS (JSON_CONTAINS(roles, \'"inventory"\')) STORED');
                }
            }
        });
        
        // 2. 添加性能优化索引
        Schema::table('product_units', function (Blueprint $table) {
            // 商品活跃单位查询优化（商品详情页最常用）
            if (!$this->indexExists('product_units', 'idx_product_active')) {
                $table->index(['product_id', 'is_active'], 'idx_product_active');
            }
            
            // 默认单位查询优化
            if (!$this->indexExists('product_units', 'idx_product_default')) {
                $table->index(['product_id', 'is_default'], 'idx_product_default');
            }
            
            // 单位反查优化（单位管理页面）
            if (!$this->indexExists('product_units', 'idx_unit_active')) {
                $table->index(['unit_id', 'is_active'], 'idx_unit_active');
            }
        });
        
        // 3. 为虚拟列添加索引（如果支持）
        if (Schema::hasColumn('product_units', 'has_sales_role')) {
            Schema::table('product_units', function (Blueprint $table) {
                if (!$this->indexExists('product_units', 'idx_product_sales_role')) {
                    $table->index(['product_id', 'has_sales_role', 'is_active'], 'idx_product_sales_role');
                }
                
                if (!$this->indexExists('product_units', 'idx_product_purchase_role')) {
                    $table->index(['product_id', 'has_purchase_role', 'is_active'], 'idx_product_purchase_role');
                }
                
                if (!$this->indexExists('product_units', 'idx_product_inventory_role')) {
                    $table->index(['product_id', 'has_inventory_role', 'is_active'], 'idx_product_inventory_role');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_units', function (Blueprint $table) {
            // 删除添加的索引
            $indexes = [
                'idx_product_active',
                'idx_product_default', 
                'idx_unit_active',
                'idx_product_sales_role',
                'idx_product_purchase_role',
                'idx_product_inventory_role'
            ];
            
            foreach ($indexes as $index) {
                if ($this->indexExists('product_units', $index)) {
                    $table->dropIndex($index);
                }
            }
        });
        
        // 删除虚拟列
        if (Schema::hasColumn('product_units', 'has_sales_role')) {
            Schema::table('product_units', function (Blueprint $table) {
                $table->dropColumn(['has_sales_role', 'has_purchase_role', 'has_inventory_role']);
            });
        }
    }
    
    /**
     * 检查索引是否存在
     */
    private function indexExists($table, $index)
    {
        $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Key_name = '{$index}'");
        return !empty($indexes);
    }
}; 