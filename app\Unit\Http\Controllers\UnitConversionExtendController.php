<?php

namespace App\Unit\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Product\Models\Product;
use App\Unit\Models\Unit;
use App\Unit\Models\UnitConversionGraph;
use App\Unit\Models\UnitConversionEdge;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class UnitConversionExtendController extends Controller
{
    /**
     * 获取单位转换的复杂路径（多级转换）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getConversionPath(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from_unit_id' => 'required|exists:units,id',
            'to_unit_id' => 'required|exists:units,id|different:from_unit_id',
            'product_id' => 'nullable|exists:products,id',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        $fromUnitId = $request->from_unit_id;
        $toUnitId = $request->to_unit_id;
        $productId = $request->product_id;
        
        // 优先使用商品特定的转换路径
        if ($productId) {
            $product = Product::with(['baseUnit', 'auxiliaryUnits'])->findOrFail($productId);
            $path = $this->getProductSpecificConversionPath($product, $fromUnitId, $toUnitId);
            
            if ($path) {
                return response()->json(ApiResponse::success($path));
            }
        }
        
        // 如果没有商品特定路径，尝试使用图论转换路径
        $path = $this->getGraphConversionPath($fromUnitId, $toUnitId);
        
        if ($path) {
            return response()->json(ApiResponse::success($path));
        }
        
        return response()->json(ApiResponse::error('找不到可用的转换路径', 404), 404);
    }
    
    /**
     * 获取商品特定的单位转换路径
     * 
     * @param Product $product
     * @param int $fromUnitId
     * @param int $toUnitId
     * @return array|null
     */
    protected function getProductSpecificConversionPath(Product $product, $fromUnitId, $toUnitId)
    {
        // 基础情况：单位相同
        if ($fromUnitId == $toUnitId) {
            return [
                'path' => [
                    [
                        'from_unit_id' => $fromUnitId,
                        'from_unit' => Unit::find($fromUnitId),
                        'to_unit_id' => $toUnitId,
                        'to_unit' => Unit::find($toUnitId),
                        'conversion_factor' => 1,
                        'is_direct' => true
                    ]
                ],
                'total_conversion_factor' => 1,
                'is_product_specific' => true,
                'product_id' => $product->id,
                'product_name' => $product->name,
            ];
        }
        
        // 获取产品基本单位
        $baseUnitId = $product->base_unit_id;
        if (!$baseUnitId) {
            return null;
        }
        
        // 通过基本单位中转转换
        $fromToBase = null;
        $baseToTo = null;
        
        // 情况1：From单位是基本单位
        if ($fromUnitId == $baseUnitId) {
            $toUnit = $product->auxiliaryUnits()
                ->where('unit_id', $toUnitId)
                ->first();
                
            if ($toUnit) {
                $conversionFactor = 1 / $toUnit->pivot->conversion_factor;
                return [
                    'path' => [
                        [
                            'from_unit_id' => $fromUnitId,
                            'from_unit' => Unit::find($fromUnitId),
                            'to_unit_id' => $toUnitId,
                            'to_unit' => Unit::find($toUnitId),
                            'conversion_factor' => $conversionFactor,
                            'is_direct' => true
                        ]
                    ],
                    'total_conversion_factor' => $conversionFactor,
                    'is_product_specific' => true,
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                ];
            }
        }
        
        // 情况2：To单位是基本单位
        if ($toUnitId == $baseUnitId) {
            $fromUnit = $product->auxiliaryUnits()
                ->where('unit_id', $fromUnitId)
                ->first();
                
            if ($fromUnit) {
                $conversionFactor = $fromUnit->pivot->conversion_factor;
                return [
                    'path' => [
                        [
                            'from_unit_id' => $fromUnitId,
                            'from_unit' => Unit::find($fromUnitId),
                            'to_unit_id' => $toUnitId,
                            'to_unit' => Unit::find($toUnitId),
                            'conversion_factor' => $conversionFactor,
                            'is_direct' => true
                        ]
                    ],
                    'total_conversion_factor' => $conversionFactor,
                    'is_product_specific' => true,
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                ];
            }
        }
        
        // 情况3：都是辅助单位
        $fromUnit = $product->auxiliaryUnits()
            ->where('unit_id', $fromUnitId)
            ->first();
            
        $toUnit = $product->auxiliaryUnits()
            ->where('unit_id', $toUnitId)
            ->first();
            
        if ($fromUnit && $toUnit) {
            $fromToBaseFactor = $fromUnit->pivot->conversion_factor;
            $baseToToFactor = 1 / $toUnit->pivot->conversion_factor;
            $totalFactor = $fromToBaseFactor * $baseToToFactor;
            
            return [
                'path' => [
                    [
                        'from_unit_id' => $fromUnitId,
                        'from_unit' => Unit::find($fromUnitId),
                        'to_unit_id' => $baseUnitId,
                        'to_unit' => Unit::find($baseUnitId),
                        'conversion_factor' => $fromToBaseFactor,
                        'is_direct' => true
                    ],
                    [
                        'from_unit_id' => $baseUnitId,
                        'from_unit' => Unit::find($baseUnitId),
                        'to_unit_id' => $toUnitId,
                        'to_unit' => Unit::find($toUnitId),
                        'conversion_factor' => $baseToToFactor,
                        'is_direct' => true
                    ]
                ],
                'total_conversion_factor' => $totalFactor,
                'is_product_specific' => true,
                'product_id' => $product->id,
                'product_name' => $product->name,
            ];
        }
        
        return null;
    }
    
    /**
     * 获取基于图论的单位转换路径
     * 
     * @param int $fromUnitId
     * @param int $toUnitId
     * @return array|null
     */
    protected function getGraphConversionPath($fromUnitId, $toUnitId)
    {
        // 如果单位相同，直接返回
        if ($fromUnitId == $toUnitId) {
            return [
                'path' => [
                    [
                        'from_unit_id' => $fromUnitId,
                        'from_unit' => Unit::find($fromUnitId),
                        'to_unit_id' => $toUnitId,
                        'to_unit' => Unit::find($toUnitId),
                        'conversion_factor' => 1,
                        'is_direct' => true
                    ]
                ],
                'total_conversion_factor' => 1,
                'is_product_specific' => false
            ];
        }
        
        // 获取单位类型
        $fromUnit = Unit::find($fromUnitId);
        $toUnit = Unit::find($toUnitId);
        
        if (!$fromUnit || !$toUnit || $fromUnit->type !== $toUnit->type) {
            return null;
        }
        
        // 获取默认转换图
        $graph = UnitConversionGraph::where('type', $fromUnit->type)
                                   ->where('is_default', true)
                                   ->first();
        
        if (!$graph) {
            return null;
        }
        
        // 缓存键
        $cacheKey = "unit_conversion_path_{$graph->id}_{$fromUnitId}_{$toUnitId}";
        
        // 尝试从缓存获取路径
        return Cache::remember($cacheKey, now()->addHour(), function() use ($graph, $fromUnitId, $toUnitId, $fromUnit, $toUnit) {
            // 执行广度优先搜索找到最短路径
            $queue = new \SplQueue();
            $visited = [];
            $previous = [];
            $factors = [];
            
            // 初始化
            $queue->enqueue($fromUnitId);
            $visited[$fromUnitId] = true;
            $factors[$fromUnitId] = 1;
            
            while (!$queue->isEmpty()) {
                $currentId = $queue->dequeue();
                
                // 找到了目标单位
                if ($currentId == $toUnitId) {
                    break;
                }
                
                // 获取所有出边
                $edges = UnitConversionEdge::where('graph_id', $graph->id)
                                          ->where('from_unit_id', $currentId)
                                          ->get();
                
                foreach ($edges as $edge) {
                    $nextId = $edge->to_unit_id;
                    
                    if (!isset($visited[$nextId])) {
                        $visited[$nextId] = true;
                        $previous[$nextId] = [
                            'id' => $currentId,
                            'factor' => $edge->conversion_factor
                        ];
                        $factors[$nextId] = $factors[$currentId] * $edge->conversion_factor;
                        $queue->enqueue($nextId);
                    }
                }
            }
            
            // 如果找不到路径
            if (!isset($previous[$toUnitId])) {
                return null;
            }
            
            // 构造路径
            $path = [];
            $current = $toUnitId;
            
            while ($current != $fromUnitId) {
                $prev = $previous[$current];
                $path[] = [
                    'from_unit_id' => $prev['id'],
                    'from_unit' => Unit::find($prev['id']),
                    'to_unit_id' => $current,
                    'to_unit' => Unit::find($current),
                    'conversion_factor' => $prev['factor'],
                    'is_direct' => true
                ];
                $current = $prev['id'];
            }
            
            // 反转路径
            $path = array_reverse($path);
            
            return [
                'path' => $path,
                'total_conversion_factor' => $factors[$toUnitId],
                'is_product_specific' => false,
                'graph_id' => $graph->id,
                'graph_name' => $graph->name
            ];
        });
    }

    /**
     * 格式化转换值
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function formatConversionValue(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'value' => 'required|numeric',
            'from_unit_id' => 'required|exists:units,id',
            'to_unit_id' => 'required|exists:units,id',
            'decimals' => 'nullable|integer|min:0|max:10',
            'product_id' => 'nullable|exists:products,id',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        $value = $request->value;
        $fromUnitId = $request->from_unit_id;
        $toUnitId = $request->to_unit_id;
        $decimals = $request->input('decimals', 2);
        $productId = $request->product_id;
        
        $fromUnit = Unit::findOrFail($fromUnitId);
        $toUnit = Unit::findOrFail($toUnitId);
        
        try {
            // 使用UnitService进行转换
            $result = app(\App\Unit\Services\UnitService::class)
                ->convertValue($value, $fromUnit, $toUnit);
            
            // 格式化结果
            $formattedResult = number_format($result, $decimals) . ' ' . $toUnit->symbol;
            
            return response()->json(ApiResponse::success([
                'original_value' => $value,
                'original_unit' => $fromUnit,
                'converted_value' => $result,
                'converted_unit' => $toUnit,
                'formatted_result' => $formattedResult
            ]));
            
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error($e->getMessage(), 400), 400);
        }
    }

    /**
     * 检查单位类型是否匹配
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkCategoryMismatch(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'unit_ids' => 'required|array',
            'unit_ids.*' => 'exists:units,id',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        $unitIds = $request->unit_ids;
        
        if (count($unitIds) < 2) {
            return response()->json(ApiResponse::success([
                'has_mismatch' => false,
                'types' => []
            ]));
        }
        
        $units = Unit::whereIn('id', $unitIds)->get();
        $types = $units->pluck('type')->unique()->toArray();
        
        $hasMismatch = count($types) > 1;
        
        return response()->json(ApiResponse::success([
            'has_mismatch' => $hasMismatch,
            'types' => $types,
            'units' => $units->groupBy('type')->toArray()
        ]));
    }
} 