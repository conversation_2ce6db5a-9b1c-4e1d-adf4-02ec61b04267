// pages/order-success/index.js - 订单成功/支付结果页面
const api = require('../../utils/api');

Page({
  data: {
    // 页面状态
    status: 'success', // success成功 / fail失败
    paymentMethod: 'online', // online在线支付 / cod货到付款
    
    // 订单信息
    orderId: null,
    orderNo: '',
    amount: '0.00',
    
    // 页面文案
    title: '订单提交成功',
    message: '您的订单已提交成功，感谢您的购买！',
    
    // 按钮配置
    buttons: [],
    
    // 系统状态栏高度
    statusBarHeight: 44,
    
    // 加载状态
    loading: true
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    console.log('订单成功页面参数:', options);
    
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 44;
    
    this.setData({
      statusBarHeight: statusBarHeight
    });
    
    // 初始化页面数据
    this.initPageData(options);
  },

  /**
   * 初始化页面数据
   */
  async initPageData(options) {
    const { 
      orderId, 
      paymentMethod = 'wechat', // 修复：使用正确的默认支付方式值
      status = 'success' 
    } = options;
    
    // 设置基本信息
    let pageData = {
      orderId: orderId ? parseInt(orderId) : null,
      paymentMethod: paymentMethod,
      status: status,
      loading: false
    };
    
    // 根据支付方式和状态设置页面内容
    if (paymentMethod === 'cod') {
      // 货到付款
      pageData.title = '订单提交成功';
      pageData.message = '您的订单已提交成功，我们会尽快为您安排配送！';
      pageData.buttons = [
        { text: '查看订单', type: 'primary', action: 'viewOrder' },
        { text: '继续购物', type: 'default', action: 'continueShopping' }
      ];
    } else if (paymentMethod === 'wechat') { // 修复：使用正确的支付方式值判断
      if (status === 'success') {
        // 在线支付成功
        pageData.title = '支付成功';
        pageData.message = '恭喜您，支付成功！我们会尽快为您安排配送。';
        pageData.buttons = [
          { text: '查看订单', type: 'primary', action: 'viewOrder' },
          { text: '继续购物', type: 'default', action: 'continueShopping' }
        ];
      } else {
        // 在线支付失败
        pageData.title = '支付失败';
        pageData.message = '很抱歉，支付未成功，您可以重新支付或选择其他支付方式。';
        pageData.status = 'fail';
        pageData.buttons = [
          { text: '重新支付', type: 'primary', action: 'retryPayment' },
          { text: '查看订单', type: 'default', action: 'viewOrder' }
        ];
      }
    }
    
    this.setData(pageData);
    
    // 如果有订单ID，加载订单详情
    if (orderId) {
      this.loadOrderDetail(orderId);
    }
    
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: pageData.title
    });
  },

  /**
   * 加载订单详情
   */
  async loadOrderDetail(orderId) {
    try {
      const result = await api.getOrderDetail(orderId);
      
      if (result.success && result.data) {
        const order = result.data;
        this.setData({
          orderNo: order.orderNo || order.order_no || `ORD${orderId}`,
          amount: order.totalAmount || order.total || '0.00'
        });
      }
      
    } catch (error) {
      console.error('加载订单详情失败:', error);
      // 不影响页面显示，只是无法显示订单号和金额
    }
  },

  /**
   * 按钮点击处理
   */
  onButtonTap(e) {
    const { action } = e.currentTarget.dataset;
    
    switch (action) {
      case 'viewOrder':
        this.viewOrder();
        break;
      case 'continueShopping':
        this.continueShopping();
        break;
      case 'retryPayment':
        this.retryPayment();
        break;
      default:
        console.warn('未知的按钮操作:', action);
    }
  },

  /**
   * 查看订单
   */
  viewOrder() {
    if (this.data.orderId) {
      wx.redirectTo({
        url: `/pages/order-detail/index?id=${this.data.orderId}`
      }).catch(() => {
        // 如果订单详情页面不存在，跳转到订单列表
        wx.redirectTo({
          url: '/pages/order-list/index'
        }).catch(() => {
          wx.switchTab({
            url: '/pages/profile/index'
          });
        });
      });
    } else {
      // 没有订单ID，直接跳转到订单列表
      wx.redirectTo({
        url: '/pages/order-list/index'
      }).catch(() => {
        wx.switchTab({
          url: '/pages/profile/index'
        });
      });
    }
  },

  /**
   * 继续购物
   */
  continueShopping() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  /**
   * 重新支付
   */
  async retryPayment() {
    if (!this.data.orderId) {
      wx.showToast({
        title: '订单信息异常',
        icon: 'none'
      });
      return;
    }
    
    try {
      // 显示支付加载提示
      wx.showLoading({
        title: '准备支付...',
        mask: true
      });
      
      // 调用支付流程
      const payResult = await api.payOrder(this.data.orderId);
      
      wx.hideLoading();
      
      if (payResult.success) {
        // 支付成功，更新页面状态
        this.setData({
          status: 'success',
          title: '支付成功',
          message: '恭喜您，支付成功！我们会尽快为您安排配送。',
          buttons: [
            { text: '查看订单', type: 'primary', action: 'viewOrder' },
            { text: '继续购物', type: 'default', action: 'continueShopping' }
          ]
        });
        
        wx.setNavigationBarTitle({
          title: '支付成功'
        });
        
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('重新支付失败:', error);
      
      let errorMsg = '支付失败，请重试';
      if (error.message) {
        if (error.message.includes('取消')) {
          errorMsg = '支付已取消';
        } else if (error.message.includes('openid')) {
          errorMsg = '用户信息异常，请重新登录';
        } else {
          errorMsg = error.message;
        }
      }
      
      wx.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }
  },

  /**
   * 返回首页
   */
  onBack() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
}); 