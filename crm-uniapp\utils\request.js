import config from './config.js'
import loadingManager from './loading-manager.js'

class Request {
	constructor() {
		this.baseUrl = config.baseUrl
		this.timeout = config.timeout
		this.requestQueue = new Map() // 请求队列，用于防重复请求
		this.isRedirectingToLogin = false // 防止重复跳转登录页
		this.lastUnauthorizedTime = 0 // 上次401处理时间
	}
	
	// 获取存储的token
	getToken() {
		return uni.getStorageSync(config.storageKeys.token) || ''
	}
	
	// 设置token
	setToken(token) {
		uni.setStorageSync(config.storageKeys.token, token)
	}
	
	// 清除token
	clearToken() {
		uni.removeStorageSync(config.storageKeys.token)
		uni.removeStorageSync(config.storageKeys.userInfo)
		uni.removeStorageSync(config.storageKeys.employeeInfo)
	}
	
	// 生成请求唯一标识
	generateRequestKey(options) {
		const { url, method = 'GET', data = {} } = options
		const dataStr = JSON.stringify(data)
		return `${method}:${url}:${dataStr}`
	}
	
	// 检查是否为重复请求
	isDuplicateRequest(key) {
		return this.requestQueue.has(key)
	}
	
	// 添加请求到队列
	addRequestToQueue(key, promise) {
		this.requestQueue.set(key, promise)
		
		// 请求完成后从队列中移除
		promise.finally(() => {
			this.requestQueue.delete(key)
		})
	}
	
	// 请求拦截器
	interceptRequest(options) {
		// 初始化header对象
		if (!options.header) {
			options.header = {}
		}
		
		// 添加token
		const token = this.getToken()
		if (token) {
			options.header = {
				...options.header,
				'Authorization': `Bearer ${token}`
			}
		}
		
		// 设置默认Content-Type
		if (!options.header['Content-Type']) {
			options.header['Content-Type'] = 'application/json'
		}
		
		// 生成加载标识
		const loadingKey = options.loadingKey || `request_${Date.now()}_${Math.random()}`
		options.loadingKey = loadingKey
		
		// 显示加载提示
		if (options.loading !== false) {
			const loadingTitle = options.loadingTitle || '加载中...'
			loadingManager.showLoading(loadingKey, { 
				title: loadingTitle,
				mask: options.loadingMask !== false
			})
		}
		
		return options
	}
	
	// 响应拦截器
	interceptResponse(response, options) {
		// 隐藏加载提示
		if (options.loading !== false && options.loadingKey) {
			loadingManager.hideLoading(options.loadingKey)
		}
		
		const { statusCode, data } = response
		
		// HTTP状态码检查
		if (statusCode !== 200) {
			this.handleError(statusCode, data, options)
			throw response
		}
		
		// 业务状态码检查
		if (data.code !== undefined && data.code !== 0 && data.code !== 200) {
			// token过期或无效
			if (data.code === 401) {
				this.handleUnauthorized()
				throw data
			}
			
			// 其他业务错误
			if (options.showError !== false) {
				loadingManager.showToast(data.message || '请求失败')
			}
			throw data
		}
		
		return data
	}
	
	// 处理未授权
	handleUnauthorized() {
		const now = Date.now()
		
		// 防抖：如果1秒内已经处理过401，则忽略
		if (now - this.lastUnauthorizedTime < 1000) {
			console.log('忽略重复的401响应')
			return
		}
		
		this.lastUnauthorizedTime = now
		
		// 如果已经在跳转登录页的过程中，则忽略
		if (this.isRedirectingToLogin) {
			console.log('已在跳转登录页，忽略重复处理')
			return
		}
		
		// 检查当前是否已经在登录页
		const pages = getCurrentPages()
		const currentPage = pages[pages.length - 1]
		const currentRoute = currentPage ? currentPage.route : ''
		
		if (currentRoute === 'pages/login/login') {
			console.log('当前已在登录页，无需跳转')
			return
		}
		
		this.isRedirectingToLogin = true
		this.clearToken()
		
		loadingManager.showToast('登录已过期，请重新登录', 'none', 2000)
		
		// 跳转到登录页
		setTimeout(() => {
			uni.reLaunch({
				url: '/pages/login/login',
				success: () => {
					console.log('成功跳转到登录页')
					// 重置跳转标志
					setTimeout(() => {
						this.isRedirectingToLogin = false
					}, 1000)
				},
				fail: (error) => {
					console.error('跳转登录页失败:', error)
					this.isRedirectingToLogin = false
				}
			})
		}, 1500) // 稍微延迟，让用户看到提示信息
	}
	
	// 处理错误
	handleError(statusCode, data, options) {
		let message = '请求失败'
		
		switch (statusCode) {
			case 400:
				message = '请求参数错误'
				break
			case 401:
				message = '未授权访问'
				this.handleUnauthorized()
				return
			case 403:
				message = '禁止访问'
				break
			case 404:
				message = '请求地址不存在'
				break
			case 500:
				message = '服务器内部错误'
				break
			case 502:
				message = '网关错误'
				break
			case 503:
				message = '服务不可用'
				break
			case 504:
				message = '网关超时'
				break
			default:
				message = `请求失败(${statusCode})`
		}
		
		if (options.showError !== false) {
			loadingManager.showToast(data?.message || message)
		}
	}
	
	// 通用请求方法
	request(options) {
		// 处理URL
		if (!options.url.startsWith('http')) {
			options.url = this.baseUrl + options.url
		}
		
		// 生成请求标识
		const requestKey = this.generateRequestKey(options)
		
		// 检查重复请求（可选）
		if (options.preventDuplicate !== false && this.isDuplicateRequest(requestKey)) {
			console.log('阻止重复请求:', requestKey)
			return this.requestQueue.get(requestKey)
		}
		
		// 请求拦截
		options = this.interceptRequest(options)
		
		// 添加调试日志
		console.log('发起请求:', {
			url: options.url,
			method: options.method,
			data: options.data,
			header: options.header
		})
		
		const requestPromise = new Promise((resolve, reject) => {
			uni.request({
				...options,
				timeout: this.timeout,
				success: (response) => {
					console.log('请求成功:', response)
					try {
						const result = this.interceptResponse(response, options)
						resolve(result)
					} catch (error) {
						reject(error)
					}
				},
				fail: (error) => {
					console.error('请求失败:', error)
					
					// 隐藏加载提示
					if (options.loading !== false && options.loadingKey) {
						loadingManager.hideLoading(options.loadingKey)
					}
					
					// 网络错误处理
					let errorMessage = '网络连接失败'
					
					if (error.errMsg) {
						if (error.errMsg.includes('timeout')) {
							errorMessage = '请求超时，请稍后重试'
						} else if (error.errMsg.includes('fail')) {
							errorMessage = '网络连接失败，请检查网络'
						}
					}
					
					if (options.showError !== false) {
						loadingManager.showToast(errorMessage)
					}
					
					reject(error)
				}
			})
		})
		
		// 添加到请求队列
		if (options.preventDuplicate !== false) {
			this.addRequestToQueue(requestKey, requestPromise)
		}
		
		return requestPromise
	}
	
	// GET请求
	get(url, params = {}, options = {}) {
		// 处理查询参数
		if (Object.keys(params).length > 0) {
			const queryString = Object.keys(params)
				.filter(key => params[key] !== undefined && params[key] !== null && params[key] !== '')
				.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
				.join('&')
			
			if (queryString) {
				url += (url.includes('?') ? '&' : '?') + queryString
			}
		}
		
		return this.request({
			url,
			method: 'GET',
			...options
		})
	}
	
	// POST请求
	post(url, data = {}, options = {}) {
		return this.request({
			url,
			method: 'POST',
			data,
			...options
		})
	}
	
	// PUT请求
	put(url, data = {}, options = {}) {
		return this.request({
			url,
			method: 'PUT',
			data,
			...options
		})
	}
	
	// DELETE请求
	delete(url, data = {}, options = {}) {
		return this.request({
			url,
			method: 'DELETE',
			data,
			...options
		})
	}
	
	// 批量请求
	async batchRequest(requests) {
		const promises = requests.map(request => {
			const { method = 'GET', url, data, options = {} } = request
			return this[method.toLowerCase()](url, data, {
				...options,
				loading: false, // 批量请求时不显示单个loading
				showError: false // 批量请求时不显示单个错误
			})
		})
		
		try {
			const results = await Promise.allSettled(promises)
			return results.map((result, index) => ({
				...requests[index],
				success: result.status === 'fulfilled',
				data: result.status === 'fulfilled' ? result.value : null,
				error: result.status === 'rejected' ? result.reason : null
			}))
		} catch (error) {
			console.error('批量请求失败:', error)
			throw error
		}
	}
	
	// 取消所有请求
	cancelAllRequests() {
		this.requestQueue.clear()
		loadingManager.clearAll()
	}
	
	// 获取请求统计
	getRequestStats() {
		return {
			activeRequests: this.requestQueue.size,
			loadingStates: loadingManager.getStats()
		}
	}
}

// 创建全局实例
const request = new Request()

export default request 