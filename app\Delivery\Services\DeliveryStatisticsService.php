<?php

namespace App\Delivery\Services;

use App\Delivery\Models\Delivery;
use App\Delivery\Models\Deliverer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DeliveryStatisticsService
{
    /**
     * 获取配送概览统计
     *
     * @return array
     */
    public function getOverview()
    {
        $today = Carbon::today();
        $thisWeekStart = Carbon::now()->startOfWeek();
        $thisMonthStart = Carbon::now()->startOfMonth();
        
        // 总配送数
        $totalDeliveries = Delivery::count();
        
        // 今日配送数
        $todayDeliveries = Delivery::whereDate('created_at', $today)->count();
        
        // 本周配送数
        $thisWeekDeliveries = Delivery::where('created_at', '>=', $thisWeekStart)->count();
        
        // 本月配送数
        $thisMonthDeliveries = Delivery::where('created_at', '>=', $thisMonthStart)->count();
        
        // 各状态配送数
        $pendingDeliveries = Delivery::where('status', 'pending')->count();
        $inProgressDeliveries = Delivery::where('status', 'in_progress')->count();
        $completedDeliveries = Delivery::where('status', 'completed')->count();
        
        // 配送员统计
        $totalDeliverers = Deliverer::count();
        $activeDeliverers = Deliverer::where('status', 'available')->orWhere('status', 'busy')->count();
        
        // 平均配送时间（小时）
        $avgDeliveryTime = DB::table('deliveries')
            ->join('orders', 'deliveries.order_id', '=', 'orders.id')
            ->whereNotNull('orders.delivered_at')
            ->whereNotNull('orders.shipped_at')
            ->select(DB::raw('AVG(TIMESTAMPDIFF(HOUR, orders.shipped_at, orders.delivered_at)) as avg_time'))
            ->first();
        
        return [
            'total_deliveries' => $totalDeliveries,
            'today_deliveries' => $todayDeliveries,
            'this_week_deliveries' => $thisWeekDeliveries,
            'this_month_deliveries' => $thisMonthDeliveries,
            'pending_deliveries' => $pendingDeliveries,
            'in_progress_deliveries' => $inProgressDeliveries,
            'completed_deliveries' => $completedDeliveries,
            'total_deliverers' => $totalDeliverers,
            'active_deliverers' => $activeDeliverers,
            'avg_delivery_time' => $avgDeliveryTime ? round($avgDeliveryTime->avg_time, 1) : 0,
        ];
    }
    
    /**
     * 获取订单趋势数据
     *
     * @param Request $request
     * @return array
     */
    public function getOrderTrend(Request $request)
    {
        $period = $request->input('period', 'week');
        $endDate = Carbon::today();
        $startDate = null;
        $groupFormat = null;
        
        switch ($period) {
            case 'week':
                $startDate = Carbon::today()->subDays(6);
                $groupFormat = 'Y-m-d';
                break;
            case 'month':
                $startDate = Carbon::today()->subDays(29);
                $groupFormat = 'Y-m-d';
                break;
            case 'year':
                $startDate = Carbon::today()->subMonths(11)->startOfMonth();
                $groupFormat = 'Y-m';
                break;
            default:
                $startDate = Carbon::today()->subDays(6);
                $groupFormat = 'Y-m-d';
        }
        
        // 获取每日配送数据
        $deliveryData = Delivery::whereBetween(DB::raw('DATE(created_at)'), [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->select(DB::raw("DATE_FORMAT(created_at, '{$groupFormat}') as date"), DB::raw('COUNT(*) as count'))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date')
            ->map(function ($item) {
                return $item->count;
            })
            ->toArray();
        
        // 获取每日完成配送数据
        $completedData = Delivery::where('status', 'completed')
            ->whereBetween(DB::raw('DATE(created_at)'), [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->select(DB::raw("DATE_FORMAT(created_at, '{$groupFormat}') as date"), DB::raw('COUNT(*) as count'))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date')
            ->map(function ($item) {
                return $item->count;
            })
            ->toArray();
        
        // 生成日期范围
        $dateRange = [];
        $currentDate = clone $startDate;
        
        while ($currentDate->lte($endDate)) {
            $dateKey = $currentDate->format($groupFormat);
            $dateRange[] = $dateKey;
            
            if ($period === 'year') {
                $currentDate->addMonth();
            } else {
                $currentDate->addDay();
            }
        }
        
        // 填充没有数据的日期
        $result = [
            'labels' => $dateRange,
            'datasets' => [
                [
                    'name' => '总配送量',
                    'data' => []
                ],
                [
                    'name' => '完成配送量',
                    'data' => []
                ]
            ]
        ];
        
        foreach ($dateRange as $date) {
            $result['datasets'][0]['data'][] = $deliveryData[$date] ?? 0;
            $result['datasets'][1]['data'][] = $completedData[$date] ?? 0;
        }
        
        return $result;
    }
} 