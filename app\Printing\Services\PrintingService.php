<?php

namespace App\Printing\Services;

use App\Printing\Contracts\PrintDriverInterface;
use App\Printing\Models\PrintRecord;
use Illuminate\Support\Facades\Log;

class PrintingService
{
    protected PrintDriverInterface $driver;
    protected array $drivers = [];

    /**
     * 模板打印设置配置
     */
    protected const TEMPLATE_CONFIGS = [
        'delivery' => [
            'paper_size' => 'A4',
            'orientation' => 'portrait',
            'margin_top' => 10,
            'margin_left' => 10,
            'margin_right' => 10,
            'margin_bottom' => 10,
            'font_size' => 12,
            'font_name' => '宋体'
        ],
        'receipt' => [
            'paper_width' => 80, // 80mm热敏纸
            'orientation' => 'portrait',
            'margin_top' => 5,
            'margin_left' => 5,
            'margin_right' => 5,
            'margin_bottom' => 5,
            'font_size' => 11,
            'font_name' => '宋体'
        ],
        'normal' => [
            'paper_size' => 'A4',
            'orientation' => 'portrait',
            'margin_top' => 15,
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_bottom' => 15,
            'font_size' => 12,
            'font_name' => '宋体'
        ]
    ];

    public function __construct(PrintDriverInterface $driver)
    {
        $this->driver = $driver;
    }

    /**
     * 设置打印驱动
     */
    public function setDriver(string $driverName): void
    {
        // 根据驱动名称创建对应的驱动实例
        switch ($driverName) {
            case 'lodop':
            case 'clodop':
                $this->driver = app(\App\Printing\Services\Drivers\CLodopDriver::class);
                $this->driver->initialize(config('printing.drivers.lodop', []));
                break;
            case 'browser':
            default:
                $this->driver = app(\App\Printing\Services\Drivers\BrowserDriver::class);
                $this->driver->initialize(config('printing.drivers.browser', []));
                break;
        }
    }

    /**
     * 打印文本
     */
    public function printText(string $content, array $options = []): bool
    {
        return $this->driver->printText($content, $options);
    }

    /**
     * 打印HTML
     */
    public function printHtml(string $html, array $options = []): bool
    {
        return $this->driver->printHtml($html, $options);
    }

    /**
     * 预览打印内容
     */
    public function preview(string $content, array $options = []): string
    {
        return $this->driver->preview($content, $options);
    }

    /**
     * 生成打印脚本
     */
    public function generatePrintScript(string $content, array $options = []): string
    {
        return $this->driver->generatePrintScript($content, $options);
    }

    /**
     * 生成小票打印脚本
     */
    public function generateReceiptScript(string $content, array $options = []): string
    {
        return $this->driver->generateReceiptScript($content, $options);
    }

    /**
     * 打印订单
     */
    public function printOrder($order, array $options = []): array
    {
        try {
            // 创建打印记录
            $printRecord = PrintRecord::createRecord($order, array_merge($options, [
                'print_type' => $options['type'] ?? 'normal', // 确保print_type正确设置
                'print_content' => null, // 会在生成HTML后更新
                'driver' => $this->getCurrentDriverName()
            ]));

            // 标记为打印中
            $printRecord->markAsPrinting();

            // 根据类型选择不同的模板
            $type = $options['type'] ?? 'normal';
            
            if ($type === 'delivery') {
                $html = $this->generateDeliveryTemplate($order, $options);
            } else {
                $html = $this->generateOrderHtml($order);
            }
            
            // 更新打印记录的内容
            $printRecord->update(['print_content' => $html]);
            
            // 获取模板默认配置
            $templateConfig = self::TEMPLATE_CONFIGS[$type] ?? self::TEMPLATE_CONFIGS['normal'];
            
            // 从HTML模板中提取打印设置
            $templateSettings = $this->extractPrintSettings($html);
            
            // 合并设置：模板配置 < HTML设置 < 传入参数
            $printOptions = array_merge($templateConfig, $templateSettings, $options);
            
            if ($type === 'receipt') {
                $script = $this->driver->generateReceiptScript($html, $printOptions);
            } else {
                $script = $this->driver->generatePrintScript($html, $printOptions);
            }

            // 注意：这里不能直接标记为已完成，需要等待前端回调
            // $printRecord->markAsCompleted();

            return [
                'success' => true,
                'print_record_id' => $printRecord->id,
                'html' => $html,
                'script' => $script,
                'preview_url' => $this->generatePreviewUrl($html),
                'template_type' => $type,
                'template_config' => $templateConfig,
                'html_settings' => $templateSettings,
                'final_settings' => $printOptions
            ];
        } catch (\Exception $e) {
            // 如果有打印记录，标记为失败
            if (isset($printRecord)) {
                $printRecord->markAsFailed($e->getMessage());
            }

            Log::error('Print order failed', [
                'order_id' => $order->id ?? null,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 从HTML模板中提取打印设置
     */
    protected function extractPrintSettings(string $html): array
    {
        $settings = [];
        
        // 使用正则表达式提取data-*属性
        if (preg_match('/data-paper-size=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['paper_size'] = $matches[1];
        }
        
        if (preg_match('/data-paper-width=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['paper_width'] = (int)$matches[1];
        }
        
        if (preg_match('/data-orientation=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['orientation'] = $matches[1];
        }
        
        if (preg_match('/data-margin-top=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['margin_top'] = (int)$matches[1];
        }
        
        if (preg_match('/data-margin-left=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['margin_left'] = (int)$matches[1];
        }
        
        if (preg_match('/data-margin-right=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['margin_right'] = (int)$matches[1];
        }
        
        if (preg_match('/data-margin-bottom=[\'"]([^\'"]*)[\'"]/', $html, $matches)) {
            $settings['margin_bottom'] = (int)$matches[1];
        }
        
        // 从CSS @page规则中提取设置（备用方案）
        if (preg_match('/@page\s*\{[^}]*size:\s*([^;]+);/', $html, $matches)) {
            if (empty($settings['paper_size']) && empty($settings['paper_width'])) {
                $pageSize = trim($matches[1]);
                if (preg_match('/(\d+)mm/', $pageSize, $widthMatch)) {
                    $settings['paper_width'] = (int)$widthMatch[1];
                } else {
                    $settings['paper_size'] = $pageSize;
                }
            }
        }
        
        return $settings;
    }

    /**
     * 生成订单HTML
     */
    protected function generateOrderHtml($order): string
    {
        $html = "
<style>
    /* 小票打印设置 */
    @page {
        size: 80mm auto;
        margin: 5mm;
    }
    
    .receipt {
        width: 70mm;
        font-family: '宋体', SimSun, serif;
        font-size: 11px;
        line-height: 1.3;
        margin: 0;
        padding: 5mm;
        box-sizing: border-box;
        /* 小票专用设置 */
        --paper-width: 80;
        --orientation: portrait;
        --margin-top: 5mm;
        --margin-bottom: 5mm;
        --margin-left: 5mm;
        --margin-right: 5mm;
    }
    .header {
        text-align: center;
        margin-bottom: 10px;
    }
    .store-name {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 3px;
    }
    .store-info {
        font-size: 12px;
        margin-bottom: 5px;
    }
    .info {
        margin-bottom: 8px;
    }
    .info-item {
        margin: 1px 0;
        font-size: 10px;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 5px 0;
        font-size: 10px;
    }
    th, td {
        padding: 1px 2px;
        text-align: left;
        border-bottom: 1px dashed #ccc;
    }
    th {
        font-weight: bold;
        font-size: 9px;
    }
    .total {
        font-weight: bold;
        font-size: 12px;
        text-align: right;
        margin-top: 8px;
    }
    .footer {
        text-align: center;
        margin-top: 10px;
        font-size: 9px;
        border-top: 1px dashed #ccc;
        padding-top: 3px;
    }
    .divider {
        border-top: 1px dashed #ccc;
        margin: 8px 0;
    }
    
    @media print {
        .receipt {
            padding: 0;
            margin: 0;
        }
    }
</style>

<div class='receipt' 
     data-paper-width='80' 
     data-orientation='portrait' 
     data-margin-top='5' 
     data-margin-left='5' 
     data-margin-right='5' 
     data-margin-bottom='5'>
    <div class='header'>
        <div class='store-name'>万家生鲜</div>
        <div class='store-info'>订单小票</div>
    </div>
    
    <div class='divider'></div>
    
    <div class='info'>
        <div class='info-item'>订单号: {$order->order_no}</div>
        <div class='info-item'>下单时间: " . date('Y-m-d H:i:s', strtotime($order->created_at)) . "</div>
        <div class='info-item'>收货人: {$order->contact_name}</div>
        <div class='info-item'>联系电话: {$order->contact_phone}</div>
        <div class='info-item'>收货地址: {$order->shipping_address}</div>
    </div>
    
    <div class='divider'></div>
    
    <table>
        <thead>
            <tr>
                <th>商品</th>
                <th>单价</th>
                <th>数量</th>
                <th>小计</th>
            </tr>
        </thead>
        <tbody>";

        $totalAmount = 0;
        if ($order->items && count($order->items) > 0) {
            foreach ($order->items as $item) {
                $productName = $item->product_name ?? ($item->product->name ?? '商品');
                $price = $item->price ?? 0;
                $quantity = $item->quantity ?? 1;
                $subtotal = $price * $quantity;
                $totalAmount += $subtotal;
                
                // 获取单位信息
                $unit = '';
                if (isset($item->unit) && $item->unit) {
                    $unit = $item->unit->symbol ?? $item->unit->name ?? '';
                } elseif (isset($item->unit_symbol)) {
                    $unit = $item->unit_symbol;
                } elseif (isset($item->unit_name)) {
                    $unit = $item->unit_name;
                }
                
                $displayQuantity = $unit ? "{$quantity}{$unit}" : $quantity;
                
                $html .= "
            <tr>
                <td>" . htmlspecialchars($productName) . "</td>
                <td>¥" . number_format($price, 2) . "</td>
                <td>{$displayQuantity}</td>
                <td>¥" . number_format($subtotal, 2) . "</td>
            </tr>";
            }
        } else {
            $totalAmount = $order->total ?? 0;
        }

        // 使用计算的总金额或订单总金额
        $finalTotal = $totalAmount > 0 ? $totalAmount : ($order->total ?? 0);
        $paymentMethod = $this->getPaymentMethodName($order->payment_method ?? '');

        $html .= "
        </tbody>
    </table>
    
    <div class='divider'></div>
    
    <div class='total'>
        <div>商品总额: ¥" . number_format($finalTotal, 2) . "</div>
        <div>配送费: ¥0.00</div>
        <div style='font-size: 16px; margin-top: 5px;'>实付金额: ¥" . number_format($finalTotal, 2) . "</div>
    </div>
    
    <div class='divider'></div>
    
    <div class='info'>
        <div class='info-item'>支付方式: {$paymentMethod}</div>
        <div class='info-item'>订单状态: " . $this->getOrderStatusName($order->status ?? '') . "</div>
    </div>
    
    <div class='footer'>
        <div>感谢您的惠顾！</div>
        <div>客服电话: 400-123-4567</div>
        <div>打印时间: " . date('Y-m-d H:i:s') . "</div>
    </div>
</div>";

        return $html;
    }

    /**
     * 生成配送单HTML（增强版 - 支持分页、优惠明细、实发数量）
     */
    protected function generateDeliveryTemplate($order, $options = [])
    {
        $isPrintPreview = $options['preview'] ?? false;
        $companyName = config('app.company_name', '天鑫商贸');
        
        try {
            $items = $order->items ?? collect([]);
            $itemsPerPage = 8; // 每页显示8行商品
            $totalPages = max(1, ceil($items->count() / $itemsPerPage));
            
            // 预先计算一次总计，避免重复计算
            $orderTotals = [
                'subtotal' => $order->subtotal ?? 0,
                'discount' => $order->discount ?? 0,
                'payment_discount' => $order->payment_discount ?? 0,
                'total' => $order->total ?? 0,
                'original_total' => $order->original_total ?? $order->total ?? 0,
            ];
            
            $allPagesHtml = '';
            
            for ($page = 1; $page <= $totalPages; $page++) {
                $startIndex = ($page - 1) * $itemsPerPage;
                $pageItems = $items->slice($startIndex, $itemsPerPage);
                $isLastPage = ($page === $totalPages);
                
                $allPagesHtml .= $this->generateDeliveryPageTemplate(
                    $order, 
                    $pageItems, 
                    $page, 
                    $totalPages, 
                    $isLastPage, 
                    $orderTotals,
                    $companyName
                );
                
                // 页面之间添加分页符（除了最后一页）
                if ($page < $totalPages) {
                    $allPagesHtml .= '<div style="page-break-after: always;"></div>';
                }
            }
            
            return $allPagesHtml;
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('配送单模板生成失败', [
                'order_id' => $order->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return '<div style="color: red; padding: 20px;">配送单生成失败: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
    }

    private function generateDeliveryPageTemplate($order, $pageItems, $currentPage, $totalPages, $isLastPage, $orderTotals, $companyName)
    {
        $html = "
        <div style='width: 190mm; min-height: 140mm; margin: 0; padding: 3mm; font-family: \"Microsoft YaHei\", sans-serif; font-size: 8px; line-height: 1.2; box-sizing: border-box; page-break-inside: avoid;'>
            <!-- 页眉 -->
            <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; border-bottom: 1px solid #000; padding-bottom: 4px;'>
                <div style='font-size: 13px; font-weight: bold;'>{$companyName}</div>
                <div style='font-size: 9px; text-align: right;'>
                    <div>配送单</div>
                    <div>第{$currentPage}页/共{$totalPages}页</div>
                </div>
            </div>
            
            <!-- 订单信息 -->
            <div style='display: flex; justify-content: space-between; margin-bottom: 6px; font-size: 7px;'>
                <div style='flex: 1;'>
                    <div><strong>订单号:</strong> " . htmlspecialchars($order->order_no ?? '') . "</div>
                    <div><strong>客户:</strong> " . htmlspecialchars($order->contact_name ?? '') . "</div>
                    <div><strong>电话:</strong> " . htmlspecialchars($order->contact_phone ?? '') . "</div>
                </div>
                <div style='flex: 1; text-align: right;'>
                    <div><strong>配送日期:</strong> " . ($order->delivery_date ? $order->delivery_date->format('Y-m-d') : '') . "</div>
                    <div><strong>配送时间:</strong> " . htmlspecialchars($order->delivery_time ?? '') . "</div>
                    <div><strong>支付方式:</strong> " . htmlspecialchars($this->getPaymentMethodName($order->payment_method ?? '')) . "</div>
                </div>
            </div>
            
            <!-- 配送地址 -->
            <div style='margin-bottom: 6px; font-size: 7px; border: 1px solid #ddd; padding: 3px;'>
                <strong>配送地址:</strong> " . htmlspecialchars($order->shipping_address ?? '') . "
            </div>
            
            <!-- 商品表格 -->
            <table style='width: 100%; border-collapse: collapse; margin-bottom: 6px; font-size: 7px;'>
                <thead>
                    <tr style='background-color: #f0f0f0; font-weight: bold;'>
                        <th style='border: 1px solid #000; padding: 2px; width: 20%;'>产品名称</th>
                        <th style='border: 1px solid #000; padding: 2px; width: 8%;'>订货数量</th>
                        <th style='border: 1px solid #000; padding: 2px; width: 6%;'>单位</th>
                        <th style='border: 1px solid #000; padding: 2px; width: 8%;'>单价</th>
                        <th style='border: 1px solid #000; padding: 2px; width: 9%;'>订货金额</th>
                        <th style='border: 1px solid #000; padding: 2px; width: 8%;'>实发数量</th>
                        <th style='border: 1px solid #000; padding: 2px; width: 9%;'>实发金额</th>
                        <th style='border: 1px solid #000; padding: 2px; width: 7%;'>优惠</th>
                        <th style='border: 1px solid #000; padding: 2px; width: 25%;'>备注</th>
                    </tr>
                </thead>
                <tbody>";

        // 商品行
        $pageSubtotal = 0;
        foreach ($pageItems as $item) {
            $productName = $item->product_name ?? ($item->product->name ?? '商品');
            
            // 获取单位信息 - 直接使用订单项的金额，不重新计算
            $unit = '';
            if (isset($item->unit) && $item->unit) {
                $unit = $item->unit->symbol ?? $item->unit->name ?? '';
            } elseif (isset($item->unit_symbol)) {
                $unit = $item->unit_symbol;
            } elseif (isset($item->unit_name)) {
                $unit = $item->unit_name;
            }
            
            // 直接使用订单项的数据
            $quantity = $item->quantity ?? 0;
            $price = $item->price ?? 0;
            $itemTotal = $item->total ?? 0; // 直接使用订单项的总金额
            
            $pageSubtotal += $itemTotal;
            
            $html .= "
                <tr>
                    <td style='border: 1px solid #000; padding: 2px; font-size: 6px;'>" . htmlspecialchars($productName) . "</td>
                    <td style='border: 1px solid #000; padding: 2px; text-align: center;'>{$quantity}</td>
                    <td style='border: 1px solid #000; padding: 2px; text-align: center;'>" . htmlspecialchars($unit) . "</td>
                    <td style='border: 1px solid #000; padding: 2px; text-align: right;'>¥" . number_format($price, 2) . "</td>
                    <td style='border: 1px solid #000; padding: 2px; text-align: right;'>¥" . number_format($itemTotal, 2) . "</td>
                    <td style='border: 1px solid #000; padding: 2px; text-align: center;'>_____</td>
                    <td style='border: 1px solid #000; padding: 2px; text-align: right;'>_____</td>
                    <td style='border: 1px solid #000; padding: 2px; text-align: right;'>-</td>
                    <td style='border: 1px solid #000; padding: 2px;'></td>
                </tr>";
        }
        
        // 填充空行到8行
        $currentRows = $pageItems->count();
        for ($i = $currentRows; $i < 8; $i++) {
            $html .= "
                <tr>
                    <td style='border: 1px solid #000; padding: 2px; height: 12px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 2px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 2px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 2px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 2px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 2px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 2px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 2px;'>&nbsp;</td>
                    <td style='border: 1px solid #000; padding: 2px;'>&nbsp;</td>
                </tr>";
        }
        
        // 本页小计行
        $html .= "
                <tr style='background-color: #f9f9f9; font-weight: bold;'>
                    <td colspan='4' style='border: 1px solid #000; padding: 2px; text-align: right;'>本页小计:</td>
                    <td style='border: 1px solid #000; padding: 2px; text-align: right;'>¥" . number_format($pageSubtotal, 2) . "</td>
                    <td colspan='4' style='border: 1px solid #000; padding: 2px;'>&nbsp;</td>
                </tr>
            </tbody>
        </table>";
        
        // 只在最后一页显示汇总信息
        if ($isLastPage) {
            // 计算优惠信息
            $totalDiscount = $orderTotals['discount'] + $orderTotals['payment_discount'];
            $hasDiscount = $totalDiscount > 0;
            
            $html .= "
            <!-- 汇总信息 -->
            <div style='margin-top: 8px; font-size: 7px;'>
                <div style='display: flex; justify-content: space-between; border-top: 2px solid #000; padding-top: 4px;'>
                    <div style='flex: 1;'>
                        <div><strong>商品总计:</strong> ¥" . number_format($orderTotals['subtotal'], 2) . "</div>";
            
            if ($hasDiscount) {
                $html .= "
                        <div><strong>优惠金额:</strong> -¥" . number_format($totalDiscount, 2) . "</div>";
            }
            
            $html .= "
                        <div style='font-size: 9px; margin-top: 2px;'><strong>应收金额:</strong> ¥" . number_format($orderTotals['total'], 2) . "</div>
                    </div>
                    <div style='flex: 1; text-align: right;'>
                        <div><strong>实收金额:</strong> _____________</div>
                        <div style='margin-top: 8px;'><strong>客户签名:</strong> _____________</div>
                    </div>
                </div>
            </div>";
        }
        
        $html .= "
            <!-- 页脚 -->
            <div style='margin-top: 8px; font-size: 6px; color: #666; text-align: center;'>
                配送员签名: _____________ &nbsp;&nbsp;&nbsp; 配送时间: _____________ &nbsp;&nbsp;&nbsp; 客户确认: _____________
            </div>
        </div>";
        
        return $html;
    }

    /**
     * 获取支付方式名称
     */
    protected function getPaymentMethodName(string $method): string
    {
        $methods = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'cash' => '现金支付',
            'bank' => '银行转账',
            'cod' => '货到付款'
        ];

        return $methods[$method] ?? $method;
    }

    /**
     * 获取订单状态名称
     */
    protected function getOrderStatusName(string $status): string
    {
        $statuses = [
            'pending' => '待付款',
            'paid' => '已付款',
            'shipped' => '已发货',
            'delivered' => '已送达',
            'cancelled' => '已取消'
        ];

        return $statuses[$status] ?? $status;
    }

    /**
     * 生成预览URL
     */
    protected function generatePreviewUrl(string $html): string
    {
        // 这里可以将HTML保存到临时文件或缓存中，返回预览URL
        $filename = 'print_preview_' . time() . '_' . uniqid() . '.html';
        $path = storage_path('app/temp/' . $filename);
        
        // 确保目录存在
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }
        
        file_put_contents($path, $html);
        
        return url('api/print/preview/' . $filename);
    }

    /**
     * 获取打印机列表
     */
    public function getPrinters(): array
    {
        return $this->driver->getPrinters();
    }

    /**
     * 设置默认打印机
     */
    public function setDefaultPrinter(string $printerName): bool
    {
        return $this->driver->setDefaultPrinter($printerName);
    }

    /**
     * 检查打印机状态
     */
    public function getPrinterStatus(string $printerName): array
    {
        return $this->driver->getPrinterStatus($printerName);
    }

    /**
     * 获取当前驱动名称
     */
    protected function getCurrentDriverName(): string
    {
        $driverClass = get_class($this->driver);
        
        if (str_contains($driverClass, 'CLodopDriver')) {
            return PrintRecord::DRIVER_CLODOP;
        } elseif (str_contains($driverClass, 'BrowserDriver')) {
            return PrintRecord::DRIVER_BROWSER;
        }
        
        return PrintRecord::DRIVER_CLODOP; // 默认
    }

    /**
     * 打印完成回调
     */
    public function markPrintCompleted(int $printRecordId): bool
    {
        try {
            $printRecord = PrintRecord::findOrFail($printRecordId);
            return $printRecord->markAsCompleted();
        } catch (\Exception $e) {
            Log::error('Mark print completed failed', [
                'print_record_id' => $printRecordId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 打印失败回调
     */
    public function markPrintFailed(int $printRecordId, string $errorMessage = ''): bool
    {
        try {
            $printRecord = PrintRecord::findOrFail($printRecordId);
            return $printRecord->markAsFailed($errorMessage);
        } catch (\Exception $e) {
            Log::error('Mark print failed failed', [
                'print_record_id' => $printRecordId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取打印历史
     */
    public function getPrintHistory($printable, string $printType = null): \Illuminate\Database\Eloquent\Collection
    {
        return PrintRecord::getPrintHistory($printable, $printType);
    }

    /**
     * 检查是否已打印过
     */
    public function isPrinted($printable, string $printType = null): bool
    {
        return PrintRecord::isPrinted($printable, $printType);
    }

    /**
     * 获取打印统计
     */
    public function getPrintStats($printable, string $printType = null): array
    {
        $totalCount = PrintRecord::countPrints($printable, $printType);
        $lastRecord = PrintRecord::getLastPrintRecord($printable, $printType);
        
        return [
            'total_prints' => $totalCount,
            'last_printed_at' => $lastRecord ? $lastRecord->printed_at : null,
            'last_printed_by' => $lastRecord && $lastRecord->printedBy ? $lastRecord->printedBy->name : null,
            'is_printed' => $totalCount > 0
        ];
    }

    /**
     * 获取待打印列表
     */
    public function getPendingPrints(): \Illuminate\Database\Eloquent\Collection
    {
        return PrintRecord::pending()
            ->with(['printable', 'printedBy'])
            ->orderBy('created_at', 'asc')
            ->get();
    }

    /**
     * 重新打印
     */
    public function reprintOrder($order, array $options = []): array
    {
        // 检查是否允许重复打印
        $allowReprint = $options['allow_reprint'] ?? true;
        
        if (!$allowReprint && $this->isPrinted($order, $options['type'] ?? null)) {
            return [
                'success' => false,
                'error' => '该订单已经打印过，不允许重复打印'
            ];
        }

        // 添加重印标识
        $options['is_reprint'] = true;
        
        return $this->printOrder($order, $options);
    }
} 