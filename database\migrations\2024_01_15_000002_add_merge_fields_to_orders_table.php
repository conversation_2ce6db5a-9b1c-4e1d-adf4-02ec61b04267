<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // 合并状态标识
            $table->boolean('is_merged')->default(false)->comment('是否为合并订单');
            $table->boolean('is_merged_from')->default(false)->comment('是否被合并的原订单');
            
            // 合并关联信息
            $table->unsignedBigInteger('merged_to_order_id')->nullable()->comment('合并到的订单ID');
            $table->unsignedBigInteger('order_merge_id')->nullable()->comment('合并记录ID');
            
            // 合并前的原始信息（用于撤销）
            $table->json('pre_merge_data')->nullable()->comment('合并前数据快照');
            
            // 合并优惠信息
            $table->json('merge_discount_details')->nullable()->comment('合并优惠明细');
            $table->decimal('merge_savings', 10, 2)->default(0)->comment('合并节省金额');
            
            // 索引
            $table->index(['is_merged', 'created_at']);
            $table->index(['is_merged_from', 'merged_to_order_id']);
            $table->index(['order_merge_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex(['is_merged', 'created_at']);
            $table->dropIndex(['is_merged_from', 'merged_to_order_id']);
            $table->dropIndex(['order_merge_id']);
            
            $table->dropColumn([
                'is_merged',
                'is_merged_from', 
                'merged_to_order_id',
                'order_merge_id',
                'pre_merge_data',
                'merge_discount_details',
                'merge_savings'
            ]);
        });
    }
}; 