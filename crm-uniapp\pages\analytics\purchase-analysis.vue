<template>
	<view class="purchase-analysis">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
				<view class="nav-left" @tap="goBack">
					<text class="nav-icon">←</text>
				</view>
			<view class="nav-center">
				<text class="nav-title">购买分析</text>
				</view>
			<view class="nav-right" @tap="refreshData">
				<text class="nav-icon">⟳</text>
			</view>
		</view>

		<!-- 时间筛选器 -->
		<view class="time-filter">
			<view class="filter-container">
				<view 
					class="filter-item" 
					v-for="(period, index) in timePeriods" 
					:key="index"
					:class="{ active: selectedPeriod === period.value }"
					@tap="selectPeriod(period.value)"
				>
					<text class="filter-text">{{ period.label }}</text>
				</view>
			</view>
		</view>

		<!-- 核心指标卡片 -->
		<view class="metrics-section">
			<view class="metrics-grid">
				<view class="metric-card primary">
					<view class="metric-icon">📊</view>
					<view class="metric-content">
						<text class="metric-value">{{ overview.totalOrders || 0 }}</text>
						<text class="metric-label">总订单数</text>
					</view>
					<view class="metric-trend up">
						<text class="trend-text">+12%</text>
			</view>
				</view>
				
				<view class="metric-card success">
					<view class="metric-icon">💰</view>
					<view class="metric-content">
						<text class="metric-value">¥{{ formatNumber(overview.totalAmount || 0) }}</text>
						<text class="metric-label">总销售额</text>
					</view>
					<view class="metric-trend up">
						<text class="trend-text">+8%</text>
				</view>
				</view>
				
				<view class="metric-card warning">
					<view class="metric-icon">🎯</view>
					<view class="metric-content">
						<text class="metric-value">¥{{ overview.avgOrderValue || 0 }}</text>
						<text class="metric-label">平均客单价</text>
				</view>
					<view class="metric-trend down">
						<text class="trend-text">-3%</text>
			</view>
		</view>

				<view class="metric-card info">
					<view class="metric-icon">👥</view>
					<view class="metric-content">
						<text class="metric-value">{{ overview.activeCustomers || 0 }}</text>
						<text class="metric-label">活跃客户</text>
					</view>
					<view class="metric-trend up">
						<text class="trend-text">+15%</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 分析内容区域 -->
		<view class="analysis-content">
			<!-- 购买频次分析 -->
			<view class="analysis-card">
				<view class="card-header">
					<view class="header-left">
						<text class="card-title">购买频次分布</text>
						<text class="card-subtitle">客户购买行为分析</text>
			</view>
					<view class="header-right">
						<text class="more-btn">详情</text>
					</view>
				</view>
				<view class="frequency-chart">
					<view class="chart-container">
						<view class="frequency-item" v-for="(item, index) in frequencyData" :key="index">
							<view class="frequency-label">
								<text class="label-text">{{ item.label }}</text>
								<text class="label-count">{{ item.count }}人</text>
							</view>
							<view class="frequency-bar">
								<view class="bar-track">
									<view 
										class="bar-fill" 
										:style="{ 
											width: item.percentage + '%',
											background: getFrequencyColor(index)
										}"
									></view>
								</view>
								<text class="percentage-text">{{ item.percentage }}%</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 金额分布分析 -->
			<view class="analysis-card">
				<view class="card-header">
					<view class="header-left">
						<text class="card-title">消费金额分布</text>
						<text class="card-subtitle">不同价格区间的客户占比</text>
		</view>
			</view>
				<view class="amount-distribution">
					<view class="distribution-grid">
				<view 
							class="distribution-item" 
							v-for="(item, index) in amountDistribution" 
					:key="index"
							:style="{ background: getAmountGradient(index) }"
				>
							<view class="distribution-content">
								<text class="range-text">{{ item.range }}</text>
								<text class="count-text">{{ item.count }}人</text>
								<text class="percentage-text">{{ item.percentage }}%</text>
				</view>
			</view>
					</view>
				</view>
			</view>

			<!-- 复购率分析 -->
			<view class="analysis-card">
				<view class="card-header">
					<view class="header-left">
						<text class="card-title">复购率分析</text>
						<text class="card-subtitle">客户忠诚度指标</text>
		</view>
			</view>
				<view class="repurchase-analysis">
					<view class="repurchase-main">
						<view class="repurchase-circle">
							<view class="circle-progress" :style="{ background: getCircleGradient(repurchaseData.rate) }">
						<view class="circle-inner">
									<text class="circle-rate">{{ repurchaseData.rate }}%</text>
							<text class="circle-label">复购率</text>
						</view>
					</view>
				</view>
						<view class="repurchase-stats">
							<view class="stat-row">
								<view class="stat-item">
									<text class="stat-value">{{ repurchaseData.firstTime }}</text>
									<text class="stat-label">首购客户</text>
								</view>
								<view class="stat-item">
									<text class="stat-value">{{ repurchaseData.repeat }}</text>
									<text class="stat-label">复购客户</text>
								</view>
							</view>
							<view class="stat-row">
								<view class="stat-item full">
									<text class="stat-value">{{ repurchaseData.avgInterval }}天</text>
									<text class="stat-label">平均复购间隔</text>
								</view>
					</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 客户价值分层 -->
			<view class="analysis-card">
				<view class="card-header">
					<view class="header-left">
						<text class="card-title">客户价值分层</text>
						<text class="card-subtitle">基于消费行为的客户分类</text>
					</view>
			</view>
				<view class="value-segments">
					<view class="segments-list">
						<view 
							class="segment-card" 
							v-for="(segment, index) in valueSegments" 
							:key="index"
							:class="segment.level"
						>
							<view class="segment-header">
								<view class="segment-icon">
									<text class="icon-emoji">{{ segment.icon }}</text>
					</view>
					<view class="segment-info">
						<text class="segment-name">{{ segment.name }}</text>
						<text class="segment-desc">{{ segment.description }}</text>
					</view>
							</view>
							<view class="segment-metrics">
								<view class="metric-item">
									<text class="metric-number">{{ segment.count }}</text>
									<text class="metric-unit">人</text>
								</view>
								<view class="metric-item">
									<text class="metric-number">¥{{ formatNumber(segment.totalAmount) }}</text>
									<text class="metric-unit">总消费</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="loading">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">数据加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
import analyticsApi from '../../api/analytics.js'
import { getPeriodDateRange } from '../../utils/dateUtils.js'

export default {
	data() {
		return {
			loading: false,
			selectedPeriod: 'last_30_days',
			timePeriods: [
				{ label: '7天', value: 'last_7_days' },
				{ label: '30天', value: 'last_30_days' },
				{ label: '90天', value: 'last_90_days' }
			],
			overview: {},
			frequencyData: [],
			amountDistribution: [],
			repurchaseData: {},
			valueSegments: []
		}
	},
	
	onLoad() {
		this.loadPurchaseAnalysis()
	},
	
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 刷新数据
		async refreshData() {
			await this.loadPurchaseAnalysis()
			uni.showToast({
				title: '刷新成功',
				icon: 'success',
				duration: 1500
			})
		},
		
		// 选择时间周期
		selectPeriod(period) {
			this.selectedPeriod = period
			this.loadPurchaseAnalysis()
		},
		
		// 格式化数字
		formatNumber(num) {
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + 'w'
			} else if (num >= 1000) {
				return (num / 1000).toFixed(1) + 'k'
			}
			return num.toString()
		},
		
		// 获取频次颜色
		getFrequencyColor(index) {
			const colors = [
				'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
				'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
				'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
				'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
			]
			return colors[index % colors.length]
		},
		
		// 获取金额分布渐变
		getAmountGradient(index) {
			const gradients = [
				'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
				'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
				'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
				'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
				'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
			]
			return gradients[index % gradients.length]
		},
		
		// 获取圆形进度条渐变
		getCircleGradient(rate) {
			return `conic-gradient(#4facfe 0deg ${rate * 3.6}deg, #f0f0f0 ${rate * 3.6}deg 360deg)`
		},
		
		// 加载购买分析数据
		async loadPurchaseAnalysis() {
			this.loading = true
			try {
				// 将period转换为日期范围
				const dateRange = getPeriodDateRange(this.selectedPeriod)
				const params = {
					start_date: dateRange.start_date,
					end_date: dateRange.end_date
				}
				
				const response = await analyticsApi.getPurchaseAnalysis(params)
				
				if (response && response.code === 0 && response.data) {
					const data = response.data
				
				// 处理概览数据
					this.overview = data.overview || {
						totalOrders: 0,
						totalAmount: 0,
						avgOrderValue: 0,
						activeCustomers: 0
					}
				
				// 处理频次数据
					this.frequencyData = data.frequency || []
				
				// 处理金额分布
					this.amountDistribution = data.amount_distribution || []
				
				// 处理复购数据
					this.repurchaseData = data.repurchase || {
						rate: 0,
						firstTime: 0,
						repeat: 0,
						avgInterval: 0
					}
				
				// 处理价值分层
					this.valueSegments = data.value_segments || []
				} else {
					throw new Error(response?.message || '数据格式错误')
				}
				
			} catch (error) {
				console.error('加载购买分析数据失败:', error)
				uni.showToast({
					title: '加载数据失败',
					icon: 'none',
					duration: 2000
				})
				
				// 设置默认空数据
				this.overview = {
					totalOrders: 0,
					totalAmount: 0,
					avgOrderValue: 0,
					activeCustomers: 0
				}
				this.frequencyData = []
				this.amountDistribution = []
				this.repurchaseData = {
					rate: 0,
					firstTime: 0,
					repeat: 0,
					avgInterval: 0
				}
				this.valueSegments = []
			} finally {
				this.loading = false
			}
		}
	}
}
</script>

<style scoped>
.purchase-analysis {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏 */
.nav-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	background: #ffffff;
	border-bottom: 2rpx solid #f0f0f0;
	position: sticky;
	top: 0;
	z-index: 100;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 40rpx;
	background: #f8f9fa;
}

.nav-icon {
	font-size: 32rpx;
	color: #666666;
}

.nav-center {
	flex: 1;
	text-align: center;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
}

/* 时间筛选器 */
.time-filter {
	background: #ffffff;
	padding: 24rpx 32rpx;
	margin-bottom: 16rpx;
}

.filter-container {
	display: flex;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 8rpx;
}

.filter-item {
	flex: 1;
	padding: 16rpx 24rpx;
	text-align: center;
	border-radius: 8rpx;
	transition: all 0.3s ease;
}

.filter-item.active {
	background: #007AFF;
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.filter-text {
	font-size: 28rpx;
	color: #666666;
}

.filter-item.active .filter-text {
	color: #ffffff;
	font-weight: 600;
}

/* 核心指标卡片 */
.metrics-section {
	padding: 0 32rpx 16rpx;
}

.metrics-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
}

.metric-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx 24rpx;
	position: relative;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.metric-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 8rpx;
}

.metric-card.primary::before {
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.metric-card.success::before {
	background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
}

.metric-card.warning::before {
	background: linear-gradient(90deg, #fa709a 0%, #fee140 100%);
}

.metric-card.info::before {
	background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
}

.metric-icon {
	font-size: 48rpx;
	margin-bottom: 16rpx;
}

.metric-content {
	margin-bottom: 16rpx;
}

.metric-value {
	display: block;
	font-size: 48rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.metric-label {
	font-size: 24rpx;
	color: #666666;
}

.metric-trend {
	position: absolute;
	top: 24rpx;
	right: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 20rpx;
}

.metric-trend.up {
	background: rgba(67, 233, 123, 0.1);
	color: #43e97b;
}

.metric-trend.down {
	background: rgba(255, 107, 107, 0.1);
	color: #ff6b6b;
}

/* 分析内容区域 */
.analysis-content {
	padding: 0 32rpx 120rpx;
}

.analysis-card {
	background: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.card-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 32rpx 24rpx;
	border-bottom: 2rpx solid #f8f9fa;
}

.header-left {
	flex: 1;
}

.card-title {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.card-subtitle {
	font-size: 24rpx;
	color: #999999;
}

.more-btn {
	font-size: 24rpx;
	color: #007AFF;
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
	background: rgba(0, 122, 255, 0.1);
}

/* 购买频次图表 */
.frequency-chart {
	padding: 32rpx;
}

.frequency-item {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;
}

.frequency-item:last-child {
	margin-bottom: 0;
}

.frequency-label {
	width: 200rpx;
	margin-right: 24rpx;
}

.label-text {
	display: block;
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 4rpx;
}

.label-count {
	font-size: 24rpx;
	color: #666666;
}

.frequency-bar {
	flex: 1;
	display: flex;
	align-items: center;
}

.bar-track {
	flex: 1;
	height: 16rpx;
	background: #f0f0f0;
	border-radius: 8rpx;
	margin-right: 16rpx;
	overflow: hidden;
}

.bar-fill {
	height: 100%;
	border-radius: 8rpx;
	transition: width 0.8s ease;
}

.percentage-text {
	font-size: 24rpx;
	color: #666666;
	width: 80rpx;
	text-align: right;
}

/* 金额分布 */
.amount-distribution {
	padding: 32rpx;
}

.distribution-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
}

.distribution-item {
	border-radius: 12rpx;
	padding: 32rpx 24rpx;
	text-align: center;
	position: relative;
	overflow: hidden;
}

.distribution-content {
	position: relative;
	z-index: 2;
}

.range-text {
	display: block;
	font-size: 24rpx;
	color: #ffffff;
	margin-bottom: 8rpx;
	opacity: 0.9;
}

.count-text {
	display: block;
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 4rpx;
}

.percentage-text {
	font-size: 20rpx;
	color: #ffffff;
	opacity: 0.8;
}

/* 复购率分析 */
.repurchase-analysis {
	padding: 32rpx;
}

.repurchase-main {
	display: flex;
	align-items: center;
}

.repurchase-circle {
	margin-right: 40rpx;
}

.circle-progress {
	width: 200rpx;
	height: 200rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.circle-inner {
	width: 140rpx;
	height: 140rpx;
	background: #ffffff;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.circle-rate {
	font-size: 48rpx;
	font-weight: 700;
	color: #007AFF;
	margin-bottom: 8rpx;
}

.circle-label {
	font-size: 24rpx;
	color: #666666;
}

.repurchase-stats {
	flex: 1;
}

.stat-row {
	display: flex;
	margin-bottom: 24rpx;
}

.stat-row:last-child {
	margin-bottom: 0;
}

.stat-item {
	flex: 1;
	text-align: center;
	padding: 24rpx 16rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-right: 16rpx;
}

.stat-item:last-child {
	margin-right: 0;
}

.stat-item.full {
	margin-right: 0;
}

.stat-value {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666666;
}

/* 客户价值分层 */
.value-segments {
	padding: 32rpx;
}

.segments-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.segment-card {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 24rpx;
	border-radius: 16rpx;
	border: 2rpx solid #f0f0f0;
	transition: all 0.3s ease;
}

.segment-card.high {
	border-color: #ff6b6b;
	background: linear-gradient(135deg, rgba(255, 107, 107, 0.05) 0%, rgba(238, 90, 36, 0.05) 100%);
}

.segment-card.medium {
	border-color: #ffa726;
	background: linear-gradient(135deg, rgba(255, 167, 38, 0.05) 0%, rgba(255, 152, 0, 0.05) 100%);
}

.segment-card.low {
	border-color: #66bb6a;
	background: linear-gradient(135deg, rgba(102, 187, 106, 0.05) 0%, rgba(76, 175, 80, 0.05) 100%);
}

.segment-header {
	display: flex;
	align-items: center;
	flex: 1;
}

.segment-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
	background: #f8f9fa;
}

.icon-emoji {
	font-size: 36rpx;
}

.segment-info {
	flex: 1;
}

.segment-name {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.segment-desc {
	font-size: 24rpx;
	color: #666666;
}

.segment-metrics {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.metric-item {
	display: flex;
	align-items: baseline;
	margin-bottom: 8rpx;
}

.metric-item:last-child {
	margin-bottom: 0;
}

.metric-number {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-right: 8rpx;
}

.metric-unit {
	font-size: 20rpx;
	color: #999999;
}

/* 加载状态 */
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.loading-spinner {
	width: 80rpx;
	height: 80rpx;
	border: 6rpx solid #f0f0f0;
	border-top: 6rpx solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 24rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}
</style> 