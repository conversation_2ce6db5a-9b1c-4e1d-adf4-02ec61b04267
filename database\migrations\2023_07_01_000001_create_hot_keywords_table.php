<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('hot_keywords', function (Blueprint $table) {
            $table->id();
            $table->string('keyword', 255)->unique()->comment('关键词');
            $table->integer('weight')->default(0)->comment('权重');
            $table->boolean('highlight')->default(false)->comment('是否高亮');
            $table->boolean('is_manual')->default(false)->comment('是否手动添加');
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->timestamps();
            
            // 添加索引
            $table->index('weight');
            $table->index(['is_active', 'weight']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hot_keywords');
    }
}; 