<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('crm_agents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained()->onDelete('cascade')->comment('关联的员工ID');
            $table->json('service_area')->nullable()->comment('服务区域（支持多个）');
            $table->integer('max_clients')->default(50)->comment('最大客户数量');
            $table->decimal('performance_rating', 3, 2)->default(5.00)->comment('绩效评分');
            $table->enum('status', ['available', 'busy', 'offline'])->default('available')->comment('状态：可用、忙碌、离线');
            $table->string('specialty')->nullable()->comment('专长领域');
            $table->integer('monthly_target')->default(0)->comment('月度目标金额');
            $table->integer('clients_count')->default(0)->comment('当前客户数');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crm_agents');
    }
}; 