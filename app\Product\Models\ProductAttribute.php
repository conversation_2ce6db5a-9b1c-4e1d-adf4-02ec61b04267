<?php

namespace App\Product\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductAttribute extends Model
{
    use HasFactory;

    /**
     * 表名
     */
    protected $table = 'product_attributes';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'name',
        'key',
        'type',
        'options',
        'unit',
        'placeholder',
        'description',
        'is_required',
        'is_searchable',
        'show_in_list',
        'sort',
        'status',
    ];

    /**
     * 类型转换
     */
    protected $casts = [
        'options' => 'array',
        'is_required' => 'boolean',
        'is_searchable' => 'boolean',
        'show_in_list' => 'boolean',
        'sort' => 'integer',
        'status' => 'boolean',
    ];

    /**
     * 属性类型常量
     */
    const TYPE_TEXT = 'text';
    const TYPE_NUMBER = 'number';
    const TYPE_SELECT = 'select';
    const TYPE_MULTI_SELECT = 'multi_select';
    const TYPE_BOOLEAN = 'boolean';
    const TYPE_DATE = 'date';
    const TYPE_TEXTAREA = 'textarea';

    /**
     * 获取所有属性类型
     */
    public static function getTypes()
    {
        return [
            self::TYPE_TEXT => '文本',
            self::TYPE_NUMBER => '数字',
            self::TYPE_SELECT => '单选',
            self::TYPE_MULTI_SELECT => '多选',
            self::TYPE_BOOLEAN => '布尔值',
            self::TYPE_DATE => '日期',
            self::TYPE_TEXTAREA => '长文本',
        ];
    }

    /**
     * 获取该属性的所有值
     */
    public function values()
    {
        return $this->hasMany(ProductAttributeValue::class, 'attribute_id');
    }

    /**
     * 作用域：仅获取启用的属性
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * 作用域：按排序获取
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 作用域：可搜索的属性
     */
    public function scopeSearchable($query)
    {
        return $query->where('is_searchable', true);
    }

    /**
     * 作用域：在列表中显示的属性
     */
    public function scopeShowInList($query)
    {
        return $query->where('show_in_list', true);
    }

    /**
     * 获取属性类型的中文名称
     */
    public function getTypeNameAttribute()
    {
        $types = self::getTypes();
        return $types[$this->type] ?? $this->type;
    }

    /**
     * 检查是否为选择类型
     */
    public function isSelectType()
    {
        return in_array($this->type, [self::TYPE_SELECT, self::TYPE_MULTI_SELECT]);
    }

    /**
     * 格式化属性值用于显示
     */
    public function formatValue($value)
    {
        switch ($this->type) {
            case self::TYPE_BOOLEAN:
                return $value ? '是' : '否';
            case self::TYPE_SELECT:
                if ($this->options && is_array($this->options)) {
                    return $this->options[$value] ?? $value;
                }
                return $value;
            case self::TYPE_MULTI_SELECT:
                if ($this->options && is_array($this->options) && is_array($value)) {
                    $formatted = [];
                    foreach ($value as $v) {
                        $formatted[] = $this->options[$v] ?? $v;
                    }
                    return implode(', ', $formatted);
                }
                return is_array($value) ? implode(', ', $value) : $value;
            case self::TYPE_NUMBER:
                return $value . ($this->unit ? ' ' . $this->unit : '');
            default:
                return $value;
        }
    }

    /**
     * 验证属性值是否有效
     */
    public function validateValue($value)
    {
        switch ($this->type) {
            case self::TYPE_NUMBER:
                return is_numeric($value);
            
            case self::TYPE_BOOLEAN:
                return is_bool($value) || in_array($value, [0, 1, '0', '1', 'true', 'false']);
            
            case self::TYPE_SELECT:
                if (!$this->options) return true;
                return in_array($value, $this->options);
            
            case self::TYPE_MULTI_SELECT:
                if (!$this->options || !is_array($value)) return false;
                return empty(array_diff($value, $this->options));
            
            case self::TYPE_DATE:
                return strtotime($value) !== false;
            
            default:
                return true;
        }
    }

    /**
     * 获取启用的属性列表
     */
    public static function getActiveAttributes()
    {
        return self::active()->ordered()->get();
    }
} 