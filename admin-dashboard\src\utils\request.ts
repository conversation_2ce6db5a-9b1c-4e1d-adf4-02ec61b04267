import { ElMessage } from 'element-plus'
import { Storage } from './storage'

// 基础配置
const BASE_URL = 'http://localhost:8000/api'
const TIMEOUT = 10000

// 请求拦截器
const requestInterceptor = (url: string, options: RequestInit = {}) => {
  // 自动添加token到请求头
  const token = Storage.getToken()
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(options.headers as Record<string, string> || {}),
  }
  
  if (token) {
    headers.Authorization = `Bearer ${token}`
  }
  
  return {
    ...options,
    headers,
  }
}

// 响应拦截器
const responseInterceptor = async (response: Response, originalUrl: string, originalOptions: RequestInit) => {
  // token过期处理
  if (response.status === 401) {
    const refreshTokenValue = Storage.getRefreshToken()
    
    if (!refreshTokenValue) {
      // 没有刷新token，直接跳转登录
      Storage.clearAuth()
      window.location.href = '/login'
      throw new Error('未登录')
    }
    
    try {
      // 模拟刷新token成功
      const newToken = 'new-token-' + Date.now()
      Storage.setToken(newToken)
      
      // 重新发送原请求
      const newHeaders: Record<string, string> = {
        ...(originalOptions.headers as Record<string, string> || {}),
        Authorization: `Bearer ${newToken}`
      }
      
      const retryResponse = await fetch(originalUrl, {
        ...originalOptions,
        headers: newHeaders
      })
      
      return retryResponse
      
    } catch (refreshError) {
      // 刷新token失败，清除认证信息并跳转登录
      Storage.clearAuth()
      window.location.href = '/login'
      ElMessage.error('登录已过期，请重新登录')
      throw refreshError
    }
  }
  
  return response
}

// 封装的请求方法
const request = async (url: string, options: RequestInit = {}) => {
  const fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`
  const requestOptions = requestInterceptor(fullUrl, options)
  
  try {
    // 添加超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT)
    
    const response = await fetch(fullUrl, {
      ...requestOptions,
      signal: controller.signal
    })
    
    clearTimeout(timeoutId)
    
    // 响应拦截处理
    const finalResponse = await responseInterceptor(response, fullUrl, requestOptions)
    
    // 检查响应状态
    if (!finalResponse.ok) {
      const errorData = await finalResponse.json().catch(() => ({}))
      const message = errorData.message || `请求失败: ${finalResponse.status}`
      ElMessage.error(message)
      throw new Error(message)
    }
    
    return finalResponse
    
  } catch (error: any) {
    if (error.name === 'AbortError') {
      ElMessage.error('请求超时')
      throw new Error('请求超时')
    }
    
    if (!error.message.includes('登录')) {
      ElMessage.error(error.message || '网络请求失败')
    }
    
    throw error
  }
}

// 便捷方法
export const get = (url: string, options?: RequestInit) => {
  return request(url, { ...options, method: 'GET' })
}

export const post = (url: string, data?: any, options?: RequestInit) => {
  return request(url, {
    ...options,
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined
  })
}

export const put = (url: string, data?: any, options?: RequestInit) => {
  return request(url, {
    ...options,
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined
  })
}

export const del = (url: string, options?: RequestInit) => {
  return request(url, { ...options, method: 'DELETE' })
}

export default request 