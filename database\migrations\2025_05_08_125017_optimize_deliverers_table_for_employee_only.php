<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * 运行迁移
     * 优化配送员表，使其仅支持员工配送员类型
     */
    public function up(): void
    {
        // 首先修改user_id字段，允许为NULL
        Schema::table('deliverers', function (Blueprint $table) {
            $table->foreignId('user_id')->nullable()->change();
        });
        
        Schema::table('deliverers', function (Blueprint $table) {
            // 检查employee_id字段是否已存在
            if (!Schema::hasColumn('deliverers', 'employee_id')) {
                // 添加employee_id字段并设置外键关联
                $table->foreignId('employee_id')->nullable()->after('user_id')
                    ->constrained('employees')->onDelete('cascade');
            }
            
            // 检查type字段是否存在
            if (!Schema::hasColumn('deliverers', 'type')) {
                // 添加type字段并设置默认值为'employee'
                $table->string('type')->default('employee')->after('employee_id')
                    ->comment('配送员类型，固定为employee');
            } else {
                // 更新所有记录的type字段为'employee'
                DB::table('deliverers')->update(['type' => 'employee']);
            }
            
            // 可选：添加索引以提高查询性能
            $table->index('type');
            $table->index('status');
        });
        
        // 迁移原有的用户与配送员关联
        // 从用户表获取已经关联的配送员，为其创建员工记录并关联
        $this->migrateUserDelivererRelationships();
    }

    /**
     * 迁移用户与配送员的关联关系
     * 为直接关联到用户的配送员创建对应的员工记录
     */
    private function migrateUserDelivererRelationships()
    {
        $deliverers = DB::table('deliverers')
                        ->whereNotNull('user_id')
                        ->whereNull('employee_id')
                        ->get();
                        
        foreach ($deliverers as $deliverer) {
            // 检查用户是否存在
            $user = DB::table('users')->find($deliverer->user_id);
            if (!$user) continue;
            
            // 检查此用户是否已有员工记录
            $employee = DB::table('employees')->where('user_id', $user->id)->first();
            
            // 如果没有员工记录，为用户创建一个员工记录
            if (!$employee) {
                $employeeId = DB::table('employees')->insertGetId([
                    'user_id' => $user->id,
                    'name' => $user->name ?? '配送员-'.$user->id,
                    'username' => 'deliverer_'.$user->id,
                    'role' => 'delivery',
                    'phone' => $user->phone ?? null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            } else {
                $employeeId = $employee->id;
            }
            
            // 更新配送员记录，关联到员工
            DB::table('deliverers')
                ->where('id', $deliverer->id)
                ->update([
                    'employee_id' => $employeeId,
                    'type' => 'employee',
                    'updated_at' => now()
                ]);
        }
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::table('deliverers', function (Blueprint $table) {
            // 移除索引
            $table->dropIndex(['type']);
            $table->dropIndex(['status']);
            
            // 如果存在employee_id字段，则移除外键和字段
            if (Schema::hasColumn('deliverers', 'employee_id')) {
                $table->dropForeign(['employee_id']);
                $table->dropColumn('employee_id');
            }
            
            // 保留type字段，但不回滚其值
        });
    }
};
