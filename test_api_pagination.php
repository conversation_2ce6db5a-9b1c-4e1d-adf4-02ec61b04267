<?php

/**
 * API分页参数测试脚本
 * 模拟前端请求，测试分页参数传递
 */

require_once 'vendor/autoload.php';

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔗 API分页参数测试脚本\n";
echo "====================\n\n";

try {
    // 模拟不同的API请求参数
    $testCases = [
        [
            'name' => '默认参数（无limit）',
            'params' => [
                'page' => 1,
            ]
        ],
        [
            'name' => '指定limit=5',
            'params' => [
                'page' => 1,
                'limit' => 5,
            ]
        ],
        [
            'name' => '指定limit=20',
            'params' => [
                'page' => 1,
                'limit' => 20,
            ]
        ],
        [
            'name' => '第2页，limit=10',
            'params' => [
                'page' => 2,
                'limit' => 10,
            ]
        ],
    ];

    foreach ($testCases as $index => $testCase) {
        echo "测试案例 " . ($index + 1) . ": {$testCase['name']}\n";
        echo "请求参数: " . json_encode($testCase['params']) . "\n";
        
        // 创建模拟请求
        $request = new \Illuminate\Http\Request();
        $request->merge($testCase['params']);
        
        // 获取ProductController
        $controller = new \App\Product\Http\Controllers\ProductController(
            new \App\Product\Services\ProductService()
        );
        
        // 调用index方法
        $response = $controller->index($request);
        $responseData = json_decode($response->getContent(), true);
        
        if ($responseData && isset($responseData['data'])) {
            $paginationInfo = $responseData['data'];
            
            echo "响应结果:\n";
            echo "  - 当前页: " . ($paginationInfo['current_page'] ?? 'N/A') . "\n";
            echo "  - 每页数量: " . ($paginationInfo['per_page'] ?? 'N/A') . "\n";
            echo "  - 总数: " . ($paginationInfo['total'] ?? 'N/A') . "\n";
            echo "  - 当前页商品数: " . (isset($paginationInfo['data']) ? count($paginationInfo['data']) : 'N/A') . "\n";
            echo "  - 总页数: " . ($paginationInfo['last_page'] ?? 'N/A') . "\n";
            
            // 检查实际返回的商品数量是否符合预期
            $actualCount = isset($paginationInfo['data']) ? count($paginationInfo['data']) : 0;
            $expectedLimit = $testCase['params']['limit'] ?? 10;
            $totalProducts = $paginationInfo['total'] ?? 0;
            $expectedCount = min($expectedLimit, $totalProducts);
            
            if ($actualCount == $expectedCount) {
                echo "  ✅ 商品数量正确\n";
            } else {
                echo "  ❌ 商品数量不正确: 期望 {$expectedCount}, 实际 {$actualCount}\n";
            }
        } else {
            echo "  ❌ 响应格式错误\n";
            echo "  响应内容: " . $response->getContent() . "\n";
        }
        
        echo "\n" . str_repeat("-", 50) . "\n\n";
    }

    echo "✅ API分页参数测试完成！\n";

} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
