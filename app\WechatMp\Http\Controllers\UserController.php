<?php

namespace App\WechatMp\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\UserAddress;

class UserController extends Controller
{
    /**
     * 获取当前用户信息
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCurrentUser(Request $request)
    {
        $user = Auth::user();
        
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $user
        ]);
    }
    
    /**
     * 更新用户信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateUserInfo(Request $request)
    {
        $user = Auth::user();
        
        try {
            // 支持更多字段的更新
            $updateFields = $request->only([
                'nickname',
                'avatar', 
                'gender',
                'province',
                'city',
                'district',
                'merchant_name',
                'phone'
            ]);
            
            // 如果提供了手机号和验证码，需要验证
            if ($request->has('phone') && $request->has('code')) {
                // 使用短信服务验证验证码
                $smsService = app(\App\shop\Services\Sms\AliyunSmsService::class);
                
                // 检查短信服务是否启用
                if (!$smsService->isEnabled()) {
                    return response()->json([
                        'code' => 403,
                        'message' => '短信验证服务未启用',
                        'data' => null
                    ], 403);
                }
                
                // 验证验证码
                $isValid = $smsService->verifyCode(
                    $request->phone,
                    $request->code,
                    $request->input('type', 'bind')
                );
                
                if (!$isValid) {
                    return response()->json([
                        'code' => 400,
                        'message' => '验证码无效或已过期',
                        'data' => null
                    ], 400);
                }
            }
            
            // 如果提供了昵称，也更新name字段
            if (isset($updateFields['nickname']) && !empty($updateFields['nickname'])) {
                $updateFields['name'] = $updateFields['nickname'];
            }
            
            // 过滤空值
            $updateFields = array_filter($updateFields, function($value) {
                return $value !== null && $value !== '';
            });

            // 自动查找region_id
            if (isset($updateFields['province'], $updateFields['city'], $updateFields['district'])) {
                $region = \App\Region\Models\Region::where('name', $updateFields['district'])
                    ->where('level', 3)
                    ->whereHas('parent', function($q) use ($updateFields) {
                        $q->where('name', $updateFields['city'])
                          ->where('level', 2)
                          ->whereHas('parent', function($q2) use ($updateFields) {
                              $q2->where('name', $updateFields['province'])
                                 ->where('level', 1);
                          });
                    })
                    ->first();
                if ($region) {
                    $updateFields['region_id'] = $region->id;
                }
            }

            if (!empty($updateFields)) {
                DB::table('users')->where('id', $user->id)->update($updateFields);
            }
            
            // 重新获取用户信息
            $updatedUser = DB::table('users')->where('id', $user->id)->first();
            
            Log::info('用户信息更新成功', [
                'user_id' => $user->id,
                'updated_fields' => array_keys($updateFields)
            ]);
            
            return response()->json([
                'code' => 200,
                'message' => '更新成功',
                'data' => [
                    'user' => $updatedUser,
                    'updated_fields' => array_keys($updateFields)
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('更新用户信息失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '更新失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 绑定手机号（微信解密）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bindPhone(Request $request)
    {
        // 这里需要实现微信手机号解密逻辑
        return response()->json([
            'code' => 200,
            'message' => '绑定成功',
            'data' => []
        ]);
    }
    
    /**
     * 使用验证码绑定手机号
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bindPhoneWithCode(Request $request)
    {
        $user = Auth::user();
        
        // 验证验证码
        // 绑定手机号，使用DB直接更新
        DB::table('users')->where('id', $user->id)->update(['phone' => $request->input('phone')]);
        
        // 重新获取用户信息
        $updatedUser = DB::table('users')->where('id', $user->id)->first();
        
        return response()->json([
            'code' => 200,
            'message' => '绑定成功',
            'data' => $updatedUser
        ]);
    }
    
    /**
     * 微信登录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        // 这里需要实现微信登录逻辑
        return response()->json([
            'code' => 200,
            'message' => '登录成功',
            'data' => [
                'token' => 'mock_token',
                'user' => [],
            ]
        ]);
    }
    
    /**
     * 用户注册
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        // 这里需要实现用户注册逻辑
        return response()->json([
            'code' => 200,
            'message' => '注册成功',
            'data' => []
        ]);
    }
    
    /**
     * 发送短信验证码
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendSmsCode(Request $request)
    {
        // 这里需要实现发送短信验证码逻辑
        return response()->json([
            'code' => 200,
            'message' => '发送成功',
            'data' => []
        ]);
    }

    /**
     * 获取用户常购商品（个性化推荐）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFrequentProducts(Request $request)
    {
        $user = Auth::user();
        
        // 验证用户登录状态
        if (!$user) {
            return response()->json([
                'code' => 401,
                'message' => '用户未登录',
                'data' => []
            ], 401);
        }
        
        // 验证请求参数
        $page = max(1, (int)$request->input('page', 1));
        $perPage = min(50, max(1, (int)$request->input('per_page', 20))); // 限制每页最多50个
        
        Log::info('获取用户常购商品请求', [
            'user_id' => $user->id,
            'page' => $page,
            'per_page' => $perPage
        ]);

        // 缓存键
        $cacheKey = "user_frequent_products:{$user->id}:page:{$page}:per_page:{$perPage}";
        
        // 尝试从缓存获取
        $cachedResult = cache()->get($cacheKey);
        if ($cachedResult) {
            Log::info('从缓存获取用户常购商品', ['user_id' => $user->id, 'cache_key' => $cacheKey]);
            return response()->json($cachedResult);
        }
        
        try {
            // 第一步：高效获取用户常购商品统计（最小化JOIN）
            $frequentProductStats = DB::table('order_items')
                ->join('orders', 'order_items.order_id', '=', 'orders.id')
                ->where('orders.user_id', $user->id)
                ->where('orders.status', 'completed')
                ->where('orders.created_at', '>=', now()->subMonths(12))
                ->select([
                    'order_items.product_id',
                    DB::raw('COUNT(DISTINCT order_items.order_id) as purchase_count'),
                    DB::raw('SUM(order_items.quantity) as total_quantity'),
                    DB::raw('MAX(orders.created_at) as last_purchase_date')
                ])
                ->groupBy('order_items.product_id')
                ->having('purchase_count', '>=', 2)
                ->orderByDesc('purchase_count')
                ->orderByDesc('last_purchase_date')
                ->offset(($page - 1) * $perPage)
                ->limit($perPage)
                ->get();

            if ($frequentProductStats->isEmpty()) {
                Log::info('用户无常购商品历史', ['user_id' => $user->id]);
                return response()->json([
                    'code' => 200,
                    'message' => '暂无常购商品',
                    'data' => [],
                    'meta' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => 0,
                        'last_page' => 1,
                        'from' => null,
                        'to' => null
                    ]
                ]);
            }

            // 第二步：批量获取商品详情（避免复杂JOIN）
            $productIds = $frequentProductStats->pluck('product_id')->toArray();
            $statsMap = $frequentProductStats->keyBy('product_id');

            $products = DB::table('products')
                ->whereIn('id', $productIds)
                ->get()
                ->keyBy('id');

            // 第三步：获取商品图片（单独查询，避免重复数据）
            $productImages = DB::table('product_images')
                ->whereIn('product_id', $productIds)
                ->select('product_id', 'image_url')
                ->get()
                ->groupBy('product_id');

            // 第四步：获取分类信息（单独查询）
            $categoryIds = $products->pluck('category_id')->filter()->unique()->toArray();
            $categories = [];
            if (!empty($categoryIds)) {
                $categories = DB::table('categories')
                    ->whereIn('id', $categoryIds)
                    ->get()
                    ->keyBy('id');
            }

            // 第五步：组装最终数据
            $processedProducts = [];
            foreach ($productIds as $productId) {
                $product = $products->get($productId);
                $stats = $statsMap->get($productId);
                
                if (!$product || !$stats) {
                    continue;
                }
                
                // 获取商品图片
                $images = $productImages->get($productId, collect());
                $imageUrl = $images->first()->image_url ?? '';
                
                // 获取分类名称
                $categoryName = '';
                if ($product->category_id && isset($categories[$product->category_id])) {
                    $categoryName = $categories[$product->category_id]->name;
                }
                
                $processedProducts[] = [
                    'id' => $product->id,
                    'name' => $product->name,
                    'price' => (float)$product->price,
                    'sale_price' => $product->sale_price ? (float)$product->sale_price : null,
                    'stock' => (int)$product->stock,
                    'sales_count' => (int)$product->sales_count,
                    'rating' => (float)($product->rating ?? 0),
                    'description' => $product->description ?? '',
                    'created_at' => $product->created_at,
                    'image_url' => $this->formatImageUrl($imageUrl),
                    'category_name' => $categoryName,
                    'frequent_info' => [
                        'purchase_count' => (int)$stats->purchase_count,
                        'total_quantity' => (int)$stats->total_quantity,
                        'last_purchase_date' => $stats->last_purchase_date
                    ]
                ];
            }

            // 获取总数（用于分页）
            $totalCount = DB::table('order_items')
                ->join('orders', 'order_items.order_id', '=', 'orders.id')
                ->where('orders.user_id', $user->id)
                ->where('orders.status', 'completed')
                ->where('orders.created_at', '>=', now()->subMonths(12))
                ->select('order_items.product_id')
                ->groupBy('order_items.product_id')
                ->havingRaw('COUNT(DISTINCT order_items.order_id) >= 2')
                ->get()
                ->count();

            Log::info('用户常购商品处理完成', [
                'user_id' => $user->id,
                'total_found' => $totalCount,
                'processed_count' => count($processedProducts)
            ]);

            $result = [
                'code' => 200,
                'message' => '获取成功',
                'data' => $processedProducts,
                'meta' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $totalCount,
                    'last_page' => ceil($totalCount / $perPage),
                    'from' => ($page - 1) * $perPage + 1,
                    'to' => min($page * $perPage, $totalCount)
                ]
            ];

            // 缓存结果（缓存30分钟）
            cache()->put($cacheKey, $result, now()->addMinutes(30));

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('获取用户常购商品失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 500,
                'message' => '获取常购商品失败',
                'data' => []
            ], 500);
        }
    }

    /**
     * 格式化图片URL
     */
    private function formatImageUrl($imageUrl)
    {
        if (empty($imageUrl)) {
            return config('app.url') . '/images/default-product.png';
        }
        
        if (str_starts_with($imageUrl, 'http')) {
            return $imageUrl;
        }
        
        return config('app.url') . '/storage/' . $imageUrl;
    }

    /**
     * 获取用户地址列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserAddresses(Request $request)
    {
        Log::info('开始获取用户地址列表', [
            'request_headers' => $request->headers->all(),
            'auth_check' => Auth::check()
        ]);
        
        $user = Auth::user();
        
        if (!$user) {
            Log::error('用户未认证');
            return response()->json([
                'code' => 401,
                'message' => '用户未认证',
                'data' => null
            ], 401);
        }
        
        Log::info('用户认证成功', [
            'user_id' => $user->id,
            'user_name' => $user->name
        ]);
        
        try {
            // 直接使用UserAddress模型查询用户地址，默认地址排在前面
            $addresses = UserAddress::where('user_id', $user->id)
                ->orderByDesc('is_default')
                ->orderByDesc('created_at')
                ->get();
            
            Log::info('地址查询成功', [
                'user_id' => $user->id,
                'address_count' => $addresses->count()
            ]);
            
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $addresses
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取用户地址失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '获取地址失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 创建用户地址
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createUserAddress(Request $request)
    {
        $user = Auth::user();
        
        $validator = Validator::make($request->all(), [
            'contact_name' => 'required|string|max:50',
            'contact_phone' => 'required|string|max:20',
            'province' => 'nullable|string|max:50',
            'city' => 'nullable|string|max:50',
            'district' => 'nullable|string|max:50',
            'address' => 'required|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:500',
            'is_default' => 'boolean',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => $validator->errors()->first(),
                'data' => null
            ], 422);
        }
        
        try {
            // 如果设置为默认地址，将其他地址设为非默认
            if ($request->is_default) {
                UserAddress::where('user_id', $user->id)->update(['is_default' => false]);
            }
            
            $addressData = $request->all();
            $addressData['user_id'] = $user->id;
            
            $address = UserAddress::create($addressData);
            
            Log::info('用户地址创建成功', [
                'user_id' => $user->id,
                'address_id' => $address->id
            ]);
            
            return response()->json([
                'code' => 200,
                'message' => '地址添加成功',
                'data' => $address
            ], 201);
            
        } catch (\Exception $e) {
            Log::error('创建用户地址失败', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '创建地址失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取单个地址详情
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserAddress(Request $request, $id)
    {
        $user = Auth::user();
        
        try {
            $address = UserAddress::where('user_id', $user->id)->where('id', $id)->firstOrFail();
            
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $address
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 404,
                'message' => '地址不存在',
                'data' => null
            ], 404);
        }
    }

    /**
     * 更新用户地址
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateUserAddress(Request $request, $id)
    {
        $user = Auth::user();
        
        $validator = Validator::make($request->all(), [
            'contact_name' => 'string|max:50',
            'contact_phone' => 'string|max:20',
            'province' => 'nullable|string|max:50',
            'city' => 'nullable|string|max:50',
            'district' => 'nullable|string|max:50',
            'address' => 'string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:500',
            'is_default' => 'boolean',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => $validator->errors()->first(),
                'data' => null
            ], 422);
        }
        
        try {
            $address = UserAddress::where('user_id', $user->id)->where('id', $id)->firstOrFail();
            
            // 如果设置为默认地址，将其他地址设为非默认
            if ($request->has('is_default') && $request->is_default) {
                UserAddress::where('user_id', $user->id)->where('id', '!=', $id)->update(['is_default' => false]);
            }
            
            $address->update($request->all());
            
            Log::info('用户地址更新成功', [
                'user_id' => $user->id,
                'address_id' => $address->id
            ]);
            
            return response()->json([
                'code' => 200,
                'message' => '地址更新成功',
                'data' => $address
            ]);
            
        } catch (\Exception $e) {
            Log::error('更新用户地址失败', [
                'user_id' => $user->id,
                'address_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '更新地址失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 删除用户地址
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteUserAddress(Request $request, $id)
    {
        $user = Auth::user();
        
        try {
            $address = UserAddress::where('user_id', $user->id)->where('id', $id)->firstOrFail();
            $address->delete();
            
            Log::info('用户地址删除成功', [
                'user_id' => $user->id,
                'address_id' => $id
            ]);
            
            return response()->json([
                'code' => 200,
                'message' => '地址删除成功',
                'data' => null
            ]);
            
        } catch (\Exception $e) {
            Log::error('删除用户地址失败', [
                'user_id' => $user->id,
                'address_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '删除地址失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 设置默认地址
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function setDefaultAddress(Request $request, $id)
    {
        $user = Auth::user();
        
        try {
            $address = UserAddress::where('user_id', $user->id)->where('id', $id)->firstOrFail();
            
            // 将所有地址设为非默认
            UserAddress::where('user_id', $user->id)->update(['is_default' => false]);
            
            // 设置当前地址为默认
            $address->update(['is_default' => true]);
            
            Log::info('设置默认地址成功', [
                'user_id' => $user->id,
                'address_id' => $id
            ]);
            
            return response()->json([
                'code' => 200,
                'message' => '设置默认地址成功',
                'data' => $address
            ]);
            
        } catch (\Exception $e) {
            Log::error('设置默认地址失败', [
                'user_id' => $user->id,
                'address_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '设置默认地址失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
} 