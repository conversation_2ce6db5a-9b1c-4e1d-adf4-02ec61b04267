<?php

namespace App\Crm\Models;

use App\Models\User;
use App\Employee\Models\Employee;
use App\Crm\Models\CrmAgent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientFollowUp extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'employee_id',
        'follow_up_date',
        'contact_method',
        'notes',
        'result',
        'next_follow_up',
    ];

    /**
     * 属性转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'follow_up_date' => 'date',
        'next_follow_up' => 'date',
    ];

    /**
     * 获取关联的客户
     */
    public function client()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 获取关联的CRM专员
     */
    public function agent()
    {
        return $this->belongsTo(Employee::class, 'employee_id');
    }

    /**
     * 获取CRM专员信息
     */
    public function crmAgent()
    {
        return $this->hasOneThrough(
            CrmAgent::class,
            Employee::class,
            'id', // 员工表外键
            'employee_id', // CRM专员表外键
            'employee_id', // 当前表关联字段
            'id' // 员工表关联字段
        );
    }

    /**
     * 创建后续跟进记录
     */
    public function createFollowUp(array $attributes)
    {
        return self::create([
            'user_id' => $this->user_id,
            'employee_id' => $this->employee_id,
            'follow_up_date' => $attributes['follow_up_date'] ?? now(),
            'contact_method' => $attributes['contact_method'] ?? 'phone',
            'notes' => $attributes['notes'] ?? null,
            'result' => $attributes['result'] ?? 'follow_up',
            'next_follow_up' => $attributes['next_follow_up'] ?? null,
        ]);
    }
} 