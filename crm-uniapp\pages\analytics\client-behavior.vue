<template>
	<view class="container">
		<view class="header">
			<text class="title">客户行为详情</text>
		</view>
		
		<view class="content">
			<view class="info-card">
				<text class="card-title">📊 行为分析</text>
				<text class="card-desc">详细的客户行为数据分析</text>
			</view>
			
			<view class="placeholder">
				<text class="placeholder-text">功能开发中...</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			
		}
	},
	onLoad() {
		
	}
}
</script>

<style scoped>
.container {
	padding: 20px;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	margin-bottom: 20px;
}

.title {
	font-size: 24px;
	font-weight: bold;
	color: #333;
}

.content {
	
}

.info-card {
	background: white;
	padding: 20px;
	border-radius: 10px;
	margin-bottom: 20px;
}

.card-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10px;
}

.card-desc {
	color: #666;
	font-size: 14px;
}

.placeholder {
	text-align: center;
	padding: 50px 20px;
}

.placeholder-text {
	color: #999;
	font-size: 16px;
}
</style> 