<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 检查product_units表是否存在
        if (Schema::hasTable('product_units')) {
            // 检查是否已存在新字段
            if (!Schema::hasColumn('product_units', 'conversion_factor') && Schema::hasColumn('product_units', 'conversion_rate')) {
                // 添加新字段
                Schema::table('product_units', function (Blueprint $table) {
                    $table->decimal('conversion_factor', 20, 10)->nullable()->after('conversion_rate');
                });
                
                // 复制旧数据到新字段
                DB::statement('UPDATE product_units SET conversion_factor = conversion_rate');
                
                // 删除旧字段
                Schema::table('product_units', function (Blueprint $table) {
                    $table->dropColumn('conversion_rate');
                });
            }
            
            // 检查并添加其他新字段
            if (!Schema::hasColumn('product_units', 'roles')) {
                Schema::table('product_units', function (Blueprint $table) {
                    $table->json('roles')->nullable()->after('conversion_factor');
                });
            }
            
            if (!Schema::hasColumn('product_units', 'role_priority')) {
                Schema::table('product_units', function (Blueprint $table) {
                    $table->json('role_priority')->nullable()->after('roles');
                });
            }
            
            if (!Schema::hasColumn('product_units', 'is_default')) {
                Schema::table('product_units', function (Blueprint $table) {
                    $table->boolean('is_default')->default(false)->after('role_priority');
                });
                
                // 为每个产品设置一个默认单位（优先使用base_unit_id）
                DB::statement('
                    UPDATE product_units 
                    SET is_default = true 
                    WHERE (product_id, unit_id) IN (
                        SELECT pu.product_id, pu.unit_id
                        FROM product_units pu
                        JOIN products p ON pu.product_id = p.id
                        WHERE pu.unit_id = p.base_unit_id
                    )
                ');
            }
            
            if (!Schema::hasColumn('product_units', 'is_active')) {
                Schema::table('product_units', function (Blueprint $table) {
                    $table->boolean('is_active')->default(true)->after('is_default');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     * 注意：此方法不会恢复原始字段，因为图论模型需要新的字段格式
     */
    public function down(): void
    {
        // 此处不执行任何操作，保持前向兼容性
    }
}; 