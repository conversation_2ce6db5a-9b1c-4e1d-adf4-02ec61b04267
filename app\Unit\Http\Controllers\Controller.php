<?php

namespace App\Unit\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 防止PHP源码直接输出
        $this->preventPhpSourceOutput();
    }
    
    /**
     * 防止PHP源码直接输出
     */
    protected function preventPhpSourceOutput()
    {
        // 如果当前不是处理中的请求，直接返回
        if (!app()->runningInConsole() && !app()->runningUnitTests()) {
            // 设置默认内容类型为JSON
            if (!headers_sent()) {
                header('Content-Type: application/json; charset=utf-8');
            }
            
            // 检查并清理任何已经开始的输出
            if (ob_get_level() > 0) {
                ob_end_clean();
                Log::warning("检测到已有输出开始，已清理输出缓冲区");
            }
            
            // 开始新的输出缓冲，确保所有输出都可控
            ob_start();
        }
    }
} 