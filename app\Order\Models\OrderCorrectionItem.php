<?php

namespace App\Order\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderCorrectionItem extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $fillable = [
        'correction_id',
        'order_item_id',
        'original_quantity',
        'corrected_quantity',
        'original_weight',
        'corrected_weight',
        'original_price',
        'corrected_price',
        'original_total',
        'corrected_total',
        'difference_amount',
        'correction_reason',
    ];

    protected $casts = [
        'original_weight' => 'decimal:3',
        'corrected_weight' => 'decimal:3',
        'original_price' => 'decimal:2',
        'corrected_price' => 'decimal:2',
        'original_total' => 'decimal:2',
        'corrected_total' => 'decimal:2',
        'difference_amount' => 'decimal:2',
        'created_at' => 'datetime',
    ];

    /**
     * 关联更正单
     */
    public function correction(): BelongsTo
    {
        return $this->belongsTo(OrderCorrection::class, 'correction_id');
    }

    /**
     * 关联原订单项
     */
    public function orderItem(): BelongsTo
    {
        return $this->belongsTo(OrderItem::class, 'order_item_id');
    }

    /**
     * 获取数量变化
     */
    public function getQuantityChangeAttribute(): int
    {
        return $this->corrected_quantity - $this->original_quantity;
    }

    /**
     * 获取重量变化
     */
    public function getWeightChangeAttribute(): float
    {
        return $this->corrected_weight - $this->original_weight;
    }

    /**
     * 是否有更正变化
     */
    public function hasCorrectionChanges(): bool
    {
        return $this->difference_amount != 0;
    }

    /**
     * 获取商品名称
     */
    public function getProductNameAttribute(): string
    {
        if ($this->relationLoaded('orderItem') && $this->orderItem) {
            return $this->orderItem->product_name ?? '未知商品';
        }
        
        // 如果关联未加载，尝试动态加载
        $orderItem = $this->orderItem;
        return $orderItem ? $orderItem->product_name : '未知商品';
    }

    /**
     * 获取商品规格
     */
    public function getProductSpecAttribute(): string
    {
        if ($this->relationLoaded('orderItem') && $this->orderItem) {
            return $this->orderItem->product_sku ?? '';
        }
        
        // 如果关联未加载，尝试动态加载
        $orderItem = $this->orderItem;
        return $orderItem ? $orderItem->product_sku : '';
    }

    /**
     * 获取单位名称
     */
    public function getUnitNameAttribute(): string
    {
        if ($this->relationLoaded('orderItem') && $this->orderItem) {
            return $this->orderItem->unit_name ?? '件';
        }
        
        // 如果关联未加载，尝试动态加载
        $orderItem = $this->orderItem;
        return $orderItem ? $orderItem->unit_name : '件';
    }
} 