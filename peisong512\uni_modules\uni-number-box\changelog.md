## 1.2.8（2024-04-26）
- 修复 在vue2下H5黑边的bug
## 1.2.7（2024-04-26）
- 修复 在vue2手动输入后失焦导致清空数值的严重bug
## 1.2.6（2024-02-22）
- 新增 设置宽度属性width(单位：px)
## 1.2.5（2024-02-21）
- 修复 step步长小于1时，键盘类型为number的bug
## 1.2.4（2024-02-02）
- 修复 加减号垂直位置偏移样式问题
## 1.2.3（2023-05-23）
- 更新示例工程
## 1.2.2（2023-05-08）
- 修复 change 事件执行顺序错误的问题
## 1.2.1（2021-11-22）
- 修复 vue3中某些scss变量无法找到的问题
## 1.2.0（2021-11-19）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-number-box](https://uniapp.dcloud.io/component/uniui/uni-number-box)
## 1.1.2（2021-11-09） 
- 新增 提供组件设计资源，组件样式调整
## 1.1.1（2021-07-30）
- 优化 vue3下事件警告的问题
## 1.1.0（2021-07-13）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.0.7（2021-05-12）
- 新增 组件示例地址
## 1.0.6（2021-04-20）
- 修复 uni-number-box 浮点数运算不精确的 bug
- 修复 uni-number-box change 事件触发不正确的 bug
- 新增 uni-number-box v-model 双向绑定
## 1.0.5（2021-02-05）
- 调整为uni_modules目录规范

## 1.0.7（2021-02-05）
- 调整为uni_modules目录规范
- 新增 支持 v-model
- 新增 支持 focus、blur 事件
- 新增 支持 PC 端
