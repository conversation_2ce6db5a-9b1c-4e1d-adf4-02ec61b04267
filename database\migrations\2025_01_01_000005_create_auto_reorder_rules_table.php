<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('auto_reorder_rules', function (Blueprint $table) {
            $table->id();
            
            // 关联信息
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('warehouse_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('supplier_id')->nullable()->constrained()->onDelete('set null');
            
            // 规则基本信息
            $table->string('name')->comment('规则名称');
            $table->text('description')->nullable()->comment('规则描述');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            
            // 触发条件
            $table->enum('trigger_type', [
                'stock_level',      // 库存水平触发
                'sales_velocity',   // 销售速度触发
                'time_based',       // 时间触发
                'manual',           // 手动触发
                'combined'          // 组合条件
            ])->default('stock_level')->comment('触发类型');
            
            $table->decimal('reorder_point', 10, 2)->comment('补货点');
            $table->decimal('reorder_quantity', 10, 2)->comment('补货数量');
            $table->decimal('max_stock_level', 10, 2)->nullable()->comment('最大库存水平');
            
            // 高级设置
            $table->json('trigger_conditions')->nullable()->comment('触发条件配置');
            $table->integer('lead_time_days')->default(7)->comment('采购周期（天）');
            $table->decimal('safety_stock', 10, 2)->default(0)->comment('安全库存');
            
            // 补货策略
            $table->enum('reorder_strategy', [
                'fixed_quantity',   // 固定数量
                'economic_order',   // 经济订货量
                'target_level',     // 目标库存水平
                'dynamic'           // 动态计算
            ])->default('fixed_quantity')->comment('补货策略');
            
            $table->json('strategy_params')->nullable()->comment('策略参数');
            
            // 供应商和采购信息
            $table->decimal('unit_cost', 10, 2)->nullable()->comment('单位成本');
            $table->decimal('min_order_quantity', 10, 2)->nullable()->comment('最小订货量');
            $table->decimal('order_multiple', 10, 2)->nullable()->comment('订货倍数');
            
            // 时间设置
            $table->json('schedule_config')->nullable()->comment('调度配置');
            $table->timestamp('last_triggered_at')->nullable()->comment('最后触发时间');
            $table->timestamp('next_check_at')->nullable()->comment('下次检查时间');
            
            // 统计信息
            $table->integer('trigger_count')->default(0)->comment('触发次数');
            $table->integer('success_count')->default(0)->comment('成功次数');
            $table->timestamp('last_success_at')->nullable()->comment('最后成功时间');
            
            // 审批设置
            $table->boolean('require_approval')->default(false)->comment('是否需要审批');
            $table->decimal('approval_threshold', 10, 2)->nullable()->comment('审批阈值');
            $table->json('approval_workflow')->nullable()->comment('审批流程');
            
            $table->timestamps();
            
            // 索引
            $table->index(['product_id', 'warehouse_id']);
            $table->index(['is_active', 'trigger_type']);
            $table->index('next_check_at');
            $table->index(['supplier_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('auto_reorder_rules');
    }
}; 