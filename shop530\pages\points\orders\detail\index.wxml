<!-- pages/points/orders/detail/index.wxml - 积分订单详情页 -->
<view class="order-detail-container" wx:if="{{!loading}}">
  
  <!-- 订单状态 -->
  <view class="order-status-section">
    <view class="status-icon">
      <image class="status-image" src="/images/order-status/{{orderDetail.status}}.png" mode="aspectFit" />
    </view>
    <view class="status-info">
      <text class="status-text">{{formatOrderStatus(orderDetail.status)}}</text>
      <text class="status-desc" wx:if="{{orderDetail.status_desc}}">{{orderDetail.status_desc}}</text>
    </view>
  </view>

  <!-- 订单基本信息 -->
  <view class="order-info-section">
    <view class="section-title">订单信息</view>
    <view class="info-item">
      <text class="info-label">订单号</text>
      <view class="info-value">
        <text class="order-no">{{orderDetail.order_no}}</text>
        <text class="copy-btn" bindtap="copyOrderNo">复制</text>
      </view>
    </view>
    <view class="info-item">
      <text class="info-label">下单时间</text>
      <text class="info-value">{{formatDate(orderDetail.created_at)}}</text>
    </view>
    <view class="info-item" wx:if="{{orderDetail.paid_at}}">
      <text class="info-label">支付时间</text>
      <text class="info-value">{{formatDate(orderDetail.paid_at)}}</text>
    </view>
    <view class="info-item" wx:if="{{orderDetail.shipped_at}}">
      <text class="info-label">发货时间</text>
      <text class="info-value">{{formatDate(orderDetail.shipped_at)}}</text>
    </view>
    <view class="info-item" wx:if="{{orderDetail.completed_at}}">
      <text class="info-label">完成时间</text>
      <text class="info-value">{{formatDate(orderDetail.completed_at)}}</text>
    </view>
  </view>

  <!-- 商品信息 -->
  <view class="products-section">
    <view class="section-title">商品信息</view>
    <view 
      class="product-item"
      wx:for="{{orderDetail.items}}"
      wx:key="id"
      data-product-id="{{item.product_id}}"
      bindtap="goToProduct"
    >
      <image 
        class="product-image" 
        src="{{item.image}}" 
        mode="aspectFill"
        data-url="{{item.image}}"
        bindtap="previewImage"
      />
      <view class="product-info">
        <text class="product-name">{{item.name}}</text>
        <text class="product-spec" wx:if="{{item.spec_name}}">规格：{{item.spec_name}}</text>
        <view class="product-price">
          <text class="points-price">{{item.points_price}}积分</text>
          <text class="cash-price" wx:if="{{item.cash_price > 0}}">+¥{{item.cash_price}}</text>
        </view>
      </view>
      <view class="product-quantity">
        <text>×{{item.quantity}}</text>
      </view>
    </view>
  </view>

  <!-- 配送信息 -->
  <view class="shipping-section" wx:if="{{orderDetail.shipping_address}}">
    <view class="section-title">配送信息</view>
    <view class="address-info">
      <view class="address-header">
        <text class="receiver-name">{{orderDetail.shipping_address.name}}</text>
        <text class="receiver-phone">{{orderDetail.shipping_address.phone}}</text>
      </view>
      <text class="address-detail">{{orderDetail.shipping_address.full_address}}</text>
    </view>
    
    <!-- 物流信息 -->
    <view class="logistics-info" wx:if="{{orderDetail.tracking_no}}">
      <view class="logistics-item">
        <text class="logistics-label">物流公司</text>
        <text class="logistics-value">{{orderDetail.logistics_company}}</text>
      </view>
      <view class="logistics-item">
        <text class="logistics-label">运单号</text>
        <text class="logistics-value">{{orderDetail.tracking_no}}</text>
      </view>
    </view>
  </view>

  <!-- 费用明细 -->
  <view class="cost-section">
    <view class="section-title">费用明细</view>
    <view class="cost-item">
      <text class="cost-label">商品积分</text>
      <text class="cost-value">{{formatPoints(orderDetail.items_points)}}</text>
    </view>
    <view class="cost-item" wx:if="{{orderDetail.items_cash > 0}}">
      <text class="cost-label">补差金额</text>
      <text class="cost-value">¥{{orderDetail.items_cash}}</text>
    </view>
    <view class="cost-item" wx:if="{{orderDetail.shipping_fee > 0}}">
      <text class="cost-label">运费</text>
      <text class="cost-value">¥{{orderDetail.shipping_fee}}</text>
    </view>
    <view class="cost-total">
      <text class="total-label">合计</text>
      <view class="total-value">
        <text class="total-points">{{formatPoints(orderDetail.total_points)}}</text>
        <text class="total-cash" wx:if="{{orderDetail.total_cash > 0}}">+¥{{orderDetail.total_cash}}</text>
      </view>
    </view>
  </view>

  <!-- 订单备注 -->
  <view class="remarks-section" wx:if="{{orderDetail.remarks}}">
    <view class="section-title">订单备注</view>
    <text class="remarks-text">{{orderDetail.remarks}}</text>
  </view>

</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-spinner"></view>
  <text class="loading-text">加载中...</text>
</view>

<!-- 底部操作栏 -->
<view class="bottom-actions" wx:if="{{!loading && getOrderActions(orderDetail).length > 0}}">
  <button 
    class="action-btn {{action.type}}"
    wx:for="{{getOrderActions(orderDetail)}}"
    wx:key="key"
    wx:for-item="action"
    data-action="{{action.key}}"
    bindtap="onOrderAction"
    loading="{{actionLoading}}"
  >
    {{action.name}}
  </button>
</view> 