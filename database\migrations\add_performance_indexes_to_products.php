<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 新品查询优化：状态+创建时间复合索引
            if (!$this->indexExists('products', 'idx_products_status_created')) {
                $table->index(['status', 'created_at'], 'idx_products_status_created');
            }
            
            // 热门商品优化：状态+销量复合索引
            if (!$this->indexExists('products', 'idx_products_status_sales')) {
                $table->index(['status', 'sales_count'], 'idx_products_status_sales');
            }
            
            // 分类商品优化：分类+状态复合索引
            if (!$this->indexExists('products', 'idx_products_category_status')) {
                $table->index(['category_id', 'status'], 'idx_products_category_status');
            }
            
            // 精选商品优化：状态+精选标记复合索引
            if (!$this->indexExists('products', 'idx_products_status_featured')) {
                $table->index(['status', 'is_featured'], 'idx_products_status_featured');
            }
            
            // 商品排序优化：状态+排序字段复合索引
            if (!$this->indexExists('products', 'idx_products_status_sort')) {
                $table->index(['status', 'sort'], 'idx_products_status_sort');
            }
            
            // 库存管理优化：库存跟踪+状态复合索引
            if (!$this->indexExists('products', 'idx_products_track_inventory_status')) {
                $table->index(['track_inventory', 'status'], 'idx_products_track_inventory_status');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            if ($this->indexExists('products', 'idx_products_status_created')) {
                $table->dropIndex('idx_products_status_created');
            }
            if ($this->indexExists('products', 'idx_products_status_sales')) {
                $table->dropIndex('idx_products_status_sales');
            }
            if ($this->indexExists('products', 'idx_products_category_status')) {
                $table->dropIndex('idx_products_category_status');
            }
            if ($this->indexExists('products', 'idx_products_status_featured')) {
                $table->dropIndex('idx_products_status_featured');
            }
            if ($this->indexExists('products', 'idx_products_status_sort')) {
                $table->dropIndex('idx_products_status_sort');
            }
            if ($this->indexExists('products', 'idx_products_track_inventory_status')) {
                $table->dropIndex('idx_products_track_inventory_status');
            }
        });
    }
    
    /**
     * 检查索引是否存在
     */
    private function indexExists(string $table, string $indexName): bool
    {
        try {
            $indexes = DB::select("SHOW INDEX FROM `{$table}` WHERE Key_name = ?", [$indexName]);
            return !empty($indexes);
        } catch (\Exception $e) {
            // 如果表不存在或其他错误，返回 false
            return false;
        }
    }
}; 