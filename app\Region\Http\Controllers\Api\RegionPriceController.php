<?php

namespace App\Region\Http\Controllers\Api;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Region\Models\RegionPrice;
use App\Region\Resources\RegionPriceResource;
use App\Region\Services\RegionPriceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class RegionPriceController extends Controller
{
    /**
     * 区域价格服务
     *
     * @var RegionPriceService
     */
    protected $regionPriceService;
    
    /**
     * 构造函数
     *
     * @param RegionPriceService $regionPriceService
     */
    public function __construct(RegionPriceService $regionPriceService)
    {
        $this->regionPriceService = $regionPriceService;
    }
    
    /**
     * 获取商品在指定区域的价格
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductRegionPrice(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|integer|exists:products,id',
                'region_id' => 'required|integer|exists:regions,id',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            $productId = $request->input('product_id');
            $regionId = $request->input('region_id');
            
            $price = $this->regionPriceService->getProductRegionPrice($productId, $regionId);
            
            if (!$price) {
                return response()->json(ApiResponse::error('未找到商品在该区域的价格', 404), 404);
            }
            
            return response()->json(ApiResponse::success(new RegionPriceResource($price)));
        } catch (\Exception $e) {
            Log::error('获取商品区域价格失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取商品区域价格失败', 500), 500);
        }
    }
    
    /**
     * 获取商品的所有区域价格
     *
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductAllRegionPrices($productId)
    {
        try {
            $prices = $this->regionPriceService->getProductAllRegionPrices($productId);
            return response()->json(ApiResponse::success(RegionPriceResource::collection($prices)));
        } catch (\Exception $e) {
            Log::error('获取商品所有区域价格失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取商品所有区域价格失败', 500), 500);
        }
    }
    
    /**
     * 批量设置商品区域价格
     *
     * @param Request $request
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchSetProductRegionPrices(Request $request, $productId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'prices' => 'required|array',
                'prices.*.region_id' => 'required|integer|exists:regions,id',
                'prices.*.price' => 'required|numeric|min:0',
                'prices.*.original_price' => 'nullable|numeric|min:0',
                'prices.*.stock' => 'nullable|integer|min:0',
                'prices.*.status' => 'nullable|boolean',
                'prices.*.start_date' => 'nullable|date',
                'prices.*.end_date' => 'nullable|date|after_or_equal:prices.*.start_date',
                'prices.*.special_conditions' => 'nullable|array',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            $prices = $request->input('prices');
            $results = $this->regionPriceService->batchSetProductRegionPrices($productId, $prices);
            
            return response()->json(ApiResponse::success($results, '批量设置商品区域价格成功'));
        } catch (\Exception $e) {
            Log::error('批量设置商品区域价格失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('批量设置商品区域价格失败', 500), 500);
        }
    }
    
    /**
     * 复制区域价格到其他区域
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function copyRegionPrices(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'source_region_id' => 'required|integer|exists:regions,id',
                'target_region_id' => 'required|integer|exists:regions,id',
                'product_id' => 'nullable|integer|exists:products,id',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            $sourceRegionId = $request->input('source_region_id');
            $targetRegionId = $request->input('target_region_id');
            $filters = [];
            
            if ($request->has('product_id')) {
                $filters['product_id'] = $request->input('product_id');
            }
            
            $count = $this->regionPriceService->copyRegionPrices($sourceRegionId, $targetRegionId, $filters);
            
            return response()->json(ApiResponse::success(['count' => $count], '复制区域价格成功'));
        } catch (\Exception $e) {
            Log::error('复制区域价格失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('复制区域价格失败', 500), 500);
        }
    }
    
    /**
     * 批量调整区域价格
     *
     * @param Request $request
     * @param int $regionId
     * @return \Illuminate\Http\JsonResponse
     */
    public function adjustRegionPrices(Request $request, $regionId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'adjustment_value' => 'required|numeric',
                'adjustment_type' => 'required|in:amount',
                'product_id' => 'nullable|integer|exists:products,id',
                'min_price' => 'nullable|numeric|min:0',
                'max_price' => 'nullable|numeric|min:0',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            $adjustmentValue = $request->input('adjustment_value');
            $adjustmentType = $request->input('adjustment_type');
            $filters = [];
            
            if ($request->has('product_id')) {
                $filters['product_id'] = $request->input('product_id');
            }
            
            if ($request->has('min_price')) {
                $filters['min_price'] = $request->input('min_price');
            }
            
            if ($request->has('max_price')) {
                $filters['max_price'] = $request->input('max_price');
            }
            
            $count = $this->regionPriceService->adjustRegionPrices($regionId, $adjustmentValue, $adjustmentType, $filters);
            
            return response()->json(ApiResponse::success(['count' => $count], '调整区域价格成功'));
        } catch (\Exception $e) {
            Log::error('调整区域价格失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('调整区域价格失败', 500), 500);
        }
    }
    
    /**
     * 删除商品的区域价格
     *
     * @param Request $request
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteProductRegionPrices(Request $request, $productId)
    {
        try {
            $regionId = $request->input('region_id');
            $count = $this->regionPriceService->deleteProductRegionPrices($productId, $regionId);
            
            return response()->json(ApiResponse::success(['count' => $count], '删除商品区域价格成功'));
        } catch (\Exception $e) {
            Log::error('删除商品区域价格失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('删除商品区域价格失败', 500), 500);
        }
    }
    
    /**
     * 获取区域内的商品及价格
     *
     * @param Request $request
     * @param int $regionId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRegionProducts(Request $request, $regionId)
    {
        try {
            $filters = [
                'keyword' => $request->input('keyword'),
                'min_price' => $request->input('min_price'),
                'max_price' => $request->input('max_price'),
                'status' => $request->input('status'),
                'price_type' => $request->input('price_type'),
                'stock_status' => $request->input('stock_status'),
                'validity_status' => $request->input('validity_status'),
                'category' => $request->input('category'),
                'show_inherited' => $request->input('show_inherited', true),
            ];
            
            $page = $request->input('page', 1);
            $limit = $request->input('limit', $request->input('per_page', 20));
            
            $products = $this->regionPriceService->getRegionProducts($regionId, $filters, $page, $limit);
            
            return response()->json(ApiResponse::success($products));
        } catch (\Exception $e) {
            Log::error('获取区域商品失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取区域商品失败', 500), 500);
        }
    }
    
    /**
     * 设置商品在区域的价格
     *
     * @param Request $request
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function setProductRegionPrice(Request $request, $productId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'region_id' => 'required|integer|exists:regions,id',
                'price' => 'required|numeric|min:0',
                'original_price' => 'nullable|numeric|min:0',
                'stock' => 'nullable|integer|min:0',
                'status' => 'nullable|boolean',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            $data = $request->only(['region_id', 'price', 'original_price', 'stock', 'status', 'start_date', 'end_date']);
            $data['product_id'] = $productId;
            
            $price = $this->regionPriceService->setProductRegionPrice($productId, $data);
            
            return response()->json(ApiResponse::success(new RegionPriceResource($price), '设置商品区域价格成功'));
        } catch (\Exception $e) {
            Log::error('设置商品区域价格失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('设置商品区域价格失败', 500), 500);
        }
    }
    
    /**
     * 删除商品在指定区域的价格
     *
     * @param int $productId
     * @param int $regionId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteProductRegionPrice($productId, $regionId)
    {
        try {
            $count = $this->regionPriceService->deleteProductRegionPrices($productId, $regionId);
            
            return response()->json(ApiResponse::success(['count' => $count], '删除商品区域价格成功'));
        } catch (\Exception $e) {
            Log::error('删除商品区域价格失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('删除商品区域价格失败', 500), 500);
        }
    }
    
    /**
     * 批量调整区域价格预览
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function previewAdjustPrices(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'region_id' => 'required|integer|exists:regions,id',
                'adjustment_value' => 'required|numeric',
                'adjustment_type' => 'required|in:amount',
                'product_ids' => 'nullable|array',
                'product_ids.*' => 'integer|exists:products,id',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            $regionId = $request->input('region_id');
            $adjustmentValue = $request->input('adjustment_value');
            $adjustmentType = $request->input('adjustment_type');
            $productIds = $request->input('product_ids', []);
            
            $preview = $this->regionPriceService->previewAdjustPrices($regionId, $adjustmentValue, $adjustmentType, $productIds);
            
            return response()->json(ApiResponse::success($preview));
        } catch (\Exception $e) {
            Log::error('预览价格调整失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('预览价格调整失败', 500), 500);
        }
    }
    
    /**
     * 批量调整区域价格
     *
     * @param Request $request
     * @param int $regionId
     * @return \Illuminate\Http\JsonResponse
     */
    public function adjustPrices(Request $request, $regionId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'adjustment_value' => 'required|numeric',
                'adjustment_type' => 'required|in:amount',
                'product_ids' => 'nullable|array',
                'product_ids.*' => 'integer|exists:products,id',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            $adjustmentValue = $request->input('adjustment_value');
            $adjustmentType = $request->input('adjustment_type');
            $productIds = $request->input('product_ids', []);
            
            $count = $this->regionPriceService->adjustPrices($regionId, $adjustmentValue, $adjustmentType, $productIds);
            
            return response()->json(ApiResponse::success(['count' => $count], '批量调整价格成功'));
        } catch (\Exception $e) {
            Log::error('批量调整价格失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('批量调整价格失败', 500), 500);
        }
    }
    
    /**
     * 从其他区域复制价格
     *
     * @param Request $request
     * @param int $targetRegionId
     * @param int $sourceRegionId
     * @return \Illuminate\Http\JsonResponse
     */
    public function copyPricesFromRegion(Request $request, $targetRegionId, $sourceRegionId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_ids' => 'nullable|array',
                'product_ids.*' => 'integer|exists:products,id',
                'overwrite' => 'nullable|boolean',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            $productIds = $request->input('product_ids', []);
            $overwrite = $request->input('overwrite', false);
            
            $count = $this->regionPriceService->copyPricesFromRegion($targetRegionId, $sourceRegionId, $productIds, $overwrite);
            
            return response()->json(ApiResponse::success(['count' => $count], '复制价格成功'));
        } catch (\Exception $e) {
            Log::error('复制价格失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('复制价格失败', 500), 500);
        }
    }

    /**
     * 批量添加商品到区域
     */
    public function batchAddProducts(Request $request, $regionId)
    {
        $request->validate([
            'product_ids' => 'required|array',
            'product_ids.*' => 'integer|exists:products,id',
            'default_price' => 'nullable|numeric|min:0',
            'default_stock' => 'nullable|integer|min:0',
            'copy_from_region_id' => 'nullable|integer|exists:regions,id',
            'inherit_parent_price' => 'boolean'
        ]);

        try {
            $result = $this->regionPriceService->batchAddProducts(
                $regionId,
                $request->product_ids,
                $request->only(['default_price', 'default_stock', 'copy_from_region_id', 'inherit_parent_price'])
            );

            return response()->json([
                'success' => true,
                'message' => '批量添加商品成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量添加商品失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量设置商品价格
     */
    public function batchSetPrices(Request $request, $regionId)
    {
        $request->validate([
            'products' => 'required|array',
            'products.*.product_id' => 'required|integer|exists:products,id',
            'products.*.price' => 'required|numeric|min:0',
            'products.*.stock' => 'nullable|integer|min:0',
            'products.*.status' => 'boolean',
            'products.*.start_date' => 'nullable|date',
            'products.*.end_date' => 'nullable|date|after:start_date'
        ]);

        try {
            $result = $this->regionPriceService->batchSetPrices($regionId, $request->products);

            return response()->json([
                'success' => true,
                'message' => '批量设置价格成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量设置价格失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量修改商品库存
     */
    public function batchUpdateStock(Request $request, $regionId)
    {
        $request->validate([
            'products' => 'required|array',
            'products.*.product_id' => 'required|integer|exists:products,id',
            'products.*.stock' => 'required|integer|min:0',
            'operation' => 'required|in:set,add,subtract'
        ]);

        try {
            $result = $this->regionPriceService->batchUpdateStock(
                $regionId, 
                $request->products, 
                $request->operation
            );

            return response()->json([
                'success' => true,
                'message' => '批量修改库存成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量修改库存失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量设置商品状态
     */
    public function batchUpdateStatus(Request $request, $regionId)
    {
        $request->validate([
            'product_ids' => 'required|array',
            'product_ids.*' => 'integer|exists:products,id',
            'status' => 'required|boolean'
        ]);

        try {
            $result = $this->regionPriceService->batchUpdateStatus(
                $regionId, 
                $request->product_ids, 
                $request->status
            );

            return response()->json([
                'success' => true,
                'message' => '批量修改状态成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量修改状态失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量设置有效期
     */
    public function batchSetValidityPeriod(Request $request, $regionId)
    {
        $request->validate([
            'product_ids' => 'required|array',
            'product_ids.*' => 'integer|exists:products,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date'
        ]);

        try {
            $result = $this->regionPriceService->batchSetValidityPeriod(
                $regionId,
                $request->product_ids,
                $request->start_date,
                $request->end_date
            );

            return response()->json([
                'success' => true,
                'message' => '批量设置有效期成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '批量设置有效期失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取可添加的商品列表
     */
    public function getAvailableProducts(Request $request, $regionId)
    {
        $request->validate([
            'keyword' => 'nullable|string|max:255',
            'category_id' => 'nullable|integer',
            'price_min' => 'nullable|numeric|min:0',
            'price_max' => 'nullable|numeric|min:0',
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100'
        ]);

        try {
            $result = $this->regionPriceService->getAvailableProducts(
                $regionId,
                $request->only(['keyword', 'category_id', 'price_min', 'price_max', 'page', 'limit'])
            );

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取可添加商品失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取商品的完整价格信息（包括会员价格）
     *
     * @param int $regionId
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductFullPriceInfo($regionId, $productId)
    {
        try {
            $product = \App\Product\Models\Product::findOrFail($productId);
            $priceService = app(\App\Product\Services\PriceCalculationService::class);
            
            // 获取游客价格
            $guestPrice = $priceService->calculatePrice($product, null, $regionId);
            
            // 获取所有会员等级的价格
            $membershipLevels = \App\Crm\Models\MembershipLevel::where('status', true)->get();
            $memberPrices = [];
            
            foreach ($membershipLevels as $level) {
                // 创建一个模拟用户来计算会员价格
                $mockUser = new \App\Models\User();
                $mockUser->membership_level_id = $level->id;
                $mockUser->setRelation('membershipLevel', $level);
                
                $memberPrice = $priceService->calculatePrice($product, $mockUser, $regionId);
                $memberPrices[] = [
                    'level' => $level,
                    'price_info' => $memberPrice
                ];
            }
            
            $result = [
                'product' => $product,
                'region_id' => $regionId,
                'guest_price' => $guestPrice,
                'member_prices' => $memberPrices,
                'region_price' => $this->regionPriceService->getProductRegionPrice($productId, $regionId)
            ];
            
            return response()->json(ApiResponse::success($result));
        } catch (\Exception $e) {
            Log::error('获取商品完整价格信息失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取商品完整价格信息失败', 500), 500);
        }
    }

    /**
     * 预览会员价格
     *
     * @param int $regionId
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function previewMemberPrices($regionId, $productId)
    {
        try {
            $product = \App\Product\Models\Product::findOrFail($productId);
            $priceService = app(\App\Product\Services\PriceCalculationService::class);
            
            // 获取所有会员等级
            $membershipLevels = \App\Crm\Models\MembershipLevel::where('status', true)->orderBy('level')->get();
            $priceComparison = [];
            
            // 游客价格
            $guestPrice = $priceService->calculatePrice($product, null, $regionId);
            $priceComparison[] = [
                'user_type' => 'guest',
                'level_name' => '游客',
                'level_id' => null,
                'base_price' => $guestPrice['base_price'],
                'final_price' => $guestPrice['final_price'],
                'discount_amount' => $guestPrice['total_discount'],
                'discount_info' => $guestPrice['discount_info'],
                'price_type' => $guestPrice['price_type']
            ];
            
            // 各会员等级价格
            foreach ($membershipLevels as $level) {
                $mockUser = new \App\Models\User();
                $mockUser->membership_level_id = $level->id;
                $mockUser->setRelation('membershipLevel', $level);
                
                $memberPrice = $priceService->calculatePrice($product, $mockUser, $regionId);
                
                $priceComparison[] = [
                    'user_type' => 'member',
                    'level_name' => $level->name,
                    'level_id' => $level->id,
                    'base_price' => $memberPrice['base_price'],
                    'final_price' => $memberPrice['final_price'],
                    'discount_amount' => $memberPrice['total_discount'],
                    'discount_info' => $memberPrice['discount_info'],
                    'price_type' => $memberPrice['price_type']
                ];
            }
            
            $result = [
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'base_price' => $product->price
                ],
                'region_id' => $regionId,
                'price_comparison' => $priceComparison
            ];
            
            return response()->json(ApiResponse::success($result));
        } catch (\Exception $e) {
            Log::error('预览会员价格失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('预览会员价格失败', 500), 500);
        }
    }

    /**
     * 获取区域价格统计信息
     *
     * @param int $regionId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRegionPriceStats($regionId)
    {
        try {
            $stats = $this->regionPriceService->getRegionPriceStats($regionId);
            return response()->json(ApiResponse::success($stats));
        } catch (\Exception $e) {
            Log::error('获取区域价格统计失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取区域价格统计失败', 500), 500);
        }
    }

    /**
     * 获取区域的分类价格规则列表
     *
     * @param int $regionId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategoryPrices($regionId)
    {
        try {
            $categoryPrices = \App\Product\Models\CategoryRegionPrice::where('region_id', $regionId)
                ->with(['category', 'region'])
                ->orderBy('created_at', 'desc')
                ->get();
            
            return response()->json(ApiResponse::success($categoryPrices));
        } catch (\Exception $e) {
            Log::error('获取分类价格规则失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取分类价格规则失败', 500), 500);
        }
    }

    /**
     * 设置分类区域价格
     *
     * @param Request $request
     * @param int $regionId
     * @return \Illuminate\Http\JsonResponse
     */
    public function setCategoryPrice(Request $request, $regionId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'category_id' => 'required|integer|exists:categories,id',
                'discount_type' => 'required|in:fixed_amount',
                'discount_value' => 'required|numeric|min:0',
                'max_discount' => 'nullable|numeric|min:0',
                'status' => 'nullable|boolean',
                'start_time' => 'nullable|date',
                'end_time' => 'nullable|date|after_or_equal:start_time',
                'description' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }

            $data = $request->all();
            $data['region_id'] = $regionId;
            $data['status'] = $data['status'] ?? true;

            // 检查是否已存在
            $existing = \App\Product\Models\CategoryRegionPrice::where('category_id', $data['category_id'])
                ->where('region_id', $regionId)
                ->first();

            if ($existing) {
                $existing->update($data);
                $categoryPrice = $existing;
            } else {
                $categoryPrice = \App\Product\Models\CategoryRegionPrice::create($data);
            }

            $categoryPrice->load(['category', 'region']);

            return response()->json(ApiResponse::success($categoryPrice, '分类价格规则设置成功'));
        } catch (\Exception $e) {
            Log::error('设置分类价格规则失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('设置分类价格规则失败', 500), 500);
        }
    }

    /**
     * 更新分类区域价格
     *
     * @param Request $request
     * @param int $regionId
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateCategoryPrice(Request $request, $regionId, $id)
    {
        try {
            $categoryPrice = \App\Product\Models\CategoryRegionPrice::where('id', $id)
                ->where('region_id', $regionId)
                ->firstOrFail();

            $validator = Validator::make($request->all(), [
                'discount_type' => 'required|in:fixed_amount',
                'discount_value' => 'required|numeric|min:0',
                'max_discount' => 'nullable|numeric|min:0',
                'status' => 'nullable|boolean',
                'start_time' => 'nullable|date',
                'end_time' => 'nullable|date|after_or_equal:start_time',
                'description' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }

            $categoryPrice->update($request->all());
            $categoryPrice->load(['category', 'region']);

            return response()->json(ApiResponse::success($categoryPrice, '分类价格规则更新成功'));
        } catch (\Exception $e) {
            Log::error('更新分类价格规则失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('更新分类价格规则失败', 500), 500);
        }
    }

    /**
     * 删除分类区域价格
     *
     * @param int $regionId
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteCategoryPrice($regionId, $id)
    {
        try {
            $categoryPrice = \App\Product\Models\CategoryRegionPrice::where('id', $id)
                ->where('region_id', $regionId)
                ->firstOrFail();

            $categoryPrice->delete();

            return response()->json(ApiResponse::success(null, '分类价格规则删除成功'));
        } catch (\Exception $e) {
            Log::error('删除分类价格规则失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('删除分类价格规则失败', 500), 500);
        }
    }

    /**
     * 获取所有区域的分类价格规则
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllCategoryPrices(Request $request)
    {
        try {
            $query = \App\Product\Models\CategoryRegionPrice::with(['category', 'region']);
            
            // 支持按分类过滤
            if ($request->has('category_id')) {
                $query->where('category_id', $request->input('category_id'));
            }
            
            // 支持按状态过滤
            if ($request->has('status')) {
                $query->where('status', $request->boolean('status'));
            }
            
            // 支持按区域过滤
            if ($request->has('region_ids')) {
                $regionIds = $request->input('region_ids');
                if (is_array($regionIds)) {
                    $query->whereIn('region_id', $regionIds);
                }
            }
            
            $categoryPrices = $query->orderBy('region_id')
                ->orderBy('category_id')
                ->orderBy('created_at', 'desc')
                ->get();
            
            return response()->json(ApiResponse::success($categoryPrices));
        } catch (\Exception $e) {
            Log::error('获取所有分类价格规则失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取所有分类价格规则失败', 500), 500);
        }
    }
} 