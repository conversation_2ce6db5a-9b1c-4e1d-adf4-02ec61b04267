<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('order_no')->after('id')->unique()->comment('订单号');
            $table->string('shipping_address')->nullable()->comment('收货地址');
            $table->string('contact_name')->nullable()->comment('联系人');
            $table->string('contact_phone')->nullable()->comment('联系电话');
            $table->enum('payment_method', ['wechat', 'alipay', 'bank', 'cash'])->nullable()->comment('支付方式');
            $table->string('payment_no')->nullable()->comment('支付单号');
            $table->timestamp('paid_at')->nullable()->comment('支付时间');
            $table->timestamp('shipped_at')->nullable()->comment('发货时间');
            $table->timestamp('delivered_at')->nullable()->comment('送达时间');
            $table->timestamp('cancelled_at')->nullable()->comment('取消时间');
            $table->text('notes')->nullable()->comment('订单备注');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn([
                'order_no',
                'shipping_address',
                'contact_name',
                'contact_phone',
                'payment_method',
                'payment_no',
                'paid_at',
                'shipped_at',
                'delivered_at',
                'cancelled_at',
                'notes'
            ]);
        });
    }
};
