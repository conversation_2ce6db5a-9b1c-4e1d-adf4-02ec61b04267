<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserAddress extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'contact_name',
        'contact_phone',
        'province',
        'city',
        'district',
        'address',
        'postal_code',
        'notes',
        'is_default',
        'latitude',
        'longitude'
    ];

    /**
     * 应该转换的属性
     *
     * @var array
     */
    protected $casts = [
        'is_default' => 'boolean',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    /**
     * 获取拥有此地址的用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取完整地址字符串
     */
    public function getFullAddressAttribute()
    {
        return ($this->province ?? '') . 
               ($this->city ? ' ' . $this->city : '') . 
               ($this->district ? ' ' . $this->district : '') . 
               ' ' . $this->address;
    }
} 