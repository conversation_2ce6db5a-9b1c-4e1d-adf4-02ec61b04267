<?php

require_once 'vendor/autoload.php';

// 模拟调整原因映射逻辑
function getTransactionTypeByReason(string $reason, float $adjustmentQuantity): string
{
    $reasonLower = strtolower(trim($reason));
    
    switch ($reasonLower) {
        // 损耗相关
        case 'loss':
        case 'damage':
        case 'expired':
        case 'broken':
            return 'inventory_loss';
        
        // 采购入库相关
        case 'purchase':
        case 'buy':
        case 'receive':
            return 'purchase_in';
            
        // 销售出库相关
        case 'sale':
        case 'sell':
        case 'delivery':
            return 'sales_out';
            
        // 销售退货入库
        case 'return_in':
        case 'customer_return':
            return 'return_in';
            
        // 采购退货出库
        case 'return_out':
        case 'purchase_return':
            return 'return_out';
            
        // 调拨相关
        case 'transfer':
            return $adjustmentQuantity > 0 ? 'transfer_in' : 'transfer_out';
            
        // 盘点调整相关
        case 'inventory':
        case 'adjustment':
        case 'count':
        default:
            return 'inventory_adjustment';
    }
}

// 测试映射
$testCases = [
    ['loss', -10, 'inventory_loss'],
    ['purchase', 20, 'purchase_in'],
    ['sale', -5, 'sales_out'],
    ['transfer', 15, 'transfer_in'],
    ['transfer', -8, 'transfer_out'],
    ['inventory', 3, 'inventory_adjustment'],
    ['unknown', 1, 'inventory_adjustment'],
];

echo "调整原因映射测试:\n";
echo "==================\n";

foreach ($testCases as [$reason, $quantity, $expected]) {
    $result = getTransactionTypeByReason($reason, $quantity);
    $status = $result === $expected ? '✓' : '✗';
    echo sprintf("%s %s (数量: %+d) -> %s (期望: %s)\n", 
        $status, $reason, $quantity, $result, $expected);
}

echo "\n测试完成!\n"; 