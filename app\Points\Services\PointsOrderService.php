<?php

namespace App\Points\Services;

use App\Models\User;
use App\Points\Models\PointsOrder;
use App\Points\Models\PointsOrderItem;
use App\Points\Models\PointsProduct;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PointsOrderService
{
    protected PointsService $pointsService;

    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * 创建积分订单
     */
    public function createOrder(int $userId, array $items, array $shippingInfo = []): array
    {
        try {
            DB::beginTransaction();

            $user = User::findOrFail($userId);
            
            // 验证商品和计算总价
            $orderData = $this->validateAndCalculateOrder($user, $items);
            
            if (!$orderData['success']) {
                return $orderData;
            }

            // 创建订单
            $order = PointsOrder::create([
                'user_id' => $userId,
                'order_no' => PointsOrder::generateOrderNo(),
                'total_points' => $orderData['total_points'],
                'cash_amount' => $orderData['cash_amount'],
                'status' => $orderData['cash_amount'] > 0 ? PointsOrder::STATUS_PENDING : PointsOrder::STATUS_PAID,
                'shipping_address' => $shippingInfo['address'] ?? null,
                'contact_name' => $shippingInfo['contact_name'] ?? $user->name,
                'contact_phone' => $shippingInfo['contact_phone'] ?? $user->phone,
                'delivery_method' => $this->determineDeliveryMethod($orderData['items']),
                'delivery_date' => $shippingInfo['delivery_date'] ?? null,
                'delivery_time' => $shippingInfo['delivery_time'] ?? null,
                'notes' => $shippingInfo['notes'] ?? null,
            ]);

            // 创建订单项
            foreach ($orderData['items'] as $itemData) {
                PointsOrderItem::create([
                    'points_order_id' => $order->id,
                    'points_product_id' => $itemData['product_id'],
                    'product_name' => $itemData['product_name'],
                    'product_image' => $itemData['product_image'],
                    'points_price' => $itemData['points_price'],
                    'cash_price' => $itemData['cash_price'],
                    'quantity' => $itemData['quantity'],
                    'total_points' => $itemData['total_points'],
                    'total_cash' => $itemData['total_cash'],
                ]);

                // 扣减库存和增加兑换次数
                $product = PointsProduct::find($itemData['product_id']);
                if ($product) {
                    $product->decreaseStock($itemData['quantity']);
                    $product->increaseExchangedCount($itemData['quantity']);
                }
            }

            // 如果是纯积分订单，直接扣减积分并完成订单
            if ($order->isPurePointsOrder()) {
                $this->processPurePointsOrder($order);
            }

            DB::commit();

            Log::info('积分订单创建成功', [
                'order_id' => $order->id,
                'user_id' => $userId,
                'total_points' => $order->total_points,
                'cash_amount' => $order->cash_amount
            ]);

            return [
                'success' => true,
                'order' => $order->load('items'),
                'message' => '订单创建成功'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('积分订单创建失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 验证商品并计算订单总价
     */
    private function validateAndCalculateOrder(User $user, array $items): array
    {
        $totalPoints = 0;
        $totalCash = 0;
        $orderItems = [];

        foreach ($items as $item) {
            $productId = $item['product_id'];
            $quantity = $item['quantity'];

            if ($quantity <= 0) {
                return [
                    'success' => false,
                    'message' => '商品数量必须大于0'
                ];
            }

            $product = PointsProduct::find($productId);
            if (!$product) {
                return [
                    'success' => false,
                    'message' => "商品不存在：{$productId}"
                ];
            }

            // 检查商品是否可兑换
            if (!$product->canUserExchange($user)) {
                return [
                    'success' => false,
                    'message' => "商品不可兑换：{$product->name}"
                ];
            }

            // 检查库存
            if ($product->category === PointsProduct::CATEGORY_PHYSICAL && 
                $product->stock_quantity < $quantity) {
                return [
                    'success' => false,
                    'message' => "商品库存不足：{$product->name}"
                ];
            }

            $itemPoints = $product->points_price * $quantity;
            $itemCash = $product->cash_price * $quantity;

            $totalPoints += $itemPoints;
            $totalCash += $itemCash;

            $orderItems[] = [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'product_image' => $product->image,
                'points_price' => $product->points_price,
                'cash_price' => $product->cash_price,
                'quantity' => $quantity,
                'total_points' => $itemPoints,
                'total_cash' => $itemCash,
            ];
        }

        // 检查用户积分是否充足
        if ($user->member_points < $totalPoints) {
            return [
                'success' => false,
                'message' => '积分不足'
            ];
        }

        return [
            'success' => true,
            'total_points' => $totalPoints,
            'cash_amount' => $totalCash,
            'items' => $orderItems
        ];
    }

    /**
     * 确定配送方式
     */
    private function determineDeliveryMethod(array $items): string
    {
        $hasPhysical = false;
        
        foreach ($items as $item) {
            $product = PointsProduct::find($item['product_id']);
            if ($product && $product->category === PointsProduct::CATEGORY_PHYSICAL) {
                $hasPhysical = true;
                break;
            }
        }

        return $hasPhysical ? PointsOrder::DELIVERY_METHOD_EXPRESS : PointsOrder::DELIVERY_METHOD_VIRTUAL;
    }

    /**
     * 处理纯积分订单
     */
    private function processPurePointsOrder(PointsOrder $order): void
    {
        // 扣减用户积分
        $this->pointsService->deductPoints(
            $order->user_id,
            $order->total_points,
            'points_mall',
            $order->id,
            "积分商城兑换：{$order->order_no}"
        );

        // 如果是虚拟商品，直接标记为已完成
        if ($order->delivery_method === PointsOrder::DELIVERY_METHOD_VIRTUAL) {
            $order->markAsCompleted();
        } else {
            $order->markAsPaid();
        }
    }

    /**
     * 支付积分订单（混合支付）
     */
    public function payOrder(int $orderId, string $paymentNo): bool
    {
        try {
            DB::beginTransaction();

            $order = PointsOrder::findOrFail($orderId);

            if (!$order->canBePaid()) {
                throw new \Exception('订单不能支付');
            }

            // 扣减用户积分
            if ($order->total_points > 0) {
                $this->pointsService->deductPoints(
                    $order->user_id,
                    $order->total_points,
                    'points_mall',
                    $order->id,
                    "积分商城兑换：{$order->order_no}"
                );
            }

            // 标记订单为已支付
            $order->markAsPaid($paymentNo);

            // 如果是虚拟商品，直接完成订单
            if ($order->delivery_method === PointsOrder::DELIVERY_METHOD_VIRTUAL) {
                $order->markAsCompleted();
            }

            DB::commit();

            Log::info('积分订单支付成功', [
                'order_id' => $orderId,
                'payment_no' => $paymentNo
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('积分订单支付失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 取消积分订单
     */
    public function cancelOrder(int $orderId, string $reason = null): bool
    {
        try {
            DB::beginTransaction();

            $order = PointsOrder::findOrFail($orderId);

            if (!$order->canBeCancelled()) {
                throw new \Exception('订单不能取消');
            }

            // 如果已经扣减了积分，需要退还
            if ($order->status === PointsOrder::STATUS_PAID && $order->total_points > 0) {
                $this->pointsService->refundPoints(
                    $order->user_id,
                    $order->total_points,
                    'points_mall',
                    $order->id,
                    "积分订单取消退还：{$order->order_no}"
                );
            }

            // 恢复库存
            foreach ($order->items as $item) {
                $product = $item->pointsProduct;
                if ($product && $product->category === PointsProduct::CATEGORY_PHYSICAL) {
                    $product->increment('stock_quantity', $item->quantity);
                    $product->decrement('exchanged_count', $item->quantity);
                }
            }

            // 取消订单
            $order->cancel($reason);

            DB::commit();

            Log::info('积分订单取消成功', [
                'order_id' => $orderId,
                'reason' => $reason
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('积分订单取消失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发货
     */
    public function shipOrder(int $orderId): bool
    {
        try {
            $order = PointsOrder::findOrFail($orderId);

            if (!$order->canBeShipped()) {
                throw new \Exception('订单不能发货');
            }

            $order->markAsShipped();

            Log::info('积分订单发货成功', ['order_id' => $orderId]);
            return true;

        } catch (\Exception $e) {
            Log::error('积分订单发货失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 确认收货
     */
    public function confirmDelivery(int $orderId): bool
    {
        try {
            $order = PointsOrder::findOrFail($orderId);

            if ($order->status !== PointsOrder::STATUS_SHIPPED) {
                throw new \Exception('订单状态不正确');
            }

            $order->markAsDelivered();

            Log::info('积分订单确认收货成功', ['order_id' => $orderId]);
            return true;

        } catch (\Exception $e) {
            Log::error('积分订单确认收货失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取用户积分订单列表
     */
    public function getUserOrders(int $userId, array $filters = []): array
    {
        $query = PointsOrder::byUser($userId)->with('items.pointsProduct');

        // 状态筛选
        if (!empty($filters['status'])) {
            $query->byStatus($filters['status']);
        }

        // 配送方式筛选
        if (!empty($filters['delivery_method'])) {
            $query->where('delivery_method', $filters['delivery_method']);
        }

        // 排序
        $query->orderBy('created_at', 'desc');

        // 分页
        $page = $filters['page'] ?? 1;
        $perPage = $filters['per_page'] ?? 20;
        
        $result = $query->paginate($perPage, ['*'], 'page', $page);

        return [
            'data' => $result->items(),
            'current_page' => $result->currentPage(),
            'last_page' => $result->lastPage(),
            'per_page' => $result->perPage(),
            'total' => $result->total(),
        ];
    }

    /**
     * 获取订单详情
     */
    public function getOrderDetail(int $orderId, int $userId = null): ?PointsOrder
    {
        $query = PointsOrder::with(['items.pointsProduct', 'user']);

        if ($userId) {
            $query->byUser($userId);
        }

        return $query->find($orderId);
    }
} 