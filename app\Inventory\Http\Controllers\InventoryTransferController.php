<?php

namespace App\Inventory\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Inventory\Models\Inventory;
use App\Inventory\Models\InventoryTransaction;
use App\Inventory\Models\InventoryTransactionType;
use App\Product\Models\Product;
use App\Warehouse\Models\Warehouse;
use App\Unit\Services\UnitService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class InventoryTransferController extends Controller
{
    protected $unitService;
    
    public function __construct(UnitService $unitService)
    {
        $this->unitService = $unitService;
    }
    
    /**
     * 库存调拨（从一个仓库转移到另一个仓库）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function transfer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'from_warehouse_id' => 'required|exists:warehouses,id',
            'to_warehouse_id' => 'required|exists:warehouses,id|different:from_warehouse_id',
            'quantity' => 'required|numeric|gt:0',
            'unit_id' => 'required|exists:units,id',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            DB::beginTransaction();
            
            $product = Product::findOrFail($request->product_id);
            $fromWarehouse = Warehouse::findOrFail($request->from_warehouse_id);
            $toWarehouse = Warehouse::findOrFail($request->to_warehouse_id);
            
            // 检查源仓库是否有足够的库存
            $sourceInventory = Inventory::where('product_id', $request->product_id)
                ->where('warehouse_id', $request->from_warehouse_id)
                ->first();
                
            if (!$sourceInventory) {
                return response()->json(ApiResponse::error('源仓库没有此商品的库存', 422), 422);
            }
            
            // 将调拨数量转换为源仓库库存单位
            $transferQuantityInSourceUnit = $this->unitService->convertForProduct(
                $request->quantity,
                $request->unit_id,
                $sourceInventory->unit,
                $request->product_id
            );
            
            if ($sourceInventory->stock < $transferQuantityInSourceUnit) {
                return response()->json(ApiResponse::error('源仓库库存不足', 422), 422);
            }
            
            // 获取调拨出库和入库事务类型
            $outTransactionType = InventoryTransactionType::where('code', 'transfer_out')->firstOrFail();
            $inTransactionType = InventoryTransactionType::where('code', 'transfer_in')->firstOrFail();
            
            // 创建调拨出库事务
            $outTransaction = InventoryTransaction::create([
                'transaction_type_id' => $outTransactionType->id,
                'product_id' => $request->product_id,
                'warehouse_id' => $request->from_warehouse_id,
                'quantity' => -$request->quantity, // 负数表示减少
                'unit_id' => $request->unit_id,
                'status' => 'completed',
                'notes' => $request->notes ? $request->notes . ' (调拨至: ' . $toWarehouse->location . ')' : '调拨至: ' . $toWarehouse->location,
                'created_by' => auth()->id() ?? 1,
                'updated_by' => auth()->id() ?? 1,
            ]);
            
            // 创建调拨入库事务
            $inTransaction = InventoryTransaction::create([
                'transaction_type_id' => $inTransactionType->id,
                'product_id' => $request->product_id,
                'warehouse_id' => $request->to_warehouse_id,
                'quantity' => $request->quantity, // 正数表示增加
                'unit_id' => $request->unit_id,
                'status' => 'completed',
                'notes' => $request->notes ? $request->notes . ' (从仓库调拨: ' . $fromWarehouse->location . ')' : '从仓库调拨: ' . $fromWarehouse->location,
                'created_by' => auth()->id() ?? 1,
                'updated_by' => auth()->id() ?? 1,
            ]);
            
            // 更新源仓库库存
            $sourceInventory->stock -= $transferQuantityInSourceUnit;
            $sourceInventory->save();
            
            // 更新或创建目标仓库库存
            $targetInventory = Inventory::firstOrCreate(
                [
                    'product_id' => $request->product_id,
                    'warehouse_id' => $request->to_warehouse_id,
                ],
                [
                    'unit' => $sourceInventory->unit, // 使用相同的单位
                    'stock' => 0,
                ]
            );
            
            // 将调拨数量转换为目标仓库库存单位
            $transferQuantityInTargetUnit = $this->unitService->convertForProduct(
                $request->quantity,
                $request->unit_id,
                $targetInventory->unit,
                $request->product_id
            );
            
            $targetInventory->stock += $transferQuantityInTargetUnit;
            $targetInventory->save();
            
            // 更新商品总库存（实际上调拨不影响总库存，但需要更新仓库总库存）
            $fromWarehouse->updateTotalStock();
            $toWarehouse->updateTotalStock();
            
            DB::commit();
            
            return response()->json(ApiResponse::success([
                'out_transaction' => $outTransaction,
                'in_transaction' => $inTransaction,
                'source_inventory' => $sourceInventory,
                'target_inventory' => $targetInventory,
            ], '库存调拨成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('库存调拨失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取调拨历史记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTransferHistory(Request $request)
    {
        $query = InventoryTransaction::with(['transactionType', 'product', 'warehouse', 'unit', 'creator'])
            ->whereHas('transactionType', function($q) {
                $q->whereIn('code', ['transfer_out', 'transfer_in']);
            });
            
        // 商品筛选
        if ($request->has('product_id')) {
            $query->where('product_id', $request->product_id);
        }
        
        // 仓库筛选（源仓库或目标仓库）
        if ($request->has('warehouse_id')) {
            $query->where('warehouse_id', $request->warehouse_id);
        }
        
        // 日期范围筛选
        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        
        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }
        
        // 排序
        $orderBy = $request->order_by ?? 'created_at';
        $direction = $request->direction ?? 'desc';
        $query->orderBy($orderBy, $direction);
        
        $transfers = $query->paginate($request->per_page ?? 15);
        
        return response()->json(ApiResponse::success($transfers));
    }
} 