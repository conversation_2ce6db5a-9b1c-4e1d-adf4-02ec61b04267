<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 默认打印驱动
    |--------------------------------------------------------------------------
    |
    | 这里指定默认使用的打印驱动。支持的驱动：clodop
    |
    */
    'default' => env('PRINT_DRIVER', 'clodop'),

    /*
    |--------------------------------------------------------------------------
    | 打印驱动配置
    |--------------------------------------------------------------------------
    |
    | 这里配置各种打印驱动的参数
    |
    */
    'drivers' => [
        'clodop' => [
            'service_url' => env('CLODOP_SERVICE_URL', 'http://localhost:8000/CLodopfuncs.js'),
            'license' => env('CLODOP_LICENSE', ''),
            'default_printer' => env('CLODOP_DEFAULT_PRINTER', ''),
            'debug' => env('PRINTING_DEBUG', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 打印模板配置
    |--------------------------------------------------------------------------
    |
    | 配置打印模板的相关参数
    |
    */
    'templates' => [
        'order' => [
            'type' => 'receipt',
            'paper_width' => 80, // mm
            'font_size' => 12,
            'font_name' => '宋体',
        ],
        'delivery' => [
            'type' => 'normal',
            'paper_size' => 'A4',
            'font_size' => 12,
            'font_name' => '宋体',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 调试模式
    |--------------------------------------------------------------------------
    |
    | 开启调试模式会记录详细的打印日志
    |
    */
    'debug' => env('PRINTING_DEBUG', false),
]; 