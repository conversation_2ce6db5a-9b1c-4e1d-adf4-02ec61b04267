// pages/points/orders/index.js - 积分订单列表页
const PointsAPI = require('../../../utils/pointsApi');
const app = getApp();

Page({
  data: {
    // 订单列表
    orders: [],
    
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 分页
    page: 1,
    hasMore: true,
    
    // 筛选条件
    currentTab: 'all',
    tabs: [
      { key: 'all', name: '全部', count: 0 },
      { key: 'pending', name: '待支付', count: 0 },
      { key: 'paid', name: '已支付', count: 0 },
      { key: 'shipped', name: '已发货', count: 0 },
      { key: 'completed', name: '已完成', count: 0 }
    ],
    
    // 订单统计
    orderStats: {}
  },

  onLoad() {
    this.initPage();
  },

  onShow() {
    // 刷新订单状态
    this.refreshOrders();
  },

  onPullDownRefresh() {
    this.refreshOrders();
  },

  onReachBottom() {
    this.loadMoreOrders();
  },

  // ==================== 页面初始化 ====================

  async initPage() {
    wx.showLoading({ title: '加载中...' });

    try {
      await Promise.all([
        this.loadOrders(true),
        this.loadOrderStats()
      ]);
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  async refreshOrders() {
    this.setData({
      refreshing: true,
      page: 1,
      orders: [],
      hasMore: true
    });

    try {
      await Promise.all([
        this.loadOrders(true),
        this.loadOrderStats()
      ]);
    } catch (error) {
      console.error('刷新失败:', error);
    } finally {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  // ==================== 数据加载 ====================

  async loadOrders(reset = false) {
    if (this.data.loading) return;
    if (!reset && !this.data.hasMore) return;

    this.setData({ loading: true });

    try {
      const params = {
        page: reset ? 1 : this.data.page,
        per_page: 10
      };

      // 添加状态筛选
      if (this.data.currentTab !== 'all') {
        params.status = this.data.currentTab;
      }

      const result = await PointsAPI.getUserPointsOrders(params);
      const newOrders = result.data.data || [];

      this.setData({
        orders: reset ? newOrders : [...this.data.orders, ...newOrders],
        page: reset ? 2 : this.data.page + 1,
        hasMore: newOrders.length >= params.per_page,
        loading: false
      });
    } catch (error) {
      console.error('加载订单失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载订单失败',
        icon: 'error'
      });
    }
  },

  async loadMoreOrders() {
    await this.loadOrders();
  },

  async loadOrderStats() {
    try {
      const result = await PointsAPI.getOrderStatusStats();
      const stats = result.data || {};

      // 更新tab计数
      const tabs = this.data.tabs.map(tab => ({
        ...tab,
        count: stats[tab.key] || 0
      }));

      this.setData({
        orderStats: stats,
        tabs
      });
    } catch (error) {
      console.error('加载订单统计失败:', error);
    }
  },

  // ==================== 用户操作 ====================

  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    
    if (tab === this.data.currentTab) return;

    this.setData({
      currentTab: tab,
      page: 1,
      orders: [],
      hasMore: true
    });

    this.loadOrders(true);
  },

  async onPayOrder(e) {
    e.stopPropagation();
    const orderId = e.currentTarget.dataset.orderId;
    
    wx.showModal({
      title: '支付确认',
      content: '确认支付该订单吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '处理中...' });
            
            // 这里应该调用支付接口
            // await PointsAPI.payPointsOrder(orderId, paymentNo);
            
            wx.hideLoading();
            wx.showToast({
              title: '支付功能开发中',
              icon: 'none'
            });
          } catch (error) {
            wx.hideLoading();
            console.error('支付失败:', error);
            wx.showToast({
              title: error.message || '支付失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  async onCancelOrder(e) {
    e.stopPropagation();
    const orderId = e.currentTarget.dataset.orderId;
    
    wx.showModal({
      title: '取消订单',
      content: '确认取消该订单吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '取消中...' });
            
            await PointsAPI.cancelPointsOrder(orderId, '用户主动取消');
            
            wx.hideLoading();
            wx.showToast({
              title: '订单已取消',
              icon: 'success'
            });
            
            // 刷新订单列表
            this.refreshOrders();
          } catch (error) {
            wx.hideLoading();
            console.error('取消订单失败:', error);
            wx.showToast({
              title: error.message || '取消失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  async onConfirmDelivery(e) {
    e.stopPropagation();
    const orderId = e.currentTarget.dataset.orderId;
    
    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '确认中...' });
            
            await PointsAPI.confirmDelivery(orderId);
            
            wx.hideLoading();
            wx.showToast({
              title: '确认收货成功',
              icon: 'success'
            });
            
            // 刷新订单列表
            this.refreshOrders();
          } catch (error) {
            wx.hideLoading();
            console.error('确认收货失败:', error);
            wx.showToast({
              title: error.message || '确认失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  // ==================== 页面跳转 ====================

  goToOrderDetail(e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.navigateTo({
      url: `/pages/points/orders/detail/index?id=${orderId}`
    });
  },

  goToProduct(e) {
    e.stopPropagation();
    const productId = e.currentTarget.dataset.productId;
    wx.navigateTo({
      url: `/pages/points/product/index?id=${productId}`
    });
  },

  // ==================== 工具方法 ====================

  formatPoints(points) {
    return PointsAPI.formatPoints(points);
  },

  formatOrderStatus(status) {
    return PointsAPI.formatOrderStatus(status);
  },

  formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      const hours = Math.floor(diff / (1000 * 60 * 60));
      if (hours === 0) {
        const minutes = Math.floor(diff / (1000 * 60));
        return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
      }
      return `${hours}小时前`;
    } else if (days === 1) {
      return '昨天';
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString();
    }
  },

  getOrderStatusColor(status) {
    const colorMap = {
      pending: '#FF9800',
      paid: '#2196F3',
      shipped: '#4CAF50',
      delivered: '#4CAF50',
      completed: '#4CAF50',
      cancelled: '#757575'
    };
    return colorMap[status] || '#757575';
  },

  getOrderActions(order) {
    const actions = [];
    
    switch (order.status) {
      case 'pending':
        actions.push({ key: 'pay', name: '立即支付', type: 'primary' });
        actions.push({ key: 'cancel', name: '取消订单', type: 'default' });
        break;
      case 'shipped':
        actions.push({ key: 'confirm', name: '确认收货', type: 'primary' });
        break;
      case 'delivered':
        actions.push({ key: 'confirm', name: '确认收货', type: 'primary' });
        break;
    }
    
    return actions;
  },

  onOrderAction(e) {
    e.stopPropagation();
    const { action, orderId } = e.currentTarget.dataset;
    
    switch (action) {
      case 'pay':
        this.onPayOrder(e);
        break;
      case 'cancel':
        this.onCancelOrder(e);
        break;
      case 'confirm':
        this.onConfirmDelivery(e);
        break;
    }
  }
}); 