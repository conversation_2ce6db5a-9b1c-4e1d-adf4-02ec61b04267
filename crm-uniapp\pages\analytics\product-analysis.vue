<template>
	<view class="product-analysis-container">
		<!-- 页面头部 -->
		<view class="header-section">
			<view class="header-title">
				<text class="title-text">商品分析</text>
				<text class="subtitle-text">商品偏好与销售趋势分析</text>
			</view>
		</view>

		<!-- 筛选区域 -->
		<view class="filter-section">
			<view class="filter-row">
				<view class="filter-item">
					<text class="filter-label">时间范围</text>
					<picker mode="selector" :value="selectedPeriodIndex" :range="timePeriods" range-key="label" @change="onPeriodChange">
						<view class="picker-value">{{ timePeriods[selectedPeriodIndex].label }}</view>
					</picker>
				</view>
				<view class="filter-item">
					<text class="filter-label">商品分类</text>
					<picker mode="selector" :value="selectedCategoryIndex" :range="categories" range-key="label" @change="onCategoryChange">
						<view class="picker-value">{{ categories[selectedCategoryIndex].label }}</view>
					</picker>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-section" v-if="loading">
			<text class="loading-text">正在加载数据...</text>
		</view>

		<!-- 数据内容 -->
		<view v-else>
			<!-- 热门商品排行 -->
			<view class="hot-products-section">
				<view class="section-title">
					<text class="title-text">🔥 热门商品排行</text>
					<text class="subtitle">基于销量和收入综合排名</text>
				</view>
				<view class="products-list" v-if="hotProducts.length > 0">
					<view class="product-item" v-for="(product, index) in hotProducts" :key="product.uniqueKey || product.id || index">
						<view class="product-rank" :class="getRankClass(index)">
							<text class="rank-text">{{ index + 1 }}</text>
						</view>
						<view class="product-info">
							<text class="product-name">{{ product.name || product.product_name || '未知商品' }}</text>
							<text class="product-category">{{ product.category || '未分类' }}</text>
						</view>
						<view class="product-stats">
							<view class="stat-item">
								<text class="stat-label">销量</text>
								<text class="stat-value">{{ product.sales || product.total_quantity || 0 }}</text>
							</view>
							<view class="stat-item">
								<text class="stat-label">收入</text>
								<text class="stat-value">¥{{ product.revenue || product.total_amount || 0 }}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="empty-state" v-else>
					<text class="empty-text">暂无热门商品数据</text>
				</view>
			</view>

			<!-- 品类偏好分析 -->
			<view class="category-section">
				<view class="section-title">
					<text class="title-text">📊 品类偏好分析</text>
				</view>
				<view class="category-chart" v-if="categoryPreference.length > 0">
					<view class="chart-container">
						<view class="pie-chart">
							<view class="pie-slice" v-for="(category, index) in categoryPreference" :key="category.uniqueKey || index"
								:style="getPieSliceStyle(category, index)">
							</view>
							<view class="pie-center">
								<text class="center-text">品类分布</text>
							</view>
						</view>
					</view>
					<view class="legend-list">
						<view class="legend-item" v-for="(category, index) in categoryPreference" :key="category.uniqueKey || index">
							<view class="legend-color" :style="{ backgroundColor: getCategoryColor(index) }"></view>
							<text class="legend-label">{{ category.name || category.category || '未知分类' }}</text>
							<text class="legend-value">{{ category.percentage || 0 }}%</text>
						</view>
					</view>
				</view>
				<view class="empty-state" v-else>
					<text class="empty-text">暂无品类偏好数据</text>
				</view>
			</view>

			<!-- 价格敏感度分析 -->
			<view class="price-sensitivity-section">
				<view class="section-title">
					<text class="title-text">💰 价格敏感度分析</text>
				</view>
				<view class="price-chart" v-if="priceRanges.length > 0">
					<view class="price-range" v-for="(range, index) in priceRanges" :key="range.uniqueKey || index">
						<view class="range-header">
							<text class="range-label">{{ range.label || range.range || '未知价格区间' }}</text>
							<text class="range-count">{{ range.count || range.order_count || 0 }}件</text>
						</view>
						<view class="range-bar">
							<view class="bar-fill" :style="{ width: (range.percentage || 0) + '%' }"></view>
						</view>
						<view class="range-details">
							<text class="detail-text">平均价格: ¥{{ range.avgPrice || range.avg_price || 0 }}</text>
							<text class="detail-text">转化率: {{ range.conversionRate || range.conversion_rate || 0 }}%</text>
						</view>
					</view>
				</view>
				<view class="empty-state" v-else>
					<text class="empty-text">暂无价格敏感度数据</text>
				</view>
			</view>

			<!-- 购买关联分析 -->
			<view class="association-section">
				<view class="section-title">
					<text class="title-text">🔗 购买关联分析</text>
					<text class="subtitle">经常一起购买的商品组合</text>
				</view>
				<view class="association-list" v-if="associationRules.length > 0">
					<view class="association-item" v-for="(item, index) in associationRules" :key="item.uniqueKey || index">
						<view class="association-products">
							<view class="product-group">
								<text class="group-label">购买了</text>
								<text class="product-names">{{ getProductNames(item.antecedent) }}</text>
							</view>
							<view class="arrow">→</view>
							<view class="product-group">
								<text class="group-label">还会买</text>
								<text class="product-names">{{ getProductNames(item.consequent) }}</text>
							</view>
						</view>
						<view class="association-stats">
							<view class="stat-badge">
								<text class="badge-label">置信度</text>
								<text class="badge-value">{{ item.confidence || 0 }}%</text>
							</view>
							<view class="stat-badge">
								<text class="badge-label">支持度</text>
								<text class="badge-value">{{ item.support || 0 }}%</text>
							</view>
						</view>
					</view>
				</view>
				<view class="empty-state" v-else>
					<text class="empty-text">暂无关联分析数据</text>
				</view>
			</view>

			<!-- 商品生命周期分析 -->
			<view class="lifecycle-section">
				<view class="section-title">
					<text class="title-text">📈 商品生命周期分析</text>
				</view>
				<view class="lifecycle-tabs">
					<view 
						class="lifecycle-tab" 
						v-for="(stage, index) in lifecycleStages" 
						:key="index"
						:class="{ active: selectedStage === stage.value }"
						@tap="selectStage(stage.value)"
					>
						<text class="tab-text">{{ stage.label }}</text>
					</view>
				</view>
				<view class="lifecycle-content">
					<view class="stage-products" v-if="stageProducts.length > 0">
						<view class="stage-product" v-for="(product, index) in stageProducts" :key="product.uniqueKey || product.id || index">
							<view class="product-icon" :class="selectedStage">
								<text class="icon-text">{{ getStageIcon(selectedStage) }}</text>
							</view>
							<view class="product-details">
								<text class="product-name">{{ product.name || product.product_name || '未知商品' }}</text>
								<text class="product-trend">{{ product.trend || product.description || '暂无描述' }}</text>
							</view>
							<view class="product-metrics">
								<text class="metric-value">{{ product.salesTrend || product.sales_trend || '暂无数据' }}</text>
							</view>
						</view>
					</view>
					<view class="empty-state" v-else>
						<text class="empty-text">该阶段暂无商品数据</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import analyticsApi from '../../api/analytics.js'

export default {
	data() {
		return {
			loading: false,
			selectedPeriodIndex: 1,
			selectedCategoryIndex: 0,
			selectedStage: 'growth',
			timePeriods: [
				{ label: '最近7天', value: 'last_7_days' },
				{ label: '最近30天', value: 'last_30_days' },
				{ label: '最近90天', value: 'last_90_days' }
			],
			categories: [
				{ label: '全部分类', value: 'all' },
				{ label: '蔬菜类', value: 'vegetables' },
				{ label: '水果类', value: 'fruits' },
				{ label: '肉类', value: 'meat' },
				{ label: '海鲜类', value: 'seafood' }
			],
			lifecycleStages: [
				{ label: '导入期', value: 'introduction' },
				{ label: '成长期', value: 'growth' },
				{ label: '成熟期', value: 'maturity' },
				{ label: '衰退期', value: 'decline' }
			],
			hotProducts: [],
			categoryPreference: [],
			priceRanges: [],
			associationRules: [],
			stageProducts: []
		}
	},
	
	onLoad() {
		this.loadProductAnalysis()
	},
	
	onPullDownRefresh() {
		this.refreshData()
	},
	
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 刷新数据
		async refreshData() {
			await this.loadProductAnalysis()
			uni.stopPullDownRefresh()
			uni.showToast({
				title: '刷新成功',
				icon: 'success',
				duration: 1500
			})
		},
		
		// 时间周期变化
		onPeriodChange(e) {
			this.selectedPeriodIndex = e.detail.value
			this.loadProductAnalysis()
		},
		
		// 分类变化
		onCategoryChange(e) {
			this.selectedCategoryIndex = e.detail.value
			this.loadProductAnalysis()
		},
		
		// 选择生命周期阶段
		selectStage(stage) {
			this.selectedStage = stage
			this.loadStageProducts()
		},
		
		// 加载商品分析数据
		async loadProductAnalysis() {
			this.loading = true
			try {
				console.log('开始加载商品偏好分析数据...')
				
				const params = {
					period: this.timePeriods[this.selectedPeriodIndex].value,
					category: this.categories[this.selectedCategoryIndex].value
				}
				
				console.log('请求参数:', params)
				
				const response = await analyticsApi.getProductPreferenceAnalysis(params)
				console.log('商品偏好分析响应:', response)
				
				if (response && response.data) {
					// 处理热门商品数据
					this.hotProducts = (response.data.hotProducts || response.data.hot_products || []).map((item, index) => ({
						...item,
						uniqueKey: `hot-${index}-${Date.now()}`
					}))
					
					// 处理品类偏好数据
					this.categoryPreference = (response.data.category_preference || response.data.categoryPreference || []).map((item, index) => ({
						...item,
						uniqueKey: `category-${index}-${Date.now()}`
					}))
					
					// 处理价格区间数据
					this.priceRanges = (response.data.price_ranges || response.data.priceRanges || []).map((item, index) => ({
						...item,
						uniqueKey: `price-${index}-${Date.now()}`
					}))
					
					// 处理关联规则数据
					this.associationRules = (response.data.association_rules || response.data.associationRules || []).map((item, index) => ({
						...item,
						uniqueKey: `association-${index}-${Date.now()}`
					}))
					
					console.log('处理后的数据:', {
						hotProducts: this.hotProducts,
						categoryPreference: this.categoryPreference,
						priceRanges: this.priceRanges,
						associationRules: this.associationRules
					})
				}
				
				// 加载生命周期数据
				await this.loadStageProducts()
				
			} catch (error) {
				console.error('加载商品分析数据失败:', error)
				
				// 显示具体错误信息
				let errorMessage = '加载商品分析数据失败'
				if (error.response) {
					if (error.response.status === 401) {
						errorMessage = '登录已过期，请重新登录'
						// 可以跳转到登录页
						setTimeout(() => {
							uni.reLaunch({ url: '/pages/login/login' })
						}, 2000)
					} else if (error.response.status === 403) {
						errorMessage = '没有权限访问该数据'
					} else {
						errorMessage = `请求失败: ${error.response.status}`
					}
				} else if (error.message) {
					errorMessage = error.message
				}
				
				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
				
				// 清空数据
				this.hotProducts = []
				this.categoryPreference = []
				this.priceRanges = []
				this.associationRules = []
				this.stageProducts = []
			} finally {
				this.loading = false
			}
		},
		
		// 加载阶段商品
		async loadStageProducts() {
			try {
				console.log('加载生命周期阶段数据:', this.selectedStage)
				
				const params = {
					stage: this.selectedStage,
					period: this.timePeriods[this.selectedPeriodIndex].value
				}
				
				const response = await analyticsApi.getProductLifecycleAnalysis(params)
				console.log('生命周期分析响应:', response)
				
				if (response && response.data) {
					this.stageProducts = (response.data.products || response.data || []).map((item, index) => ({
						...item,
						uniqueKey: `stage-${index}-${Date.now()}`
					}))
				} else {
					this.stageProducts = []
				}
				
			} catch (error) {
				console.error('加载阶段商品数据失败:', error)
				this.stageProducts = []
			}
		},
		
		// 获取商品名称列表
		getProductNames(products) {
			if (!products) return '暂无数据'
			if (Array.isArray(products)) {
				return products.join(', ')
			}
			return String(products)
		},
		
		// 获取排名样式
		getRankClass(index) {
			if (index === 0) return 'rank-gold'
			if (index === 1) return 'rank-silver'
			if (index === 2) return 'rank-bronze'
			return 'rank-normal'
		},
		
		// 获取饼图切片样式
		getPieSliceStyle(category, index) {
			const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
			const color = colors[index % colors.length]
			
			// 简化的饼图样式，实际项目中可能需要更复杂的计算
			return {
				backgroundColor: color,
				width: '60rpx',
				height: '60rpx',
				borderRadius: '50%',
				margin: '4rpx'
			}
		},
		
		// 获取分类颜色
		getCategoryColor(index) {
			const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
			return colors[index % colors.length]
		},
		
		// 获取阶段图标
		getStageIcon(stage) {
			const icons = {
				introduction: '🌱',
				growth: '📈',
				maturity: '🏆',
				decline: '📉'
			}
			return icons[stage] || '📊'
		}
	}
}
</script>

<style scoped>
.product-analysis-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 页面头部 */
.header-section {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx 32rpx 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	color: #ffffff;
}

.header-title {
	flex: 1;
}

.title-text {
	font-size: 40rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
}

.subtitle-text {
	font-size: 28rpx;
	opacity: 0.8;
}

/* 筛选区域 */
.filter-section {
	background: #ffffff;
	padding: 24rpx 32rpx;
	margin-bottom: 20rpx;
}

.filter-row {
	display: flex;
	justify-content: space-between;
}

.filter-item {
	flex: 1;
	margin-right: 24rpx;
}

.filter-item:last-child {
	margin-right: 0;
}

.filter-label {
	display: block;
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 8rpx;
}

.picker-value {
	padding: 16rpx 24rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
	text-align: center;
}

/* 通用样式 */
.section-title {
	padding: 32rpx;
	background: #ffffff;
	border-bottom: 2rpx solid #f0f0f0;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.subtitle {
	font-size: 24rpx;
	color: #666666;
}

/* 热门商品 */
.hot-products-section {
	background: #ffffff;
	margin-bottom: 20rpx;
}

.products-list {
	padding: 32rpx;
}

.product-item {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
}

.product-item:last-child {
	border-bottom: none;
}

.product-rank {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.rank-gold {
	background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.rank-silver {
	background: linear-gradient(135deg, #C0C0C0 0%, #A9A9A9 100%);
}

.rank-bronze {
	background: linear-gradient(135deg, #CD7F32 0%, #B8860B 100%);
}

.rank-normal {
	background: #f0f0f0;
}

.rank-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
}

.rank-normal .rank-text {
	color: #666666;
}

.product-info {
	flex: 1;
}

.product-name {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.product-category {
	font-size: 24rpx;
	color: #666666;
}

.product-stats {
	text-align: right;
}

.stat-item {
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 20rpx;
	color: #999999;
	margin-right: 8rpx;
}

.stat-value {
	font-size: 24rpx;
	font-weight: 600;
	color: #007AFF;
}

/* 品类偏好 */
.category-section {
	background: #ffffff;
	margin-bottom: 20rpx;
}

.category-chart {
	padding: 32rpx;
	display: flex;
	align-items: center;
}

.chart-container {
	margin-right: 40rpx;
}

.pie-chart {
	width: 200rpx;
	height: 200rpx;
	border-radius: 50%;
	position: relative;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: center;
	background: #f0f0f0;
}

.pie-center {
	position: absolute;
	width: 120rpx;
	height: 120rpx;
	background: #ffffff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.center-text {
	font-size: 24rpx;
	color: #666666;
}

.legend-list {
	flex: 1;
}

.legend-item {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.legend-color {
	width: 24rpx;
	height: 24rpx;
	border-radius: 4rpx;
	margin-right: 16rpx;
}

.legend-label {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
}

.legend-value {
	font-size: 28rpx;
	font-weight: 600;
	color: #007AFF;
}

/* 价格敏感度 */
.price-sensitivity-section {
	background: #ffffff;
	margin-bottom: 20rpx;
}

.price-chart {
	padding: 32rpx;
}

.price-range {
	margin-bottom: 32rpx;
}

.range-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 12rpx;
}

.range-label {
	font-size: 28rpx;
	color: #333333;
}

.range-count {
	font-size: 28rpx;
	font-weight: 600;
	color: #007AFF;
}

.range-bar {
	height: 16rpx;
	background: #f0f0f0;
	border-radius: 8rpx;
	overflow: hidden;
	margin-bottom: 12rpx;
}

.bar-fill {
	height: 100%;
	background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
	border-radius: 8rpx;
	transition: width 0.5s ease;
}

.range-details {
	display: flex;
	justify-content: space-between;
}

.detail-text {
	font-size: 24rpx;
	color: #666666;
}

/* 关联分析 */
.association-section {
	background: #ffffff;
	margin-bottom: 20rpx;
}

.association-list {
	padding: 32rpx;
}

.association-item {
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
}

.association-products {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.product-group {
	flex: 1;
}

.group-label {
	display: block;
	font-size: 20rpx;
	color: #999999;
	margin-bottom: 4rpx;
}

.product-names {
	font-size: 28rpx;
	color: #333333;
	font-weight: 600;
}

.arrow {
	margin: 0 24rpx;
	font-size: 32rpx;
	color: #007AFF;
}

.association-stats {
	display: flex;
	justify-content: space-around;
}

.stat-badge {
	text-align: center;
}

.badge-label {
	display: block;
	font-size: 20rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.badge-value {
	font-size: 28rpx;
	font-weight: 600;
	color: #007AFF;
}

/* 生命周期 */
.lifecycle-section {
	background: #ffffff;
	margin-bottom: 20rpx;
}

.lifecycle-tabs {
	display: flex;
	padding: 24rpx 32rpx 0;
}

.lifecycle-tab {
	flex: 1;
	padding: 16rpx 0;
	text-align: center;
	border-bottom: 4rpx solid transparent;
	transition: all 0.3s ease;
}

.lifecycle-tab.active {
	border-bottom-color: #007AFF;
}

.lifecycle-tab.active .tab-text {
	color: #007AFF;
	font-weight: 600;
}

.tab-text {
	font-size: 28rpx;
	color: #666666;
}

.lifecycle-content {
	padding: 32rpx;
}

.stage-products {
	
}

.stage-product {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
}

.stage-product:last-child {
	border-bottom: none;
}

.product-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.product-icon.introduction {
	background: linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 100%);
}

.product-icon.growth {
	background: linear-gradient(135deg, #ffd93d 0%, #ff6b6b 100%);
}

.product-icon.maturity {
	background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.product-icon.decline {
	background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
}

.icon-text {
	font-size: 36rpx;
}

.product-details {
	flex: 1;
}

.product-name {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.product-trend {
	font-size: 24rpx;
	color: #666666;
}

.product-metrics {
	text-align: right;
}

.metric-value {
	font-size: 32rpx;
	font-weight: 600;
	color: #007AFF;
}

/* 加载状态 */
.loading-section {
	padding: 32rpx;
	text-align: center;
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}

/* 空状态 */
.empty-state {
	padding: 32rpx;
	text-align: center;
}

.empty-text {
	font-size: 28rpx;
	color: #666666;
}
</style> 