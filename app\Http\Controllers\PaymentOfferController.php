<?php

namespace App\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Payment\Models\PaymentOffer;
use App\Payment\Services\PaymentOfferService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class PaymentOfferController extends Controller
{
    protected $paymentOfferService;

    public function __construct(PaymentOfferService $paymentOfferService)
    {
        $this->paymentOfferService = $paymentOfferService;
    }

    /**
     * 获取支付优惠列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = PaymentOffer::query();

            // 按支付方式筛选
            if ($request->filled('payment_method')) {
                $query->byPaymentMethod($request->payment_method);
            }

            // 按优惠类型筛选
            if ($request->filled('offer_type')) {
                $query->byOfferType($request->offer_type);
            }

            // 按状态筛选
            if ($request->has('status') && $request->status !== '') {
                $query->byStatus((bool) $request->status);
            }

            // 关键词搜索
            if ($request->filled('keyword')) {
                $query->search($request->keyword);
            }

            // 排序
            $query->orderBy('sort_order')->orderBy('created_at', 'desc');

            // 分页
            $perPage = $request->get('per_page', 15);
            $offers = $query->paginate($perPage);

            // 添加计算属性
            foreach ($offers as $offer) {
                $offer->payment_method_name = $offer->payment_method_name;
                $offer->offer_type_name = $offer->offer_type_name;
                $offer->is_valid = $offer->isValid();
            }

            return response()->json(ApiResponse::success($offers, '获取支付优惠列表成功'));
        } catch (\Exception $e) {
            Log::error('获取支付优惠列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取支付优惠列表失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 创建支付优惠
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'payment_method' => ['required', 'string', Rule::in(array_keys(PaymentOffer::PAYMENT_METHODS))],
                'offer_type' => ['required', 'string', Rule::in(array_keys(PaymentOffer::OFFER_TYPES))],
                'offer_value' => 'required|numeric|min:0',
                'min_amount' => 'required|numeric|min:0',
                'max_offer' => 'nullable|numeric|min:0',
                'status' => 'boolean',
                'start_time' => 'nullable|date',
                'end_time' => 'nullable|date|after:start_time',
                'description' => 'required|string|max:255',
                'sort_order' => 'integer|min:0'
            ]);

            // 检查时间段冲突
            $conflictQuery = PaymentOffer::where('payment_method', $validated['payment_method']);
            
            if (isset($validated['start_time']) && isset($validated['end_time'])) {
                $conflictQuery->where(function($query) use ($validated) {
                    $query->where(function($q) use ($validated) {
                        $q->where('start_time', '<=', $validated['end_time'])
                          ->where('end_time', '>=', $validated['start_time']);
                    })->orWhere(function($q) {
                        $q->whereNull('start_time')->whereNull('end_time');
                    });
                });
            }

            if ($conflictQuery->exists()) {
                return response()->json(ApiResponse::error('该支付方式在指定时间段内已存在优惠规则', 422, ['time_conflict' => ['时间段冲突']]), 422);
            }

            $offer = PaymentOffer::create($validated);

            return response()->json(ApiResponse::success($offer, '创建支付优惠成功'), 201);
        } catch (\Exception $e) {
            Log::error('创建支付优惠失败', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            
            return response()->json(ApiResponse::error('创建支付优惠失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取单个支付优惠
     */
    public function show(PaymentOffer $paymentOffer): JsonResponse
    {
        try {
            $paymentOffer->payment_method_name = $paymentOffer->payment_method_name;
            $paymentOffer->offer_type_name = $paymentOffer->offer_type_name;
            $paymentOffer->is_valid = $paymentOffer->isValid();

            return response()->json(ApiResponse::success($paymentOffer, '获取支付优惠详情成功'));
        } catch (\Exception $e) {
            Log::error('获取支付优惠详情失败', [
                'error' => $e->getMessage(),
                'offer_id' => $paymentOffer->id
            ]);
            
            return response()->json(ApiResponse::error('获取支付优惠详情失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 更新支付优惠
     */
    public function update(Request $request, PaymentOffer $paymentOffer): JsonResponse
    {
        try {
            $validated = $request->validate([
                'payment_method' => ['required', 'string', Rule::in(array_keys(PaymentOffer::PAYMENT_METHODS))],
                'offer_type' => ['required', 'string', Rule::in(array_keys(PaymentOffer::OFFER_TYPES))],
                'offer_value' => 'required|numeric|min:0',
                'min_amount' => 'required|numeric|min:0',
                'max_offer' => 'nullable|numeric|min:0',
                'status' => 'boolean',
                'start_time' => 'nullable|date',
                'end_time' => 'nullable|date|after:start_time',
                'description' => 'required|string|max:255',
                'sort_order' => 'integer|min:0'
            ]);

            // 检查时间段冲突（排除当前记录）
            $conflictQuery = PaymentOffer::where('payment_method', $validated['payment_method'])
                ->where('id', '!=', $paymentOffer->id);
            
            if (isset($validated['start_time']) && isset($validated['end_time'])) {
                $conflictQuery->where(function($query) use ($validated) {
                    $query->where(function($q) use ($validated) {
                        $q->where('start_time', '<=', $validated['end_time'])
                          ->where('end_time', '>=', $validated['start_time']);
                    })->orWhere(function($q) {
                        $q->whereNull('start_time')->whereNull('end_time');
                    });
                });
            }

            if ($conflictQuery->exists()) {
                return response()->json(ApiResponse::error('该支付方式在指定时间段内已存在优惠规则', 422, ['time_conflict' => ['时间段冲突']]), 422);
            }

            $paymentOffer->update($validated);

            return response()->json(ApiResponse::success($paymentOffer, '更新支付优惠成功'));
        } catch (\Exception $e) {
            Log::error('更新支付优惠失败', [
                'error' => $e->getMessage(),
                'offer_id' => $paymentOffer->id,
                'data' => $request->all()
            ]);
            
            return response()->json(ApiResponse::error('更新支付优惠失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 删除支付优惠
     */
    public function destroy(PaymentOffer $paymentOffer): JsonResponse
    {
        try {
            $paymentOffer->delete();

            return response()->json(ApiResponse::success(null, '删除支付优惠成功'));
        } catch (\Exception $e) {
            Log::error('删除支付优惠失败', [
                'error' => $e->getMessage(),
                'offer_id' => $paymentOffer->id
            ]);
            
            return response()->json(ApiResponse::error('删除支付优惠失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 批量更新状态
     */
    public function batchUpdateStatus(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'ids' => 'required|array',
                'ids.*' => 'integer|exists:payment_offers,id',
                'status' => 'required|boolean'
            ]);

            $updated = PaymentOffer::whereIn('id', $validated['ids'])
                ->update(['status' => $validated['status']]);

            return response()->json(ApiResponse::success(['updated_count' => $updated], '批量更新状态成功'));
        } catch (\Exception $e) {
            Log::error('批量更新支付优惠状态失败', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            
            return response()->json(ApiResponse::error('批量更新状态失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取支付统计数据
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total_payment_methods' => count(PaymentOffer::PAYMENT_METHODS),
                'active_offers' => PaymentOffer::where('status', true)->count(),
                'total_offers' => PaymentOffer::count(),
                'valid_offers' => PaymentOffer::valid()->count(),
            ];

            // 按支付方式统计
            $paymentMethodStats = [];
            foreach (PaymentOffer::PAYMENT_METHODS as $method => $name) {
                $paymentMethodStats[] = [
                    'method' => $method,
                    'name' => $name,
                    'total_offers' => PaymentOffer::byPaymentMethod($method)->count(),
                    'active_offers' => PaymentOffer::byPaymentMethod($method)->byStatus(true)->count(),
                    'valid_offers' => PaymentOffer::byPaymentMethod($method)->valid()->count(),
                ];
            }

            // 按优惠类型统计
            $offerTypeStats = [];
            foreach (PaymentOffer::OFFER_TYPES as $type => $name) {
                $offerTypeStats[] = [
                    'type' => $type,
                    'name' => $name,
                    'count' => PaymentOffer::byOfferType($type)->count(),
                    'active_count' => PaymentOffer::byOfferType($type)->byStatus(true)->count(),
                ];
            }

            return response()->json(ApiResponse::success([
                'overview' => $stats,
                'payment_methods' => $paymentMethodStats,
                'offer_types' => $offerTypeStats
            ], '获取支付统计数据成功'));
        } catch (\Exception $e) {
            Log::error('获取支付统计数据失败', [
                'error' => $e->getMessage()
            ]);
            
            return response()->json(ApiResponse::error('获取支付统计数据失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 计算支付优惠
     */
    public function calculateOffer(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'payment_method' => ['required', 'string', Rule::in(array_keys(PaymentOffer::PAYMENT_METHODS))],
                'amount' => 'required|numeric|min:0',
                'enable_stacking' => 'boolean'
            ]);

            $offerInfo = $this->paymentOfferService->calculatePaymentOffer(
                $validated['payment_method'],
                $validated['amount'],
                $validated['enable_stacking'] ?? true
            );

            return response()->json(ApiResponse::success($offerInfo, '计算支付优惠成功'));
        } catch (\Exception $e) {
            Log::error('计算支付优惠失败', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            
            return response()->json(ApiResponse::error('计算支付优惠失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取支付方式配置
     */
    public function getPaymentMethods(): JsonResponse
    {
        try {
            return response()->json(ApiResponse::success(PaymentOffer::PAYMENT_METHODS, '获取支付方式配置成功'));
        } catch (\Exception $e) {
            Log::error('获取支付方式配置失败', [
                'error' => $e->getMessage()
            ]);
            
            return response()->json(ApiResponse::error('获取支付方式配置失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取优惠类型配置
     */
    public function getOfferTypes(): JsonResponse
    {
        try {
            return response()->json(ApiResponse::success(PaymentOffer::OFFER_TYPES, '获取优惠类型配置成功'));
        } catch (\Exception $e) {
            Log::error('获取优惠类型配置失败', [
                'error' => $e->getMessage()
            ]);
            
            return response()->json(ApiResponse::error('获取优惠类型配置失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取公共支付优惠列表（不需要认证，只返回启用的优惠）
     */
    public function publicOfferList(Request $request): JsonResponse
    {
        try {
            $query = PaymentOffer::where('status', true)->valid();

            // 按支付方式筛选
            if ($request->filled('payment_method')) {
                $query->byPaymentMethod($request->payment_method);
            }

            // 按优惠类型筛选
            if ($request->filled('offer_type')) {
                $query->byOfferType($request->offer_type);
            }

            // 关键词搜索
            if ($request->filled('keyword')) {
                $query->search($request->keyword);
            }

            // 排序
            $query->orderBy('sort_order')->orderBy('created_at', 'desc');

            // 分页
            $perPage = $request->get('per_page', 15);
            $offers = $query->paginate($perPage);

            // 添加计算属性
            foreach ($offers as $offer) {
                $offer->payment_method_name = $offer->payment_method_name;
                $offer->offer_type_name = $offer->offer_type_name;
                $offer->is_valid = $offer->isValid();
            }

            return response()->json(ApiResponse::success($offers, '获取公共支付优惠列表成功'));
        } catch (\Exception $e) {
            Log::error('获取公共支付优惠列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取公共支付优惠列表失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取前端统计数据（公共接口，返回前端期望的格式）
     */
    public function getStatistics(): JsonResponse
    {
        try {
            // 基础统计
            $totalPaymentMethods = count(PaymentOffer::PAYMENT_METHODS);
            $activeOffers = PaymentOffer::where('status', true)->count();
            $totalOffers = PaymentOffer::count();
            
            // 计算总优惠金额（这里使用模拟数据，实际应该从订单表统计）
            $totalOfferAmount = PaymentOffer::where('status', true)
                ->where('offer_type', 'fixed_amount')
                ->sum('offer_value') * 100; // 模拟使用次数
            
            // 计算平均优惠率
            $avgOfferRate = PaymentOffer::where('status', true)
                ->where('offer_type', 'percentage')
                ->avg('offer_value') ?: 0;

            // 按支付方式统计（模拟数据，实际应该从订单表统计）
            $paymentStats = [];
            foreach (PaymentOffer::PAYMENT_METHODS as $method => $name) {
                $methodOffers = PaymentOffer::byPaymentMethod($method)->where('status', true)->count();
                $isActive = $methodOffers > 0;
                
                $paymentStats[] = [
                    'method' => $method,
                    'name' => $name,
                    'transactions' => $isActive ? rand(500, 3000) : 0, // 模拟数据
                    'amount' => $isActive ? rand(20000, 100000) : 0, // 模拟数据
                    'offer_amount' => $isActive ? rand(1000, 15000) : 0, // 模拟数据
                    'percentage' => $isActive ? rand(10, 40) : 0, // 模拟数据
                    'status' => $isActive ? 'active' : 'inactive'
                ];
            }

            $data = [
                'total_payment_methods' => $totalPaymentMethods,
                'active_offers' => $activeOffers,
                'total_transactions' => array_sum(array_column($paymentStats, 'transactions')),
                'total_offer_amount' => $totalOfferAmount,
                'avg_offer_rate' => round($avgOfferRate, 1),
                'today_transactions' => rand(50, 200), // 模拟今日交易数
                'payment_stats' => $paymentStats
            ];

            return response()->json([
                'code' => 200,
                'message' => '获取统计数据成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取统计数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '获取统计数据失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
} 