<?php

namespace App\Order\Services;

use App\Order\Models\Order;
use App\Order\Models\OrderCorrection;
use App\Order\Models\PaymentLink;
use App\Order\Models\PaymentRecord;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Exception;

class PaymentLinkService
{
    /**
     * 生成货到付款付款链接
     */
    public function generateCodPaymentLink(Order $order): PaymentLink
    {
        // 检查订单是否为货到付款
        if (!$order->is_cod) {
            throw new Exception('该订单不是货到付款订单');
        }

        // 新的业务逻辑：订单在任何环节都可以生成付款链接
        // 只排除已取消和已退款的订单
        if (in_array($order->status, ['cancelled', 'refunded'])) {
            throw new Exception('已取消或已退款的订单无法生成付款链接');
        }

        // 检查是否已有有效的付款链接
        $existingLink = PaymentLink::where('order_id', $order->id)
            ->where('payment_type', 'cod')
            ->where('status', 'active')
            ->first();

        if ($existingLink && !$existingLink->isExpired()) {
            return $existingLink;
        }

        // 计算最终收款金额
        $finalAmount = $order->final_payment_amount ?? $order->total;

        return DB::transaction(function () use ($order, $finalAmount) {
            $paymentLink = PaymentLink::create([
                'id' => PaymentLink::generateLinkId(),
                'link_no' => PaymentLink::generateLinkNo(),
                'order_id' => $order->id,
                'amount' => $finalAmount,
                'paid_amount' => 0,
                'payment_type' => 'cod',
                'status' => 'active',
                'expires_at' => now()->addDays(30), // 30天有效期
                'created_by' => auth()->id(),
            ]);

            // 更新订单状态
            $order->update([
                'cod_status' => 'link_generated',
                'payment_link_id' => $paymentLink->id,
                'payment_link_expires_at' => $paymentLink->expires_at,
            ]);

            return $paymentLink;
        });
    }

    /**
     * 生成补款链接
     */
    public function generateSupplementLink(OrderCorrection $correction): PaymentLink
    {
        if (!$correction->needsSupplement()) {
            throw new Exception('订单更正不需要补款');
        }

        return DB::transaction(function () use ($correction) {
            $paymentLink = PaymentLink::create([
                'id' => PaymentLink::generateLinkId(),
                'link_no' => PaymentLink::generateLinkNo(),
                'order_id' => $correction->order_id,
                'correction_id' => $correction->id,
                'amount' => $correction->difference_amount,
                'paid_amount' => 0,
                'payment_type' => 'supplement',
                'status' => 'active',
                'expires_at' => now()->addDays(7), // 7天有效期
                'created_by' => auth()->id(),
            ]);

            return $paymentLink;
        });
    }

    /**
     * 处理付款回调
     */
    public function processPaymentCallback(string $linkId, array $paymentData): void
    {
        $paymentLink = PaymentLink::findOrFail($linkId);

        if (!$paymentLink->isActive()) {
            throw new Exception('付款链接已失效');
        }

        // 检查订单是否已经结算完成
        if ($paymentLink->correction_id) {
            $order = $paymentLink->order;
            if ($order->settlement_status === 'completed') {
                throw new Exception('订单已结算完成，无法重复付款');
            }
        }

        DB::transaction(function () use ($paymentLink, $paymentData) {
            // 更新付款链接状态
            $paymentLink->update([
                'status' => 'paid',
                'paid_at' => now(),
                'paid_amount' => $paymentData['amount'] ?? $paymentLink->amount,
                'payment_method' => $paymentData['payment_method'] ?? 'wechat',
                'transaction_id' => $paymentData['transaction_id'] ?? null,
            ]);

            // 创建付款记录
            $paymentRecord = PaymentRecord::create([
                'order_id' => $paymentLink->order_id,
                'correction_id' => $paymentLink->correction_id,
                'payment_type' => $paymentLink->payment_type,
                'business_type' => $this->getBusinessType($paymentLink->payment_type),
                'amount' => $paymentData['amount'] ?? $paymentLink->amount,
                'payment_method' => $paymentData['payment_method'] ?? 'wechat',
                'transaction_id' => $paymentData['transaction_id'] ?? null,
                'status' => 'success',
                'paid_at' => now(),
                'notes' => '通过付款链接支付',
            ]);

            // 更新订单状态
            $this->updateOrderAfterPayment($paymentLink);
        });
    }

    /**
     * 手动标记已付款
     */
    public function markAsPaid(string $linkId, string $paymentMethod = 'cash', int $operatedBy = null): void
    {
        $paymentLink = PaymentLink::findOrFail($linkId);

        if (!$paymentLink->isActive()) {
            throw new Exception('付款链接已失效');
        }

        // 检查订单是否已经结算完成
        if ($paymentLink->correction_id) {
            $order = $paymentLink->order;
            if ($order->settlement_status === 'completed') {
                throw new Exception('订单已结算完成，无法重复付款');
            }
        }

        DB::transaction(function () use ($paymentLink, $paymentMethod, $operatedBy) {
            // 更新付款链接状态
            $paymentLink->update([
                'status' => 'paid',
                'paid_at' => now(),
                'paid_amount' => $paymentLink->amount,
                'payment_method' => $paymentMethod,
            ]);

            // 创建付款记录
            PaymentRecord::create([
                'order_id' => $paymentLink->order_id,
                'correction_id' => $paymentLink->correction_id,
                'payment_type' => $paymentLink->payment_type,
                'business_type' => $this->getBusinessType($paymentLink->payment_type),
                'amount' => $paymentLink->amount,
                'payment_method' => $paymentMethod,
                'status' => 'success',
                'paid_at' => now(),
                'operated_by' => $operatedBy,
                'notes' => '手动标记已付款',
            ]);

            // 更新订单状态
            $this->updateOrderAfterPayment($paymentLink);
        });
    }

    /**
     * 配送员现金收款
     */
    public function markCashPaidByDeliverer(string $linkId, int $delivererId, array $cashDetails = []): void
    {
        $paymentLink = PaymentLink::findOrFail($linkId);

        if (!$paymentLink->isActive()) {
            throw new Exception('付款链接已失效');
        }

        if ($paymentLink->payment_type !== 'cod') {
            throw new Exception('只有货到付款链接支持配送员收款');
        }

        DB::transaction(function () use ($paymentLink, $delivererId, $cashDetails) {
            // 更新付款链接状态
            $paymentLink->update([
                'status' => 'paid',
                'paid_at' => now(),
                'paid_amount' => $cashDetails['actual_amount'] ?? $paymentLink->amount,
                'payment_method' => 'cash',
                'remark' => '配送员现场收款',
            ]);

            // 创建付款记录
            $extraData = [
                'cash_collection_type' => 'delivery',
                'deliverer_id' => $delivererId,
                'collection_location' => $cashDetails['location'] ?? '客户地址',
                'actual_amount' => $cashDetails['actual_amount'] ?? $paymentLink->amount,
                'change_amount' => $cashDetails['change_amount'] ?? 0,
                'customer_paid' => $cashDetails['customer_paid'] ?? $paymentLink->amount,
            ];

            PaymentRecord::create([
                'order_id' => $paymentLink->order_id,
                'correction_id' => $paymentLink->correction_id,
                'payment_type' => $paymentLink->payment_type,
                'business_type' => $this->getBusinessType($paymentLink->payment_type),
                'amount' => $paymentLink->amount,
                'payment_method' => 'cash',
                'status' => 'success',
                'paid_at' => now(),
                'operated_by' => $delivererId,
                'notes' => '配送员现场收取现金',
                'extra_data' => $extraData,
            ]);

            // 更新订单状态
            $this->updateOrderAfterPayment($paymentLink);
        });
    }

    /**
     * 门店现金收款
     */
    public function markCashPaidAtStore(string $linkId, int $cashierId, array $cashDetails = []): void
    {
        $paymentLink = PaymentLink::findOrFail($linkId);

        if (!$paymentLink->isActive()) {
            throw new Exception('付款链接已失效');
        }

        DB::transaction(function () use ($paymentLink, $cashierId, $cashDetails) {
            // 更新付款链接状态
            $paymentLink->update([
                'status' => 'paid',
                'paid_at' => now(),
                'paid_amount' => $cashDetails['actual_amount'] ?? $paymentLink->amount,
                'payment_method' => 'cash',
                'remark' => '门店现金收款',
            ]);

            // 创建付款记录
            $extraData = [
                'cash_collection_type' => 'store',
                'cashier_id' => $cashierId,
                'collection_location' => $cashDetails['store_name'] ?? '门店',
                'actual_amount' => $cashDetails['actual_amount'] ?? $paymentLink->amount,
                'change_amount' => $cashDetails['change_amount'] ?? 0,
                'customer_paid' => $cashDetails['customer_paid'] ?? $paymentLink->amount,
                'receipt_number' => $cashDetails['receipt_number'] ?? null,
            ];

            PaymentRecord::create([
                'order_id' => $paymentLink->order_id,
                'correction_id' => $paymentLink->correction_id,
                'payment_type' => $paymentLink->payment_type,
                'business_type' => $this->getBusinessType($paymentLink->payment_type),
                'amount' => $paymentLink->amount,
                'payment_method' => 'cash',
                'status' => 'success',
                'paid_at' => now(),
                'operated_by' => $cashierId,
                'notes' => '门店现金收款',
                'extra_data' => $extraData,
            ]);

            // 更新订单状态
            $this->updateOrderAfterPayment($paymentLink);
        });
    }

    /**
     * 取消付款链接
     */
    public function cancelPaymentLink(PaymentLink $paymentLink): void
    {
        $paymentLink->update(['status' => 'cancelled']);

        // 如果是货到付款链接，更新订单状态
        if ($paymentLink->payment_type === 'cod') {
            $paymentLink->order->update([
                'cod_status' => 'unpaid',
                'payment_link_id' => null,
                'payment_link_expires_at' => null,
            ]);
        }
    }

    /**
     * 生成二维码
     */
    public function generateQrCode(PaymentLink $paymentLink): string
    {
        // 这里可以集成二维码生成服务
        $qrCodeUrl = $this->generateQrCodeImage($paymentLink->full_url);
        
        $paymentLink->update(['qr_code_url' => $qrCodeUrl]);
        
        return $qrCodeUrl;
    }

    /**
     * 生成短链接
     */
    public function generateShortUrl(PaymentLink $paymentLink): string
    {
        // 这里可以集成短链接服务
        $shortUrl = $this->generateShortUrlService($paymentLink->full_url);
        
        $paymentLink->update(['short_url' => $shortUrl]);
        
        return $shortUrl;
    }

    /**
     * 获取业务类型
     */
    private function getBusinessType(string $paymentType): string
    {
        $typeMap = [
            'cod' => 'cod_final',
            'supplement' => 'correction_supplement',
        ];

        return $typeMap[$paymentType] ?? 'order_payment';
    }

    /**
     * 付款后更新订单状态
     */
    private function updateOrderAfterPayment(PaymentLink $paymentLink): void
    {
        $order = $paymentLink->order;

        if ($paymentLink->payment_type === 'cod') {
            // 货到付款最终收款
            $order->update([
                'cod_status' => 'paid',
                'cod_paid_at' => now(),
                'status' => 'completed',
            ]);
        } elseif ($paymentLink->payment_type === 'supplement') {
            // 补款完成，检查是否可以完成订单
            $this->checkOrderCompletion($order);
        }
    }

    /**
     * 检查订单是否可以完成
     */
    private function checkOrderCompletion(Order $order): void
    {
        // 检查所有补款是否都已完成
        $pendingSupplements = PaymentRecord::where('order_id', $order->id)
            ->where('payment_type', 'supplement')
            ->where('status', 'pending')
            ->count();

        if ($pendingSupplements === 0) {
            $order->update(['status' => 'completed']);
        }
    }

    /**
     * 生成二维码图片（示例实现）
     */
    private function generateQrCodeImage(string $url): string
    {
        // 这里应该调用实际的二维码生成服务
        return 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' . urlencode($url);
    }

    /**
     * 生成短链接服务（示例实现）
     */
    private function generateShortUrlService(string $url): string
    {
        // 这里应该调用实际的短链接服务API
        // 暂时返回原URL
        return $url;
    }

    /**
     * 发送付款提醒
     */
    public function sendPaymentReminder(PaymentLink $paymentLink, string $reminderType, string $message = null): void
    {
        if (!$paymentLink->isActive()) {
            throw new Exception('付款链接已失效，无法发送提醒');
        }

        // 更新提醒次数
        $paymentLink->increment('reminder_count');
        $paymentLink->update(['last_reminder_at' => now()]);

        // 根据提醒类型发送提醒
        switch ($reminderType) {
            case 'sms':
                $this->sendSmsReminder($paymentLink, $message);
                break;
            case 'wechat':
                $this->sendWechatReminder($paymentLink, $message);
                break;
            default:
                throw new Exception('不支持的提醒类型：' . $reminderType);
        }
    }

    /**
     * 发送短信提醒
     */
    private function sendSmsReminder(PaymentLink $paymentLink, string $message = null): void
    {
        $order = $paymentLink->order;
        $user = $order->user;

        if (!$user->phone) {
            throw new Exception('用户没有手机号码，无法发送短信提醒');
        }

        $defaultMessage = "您的订单 {$order->order_no} 需要付款，金额：￥{$paymentLink->amount}，请点击链接完成付款：{$paymentLink->full_url}";
        $smsContent = $message ?: $defaultMessage;

        // 调用阿里云短信服务
        try {
            $smsService = app(\App\shop\Services\Sms\AliyunSmsService::class);
            $result = $smsService->sendCustomMessage($user->phone, $smsContent);
            
            if (!$result['success']) {
                throw new Exception('短信发送失败：' . $result['message']);
            }
            
            Log::info('发送付款提醒短信成功', [
                'phone' => $user->phone,
                'message' => $smsContent,
                'payment_link_id' => $paymentLink->id
            ]);
        } catch (\Exception $e) {
            Log::error('发送付款提醒短信失败', [
                'phone' => $user->phone,
                'error' => $e->getMessage(),
                'payment_link_id' => $paymentLink->id
            ]);
            throw $e;
        }
    }

    /**
     * 发送微信提醒
     */
    private function sendWechatReminder(PaymentLink $paymentLink, string $message = null): void
    {
        $order = $paymentLink->order;
        $user = $order->user;

        if (!$user->wechat_openid) {
            throw new Exception('用户没有微信openid，无法发送微信提醒');
        }

        $defaultMessage = "您的订单需要付款，请及时完成付款。";
        $wechatMessage = $message ?: $defaultMessage;

        // 调用微信模板消息或客服消息API
        try {
            $wechatService = app(\App\WechatMp\Services\WechatMpService::class);
            $miniApp = $wechatService->getMiniApp();
            
            // 尝试发送模板消息
            $templateData = [
                'touser' => $user->wechat_openid,
                'template_id' => config('wechat.payment_reminder_template_id'),
                'page' => 'pages/payment/index?link_no=' . $paymentLink->link_no,
                'data' => [
                    'thing1' => ['value' => $order->order_no], // 订单号
                    'amount2' => ['value' => '￥' . $paymentLink->amount], // 付款金额
                    'thing3' => ['value' => $wechatMessage], // 催款内容
                    'time4' => ['value' => now()->format('Y-m-d H:i:s')], // 提醒时间
                ]
            ];
            
            $result = $miniApp->getNotice()->send($templateData);
            
            if (isset($result['errcode']) && $result['errcode'] != 0) {
                // 模板消息发送失败，尝试客服消息
                $this->sendWechatCustomerMessage($miniApp, $user->wechat_openid, $wechatMessage);
            }
            
            Log::info('发送付款提醒微信消息成功', [
                'openid' => $user->wechat_openid,
                'message' => $wechatMessage,
                'payment_link_id' => $paymentLink->id
            ]);
        } catch (\Exception $e) {
            Log::error('发送付款提醒微信消息失败', [
                'openid' => $user->wechat_openid,
                'error' => $e->getMessage(),
                'payment_link_id' => $paymentLink->id
            ]);
            throw new Exception('微信提醒发送失败：' . $e->getMessage());
        }
    }

    /**
     * 发送微信客服消息（备用方案）
     */
    private function sendWechatCustomerMessage($miniApp, string $openid, string $message): void
    {
        $messageData = [
            'touser' => $openid,
            'msgtype' => 'text',
            'text' => [
                'content' => $message
            ]
        ];
        
        $result = $miniApp->getCustomerService()->send($messageData);
        
        if (isset($result['errcode']) && $result['errcode'] != 0) {
            throw new Exception('微信客服消息发送失败：' . $result['errmsg']);
        }
    }

    /**
     * 清理过期付款链接
     */
    public function cleanupExpiredLinks(): int
    {
        return PaymentLink::where('status', 'active')
            ->where('expires_at', '<', now())
            ->update(['status' => 'expired']);
    }
} 