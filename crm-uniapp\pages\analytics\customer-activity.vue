<template>
	<view class="customer-activity-container">
		<!-- 页面头部 -->
		<view class="header-section">
			<view class="header-title">
				<text class="title-text">客户活跃度分析</text>
				<text class="subtitle-text">深度分析客户活跃度表现与趋势</text>
			</view>
		</view>

		<!-- 时间筛选 -->
		<view class="filter-section">
			<view class="filter-tabs">
				<view 
					class="filter-tab" 
					v-for="(period, index) in timePeriods" 
					:key="index"
					:class="{ active: selectedPeriod === period.value }"
					@tap="selectPeriod(period.value)"
				>
					<text class="tab-text">{{ period.label }}</text>
				</view>
			</view>
		</view>

		<!-- 活跃度概览 -->
		<view class="overview-section">
			<view class="section-title">
				<text class="title-text">📊 活跃度概览</text>
			</view>
			<view class="stats-grid">
				<view class="stat-item">
					<text class="stat-number">{{ activityOverview.totalUsers || 0 }}</text>
					<text class="stat-label">总客户数</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ activityOverview.activeUsers || 0 }}</text>
					<text class="stat-label">活跃客户</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ activityOverview.activityRate || 0 }}%</text>
					<text class="stat-label">活跃率</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ activityOverview.avgOrderFreq || 0 }}</text>
					<text class="stat-label">平均订单频次</text>
				</view>
			</view>
		</view>

		<!-- 活跃度分层 -->
		<view class="activity-segments-section">
			<view class="section-title">
				<text class="title-text">🎯 客户活跃度分层</text>
			</view>
			<view class="segments-chart">
				<view class="segment-item" v-for="segment in activitySegments" :key="segment.level">
					<view class="segment-header">
						<view class="segment-info">
							<text class="segment-label">{{ segment.label }}</text>
							<text class="segment-desc">{{ segment.description }}</text>
						</view>
						<view class="segment-stats">
							<text class="segment-count">{{ segment.count }}人</text>
							<text class="segment-percentage">{{ segment.percentage }}%</text>
						</view>
					</view>
					<view class="segment-bar">
						<view class="bar-fill" :class="segment.level" :style="{ width: segment.percentage + '%' }"></view>
					</view>
					<view class="segment-details">
						<text class="detail-item">平均订单: {{ segment.avgOrders }}单</text>
						<text class="detail-item">平均消费: ¥{{ segment.avgSpend }}</text>
						<text class="detail-item">最近活跃: {{ segment.lastActiveDay }}天前</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 活跃度趋势 -->
		<view class="activity-trend-section">
			<view class="section-title">
				<text class="title-text">📈 活跃度趋势</text>
			</view>
			<view class="trend-chart">
				<view class="chart-container">
					<view class="trend-line" v-for="(point, index) in activityTrend" :key="index">
						<view class="trend-point" :style="{ height: point.percentage + '%' }">
							<text class="point-value">{{ point.activeUsers }}</text>
						</view>
						<text class="trend-date">{{ formatTrendDate(point.date) }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 活跃度行为分析 -->
		<view class="behavior-analysis-section">
			<view class="section-title">
				<text class="title-text">🔍 活跃行为分析</text>
			</view>
			<view class="behavior-metrics">
				<view class="metric-card">
					<view class="metric-header">
						<text class="metric-title">页面浏览活跃度</text>
					</view>
					<view class="metric-content">
						<text class="metric-value">{{ behaviorMetrics.avgPageViews || 0 }}</text>
						<text class="metric-unit">次/人</text>
					</view>
					<view class="metric-trend">
						<text class="trend-text" :class="getTrendClass(behaviorMetrics.pageViewsTrend)">
							{{ getTrendText(behaviorMetrics.pageViewsTrend) }}
						</text>
					</view>
				</view>
				
				<view class="metric-card">
					<view class="metric-header">
						<text class="metric-title">会话活跃度</text>
					</view>
					<view class="metric-content">
						<text class="metric-value">{{ behaviorMetrics.avgSessionDuration || 0 }}</text>
						<text class="metric-unit">秒</text>
					</view>
					<view class="metric-trend">
						<text class="trend-text" :class="getTrendClass(behaviorMetrics.sessionTrend)">
							{{ getTrendText(behaviorMetrics.sessionTrend) }}
						</text>
					</view>
				</view>
				
				<view class="metric-card">
					<view class="metric-header">
						<text class="metric-title">商品浏览活跃度</text>
					</view>
					<view class="metric-content">
						<text class="metric-value">{{ behaviorMetrics.avgProductViews || 0 }}</text>
						<text class="metric-unit">次/人</text>
					</view>
					<view class="metric-trend">
						<text class="trend-text" :class="getTrendClass(behaviorMetrics.productViewsTrend)">
							{{ getTrendText(behaviorMetrics.productViewsTrend) }}
						</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 活跃客户列表 -->
		<view class="active-customers-section">
			<view class="section-title">
				<text class="title-text">⭐ 高活跃客户</text>
				<text class="more-link" @tap="viewAllActiveCustomers">查看全部</text>
			</view>
			<view class="customers-list">
				<view class="customer-card" v-for="customer in topActiveCustomers" :key="customer.id" @tap="viewCustomerDetail(customer.id)">
					<view class="customer-header">
						<view class="customer-avatar">
							<text class="avatar-text">{{ getAvatarText(customer.name) }}</text>
						</view>
						<view class="customer-info">
							<text class="customer-name">{{ customer.name }}</text>
							<text class="customer-phone">{{ customer.phone }}</text>
						</view>
						<view class="activity-badge" :class="customer.activityLevel">
							<text class="badge-text">{{ getActivityLevelText(customer.activityLevel) }}</text>
						</view>
					</view>
					<view class="customer-stats">
						<view class="stat-item">
							<text class="stat-label">订单数</text>
							<text class="stat-value">{{ customer.totalOrders }}单</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">消费额</text>
							<text class="stat-value">¥{{ customer.totalSpend }}</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">活跃天数</text>
							<text class="stat-value">{{ customer.activeDays }}天</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">最近活跃</text>
							<text class="stat-value">{{ customer.lastActiveDay }}天前</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 活跃度提升建议 -->
		<view class="suggestions-section">
			<view class="section-title">
				<text class="title-text">💡 活跃度提升建议</text>
			</view>
			<view class="suggestions-list">
				<view class="suggestion-card" v-for="suggestion in activitySuggestions" :key="suggestion.id">
					<view class="suggestion-header">
						<text class="suggestion-icon">{{ suggestion.icon }}</text>
						<text class="suggestion-title">{{ suggestion.title }}</text>
					</view>
					<text class="suggestion-desc">{{ suggestion.description }}</text>
					<view class="suggestion-actions">
						<button class="action-btn" @tap="applySuggestion(suggestion)">
							<text class="btn-text">{{ suggestion.actionText }}</text>
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
import analyticsApi from '../../api/analytics.js'
import { formatDateTime, formatDate as formatDateString } from '../../utils/date-formatter.js'

export default {
	data() {
		return {
			loading: false,
			selectedPeriod: 'last_30_days',
			timePeriods: [
				{ label: '近7天', value: 'last_7_days' },
				{ label: '近30天', value: 'last_30_days' },
				{ label: '近90天', value: 'last_90_days' }
			],
			activityOverview: {},
			activitySegments: [],
			activityTrend: [],
			behaviorMetrics: {},
			topActiveCustomers: [],
			activitySuggestions: []
		}
	},
	
	onLoad() {
		this.loadActivityAnalysis()
	},
	
	onShow() {
		this.loadActivityAnalysis()
	},
	
	onPullDownRefresh() {
		this.refreshData()
	},
	
	methods: {
		// 选择时间周期
		selectPeriod(period) {
			this.selectedPeriod = period
			this.loadActivityAnalysis()
		},
		
		// 加载活跃度分析数据
		async loadActivityAnalysis() {
			this.loading = true
			try {
				// 将period转换为日期范围
				const dateRange = this.getPeriodDateRange(this.selectedPeriod)
				const params = {
					start_date: dateRange.start_date,
					end_date: dateRange.end_date
				}
				
				const response = await analyticsApi.getCustomerActivityAnalysis(params)
				
				if (response && response.code === 0 && response.data) {
					const data = response.data
					
					// 处理活跃度概览
					this.activityOverview = data.activityOverview || {
						totalUsers: 0,
						activeUsers: 0,
						activityRate: 0,
						avgOrderFreq: 0
					}
					
					// 处理活跃度分层
					this.activitySegments = data.activitySegments || []
					
					// 处理活跃度趋势
					this.activityTrend = data.activityTrend || []
					
					// 处理行为指标
					this.behaviorMetrics = data.behaviorMetrics || {
						avgPageViews: 0,
						avgSessionDuration: 0,
						avgProductViews: 0,
						pageViewsTrend: 'stable',
						sessionTrend: 'stable',
						productViewsTrend: 'stable'
					}
					
					// 处理高活跃客户列表
					this.topActiveCustomers = data.topActiveCustomers || []
					
					// 处理活跃度提升建议
					this.activitySuggestions = data.activitySuggestions || []
				} else {
					throw new Error(response?.message || '数据格式错误')
				}
			} catch (error) {
				console.error('加载活跃度分析数据失败:', error)
				uni.showToast({
					title: '加载数据失败',
					icon: 'none'
				})
				
				// 设置默认空数据
				this.activityOverview = {
					totalUsers: 0,
					activeUsers: 0,
					activityRate: 0,
					avgOrderFreq: 0
				}
				this.activitySegments = []
				this.activityTrend = []
				this.behaviorMetrics = {
					avgPageViews: 0,
					avgSessionDuration: 0,
					avgProductViews: 0,
					pageViewsTrend: 'stable',
					sessionTrend: 'stable',
					productViewsTrend: 'stable'
				}
				this.topActiveCustomers = []
				this.activitySuggestions = []
			} finally {
				this.loading = false
			}
		},
		
		// 将period转换为日期范围
		getPeriodDateRange(period) {
			const today = new Date()
			let startDate, endDate
			
			switch (period) {
				case 'last_7_days':
					startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
					break
				case 'last_30_days':
					startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
					break
				case 'last_90_days':
					startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000)
					break
				default:
					startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
			}
			
			endDate = today
			
			return {
				start_date: this.formatDate(startDate),
				end_date: this.formatDate(endDate)
			}
		},
		
		// 格式化日期为YYYY-MM-DD格式
		formatDate(date) {
			return formatDateString(date)
		},
		
		// 刷新数据
		async refreshData() {
			await this.loadActivityAnalysis()
			uni.stopPullDownRefresh()
		},
		
		// 格式化趋势日期
		formatTrendDate(dateStr) {
			const date = new Date(dateStr)
			return `${date.getMonth() + 1}/${date.getDate()}`
		},
		
		// 获取头像文本
		getAvatarText(name) {
			return name ? name.charAt(0).toUpperCase() : 'U'
		},
		
		// 获取活跃度等级文本
		getActivityLevelText(level) {
			const levelMap = {
				'high': '高活跃',
				'medium': '中活跃',
				'low': '低活跃',
				'inactive': '不活跃'
			}
			return levelMap[level] || level
		},
		
		// 获取趋势样式类
		getTrendClass(trend) {
			return trend === 'up' ? 'positive' : 'negative'
		},
		
		// 获取趋势文本
		getTrendText(trend) {
			return trend === 'up' ? '↗ 上升' : '↘ 下降'
		},
		
		// 查看客户详情
		viewCustomerDetail(customerId) {
			uni.navigateTo({
				url: `/pages/clients/client-detail?id=${customerId}`
			})
		},
		
		// 查看所有活跃客户
		viewAllActiveCustomers() {
			uni.navigateTo({
				url: '/pages/clients/client-list?filter=active'
			})
		},
		
		// 应用建议
		applySuggestion(suggestion) {
			uni.showToast({
				title: `正在执行：${suggestion.title}`,
				icon: 'none'
			})
		}
	}
}
</script>

<style scoped>
.customer-activity-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 页面头部 */
.header-section {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx 32rpx 32rpx;
	color: #ffffff;
}

.header-title {
	text-align: center;
}

.title-text {
	font-size: 40rpx;
	font-weight: 700;
	color: #ffffff;
	margin-bottom: 8rpx;
	display: block;
}

.subtitle-text {
	font-size: 28rpx;
	color: #ffffff;
	opacity: 0.8;
	display: block;
}

/* 筛选区域 */
.filter-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.filter-tabs {
	display: flex;
	gap: 16rpx;
}

.filter-tab {
	flex: 1;
	padding: 16rpx 12rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	text-align: center;
	transition: all 0.3s ease;
}

.filter-tab.active {
	background: #667eea;
	color: #ffffff;
}

.tab-text {
	font-size: 28rpx;
	font-weight: 600;
}

/* 概览区域 */
.overview-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.section-title {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.more-link {
	font-size: 28rpx;
	color: #667eea;
}

.stats-grid {
	display: flex;
	gap: 24rpx;
}

.stat-item {
	flex: 1;
	text-align: center;
}

.stat-number {
	font-size: 48rpx;
	font-weight: 700;
	color: #333333;
	display: block;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666666;
}

/* 活跃度分层 */
.activity-segments-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.segments-chart {
	margin-top: 24rpx;
}

.segment-item {
	margin-bottom: 32rpx;
}

.segment-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 12rpx;
}

.segment-info {
	flex: 1;
}

.segment-label {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
	display: block;
}

.segment-desc {
	font-size: 24rpx;
	color: #666666;
}

.segment-stats {
	text-align: right;
}

.segment-count {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
	display: block;
}

.segment-percentage {
	font-size: 24rpx;
	color: #666666;
}

.segment-bar {
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
	margin-bottom: 12rpx;
}

.bar-fill {
	height: 100%;
	border-radius: 6rpx;
	transition: width 0.3s ease;
}

.bar-fill.high {
	background: #52c41a;
}

.bar-fill.medium {
	background: #1890ff;
}

.bar-fill.low {
	background: #faad14;
}

.bar-fill.inactive {
	background: #f5222d;
}

.segment-details {
	display: flex;
	gap: 24rpx;
}

.detail-item {
	font-size: 24rpx;
	color: #666666;
}

/* 活跃度趋势 */
.activity-trend-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.trend-chart {
	margin-top: 24rpx;
}

.chart-container {
	display: flex;
	align-items: end;
	gap: 8rpx;
	height: 200rpx;
	padding: 20rpx 0;
}

.trend-line {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.trend-point {
	background: linear-gradient(to top, #667eea, #764ba2);
	border-radius: 4rpx 4rpx 0 0;
	min-height: 20rpx;
	width: 100%;
	display: flex;
	align-items: end;
	justify-content: center;
	padding-bottom: 8rpx;
	margin-bottom: 8rpx;
}

.point-value {
	font-size: 20rpx;
	color: #ffffff;
	font-weight: 600;
}

.trend-date {
	font-size: 20rpx;
	color: #666666;
	transform: rotate(-45deg);
}

/* 行为分析 */
.behavior-analysis-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.behavior-metrics {
	display: flex;
	gap: 16rpx;
	margin-top: 24rpx;
}

.metric-card {
	flex: 1;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	text-align: center;
}

.metric-header {
	margin-bottom: 16rpx;
}

.metric-title {
	font-size: 24rpx;
	color: #666666;
}

.metric-content {
	margin-bottom: 12rpx;
}

.metric-value {
	font-size: 36rpx;
	font-weight: 700;
	color: #333333;
}

.metric-unit {
	font-size: 20rpx;
	color: #666666;
	margin-left: 4rpx;
}

.metric-trend {
	font-size: 20rpx;
}

.trend-text.positive {
	color: #52c41a;
}

.trend-text.negative {
	color: #f5222d;
}

/* 活跃客户列表 */
.active-customers-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.customers-list {
	margin-top: 24rpx;
}

.customer-card {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
}

.customer-header {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.customer-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	background: #667eea;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
}

.avatar-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

.customer-info {
	flex: 1;
}

.customer-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
	display: block;
}

.customer-phone {
	font-size: 24rpx;
	color: #666666;
}

.activity-badge {
	padding: 8rpx 16rpx;
	border-radius: 50rpx;
	font-size: 24rpx;
	font-weight: 600;
}

.activity-badge.high {
	background: #f6ffed;
	color: #52c41a;
}

.activity-badge.medium {
	background: #e6f7ff;
	color: #1890ff;
}

.activity-badge.low {
	background: #fff7e6;
	color: #faad14;
}

.customer-stats {
	display: flex;
	gap: 24rpx;
}

.customer-stats .stat-item {
	flex: 1;
	text-align: center;
}

.customer-stats .stat-label {
	font-size: 20rpx;
	color: #666666;
	margin-bottom: 4rpx;
	display: block;
}

.customer-stats .stat-value {
	font-size: 24rpx;
	font-weight: 600;
	color: #333333;
}

/* 提升建议 */
.suggestions-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.suggestions-list {
	margin-top: 24rpx;
}

.suggestion-card {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
}

.suggestion-header {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
}

.suggestion-icon {
	font-size: 32rpx;
	margin-right: 12rpx;
}

.suggestion-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

.suggestion-desc {
	font-size: 24rpx;
	color: #666666;
	line-height: 1.4;
	margin-bottom: 16rpx;
}

.suggestion-actions {
	text-align: right;
}

.action-btn {
	background: #667eea;
	color: #ffffff;
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 24rpx;
	font-size: 24rpx;
}

.btn-text {
	color: #ffffff;
}

/* 加载状态 */
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}
</style> 