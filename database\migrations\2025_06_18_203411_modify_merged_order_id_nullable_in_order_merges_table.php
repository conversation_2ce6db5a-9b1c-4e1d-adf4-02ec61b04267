<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_merges', function (Blueprint $table) {
            $table->unsignedBigInteger('merged_order_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_merges', function (Blueprint $table) {
            $table->unsignedBigInteger('merged_order_id')->nullable(false)->change();
        });
    }
};
