<?php

namespace App\Crm\Services;

use App\Crm\Models\Feedback;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FeedbackService
{
    /**
     * 获取反馈列表
     *
     * @param Request $request
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getFeedbacks(Request $request)
    {
        $query = Feedback::with(['user', 'responder']);
        
        // 筛选条件
        if ($request->has('user_id') && $request->user_id) {
            $query->where('user_id', $request->user_id);
        }
        
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }
        
        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }
        
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }
        
        // 排序
        if ($request->has('sort') && $request->sort) {
            $direction = $request->has('direction') && $request->direction === 'desc' ? 'desc' : 'asc';
            $query->orderBy($request->sort, $direction);
        } else {
            $query->latest();
        }
        
        return $query->paginate($request->input('per_page', 15));
    }
    
    /**
     * 获取反馈详情
     *
     * @param int $id
     * @return Feedback
     */
    public function getFeedback($id)
    {
        return Feedback::with(['user', 'responder'])->findOrFail($id);
    }
    
    /**
     * 创建反馈
     *
     * @param array $data
     * @return Feedback
     */
    public function createFeedback(array $data)
    {
        // 验证用户存在
        $user = User::findOrFail($data['user_id']);
        
        // 创建反馈
        $feedback = new Feedback();
        $feedback->user_id = $user->id;
        $feedback->subject = $data['subject'];
        $feedback->content = $data['content'];
        $feedback->type = $data['type'] ?? 'general';
        $feedback->status = 'pending';
        $feedback->save();
        
        return $feedback->load('user');
    }
    
    /**
     * 更新反馈
     *
     * @param int $id
     * @param array $data
     * @return Feedback
     */
    public function updateFeedback($id, array $data)
    {
        $feedback = Feedback::findOrFail($id);
        
        // 更新允许的字段
        if (isset($data['subject'])) {
            $feedback->subject = $data['subject'];
        }
        
        if (isset($data['content'])) {
            $feedback->content = $data['content'];
        }
        
        if (isset($data['type'])) {
            $feedback->type = $data['type'];
        }
        
        if (isset($data['status'])) {
            $feedback->status = $data['status'];
            
            // 如果状态变为已解决，记录解决时间
            if ($data['status'] === 'resolved' && !$feedback->resolved_at) {
                $feedback->resolved_at = now();
            }
        }
        
        if (isset($data['response'])) {
            $feedback->response = $data['response'];
        }
        
        if (isset($data['response_by'])) {
            $feedback->response_by = $data['response_by'];
        }
        
        $feedback->save();
        
        return $feedback->load(['user', 'responder']);
    }
    
    /**
     * 删除反馈
     *
     * @param int $id
     * @return bool
     */
    public function deleteFeedback($id)
    {
        $feedback = Feedback::findOrFail($id);
        return $feedback->delete();
    }
    
    /**
     * 获取反馈统计
     *
     * @return array
     */
    public function getStatistics()
    {
        $total = Feedback::count();
        $pending = Feedback::pending()->count();
        $processing = Feedback::processing()->count();
        $resolved = Feedback::resolved()->count();
        $closed = Feedback::closed()->count();
        
        // 按类型统计
        $byType = DB::table('feedbacks')
            ->select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->get()
            ->keyBy('type')
            ->map(function ($item) {
                return $item->count;
            })
            ->toArray();
        
        return [
            'total' => $total,
            'by_status' => [
                'pending' => $pending,
                'processing' => $processing,
                'resolved' => $resolved,
                'closed' => $closed,
            ],
            'by_type' => $byType,
        ];
    }
} 