<?php

/**
 * 库存查询性能测试脚本
 * 演示动态库存计算的性能优化效果
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Product\Models\Product;
use Illuminate\Support\Facades\DB;

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🚀 库存查询性能测试开始...\n\n";

// 获取测试商品
$testProductIds = Product::limit(50)->pluck('id')->toArray();
echo "📦 测试商品数量: " . count($testProductIds) . "\n\n";

// ==========================================
// 测试1: 传统逐个查询方式（模拟优化前）
// ==========================================
echo "1️⃣ 传统逐个查询方式:\n";
$result1 = Product::monitorStockPerformance(function() use ($testProductIds) {
    $stocks = [];
    foreach ($testProductIds as $productId) {
        $product = Product::find($productId);
        if ($product) {
            $stocks[$productId] = $product->getTotalStock(false); // 不使用缓存
        }
    }
    return $stocks;
}, 'individual_queries');

echo "✅ 查询完成，共获取 " . count($result1) . " 个商品库存\n\n";

// ==========================================
// 测试2: 批量查询方式（优化后）
// ==========================================
echo "2️⃣ 批量查询方式:\n";
$result2 = Product::monitorStockPerformance(function() use ($testProductIds) {
    return Product::getBatchTotalStock($testProductIds, false); // 不使用缓存
}, 'batch_queries');

echo "✅ 查询完成，共获取 " . count($result2) . " 个商品库存\n\n";

// ==========================================
// 测试3: 批量查询 + 缓存（最优）
// ==========================================
echo "3️⃣ 批量查询 + 缓存:\n";
$result3 = Product::monitorStockPerformance(function() use ($testProductIds) {
    return Product::getBatchTotalStock($testProductIds, true); // 使用缓存
}, 'batch_queries_with_cache');

echo "✅ 查询完成，共获取 " . count($result3) . " 个商品库存\n\n";

// ==========================================
// 测试4: 第二次使用缓存（命中缓存）
// ==========================================
echo "4️⃣ 第二次查询（缓存命中）:\n";
$result4 = Product::monitorStockPerformance(function() use ($testProductIds) {
    return Product::getBatchTotalStock($testProductIds, true); // 使用缓存
}, 'cache_hit');

echo "✅ 查询完成，共获取 " . count($result4) . " 个商品库存\n\n";

// ==========================================
// 缓存统计
// ==========================================
echo "📊 缓存统计信息:\n";
$cacheStats = Product::getStockCacheStats();
foreach ($cacheStats as $key => $value) {
    echo "  - {$key}: {$value}\n";
}
echo "\n";

// ==========================================
// 性能建议
// ==========================================
echo "💡 性能优化建议:\n";
echo "  1. 商品列表页面: 使用 getBatchTotalStock() 批量查询\n";
echo "  2. 单个商品: 使用 getTotalStock() 带缓存\n";
echo "  3. 购物车检查: 批量验证多个商品库存\n";
echo "  4. 高频访问: 启用Redis缓存，缓存时间5-15分钟\n";
echo "  5. 数据库优化: 在 inventory(product_id, warehouse_id) 上建复合索引\n\n";

// ==========================================
// 数据验证
// ==========================================
echo "🔍 数据一致性验证:\n";
$inconsistencies = 0;
foreach ($testProductIds as $productId) {
    if (($result1[$productId] ?? 0) !== ($result2[$productId] ?? 0)) {
        $inconsistencies++;
        $stock1 = $result1[$productId] ?? 0;
        $stock2 = $result2[$productId] ?? 0;
        echo "  ⚠️  商品 {$productId}: 逐个查询={$stock1}, 批量查询={$stock2}\n";
    }
}

if ($inconsistencies === 0) {
    echo "✅ 所有查询方式结果一致\n";
} else {
    echo "❌ 发现 {$inconsistencies} 个不一致的结果\n";
}

echo "\n🎉 性能测试完成！\n"; 