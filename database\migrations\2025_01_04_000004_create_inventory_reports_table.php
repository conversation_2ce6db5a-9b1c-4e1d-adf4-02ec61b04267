<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 库存报表配置表
        Schema::create('inventory_report_configs', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('报表名称');
            $table->string('code', 50)->unique()->comment('报表代码');
            $table->enum('type', ['status', 'movement', 'analysis', 'forecast'])->comment('报表类型');
            $table->text('description')->nullable()->comment('报表描述');
            
            // 报表配置
            $table->json('config')->nullable()->comment('报表配置参数');
            $table->json('filters')->nullable()->comment('默认过滤条件');
            $table->json('columns')->nullable()->comment('报表列配置');
            $table->json('charts')->nullable()->comment('图表配置');
            
            // 权限和访问控制
            $table->enum('access_level', ['public', 'internal', 'restricted'])->default('internal')->comment('访问级别');
            $table->json('allowed_roles')->nullable()->comment('允许访问的角色');
            
            // 调度配置
            $table->boolean('is_scheduled')->default(false)->comment('是否定时生成');
            $table->string('schedule_cron', 50)->nullable()->comment('定时任务表达式');
            $table->boolean('auto_email')->default(false)->comment('是否自动邮件发送');
            $table->json('email_recipients')->nullable()->comment('邮件接收人');
            
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->foreignId('created_by')->constrained('users')->comment('创建人');
            $table->timestamps();
            
            // 索引
            $table->index(['type', 'is_active']);
            $table->index('is_scheduled');
        });
        
        // 库存报表生成记录表
        Schema::create('inventory_report_instances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('config_id')->constrained('inventory_report_configs')->onDelete('cascade')->comment('报表配置ID');
            $table->string('title', 200)->comment('报表标题');
            $table->enum('status', ['generating', 'completed', 'failed', 'expired'])->default('generating')->comment('生成状态');
            
            // 报表参数
            $table->json('parameters')->nullable()->comment('生成参数');
            $table->date('report_date')->comment('报表日期');
            $table->date('data_start_date')->nullable()->comment('数据开始日期');
            $table->date('data_end_date')->nullable()->comment('数据结束日期');
            
            // 报表数据
            $table->longText('report_data')->nullable()->comment('报表数据（JSON格式）');
            $table->json('summary')->nullable()->comment('报表摘要');
            $table->json('charts_data')->nullable()->comment('图表数据');
            
            // 文件信息
            $table->string('file_path', 500)->nullable()->comment('报表文件路径');
            $table->string('file_format', 20)->nullable()->comment('文件格式');
            $table->bigInteger('file_size')->nullable()->comment('文件大小（字节）');
            
            // 生成信息
            $table->timestamp('started_at')->nullable()->comment('开始生成时间');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->integer('generation_time')->nullable()->comment('生成耗时（秒）');
            $table->text('error_message')->nullable()->comment('错误信息');
            
            // 访问统计
            $table->integer('view_count')->default(0)->comment('查看次数');
            $table->integer('download_count')->default(0)->comment('下载次数');
            $table->timestamp('last_accessed_at')->nullable()->comment('最后访问时间');
            
            $table->foreignId('generated_by')->constrained('users')->comment('生成人');
            $table->timestamps();
            
            // 索引
            $table->index(['config_id', 'report_date']);
            $table->index(['status', 'created_at']);
            $table->index('report_date');
        });
        
        // 库存分析指标表
        Schema::create('inventory_analysis_metrics', function (Blueprint $table) {
            $table->id();
            $table->date('analysis_date')->comment('分析日期');
            $table->enum('metric_type', ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'])->comment('指标类型');
            
            // 基础指标
            $table->decimal('total_inventory_value', 15, 2)->default(0)->comment('总库存价值');
            $table->decimal('total_inventory_quantity', 12, 2)->default(0)->comment('总库存数量');
            $table->integer('total_sku_count')->default(0)->comment('总SKU数量');
            $table->integer('active_sku_count')->default(0)->comment('有库存SKU数量');
            $table->integer('zero_stock_sku_count')->default(0)->comment('零库存SKU数量');
            
            // 周转指标
            $table->decimal('inventory_turnover_ratio', 8, 4)->nullable()->comment('库存周转率');
            $table->decimal('average_inventory_days', 8, 2)->nullable()->comment('平均库存天数');
            $table->decimal('fast_moving_ratio', 5, 4)->nullable()->comment('快速流动商品比例');
            $table->decimal('slow_moving_ratio', 5, 4)->nullable()->comment('慢速流动商品比例');
            $table->decimal('dead_stock_ratio', 5, 4)->nullable()->comment('呆滞库存比例');
            
            // 准确性指标
            $table->decimal('stock_accuracy_rate', 5, 4)->nullable()->comment('库存准确率');
            $table->integer('stockout_count')->default(0)->comment('缺货次数');
            $table->decimal('stockout_rate', 5, 4)->nullable()->comment('缺货率');
            $table->decimal('overstock_value', 12, 2)->default(0)->comment('超储价值');
            
            // 成本指标
            $table->decimal('carrying_cost', 12, 2)->default(0)->comment('持有成本');
            $table->decimal('ordering_cost', 12, 2)->default(0)->comment('订购成本');
            $table->decimal('shortage_cost', 12, 2)->default(0)->comment('缺货成本');
            $table->decimal('total_inventory_cost', 12, 2)->default(0)->comment('总库存成本');
            
            // 分类分析
            $table->json('category_analysis')->nullable()->comment('分类分析数据');
            $table->json('warehouse_analysis')->nullable()->comment('仓库分析数据');
            $table->json('supplier_analysis')->nullable()->comment('供应商分析数据');
            
            // 趋势数据
            $table->json('trend_data')->nullable()->comment('趋势数据');
            $table->json('forecast_data')->nullable()->comment('预测数据');
            
            $table->timestamps();
            
            // 索引和唯一约束
            $table->unique(['analysis_date', 'metric_type']);
            $table->index('analysis_date');
            $table->index('metric_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_analysis_metrics');
        Schema::dropIfExists('inventory_report_instances');
        Schema::dropIfExists('inventory_report_configs');
    }
}; 