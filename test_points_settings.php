<?php

/**
 * 积分设置测试脚本
 * 用于测试积分设置的获取和保存功能
 */

require_once 'vendor/autoload.php';

use App\Product\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🧪 积分设置测试脚本\n";
echo "==================\n\n";

try {
    // 1. 检查数据库连接
    echo "1. 检查数据库连接...\n";
    DB::connection()->getPdo();
    echo "✅ 数据库连接正常\n\n";

    // 2. 检查积分字段是否存在
    echo "2. 检查积分字段...\n";
    if (!Schema::hasColumn('products', 'points_reward_enabled')) {
        echo "❌ 积分字段不存在，请先运行迁移\n";
        exit(1);
    }
    echo "✅ 积分字段存在\n\n";

    // 3. 获取一个测试商品
    echo "3. 获取测试商品...\n";
    $product = Product::first();
    if (!$product) {
        echo "❌ 没有找到商品数据\n";
        exit(1);
    }
    echo "✅ 找到测试商品: ID={$product->id}, Name={$product->name}\n\n";

    // 4. 显示当前积分设置
    echo "4. 当前积分设置:\n";
    echo "   - points_reward_enabled: " . ($product->points_reward_enabled ? 'true' : 'false') . "\n";
    echo "   - points_reward_type: {$product->points_reward_type}\n";
    echo "   - points_reward_rate: {$product->points_reward_rate}\n";
    echo "   - points_reward_fixed: {$product->points_reward_fixed}\n";
    echo "   - points_min_amount: {$product->points_min_amount}\n";
    echo "   - points_reward_max: {$product->points_reward_max}\n\n";

    // 5. 测试积分设置转换逻辑
    echo "5. 测试积分设置转换逻辑:\n";
    
    // 模拟后端转换逻辑
    $pointsSettings = [
        'points_enabled' => $product->points_reward_enabled ?? true,
        'points_type' => ($product->points_reward_type === 'rate') ? 'percentage' : ($product->points_reward_type ?? 'percentage'),
        'points_min_amount' => $product->points_min_amount ?? 0,
        'points_max_per_order' => $product->points_reward_max ?? 0,
    ];

    if ($product->points_reward_type === 'fixed') {
        $pointsSettings['points_value'] = $product->points_reward_fixed ?? 1;
        $pointsSettings['points_ratio'] = 100;
    } else {
        $pointsSettings['points_value'] = 1;
        $rate = $product->points_reward_rate;
        
        if ($rate !== null && $rate > 0) {
            $floatRate = (float)$rate;
            $calculatedRatio = round(1 / $floatRate);
            
            if ($calculatedRatio > 0 && $calculatedRatio <= 10000) {
                $pointsSettings['points_ratio'] = $calculatedRatio;
            } else {
                echo "   ⚠️  计算的积分比例异常: {$calculatedRatio}\n";
                $pointsSettings['points_ratio'] = 100;
            }
        } else {
            $pointsSettings['points_ratio'] = 100;
        }
    }

    echo "   转换后的前端数据:\n";
    foreach ($pointsSettings as $key => $value) {
        echo "   - {$key}: {$value}\n";
    }
    echo "\n";

    // 6. 测试保存逻辑
    echo "6. 测试保存逻辑:\n";
    $testData = [
        'points_enabled' => true,
        'points_type' => 'percentage',
        'points_ratio' => 100,
        'points_min_amount' => 0,
        'points_max_per_order' => 0,
    ];

    echo "   测试数据: " . json_encode($testData) . "\n";

    // 模拟后端保存逻辑
    $product->points_reward_enabled = $testData['points_enabled'];
    $product->points_min_amount = $testData['points_min_amount'] ?? 0;
    $product->points_reward_max = $testData['points_max_per_order'] ?? null;

    if ($testData['points_type'] === 'fixed') {
        $product->points_reward_type = 'fixed';
        $product->points_reward_fixed = $testData['points_value'] ?? 1;
        $product->points_reward_rate = 0;
    } else {
        $product->points_reward_type = 'rate';
        $product->points_reward_fixed = 0;
        $ratio = $testData['points_ratio'] ?? 100;
        $product->points_reward_rate = 1 / $ratio;
    }

    echo "   保存后的数据库值:\n";
    echo "   - points_reward_enabled: " . ($product->points_reward_enabled ? 'true' : 'false') . "\n";
    echo "   - points_reward_type: {$product->points_reward_type}\n";
    echo "   - points_reward_rate: {$product->points_reward_rate}\n";
    echo "   - points_reward_fixed: {$product->points_reward_fixed}\n";

    // 验证往返转换
    $verifyRate = $product->points_reward_rate;
    $verifyRatio = round(1 / $verifyRate);
    echo "   验证往返转换: rate={$verifyRate} -> ratio={$verifyRatio}\n";

    if ($verifyRatio == $testData['points_ratio']) {
        echo "   ✅ 往返转换正确\n";
    } else {
        echo "   ❌ 往返转换错误: 期望={$testData['points_ratio']}, 实际={$verifyRatio}\n";
    }

    echo "\n✅ 测试完成！\n";

} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
