<?php

namespace App\Points\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PointsTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'points',
        'balance_before',
        'balance_after',
        'type',
        'source',
        'source_id',
        'description',
        'expired_at',
    ];

    protected $casts = [
        'points' => 'integer',
        'balance_before' => 'integer',
        'balance_after' => 'integer',
        'expired_at' => 'datetime',
    ];

    /**
     * 积分变动类型常量
     */
    const TYPE_EARN = 'earn';       // 获得积分
    const TYPE_SPEND = 'spend';     // 消费积分
    const TYPE_REFUND = 'refund';   // 退还积分
    const TYPE_EXPIRE = 'expire';   // 积分过期
    const TYPE_ADMIN = 'admin';     // 管理员操作

    /**
     * 积分来源常量
     */
    const SOURCE_ORDER = 'order';               // 订单完成
    const SOURCE_SIGNIN = 'signin';             // 每日签到
    const SOURCE_INVITE = 'invite';             // 邀请好友
    const SOURCE_REVIEW = 'review';             // 商品评价
    const SOURCE_SHARE = 'share';               // 分享商品
    const SOURCE_BIRTHDAY = 'birthday';         // 生日奖励
    const SOURCE_UPGRADE = 'upgrade';           // 会员升级
    const SOURCE_POINTS_MALL = 'points_mall';   // 积分商城
    const SOURCE_ADMIN = 'admin';               // 管理员操作
    const SOURCE_REFUND = 'refund';             // 退款退积分
    const SOURCE_EXPIRE = 'expire';             // 积分过期

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取积分变动类型文本
     */
    public function getTypeTextAttribute(): string
    {
        return match($this->type) {
            self::TYPE_EARN => '获得积分',
            self::TYPE_SPEND => '消费积分',
            self::TYPE_REFUND => '退还积分',
            self::TYPE_EXPIRE => '积分过期',
            self::TYPE_ADMIN => '管理员操作',
            default => '未知类型'
        };
    }

    /**
     * 获取积分来源文本
     */
    public function getSourceTextAttribute(): string
    {
        return match($this->source) {
            self::SOURCE_ORDER => '订单完成',
            self::SOURCE_SIGNIN => '每日签到',
            self::SOURCE_INVITE => '邀请好友',
            self::SOURCE_REVIEW => '商品评价',
            self::SOURCE_SHARE => '分享商品',
            self::SOURCE_BIRTHDAY => '生日奖励',
            self::SOURCE_UPGRADE => '会员升级',
            self::SOURCE_POINTS_MALL => '积分商城',
            self::SOURCE_ADMIN => '管理员操作',
            self::SOURCE_REFUND => '退款退积分',
            self::SOURCE_EXPIRE => '积分过期',
            default => '其他'
        };
    }

    /**
     * 获取积分变动符号
     */
    public function getPointsSignAttribute(): string
    {
        return $this->points > 0 ? '+' : '';
    }

    /**
     * 获取积分变动显示文本
     */
    public function getPointsDisplayAttribute(): string
    {
        return $this->getPointsSignAttribute() . $this->points;
    }

    /**
     * 是否为积分增加
     */
    public function isEarning(): bool
    {
        return $this->points > 0;
    }

    /**
     * 是否为积分消费
     */
    public function isSpending(): bool
    {
        return $this->points < 0;
    }

    /**
     * 是否已过期
     */
    public function isExpired(): bool
    {
        return $this->expired_at && $this->expired_at->isPast();
    }

    /**
     * 创建积分流水记录
     */
    public static function createTransaction(
        int $userId,
        int $points,
        string $type,
        string $source,
        ?int $sourceId = null,
        ?string $description = null,
        ?\DateTime $expiredAt = null
    ): self {
        $user = User::findOrFail($userId);
        $balanceBefore = $user->member_points;
        $balanceAfter = $balanceBefore + $points;

        return self::create([
            'user_id' => $userId,
            'points' => $points,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'type' => $type,
            'source' => $source,
            'source_id' => $sourceId,
            'description' => $description,
            'expired_at' => $expiredAt,
        ]);
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按类型筛选
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 作用域：按来源筛选
     */
    public function scopeBySource($query, string $source)
    {
        return $query->where('source', $source);
    }

    /**
     * 作用域：积分增加记录
     */
    public function scopeEarning($query)
    {
        return $query->where('points', '>', 0);
    }

    /**
     * 作用域：积分消费记录
     */
    public function scopeSpending($query)
    {
        return $query->where('points', '<', 0);
    }

    /**
     * 作用域：未过期记录
     */
    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expired_at')->orWhere('expired_at', '>', now());
        });
    }

    /**
     * 作用域：已过期记录
     */
    public function scopeExpired($query)
    {
        return $query->whereNotNull('expired_at')->where('expired_at', '<=', now());
    }
} 