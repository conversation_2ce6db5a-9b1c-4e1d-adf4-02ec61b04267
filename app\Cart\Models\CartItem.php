<?php

namespace App\Cart\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Product\Models\Product;
use App\Product\Models\ProductSku;

class CartItem extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'cart_id',
        'product_id',
        'sku_id',
        'quantity',
        'is_selected',
    ];
    
    protected $casts = [
        'is_selected' => 'boolean',
    ];
    
    /**
     * 购物车项与购物车的关系
     */
    public function cart()
    {
        return $this->belongsTo(Cart::class);
    }
    
    /**
     * 购物车项与商品的关系
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
    
    /**
     * 购物车项与SKU的关系
     */
    public function sku()
    {
        return $this->belongsTo(ProductSku::class, 'sku_id');
    }
} 