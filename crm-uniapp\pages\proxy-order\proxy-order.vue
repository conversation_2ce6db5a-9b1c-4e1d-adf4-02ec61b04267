<template>
	<view class="proxy-order-container">
		<!-- 客户信息卡片 -->
		<view class="order-card client-card">
			<view class="card-header">
				<view class="header-left">
					<text class="card-title">👤 客户信息</text>
					<text class="required">*</text>
				</view>
				<view class="header-right" v-if="selectedClient">
					<button class="change-btn" @tap="selectClient">更换</button>
				</view>
			</view>
			<view class="client-selector" @tap="selectClient">
				<view class="client-info" v-if="selectedClient">
					<view class="client-avatar">
						<text class="avatar-text">{{ getClientAvatar(selectedClient) }}</text>
					</view>
					<view class="client-details">
						<view class="client-main">
							<text class="client-name">{{ selectedClient.name }}</text>
							<view class="client-tags">
								<text class="tag vip-tag" v-if="selectedClient.membership_level">{{ selectedClient.membership_level.name || 'VIP' }}</text>
								<text class="tag new-tag" v-if="isNewClient(selectedClient)">新客户</text>
							</view>
						</view>
						<text class="client-phone">📱 {{ selectedClient.phone }}</text>
						<text class="client-merchant" v-if="selectedClient.merchant_name">🏢 {{ selectedClient.merchant_name }}</text>
					</view>
				</view>
				<view class="client-placeholder" v-else>
					<view class="placeholder-content">
						<text class="placeholder-icon">👤</text>
						<text class="placeholder-text">点击选择客户</text>
						<text class="placeholder-tip">选择要代下单的客户</text>
					</view>
				</view>
				<text class="arrow-icon" v-if="!selectedClient">></text>
			</view>
		</view>
		
		<!-- 收货地址卡片 -->
		<view class="order-card address-card" v-if="selectedClient">
			<view class="card-header">
				<view class="header-left">
					<text class="card-title">📍 收货地址</text>
					<text class="required">*</text>
				</view>
				<view class="header-right">
					<button class="action-btn primary" @tap="addAddress">
						<text class="btn-icon">+</text>
						<text class="btn-text">新增</text>
					</button>
				</view>
			</view>
			
			<!-- 地址加载状态 -->
			<view class="loading-addresses" v-if="loadingAddresses">
				<view class="loading-spinner"></view>
				<text class="loading-text">正在加载地址...</text>
			</view>
			
			<!-- 地址列表 -->
			<view class="address-list" v-else-if="clientAddresses.length > 0">
				<view 
					class="address-item" 
					:class="{ selected: selectedAddress && selectedAddress.id === address.id }"
					v-for="address in clientAddresses" 
					:key="address.id"
					@tap="selectAddress(address)"
				>
					<view class="address-info">
						<view class="address-header">
							<text class="contact-name">{{ address.contact_name }}</text>
							<text class="contact-phone">{{ address.contact_phone }}</text>
							<view class="address-badges">
								<text class="badge default-badge" v-if="address.is_default">默认</text>
								<text class="badge note-badge" v-if="address.notes">{{ address.notes }}</text>
							</view>
						</view>
						<text class="address-detail">{{ getFullAddress(address) }}</text>
					</view>
					<view class="address-actions">
						<view class="address-radio">
							<view class="radio" :class="{ checked: selectedAddress && selectedAddress.id === address.id }">
								<text class="radio-icon" v-if="selectedAddress && selectedAddress.id === address.id">✓</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 无地址状态 -->
			<view class="empty-state" v-else>
				<view class="empty-icon">📍</view>
				<text class="empty-title">该客户暂无收货地址</text>
				<text class="empty-desc">请先添加收货地址</text>
				<button class="empty-action" @tap="addAddress">+ 添加第一个地址</button>
			</view>
		</view>
		
		<!-- 商品列表卡片 -->
		<view class="order-card products-card">
			<view class="card-header">
				<view class="header-left">
					<text class="card-title">🛍️ 订单商品</text>
					<text class="required">*</text>
					<text class="product-count" v-if="orderProducts.length > 0">({{ orderProducts.length }}件)</text>
				</view>
				<view class="header-right">
					<button class="action-btn primary" @tap="addProduct">
						<text class="btn-icon">+</text>
						<text class="btn-text">添加</text>
					</button>
				</view>
			</view>
			
			<!-- 商品列表 -->
			<view class="product-list" v-if="orderProducts.length > 0">
				<view class="product-item" v-for="(product, index) in orderProducts" :key="index">
					<view class="product-info">
						<image 
							class="product-image" 
							:src="product.image || '/static/default-product.png'" 
							mode="aspectFill"
							@error="handleImageError"
						></image>
						<view class="product-details">
							<text class="product-name">{{ product.name }}</text>
							<text class="product-spec" v-if="product.specification">{{ product.specification }}</text>
							<view class="product-price-info">
								<text class="product-price">¥{{ formatPrice(product.price) }}</text>
								<text class="product-unit">/{{ product.unit || '件' }}</text>
							</view>
						</view>
					</view>
					<view class="product-actions">
						<view class="quantity-control">
							<button 
								class="quantity-btn decrease" 
								:class="{ disabled: product.quantity <= 1 }"
								@tap="decreaseQuantity(index)"
							>-</button>
							<input 
								class="quantity-input" 
								type="number" 
								:value="product.quantity"
								@input="updateQuantity(index, $event)"
								@blur="validateQuantity(index)"
							/>
							<button class="quantity-btn increase" @tap="increaseQuantity(index)">+</button>
						</view>
						<view class="product-total-info">
							<text class="product-total">¥{{ formatPrice(product.price * product.quantity) }}</text>
							<button class="remove-btn" @tap="removeProduct(index)">
								<text class="remove-icon">🗑️</text>
							</button>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 无商品状态 -->
			<view class="empty-state" v-else>
				<view class="empty-icon">🛍️</view>
				<text class="empty-title">请添加商品</text>
				<text class="empty-desc">选择客户需要的商品</text>
			</view>
		</view>
		
		<!-- 订单信息卡片 -->
		<view class="order-card order-info-card" v-if="orderProducts.length > 0">
			<view class="card-header">
				<text class="card-title">📋 订单信息</text>
			</view>
			<view class="order-form">
				<view class="form-item">
					<text class="form-label">💳 支付方式</text>
					<picker mode="selector" :value="paymentMethodIndex" :range="paymentMethods" range-key="label" @change="onPaymentMethodChange">
						<view class="picker-value">
							<text class="picker-icon">{{ paymentMethods[paymentMethodIndex].icon }}</text>
							<text class="picker-text">{{ paymentMethods[paymentMethodIndex].label }}</text>
							<text class="arrow-icon">></text>
						</view>
					</picker>
				</view>
				<view class="form-item">
					<text class="form-label">🚚 配送方式</text>
					<picker mode="selector" :value="deliveryMethodIndex" :range="deliveryMethods" range-key="label" @change="onDeliveryMethodChange">
						<view class="picker-value">
							<text class="picker-icon">{{ deliveryMethods[deliveryMethodIndex].icon }}</text>
							<text class="picker-text">{{ deliveryMethods[deliveryMethodIndex].label }}</text>
							<text class="delivery-fee" v-if="deliveryMethods[deliveryMethodIndex].fee > 0">
								+¥{{ deliveryMethods[deliveryMethodIndex].fee }}
							</text>
							<text class="arrow-icon">></text>
						</view>
					</picker>
				</view>
				<view class="form-item">
					<text class="form-label">📝 备注信息</text>
					<textarea 
						class="form-textarea" 
						placeholder="请输入订单备注（选填）" 
						v-model="orderNotes" 
						maxlength="200"
						:show-count="true"
					></textarea>
				</view>
			</view>
		</view>
		
		<!-- 订单总计 -->
		<view class="order-summary" v-if="orderProducts.length > 0">
			<view class="summary-header">
				<text class="summary-title">💰 订单总计</text>
			</view>
			<view class="summary-content">
				<view class="summary-row">
					<text class="summary-label">商品总计</text>
					<text class="summary-value">{{ getTotalQuantity() }}件</text>
				</view>
				<view class="summary-row">
					<text class="summary-label">商品金额</text>
					<text class="summary-value">¥{{ formatPrice(orderTotal) }}</text>
				</view>
				<view class="summary-row">
					<text class="summary-label">配送费用</text>
					<text class="summary-value">{{ getDeliveryFee() }}</text>
				</view>
				<view class="summary-divider"></view>
				<view class="summary-row total-row">
					<text class="summary-label">订单总额</text>
					<text class="summary-value total-value">¥{{ formatPrice(getFinalTotal()) }}</text>
				</view>
			</view>
		</view>
		
		<!-- 提交按钮 -->
		<view class="submit-section">
			<view class="submit-info" v-if="!canSubmit">
				<text class="submit-tip">{{ getSubmitTip() }}</text>
			</view>
			<button 
				class="submit-btn" 
				:class="{ disabled: !canSubmit }" 
				:disabled="!canSubmit || submitting"
				@tap="submitOrder"
			>
				<view class="submit-content">
					<text class="submit-text" v-if="submitting">提交中...</text>
					<template v-else>
						<text class="submit-text">确认下单</text>
						<text class="submit-amount" v-if="canSubmit">¥{{ formatPrice(getFinalTotal()) }}</text>
					</template>
				</view>
			</button>
		</view>
		
		<!-- 浮动操作按钮 -->
		<view class="floating-actions" v-if="selectedClient">
			<button class="floating-btn" @tap="quickAddProduct">
				<text class="floating-icon">🛍️</text>
			</button>
		</view>
	</view>
</template>

<script>
import clientApi from '../../api/client.js'
import orderApi from '../../api/order.js'
import config from '../../utils/config.js'

export default {
	data() {
		return {
			selectedClient: null,
			clientAddresses: [],
			selectedAddress: null,
			orderProducts: [],
			orderNotes: '',
			submitting: false,
			loadingAddresses: false,
			
			// 支付方式选项
			paymentMethods: [
				{ value: 'cod', label: '货到付款', icon: '💰' },
				{ value: 'wechat', label: '微信支付', icon: '💚' },
				{ value: 'alipay', label: '支付宝', icon: '💙' },
				{ value: 'cash', label: '现金支付', icon: '💵' }
			],
			paymentMethodIndex: 0,
			
			// 配送方式选项
			deliveryMethods: [
				{ value: 'standard', label: '标准配送', fee: 0, icon: '🚚' },
				{ value: 'express', label: '快递配送', fee: 10, icon: '⚡' },
				{ value: 'same_day', label: '当日达', fee: 20, icon: '🚀' },
				{ value: 'self_pickup', label: '自行提货', fee: 0, icon: '🏪' }
			],
			deliveryMethodIndex: 0
		}
	},
	
	computed: {
		// 订单总金额
		orderTotal() {
			return this.orderProducts.reduce((total, product) => {
				return total + (product.price * product.quantity)
			}, 0)
		},
		
		// 是否可以提交订单
		canSubmit() {
			return this.selectedClient && 
				   this.selectedAddress && 
				   this.orderProducts.length > 0 &&
				   this.orderProducts.every(p => p.quantity > 0)
		}
	},
	
	onLoad(options) {
		// 页面加载时的初始化
		console.log('代客下单页面参数:', options)
		
		// 如果有客户信息参数，自动选择客户
		if (options.clientId && options.clientName && options.clientPhone) {
			this.selectedClient = {
				id: parseInt(options.clientId),
				name: decodeURIComponent(options.clientName),
				phone: options.clientPhone,
				merchant_name: decodeURIComponent(options.clientName)
			}
			
			// 加载客户地址
			this.loadClientAddresses()
			
			uni.showToast({
				title: `已选择客户：${this.selectedClient.name}`,
				icon: 'none',
				duration: 2000
			})
		}
	},
	
	onShow() {
		// 页面显示时检查是否有新选择的客户或商品
		const pages = getCurrentPages()
		const currentPage = pages[pages.length - 1]
		
		// 检查是否有新选择的客户
		if (currentPage.data && currentPage.data.selectedClient) {
			this.selectedClient = currentPage.data.selectedClient
			this.loadClientAddresses()
			
			// 清除临时数据
			delete currentPage.data.selectedClient
		}
		
		// 检查是否有新选择的商品
		if (currentPage.data && currentPage.data.selectedProducts) {
			const newProducts = currentPage.data.selectedProducts
			
			// 合并商品，如果已存在则增加数量
			newProducts.forEach(newProduct => {
				const existingIndex = this.orderProducts.findIndex(p => p.id === newProduct.id)
				if (existingIndex >= 0) {
					this.orderProducts[existingIndex].quantity += newProduct.quantity
				} else {
					this.orderProducts.push({
						...newProduct,
						quantity: newProduct.quantity || 1
					})
				}
			})
			
			// 清除临时数据
			delete currentPage.data.selectedProducts
			
			uni.showToast({
				title: `已添加 ${newProducts.length} 个商品`,
				icon: 'success',
				duration: 2000
			})
		}
		
		// 检查是否有新添加的地址
		if (currentPage.data && currentPage.data.newAddress) {
			const newAddress = currentPage.data.newAddress
			
			// 添加到地址列表
			this.clientAddresses.unshift(newAddress)
			
			// 自动选择新地址
			this.selectedAddress = newAddress
			
			// 清除临时数据
			delete currentPage.data.newAddress
			
			uni.showToast({
				title: '新地址已添加并选中',
				icon: 'success',
				duration: 2000
			})
		}
	},
	
	onPullDownRefresh() {
		// 下拉刷新
		this.refreshData()
	},
	
	onReachBottom() {
		// 上拉加载更多（如果需要的话）
		console.log('到达页面底部')
	},
	
	methods: {
		// 获取客户头像文字
		getClientAvatar(client) {
			if (client.merchant_name) {
				return client.merchant_name.charAt(0)
			} else if (client.name) {
				return client.name.charAt(0)
			}
			return '客'
		},
		
		// 判断是否为新客户
		isNewClient(client) {
			// 这里可以根据注册时间或订单数量判断
			return client.orders_count === 0 || !client.orders_count
		},
		
		// 格式化价格
		formatPrice(price) {
			return parseFloat(price || 0).toFixed(2)
		},
		
		// 获取商品总数量
		getTotalQuantity() {
			return this.orderProducts.reduce((total, product) => {
				return total + product.quantity
			}, 0)
		},
		
		// 获取配送费用
		getDeliveryFee() {
			const method = this.deliveryMethods[this.deliveryMethodIndex]
			return method.fee > 0 ? `¥${method.fee}` : '免费'
		},
		
		// 获取最终总额
		getFinalTotal() {
			const deliveryFee = this.deliveryMethods[this.deliveryMethodIndex].fee || 0
			return this.orderTotal + deliveryFee
		},
		
		// 获取提交提示
		getSubmitTip() {
			if (!this.selectedClient) {
				return '请先选择客户'
			}
			if (!this.selectedAddress) {
				return '请选择收货地址'
			}
			if (this.orderProducts.length === 0) {
				return '请添加商品'
			}
			return '请完善订单信息'
		},
		
		// 处理图片加载错误
		handleImageError(e) {
			console.log('图片加载失败:', e)
			// 可以设置默认图片
		},
		
		// 验证商品数量
		validateQuantity(index) {
			const product = this.orderProducts[index]
			if (product.quantity < 1) {
				product.quantity = 1
			}
			if (product.quantity > 999) {
				product.quantity = 999
				uni.showToast({
					title: '数量不能超过999',
					icon: 'none'
				})
			}
		},
		
		// 快速添加商品
		quickAddProduct() {
			this.addProduct()
		},
		
		// 刷新数据
		async refreshData() {
			if (this.selectedClient) {
				await this.loadClientAddresses()
			}
			uni.stopPullDownRefresh()
		},
		
		// 选择客户
		selectClient() {
			uni.navigateTo({
				url: '/pages/proxy-order/select-client'
			})
		},
		
		// 选择地址
		selectAddress(address) {
			this.selectedAddress = address
			
			// 添加选择反馈
			uni.showToast({
				title: '已选择地址',
				icon: 'success',
				duration: 1000
			})
		},
		
		// 添加商品
		addProduct() {
			if (!this.selectedClient) {
				uni.showToast({
					title: '请先选择客户',
					icon: 'none'
				})
				return
			}
			
			uni.navigateTo({
				url: '/pages/proxy-order/select-product'
			})
		},
		
		// 增加商品数量
		increaseQuantity(index) {
			if (this.orderProducts[index].quantity < 999) {
			this.orderProducts[index].quantity++
			}
		},
		
		// 减少商品数量
		decreaseQuantity(index) {
			if (this.orderProducts[index].quantity > 1) {
				this.orderProducts[index].quantity--
			}
		},
		
		// 更新商品数量
		updateQuantity(index, event) {
			const quantity = parseInt(event.detail.value) || 1
			this.orderProducts[index].quantity = Math.max(1, Math.min(999, quantity))
		},
		
		// 移除商品
		removeProduct(index) {
			const product = this.orderProducts[index]
			uni.showModal({
				title: '确认删除',
				content: `确定要删除"${product.name}"吗？`,
				confirmColor: '#ff4757',
				success: (res) => {
					if (res.confirm) {
						this.orderProducts.splice(index, 1)
						uni.showToast({
							title: '已删除商品',
							icon: 'success',
							duration: 1000
						})
					}
				}
			})
		},
		
		// 支付方式改变
		onPaymentMethodChange(event) {
			this.paymentMethodIndex = event.detail.value
		},
		
		// 配送方式改变
		onDeliveryMethodChange(event) {
			this.deliveryMethodIndex = event.detail.value
		},
		
		// 加载客户地址
		async loadClientAddresses() {
			if (!this.selectedClient) return
			
			this.loadingAddresses = true
			
			try {
				const response = await clientApi.getClientAddresses(this.selectedClient.id)
				this.clientAddresses = response.data || []
				
				// 自动选择默认地址
				const defaultAddress = this.clientAddresses.find(addr => addr.is_default)
				if (defaultAddress) {
					this.selectedAddress = defaultAddress
				} else if (this.clientAddresses.length > 0) {
					this.selectedAddress = this.clientAddresses[0]
				}
			} catch (error) {
				console.error('加载客户地址失败:', error)
				uni.showToast({
					title: '加载地址失败',
					icon: 'none'
				})
			} finally {
				this.loadingAddresses = false
			}
		},
		
		// 提交订单
		async submitOrder() {
			if (!this.canSubmit || this.submitting) return
			
			// 最终确认
			const confirmResult = await new Promise((resolve) => {
				uni.showModal({
					title: '确认下单',
					content: `客户：${this.selectedClient.name}\n商品：${this.getTotalQuantity()}件\n总额：¥${this.formatPrice(this.getFinalTotal())}`,
					confirmColor: '#007AFF',
					success: (res) => resolve(res.confirm),
					fail: () => resolve(false)
				})
			})
			
			if (!confirmResult) return
			
			this.submitting = true
			
			try {
				// 准备订单数据
				const orderData = {
					client_id: this.selectedClient.id,
					user_address_id: this.selectedAddress.id,
					contact_name: this.selectedAddress.contact_name,
					contact_phone: this.selectedAddress.contact_phone,
					shipping_address: this.getFullAddress(this.selectedAddress),
					payment_method: this.paymentMethods[this.paymentMethodIndex].value,
					delivery_method: this.deliveryMethods[this.deliveryMethodIndex].value,
					delivery_fee: this.deliveryMethods[this.deliveryMethodIndex].fee || 0,
					notes: this.orderNotes,
					items: this.orderProducts.map(product => ({
						product_id: product.id,
						quantity: product.quantity,
						price: product.price,
						unit: product.unit || '件'
					}))
				}
				
				console.log('提交订单数据:', orderData)
				const response = await orderApi.createProxyOrder(orderData)
				console.log('订单创建响应:', response)
				
				// 检查响应格式
				if (response && response.data && response.data.code === 200) {
				uni.showToast({
					title: '下单成功',
					icon: 'success',
					duration: 2000
				})
					
					// 获取订单ID
					const orderId = response.data.data.id
				
				// 跳转到订单详情页
				setTimeout(() => {
					uni.redirectTo({
						url: `/pages/orders/order-detail?id=${orderId}`
					})
				}, 2000)
				} else {
					// 处理业务逻辑错误
					const errorMessage = response?.data?.message || '下单失败，请重试'
					uni.showToast({
						title: errorMessage,
						icon: 'none',
						duration: 3000
					})
				}
				
			} catch (error) {
				console.error('提交订单失败:', error)
				
				// 更详细的错误处理
				let errorMessage = '网络错误，请检查网络连接'
				if (error.response) {
					errorMessage = error.response.data?.message || `服务器错误 (${error.response.status})`
				} else if (error.message) {
					errorMessage = error.message
				}
				
				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
			} finally {
				this.submitting = false
			}
	},
	
		// 添加地址
		addAddress() {
			if (!this.selectedClient) {
				uni.showToast({
					title: '请先选择客户',
					icon: 'none'
				})
				return
			}
			
			const params = {
				clientId: this.selectedClient.id,
				clientName: encodeURIComponent(this.selectedClient.name),
				clientPhone: this.selectedClient.phone
			}
			
			const queryString = Object.keys(params).map(key => `${key}=${params[key]}`).join('&')
			
			uni.navigateTo({
				url: `/pages/proxy-order/add-address?${queryString}`
			})
		},
		
		// 获取完整地址
		getFullAddress(address) {
			if (!address) return ''
			
			let fullAddress = ''
			
			// 组合省市区
			if (address.province || address.city || address.district) {
				fullAddress = `${address.province || ''}${address.city || ''}${address.district || ''}`
			}
			
			// 添加详细地址
			if (address.detail_address) {
				fullAddress += address.detail_address
			}
			
			// 如果有full_address字段，优先使用
			if (address.full_address) {
				fullAddress = address.full_address
			}
			
			return fullAddress || '地址信息不完整'
		}
	}
}
</script>

<style scoped>
.proxy-order-container {
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	min-height: 100vh;
	padding-bottom: 200rpx;
}

/* 客户信息卡片 */
.order-card.client-card {
	margin-bottom: 20rpx;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
	background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.header-left {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
}

.required {
	color: #dc3545;
	font-size: 28rpx;
	font-weight: 700;
}

.client-selector {
	padding: 32rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.client-selector:active {
	background: rgba(0, 122, 255, 0.05);
}

.client-info {
	display: flex;
	align-items: center;
}

.client-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50rpx;
	background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 32rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.avatar-text {
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
}

.client-details {
	flex: 1;
}

.client-main {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 12rpx;
}

.client-name {
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
}

.client-tags {
	display: flex;
	gap: 8rpx;
}

.tag {
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-weight: 600;
}

.vip-tag {
	background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
	color: #ffffff;
}

.new-tag {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	color: #ffffff;
}

.client-phone,
.client-merchant {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 8rpx;
}

/* 占位符样式 */
.client-placeholder {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx 0;
}

.placeholder-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.placeholder-icon {
	font-size: 80rpx;
	opacity: 0.3;
	margin-bottom: 16rpx;
}

.placeholder-text {
	font-size: 32rpx;
	color: #666666;
	font-weight: 600;
	margin-bottom: 8rpx;
}

.placeholder-tip {
	font-size: 28rpx;
	color: #999999;
}

/* 收货地址卡片 */
.order-card.address-card {
	margin-bottom: 20rpx;
}

/* 地址列表 */
.loading-addresses {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 0;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid rgba(0, 122, 255, 0.3);
	border-radius: 50%;
	border-top-color: #007AFF;
	animation: spin 1s linear infinite;
	margin-bottom: 16rpx;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}

.address-list {
	padding: 0 32rpx 32rpx;
}

.address-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	margin-bottom: 16rpx;
	border-radius: 16rpx;
	background: #f8f9fa;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.address-item.selected {
	background: rgba(0, 122, 255, 0.05);
	border-color: #007AFF;
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.15);
}

.address-info {
	flex: 1;
}

.address-header {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 12rpx;
}

.contact-name {
	font-size: 30rpx;
	font-weight: 700;
	color: #333333;
}

.contact-phone {
	font-size: 28rpx;
	color: #666666;
}

.address-badges {
	display: flex;
	gap: 8rpx;
}

.badge {
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-weight: 600;
}

.default-badge {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	color: #ffffff;
}

.note-badge {
	background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
	color: #ffffff;
}

.address-detail {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.5;
}

.address-actions {
	margin-left: 24rpx;
}

.address-radio {
	width: 44rpx;
	height: 44rpx;
	border: 3rpx solid #d1d1d6;
	border-radius: 22rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.address-item.selected .address-radio {
	border-color: #007AFF;
	background: #007AFF;
}

.radio-icon {
	font-size: 24rpx;
	color: #ffffff;
	font-weight: 700;
}

/* 商品列表卡片 */
.order-card.products-card {
	margin-bottom: 20rpx;
}

.product-list {
	padding: 0 32rpx 32rpx;
}

.product-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	margin-bottom: 16rpx;
	border-radius: 16rpx;
	background: #f8f9fa;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.product-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.product-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 12rpx;
	margin-right: 24rpx;
	background: #e9ecef;
}

.product-details {
	flex: 1;
}

.product-name {
	font-size: 30rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
	line-height: 1.4;
}

.product-spec {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 8rpx;
}

.product-price-info {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.product-price {
	font-size: 28rpx;
	font-weight: 700;
	color: #007AFF;
}

.product-unit {
	font-size: 24rpx;
	color: #666666;
}

.product-actions {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 16rpx;
}

.quantity-control {
	display: flex;
	align-items: center;
	border: 2rpx solid #e9ecef;
	border-radius: 12rpx;
	overflow: hidden;
}

.quantity-btn {
	width: 60rpx;
	height: 60rpx;
	background: #f8f9fa;
	border: none;
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.quantity-btn:active {
	background: #e9ecef;
}

.quantity-btn.disabled {
	color: #cccccc;
	background: #f8f9fa;
}

.quantity-input {
	width: 80rpx;
	height: 60rpx;
	text-align: center;
	font-size: 28rpx;
	font-weight: 600;
	border: none;
	background: #ffffff;
}

.product-total-info {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.product-total {
	font-size: 28rpx;
	font-weight: 700;
	color: #007AFF;
}

.remove-btn {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
	border: none;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(220, 53, 69, 0.3);
}

.remove-icon {
	font-size: 24rpx;
}

/* 订单信息卡片 */
.order-card.order-info-card {
	margin-bottom: 20rpx;
}

.order-form {
	padding: 32rpx;
}

.form-item {
	margin-bottom: 32rpx;
}

.form-label {
	font-size: 30rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
	display: block;
}

.picker-value {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.picker-value:active {
	background: rgba(0, 122, 255, 0.05);
	border-color: #007AFF;
}

.picker-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
}

.picker-text {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
	font-weight: 600;
}

.delivery-fee {
	font-size: 24rpx;
	color: #007AFF;
	font-weight: 600;
	margin-right: 16rpx;
}

.form-textarea {
	width: 100%;
	min-height: 160rpx;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	border: 2rpx solid transparent;
	font-size: 28rpx;
	color: #333333;
	line-height: 1.5;
	transition: all 0.3s ease;
}

.form-textarea:focus {
	background: rgba(0, 122, 255, 0.05);
	border-color: #007AFF;
}

/* 订单总计 */
.order-summary {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
}

.summary-header {
	padding: 32rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.summary-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
}

.summary-content {
	padding: 32rpx;
}

.summary-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.summary-label {
	font-size: 28rpx;
	color: #666666;
}

.summary-value {
	font-size: 28rpx;
	color: #333333;
	font-weight: 600;
}

.summary-divider {
	height: 2rpx;
	background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
	margin: 24rpx 0;
}

.total-row {
	margin-bottom: 0;
	padding-top: 16rpx;
}

.total-row .summary-label {
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
}

.total-value {
	font-size: 40rpx;
	font-weight: 700;
	color: #007AFF;
}

/* 提交按钮 */
.submit-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
	padding: 32rpx;
	border-top: 1rpx solid rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(20rpx);
}

.submit-info {
	text-align: center;
	margin-bottom: 20rpx;
}

.submit-tip {
	font-size: 28rpx;
	color: #999999;
}

.submit-btn {
	width: 100%;
	height: 96rpx;
	background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
	color: #ffffff;
	border: none;
	border-radius: 24rpx;
	font-size: 32rpx;
	font-weight: 700;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.4);
	transition: all 0.3s ease;
}

.submit-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.submit-btn.disabled {
	background: linear-gradient(135deg, #d1d1d6 0%, #8e8e93 100%);
	color: #ffffff;
	box-shadow: none;
	transform: none;
}

.submit-content {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.submit-text {
	font-size: 32rpx;
	font-weight: 700;
}

.submit-amount {
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 80rpx 40rpx;
}

.empty-icon {
	font-size: 120rpx;
	opacity: 0.3;
	display: block;
	margin-bottom: 24rpx;
}

.empty-title {
	font-size: 32rpx;
	color: #666666;
	display: block;
	margin-bottom: 12rpx;
	font-weight: 600;
}

.empty-desc {
	font-size: 28rpx;
	color: #999999;
	display: block;
	margin-bottom: 32rpx;
}

.empty-action {
	background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
	color: #ffffff;
	border: none;
	border-radius: 24rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
	font-weight: 600;
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

/* 浮动操作按钮 */
.floating-actions {
	position: fixed;
	bottom: 140rpx;
	right: 32rpx;
	z-index: 999;
}

.floating-btn {
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
	color: #ffffff;
	border: none;
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.4);
	transition: all 0.3s ease;
}

.floating-btn:active {
	transform: scale(0.95);
}

.floating-icon {
	font-size: 48rpx;
}

/* 通用样式 */
.arrow-icon {
	font-size: 32rpx;
	color: #cccccc;
	margin-left: 16rpx;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
	.stats-overview {
		padding: 24rpx 16rpx 16rpx;
	}
	
	.stats-card {
		padding: 16rpx 12rpx;
		margin: 0 4rpx;
	}
	
	.stats-number {
		font-size: 32rpx;
	}
	
	.stats-label {
		font-size: 22rpx;
	}
	
	.progress-indicator {
		padding: 24rpx 16rpx;
	}
	
	.step-text {
		font-size: 20rpx;
	}
	
	.order-card {
		margin: 16rpx;
		padding: 24rpx;
	}
	
	.client-avatar {
		width: 80rpx;
		height: 80rpx;
		margin-right: 24rpx;
	}
	
	.avatar-text {
		font-size: 32rpx;
	}
}
</style> 