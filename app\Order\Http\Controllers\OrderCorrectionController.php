<?php

namespace App\Order\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Order\Models\Order;
use App\Order\Models\OrderCorrection;
use App\Order\Models\PaymentLink;
use App\Order\Services\OrderCorrectionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class OrderCorrectionController extends Controller
{
    protected $correctionService;

    public function __construct(OrderCorrectionService $correctionService)
    {
        $this->correctionService = $correctionService;
    }

    /**
     * 获取订单更正列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = OrderCorrection::with(['order', 'corrector', 'items.orderItem']);

        // 搜索条件 - 统一参数命名，支持前端的驼峰命名
        if ($request->has('order_id')) {
            $query->where('order_id', $request->order_id);
        }
        
        // 支持订单号搜索
        if ($request->has('order_no') || $request->has('orderNo')) {
            $orderNo = $request->order_no ?? $request->orderNo;
            $query->whereHas('order', function($q) use ($orderNo) {
                $q->where('order_no', 'like', '%' . $orderNo . '%');
            });
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('correction_type') || $request->has('correctionType')) {
            $correctionType = $request->correction_type ?? $request->correctionType;
            $query->where('correction_type', $correctionType);
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('corrected_at', [$request->start_date, $request->end_date]);
        }

        $corrections = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $corrections
        ]);
    }

    /**
     * 创建订单更正
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|exists:orders,id',
            'correction_reason' => 'nullable|string|max:500',
            'items' => 'required|array|min:1',
            'items.*.order_item_id' => 'required|exists:order_items,id',
            'items.*.original_quantity' => 'required|integer|min:0',
            'items.*.original_price' => 'required|numeric|min:0',
            'items.*.original_total' => 'required|numeric|min:0',
            'items.*.corrected_quantity' => 'required|integer|min:0',
            'items.*.corrected_weight' => 'nullable|numeric|min:0',
            'items.*.corrected_price' => 'required|numeric|min:0',
            'items.*.corrected_total' => 'required|numeric|min:0',
            'items.*.reason' => 'nullable|string|max:200',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $order = Order::findOrFail($request->order_id);
            
            // 新的业务逻辑：检查是否已生成付款链接
            $activePaymentLink = PaymentLink::where('order_id', $order->id)
                ->where('status', 'active')
                ->where('expires_at', '>', now())
                ->first();

            if ($activePaymentLink) {
                return response()->json([
                    'code' => 400,
                    'message' => '该订单已生成付款链接，无法再进行更正。如需更正，请先取消现有付款链接。',
                    'data' => [
                        'payment_link_id' => $activePaymentLink->id,
                        'payment_link_no' => $activePaymentLink->link_no,
                        'expires_at' => $activePaymentLink->expires_at
                    ]
                ], 400);
            }

            // 检查是否有已支付的付款链接
            $paidPaymentLink = PaymentLink::where('order_id', $order->id)
                ->where('status', 'paid')
                ->first();

            if ($paidPaymentLink) {
                return response()->json([
                    'code' => 400,
                    'message' => '该订单已完成付款，无法再进行更正',
                    'data' => [
                        'payment_link_id' => $paidPaymentLink->id,
                        'paid_at' => $paidPaymentLink->paid_at
                    ]
                ], 400);
            }

            // 验证原始数据与订单项是否一致
            $validationErrors = $this->validateCorrectionItems($order, $request->items);
            if (!empty($validationErrors)) {
                return response()->json([
                    'code' => 400,
                    'message' => '数据验证失败',
                    'errors' => $validationErrors
                ], 400);
            }

            // 验证是否有实际的更正变化（只检查数量变化，价格应保持不变）
            $hasChanges = false;
            foreach ($request->items as $item) {
                // 检查数量是否有变化
                if ($item['corrected_quantity'] != $item['original_quantity']) {
                    $hasChanges = true;
                    break;
                }
                
                // 验证价格是否保持不变（业务规则：不允许修改价格）
                if (abs($item['corrected_price'] - $item['original_price']) > 0.01) {
                    return response()->json([
                        'code' => 400,
                        'message' => '不允许修改商品价格，只能调整数量'
                    ], 400);
                }
            }

            if (!$hasChanges) {
                return response()->json([
                    'code' => 400,
                    'message' => '没有检测到任何数量变化，请修改商品数量后再提交'
                ], 400);
            }

            $correction = $this->correctionService->createCorrection(
                $order,
                $request->only(['correction_reason']),
                $request->items,
                Auth::id()
            );

            return response()->json([
                'code' => 200,
                'message' => '订单更正创建成功',
                'data' => $correction->load(['order', 'items.orderItem'])
            ]);

        } catch (\Exception $e) {
            Log::error('创建订单更正失败', [
                'order_id' => $request->order_id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '创建失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取订单更正详情
     */
    public function show(int $id): JsonResponse
    {
        try {
            $correction = OrderCorrection::with([
                'order',
                'items.orderItem',
                'corrector',
                'confirmer',
                'paymentRecords'
            ])->findOrFail($id);

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $correction
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 404,
                'message' => '更正记录不存在'
            ], 404);
        }
    }

    /**
     * 确认订单更正
     */
    public function confirm(int $id): JsonResponse
    {
        try {
            $correction = OrderCorrection::findOrFail($id);

            if ($correction->status !== 'pending') {
                return response()->json([
                    'code' => 400,
                    'message' => '只有待确认的更正记录才能确认'
                ], 400);
            }

            $this->correctionService->confirmCorrection($correction, Auth::id());

            return response()->json([
                'code' => 200,
                'message' => '订单更正确认成功',
                'data' => $correction->fresh(['order', 'items.orderItem'])
            ]);

        } catch (\Exception $e) {
            Log::error('确认订单更正失败', [
                'correction_id' => $id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '确认失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 取消订单更正
     */
    public function cancel(int $id): JsonResponse
    {
        try {
            $correction = OrderCorrection::findOrFail($id);

            if ($correction->status !== 'pending') {
                return response()->json([
                    'code' => 400,
                    'message' => '只有待确认的更正记录才能取消'
                ], 400);
            }

            $this->correctionService->cancelCorrection($correction);

            return response()->json([
                'code' => 200,
                'message' => '订单更正已取消',
                'data' => $correction->fresh(['order', 'items.orderItem'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '取消失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 处理退款
     */
    public function processRefund(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $correction = OrderCorrection::with(['order'])->findOrFail($id);

            // 验证更正状态
            if ($correction->status !== 'confirmed') {
                return response()->json([
                    'code' => 400,
                    'message' => '只有已确认的更正才能处理退款'
                ], 400);
            }

            // 验证是否需要退款
            if (!$correction->needsRefund()) {
                return response()->json([
                    'code' => 400,
                    'message' => '该更正不需要退款'
                ], 400);
            }

            // 检查是否已经成功处理过退款
            $successfulRefund = $correction->paymentRecords()
                ->where('payment_type', 'refund')
                ->where('status', 'success')  // 只检查已成功的退款
                ->first();

            if ($successfulRefund) {
                return response()->json([
                    'code' => 400,
                    'message' => '该更正的退款已经完成，无需重复处理'
                ], 400);
            }

            // 检查是否有待处理的退款，如果有则取消旧的记录
            $pendingRefund = $correction->paymentRecords()
                ->where('payment_type', 'refund')
                ->where('status', 'pending')
                ->first();

            if ($pendingRefund) {
                // 取消旧的待处理退款记录
                $pendingRefund->update([
                    'status' => 'cancelled',
                    'notes' => ($pendingRefund->notes ?? '') . ' [系统自动取消：重新处理退款]'
                ]);

                Log::info('取消旧的退款记录', [
                    'correction_id' => $correction->id,
                    'old_payment_record_id' => $pendingRefund->id,
                    'user_id' => Auth::id()
                ]);
            }

            // 处理退款
            $this->correctionService->processRefund($correction, Auth::id());

            // 记录操作日志
            Log::info('订单更正退款处理', [
                'correction_id' => $correction->id,
                'order_id' => $correction->order_id,
                'refund_amount' => abs($correction->difference_amount),
                'user_id' => Auth::id(),
                'notes' => $request->notes
            ]);

            return response()->json([
                'code' => 200,
                'message' => '退款处理成功',
                'data' => $correction->fresh(['order', 'paymentRecords'])
            ]);

        } catch (\Exception $e) {
            Log::error('处理退款失败', [
                'correction_id' => $id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '退款处理失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 处理补款
     */
    public function processSupplement(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $correction = OrderCorrection::with(['order'])->findOrFail($id);

            // 验证更正状态
            if ($correction->status !== 'confirmed') {
                return response()->json([
                    'code' => 400,
                    'message' => '只有已确认的更正才能处理补款'
                ], 400);
            }

            // 验证是否需要补款
            if (!$correction->needsSupplement()) {
                return response()->json([
                    'code' => 400,
                    'message' => '该更正不需要补款'
                ], 400);
            }

            // 检查是否已经成功处理过补款
            $successfulSupplement = $correction->paymentRecords()
                ->where('payment_type', 'supplement')
                ->where('status', 'success')  // 只检查已成功的补款
                ->first();

            if ($successfulSupplement) {
                return response()->json([
                    'code' => 400,
                    'message' => '该更正的补款已经完成，无需重复处理'
                ], 400);
            }

            // 检查是否有待处理的补款，如果有则取消旧的记录
            $pendingSupplement = $correction->paymentRecords()
                ->where('payment_type', 'supplement')
                ->where('status', 'pending')
                ->first();

            if ($pendingSupplement) {
                // 取消旧的待处理补款记录
                $pendingSupplement->update([
                    'status' => 'cancelled',
                    'notes' => ($pendingSupplement->notes ?? '') . ' [系统自动取消：重新生成补款链接]'
                ]);

                // 同时取消相关的付款链接
                $relatedPaymentLink = \App\Order\Models\PaymentLink::where('correction_id', $correction->id)
                    ->where('payment_type', 'supplement')
                    ->where('status', 'active')
                    ->first();

                if ($relatedPaymentLink) {
                    $relatedPaymentLink->update([
                        'status' => 'cancelled',
                        'remark' => '重新生成补款链接，自动取消旧链接'
                    ]);
                }

                Log::info('取消旧的补款记录', [
                    'correction_id' => $correction->id,
                    'old_payment_record_id' => $pendingSupplement->id,
                    'old_payment_link_id' => $relatedPaymentLink?->id,
                    'user_id' => Auth::id()
                ]);
            }

            // 处理补款
            $this->correctionService->processSupplement($correction, Auth::id());

            // 获取生成的付款链接（如果是微信支付）
            $paymentLink = null;
            if ($correction->order->payment_method === 'wechat') {
                $paymentLink = \App\Order\Models\PaymentLink::where('correction_id', $correction->id)
                    ->where('payment_type', 'supplement')
                    ->where('status', 'active')
                    ->latest()
                    ->first();
            }

            // 记录操作日志
            Log::info('订单更正补款处理', [
                'correction_id' => $correction->id,
                'order_id' => $correction->order_id,
                'supplement_amount' => $correction->difference_amount,
                'payment_link_id' => $paymentLink?->id,
                'user_id' => Auth::id(),
                'notes' => $request->notes
            ]);

            $responseData = [
                'correction' => $correction->fresh(['order', 'paymentRecords'])
            ];

            // 如果生成了付款链接，添加到返回数据中
            if ($paymentLink) {
                $responseData['payment_link'] = [
                    'id' => $paymentLink->id,
                    'link_no' => $paymentLink->link_no,
                    'amount' => $paymentLink->amount,
                    'payment_url' => url("/api/payment/{$paymentLink->id}"),
                    'short_url' => url("/api/payment/{$paymentLink->id}"),
                    'expires_at' => $paymentLink->expires_at,
                    'status' => $paymentLink->status,
                    'created_at' => $paymentLink->created_at
                ];
            }

            return response()->json([
                'code' => 200,
                'message' => '补款处理成功',
                'data' => $responseData
            ]);

        } catch (\Exception $e) {
            Log::error('处理补款失败', [
                'correction_id' => $id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '补款处理失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 选择货到付款结算方式
     */
    public function chooseCodSettlement(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|in:cash,online',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $correction = OrderCorrection::with(['order'])->findOrFail($id);

            // 验证订单是否为货到付款
            if ($correction->order->payment_method !== 'cod') {
                return response()->json([
                    'code' => 400,
                    'message' => '只有货到付款订单才能选择结算方式'
                ], 400);
            }

            // 验证更正状态
            if ($correction->status !== 'confirmed') {
                return response()->json([
                    'code' => 400,
                    'message' => '只有已确认的更正才能选择结算方式'
                ], 400);
            }

            // 选择结算方式
            $this->correctionService->chooseCodSettlementMethod(
                $correction, 
                $request->payment_method, 
                Auth::id(),
                ['notes' => $request->notes]
            );

            // 记录操作日志
            Log::info('货到付款结算方式选择', [
                'correction_id' => $correction->id,
                'order_id' => $correction->order_id,
                'payment_method' => $request->payment_method,
                'amount' => abs($correction->difference_amount),
                'user_id' => Auth::id(),
                'notes' => $request->notes
            ]);

            $message = $request->payment_method === 'cash' 
                ? '现金结算处理成功' 
                : '线上付款处理成功';

            return response()->json([
                'code' => 200,
                'message' => $message,
                'data' => $correction->fresh(['order', 'paymentRecords'])
            ]);

        } catch (\Exception $e) {
            Log::error('选择结算方式失败', [
                'correction_id' => $id,
                'payment_method' => $request->payment_method,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '选择结算方式失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新订单更正
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'correction_reason' => 'nullable|string|max:500',
            'items' => 'sometimes|array|min:1',
            'items.*.order_item_id' => 'required_with:items|exists:order_items,id',
            'items.*.corrected_quantity' => 'required_with:items|integer|min:0',
            'items.*.corrected_weight' => 'nullable|numeric|min:0',
            'items.*.corrected_price' => 'required_with:items|numeric|min:0',
            'items.*.corrected_total' => 'required_with:items|numeric|min:0',
            'items.*.reason' => 'nullable|string|max:200',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $correction = OrderCorrection::findOrFail($id);

            if ($correction->status !== 'pending') {
                return response()->json([
                    'code' => 400,
                    'message' => '只有待确认的更正记录才能修改'
                ], 400);
            }

            $correction = $this->correctionService->updateCorrection(
                $correction,
                $request->only(['correction_reason']),
                $request->get('items', [])
            );

            return response()->json([
                'code' => 200,
                'message' => '订单更正更新成功',
                'data' => $correction->load(['order', 'items.orderItem'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量确认订单更正
     */
    public function batchConfirm(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:order_corrections,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $corrections = OrderCorrection::whereIn('id', $request->ids)
                ->where('status', 'pending')
                ->get();

            if ($corrections->isEmpty()) {
                return response()->json([
                    'code' => 400,
                    'message' => '没有找到可确认的更正记录'
                ], 400);
            }

            $successCount = 0;
            $errors = [];

            foreach ($corrections as $correction) {
                try {
                    $this->correctionService->confirmCorrection($correction, Auth::id());
                    $successCount++;
                } catch (\Exception $e) {
                    $errors[] = "更正记录 {$correction->id} 确认失败：{$e->getMessage()}";
                }
            }

            return response()->json([
                'code' => 200,
                'message' => "批量确认完成，成功 {$successCount} 条",
                'data' => [
                    'success_count' => $successCount,
                    'total_count' => count($request->ids),
                    'errors' => $errors
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '批量确认失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量取消订单更正
     */
    public function batchCancel(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:order_corrections,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $corrections = OrderCorrection::whereIn('id', $request->ids)
                ->where('status', 'pending')
                ->get();

            if ($corrections->isEmpty()) {
                return response()->json([
                    'code' => 400,
                    'message' => '没有找到可取消的更正记录'
                ], 400);
            }

            $successCount = 0;
            $errors = [];

            foreach ($corrections as $correction) {
                try {
                    $this->correctionService->cancelCorrection($correction);
                    $successCount++;
                } catch (\Exception $e) {
                    $errors[] = "更正记录 {$correction->id} 取消失败：{$e->getMessage()}";
                }
            }

            return response()->json([
                'code' => 200,
                'message' => "批量取消完成，成功 {$successCount} 条",
                'data' => [
                    'success_count' => $successCount,
                    'total_count' => count($request->ids),
                    'errors' => $errors
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '批量取消失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 导出订单更正数据
     */
    public function export(Request $request): JsonResponse
    {
        try {
            $query = OrderCorrection::with(['order', 'corrector', 'items.orderItem']);

            // 应用筛选条件
            if ($request->has('order_id')) {
                $query->where('order_id', $request->order_id);
            }

            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('correction_type')) {
                $query->where('correction_type', $request->correction_type);
            }

            if ($request->has('start_date') && $request->has('end_date')) {
                $query->whereBetween('corrected_at', [$request->start_date, $request->end_date]);
            }

            $corrections = $query->orderBy('created_at', 'desc')->get();

            // 这里应该实现实际的导出逻辑，比如生成Excel文件
            // 暂时返回数据，实际项目中可以使用Laravel Excel等包
            return response()->json([
                'code' => 200,
                'message' => '导出数据准备完成',
                'data' => $corrections
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '导出失败：' . $e->getMessage()
            ], 500);
        }
    }



    /**
     * 获取订单详情（用于更正）
     */
    public function orderForCorrection(int $orderId): JsonResponse
    {
        try {
            $order = Order::with(['user', 'items.product', 'corrections'])
                ->findOrFail($orderId);

            // 注释掉订单状态检查，允许对任何状态的订单进行更正
            // if ($order->status !== 'delivered') {
            //     return response()->json([
            //         'code' => 400,
            //         'message' => '只有已送达的订单才能进行更正'
            //     ], 400);
            // }

            // 检查是否已有待处理的更正
            $hasPendingCorrection = $order->corrections()
                ->where('status', 'pending')
                ->exists();

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'order' => $order,
                    'has_pending_correction' => $hasPendingCorrection
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 404,
                'message' => '订单不存在'
            ], 404);
        }
    }

    /**
     * 获取订单的更正历史
     */
    public function orderHistory(int $orderId): JsonResponse
    {
        try {
            $corrections = OrderCorrection::with(['corrector', 'items.orderItem'])
                ->where('order_id', $orderId)
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $corrections
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取更正统计数据
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $query = OrderCorrection::query();

            // 时间范围过滤
            if ($request->has('start_date') && $request->has('end_date')) {
                $query->whereBetween('corrected_at', [$request->start_date, $request->end_date]);
            }

            $statistics = [
                'total_corrections' => $query->count(),
                'pending_corrections' => (clone $query)->where('status', 'pending')->count(),
                'confirmed_corrections' => (clone $query)->where('status', 'confirmed')->count(),
                'cancelled_corrections' => (clone $query)->where('status', 'cancelled')->count(),
                'increase_corrections' => (clone $query)->where('correction_type', 'increase')->count(),
                'decrease_corrections' => (clone $query)->where('correction_type', 'decrease')->count(),
                'no_change_corrections' => (clone $query)->where('correction_type', 'no_change')->count(),
                'total_difference_amount' => (clone $query)->where('status', 'confirmed')->sum('difference_amount'),
            ];

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $statistics
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 验证更正项目数据
     */
    private function validateCorrectionItems(Order $order, array $items): array
    {
        $errors = [];
        $orderItems = $order->items->keyBy('id');

        foreach ($items as $index => $item) {
            $orderItemId = $item['order_item_id'];
            $orderItem = $orderItems->get($orderItemId);

            if (!$orderItem) {
                $errors["items.{$index}.order_item_id"] = "订单项不存在";
                continue;
            }

            // 验证原始数量
            if (isset($item['original_quantity']) && $item['original_quantity'] != $orderItem->quantity) {
                $errors["items.{$index}.original_quantity"] = "原始数量不匹配，应为 {$orderItem->quantity}";
            }

            // 验证原始价格
            if (isset($item['original_price']) && abs($item['original_price'] - $orderItem->price) > 0.01) {
                $errors["items.{$index}.original_price"] = "原始价格不匹配，应为 {$orderItem->price}";
            }

            // 验证原始总价
            if (isset($item['original_total']) && abs($item['original_total'] - $orderItem->total) > 0.01) {
                $errors["items.{$index}.original_total"] = "原始总价不匹配，应为 {$orderItem->total}";
            }

            // 验证更正后的计算
            $expectedCorrectedTotal = $item['corrected_quantity'] * $item['corrected_price'];
            if (abs($item['corrected_total'] - $expectedCorrectedTotal) > 0.01) {
                $errors["items.{$index}.corrected_total"] = "更正后总价计算错误，应为 {$expectedCorrectedTotal}";
            }
        }

        return $errors;
    }

    /**
     * 调试订单数据
     */
    public function debugOrders(Request $request): JsonResponse
    {
        try {
            // 基础统计
            $totalOrders = Order::count();
            $deliveredOrders = Order::where('status', 'delivered')->count();
            
            // 按状态统计
            $statusStats = Order::selectRaw('status, count(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();
            
            // 最近的订单
            $recentOrders = Order::orderBy('created_at', 'desc')
                ->limit(5)
                ->get(['id', 'order_no', 'status', 'delivery_date', 'created_at']);
            
            // 已送达的订单
            $deliveredOrdersList = Order::where('status', 'delivered')
                ->orderBy('delivery_date', 'desc')
                ->limit(10)
                ->get(['id', 'order_no', 'status', 'delivery_date', 'created_at']);
            
            // 检查更正记录
            $correctionsCount = OrderCorrection::count();
            $pendingCorrections = OrderCorrection::where('status', 'pending')->count();
            
            // 有待处理更正的订单
            $ordersWithPendingCorrections = Order::whereHas('corrections', function($q) {
                $q->where('status', 'pending');
            })->count();
            
            return response()->json([
                'code' => 200,
                'message' => '调试信息',
                'data' => [
                    'total_orders' => $totalOrders,
                    'delivered_orders' => $deliveredOrders,
                    'status_stats' => $statusStats,
                    'recent_orders' => $recentOrders,
                    'delivered_orders_list' => $deliveredOrdersList,
                    'corrections_count' => $correctionsCount,
                    'pending_corrections' => $pendingCorrections,
                    'orders_with_pending_corrections' => $ordersWithPendingCorrections,
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '调试失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 查询微信退款状态
     */
    public function queryWechatRefundStatus(Request $request, int $correctionId)
    {
        try {
            $correction = OrderCorrection::findOrFail($correctionId);
            
            $refundStatus = $this->correctionService->queryWechatRefundStatus($correction);
            
            return response()->json([
                'code' => 0,
                'message' => '查询成功',
                'data' => [
                    'correction_id' => $correction->id,
                    'correction_no' => $correction->correction_no,
                    'refund_records' => $refundStatus
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('查询微信退款状态失败', [
                'correction_id' => $correctionId,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '查询失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 手动触发微信退款
     */
    public function triggerWechatRefund(Request $request, int $correctionId)
    {
        $validator = Validator::make($request->all(), [
            'refund_amount' => 'required|numeric|min:0.01',
            'refund_reason' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $correction = OrderCorrection::findOrFail($correctionId);
            $refundAmount = $request->input('refund_amount');
            $operatedBy = auth()->id() ?? 1; // 获取当前操作员ID
            
            // 调用退款处理
            $this->correctionService->processWechatRefund($correction, $refundAmount, $operatedBy);
            
            return response()->json([
                'code' => 0,
                'message' => '微信退款申请成功',
                'data' => [
                    'correction_id' => $correction->id,
                    'refund_amount' => $refundAmount
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('手动触发微信退款失败', [
                'correction_id' => $correctionId,
                'refund_amount' => $request->input('refund_amount'),
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '退款申请失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 