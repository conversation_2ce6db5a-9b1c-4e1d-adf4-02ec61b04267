<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wechat_service_provider', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('服务商名称');
            $table->string('mch_id')->comment('服务商商户号');
            $table->string('appid')->comment('服务商AppID');
            $table->text('api_key')->comment('API密钥(v2)');
            $table->text('api_v3_key')->nullable()->comment('API密钥(v3)');
            $table->text('cert_path')->nullable()->comment('证书路径');
            $table->text('key_path')->nullable()->comment('证书密钥路径');
            $table->string('notify_url')->nullable()->comment('支付回调地址');
            $table->string('refund_notify_url')->nullable()->comment('退款回调地址');
            $table->boolean('is_sandbox')->default(false)->comment('是否沙箱环境');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wechat_service_provider');
    }
};
