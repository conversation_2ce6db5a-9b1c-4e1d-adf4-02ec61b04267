<?php

namespace App\Delivery\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Delivery\Services\DeliveryRouteService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class DeliveryRouteController extends Controller
{
    /**
     * 配送路线服务
     *
     * @var DeliveryRouteService
     */
    protected $deliveryRouteService;

    /**
     * 构造函数
     *
     * @param DeliveryRouteService $deliveryRouteService
     */
    public function __construct(DeliveryRouteService $deliveryRouteService)
    {
        $this->deliveryRouteService = $deliveryRouteService;
    }

    /**
     * 获取配送路线列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $routes = $this->deliveryRouteService->getRoutes($request);
            return response()->json(ApiResponse::success($routes));
        } catch (\Exception $e) {
            Log::error('获取配送路线列表失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('获取配送路线列表失败', 500), 500);
        }
    }
    
    /**
     * 获取配送路线详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $route = $this->deliveryRouteService->getRoute($id);
            return response()->json(ApiResponse::success($route));
        } catch (\Exception $e) {
            Log::error('获取配送路线详情失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('获取配送路线详情失败', 404), 404);
        }
    }
    
    /**
     * 创建配送路线
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'coverage_area' => 'required|string',
                'starting_point' => 'required|string',
                'estimated_delivery_time' => 'required|numeric|min:1',
                'max_orders' => 'required|integer|min:1',
                'description' => 'nullable|string',
                'status' => 'required|in:active,inactive',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $route = $this->deliveryRouteService->createRoute($request->all());
            
            return response()->json(ApiResponse::success($route, '配送路线创建成功'), 201);
        } catch (\Exception $e) {
            Log::error('创建配送路线失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('创建配送路线失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 更新配送路线
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'nullable|string|max:255',
                'coverage_area' => 'nullable|string',
                'starting_point' => 'nullable|string',
                'estimated_delivery_time' => 'nullable|numeric|min:1',
                'max_orders' => 'nullable|integer|min:1',
                'description' => 'nullable|string',
                'status' => 'nullable|in:active,inactive',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $route = $this->deliveryRouteService->updateRoute($id, $request->all());
            
            return response()->json(ApiResponse::success($route, '配送路线更新成功'));
        } catch (\Exception $e) {
            Log::error('更新配送路线失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('更新配送路线失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 删除配送路线
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $this->deliveryRouteService->deleteRoute($id);
            
            return response()->json(ApiResponse::success(null, '配送路线删除成功'));
        } catch (\Exception $e) {
            Log::error('删除配送路线失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error($e->getMessage(), 400), 400);
        }
    }
    
    /**
     * 分配配送员到路线
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function assignDeliverers(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'deliverer_ids' => 'required|array',
                'deliverer_ids.*' => 'exists:employees,id',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $route = $this->deliveryRouteService->assignDeliverers($id, $request->deliverer_ids);
            
            return response()->json(ApiResponse::success($route, '配送员分配成功'));
        } catch (\Exception $e) {
            Log::error('分配配送员到路线失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('分配配送员到路线失败: ' . $e->getMessage(), 500), 500);
        }
    }
} 