# Upload模块

Upload模块负责处理文件上传功能，支持多种存储方式，目前包括本地存储和腾讯云COS存储。

## 目录结构

```
Upload/
├── Contracts/          # 接口定义
├── Controllers/        # 控制器
├── Facades/            # Facade
├── Providers/          # 服务提供者
├── Services/           # 服务实现
├── config/             # 配置文件
├── routes/             # 路由定义
└── README.md           # 说明文档
```

## 使用方法

### 配置

首先在`.env`文件中配置存储驱动：

```
# 上传驱动，可选值：local, cos
UPLOAD_DRIVER=local
```

对于腾讯云COS存储，还需要配置以下参数：

```
COS_APP_ID=
COS_SECRET_ID=
COS_SECRET_KEY=
COS_REGION=ap-beijing
COS_BUCKET=
COS_CDN=  # 可选，CDN域名
```

### 使用Facade

```php
use App\Upload\Facades\Upload;

// 上传图片
$result = Upload::uploadImage($request->file('image'), 'banner', [
    'prefix' => 'custom-prefix',
    'filename' => 'custom-filename', // 可选
]);

// 获取当前驱动
$driver = Upload::getDriver();

// 切换驱动
$localDriver = new \App\Upload\Services\LocalStorageDriver();
Upload::setDriver($localDriver);
```

### 直接使用服务类

```php
use App\Upload\Services\UploadService;
use App\Upload\Services\LocalStorageDriver;
use App\Upload\Services\CosStorageDriver;

// 通过依赖注入使用
public function uploadFile(UploadService $uploadService)
{
    $result = $uploadService->uploadBannerImage($request->file('image'));
}

// 手动创建实例
$localDriver = new LocalStorageDriver();
$uploadService = new UploadService($localDriver);
$result = $uploadService->uploadProductImage($request->file('image'));

// 使用静态工厂方法
$uploadService = UploadService::cos(); // 使用COS存储
$uploadService = UploadService::local(); // 使用本地存储
```

### 自定义存储驱动

实现`StorageDriverInterface`接口即可创建自定义存储驱动：

```php
use App\Upload\Contracts\StorageDriverInterface;

class CustomStorageDriver implements StorageDriverInterface
{
    // 实现接口方法
}
```

然后在`UploadServiceProvider`中注册：

```php
$this->app->bind(StorageDriverInterface::class, function ($app) {
    $driver = config('upload.default_driver', 'local');
    
    return match($driver) {
        'cos' => new CosStorageDriver(),
        'local' => new LocalStorageDriver(),
        'custom' => new CustomStorageDriver(),
        default => new LocalStorageDriver(),
    };
});
```

# 上传模块使用指南

本文档提供了在其他模块中使用上传模块的详细说明。

## 快速开始

### 1. 基本用法

```php
use App\Upload\Services\UploadService;
use Illuminate\Http\UploadedFile;

// 自动使用配置中的默认驱动(local或cos)
$uploadService = UploadService::createFromConfig();

// 上传图片 (适用于各种类型: banner, product, category等)
$uploadedFile = $request->file('image'); // 从请求中获取上传的文件
$result = $uploadService->uploadImage($uploadedFile, 'banner');

if ($result) {
    // 上传成功
    $url = $result['url']; // 文件URL
    $path = $result['path']; // 文件存储路径
    $driver = $result['driver']; // 使用的存储驱动(local或cos)
} else {
    // 上传失败
}
```

### 2. 指定存储驱动

您可以指定使用哪种存储驱动：

```php
// 使用腾讯云COS存储
$uploadService = UploadService::cos();

// 使用本地存储
$uploadService = UploadService::local();
```

### 3. 常用的上传方法

模块提供了几种预定义的上传方法：

```php
// 上传轮播图
$result = $uploadService->uploadBannerImage($uploadedFile);

// 上传商品图片
$result = $uploadService->uploadProductImage($uploadedFile);

// 上传分类图片
$result = $uploadService->uploadCategoryImage($uploadedFile);

// 上传用户头像
$result = $uploadService->uploadAvatarImage($uploadedFile);
```

### 4. 附加选项

您可以通过options参数传递附加选项：

```php
$options = [
    'filename' => '自定义文件名', // 自定义文件名(不含扩展名)
    'name_prefix' => 'prefix_', // 文件名前缀
    'subtype' => 'main', // 子类型(用于替换目录模板中的{type}占位符)
    'prefix' => 'custom/path', // 自定义存储路径前缀
];

$result = $uploadService->uploadImage($uploadedFile, 'product', $options);
```

## 上传配置

### 1. 数据库配置

上传模块使用数据库配置表`upload_configs`存储配置信息。主要配置包括：

- 基本配置(`upload_basic`组):
  - `upload_driver`: 存储驱动类型，支持`local`和`cos`
  - `image_max_size`: 图片最大尺寸(KB)
  - `allowed_file_types`: 允许的文件类型

- COS配置(`upload_cos`组):
  - `cos_app_id`: 腾讯云APP ID
  - `cos_secret_id`: 腾讯云Secret ID
  - `cos_secret_key`: 腾讯云Secret Key
  - `cos_region`: 存储区域，如`ap-chengdu`
  - `cos_bucket`: 存储桶名称(格式: bucket-appid)
  - `cos_cdn`: CDN加速域名(可选)

### 2. 配置修改

您可以通过`UploadConfig`模型修改配置：

```php
use App\Models\UploadConfig;

// 修改单个配置
UploadConfig::setValue('upload_driver', 'cos', 'upload_basic');

// 批量修改配置
UploadConfig::setValues([
    'cos_region' => 'ap-shanghai',
    'cos_bucket' => 'my-bucket-1234567890',
], 'upload_cos');

// 获取配置值
$driver = UploadConfig::getValue('upload_driver', 'local');

// 获取配置组
$cosConfig = UploadConfig::getGroupValues('upload_cos');
```

## 目录结构

默认目录结构在`app/Upload/config/upload.php`中定义：

```php
'directories' => [
    'banner' => 'banners/{date}',
    'product' => 'products/{date}',
    'category' => 'categories/{date}',
    'category_icon' => 'categories/icons/{date}',
    'avatar' => 'avatars/{date}',
    'article' => 'articles/{date}',
    'attachment' => 'attachments/{date}/{type}',
],
```

其中`{date}`会被替换为当前日期(格式: Ymd)，`{type}`会被替换为options中的`subtype`值。

## 自定义存储驱动

如果需要添加新的存储驱动，只需实现`StorageDriverInterface`接口：

```php
namespace App\Upload\Contracts;

use Illuminate\Http\UploadedFile;

interface StorageDriverInterface
{
    public function store(UploadedFile $file, string $directory, string $filename, array $options = []): ?array;
    public function getUrl(string $path): string;
    public function delete(string $path): bool;
    public function getDriverName(): string;
}
```

然后在`UploadService`中添加相应的工厂方法，并在`createFromConfig`方法中注册新驱动。

## 示例代码

### 商品模块中使用上传

```php
namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Upload\Services\UploadService;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    public function uploadProductImage(Request $request)
    {
        // 验证请求
        $request->validate([
            'image' => 'required|file|image|max:5120',
        ]);
        
        // 创建上传服务
        $uploadService = UploadService::createFromConfig();
        
        // 上传图片
        $result = $uploadService->uploadProductImage($request->file('image'));
        
        if (!$result) {
            return response()->json([
                'success' => false,
                'message' => '图片上传失败'
            ], 500);
        }
        
        return response()->json([
            'success' => true,
            'data' => $result
        ]);
    }
}
```

### 用户头像上传

```php
public function updateAvatar(Request $request)
{
    $request->validate([
        'avatar' => 'required|file|image|max:2048',
    ]);
    
    $uploadService = UploadService::createFromConfig();
    
    $options = [
        'name_prefix' => 'user_' . auth()->id() . '_'
    ];
    
    $result = $uploadService->uploadAvatarImage($request->file('avatar'), $options);
    
    if ($result) {
        $user = auth()->user();
        $user->avatar_url = $result['url'];
        $user->save();
        
        return response()->json([
            'success' => true,
            'data' => [
                'avatar_url' => $user->avatar_url
            ]
        ]);
    }
    
    return response()->json([
        'success' => false,
        'message' => '头像上传失败'
    ], 500);
}
``` 