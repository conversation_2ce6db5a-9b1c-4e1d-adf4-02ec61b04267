<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * 运行迁移
     * 优化配送记录表，合并配送员关联字段
     */
    public function up(): void
    {
        // 首先检查配送记录表是否存在
        if (Schema::hasTable('deliveries')) {
            // 检查是否同时存在两个字段
            if (Schema::hasColumn('deliveries', 'employee_deliverer_id') && 
                Schema::hasColumn('deliveries', 'deliverer_id')) {
                
                // 迁移数据，将employee_deliverer_id关联的员工找到对应的deliverer记录
                $this->migrateEmployeeDelivererToDeliverer();
                
                // 删除employee_deliverer_id字段及其外键
                Schema::table('deliveries', function (Blueprint $table) {
                    $table->dropForeign(['employee_deliverer_id']);
                    $table->dropColumn('employee_deliverer_id');
                });
                
                // 确保deliverer_id字段有正确的外键约束
                Schema::table('deliveries', function (Blueprint $table) {
                    // 检查是否已有外键约束
                    $constraints = DB::select("
                        SELECT CONSTRAINT_NAME 
                        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                        WHERE TABLE_NAME = 'deliveries' 
                        AND COLUMN_NAME = 'deliverer_id'
                        AND CONSTRAINT_NAME != 'PRIMARY' 
                    ");
                    
                    // 如果有外键约束，先删除
                    foreach ($constraints as $constraint) {
                        $table->dropForeign($constraint->CONSTRAINT_NAME);
                    }
                    
                    // 添加新的外键约束
                    $table->foreign('deliverer_id')
                          ->references('id')
                          ->on('deliverers')
                          ->onDelete('set null');
                });
            }
            
            // 为deliverer_id添加索引提高查询性能
            Schema::table('deliveries', function (Blueprint $table) {
                if (!Schema::hasIndex('deliveries', 'deliveries_deliverer_id_index')) {
                    $table->index('deliverer_id');
                }
            });
        }
    }
    
    /**
     * 迁移员工配送员ID到配送员ID
     * 将使用employee_deliverer_id的记录更新为使用deliverer_id
     */
    private function migrateEmployeeDelivererToDeliverer()
    {
        // 获取所有设置了employee_deliverer_id但没有设置deliverer_id的配送记录
        $deliveries = DB::table('deliveries')
                        ->whereNotNull('employee_deliverer_id')
                        ->whereNull('deliverer_id')
                        ->get();
        
        foreach ($deliveries as $delivery) {
            // 查找对应的员工配送员记录
            $deliverer = DB::table('deliverers')
                           ->where('employee_id', $delivery->employee_deliverer_id)
                           ->where('type', 'employee')
                           ->first();
            
            // 如果找到对应的配送员记录，则更新配送记录
            if ($deliverer) {
                DB::table('deliveries')
                    ->where('id', $delivery->id)
                    ->update([
                        'deliverer_id' => $deliverer->id,
                        'updated_at' => now()
                    ]);
            } else {
                // 如果没有找到对应的配送员记录，则为此员工创建一个配送员记录
                $employee = DB::table('employees')->find($delivery->employee_deliverer_id);
                if ($employee) {
                    // 检查员工是否关联到用户
                    $userId = $employee->user_id;
                    
                    $delivererId = DB::table('deliverers')->insertGetId([
                        'employee_id' => $employee->id,
                        'user_id' => $userId, // 设置user_id为员工关联的用户ID，可能为NULL
                        'type' => 'employee',
                        'status' => 'available',
                        'max_orders' => 10,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                    
                    // 更新配送记录使用新创建的配送员ID
                    DB::table('deliveries')
                        ->where('id', $delivery->id)
                        ->update([
                            'deliverer_id' => $delivererId,
                            'updated_at' => now()
                        ]);
                }
            }
        }
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        if (Schema::hasTable('deliveries')) {
            // 添加回employee_deliverer_id字段
            Schema::table('deliveries', function (Blueprint $table) {
                if (!Schema::hasColumn('deliveries', 'employee_deliverer_id')) {
                    $table->unsignedBigInteger('employee_deliverer_id')->nullable()->after('deliverer_id');
                    
                    // 添加外键约束
                    $table->foreign('employee_deliverer_id')
                          ->references('id')
                          ->on('employees')
                          ->onDelete('set null');
                }
            });
            
            // 回滚数据 - 将deliverer_id关联的员工配送员信息迁回employee_deliverer_id
            $deliveries = DB::table('deliveries')
                            ->whereNotNull('deliverer_id')
                            ->get();
            
            foreach ($deliveries as $delivery) {
                $deliverer = DB::table('deliverers')->find($delivery->deliverer_id);
                if ($deliverer && $deliverer->employee_id) {
                    DB::table('deliveries')
                        ->where('id', $delivery->id)
                        ->update([
                            'employee_deliverer_id' => $deliverer->employee_id,
                            'updated_at' => now()
                        ]);
                }
            }
        }
    }
};
