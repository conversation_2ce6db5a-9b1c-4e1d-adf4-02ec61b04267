<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Crm\Models\CustomerBehaviorAnalytics;
use App\Crm\Models\UserSession;
use App\Crm\Models\BehaviorStatistics;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Str;

class BehaviorAnalyticsSeeder extends Seeder
{
    /**
     * 运行数据库填充
     */
    public function run(): void
    {
        $this->command->info('开始生成用户行为分析模拟数据...');
        
        // 清空现有数据
        $this->clearExistingData();
        
        // 获取所有用户（users表是客户表，不需要role过滤）
        $users = User::limit(50)->get();
        
        if ($users->isEmpty()) {
            $this->command->warn('没有找到用户，请先运行用户数据填充');
            return;
        }
        
        $this->command->info("找到 {$users->count()} 个用户");
        
        // 生成最近90天的数据
        $startDate = Carbon::now()->subDays(90);
        $endDate = Carbon::now();
        
        $totalDays = $startDate->diffInDays($endDate);
        $this->command->info("生成 {$totalDays} 天的行为数据");
        
        // 为每个用户生成行为数据
        foreach ($users as $user) {
            $this->generateUserBehaviorData($user, $startDate, $endDate);
        }
        
        $this->command->info('用户行为分析模拟数据生成完成！');
    }
    
    /**
     * 清空现有数据
     */
    private function clearExistingData(): void
    {
        $this->command->info('清空现有行为分析数据...');
        
        DB::table('cart_analytics')->truncate();
        DB::table('search_analytics')->truncate();
        DB::table('product_view_analytics')->truncate();
        DB::table('behavior_statistics')->truncate();
        DB::table('customer_behavior_analytics')->truncate();
        DB::table('user_sessions')->truncate();
    }
    
    /**
     * 为单个用户生成行为数据
     */
    private function generateUserBehaviorData(User $user, Carbon $startDate, Carbon $endDate): void
    {
        $currentDate = $startDate->copy();
        
        while ($currentDate->lte($endDate)) {
            // 随机决定用户是否在这一天活跃（70%概率）
            if (rand(1, 100) <= 70) {
                $this->generateDayBehaviorData($user, $currentDate);
            }
            
            $currentDate->addDay();
        }
    }
    
    /**
     * 生成单天的行为数据
     */
    private function generateDayBehaviorData(User $user, Carbon $date): void
    {
        // 随机生成1-3个会话
        $sessionCount = rand(1, 3);
        
        $dayStats = [
            'page_views' => 0,
            'product_views' => 0,
            'cart_operations' => 0,
            'search_count' => 0,
            'order_count' => 0,
            'session_count' => $sessionCount,
            'total_duration' => 0,
            'total_amount' => 0,
        ];
        
        for ($i = 0; $i < $sessionCount; $i++) {
            $sessionStats = $this->generateSessionData($user, $date, $i);
            
            // 累加统计数据
            $dayStats['page_views'] += $sessionStats['page_views'];
            $dayStats['product_views'] += $sessionStats['product_views'];
            $dayStats['cart_operations'] += $sessionStats['cart_operations'];
            $dayStats['search_count'] += $sessionStats['search_count'];
            $dayStats['total_duration'] += $sessionStats['duration'];
        }
        
        // 保存日统计数据
        BehaviorStatistics::create([
            'user_id' => $user->id,
            'stat_date' => $date->format('Y-m-d'),
            ...$dayStats
        ]);
    }
    
    /**
     * 生成单个会话的数据
     */
    private function generateSessionData(User $user, Carbon $date, int $sessionIndex): array
    {
        // 生成会话时间（模拟不同时段的活跃度）
        $hour = $this->getRandomActiveHour();
        $minute = rand(0, 59);
        $sessionStart = $date->copy()->setTime($hour, $minute);
        
        // 会话时长：5分钟到2小时
        $durationMinutes = rand(5, 120);
        $sessionEnd = $sessionStart->copy()->addMinutes($durationMinutes);
        
        // 生成会话ID
        $sessionId = Str::uuid()->toString();
        
        // 创建会话记录
        $session = UserSession::create([
            'session_id' => $sessionId,
            'user_id' => $user->id,
            'start_time' => $sessionStart,
            'end_time' => $sessionEnd,
            'duration' => $durationMinutes * 60,
            'device_info' => $this->generateDeviceInfo(),
            'ip_address' => $this->generateRandomIP(),
            'referrer' => $this->getRandomReferrer(),
        ]);
        
        // 生成会话内的行为事件
        return $this->generateSessionEvents($user, $session, $sessionStart, $sessionEnd);
    }
    
    /**
     * 生成会话内的行为事件
     */
    private function generateSessionEvents(User $user, UserSession $session, Carbon $start, Carbon $end): array
    {
        $stats = [
            'page_views' => 0,
            'product_views' => 0,
            'cart_operations' => 0,
            'search_count' => 0,
            'duration' => $session->duration,
        ];
        
        $currentTime = $start->copy();
        $eventCount = 0;
        
        // 首页访问（会话开始）
        $this->createPageViewEvent($user, $session, $currentTime, 'home');
        $stats['page_views']++;
        $eventCount++;
        
        // 随机生成5-20个事件
        $totalEvents = rand(5, 20);
        
        for ($i = 1; $i < $totalEvents && $currentTime->lt($end); $i++) {
            // 随机推进时间
            $currentTime->addSeconds(rand(10, 300));
            
            if ($currentTime->gte($end)) break;
            
            // 随机选择事件类型
            $eventType = $this->getRandomEventType();
            
            switch ($eventType) {
                case 'page_view':
                    $this->createPageViewEvent($user, $session, $currentTime);
                    $stats['page_views']++;
                    break;
                    
                case 'product_view':
                    $this->createProductViewEvent($user, $session, $currentTime);
                    $stats['product_views']++;
                    break;
                    
                case 'search':
                    $this->createSearchEvent($user, $session, $currentTime);
                    $stats['search_count']++;
                    break;
                    
                case 'cart_operation':
                    $this->createCartOperationEvent($user, $session, $currentTime);
                    $stats['cart_operations']++;
                    break;
            }
            
            $eventCount++;
        }
        
        // 更新会话统计
        $session->update([
            'event_count' => $eventCount,
            'page_count' => $stats['page_views'],
        ]);
        
        return $stats;
    }
    
    /**
     * 创建页面浏览事件
     */
    private function createPageViewEvent(User $user, UserSession $session, Carbon $time, ?string $pageName = null): void
    {
        $pages = ['home', 'category', 'product_list', 'product_detail', 'cart', 'profile', 'search'];
        $pageName = $pageName ?? $pages[array_rand($pages)];
        
        CustomerBehaviorAnalytics::create([
            'user_id' => $user->id,
            'session_id' => $session->session_id,
            'event_type' => CustomerBehaviorAnalytics::EVENT_PAGE_VIEW,
            'event_data' => [
                'page_name' => $pageName,
                'enter_time' => $time->timestamp * 1000,
                'source' => $this->getRandomSource(),
                'referrer' => $this->getRandomReferrer(),
            ],
            'device_info' => $this->generateDeviceInfo(),
            'ip_address' => $session->ip_address,
            'user_agent' => $this->generateUserAgent(),
            'created_at' => $time,
        ]);
    }
    
    /**
     * 创建商品浏览事件
     */
    private function createProductViewEvent(User $user, UserSession $session, Carbon $time): void
    {
        CustomerBehaviorAnalytics::create([
            'user_id' => $user->id,
            'session_id' => $session->session_id,
            'event_type' => CustomerBehaviorAnalytics::EVENT_PRODUCT_VIEW,
            'event_data' => [
                'product_id' => rand(1, 100),
                'category_id' => rand(1, 10),
                'source' => $this->getRandomSource(),
                'view_duration' => rand(10, 300),
                'scroll_depth' => rand(20, 100),
                'image_views' => rand(1, 5),
            ],
            'device_info' => $this->generateDeviceInfo(),
            'ip_address' => $session->ip_address,
            'user_agent' => $this->generateUserAgent(),
            'created_at' => $time,
        ]);
    }
    
    /**
     * 创建搜索事件
     */
    private function createSearchEvent(User $user, UserSession $session, Carbon $time): void
    {
        $keywords = ['苹果', '香蕉', '蔬菜', '肉类', '海鲜', '牛奶', '面包', '鸡蛋', '土豆', '西红柿'];
        
        CustomerBehaviorAnalytics::create([
            'user_id' => $user->id,
            'session_id' => $session->session_id,
            'event_type' => CustomerBehaviorAnalytics::EVENT_SEARCH,
            'event_data' => [
                'keyword' => $keywords[array_rand($keywords)],
                'filters' => [
                    'category' => rand(1, 10),
                    'price_range' => rand(1, 5) * 10 . '-' . rand(5, 10) * 10,
                    'sort' => ['price_asc', 'price_desc', 'sales_desc'][array_rand(['price_asc', 'price_desc', 'sales_desc'])],
                ],
                'result_count' => rand(5, 50),
                'click_position' => rand(1, 10),
            ],
            'device_info' => $this->generateDeviceInfo(),
            'ip_address' => $session->ip_address,
            'user_agent' => $this->generateUserAgent(),
            'created_at' => $time,
        ]);
    }
    
    /**
     * 创建购物车操作事件
     */
    private function createCartOperationEvent(User $user, UserSession $session, Carbon $time): void
    {
        $operations = ['add', 'remove', 'update'];
        $operation = $operations[array_rand($operations)];
        
        CustomerBehaviorAnalytics::create([
            'user_id' => $user->id,
            'session_id' => $session->session_id,
            'event_type' => CustomerBehaviorAnalytics::EVENT_CART_OPERATION,
            'event_data' => [
                'operation' => $operation,
                'product_id' => rand(1, 100),
                'quantity' => rand(1, 5),
                'price' => rand(10, 100) + rand(0, 99) / 100,
                'total_cart_value' => rand(50, 500) + rand(0, 99) / 100,
            ],
            'device_info' => $this->generateDeviceInfo(),
            'ip_address' => $session->ip_address,
            'user_agent' => $this->generateUserAgent(),
            'created_at' => $time,
        ]);
    }
    
    /**
     * 获取随机活跃时段
     */
    private function getRandomActiveHour(): int
    {
        // 模拟真实的用户活跃时段分布
        $activeHours = [
            8 => 5,   // 早上8点，权重5
            9 => 8,   // 早上9点，权重8
            10 => 6,
            11 => 7,
            12 => 10, // 中午12点，权重10（高峰）
            13 => 8,
            14 => 6,
            15 => 5,
            16 => 4,
            17 => 6,
            18 => 8,
            19 => 12, // 晚上7点，权重12（高峰）
            20 => 15, // 晚上8点，权重15（最高峰）
            21 => 12, // 晚上9点，权重12（高峰）
            22 => 8,
            23 => 4,
        ];
        
        // 根据权重随机选择
        $weightedHours = [];
        foreach ($activeHours as $hour => $weight) {
            for ($i = 0; $i < $weight; $i++) {
                $weightedHours[] = $hour;
            }
        }
        
        return $weightedHours[array_rand($weightedHours)];
    }
    
    /**
     * 获取随机事件类型
     */
    private function getRandomEventType(): string
    {
        $eventTypes = [
            'page_view' => 30,      // 30%
            'product_view' => 40,   // 40%
            'search' => 15,         // 15%
            'cart_operation' => 15, // 15%
        ];
        
        $rand = rand(1, 100);
        $cumulative = 0;
        
        foreach ($eventTypes as $type => $probability) {
            $cumulative += $probability;
            if ($rand <= $cumulative) {
                return $type;
            }
        }
        
        return 'page_view';
    }
    
    /**
     * 生成设备信息
     */
    private function generateDeviceInfo(): array
    {
        $platforms = ['android', 'ios'];
        $platform = $platforms[array_rand($platforms)];
        
        return [
            'platform' => $platform,
            'system' => $platform === 'android' ? 'Android ' . rand(8, 13) : 'iOS ' . rand(13, 16),
            'model' => $platform === 'android' ? 
                ['Huawei P40', 'Xiaomi Mi 11', 'Samsung Galaxy S21', 'OnePlus 9'][array_rand(['Huawei P40', 'Xiaomi Mi 11', 'Samsung Galaxy S21', 'OnePlus 9'])] :
                ['iPhone 12', 'iPhone 13', 'iPhone 14', 'iPhone 15'][array_rand(['iPhone 12', 'iPhone 13', 'iPhone 14', 'iPhone 15'])],
            'screen_width' => rand(360, 414),
            'screen_height' => rand(640, 896),
            'network_type' => ['wifi', '4g', '5g'][array_rand(['wifi', '4g', '5g'])],
        ];
    }
    
    /**
     * 生成随机IP地址
     */
    private function generateRandomIP(): string
    {
        return rand(192, 223) . '.' . rand(168, 255) . '.' . rand(1, 255) . '.' . rand(1, 254);
    }
    
    /**
     * 获取随机来源
     */
    private function getRandomSource(): string
    {
        return ['direct', 'search', 'recommendation', 'category', 'banner'][array_rand(['direct', 'search', 'recommendation', 'category', 'banner'])];
    }
    
    /**
     * 获取随机引荐页面
     */
    private function getRandomReferrer(): string
    {
        return ['home', 'category', 'search', 'recommendation', 'external'][array_rand(['home', 'category', 'search', 'recommendation', 'external'])];
    }
    
    /**
     * 生成用户代理字符串
     */
    private function generateUserAgent(): string
    {
        $userAgents = [
            'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Linux; Android 10; Pixel 4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
        ];
        
        return $userAgents[array_rand($userAgents)];
    }
} 