<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('deliveries')) {
            // 首先检查deliverer_id是否存在
            if (Schema::hasColumn('deliveries', 'deliverer_id')) {
                // 1. 删除现有外键约束
                Schema::table('deliveries', function (Blueprint $table) {
                    $constraints = DB::select("
                        SELECT CONSTRAINT_NAME 
                        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                        WHERE TABLE_NAME = 'deliveries' 
                        AND COLUMN_NAME = 'deliverer_id'
                        AND CONSTRAINT_NAME != 'PRIMARY' 
                    ");
                    
                    foreach ($constraints as $constraint) {
                        $table->dropForeign($constraint->CONSTRAINT_NAME);
                    }
                });
                
                // 2. 添加临时字段
                Schema::table('deliveries', function (Blueprint $table) {
                    $table->unsignedBigInteger('new_deliverer_id')->nullable();
                });
                
                // 3. 添加employee_deliverer_id字段
                Schema::table('deliveries', function (Blueprint $table) {
                    // 重命名原字段为employee_deliverer_id
                    $table->renameColumn('deliverer_id', 'employee_deliverer_id');
                });
                
                // 4. 重命名新字段为deliverer_id
                Schema::table('deliveries', function (Blueprint $table) {
                    $table->renameColumn('new_deliverer_id', 'deliverer_id');
                });
                
                // 5. 添加外键约束
                Schema::table('deliveries', function (Blueprint $table) {
                    // 为employee_deliverer_id添加外键约束
                    $table->foreign('employee_deliverer_id')
                        ->references('id')
                        ->on('employees')
                        ->onDelete('set null');
                        
                    // 为deliverer_id添加外键约束
                    $table->foreign('deliverer_id')
                        ->references('id')
                        ->on('deliverers')
                        ->onDelete('set null');
                });
            } else {
                // 如果deliverer_id不存在，直接添加两个字段
                Schema::table('deliveries', function (Blueprint $table) {
                    $table->unsignedBigInteger('employee_deliverer_id')->nullable();
                    $table->unsignedBigInteger('deliverer_id')->nullable();
                    
                    // 添加外键约束
                    $table->foreign('employee_deliverer_id')
                        ->references('id')
                        ->on('employees')
                        ->onDelete('set null');
                        
                    $table->foreign('deliverer_id')
                        ->references('id')
                        ->on('deliverers')
                        ->onDelete('set null');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('deliveries')) {
            // 移除外键约束
            Schema::table('deliveries', function (Blueprint $table) {
                if (Schema::hasColumn('deliveries', 'deliverer_id')) {
                    $table->dropForeign(['deliverer_id']);
                }
                
                if (Schema::hasColumn('deliveries', 'employee_deliverer_id')) {
                    $table->dropForeign(['employee_deliverer_id']);
                }
            });
            
            // 如果有两个字段，则合并回一个
            if (Schema::hasColumn('deliveries', 'employee_deliverer_id') && 
                Schema::hasColumn('deliveries', 'deliverer_id')) {
                
                // 删除deliverer_id字段
                Schema::table('deliveries', function (Blueprint $table) {
                    $table->dropColumn('deliverer_id');
                });
                
                // 重命名employee_deliverer_id为deliverer_id
                Schema::table('deliveries', function (Blueprint $table) {
                    $table->renameColumn('employee_deliverer_id', 'deliverer_id');
                });
                
                // 添加外键约束
                Schema::table('deliveries', function (Blueprint $table) {
                    $table->foreign('deliverer_id')
                        ->references('id')
                        ->on('employees')
                        ->onDelete('set null');
                });
            } else {
                // 如果只有一个字段，直接删除
                Schema::table('deliveries', function (Blueprint $table) {
                    if (Schema::hasColumn('deliveries', 'employee_deliverer_id')) {
                        $table->dropColumn('employee_deliverer_id');
                    }
                    
                    if (Schema::hasColumn('deliveries', 'deliverer_id')) {
                        $table->dropColumn('deliverer_id');
                    }
                });
            }
        }
    }
};
