<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 默认库存策略
    |--------------------------------------------------------------------------
    |
    | 当商品和仓库的库存策略都设置为 'inherit' 时使用的默认策略
    |
    | 支持的策略:
    | - strict: 严格库存控制，不允许负库存
    | - allow_negative: 允许负库存出库
    | - unlimited: 无限库存，不检查库存量
    |
    */
    'default_policy' => env('INVENTORY_DEFAULT_POLICY', 'strict'),

    /*
    |--------------------------------------------------------------------------
    | 库存策略配置
    |--------------------------------------------------------------------------
    */
    'policies' => [
        'strict' => [
            'name' => '严格库存控制',
            'description' => '不允许超卖，库存不足时阻止销售',
            'allow_negative' => false,
            'check_stock' => true,
        ],
        'allow_negative' => [
            'name' => '允许负库存',
            'description' => '允许超卖，库存可以为负数',
            'allow_negative' => true,
            'check_stock' => true,
        ],
        'unlimited' => [
            'name' => '无限库存',
            'description' => '不检查库存，视为无限供应',
            'allow_negative' => true,
            'check_stock' => false,
        ],
        'inherit' => [
            'name' => '继承策略',
            'description' => '继承仓库或系统的库存策略设置',
            'inherit' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 库存预警配置
    |--------------------------------------------------------------------------
    */
    'alerts' => [
        'enabled' => env('INVENTORY_ALERTS_ENABLED', true),
        'low_stock_enabled' => env('INVENTORY_LOW_STOCK_ALERTS', true),
        'negative_stock_enabled' => env('INVENTORY_NEGATIVE_STOCK_ALERTS', true),
        'default_threshold' => env('INVENTORY_DEFAULT_THRESHOLD', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | 自动补货配置
    |--------------------------------------------------------------------------
    */
    'auto_reorder' => [
        'enabled' => env('INVENTORY_AUTO_REORDER_ENABLED', false),
        'default_reorder_point' => env('INVENTORY_DEFAULT_REORDER_POINT', 5),
        'default_reorder_quantity' => env('INVENTORY_DEFAULT_REORDER_QUANTITY', 50),
    ],
]; 