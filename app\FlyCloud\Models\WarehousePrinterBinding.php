<?php

namespace App\FlyCloud\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
use App\Warehouse\Models\Warehouse;

class WarehousePrinterBinding extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'warehouse_printer_bindings';

    protected $fillable = [
        'warehouse_id',
        'flycloud_printer_id',
        'print_type',
        'is_active',
        'is_default',
        'priority',
        'settings',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'priority' => 'integer',
        'settings' => 'array'
    ];

    /**
     * 打印类型常量
     */
    const PRINT_TYPE_ORDER = 'order';        // 订单打印
    const PRINT_TYPE_PICKING = 'picking';    // 拣货单打印
    const PRINT_TYPE_DELIVERY = 'delivery';  // 配送单打印

    /**
     * 获取所有打印类型
     */
    public static function getPrintTypes(): array
    {
        return [
            self::PRINT_TYPE_ORDER => '订单小票',
            self::PRINT_TYPE_PICKING => '拣货单',
            self::PRINT_TYPE_DELIVERY => '配送单'
        ];
    }

    /**
     * 关联仓库
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * 关联飞蛾云打印机
     */
    public function flyCloudPrinter()
    {
        return $this->belongsTo(FlyCloudPrinter::class);
    }

    /**
     * 关联创建用户
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 关联更新用户
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * 查询活跃的绑定关系
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 查询默认绑定关系
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * 按打印类型筛选
     */
    public function scopeByPrintType($query, string $printType)
    {
        return $query->where('print_type', $printType);
    }

    /**
     * 按仓库筛选
     */
    public function scopeByWarehouse($query, int $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    /**
     * 按优先级排序
     */
    public function scopeOrderByPriority($query)
    {
        return $query->orderBy('priority', 'desc')->orderBy('id', 'asc');
    }

    /**
     * 获取仓库指定类型的默认打印机
     */
    public static function getWarehouseDefaultPrinter(int $warehouseId, string $printType = self::PRINT_TYPE_ORDER): ?FlyCloudPrinter
    {
        $binding = static::active()
            ->byWarehouse($warehouseId)
            ->byPrintType($printType)
            ->default()
            ->with('flyCloudPrinter')
            ->first();

        return $binding ? $binding->flyCloudPrinter : null;
    }

    /**
     * 获取仓库指定类型的可用打印机列表
     */
    public static function getWarehouseAvailablePrinters(int $warehouseId, string $printType = self::PRINT_TYPE_ORDER): array
    {
        return static::active()
            ->byWarehouse($warehouseId)
            ->byPrintType($printType)
            ->orderByPriority()
            ->with('flyCloudPrinter')
            ->get()
            ->map(function ($binding) {
                return $binding->flyCloudPrinter;
            })
            ->filter()
            ->values()
            ->toArray();
    }

    /**
     * 设置为仓库默认打印机
     */
    public function setAsWarehouseDefault(): bool
    {
        // 清除同仓库同类型的其他默认设置
        static::where('warehouse_id', $this->warehouse_id)
            ->where('print_type', $this->print_type)
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);

        // 设置当前绑定为默认
        return $this->update(['is_default' => true]);
    }

    /**
     * 绑定仓库和打印机
     */
    public static function bindWarehousePrinter(
        int $warehouseId,
        int $flyCloudPrinterId,
        string $printType = self::PRINT_TYPE_ORDER,
        array $options = []
    ): self {
        $binding = static::create([
            'warehouse_id' => $warehouseId,
            'flycloud_printer_id' => $flyCloudPrinterId,
            'print_type' => $printType,
            'is_active' => $options['is_active'] ?? true,
            'is_default' => $options['is_default'] ?? false,
            'priority' => $options['priority'] ?? 0,
            'settings' => $options['settings'] ?? [],
            'created_by' => $options['created_by'] ?? null
        ]);

        // 如果设置为默认，更新其他绑定
        if ($options['is_default'] ?? false) {
            $binding->setAsWarehouseDefault();
        }

        return $binding;
    }

    /**
     * 批量获取多个仓库的默认打印机
     */
    public static function getMultiWarehouseDefaultPrinters(array $warehouseIds, string $printType = self::PRINT_TYPE_ORDER): array
    {
        $bindings = static::active()
            ->whereIn('warehouse_id', $warehouseIds)
            ->byPrintType($printType)
            ->default()
            ->with(['warehouse', 'flyCloudPrinter'])
            ->get();

        $result = [];
        foreach ($bindings as $binding) {
            $result[$binding->warehouse_id] = $binding->flyCloudPrinter;
        }

        return $result;
    }

    /**
     * 验证打印机是否可用
     */
    public function isPrinterAvailable(): bool
    {
        return $this->is_active && 
               $this->flyCloudPrinter && 
               $this->flyCloudPrinter->is_active;
    }

    /**
     * 获取打印设置
     */
    public function getPrintSettings(): array
    {
        return array_merge([
            'copies' => 1,
            'auto_print' => false,
            'print_delay' => 0, // 延迟打印（秒）
        ], $this->settings ?? []);
    }
} 