# 统一加载状态管理器使用指南

## 概述

统一加载状态管理器是为了解决小程序中多个加载状态管理混乱、容易卡住等问题而设计的解决方案。它提供了：

- **统一管理**：所有加载状态通过一个管理器统一控制
- **防卡死机制**：自动检测和恢复卡住的加载状态
- **心跳监控**：定期检查加载状态健康度
- **智能计数**：避免多个加载状态相互干扰
- **调试支持**：提供详细的调试信息和监控页面

## 核心特性

### 1. 防卡死机制
- **超时保护**：每个加载状态都有最大持续时间限制（默认15秒）
- **紧急恢复**：10秒后自动触发紧急恢复机制
- **心跳检测**：每2秒检查一次加载状态健康度
- **自动清理**：自动清理卡住的加载状态

### 2. 智能管理
- **全局计数**：只有第一个加载状态显示UI，避免重复显示
- **静默模式**：支持静默加载，不显示微信加载提示
- **唯一标识**：每个加载状态都有唯一的key，便于管理
- **状态追踪**：记录每个加载状态的开始时间、配置等信息

## 使用方法

### 1. 基础用法

```javascript
// 引入管理器
const { showLoading, hideLoading } = require('../../utils/loading-manager');

// 显示加载状态
const loadingKey = showLoading('my_operation', {
  title: '处理中...',
  mask: true,
  duration: 10000
});

// 隐藏加载状态
hideLoading(loadingKey);
```

### 2. 页面混入用法

```javascript
// 在页面的onLoad中初始化
onLoad() {
  // 初始化统一加载管理器
  const { loadingMixin } = require('../../utils/loading-manager');
  Object.assign(this, loadingMixin);
},

// 使用页面加载方法
async loadData() {
  // 显示页面加载（使用页面自己的UI）
  this.showPageLoading('加载数据中...');
  
  try {
    // 执行数据加载
    await this.fetchData();
  } finally {
    // 隐藏页面加载
    this.hidePageLoading();
  }
}

// 使用操作加载方法
async submitForm() {
  // 显示操作加载（使用微信加载提示）
  const loadingKey = this.showActionLoading('提交中...');
  
  try {
    // 执行提交操作
    await this.submit();
  } finally {
    // 隐藏操作加载
    this.hideActionLoading(loadingKey);
  }
}
```

### 3. 请求工具集成

请求工具已自动集成了统一加载管理器，无需手动处理：

```javascript
// 自动显示和隐藏加载状态
const result = await api.getData({
  showLoading: true,  // 默认为true
  loadingText: '加载中...'  // 自定义加载文字
});

// 静默请求（不显示加载状态）
const result = await api.getData({
  showLoading: false
});
```

## API参考

### showLoading(key, options)

显示加载状态

**参数：**
- `key` (string): 加载状态的唯一标识，默认为 'default'
- `options` (object): 配置选项
  - `title` (string): 加载提示文字，默认为 '加载中...'
  - `mask` (boolean): 是否显示遮罩，默认为 true
  - `duration` (number): 最大持续时间，默认为 15000ms
  - `emergency` (boolean): 是否启用紧急恢复，默认为 true
  - `silent` (boolean): 是否静默显示，默认为 false

**返回值：**
- `string`: 加载状态的key，用于后续隐藏

### hideLoading(key)

隐藏加载状态

**参数：**
- `key` (string): 要隐藏的加载状态key

### isLoading(key)

检查加载状态是否存在

**参数：**
- `key` (string): 要检查的加载状态key

**返回值：**
- `boolean`: 是否正在加载

### hideAllLoading()

强制隐藏所有加载状态

### resetLoading()

重置加载管理器（紧急情况使用）

## 页面混入方法

### showPageLoading(text, options)

显示页面加载状态（静默模式，使用页面自己的UI）

### hidePageLoading()

隐藏页面加载状态

### showActionLoading(action, options)

显示操作加载状态（显示微信加载提示）

### hideActionLoading(key)

隐藏操作加载状态

## 配置选项

```javascript
const config = {
  maxDuration: 15000,        // 最大加载时间15秒
  defaultText: '加载中...',   // 默认加载文字
  emergencyTimeout: 10000,   // 紧急恢复时间10秒
  heartbeatInterval: 2000,   // 心跳检测间隔2秒
};
```

## 调试功能

### 调试页面

访问 `/pages/loading-debug/index` 页面可以：

- 查看当前所有活跃的加载状态
- 监控加载状态的持续时间
- 强制隐藏卡住的加载状态
- 测试加载管理器功能
- 查看配置信息和统计数据

### 调试方法

```javascript
const { loadingManager } = require('../../utils/loading-manager');

// 获取统计信息
const stats = loadingManager.getStats();
console.log('加载状态统计:', stats);

// 获取活跃加载列表
const activeLoadings = loadingManager.getActiveLoadings();
console.log('活跃加载:', activeLoadings);
```

## 最佳实践

### 1. 使用唯一的key

```javascript
// 好的做法：使用描述性的唯一key
const loadingKey = showLoading('user_profile_update', {
  title: '更新用户信息...'
});

// 避免：使用通用的key
const loadingKey = showLoading('loading', {
  title: '处理中...'
});
```

### 2. 及时清理加载状态

```javascript
// 好的做法：使用try-finally确保清理
const loadingKey = showLoading('submit_form');
try {
  await submitForm();
} finally {
  hideLoading(loadingKey);
}

// 避免：忘记清理加载状态
const loadingKey = showLoading('submit_form');
await submitForm();
// 忘记调用 hideLoading(loadingKey);
```

### 3. 合理设置超时时间

```javascript
// 快速操作：较短的超时时间
const loadingKey = showLoading('quick_action', {
  duration: 5000
});

// 复杂操作：较长的超时时间
const loadingKey = showLoading('complex_operation', {
  duration: 30000
});
```

### 4. 使用页面混入简化代码

```javascript
// 在页面初始化时混入
onLoad() {
  const { loadingMixin } = require('../../utils/loading-manager');
  Object.assign(this, loadingMixin);
},

// 简化的使用方式
async loadData() {
  this.showPageLoading('加载数据...');
  try {
    await this.fetchData();
  } finally {
    this.hidePageLoading();
  }
}
```

## 故障排除

### 1. 加载状态卡住

- **现象**：加载提示一直显示，无法消失
- **原因**：代码异常导致hideLoading未被调用
- **解决**：
  1. 检查调试页面 `/pages/loading-debug/index`
  2. 使用"隐藏所有"按钮强制清理
  3. 检查代码中的异常处理

### 2. 多个加载状态冲突

- **现象**：多个加载提示同时显示
- **原因**：使用了相同的key或未正确管理
- **解决**：
  1. 确保每个加载状态使用唯一的key
  2. 检查是否正确调用了hideLoading

### 3. 加载状态不显示

- **现象**：调用showLoading但没有显示加载提示
- **原因**：可能是静默模式或配置问题
- **解决**：
  1. 检查options.silent是否为true
  2. 检查是否有其他加载状态正在显示
  3. 查看控制台日志

## 迁移指南

### 从wx.showLoading迁移

```javascript
// 旧代码
wx.showLoading({ title: '加载中...' });
try {
  await doSomething();
} finally {
  wx.hideLoading();
}

// 新代码
const { showLoading, hideLoading } = require('../../utils/loading-manager');
const loadingKey = showLoading('operation', { title: '加载中...' });
try {
  await doSomething();
} finally {
  hideLoading(loadingKey);
}
```

### 从自定义loading迁移

```javascript
// 旧代码
this.setData({ loading: true });
try {
  await doSomething();
} finally {
  this.setData({ loading: false });
}

// 新代码（使用页面混入）
const { loadingMixin } = require('../../utils/loading-manager');
Object.assign(this, loadingMixin);

this.showPageLoading('处理中...');
try {
  await doSomething();
} finally {
  this.hidePageLoading();
}
```

## 注意事项

1. **避免嵌套使用相同key**：同一个key在同一时间只能有一个活跃状态
2. **及时清理**：始终在finally块中清理加载状态
3. **合理设置超时**：根据操作复杂度设置合适的超时时间
4. **使用描述性key**：便于调试和维护
5. **监控调试页面**：定期检查是否有卡住的加载状态

## 版本信息

- **当前版本**：1.0.0
- **兼容性**：微信小程序基础库 2.0.0+
- **依赖**：无外部依赖

## 支持

如果遇到问题或有改进建议，请：

1. 查看调试页面获取详细信息
2. 检查控制台日志
3. 参考本文档的故障排除部分
4. 联系开发团队 