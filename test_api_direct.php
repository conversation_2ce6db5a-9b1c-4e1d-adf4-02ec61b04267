<?php

/**
 * 直接测试API端点
 */

require_once 'vendor/autoload.php';

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Product\Http\Controllers\ProductController;
use App\Product\Services\ProductService;
use Illuminate\Http\Request;

echo "🔗 直接测试API端点\n";
echo "================\n\n";

try {
    // 创建模拟请求
    $request = new Request();
    $request->merge([
        'page' => 1,
        'limit' => 20,
        'with_units' => true,
        'keyword' => '',
        'category_id' => null,
        'status' => null,
        'stock_status' => '',
        'supplier' => '',
        'min_price' => null,
        'max_price' => null,
        'date_range' => [],
        'sort_field' => 'id',
        'sort_order' => 'desc'
    ]);
    
    echo "1. 模拟请求参数:\n";
    echo json_encode($request->all(), JSON_PRETTY_PRINT) . "\n\n";
    
    // 创建ProductController实例
    $productService = app(ProductService::class);
    $controller = new ProductController($productService);
    
    echo "2. 调用ProductController::index方法...\n";
    
    // 调用index方法
    $response = $controller->index($request);
    
    echo "3. 分析响应结果...\n";
    
    $statusCode = $response->getStatusCode();
    echo "HTTP状态码: {$statusCode}\n";
    
    $content = $response->getContent();
    $data = json_decode($content, true);
    
    if ($data) {
        echo "响应结构:\n";
        echo "  - code: " . ($data['code'] ?? 'N/A') . "\n";
        echo "  - message: " . ($data['message'] ?? 'N/A') . "\n";
        
        if (isset($data['data'])) {
            $responseData = $data['data'];
            echo "  - data类型: " . gettype($responseData) . "\n";
            
            if (is_array($responseData)) {
                echo "  - data键: " . implode(', ', array_keys($responseData)) . "\n";
                
                // 检查是否是分页格式
                if (isset($responseData['data']) && is_array($responseData['data'])) {
                    echo "  - 分页格式检测: ✅ Laravel分页格式\n";
                    echo "  - 商品数据数量: " . count($responseData['data']) . "\n";
                    echo "  - current_page: " . ($responseData['current_page'] ?? 'N/A') . "\n";
                    echo "  - per_page: " . ($responseData['per_page'] ?? 'N/A') . "\n";
                    echo "  - total: " . ($responseData['total'] ?? 'N/A') . "\n";
                    echo "  - last_page: " . ($responseData['last_page'] ?? 'N/A') . "\n";
                    echo "  - from: " . ($responseData['from'] ?? 'N/A') . "\n";
                    echo "  - to: " . ($responseData['to'] ?? 'N/A') . "\n";
                } elseif (is_array($responseData) && isset($responseData[0])) {
                    echo "  - 分页格式检测: ❌ 直接数组格式\n";
                    echo "  - 数组长度: " . count($responseData) . "\n";
                    echo "  - 第一个元素类型: " . gettype($responseData[0]) . "\n";
                    if (is_array($responseData[0]) && isset($responseData[0]['id'])) {
                        echo "  - 第一个商品ID: " . $responseData[0]['id'] . "\n";
                        echo "  - 第一个商品名称: " . ($responseData[0]['name'] ?? 'N/A') . "\n";
                    }
                } else {
                    echo "  - 分页格式检测: ❓ 未知格式\n";
                    echo "  - 数据内容: " . json_encode($responseData) . "\n";
                }
            } else {
                echo "  - data不是数组: " . json_encode($responseData) . "\n";
            }
        } else {
            echo "  - 没有data字段\n";
        }
    } else {
        echo "响应内容解析失败:\n";
        echo $content . "\n";
    }
    
    echo "\n4. 测试不同的分页参数...\n";
    
    $testCases = [
        ['page' => 1, 'limit' => 10],
        ['page' => 2, 'limit' => 10],
        ['page' => 1, 'limit' => 5],
    ];
    
    foreach ($testCases as $index => $testCase) {
        echo "测试案例 " . ($index + 1) . ": page={$testCase['page']}, limit={$testCase['limit']}\n";
        
        $testRequest = new Request();
        $testRequest->merge($testCase);
        
        $testResponse = $controller->index($testRequest);
        $testData = json_decode($testResponse->getContent(), true);
        
        if ($testData && isset($testData['data'])) {
            $testResponseData = $testData['data'];
            if (isset($testResponseData['data']) && is_array($testResponseData['data'])) {
                echo "  结果: {$testResponseData['current_page']}页, {$testResponseData['per_page']}条/页, 总数{$testResponseData['total']}, 当前{$testResponseData['count']}条\n";
            } else {
                echo "  结果: 非分页格式, 数据量" . (is_array($testResponseData) ? count($testResponseData) : 'N/A') . "\n";
            }
        } else {
            echo "  结果: 响应解析失败\n";
        }
    }

    echo "\n✅ 测试完成！\n";

} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
