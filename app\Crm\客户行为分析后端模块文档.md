# 客户行为分析后端模块文档

## 📋 模块概述

客户行为分析模块是生鲜配送CRM系统的核心功能之一，用于收集、分析和展示客户在商城端的行为数据，为CRM专员提供数据驱动的客户管理决策支持。

## 🏗️ 系统架构

### 架构设计原则
- **边界清晰**：严格的权限控制，CRM专员只能访问分配给自己的客户数据
- **无污染**：模块化设计，不影响现有系统功能
- **逻辑顺畅**：从数据收集到分析展示的完整闭环

### 技术栈
- **后端框架**：Laravel 10
- **数据库**：MySQL 8.0+
- **认证方式**：Laravel Sanctum
- **权限控制**：基于员工角色的权限系统

## 📊 数据库设计

### 核心数据表

#### 1. customer_behavior_analytics (用户行为记录表)
```sql
- id: 主键
- user_id: 用户ID (可为空，支持匿名用户)
- session_id: 会话ID
- event_type: 事件类型 (page_view, product_view, cart_operation, search, order_behavior)
- event_data: 事件详细数据 (JSON)
- device_info: 设备信息 (JSON)
- ip_address: IP地址
- user_agent: 用户代理
- created_at: 创建时间
```

#### 2. user_sessions (用户会话表)
```sql
- id: 主键
- session_id: 会话ID (唯一)
- user_id: 用户ID
- start_time: 会话开始时间
- end_time: 会话结束时间
- page_count: 访问页面数
- event_count: 事件总数
- duration: 会话时长(秒)
- device_info: 设备信息 (JSON)
- ip_address: IP地址
- referrer: 来源页面
```

#### 3. behavior_statistics (行为统计汇总表)
```sql
- id: 主键
- user_id: 用户ID
- stat_date: 统计日期
- page_views: 页面访问次数
- product_views: 商品浏览次数
- cart_operations: 购物车操作次数
- search_count: 搜索次数
- order_count: 订单数量
- session_count: 会话数量
- total_duration: 总停留时间(秒)
- total_amount: 当日消费金额
```

#### 4. product_view_analytics (商品浏览记录表)
```sql
- id: 主键
- user_id: 用户ID
- product_id: 商品ID
- session_id: 会话ID
- source: 来源 (list, search, recommendation, banner)
- view_duration: 浏览时长(秒)
- scroll_depth: 滚动深度(百分比)
- image_views: 图片查看次数
- added_to_cart: 是否加入购物车
```

#### 5. search_analytics (搜索行为记录表)
```sql
- id: 主键
- user_id: 用户ID
- session_id: 会话ID
- keyword: 搜索关键词
- filters: 筛选条件 (JSON)
- result_count: 搜索结果数量
- click_position: 点击位置
- clicked_product_id: 点击的商品ID
```

#### 6. cart_analytics (购物车行为记录表)
```sql
- id: 主键
- user_id: 用户ID
- session_id: 会话ID
- operation: 操作类型 (add, remove, update, clear)
- product_id: 商品ID
- quantity: 数量
- price: 商品价格
- total_cart_value: 购物车总价值
```

### 索引优化
- 用户+时间复合索引：`idx_user_time`
- 会话索引：`idx_session`
- 事件类型索引：`idx_event_type`
- 时间索引：`idx_created_at`

## 🔧 后端架构

### 模型层 (Models)

#### CustomerBehaviorAnalytics
- **职责**：用户行为数据的ORM模型
- **特性**：
  - 事件类型常量定义
  - 静态工厂方法创建不同类型事件
  - 查询作用域 (Scopes)
  - 关联关系定义

#### UserSession
- **职责**：用户会话管理
- **特性**：
  - 会话生命周期管理
  - 活跃状态判断
  - 统计计数器

#### BehaviorStatistics
- **职责**：行为统计数据汇总
- **特性**：
  - 日期维度统计
  - 增量更新方法
  - 平均值计算

### 服务层 (Services)

#### BehaviorAnalyticsService
- **职责**：核心业务逻辑处理
- **主要功能**：
  1. `getBehaviorOverview()` - 行为分析概览
  2. `getClientBehavior()` - 客户行为详情
  3. `getPurchaseAnalysis()` - 购买行为分析
  4. `getBrowseAnalysis()` - 浏览行为分析
  5. `getTimeAnalysis()` - 时间行为分析
  6. `getGeoAnalysis()` - 地理行为分析
  7. `getTrendAnalysis()` - 趋势分析
  8. `getCustomerValueAnalysis()` - 客户价值分析
  9. `getProductPreferenceAnalysis()` - 商品偏好分析
  10. `getChurnWarning()` - 流失预警

#### 权限控制机制
```php
private function getAuthorizedUserIds(?int $crmAgentId = null): array
{
    if (is_null($crmAgentId)) {
        // 管理员可以看到所有用户
        return User::pluck('id')->toArray();
    }
    
    // CRM专员只能看到分配给自己的客户
    return User::where('crm_agent_id', $crmAgentId)->pluck('id')->toArray();
}
```

### 控制器层 (Controllers)

#### BehaviorAnalyticsController
- **职责**：API接口处理
- **特性**：
  - 统一的错误处理
  - 完整的日志记录
  - 权限验证
  - 参数验证

## 🌐 API接口文档

### 基础路径
```
/api/crm/behavior-analytics/
```

### 接口列表

#### 1. 获取行为分析概览
```http
GET /api/crm/behavior-analytics/overview
```
**参数**：
- `start_date` (可选): 开始日期
- `end_date` (可选): 结束日期

**响应**：
```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "active_users": 1250,
    "avg_order_value": 85.50,
    "repurchase_rate": 35.8,
    "churn_warnings": 15,
    "last_updated": "2024-12-01 10:30:00"
  }
}
```

#### 2. 获取客户行为详情
```http
GET /api/crm/behavior-analytics/client/{clientId}
```
**参数**：
- `start_date` (可选): 开始日期
- `end_date` (可选): 结束日期

#### 3. 获取购买行为分析
```http
GET /api/crm/behavior-analytics/purchase
```

#### 4. 获取浏览行为分析
```http
GET /api/crm/behavior-analytics/browse
```

#### 5. 获取时间行为分析
```http
GET /api/crm/behavior-analytics/time
```

#### 6. 获取地理行为分析
```http
GET /api/crm/behavior-analytics/geo
```

#### 7. 获取趋势分析
```http
GET /api/crm/behavior-analytics/trend
```

#### 8. 获取客户价值分析
```http
GET /api/crm/behavior-analytics/customer-value
```

#### 9. 获取商品偏好分析
```http
GET /api/crm/behavior-analytics/product-preference
```

#### 10. 获取流失预警
```http
GET /api/crm/behavior-analytics/churn-warning
```

#### 11. 获取分析配置
```http
GET /api/crm/behavior-analytics/config
```

#### 12. 导出分析数据
```http
POST /api/crm/behavior-analytics/export
```
**参数**：
- `type`: 导出类型 (overview, purchase, browse, time, geo, trend, value, preference, churn)
- `format`: 导出格式 (json, csv, excel)

## 🔐 权限控制

### 角色权限矩阵

| 角色 | 权限范围 | 说明 |
|------|----------|------|
| 管理员 (admin) | 全部客户数据 | 可以查看所有客户的行为分析 |
| 经理 (manager) | 全部客户数据 | 可以查看所有客户的行为分析 |
| CRM专员 (crm_agent) | 分配客户数据 | 只能查看分配给自己的客户数据 |
| 其他角色 | 无权限 | 无法访问行为分析功能 |

### 权限实现
```php
private function getCrmAgentId(): ?int
{
    $user = Auth::user();
    
    if (!$user) {
        throw new \Exception('用户未登录');
    }

    // 检查用户角色
    if ($user->hasAdminPermission()) {
        // 管理员可以查看所有数据
        return null;
    }

    // 检查是否是CRM专员
    if ($user->isCrmAgent()) {
        // 返回员工ID作为CRM专员ID
        return $user->employee?->id;
    }

    throw new \Exception('无权限访问行为分析数据');
}
```

## 📈 数据分析功能

### 1. 行为概览分析
- 活跃用户数量
- 平均客单价
- 复购率
- 流失预警数量

### 2. 购买行为分析
- 购买频次分布
- 购买金额分布
- 购买时间模式
- 支付方式分析
- 购买趋势

### 3. 浏览行为分析
- 页面浏览统计
- 商品浏览分析
- 会话分析
- 跳出率分析

### 4. 时间行为分析
- 24小时活跃度热力图
- 日常活动模式
- 季节性趋势

### 5. 地理行为分析
- 地区分布
- 配送偏好
- 地区消费水平

### 6. 趋势分析
- 用户增长趋势
- 活动趋势
- 收入趋势
- 留存趋势

### 7. 客户价值分析
- RFM分析
- 价值细分
- 生命周期价值

### 8. 商品偏好分析
- 品类偏好
- 价格敏感度
- 热门商品
- 交叉销售机会

### 9. 流失预警
- 高风险客户
- 中风险客户
- 低风险客户
- 风险因素分析

## 🚀 部署和使用

### 1. 数据库迁移
```bash
php artisan migrate --path=database/migrations/2024_12_01_000001_create_customer_behavior_analytics_tables.php
```

### 2. 服务注册
确保在 `config/app.php` 中注册了 `CrmServiceProvider`：
```php
'providers' => [
    // ...
    App\Crm\Providers\CrmServiceProvider::class,
],
```

### 3. 权限配置
确保员工表中正确设置了角色：
- `admin`: 管理员
- `manager`: 经理
- `crm_agent`: CRM专员

### 4. API调用示例
```javascript
// 获取行为分析概览
const response = await fetch('/api/crm/behavior-analytics/overview', {
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    }
});

const data = await response.json();
```

## 🔧 扩展和维护

### 添加新的分析维度
1. 在 `BehaviorAnalyticsService` 中添加新的分析方法
2. 在 `BehaviorAnalyticsController` 中添加对应的API端点
3. 在路由文件中注册新路由
4. 更新前端API配置

### 性能优化建议
1. **数据分区**：按时间对大表进行分区
2. **缓存策略**：对频繁查询的统计数据使用Redis缓存
3. **异步处理**：使用队列处理大量数据的统计计算
4. **索引优化**：根据查询模式优化数据库索引

### 监控和日志
- 所有API调用都有完整的日志记录
- 错误信息记录在Laravel日志中
- 建议配置监控告警

## 📝 注意事项

### 数据隐私
- 严格遵循权限控制，确保数据安全
- 敏感信息需要脱敏处理
- 遵循数据保护法规

### 性能考虑
- 大数据量查询需要分页处理
- 复杂统计建议使用后台任务
- 定期清理过期数据

### 扩展性
- 模块化设计，便于功能扩展
- 接口设计考虑向后兼容
- 数据结构支持灵活扩展

## 🎯 未来规划

### 短期目标
- [ ] 实时数据推送
- [ ] 更多可视化图表
- [ ] 自定义报表功能

### 长期目标
- [ ] 机器学习预测模型
- [ ] 智能推荐系统
- [ ] 自动化营销触发

---

**版本**: v1.0.0  
**更新时间**: 2024-12-01  
**维护者**: CRM开发团队 