<?php

namespace App\Crm\Services;

use App\Crm\Models\MembershipLevel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MembershipLevelService
{
    /**
     * 获取会员等级列表
     *
     * @param Request $request
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Pagination\LengthAwarePaginator
     */
    public function getLevels(Request $request)
    {
        $query = MembershipLevel::query();
        
        // 排序
        if ($request->has('sort') && $request->sort) {
            $direction = $request->has('direction') && $request->direction === 'desc' ? 'desc' : 'asc';
            $query->orderBy($request->sort, $direction);
        } else {
            $query->orderBy('sort_order', 'asc');
        }
        
        // 返回分页或全部数据
        if ($request->has('per_page')) {
            return $query->paginate($request->per_page);
        }
        
        return $query->get();
    }
    
    /**
     * 获取会员等级详情
     *
     * @param int $id
     * @return MembershipLevel
     */
    public function getLevel($id)
    {
        return MembershipLevel::findOrFail($id);
    }
    
    /**
     * 创建会员等级
     *
     * @param array $data
     * @return MembershipLevel
     */
    public function createLevel(array $data)
    {
        // 如果设置为默认等级，需要将其他等级设为非默认
        if (isset($data['is_default']) && $data['is_default']) {
            MembershipLevel::query()->update(['is_default' => false]);
        }
        
        return MembershipLevel::create($data);
    }
    
    /**
     * 更新会员等级
     *
     * @param int $id
     * @param array $data
     * @return MembershipLevel
     */
    public function updateLevel($id, array $data)
    {
        $level = MembershipLevel::findOrFail($id);
        
        // 如果设置为默认等级，需要将其他等级设为非默认
        if (isset($data['is_default']) && $data['is_default']) {
            MembershipLevel::query()->where('id', '!=', $id)->update(['is_default' => false]);
        }
        
        $level->update($data);
        return $level;
    }
    
    /**
     * 删除会员等级
     *
     * @param int $id
     * @return bool
     */
    public function deleteLevel($id)
    {
        $level = MembershipLevel::findOrFail($id);
        
        // 检查是否是默认等级
        if ($level->is_default) {
            throw new \Exception('不能删除默认会员等级');
        }
        
        // 检查是否有用户使用此等级
        if ($level->users()->count() > 0) {
            throw new \Exception('此会员等级下有用户，不能删除');
        }
        
        return $level->delete();
    }
    
    /**
     * 设置为默认会员等级
     *
     * @param int $id
     * @return MembershipLevel
     */
    public function setAsDefault($id)
    {
        $level = MembershipLevel::findOrFail($id);
        $level->setAsDefault();
        return $level;
    }
    
    /**
     * 计算用户应该属于的会员等级
     *
     * @param int $points 用户积分
     * @param float $totalSpend 累计消费
     * @param float $largestOrder 最大订单金额
     * @return MembershipLevel
     */
    public function calculateUserLevel($points, $totalSpend, $largestOrder)
    {
        return MembershipLevel::getAppropriateLevel($points, $totalSpend, $largestOrder);
    }
} 