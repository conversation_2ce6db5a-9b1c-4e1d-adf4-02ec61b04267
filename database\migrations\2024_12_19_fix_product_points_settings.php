<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 检查积分字段是否存在
        if (!Schema::hasColumn('products', 'points_reward_enabled')) {
            Log::info('积分字段不存在，跳过修复');
            return;
        }

        Log::info('开始修复商品积分设置数据');

        // 修复积分设置数据
        DB::transaction(function () {
            // 1. 修复 points_reward_type 为 null 或空的记录
            $nullTypeCount = DB::table('products')
                ->whereNull('points_reward_type')
                ->orWhere('points_reward_type', '')
                ->update([
                    'points_reward_type' => 'rate',
                    'points_reward_rate' => 0.0100, // 默认100元获得1积分
                    'points_reward_enabled' => true,
                ]);

            Log::info("修复了 {$nullTypeCount} 个积分类型为空的商品");

            // 2. 修复 points_reward_rate 为 0 或 null 的记录（当类型为 rate 时）
            $zeroRateCount = DB::table('products')
                ->where('points_reward_type', 'rate')
                ->where(function ($query) {
                    $query->whereNull('points_reward_rate')
                          ->orWhere('points_reward_rate', 0)
                          ->orWhere('points_reward_rate', '<=', 0);
                })
                ->update([
                    'points_reward_rate' => 0.0100, // 默认100元获得1积分
                ]);

            Log::info("修复了 {$zeroRateCount} 个积分比例为0的商品");

            // 3. 修复 points_reward_fixed 为 null 的记录（当类型为 fixed 时）
            $nullFixedCount = DB::table('products')
                ->where('points_reward_type', 'fixed')
                ->whereNull('points_reward_fixed')
                ->update([
                    'points_reward_fixed' => 1, // 默认每件商品奖励1积分
                ]);

            Log::info("修复了 {$nullFixedCount} 个固定积分为空的商品");

            // 4. 确保所有商品都有 points_reward_enabled 字段值
            $nullEnabledCount = DB::table('products')
                ->whereNull('points_reward_enabled')
                ->update([
                    'points_reward_enabled' => true,
                ]);

            Log::info("修复了 {$nullEnabledCount} 个积分启用状态为空的商品");

            // 5. 修复异常的积分比例值（过大或过小）
            $abnormalRateCount = DB::table('products')
                ->where('points_reward_type', 'rate')
                ->where(function ($query) {
                    $query->where('points_reward_rate', '>', 1) // 比例大于100%
                          ->orWhere('points_reward_rate', '<', 0.0001); // 比例小于0.01%
                })
                ->update([
                    'points_reward_rate' => 0.0100, // 重置为默认值
                ]);

            Log::info("修复了 {$abnormalRateCount} 个异常积分比例的商品");

            // 6. 记录修复后的统计信息
            $stats = DB::table('products')
                ->selectRaw('
                    COUNT(*) as total_products,
                    SUM(CASE WHEN points_reward_enabled = 1 THEN 1 ELSE 0 END) as enabled_count,
                    SUM(CASE WHEN points_reward_type = "rate" THEN 1 ELSE 0 END) as rate_type_count,
                    SUM(CASE WHEN points_reward_type = "fixed" THEN 1 ELSE 0 END) as fixed_type_count,
                    AVG(CASE WHEN points_reward_type = "rate" THEN points_reward_rate ELSE NULL END) as avg_rate,
                    AVG(CASE WHEN points_reward_type = "fixed" THEN points_reward_fixed ELSE NULL END) as avg_fixed
                ')
                ->first();

            Log::info('积分设置修复完成，统计信息:', [
                'total_products' => $stats->total_products,
                'enabled_count' => $stats->enabled_count,
                'rate_type_count' => $stats->rate_type_count,
                'fixed_type_count' => $stats->fixed_type_count,
                'avg_rate' => $stats->avg_rate,
                'avg_fixed' => $stats->avg_fixed,
            ]);
        });

        Log::info('商品积分设置数据修复完成');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 这个迁移主要是数据修复，不需要回滚操作
        Log::info('积分设置修复迁移回滚（无操作）');
    }
};
