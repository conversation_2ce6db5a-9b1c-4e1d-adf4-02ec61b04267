<?php

namespace App\Order\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class PaymentLink extends Model
{
    use HasFactory;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id',
        'link_no',
        'order_id',
        'correction_id',
        'amount',
        'paid_amount',
        'payment_type',
        'status',
        'qr_code_url',
        'short_url',
        'expires_at',
        'paid_at',
        'payment_method',
        'transaction_id',
        'reminder_count',
        'last_reminder_at',
        'remark',
        'failure_reason',
        'idempotency_key',
        'created_by',

    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'expires_at' => 'datetime',
        'paid_at' => 'datetime',
        'last_reminder_at' => 'datetime',

    ];

    /**
     * 关联订单
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * 关联订单更正
     */
    public function correction(): BelongsTo
    {
        return $this->belongsTo(OrderCorrection::class, 'correction_id');
    }

    /**
     * 关联创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Employee::class, 'created_by');
    }

    /**
     * 生成付款链接ID
     */
    public static function generateLinkId(): string
    {
        return 'PL' . date('YmdHis') . Str::random(8);
    }

    /**
     * 生成付款链接编号
     */
    public static function generateLinkNo(): string
    {
        return 'PAY' . date('YmdHis') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * 是否已过期
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * 是否已支付
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * 是否可用
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && !$this->isExpired();
    }

    /**
     * 获取付款类型中文名称
     */
    public function getPaymentTypeNameAttribute(): string
    {
        $types = [
            'cod' => '货到付款',
            'supplement' => '补款',
        ];

        return $types[$this->payment_type] ?? $this->payment_type;
    }

    /**
     * 获取状态中文名称
     */
    public function getStatusNameAttribute(): string
    {
        $statuses = [
            'active' => '有效',
            'paid' => '已支付',
            'expired' => '已过期',
            'cancelled' => '已取消',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * 获取完整的付款链接URL
     */
    public function getFullUrlAttribute(): string
    {
        return config('app.url') . '/payment/' . $this->id;
    }

    /**
     * 检查是否可以支付
     */
    public function canPay(): bool
    {
        return $this->status === 'active' && !$this->isExpired();
    }

    /**
     * 检查是否正在处理支付
     */
    public function isProcessing(): bool
    {
        return $this->status === 'processing';
    }

    /**
     * 开始支付处理（防重复）
     */
    public function startPaymentProcessing(): bool
    {
        // 使用数据库锁防止并发
        return $this->where('id', $this->id)
            ->where('status', 'active')
            ->update(['status' => 'processing', 'updated_at' => now()]) > 0;
    }

    /**
     * 完成支付
     */
    public function completePayment(array $paymentData): void
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
            'payment_method' => $paymentData['payment_method'] ?? null,
            'transaction_id' => $paymentData['transaction_id'] ?? null,
            'payment_amount' => $paymentData['amount'] ?? $this->amount,
        ]);
    }

    /**
     * 支付失败，恢复状态
     */
    public function failPayment(string $reason = null): void
    {
        $this->update([
            'status' => 'active',
            'failure_reason' => $reason,
            'updated_at' => now(),
        ]);
    }

    /**
     * 检查支付处理是否超时
     */
    public function isPaymentTimeout(): bool
    {
        if ($this->status !== 'processing') {
            return false;
        }

        // 支付处理超过10分钟视为超时
        return $this->updated_at->addMinutes(10)->isPast();
    }
} 