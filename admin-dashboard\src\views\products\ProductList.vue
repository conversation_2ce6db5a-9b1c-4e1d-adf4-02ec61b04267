<template>
  <div class="product-list">
    <!-- 标签页区域 -->
    <div class="tabs-container">
      <div class="main-tabs">
        <span class="tab-title">类型</span>
        <el-button 
          :type="activeTab === 'all' ? 'primary' : ''" 
          size="small" 
          @click="activeTab = 'all'"
          class="tab-btn"
        >
          全部(1007)
        </el-button>
        <el-button 
          :type="activeTab === 'normal' ? 'primary' : ''" 
          size="small" 
          @click="activeTab = 'normal'"
          class="tab-btn"
        >
          普通商品(970)
        </el-button>
        <el-button 
          :type="activeTab === 'presale' ? 'primary' : ''" 
          size="small" 
          @click="activeTab = 'presale'"
          class="tab-btn"
        >
          预售商品(37)
        </el-button>
      </div>
      
      <div class="status-buttons">
        <el-button 
          :type="activeStatus === 'all' ? 'primary' : ''" 
          size="small" 
          @click="activeStatus = 'all'"
          class="status-btn"
        >
          全部(1007)
        </el-button>
        <el-button 
          :type="activeStatus === 'selling' ? 'primary' : ''" 
          size="small" 
          @click="activeStatus = 'selling'"
          class="status-btn"
        >
          出售中(218)
        </el-button>
        <el-button 
          :type="activeStatus === 'offline' ? 'primary' : ''" 
          size="small" 
          @click="activeStatus = 'offline'"
          class="status-btn"
        >
          已下架(789)
        </el-button>
        <el-button 
          :type="activeStatus === 'stock-warning' ? 'primary' : ''" 
          size="small" 
          @click="activeStatus = 'stock-warning'"
          class="status-btn"
        >
          库存预警(65)
        </el-button>
        <el-button 
          :type="activeStatus === 'price-warning' ? 'primary' : ''" 
          size="small" 
          @click="activeStatus = 'price-warning'"
          class="status-btn"
        >
          价格预警(61)
        </el-button>
        <el-button 
          :type="activeStatus === 'recycle' ? 'primary' : ''" 
          size="small" 
          @click="activeStatus = 'recycle'"
          class="status-btn"
        >
          回收站(816)
        </el-button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-container">
      <div class="filter-row">
        <div class="filter-group">
          <label>分类</label>
          <el-select v-model="filters.category" placeholder="请选择分类" clearable size="default">
            <el-option label="全部分类" value=""></el-option>
            <el-option label="生鲜" value="fresh"></el-option>
            <el-option label="蔬菜" value="vegetable"></el-option>
            <el-option label="水果" value="fruit"></el-option>
          </el-select>
        </div>
        
        <div class="filter-group">
          <label>标品</label>
          <el-select v-model="filters.standard" placeholder="请选择标品" clearable size="default">
            <el-option label="全部标品" value=""></el-option>
            <el-option label="标准商品" value="standard"></el-option>
            <el-option label="非标商品" value="non-standard"></el-option>
          </el-select>
        </div>
        
        
        <div class="filter-group">
          <label>商品标签</label>
          <el-select v-model="filters.tagStatus" placeholder="请选择状态" clearable size="default">
            <el-option label="全部状态" value=""></el-option>
            <el-option label="已标记" value="tagged"></el-option>
            <el-option label="未标记" value="untagged"></el-option>
          </el-select>
        </div>
        
        <div class="filter-group search-group">
          <label>关键字搜索</label>
          <el-input 
            v-model="filters.keyword" 
            placeholder="ID或商品名称的关键字搜索"
            clearable
            size="default"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <div class="filter-group action-group">
          <div class="filter-actions">
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
          </div>
        </div>
      </div>
    </div>


    <!-- 商品表格 -->
    <div class="table-container">
      <el-table 
        :data="productList" 
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#fafafa', color: '#606266', fontWeight: '500' }"
        :row-style="{ height: '60px' }"
        border
        v-loading="loading"
        element-loading-text="加载商品数据..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.1)"
        element-loading-svg-view-box="-10, -10, 50, 50"
        element-loading-svg='<path class="path" d="M 30,15 A 15,15 0 0,1 15,30 A 15,15 0 0,1 0,15 A 15,15 0 0,1 15,0 A 15,15 0 0,1 30,15" style="stroke: #28a745; stroke-width: 4px; fill: none;"/>'
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="id" label="ID" width="80" align="center" />
        
        <el-table-column label="商品名称" width="250">
          <template #default="scope">
            <div class="product-cell">
              <img :src="scope.row.image" alt="" class="product-img" />
              <div class="product-info">
                <div class="product-name">{{ scope.row.name }}</div>
                <div class="product-subtitle">{{ scope.row.subtitle }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="category" label="所属分类" width="150" />
        
        <el-table-column label="库存/销量" width="100" align="center">
          <template #default="scope">
            <div class="stock-info">
              <div class="stock-num">{{ scope.row.stock }}</div>
              <div class="sales-num">/ {{ scope.row.sales }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="类型/标签" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.type" size="small" :type="getTypeTagType(scope.row.type)">
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="price" label="期货(售价)" width="120" align="center">
          <template #default="scope">
            <span class="price-text">{{ scope.row.price }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="商品标签" width="200">
          <template #default="scope">
            <div class="tags-cell">
              <el-tag 
                v-for="tag in scope.row.tags.slice(0, 4)" 
                :key="tag" 
                size="small" 
                class="tag-item"
              >
                {{ tag }}
              </el-tag>
              <span v-if="scope.row.tags.length > 4" class="more-tags">...</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="收银台状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="180" fixed="right" align="center">
          <template #default="scope">
            <div class="action-buttons">
              <el-button type="primary" size="small" circle>
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button type="danger" size="small" circle>
                <el-icon><Delete /></el-icon>
              </el-button>
              <el-button type="info" size="small" circle>
                <el-icon><CopyDocument /></el-icon>
              </el-button>
              <el-button type="success" size="small" circle>
                <el-icon><Top /></el-icon>
              </el-button>
              <el-button type="warning" size="small" circle>
                <el-icon><PriceTag /></el-icon>
              </el-button>
              <el-button size="small" circle>
                <el-icon><Setting /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          background
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { 
  Plus, Search, Edit, PriceTag, CopyDocument, ArrowDown, 
  Delete, Top, Setting 
} from '@element-plus/icons-vue'
import { getProductList, type Product, type ProductListParams } from '@/api/products'
import { ElMessage } from 'element-plus'

// 筛选条件
const filters = reactive({
  category: '',
  standard: '',
  tagStatus: '',
  keyword: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 标签页
const activeTab = ref('all')
const activeSubTab = ref('all-sub')
const activeStatus = ref('all')

// 商品列表数据
const productList = ref<Product[]>([])

// 简单的加载状态
const loading = ref(false)

// 加载商品列表
const loadProductList = async () => {
  try {
    loading.value = true
    
    const params: ProductListParams = {
      page: currentPage.value,
      pageSize: pageSize.value,
      category: filters.category,
      standard: filters.standard,
      tagStatus: filters.tagStatus,
      keyword: filters.keyword,
      tab: activeTab.value,
      status: activeStatus.value
    }
    
    const response = await getProductList(params)
    productList.value = response.data
    total.value = response.total
    
  } catch (error) {
    console.error('加载商品列表失败:', error)
    ElMessage.error('加载商品列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 搜索商品
const handleSearch = () => {
  currentPage.value = 1
  loadProductList()
}

// 重置筛选
const handleReset = () => {
  Object.assign(filters, {
    category: '',
    standard: '',
    tagStatus: '',
    keyword: ''
  })
  currentPage.value = 1
  loadProductList()
}

// 页面初始化
onMounted(() => {
  loadProductList()
})

// 监听分页变化
watch([currentPage, pageSize], () => {
  loadProductList()
})

// 监听标签页变化
watch([activeTab, activeStatus], () => {
  currentPage.value = 1
  loadProductList()
})

// 获取类型标签样式
const getTypeTagType = (type: string) => {
  switch (type) {
    case '正常': return 'success'
    case '主上架': return 'primary'
    default: return ''
  }
}

// 获取状态标签样式
const getStatusTagType = (status: string) => {
  switch (status) {
    case '正常': return 'success'
    case '主上架': return 'primary'
    default: return ''
  }
}
</script>

<style scoped>
.product-list {
  padding: 0;
}

/* 标签页容器 */
.tabs-container {
  background: white;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.main-tabs, .status-buttons {
  display: flex;
  padding: 0 20px;
  border-bottom: 1px solid #e4e7ed;
}

.main-tabs {
  padding: 15px 20px;
  gap: 8px;
  align-items: center;
}

.tab-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

.status-buttons {
  border-bottom: none;
  background-color: #f8f9fa;
  border-radius: 0 0 8px 8px;
  padding: 15px 20px;
  gap: 8px;
}

.tab-btn, .status-btn {
  border-radius: 4px;
  font-size: 13px;
  padding: 6px 12px;
}

.tab-btn.el-button--primary, .status-btn.el-button--primary {
  background-color: #28a745;
  border-color: #28a745;
}

.tab-btn.el-button--primary:hover, .status-btn.el-button--primary:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

/* 筛选容器 */
.filter-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  min-width: 180px;
}

.search-group {
  min-width: 250px;
}

.action-group {
  min-width: 160px;
}

.filter-group label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px 0 0 4px;
  padding: 6px 12px;
  display: inline-block;
  white-space: nowrap;
  flex-shrink: 0;
  margin: 0;
  border-right: none;
}

.filter-group .el-select {
  flex: 1;
}

.filter-group .el-select .el-input__wrapper {
  border-radius: 0 4px 4px 0;
  border-left: none;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

/* 操作栏 */
.action-bar {
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 表格容器 */
.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 商品信息 */
.product-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-img {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid #e4e7ed;
}

.product-info {
  flex: 1;
}

.product-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
  font-size: 14px;
}

.product-subtitle {
  font-size: 12px;
  color: #909399;
}

/* 库存销量 */
.stock-info {
  text-align: center;
}

.stock-num {
  color: #e74c3c;
  font-weight: 500;
  font-size: 14px;
}

.sales-num {
  color: #909399;
  font-size: 12px;
}

/* 价格 */
.price-text {
  color: #e74c3c;
  font-weight: 500;
  font-size: 14px;
}

/* 标签 */
.tags-cell {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.tag-item {
  font-size: 11px;
}

.more-tags {
  color: #909399;
  font-size: 12px;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-buttons .el-button {
  width: 24px;
  height: 24px;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table th) {
  background-color: #fafafa !important;
  color: #606266 !important;
  font-weight: 500;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #28a745;
  color: #fff;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled):hover) {
  color: #28a745;
}

:deep(.el-button--primary) {
  background-color: #28a745;
  border-color: #28a745;
}

:deep(.el-button--primary:hover) {
  background-color: #218838;
  border-color: #1e7e34;
}

/* 自定义加载动画 */
:deep(.el-loading-spinner .path) {
  animation: rotate 2s linear infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-linecap: round;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}
</style> 