use App\Printing\Http\Controllers\PrintController;

Route::prefix('api')->group(function () {
    // 打印相关路由
    Route::post('/print/order/{id}', [PrintController::class, 'printOrder']);
    Route::post('/print/order/{id}/delivery', [PrintController::class, 'printDelivery']);
    Route::post('/print/order/{id}/reprint', [PrintController::class, 'reprintOrder']);
    Route::post('/print/orders/batch', [PrintController::class, 'batchPrintOrders']);
    Route::get('/print/config', [PrintController::class, 'getConfig']);
    
    // 打印机相关
    Route::get('/print/printers', [PrintController::class, 'printers']);
    Route::post('/print/printer/default', [PrintController::class, 'setDefaultPrinter']);
    Route::post('/print/printers/default', [PrintController::class, 'setDefaultPrinter']);
    Route::get('/print/printer/status', [PrintController::class, 'printerStatus']);
    
    // 打印预览和脚本
    Route::post('/print/preview', [PrintController::class, 'preview']);
    Route::post('/print/script', [PrintController::class, 'script']);
    
    // 打印状态回调
    Route::post('/print/completed', [PrintController::class, 'printCompleted']);
    Route::post('/print/failed', [PrintController::class, 'printFailed']);
    Route::post('/print/batch-callback', [PrintController::class, 'batchPrintCallback']);
    
    // 打印状态查询
    Route::get('/print/order/{orderId}/status', [PrintController::class, 'orderPrintStatus']);
    Route::post('/print/orders/batch-status', [PrintController::class, 'batchGetOrderPrintStatus']);
    
    // 打印记录管理
    Route::get('/print/pending', [PrintController::class, 'pendingPrints']);
    Route::get('/print/record/{recordId}', [PrintController::class, 'printRecord']);
}); 