<?php

namespace App\Region\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Product\Models\Product;

class RegionPrice extends Model
{
    use HasFactory;
    
    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'product_id',
        'region_id',
        'price',
        'original_price',
        'stock',
        'status',
        'start_date',
        'end_date',
        'special_conditions'
    ];
    
    /**
     * 类型转换
     *
     * @var array
     */
    protected $casts = [
        'price' => 'decimal:2',
        'original_price' => 'decimal:2',
        'stock' => 'integer',
        'status' => 'boolean',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'special_conditions' => 'array'
    ];
    
    /**
     * 关联商品
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
    
    /**
     * 关联区域
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function region()
    {
        return $this->belongsTo(Region::class);
    }
    
    /**
     * 检查价格是否有效
     *
     * @return bool
     */
    public function isValid()
    {
        if (!$this->status) {
            return false;
        }
        
        $now = now();
        
        if ($this->start_date && $this->start_date->gt($now)) {
            return false;
        }
        
        if ($this->end_date && $this->end_date->lt($now)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查库存是否充足
     *
     * @param int $quantity 数量
     * @return bool
     */
    public function hasStock($quantity = 1)
    {
        if ($this->stock === null) {
            return true; // 无限库存
        }
        
        return $this->stock >= $quantity;
    }
    
    /**
     * 扣减库存
     *
     * @param int $quantity 扣减数量
     * @return bool
     */
    public function decreaseStock($quantity = 1)
    {
        if ($this->stock === null) {
            return true; // 无限库存
        }
        
        if ($this->stock < $quantity) {
            return false;
        }
        
        $this->stock -= $quantity;
        return $this->save();
    }
    
    /**
     * 增加库存
     *
     * @param int $quantity 增加数量
     * @return bool
     */
    public function increaseStock($quantity = 1)
    {
        if ($this->stock === null) {
            return true; // 无限库存
        }
        
        $this->stock += $quantity;
        return $this->save();
    }
} 