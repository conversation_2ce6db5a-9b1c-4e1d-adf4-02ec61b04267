<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_printer_bindings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('warehouse_id')->comment('仓库ID');
            $table->unsignedBigInteger('flycloud_printer_id')->comment('飞蛾云打印机ID');
            $table->string('print_type')->default('order')->comment('打印类型：order订单,picking拣货,delivery配送');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->boolean('is_default')->default(false)->comment('是否为该仓库的默认打印机');
            $table->integer('priority')->default(0)->comment('优先级，数值越大优先级越高');
            $table->json('settings')->nullable()->comment('打印设置');
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            // 建立外键关系
            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->foreign('flycloud_printer_id')->references('id')->on('flycloud_printers')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
            
            // 创建索引
            $table->index(['warehouse_id', 'print_type']);
            $table->index(['warehouse_id', 'is_default']);
            $table->index(['flycloud_printer_id', 'is_active']);
            
            // 确保同一仓库同一打印类型只有一个默认打印机
            $table->unique(['warehouse_id', 'print_type', 'is_default'], 'unique_warehouse_default_printer');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_printer_bindings');
    }
}; 