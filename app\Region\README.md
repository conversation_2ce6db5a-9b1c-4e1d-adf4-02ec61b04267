# 区域模块 (Region Module)

区域模块提供地理区域及其相关价格的管理功能，支持商品在不同区域设置不同价格的业务需求。

## 功能特性

- **区域层级管理**：支持多层级区域结构，如国家、省份、城市等
- **树形结构操作**：支持区域的树形结构展示和操作
- **区域价格管理**：为商品设置不同区域的不同价格
- **价格继承机制**：子区域可以继承父区域的价格设置
- **批量价格操作**：支持批量设置、复制和调整区域价格

## 核心组件

- **Models**：
  - `Region`: 区域模型，实现树形结构相关方法
  - `RegionPrice`: 区域价格模型，管理商品在不同区域的价格

- **Services**：
  - `RegionService`: 区域服务，提供区域相关业务逻辑
  - `RegionPriceService`: 区域价格服务，处理价格相关业务逻辑

- **Repositories**：
  - `RegionRepository`: 区域仓库，处理区域数据的存取

- **Controllers**：
  - `RegionController`: 区域API控制器
  - `RegionPriceController`: 区域价格API控制器

## API 接口

### 区域管理接口
- `GET /api/regions` - 获取区域列表（分页）
- `GET /api/regions/tree` - 获取区域树结构
- `GET /api/regions/{id}` - 获取区域详情
- `GET /api/regions/{id}/ancestors` - 获取区域祖先
- `GET /api/regions/{id}/descendants` - 获取区域后代
- `GET /api/regions/{id}/breadcrumb` - 获取区域面包屑

### 区域价格接口
- `GET /api/regions/prices` - 获取商品在指定区域的价格
- `GET /api/regions/products/{productId}/prices` - 获取商品的所有区域价格
- `POST /api/regions/products/{productId}/prices` - 批量设置商品区域价格
- `POST /api/regions/prices/copy` - 复制区域价格到其他区域
- `POST /api/regions/{regionId}/prices/adjust` - 批量调整区域价格
- `DELETE /api/regions/products/{productId}/prices` - 删除商品的区域价格

## 使用示例

```php
// 获取区域树
$regionService = app(RegionService::class);
$tree = $regionService->getTree();

// 获取商品在指定区域的价格
$regionPriceService = app(RegionPriceService::class);
$price = $regionPriceService->getProductRegionPrice($productId, $regionId);

// 批量设置商品区域价格
$prices = [
    ['region_id' => 1, 'price' => 100],
    ['region_id' => 2, 'price' => 120],
];
$regionPriceService->batchSetProductRegionPrices($productId, $prices);
``` 