# 商户名称优先显示功能说明

## 修改概述
根据生鲜配送B2B业务特点，将客户列表的显示逻辑调整为以商户名称为主，个人信息为辅的展示方式。

## 主要修改内容

### 1. 客户列表页面 (`pages/clients/clients.vue`)

#### 显示优先级调整
- **主标题**：商户名称 (`merchant_name`) 优先显示
- **副标题**：如果有商户名称，则显示"联系人：个人姓名"
- **头像文字**：优先使用商户名称首字符

#### 搜索功能优化
- 搜索提示文本：`"搜索商户名称、联系人或手机号"`
- 搜索优先级：商户名称 > 联系人姓名 > 手机号码

#### 空状态优化
- 图标：🏪 (商店图标)
- 文本：`"暂无商户数据"` / `"未找到相关商户"`

### 2. 客户选择页面 (`pages/proxy-order/select-client.vue`)

#### 简化显示信息
- 移除订单统计信息
- 突出商户名称和联系人信息
- 优化布局，更适合选择操作

#### 搜索优化
- 搜索提示：`"搜索商户名称或联系人"`

## 业务逻辑说明

### 显示规则
```javascript
// 主标题显示逻辑
主标题 = client.merchant_name || client.name || '未知商户'

// 副标题显示逻辑
if (client.merchant_name) {
    副标题 = '联系人：' + client.name
} else {
    副标题 = 不显示
}

// 头像文字逻辑
头像文字 = client.merchant_name?.charAt(0) || client.name?.charAt(0) || '商'
```

### 搜索过滤逻辑
```javascript
// 搜索匹配优先级
1. 商户名称匹配 (merchant_name)
2. 联系人姓名匹配 (name)  
3. 手机号码匹配 (phone)
```

## 用户体验改进

### 1. 更符合B2B场景
- 商户是业务的核心实体
- 个人信息作为联系方式补充
- 便于快速识别和管理商户

### 2. 搜索体验优化
- 优先匹配商户名称，提高搜索准确性
- 支持多种搜索方式，提高查找效率

### 3. 视觉层次清晰
- 主要信息突出显示
- 次要信息适当弱化
- 保持界面简洁美观

## 兼容性说明

### 数据兼容
- 如果没有商户名称，自动降级显示个人姓名
- 保持向后兼容，不影响现有数据

### 功能兼容
- 所有原有功能保持不变
- 仅调整显示优先级和搜索逻辑

## 测试建议

### 测试场景
1. **有商户名称的客户**：验证商户名称优先显示
2. **无商户名称的客户**：验证降级到个人姓名显示
3. **搜索功能**：验证商户名称优先匹配
4. **空状态**：验证提示文本正确显示

### 测试数据
```javascript
// 完整商户信息
{
    name: '张三',
    merchant_name: '张三生鲜店',
    phone: '13800138001'
}

// 仅个人信息
{
    name: '李四',
    merchant_name: null,
    phone: '13800138002'
}
```

## 后续优化建议

1. **商户分类**：可考虑添加商户类型标签
2. **商户等级**：可添加VIP、普通等级别标识
3. **地理位置**：可显示商户所在区域信息
4. **营业状态**：可显示商户当前营业状态 