<?php

use App\FlyCloud\Http\Controllers\FlyCloudController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| FlyCloud API 路由
|--------------------------------------------------------------------------
|
| FlyCloud模块的API路由定义
|
*/

// 需要认证的飞蛾云打印路由
Route::middleware(['auth:sanctum', 'employee.role:admin,manager'])->group(function () {
    // 飞蛾云打印模块路由
    Route::prefix('flycloud')->group(function () {
        // 基础打印功能
        Route::post('/print/text', [FlyCloudController::class, 'printText']);
        Route::post('/print/html', [FlyCloudController::class, 'printHtml']);
        Route::post('/print/order-receipt', [FlyCloudController::class, 'printOrderReceipt']);
        
        // 打印机管理
        Route::get('/printers', [FlyCloudController::class, 'getPrinters']);
        Route::get('/printer/status', [FlyCloudController::class, 'getPrinterStatus']);
        Route::post('/printer/add', [FlyCloudController::class, 'addPrinter']);
        Route::delete('/printer/delete', [FlyCloudController::class, 'deletePrinter']);
        Route::post('/printer/clear-queue', [FlyCloudController::class, 'clearPrintQueue']);
    });
}); 