<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_follow_ups', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->comment('客户ID');
            $table->foreignId('employee_id')->constrained('employees')->comment('CRM专员ID(员工)');
            $table->date('follow_up_date')->comment('跟进日期');
            $table->enum('contact_method', ['phone', 'sms', 'email', 'visit', 'wechat', 'other'])->comment('联系方式');
            $table->text('notes')->nullable()->comment('跟进内容');
            $table->enum('result', ['successful', 'follow_up', 'no_answer', 'rejected', 'other'])->comment('跟进结果');
            $table->date('next_follow_up')->nullable()->comment('下次跟进日期');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_follow_ups');
    }
}; 