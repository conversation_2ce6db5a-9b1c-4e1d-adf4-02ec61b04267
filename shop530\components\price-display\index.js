// components/price-display/index.js
const { addLoginStateListener, removeLoginStateListener, getLoginStatus } = require('../../utils/login-state-manager');
const { shouldShowPrice, getProductPrice, updateUserContext } = require('../../utils/price-manager');

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 核心属性
    productId: {
      type: Number,
      value: 0,
      observer: 'onProductIdChange'
    },
    
    quantity: {
      type: Number,
      value: 1,
      observer: 'onQuantityChange'
    },
    
    // 静态价格（向后兼容）
    price: {
      type: String,
      value: '0.00',
      observer: 'onPriceChange'
    },
    
    originalPrice: {
      type: String,
      value: '',
      observer: 'onOriginalPriceChange'
    },
    
    // 单位
    unit: {
      type: String,
      value: '',
      observer: 'onUnitChange'
    },
    
    // 显示控制
    showPriceLabels: {
      type: Boolean,
      value: true
    },
    
    showDiscountInfo: {
      type: Boolean,
      value: true
    },
    
    showMemberUpgradeTip: {
      type: Boolean,
      value: false
    },
    
    // 样式控制
    size: {
      type: String,
      value: 'normal' // small, normal, large
    },
    
    theme: {
      type: String,
      value: 'default' // default, card, minimal
    },
    
    // 自定义样式类
    className: {
      type: String,
      value: ''
    },
    
    customStyle: {
      type: String,
      value: ''
    },
    
    // 登录相关
    loginText: {
      type: String,
      value: '请登录查看价格'
    },
    
    loginPromptClass: {
      type: String,
      value: ''
    },
    
    showLoginIcon: {
      type: Boolean,
      value: true
    },
    
    // 行为控制
    forceShowPrice: {
      type: Boolean,
      value: false
    },
    
    disableLoginTap: {
      type: Boolean,
      value: false
    },
    
    // 静态折扣信息（用于购物车等已计算好价格的场景）
    discountInfo: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isLoggedIn: false,
    showPrice: false,
    loading: false,
    error: false,
    
    // 价格信息
    priceInfo: {
      mainPrice: '0',
      decimalPrice: '.00',
      originalPrice: '',
      hasDiscount: false,
      discountAmount: '0.00',
      priceType: 'base',
      priceLabels: [],
      userType: 'guest'
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 属性变化观察者
     */
    onProductIdChange(newVal, oldVal) {
      const safeNewVal = newVal || 0;
      if (safeNewVal !== oldVal && this.data.showPrice) {
        this.loadPriceData();
      }
    },

    onPriceChange(newVal, oldVal) {
      const safeNewVal = newVal || '0.00';
      if (safeNewVal !== oldVal && !this.properties.productId) {
        this.formatStaticPrice();
      }
    },

    onOriginalPriceChange(newVal, oldVal) {
      const safeNewVal = newVal || '';
      if (safeNewVal !== oldVal && !this.properties.productId) {
        this.formatStaticPrice();
      }
    },

    onQuantityChange(newVal, oldVal) {
      const safeNewVal = newVal || 1;
      if (safeNewVal !== oldVal && this.properties.productId && this.data.showPrice) {
        this.loadPriceData();
      }
    },

    onUnitChange(newVal, oldVal) {
      // 单位变化只影响显示，不需要重新计算价格
    },

    /**
     * 检查登录状态并更新价格显示
     */
    async checkLoginStatus() {
      // 直接从登录状态管理器获取最新状态
      const loginStatus = getLoginStatus();
      const isLoggedIn = loginStatus.isLoggedIn;
      const userInfo = loginStatus.userInfo;
      
      const forceShow = this.properties.forceShowPrice;
      const finalShowPrice = isLoggedIn || forceShow;

      // 更新状态
      this.setData({
        isLoggedIn: isLoggedIn,
        showPrice: finalShowPrice
      });

      // 如果可以显示价格，加载价格数据
      if (finalShowPrice) {
        await this.loadPriceData();
      } else {
        this.resetPriceData();
      }
    },

    /**
     * 加载价格数据
     */
    async loadPriceData() {
      if (this.properties.productId > 0) {
        await this.fetchDynamicPrice();
      } else {
        this.formatStaticPrice();
      }
    },

    /**
     * 获取动态价格
     */
    async fetchDynamicPrice() {
      if (!this.properties.productId) {
        return;
      }

      try {
        this.setData({ loading: true, error: false });
        
        // 添加重试机制
        const priceData = await this.fetchPriceWithRetry(
          this.properties.productId, 
          this.properties.quantity || 1
        );
        
        if (priceData.shouldShow) {
          if (priceData.error) {
            this.setData({ 
              error: true,
              loading: false 
            });
            // 即使出错也显示基础价格信息
            this.formatPriceInfo(priceData);
          } else {
            this.setData({ 
              error: false,
              loading: false 
            });
            this.formatPriceInfo(priceData);
          }
        } else {
          this.setData({ 
            showPrice: false,
            loading: false 
          });
          this.resetPriceData();
        }
        
      } catch (error) {
        this.setData({ 
          error: true,
          loading: false 
        });
        
        // 降级到静态价格显示
        this.fallbackToStaticPrice();

        // 触发价格加载错误事件
        this.triggerEvent('priceError', {
          error: error.message || '价格获取失败',
          productId: this.properties.productId,
          quantity: this.properties.quantity
        });
      }
    },

    /**
     * 带重试的价格获取
     */
    async fetchPriceWithRetry(productId, quantity, maxRetries = 2) {
      let lastError;
      
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          const priceData = await getProductPrice(productId, quantity);
          
          // 处理价格加载超时返回的错误状态
          if (priceData && priceData.error) {
            console.warn('💰 价格组件收到错误状态价格数据:', priceData);
            
            // 如果不是最后一次尝试，继续重试
            if (attempt < maxRetries) {
              await new Promise(resolve => setTimeout(resolve, 500 * attempt));
              continue;
            }
            
            // 最后一次尝试，使用基础价格
            return {
              base_price: this.properties.price || '0.00',
              final_price: this.properties.price || '0.00',
              original_price: this.properties.originalPrice || this.properties.price || '0.00',
              price_type: 'base',
              discount_types: [],
              price_labels: [],
              has_discount: false,
              discount_amount: '0.00',
              user_type: 'guest',
              error: true,
              error_message: priceData.error_message || '价格加载失败'
            };
          }
          
          // 检查返回的价格数据是否有效
          if (priceData && priceData.final_price) {
            return priceData;
          } else if (priceData && priceData.shouldShow === false) {
            // 用户未登录等正常情况
            return priceData;
          } else {
            if (attempt === maxRetries) {
              return priceData; // 最后一次尝试，返回原始数据
            }
          }
          
        } catch (error) {
          lastError = error;
          console.warn(`💰 价格获取失败 (尝试 ${attempt}/${maxRetries}):`, error.message);
          
          if (attempt < maxRetries) {
            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 500 * attempt));
          }
        }
      }
      
      // 所有重试都失败，返回基础价格对象
      return {
        base_price: this.properties.price || '0.00',
        final_price: this.properties.price || '0.00',
        original_price: this.properties.originalPrice || this.properties.price || '0.00',
        price_type: 'base',
        discount_types: [],
        price_labels: [],
        has_discount: false,
        discount_amount: '0.00',
        user_type: 'guest',
        error: true,
        error_message: lastError?.message || '价格获取失败'
      };
    },

    /**
     * 降级到静态价格显示
     */
    fallbackToStaticPrice() {
      if (this.properties.price && parseFloat(this.properties.price) > 0) {
        this.setData({ 
          showPrice: true,
          loading: false,
          error: false
        });
        this.formatStaticPrice();
      } else {
        this.resetPriceData();
      }
    },
    
    /**
     * 格式化价格信息
     */
    formatPriceInfo(priceData) {
      const finalPrice = priceData.final_price || this.properties.price || '0.00';
      const originalPrice = priceData.original_price || this.properties.originalPrice || '';
      const hasDiscount = priceData.has_discount || false;
      const discountAmount = priceData.discount_amount || '0.00';
      const priceType = priceData.price_type || 'base';
      const userType = priceData.user_type || 'guest';
      
      // 分割价格显示
      const priceStr = parseFloat(finalPrice).toFixed(2);
      const [main, decimal] = priceStr.split('.');
      
      // 生成价格标签
      const priceLabels = this.generatePriceLabels(priceData);
      
      // 检查是否为会员价格
      const isMemberPrice = this.checkIsMemberPrice(priceData);
      
      this.setData({
        priceInfo: {
          mainPrice: main,
          decimalPrice: '.' + decimal,
          originalPrice: originalPrice,
          hasDiscount: hasDiscount,
          discountAmount: discountAmount,
          priceType: priceType,
          priceLabels: priceLabels,
          userType: userType,
          isMemberPrice: isMemberPrice
        }
      });

      // 触发价格加载完成事件
      this.triggerEvent('priceLoaded', {
        priceInfo: {
          mainPrice: main,
          decimalPrice: '.' + decimal,
          originalPrice: originalPrice,
          hasDiscount: hasDiscount,
          discountAmount: discountAmount,
          priceType: priceType,
          priceLabels: priceLabels,
          userType: userType,
          isMemberPrice: isMemberPrice
        },
        productId: this.properties.productId,
        quantity: this.properties.quantity
      });
    },

    /**
     * 检查是否为会员价格
     */
    checkIsMemberPrice(priceData) {
      if (!priceData) return false;
      
      // 检查价格类型 - 扩展支持category_discount
      if (priceData.price_type) {
        const priceType = priceData.price_type;
        if (priceType.includes('member') || priceType === 'member_discount') {
          return true;
        }
        
        // 特殊处理：category_discount可能包含会员折扣
        if (priceType === 'category_discount') {
          // 需要进一步检查是否包含会员相关的折扣
          return this.checkCategoryDiscountForMember(priceData);
        }
      }
      
      // 检查折扣类型
      if (priceData.discount_types && Array.isArray(priceData.discount_types)) {
        const memberDiscountTypes = ['product_member_discount', 'global_member_discount', 'category_member_discount'];
        return priceData.discount_types.some(type => memberDiscountTypes.includes(type));
      }
      
      // 检查价格标签
      if (priceData.price_labels && Array.isArray(priceData.price_labels)) {
        const memberLabelTypes = ['product_member_discount', 'global_member_discount', 'category_member_discount'];
        return priceData.price_labels.some(label => memberLabelTypes.includes(label.type));
      }
      
      // 检查用户类型和价格差异
      if (priceData.user_type === 'member' && priceData.has_discount) {
        return true;
      }
      
      return false;
    },

    /**
     * 检查category_discount是否包含会员折扣
     */
    checkCategoryDiscountForMember(priceData) {
      // 检查discount_info中是否有会员相关的折扣
      if (priceData.discount_info && Array.isArray(priceData.discount_info)) {
        const memberDiscountTypes = ['product_member_discount', 'global_member_discount', 'category_member_discount'];
        const hasMemberDiscount = priceData.discount_info.some(discount => 
          discount && discount.type && memberDiscountTypes.includes(discount.type)
        );
        if (hasMemberDiscount) {
          return true;
        }
      }
      
      // 检查price_labels中是否有会员标签
      if (priceData.price_labels && Array.isArray(priceData.price_labels)) {
        const memberLabels = ['会员价', '分类会员价', '会员减免'];
        const hasMemberLabel = priceData.price_labels.some(label => 
          label && (memberLabels.includes(label.text) || (label.type && label.type.includes('member')))
        );
        if (hasMemberLabel) {
          return true;
        }
      }
      
      return false;
    },
    
    /**
     * 格式化静态价格
     */
    formatStaticPrice() {
      // 安全处理 null 值
      const price = this.properties.price || '0.00';
      const originalPrice = this.properties.originalPrice || '';
      const unit = this.properties.unit || '';
      
      // 确保价格是有效的数字字符串
      let safePrice = '0.00';
      try {
        const numPrice = parseFloat(price);
        if (!isNaN(numPrice) && numPrice >= 0) {
          safePrice = numPrice.toFixed(2);
        }
      } catch (e) {
        // 静默处理格式化失败
      }
      
      const [main, decimal] = safePrice.split('.');
      
      // 安全处理折扣信息
      const discountInfo = this.properties.discountInfo || [];
      const priceLabels = discountInfo.length > 0 
        ? this.generatePriceLabelsFromDiscountInfo(discountInfo)
        : [];
      
      // 计算折扣
      let hasDiscount = false;
      let discountAmount = '0.00';
      if (originalPrice && originalPrice !== '' && originalPrice !== price) {
        try {
          const origNum = parseFloat(originalPrice);
          const currNum = parseFloat(safePrice);
          if (!isNaN(origNum) && !isNaN(currNum) && origNum > currNum) {
            hasDiscount = true;
            discountAmount = (origNum - currNum).toFixed(2);
          }
        } catch (e) {
          // 静默处理折扣计算失败
        }
      }
      
      // 检查是否为会员价格（静态模式）
      const isMemberPrice = this.checkStaticMemberPrice(discountInfo);
      
      this.setData({
        priceInfo: {
          mainPrice: main,
          decimalPrice: '.' + decimal,
          originalPrice: originalPrice,
          hasDiscount: hasDiscount,
          discountAmount: discountAmount,
          priceType: 'static',
          priceLabels: priceLabels,
          userType: 'guest',
          isMemberPrice: isMemberPrice
        }
      });

      // 触发价格加载完成事件（静态价格）
      this.triggerEvent('priceLoaded', {
        priceInfo: {
          mainPrice: main,
          decimalPrice: '.' + decimal,
          originalPrice: originalPrice,
          hasDiscount: hasDiscount,
          discountAmount: discountAmount,
          priceType: 'static',
          priceLabels: priceLabels,
          userType: 'guest',
          isMemberPrice: isMemberPrice
        },
        productId: this.properties.productId,
        quantity: this.properties.quantity,
        isStatic: true
      });
    },

    /**
     * 检查静态价格是否为会员价格
     */
    checkStaticMemberPrice(discountInfo) {
      if (!Array.isArray(discountInfo)) return false;
      
      const memberDiscountTypes = ['product_member_discount', 'global_member_discount', 'category_member_discount'];
      return discountInfo.some(info => 
        info && info.type && memberDiscountTypes.includes(info.type)
      );
    },
    
    /**
     * 生成价格标签
     */
    generatePriceLabels(priceData) {
      if (!this.properties.showPriceLabels || !priceData.price_labels) {
        return [];
      }
      
      return priceData.price_labels.map(label => ({
        ...label,
        // 确保颜色格式正确，添加样式
        style: `background-color: ${label.color}; color: white; border-radius: 2px; padding: 1px 4px; font-size: 10px;`
      }));
    },
    
    /**
     * 从折扣信息生成价格标签（兼容模式）
     * 支持并轨优惠：区域优惠和会员优惠可以同时显示
     */
    generatePriceLabelsFromDiscountInfo(discountInfo) {
      if (!Array.isArray(discountInfo) || !this.properties.showPriceLabels) {
        return [];
      }
      
      const labels = [];
      const addedTypes = new Set(); // 防止重复添加相同类型的标签
      
      discountInfo.forEach(info => {
        if (!info || typeof info !== 'object') return;
        
        // 根据折扣类型生成标签（支持并轨显示）
        switch (info.type) {
          case 'region_price':
            if (!addedTypes.has('region')) {
              labels.push({
                text: '区域价',
                type: 'region',
                color: '#1890ff',
                style: 'background-color: #1890ff; color: white; border-radius: 2px; padding: 1px 4px; font-size: 10px;'
              });
              addedTypes.add('region');
            }
            break;
            
          case 'product_member_discount':
          case 'global_member_discount':
            if (!addedTypes.has('member')) {
              labels.push({
                text: '会员价',
                type: 'member',
                color: '#faad14',
                style: 'background-color: #faad14; color: white; border-radius: 2px; padding: 1px 4px; font-size: 10px;'
              });
              addedTypes.add('member');
            }
            break;
            
          case 'category_region_discount':
            if (!addedTypes.has('category_region')) {
              labels.push({
                text: '区域优惠',
                type: 'category_region',
                color: '#52c41a',
                style: 'background-color: #52c41a; color: white; border-radius: 2px; padding: 1px 4px; font-size: 10px;'
              });
              addedTypes.add('category_region');
            }
            break;
            
          case 'category_member_discount':
            if (!addedTypes.has('category_member')) {
              labels.push({
                text: '分类会员价',
                type: 'category_member',
                color: '#722ed1',
                style: 'background-color: #722ed1; color: white; border-radius: 2px; padding: 1px 4px; font-size: 10px;'
              });
              addedTypes.add('category_member');
            }
            break;
            
          default:
            // 其他类型的折扣
            if (info.label && info.active && !addedTypes.has(info.type)) {
              labels.push({
                text: info.label,
                type: info.type || 'discount',
                color: info.color || '#ff4d4f',
                style: `background-color: ${info.color || '#ff4d4f'}; color: white; border-radius: 2px; padding: 1px 4px; font-size: 10px;`
              });
              addedTypes.add(info.type || 'discount');
            }
            break;
        }
      });
      
      // 按优先级排序标签：区域价 > 会员价 > 分类优惠
      const priorityOrder = ['region', 'member', 'category_region', 'category_member'];
      labels.sort((a, b) => {
        const aIndex = priorityOrder.indexOf(a.type);
        const bIndex = priorityOrder.indexOf(b.type);
        return (aIndex === -1 ? 999 : aIndex) - (bIndex === -1 ? 999 : bIndex);
      });
      
      return labels;
    },

    /**
     * 重置价格数据
     */
    resetPriceData() {
      this.setData({
        priceInfo: {
          mainPrice: '0',
          decimalPrice: '.00',
          originalPrice: '',
          hasDiscount: false,
          discountAmount: '0.00',
          priceType: 'base',
          priceLabels: [],
          userType: 'guest'
        }
      });
    },

    /**
     * 登录提示点击事件
     */
    onLoginPromptTap() {
      if (this.properties.disableLoginTap) {
        return;
      }
      
      // 触发自定义事件
      this.triggerEvent('loginPromptTap', {
        source: 'price-display'
      });
      
      // 默认跳转到登录页
      try {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      } catch (error) {
        // 静默处理跳转失败
      }
    },

    /**
     * 刷新价格
     */
    async refreshPrice() {
      await this.loadPriceData();
    },

    /**
     * 价格标签点击事件
     */
    onPriceLabelTap(e) {
      const label = e.currentTarget.dataset.label;
      
      // 触发自定义事件
      this.triggerEvent('priceLabelTap', {
        label: label,
        priceInfo: this.data.priceInfo
      });
      
      // 根据标签类型执行不同操作
      switch (label.type) {
        case 'member':
          this.showMemberInfo();
          break;
        case 'region':
          this.showRegionInfo();
          break;
        case 'category_region':
          this.showCategoryRegionInfo();
          break;
        case 'category_member':
          this.showCategoryMemberInfo();
          break;
        default:
          this.showPriceInfo();
          break;
      }
    },

    /**
     * 显示会员信息
     */
    showMemberInfo() {
      wx.showModal({
        title: '会员价格',
        content: '您享受的是会员专属价格，感谢您的支持！',
        showCancel: false
      });
    },

    /**
     * 显示区域信息
     */
    showRegionInfo() {
      wx.showModal({
        title: '区域价格',
        content: '根据您的地区显示的专属价格',
        showCancel: false
      });
    },

    /**
     * 显示区域优惠信息
     */
    showCategoryRegionInfo() {
      wx.showModal({
                    title: '区域优惠',
        content: '该商品分类在您的地区享受特殊优惠价格',
        showCancel: false
      });
    },

    /**
     * 显示分类会员优惠信息
     */
    showCategoryMemberInfo() {
      wx.showModal({
        title: '分类会员价',
        content: '该商品分类为会员提供专属优惠价格',
        showCancel: false
      });
    },

    /**
     * 显示价格信息
     */
    showPriceInfo() {
      const priceInfo = this.data.priceInfo;
      let content = `当前价格：¥${priceInfo.mainPrice}${priceInfo.decimalPrice}`;
      
      if (priceInfo.hasDiscount) {
        content += `\n优惠金额：¥${priceInfo.discountAmount}`;
      }
      
      wx.showModal({
        title: '价格信息',
        content: content,
        showCancel: false
      });
    },

    /**
     * 外部调用刷新价格
     */
    refresh() {
      return this.refreshPrice();
    },

    /**
     * 获取当前价格信息
     */
    getPriceInfo() {
      return this.data.priceInfo;
    },

    /**
     * 设置价格数据（用于外部直接设置）
     */
    setPriceData(priceData) {
      this.formatPriceInfo(priceData);
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 生成唯一的监听器ID
      this.listenerId = `price-display-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // 添加登录状态监听
      addLoginStateListener(this.listenerId, this.checkLoginStatus.bind(this));
      
      // 初始化检查登录状态
      this.checkLoginStatus();
    },

    detached() {
      // 移除登录状态监听
      if (this.listenerId) {
        removeLoginStateListener(this.listenerId);
      }
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面显示时延迟检查登录状态，确保价格管理器状态同步
      setTimeout(() => {
        this.checkLoginStatus();
      }, 50);
    }
  },


}); 