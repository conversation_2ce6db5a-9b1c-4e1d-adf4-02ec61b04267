<?php

namespace App\WechatPayment\Models;

use App\Order\Models\Order;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WechatServiceRefund extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'payment_id',
        'order_id',
        'out_refund_no',
        'refund_id',
        'transaction_id',
        'out_trade_no',
        'total_fee',
        'refund_fee',
        'service_refund_fee',
        'refund_status',
        'refund_channel',
        'refund_reason',
        'refund_time',
        'notify_data',
        'error_message',
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'total_fee' => 'float',
        'refund_fee' => 'float',
        'service_refund_fee' => 'float',
        'refund_time' => 'datetime',
        'notify_data' => 'array',
    ];

    /**
     * 获取所属的订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * 获取所属的支付记录
     */
    public function payment()
    {
        return $this->belongsTo(WechatServicePayment::class, 'payment_id');
    }
} 