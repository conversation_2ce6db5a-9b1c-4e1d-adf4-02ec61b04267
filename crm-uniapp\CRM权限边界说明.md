# CRM系统权限边界说明

## 概述
CRM系统采用基于角色的权限控制（RBAC），不同角色的员工只能访问属于自己管理范围内的客户数据。

## 数据库结构

### 核心表关系
```
employees (员工表)
├── id (主键)
├── role (角色: admin, manager, crm_agent, delivery, etc.)
└── ...

users (客户表)
├── id (主键)
├── crm_agent_id (外键 -> employees.id) 
├── name (客户姓名)
├── phone (手机号)
└── ...

orders (订单表)
├── id (主键)
├── user_id (外键 -> users.id)
├── order_no (订单号)
├── total (订单金额)
└── ...

crm_agents (CRM专员详情表)
├── id (主键)
├── employee_id (外键 -> employees.id)
├── max_clients (最大客户数)
├── clients_count (当前客户数)
└── ...
```

## 权限边界规则

### 1. 管理员 (admin)
- **权限范围**: 所有客户和订单
- **API行为**: 无过滤条件，返回所有数据
- **使用场景**: 系统管理、全局数据查看

### 2. 经理 (manager)  
- **权限范围**: 所有客户和订单
- **API行为**: 无过滤条件，返回所有数据
- **使用场景**: 业务管理、团队监督

### 3. CRM专员 (crm_agent)
- **权限范围**: 仅分配给自己的客户及其订单
- **API行为**: 添加过滤条件 `WHERE crm_agent_id = 当前员工ID`
- **使用场景**: 客户跟进、订单管理、客户服务

### 4. 配送员 (delivery)
- **权限范围**: 仅配送过的订单相关客户
- **API行为**: 通过订单-配送关联查询客户
- **使用场景**: 配送服务、客户联系

### 5. 其他角色 (staff, warehouse_manager)
- **权限范围**: 根据具体业务需求定义
- **API行为**: 可能有特定的过滤条件
- **使用场景**: 特定业务功能

## API权限控制实现

### 1. 客户管理API (`/api/crm/users`)

#### UserController::index() 方法
```php
// CRM专员只能看到自己的客户
if ($currentEmployee->role === 'crm_agent') {
    $users->where('crm_agent_id', $currentEmployee->id);
}

// 配送员只能看到分配给自己的用户
if ($currentEmployee->role === 'delivery') {
    $users->whereHas('orders', function ($q) use ($currentEmployee) {
        $q->whereHas('delivery', function ($dq) use ($currentEmployee) {
            $dq->whereHas('deliverer', function ($delivererQuery) use ($currentEmployee) {
                $delivererQuery->where('employee_id', $currentEmployee->id);
            });
        });
    });
}
```

#### UserController::getUserStatistics() 方法
```php
// CRM专员只能查看自己的客户统计
if ($currentEmployee->role === 'crm_agent' && $user->crm_agent_id !== $currentEmployee->id) {
    return response()->json(ApiResponse::error('没有权限查看该用户统计', 403), 403);
}
```

#### UserController::getUserOrders() 方法
```php
// CRM专员只能查看自己的客户订单
if ($currentEmployee->role === 'crm_agent' && $user->crm_agent_id !== $currentEmployee->id) {
    return response()->json(ApiResponse::error('没有权限查看该用户订单', 403), 403);
}
```

### 2. 订单管理API (`/api/orders`)

#### OrderService::getOrders() 方法
```php
// 权限控制：根据用户角色限制数据访问范围
if ($user && $user->employee) {
    $employee = $user->employee;
    
    switch ($employee->role) {
        case 'admin':
        case 'manager':
            // 管理员和经理可以查看所有订单
            break;
            
        case 'crm_agent':
            // CRM专员只能查看分配给自己的客户的订单
            $query->whereHas('user', function($q) use ($employee) {
                $q->where('crm_agent_id', $employee->id);
            });
            break;
            
        case 'delivery':
            // 配送员只能查看自己配送的订单
            $query->whereHas('delivery', function($q) use ($employee) {
                $q->whereHas('deliverer', function($dq) use ($employee) {
                    $dq->where('employee_id', $employee->id);
                });
            });
            break;
    }
}
```

#### OrderController::getStats() 方法
```php
// 权限控制：根据用户角色限制数据访问范围
if ($user && $user->employee) {
    $employee = $user->employee;
    
    switch ($employee->role) {
        case 'crm_agent':
            // CRM专员只能查看分配给自己的客户的订单统计
            $query->whereHas('user', function($q) use ($employee) {
                $q->where('crm_agent_id', $employee->id);
            });
            break;
    }
}
```

### 3. 代客下单API (`/api/orders/proxy`)

#### OrderController::createForClient() 方法
```php
// CRM专员权限检查：只能为分配给自己的客户代下单
if ($currentUser->employee && $currentUser->employee->role === 'crm_agent') {
    if ($client->crm_agent_id !== $currentUser->employee->id) {
        return response()->json(ApiResponse::error('您只能为分配给您的客户代下单', 403), 403);
    }
}
```

### 4. 客户分配API (`/api/crm/client-assignments`)

#### ClientAssignmentController 权限控制
```php
// 获取客户的CRM专员列表 - 权限检查
if ($currentUser->employee && $currentUser->employee->role == 'crm_agent') {
    // 检查此用户是否是该CRM专员负责的客户
    $isMyClient = User::where('id', $userId)
        ->where('crm_agent_id', $currentUser->employee->id)
        ->exists();
        
    if ($isMyClient) {
        $hasPermission = true;
    }
}
```

### 5. 跟进记录API (`/api/crm/follow-ups`)

#### ClientFollowUpController 权限控制
```php
// 如果当前用户是CRM专员，需要检查该用户是否是他的客户
if ($currentUser->isCrmAgent() && $currentUser->id != $userId) {
    $isClientOfAgent = DB::table('user_crm_agent')
        ->where('user_id', $userId)
        ->where('agent_id', $currentUser->id)
        ->where('status', 'active')
        ->exists();
        
    if (!$isClientOfAgent) {
        return response()->json(ApiResponse::error('该用户不是您的客户', 403), 403);
    }
}
```

## 前端权限控制

### 1. 路由守卫
```javascript
// 在uni-app中通过角色检查控制页面访问
if (userRole === 'crm_agent') {
    // CRM专员只能访问特定页面
}
```

### 2. 数据过滤
```javascript
// 前端调用API时，后端会自动根据用户角色过滤数据
// 无需前端额外处理权限逻辑
```

## 安全措施

### 1. 认证中间件
- 所有API都需要通过 `auth:sanctum` 中间件认证
- 员工角色通过 `employee.role` 中间件验证

### 2. 数据库层面
- 使用Eloquent关联查询确保数据隔离
- 通过 `whereHas` 子查询实现权限过滤

### 3. 日志记录
- 记录所有敏感操作的访问日志
- 包含用户ID、操作类型、访问时间等信息

## 测试验证

### 1. 单元测试
- 为每个权限控制点编写测试用例
- 验证不同角色的数据访问边界

### 2. 集成测试
- 测试完整的业务流程权限控制
- 验证跨模块的权限一致性

### 3. 安全测试
- 尝试越权访问其他用户数据
- 验证权限控制的有效性

## 注意事项

1. **数据一致性**: 确保所有相关API都实现了相同的权限控制逻辑
2. **性能优化**: 权限查询可能影响性能，需要适当的数据库索引
3. **错误处理**: 权限不足时返回明确的错误信息
4. **日志监控**: 监控异常的权限访问尝试
5. **定期审计**: 定期检查权限控制的实现是否完整

## 更新记录

- **2024-01-XX**: 初始版本，实现基本权限控制
- **2024-01-XX**: 修复订单统计API权限边界问题
- **2024-01-XX**: 完善代客下单API权限控制
- **2024-01-XX**: 添加所有API权限边界控制 