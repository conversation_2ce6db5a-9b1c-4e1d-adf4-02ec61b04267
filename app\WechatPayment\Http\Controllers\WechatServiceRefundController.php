<?php

namespace App\WechatPayment\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Order\Models\Order;
use App\WechatPayment\Models\WechatServicePayment;
use App\WechatPayment\Models\WechatServiceProvider;
use App\WechatPayment\Models\WechatServiceRefund;
use App\WechatPayment\Services\WechatServiceProviderPayment;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class WechatServiceRefundController extends Controller
{
    /**
     * 显示退款记录列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = WechatServiceRefund::query()
            ->with(['payment', 'payment.provider', 'order']);
            
        // 搜索条件
        if ($request->has('order_id') && $request->input('order_id') > 0) {
            $query->where('order_id', $request->input('order_id'));
        }
        
        if ($request->has('refund_id')) {
            $query->where('refund_id', 'like', '%' . $request->input('refund_id') . '%');
        }
        
        if ($request->has('out_refund_no')) {
            $query->where('out_refund_no', 'like', '%' . $request->input('out_refund_no') . '%');
        }
        
        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }
        
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('refunded_at', [$request->input('start_date'), $request->input('end_date')]);
        }
        
        $refunds = $query->orderBy('created_at', 'desc')->paginate(10);
        
        return response()->json([
            'data' => $refunds->items(),
            'total' => $refunds->total(),
            'current_page' => $refunds->currentPage(),
            'last_page' => $refunds->lastPage(),
        ]);
    }

    /**
     * 申请退款
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function refund(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|exists:orders,id',
                'refund_amount' => 'required|numeric|min:0.01',
                'refund_reason' => 'nullable|string|max:255',
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'message' => $validator->errors()->first(),
                ], 422);
            }
            
            $orderId = $request->input('order_id');
            $refundAmount = $request->input('refund_amount');
            $refundReason = $request->input('refund_reason') ?? '用户申请退款';
            
            // 获取订单信息
            $order = Order::findOrFail($orderId);
            
            // 检查订单状态
            if ($order->payment_status !== 'paid') {
                return response()->json([
                    'code' => 400,
                    'message' => '订单未支付，无法退款',
                ]);
            }
            
            // 获取支付记录
            $payment = WechatServicePayment::where('order_id', $orderId)
                ->where('trade_state', 'SUCCESS')
                ->first();
                
            if (!$payment) {
                return response()->json([
                    'code' => 400,
                    'message' => '未找到该订单的支付记录',
                ]);
            }
            
            // 检查退款金额
            if ($refundAmount > $payment->total_fee) {
                return response()->json([
                    'code' => 400,
                    'message' => '退款金额不能大于支付金额',
                ]);
            }
            
            // 获取服务商信息 - 特约商户模式
            $provider = WechatServiceProvider::where('is_active', 1)->first();
            if (!$provider) {
                return response()->json([
                    'code' => 400,
                    'message' => '微信支付配置不可用',
                ]);
            }
            
            // 创建支付服务实例 - 特约商户模式
            $paymentService = new WechatServiceProviderPayment($provider);
            
            // 生成退款单号
            $outRefundNo = 'RF' . date('YmdHis') . Str::random(6);
            
            // 申请退款
            $result = $paymentService->refund([
                'out_trade_no' => $payment->out_trade_no,
                'out_refund_no' => $outRefundNo,
                'total_fee' => (int)($payment->total_fee * 100), // 转换为分
                'refund_fee' => (int)($refundAmount * 100), // 转换为分
                'refund_desc' => $refundReason,
            ]);
            
            // 记录退款信息
            $refund = WechatServiceRefund::create([
                'payment_id' => $payment->id,
                'order_id' => $orderId,
                'refund_id' => $result['refund_id'] ?? null,
                'out_refund_no' => $outRefundNo,
                'transaction_id' => $payment->transaction_id,
                'out_trade_no' => $payment->out_trade_no,
                'total_fee' => $payment->total_fee,
                'refund_fee' => $refundAmount,
                'refund_status' => 'PROCESSING', // 退款中
                'refund_reason' => $refundReason,
                'notify_data' => $result,
            ]);
            
            return response()->json([
                'code' => 0,
                'message' => '退款申请成功，等待处理',
                'data' => $refund,
            ]);
            
        } catch (Exception $e) {
            Log::error('微信退款申请失败: ' . $e->getMessage(), [
                'order_id' => $request->input('order_id'),
                'refund_amount' => $request->input('refund_amount'),
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '退款申请失败: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * 查询退款状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function queryRefundStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'refund_id' => 'required_without:out_refund_no',
                'out_refund_no' => 'required_without:refund_id',
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'message' => $validator->errors()->first(),
                ], 422);
            }
            
            // 根据退款单号或商户退款单号查询
            $refundId = $request->input('refund_id');
            $outRefundNo = $request->input('out_refund_no');
            
            $query = WechatServiceRefund::query();
            
            if ($refundId) {
                $query->where('refund_id', $refundId);
            } else {
                $query->where('out_refund_no', $outRefundNo);
            }
            
            $refund = $query->with(['payment', 'order'])->first();
            
            if (!$refund) {
                return response()->json([
                    'code' => 404,
                    'message' => '未找到退款记录',
                ]);
            }
            
            // 获取服务商信息 - 特约商户模式
            $provider = WechatServiceProvider::where('is_active', 1)->first();
            if (!$provider) {
                return response()->json([
                    'code' => 400,
                    'message' => '微信支付配置不可用',
                ]);
            }
            
            // 创建支付服务实例
            $paymentService = new WechatServiceProviderPayment($provider);
            
            // 查询退款状态
            $result = $paymentService->queryRefund($refund->out_refund_no);
            
            // 更新退款状态
            if (isset($result['refund_status_0'])) {
                $status = $result['refund_status_0'];
                $refundStatus = '';
                
                switch ($status) {
                    case 'SUCCESS':
                        $refundStatus = 'success';
                        break;
                    case 'REFUNDCLOSE':
                        $refundStatus = 'closed';
                        break;
                    case 'PROCESSING':
                        $refundStatus = 'processing';
                        break;
                    case 'CHANGE':
                        $refundStatus = 'changed';
                        break;
                    default:
                        $refundStatus = 'failed';
                }
                
                $refund->update([
                    'refund_status' => $status, // 使用微信原始状态
                    'notify_data' => array_merge($refund->notify_data ?? [], $result),
                ]);
                
                if ($status === 'SUCCESS' && !$refund->refund_time) {
                    $refund->update(['refund_time' => now()]);
                }
            }
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => [
                    'refund' => $refund,
                    'status' => $refund->refund_status,
                    'query_result' => $result,
                ],
            ]);
            
        } catch (Exception $e) {
            Log::error('查询退款状态失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 500,
                'message' => '查询退款状态失败: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * 显示退款详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $refund = WechatServiceRefund::with(['payment', 'payment.provider', 'order'])
            ->findOrFail($id);
            
        return response()->json([
            'code' => 0,
            'data' => $refund,
        ]);
    }

    /**
     * 微信退款回调处理
     */
    public function refundNotify(Request $request)
    {
        try {
            $provider = WechatServiceProvider::where('is_active', 1)->first();
            if (!$provider) {
                Log::error('微信支付配置不可用');
                return response('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[配置错误]]></return_msg></xml>', 400);
            }

            // 解析XML数据
            $xml = $request->getContent();
            $data = $this->xmlToArray($xml);

            // 验证签名
            if (!$this->verifySign($data, $provider->key)) {
                Log::error('微信退款回调签名验证失败', ['data' => $data]);
                return response('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[签名验证失败]]></return_msg></xml>', 400);
            }

            if ($data['return_code'] === 'SUCCESS') {
                // 解密退款信息
                if (isset($data['req_info'])) {
                    $refundInfo = $this->decryptRefundInfo($data['req_info'], $provider->key);
                    
                    // 查找对应的退款记录
                    $refund = WechatServiceRefund::where('out_refund_no', $refundInfo['out_refund_no'])->first();
                    
                    if ($refund) {
                        // 更新退款记录
                        $refund->update([
                            'refund_id' => $refundInfo['refund_id'] ?? null,
                            'refund_status' => $refundInfo['refund_status'] ?? 'UNKNOWN',
                            'refund_time' => isset($refundInfo['success_time']) ? 
                                \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $refundInfo['success_time']) : null,
                            'notify_data' => array_merge($data, $refundInfo),
                        ]);

                        // 调用订单更正服务处理退款回调
                        $correctionService = app(\App\Order\Services\OrderCorrectionService::class);
                        $correctionService->handleWechatRefundCallback($refundInfo['out_refund_no'], $refundInfo);
                        
                        Log::info('微信退款回调处理成功', [
                            'out_refund_no' => $refundInfo['out_refund_no'],
                            'refund_status' => $refundInfo['refund_status'],
                            'refund_id' => $refundInfo['refund_id'] ?? null
                        ]);
                    } else {
                        Log::warning('未找到对应的退款记录', ['out_refund_no' => $refundInfo['out_refund_no']]);
                    }
                }
            }

            return response('<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>');
            
        } catch (\Exception $e) {
            Log::error('微信退款回调处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->getContent()
            ]);
            
            return response('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理失败]]></return_msg></xml>', 500);
        }
    }

    /**
     * XML转数组
     */
    private function xmlToArray(string $xml): array
    {
        $data = [];
        $xml = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
        
        if ($xml !== false) {
            $data = json_decode(json_encode($xml), true);
        }
        
        return $data;
    }

    /**
     * 验证签名
     */
    private function verifySign(array $data, string $key): bool
    {
        $sign = $data['sign'] ?? '';
        unset($data['sign']);
        
        ksort($data);
        $stringA = '';
        foreach ($data as $k => $v) {
            if ($v !== '' && $k !== 'sign') {
                $stringA .= $k . '=' . $v . '&';
            }
        }
        $stringSignTemp = $stringA . 'key=' . $key;
        $expectedSign = strtoupper(md5($stringSignTemp));
        
        return $sign === $expectedSign;
    }

    /**
     * 解密退款信息
     */
    private function decryptRefundInfo(string $reqInfo, string $key): array
    {
        $decrypted = openssl_decrypt(base64_decode($reqInfo), 'AES-256-ECB', md5($key), OPENSSL_RAW_DATA);
        
        if ($decrypted === false) {
            throw new \Exception('退款信息解密失败');
        }
        
        return $this->xmlToArray($decrypted);
    }
} 