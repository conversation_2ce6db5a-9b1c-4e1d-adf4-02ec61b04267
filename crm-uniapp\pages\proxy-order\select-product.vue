<template>
	<view class="select-product-container">
		<!-- 固定搜索栏 -->
		<view class="fixed-search-section">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input 
					class="search-input" 
					type="text" 
					placeholder="搜索商品名称、分类或描述" 
					v-model="searchKeyword"
					@input="onSearchInput"
					@confirm="onSearchConfirm"
				/>
				<button class="clear-search-btn" @tap="clearSearch" v-if="searchKeyword">
					<text class="clear-icon">✕</text>
				</button>
			</view>
			<!-- 搜索状态提示 -->
			<view class="search-status" v-if="searchKeyword">
				<text class="search-result-text">搜索"{{ searchKeyword }}"的结果</text>
				<text class="search-count" v-if="!listLoading">{{ productList.length }}个商品</text>
			</view>
		</view>
		
		<!-- 内容区域 -->
		<scroll-view 
			class="content-scroll" 
			scroll-y="true" 
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="onRefresh"
			:refresher-triggered="refreshing"
		>
			<!-- 已选商品统计 -->
			<view class="selected-summary" v-if="selectedProducts.length > 0">
				<view class="summary-info">
					<text class="summary-text">已选择 {{ selectedProducts.length }} 种商品，共 {{ getTotalQuantity() }} 件</text>
					<text class="summary-amount">¥{{ getTotalAmount() }}</text>
				</view>
				<button class="view-cart-btn" @tap="toggleCartView">
					<text class="cart-icon">🛒</text>
					<text class="cart-text">{{ showCart ? '隐藏' : '查看' }}购物车</text>
				</button>
			</view>
			
			<!-- 购物车视图 -->
			<view class="cart-section" v-if="showCart && selectedProducts.length > 0">
				<view class="cart-header">
					<text class="cart-title">购物车</text>
					<button class="clear-cart-btn" @tap="clearCart">清空</button>
				</view>
				<view class="cart-list">
					<view class="cart-item" v-for="(product, index) in selectedProducts" :key="product.id">
						<image class="cart-product-image" :src="product.image || '/static/default-product.png'" mode="aspectFill"></image>
						<view class="cart-product-info">
							<text class="cart-product-name">{{ product.name }}</text>
							<text class="cart-product-price">¥{{ formatPrice(product.price) }}/{{ product.unit || '件' }}</text>
						</view>
						<view class="cart-quantity-control">
							<button class="quantity-btn decrease" @tap="decreaseQuantity(index)">-</button>
							<text class="quantity-text">{{ product.quantity }}</text>
							<button class="quantity-btn increase" @tap="increaseQuantity(index)">+</button>
						</view>
						<button class="remove-btn" @tap="removeFromCart(index)">
							<text class="remove-icon">🗑️</text>
						</button>
					</view>
				</view>
			</view>
			
			<!-- 商品列表 -->
			<view class="product-list" v-if="productList.length > 0">
				<view 
					class="product-card" 
					v-for="product in productList" 
					:key="product.id"
				>
					<!-- 商品信息 -->
					<view class="product-info">
						<view class="product-image">
							<image 
								:src="product.cover_url || product.image || '/static/images/default-product.png'" 
								mode="aspectFill"
								class="product-img"
							/>
						</view>
						<view class="product-details">
							<text class="product-name">{{ product.name }}</text>
							<text class="product-desc">{{ product.description || '新鲜直达' }}</text>
							<view class="product-price-row">
								<text class="product-price">¥{{ formatPrice(product.price) }}</text>
								<text class="product-unit">/{{ product.unit || '份' }}</text>
							</view>
						</view>
					</view>
					
					<!-- 数量选择器 -->
					<view class="quantity-selector">
						<button 
							class="quantity-btn minus" 
							@tap="decreaseQuantity(product)"
							:disabled="getProductQuantity(product.id) === 0"
						>
							<text class="btn-text">-</text>
						</button>
						<text class="quantity-text">{{ getProductQuantity(product.id) }}</text>
						<button 
							class="quantity-btn plus" 
							@tap="increaseQuantity(product)"
						>
							<text class="btn-text">+</text>
						</button>
					</view>
				</view>
			</view>
			
			<!-- 加载状态 -->
			<view class="loading-section" v-if="listLoading && productList.length === 0">
				<view class="loading-content">
					<view class="loading-spinner"></view>
					<text class="loading-text">正在加载商品列表...</text>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-section" v-if="!listLoading && productList.length === 0">
				<text class="empty-icon">📦</text>
				<text class="empty-text">{{ searchKeyword ? '未找到相关商品' : '暂无商品数据' }}</text>
				<text class="empty-tip" v-if="searchKeyword">请尝试其他关键词或清空搜索条件</text>
				<button class="empty-action" v-if="searchKeyword" @tap="clearSearch">清空搜索</button>
			</view>
			
			<!-- 分页加载 -->
			<view class="pagination-section" v-if="hasMore && !listLoading && productList.length > 0">
				<button class="load-more-btn" @tap="loadMore">加载更多</button>
			</view>
		</scroll-view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions" v-if="selectedProducts.length > 0">
			<view class="action-info">
				<text class="total-text">共 {{ getTotalQuantity() }} 件</text>
				<text class="total-amount">¥{{ getTotalAmount() }}</text>
			</view>
			<button class="confirm-btn" @tap="confirmSelection">
				<text class="confirm-text">确认选择</text>
			</button>
		</view>
	</view>
</template>

<script>
import productApi from '../../api/product.js'

export default {
	data() {
		return {
			searchKeyword: '',
			productList: [],
			selectedProducts: [], // 已选择的商品列表
			showCart: false, // 是否显示购物车
			listLoading: false,
			hasMore: true,
			currentPage: 1,
			pageSize: 20,
			searchTimer: null,
			refreshing: false
		}
	},
	
	onLoad() {
		this.loadProducts()
	},
	
	onPullDownRefresh() {
		this.refreshProducts()
	},
	
	onReachBottom() {
		if (this.hasMore && !this.listLoading) {
			this.loadMore()
		}
	},
	
	methods: {
		// 格式化价格
		formatPrice(price) {
			return parseFloat(price || 0).toFixed(2)
		},
		
		// 获取总数量
		getTotalQuantity() {
			return this.selectedProducts.reduce((total, product) => total + product.quantity, 0)
		},
		
		// 获取总金额
		getTotalAmount() {
			const total = this.selectedProducts.reduce((sum, product) => {
				return sum + (product.price * product.quantity)
			}, 0)
			return this.formatPrice(total)
		},
		
		// 检查商品是否已选择
		isProductSelected(productId) {
			return this.selectedProducts.some(p => p.id === productId)
		},
		
		// 获取商品数量
		getProductQuantity(productId) {
			const product = this.selectedProducts.find(p => p.id === productId)
			return product ? product.quantity : 0
		},
		
		// 切换购物车视图
		toggleCartView() {
			this.showCart = !this.showCart
		},
		
		// 添加到购物车
		addToCart(product) {
			const existingProduct = this.selectedProducts.find(p => p.id === product.id)
			if (existingProduct) {
				this.increaseQuantity(product)
			} else {
				this.selectedProducts.push({
					...product,
					quantity: 1
				})
			}
		},
		
		// 从购物车移除
		removeFromCart(index) {
			this.selectedProducts.splice(index, 1)
			
			uni.showToast({
				title: '已移除',
				icon: 'success',
				duration: 1000
			})
		},
		
		// 清空购物车
		clearCart() {
			uni.showModal({
				title: '确认清空',
				content: '确定要清空购物车吗？',
				success: (res) => {
					if (res.confirm) {
						this.selectedProducts = []
						this.showCart = false
						uni.showToast({
							title: '已清空购物车',
							icon: 'success',
							duration: 1000
						})
					}
				}
			})
		},
		
		// 增加商品数量
		increaseQuantity(product) {
			const cartProduct = this.selectedProducts.find(p => p.id === product.id)
			if (cartProduct.stock && cartProduct.quantity >= cartProduct.stock) {
				uni.showToast({
					title: '库存不足',
					icon: 'none'
				})
				return
			}
			cartProduct.quantity++
		},
		
		// 减少商品数量
		decreaseQuantity(product) {
			const cartProduct = this.selectedProducts.find(p => p.id === product.id)
			if (cartProduct.quantity > 1) {
				cartProduct.quantity--
			} else {
				this.removeFromCart(this.selectedProducts.indexOf(cartProduct))
			}
		},
		
		// 确认选择
		confirmSelection() {
			if (this.selectedProducts.length === 0) {
				uni.showToast({
					title: '请选择商品',
					icon: 'none'
				})
				return
			}
			
			// 通过页面栈传递数据
			const pages = getCurrentPages()
			const prevPage = pages[pages.length - 2]
			
			if (prevPage) {
				prevPage.data = prevPage.data || {}
				prevPage.data.selectedProducts = [...this.selectedProducts]
			}
			
			// 返回上一页
			uni.navigateBack()
		},
		
		// 清空搜索
		clearSearch() {
			this.searchKeyword = ''
			this.refreshProducts()
		},
		
		// 加载商品列表
		async loadProducts(isRefresh = false) {
			if (this.listLoading) return
			
			this.listLoading = true
			
			try {
				const params = {
					page: isRefresh ? 1 : this.currentPage,
					per_page: this.pageSize
				}
				
				// 如果有搜索关键词，添加到参数中
				if (this.searchKeyword.trim()) {
					params.keyword = this.searchKeyword.trim()
				}
				
				// 使用统一的商品列表接口，支持搜索参数
				const response = await productApi.getProductList(params)
				const newProducts = response.data.data || []
				
				if (isRefresh) {
					this.productList = newProducts
					this.currentPage = 1
				} else {
					this.productList = [...this.productList, ...newProducts]
				}
				
				// 更新分页信息
				this.hasMore = newProducts.length === this.pageSize
				this.currentPage++
				
			} catch (error) {
				console.error('加载商品列表失败:', error)
				uni.showToast({
					title: this.searchKeyword ? '搜索失败' : '加载失败',
					icon: 'none'
				})
			} finally {
				this.listLoading = false
				if (isRefresh) {
					this.refreshing = false
					uni.stopPullDownRefresh()
				}
			}
		},
		
		// 刷新商品列表
		refreshProducts() {
			this.currentPage = 1
			this.hasMore = true
			this.productList = []
			this.loadProducts(true)
		},
		
		// 加载更多
		loadMore() {
			this.loadProducts()
		},
		
		// 搜索输入处理
		onSearchInput(event) {
			this.searchKeyword = event.detail.value
			
			// 清除之前的定时器
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
			}
			
			// 设置新的定时器
			this.searchTimer = setTimeout(() => {
				this.searchProducts()
			}, 500)
		},
		
		// 搜索商品
		searchProducts() {
			this.refreshProducts()
		},
		
		// 搜索确认处理
		onSearchConfirm(event) {
			this.searchProducts()
		},
		
		// 刷新处理
		onRefresh() {
			this.refreshing = true
			this.refreshProducts()
		}
	}
}
</script>

<style scoped>
.select-product-container {
	background: #f5f7fa;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

/* 固定搜索栏 */
.fixed-search-section {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	z-index: 999;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-box {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 16rpx 20rpx;
	position: relative;
}

.search-icon {
	font-size: 32rpx;
	color: #999999;
	margin-right: 16rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
}

.clear-search-btn {
	position: absolute;
	right: 16rpx;
	width: 40rpx;
	height: 40rpx;
	background: #cccccc;
	border: none;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.clear-icon {
	font-size: 24rpx;
	color: #ffffff;
}

.search-status {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 16rpx;
	padding: 0 8rpx;
}

.search-result-text {
	font-size: 24rpx;
	color: #666666;
}

.search-count {
	font-size: 24rpx;
	color: #007AFF;
	font-weight: 600;
}

/* 内容区域 */
.content-scroll {
	flex: 1;
	margin-top: 140rpx; /* 为固定搜索栏留出空间 */
	padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 已选商品统计 */
.selected-summary {
	background: #ffffff;
	padding: 20rpx;
	margin: 16rpx 20rpx;
	border-radius: 16rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.summary-info {
	flex: 1;
}

.summary-text {
	font-size: 28rpx;
	color: #333333;
	display: block;
	margin-bottom: 8rpx;
}

.summary-amount {
	font-size: 32rpx;
	font-weight: 700;
	color: #007AFF;
}

.view-cart-btn {
	background: #f8f9fa;
	border: 2rpx solid #007AFF;
	color: #007AFF;
	border-radius: 12rpx;
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	font-size: 24rpx;
}

/* 购物车视图 */
.cart-section {
	background: #ffffff;
	margin: 0 20rpx 16rpx;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.cart-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.cart-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
}

.clear-cart-btn {
	background: none;
	border: none;
	color: #dc3545;
	font-size: 28rpx;
	padding: 8rpx 16rpx;
}

.cart-list {
	padding: 0 24rpx 24rpx;
}

.cart-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f8f9fa;
}

.cart-product-image {
	width: 80rpx;
	height: 80rpx;
	border-radius: 8rpx;
	margin-right: 16rpx;
}

.cart-product-info {
	flex: 1;
}

.cart-product-name {
	font-size: 28rpx;
	color: #333333;
	display: block;
	margin-bottom: 8rpx;
}

.cart-product-price {
	font-size: 24rpx;
	color: #666666;
}

.cart-quantity-control {
	display: flex;
	align-items: center;
	margin-right: 16rpx;
}

.quantity-btn {
	width: 48rpx;
	height: 48rpx;
	background: #f8f9fa;
	border: 1rpx solid #e9ecef;
	border-radius: 8rpx;
	font-size: 24rpx;
	color: #333333;
	display: flex;
	align-items: center;
	justify-content: center;
}

.quantity-text {
	min-width: 60rpx;
	text-align: center;
	font-size: 28rpx;
	color: #333333;
	margin: 0 12rpx;
}

.remove-btn {
	width: 48rpx;
	height: 48rpx;
	background: #dc3545;
	border: none;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.remove-icon {
	font-size: 20rpx;
	color: #ffffff;
}

/* 商品列表 */
.product-list {
	padding: 0 20rpx;
}

.product-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.product-item.selected {
	border-color: #007AFF;
	background: #f0f8ff;
}

.product-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 12rpx;
	margin-right: 24rpx;
	background: #f8f9fa;
}

.product-info {
	flex: 1;
}

.product-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
	line-height: 1.4;
}

.product-desc {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 12rpx;
	line-height: 1.4;
}

.product-details {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 12rpx;
}

.product-price {
	font-size: 28rpx;
	font-weight: 700;
	color: #007AFF;
}

.product-unit {
	font-size: 24rpx;
	color: #666666;
}

.product-stock {
	font-size: 24rpx;
	color: #999999;
}

.product-tags {
	display: flex;
	gap: 8rpx;
}

.tag {
	background: #f8f9fa;
	color: #666666;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.product-actions {
	margin-left: 24rpx;
}

.add-btn {
	background: #007AFF;
	color: #ffffff;
	border: none;
	border-radius: 12rpx;
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	font-size: 24rpx;
	font-weight: 600;
}

.add-icon {
	font-size: 28rpx;
}

/* 加载状态 */
.loading-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 0;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid #f0f0f0;
	border-top: 4rpx solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 16rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}

/* 空状态 */
.empty-section {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-icon {
	font-size: 120rpx;
	opacity: 0.3;
	display: block;
	margin-bottom: 24rpx;
}

.empty-text {
	font-size: 32rpx;
	color: #666666;
	display: block;
	margin-bottom: 12rpx;
}

.empty-tip {
	font-size: 28rpx;
	color: #999999;
	margin-bottom: 32rpx;
}

.empty-action {
	background: #007AFF;
	color: #ffffff;
	border: none;
	border-radius: 12rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
}

/* 分页加载 */
.pagination-section {
	text-align: center;
	padding: 40rpx 0;
}

.load-more-btn {
	background: #f8f9fa;
	color: #007AFF;
	border: 2rpx solid #007AFF;
	border-radius: 12rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
}

/* 底部操作栏 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	padding: 24rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
	z-index: 998;
}

.action-info {
	flex: 1;
}

.total-text {
	font-size: 28rpx;
	color: #666666;
	display: block;
	margin-bottom: 4rpx;
}

.total-amount {
	font-size: 36rpx;
	font-weight: 700;
	color: #007AFF;
}

.confirm-btn {
	background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
	color: #ffffff;
	border: none;
	border-radius: 16rpx;
	padding: 20rpx 40rpx;
	font-size: 32rpx;
	font-weight: 700;
	box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.confirm-text {
	font-size: 32rpx;
	font-weight: 700;
}
</style> 