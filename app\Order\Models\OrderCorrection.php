<?php

namespace App\Order\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Employee\Models\Employee;

class OrderCorrection extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'correction_no',
        'status',
        'original_total',
        'corrected_total',
        'difference_amount',
        'correction_type',
        'correction_reason',
        'corrected_by',
        'confirmed_by',
        'corrected_at',
        'confirmed_at',
    ];

    protected $casts = [
        'original_total' => 'decimal:2',
        'corrected_total' => 'decimal:2',
        'difference_amount' => 'decimal:2',
        'corrected_at' => 'datetime',
        'confirmed_at' => 'datetime',
    ];

    /**
     * 关联订单
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * 关联更正明细
     */
    public function items(): HasMany
    {
        return $this->hasMany(OrderCorrectionItem::class, 'correction_id');
    }

    /**
     * 关联操作员
     */
    public function corrector(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'corrected_by');
    }

    /**
     * 关联确认人
     */
    public function confirmer(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'confirmed_by');
    }

    /**
     * 关联付款记录
     */
    public function paymentRecords(): HasMany
    {
        return $this->hasMany(PaymentRecord::class, 'correction_id');
    }

    /**
     * 获取更正类型中文名称
     */
    public function getCorrectionTypeNameAttribute(): string
    {
        $types = [
            'price_increase' => '价格上调',
            'price_decrease' => '价格下调',
            'quantity_increase' => '数量增加',
            'quantity_decrease' => '数量减少',
            'product_change' => '商品变更',
            'increase' => '订单增加',
            'decrease' => '订单减少',
            'no_change' => '无变化',
        ];

        return $types[$this->correction_type] ?? $this->correction_type;
    }

    /**
     * 获取更正前金额
     */
    public function getOriginalAmountAttribute(): float
    {
        return $this->original_total;
    }

    /**
     * 获取更正后金额
     */
    public function getCorrectedAmountAttribute(): float
    {
        return $this->corrected_total;
    }

    /**
     * 获取更正原因
     */
    public function getReasonAttribute(): ?string
    {
        return $this->correction_reason;
    }

    /**
     * 获取备注说明
     */
    public function getNotesAttribute(): ?string
    {
        return $this->correction_reason;
    }

    /**
     * 获取状态中文名称
     */
    public function getStatusNameAttribute(): string
    {
        $statuses = [
            'pending' => '待确认',
            'confirmed' => '已确认',
            'cancelled' => '已取消',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * 是否需要补款
     */
    public function needsSupplement(): bool
    {
        return $this->correction_type === 'increase' && $this->difference_amount > 0;
    }

    /**
     * 是否需要退款
     */
    public function needsRefund(): bool
    {
        return $this->correction_type === 'decrease' && $this->difference_amount < 0;
    }

    /**
     * 生成更正单号
     */
    public static function generateCorrectionNo(): string
    {
        return 'CR' . date('YmdHis') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * 获取结算状态
     * 通过关联的PaymentRecord来计算
     */
    public function getSettlementStatusAttribute(): string
    {
        $records = $this->paymentRecords;
        
        if ($records->isEmpty()) {
            return 'pending'; // 无支付记录，待处理
        }
        
        $pendingCount = $records->where('status', 'pending')->count();
        $failedCount = $records->where('status', 'failed')->count();
        $successCount = $records->where('status', 'success')->count();
        
        if ($pendingCount > 0) {
            return 'processing'; // 有待处理的记录
        }
        
        if ($failedCount > 0 && $successCount === 0) {
            return 'failed'; // 全部失败
        }
        
        if ($successCount > 0 && $failedCount === 0 && $pendingCount === 0) {
            return 'completed'; // 全部成功
        }
        
        return 'partial'; // 部分成功部分失败
    }

    /**
     * 获取结算方式
     * 返回所有成功的支付方式
     */
    public function getSettlementMethodsAttribute(): array
    {
        return $this->paymentRecords()
            ->where('status', 'success')
            ->pluck('payment_method')
            ->unique()
            ->toArray();
    }

    /**
     * 获取主要结算方式（第一个成功的）
     */
    public function getPrimarySettlementMethodAttribute(): ?string
    {
        $methods = $this->settlement_methods;
        return $methods[0] ?? null;
    }

    /**
     * 获取结算完成时间
     */
    public function getSettlementCompletedAtAttribute(): ?string
    {
        $latestRecord = $this->paymentRecords()
            ->where('status', 'success')
            ->orderBy('settlement_at', 'desc')
            ->first();
            
        return $latestRecord?->settlement_at;
    }

    /**
     * 是否已完成结算
     */
    public function isSettlementCompleted(): bool
    {
        return $this->settlement_status === 'completed';
    }

    /**
     * 是否需要结算处理
     */
    public function needsSettlement(): bool
    {
        return $this->status === 'confirmed' && 
               $this->correction_type !== 'no_change' &&
               !$this->isSettlementCompleted();
    }

    /**
     * 获取待处理的支付记录
     */
    public function getPendingPaymentRecords()
    {
        return $this->paymentRecords()->where('status', 'pending')->get();
    }

    /**
     * 获取结算总金额
     */
    public function getSettlementAmountAttribute(): float
    {
        return $this->paymentRecords()
            ->where('status', 'success')
            ->sum('amount');
    }
} 