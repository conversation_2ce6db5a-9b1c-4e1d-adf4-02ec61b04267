<?php

namespace App\Unit\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class UnitConversionGraph extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'type', 'is_default', 'description'
    ];

    protected $casts = [
        'is_default' => 'boolean',
    ];

    /**
     * 获取图的边
     */
    public function edges(): HasMany
    {
        return $this->hasMany(UnitConversionEdge::class, 'graph_id');
    }
    
    /**
     * 获取类型的默认转换图
     *
     * @param string $type 单位类型
     * @return self|null
     */
    public static function getDefaultForType(string $type): ?self
    {
        return static::where('type', $type)
                    ->where('is_default', true)
                    ->first();
    }

    /**
     * 设置为默认转换图
     *
     * @return self
     */
    public function setAsDefault(): self
    {
        // 先将同类型的其他图设为非默认
        self::where('type', $this->type)
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);
        
        $this->is_default = true;
        $this->save();
        
        return $this;
    }
} 