<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('search_logs', function (Blueprint $table) {
            $table->id();
            $table->string('keyword', 255)->index()->comment('搜索关键词');
            $table->unsignedBigInteger('user_id')->nullable()->index()->comment('用户ID');
            $table->string('session_id', 100)->nullable()->index()->comment('会话ID');
            $table->string('ip_address', 45)->nullable()->comment('IP地址');
            $table->text('user_agent')->nullable()->comment('用户代理');
            $table->integer('result_count')->default(0)->comment('结果数量');
            $table->string('platform', 20)->default('web')->comment('平台');
            $table->string('source', 20)->default('search')->comment('来源');
            $table->timestamps();
            
            // 添加索引
            $table->index('created_at');
            $table->index(['platform', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('search_logs');
    }
}; 