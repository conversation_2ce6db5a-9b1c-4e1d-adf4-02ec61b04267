<!-- components/product-item/product-item.wxml -->
<view class="product-item {{mode === 'list' ? 'list-mode' : 'grid-mode'}}" bindtap="onTap">
  <view class="product-image-container">
    <image class="product-image" src="{{product.image || '/images/common/placeholder.png'}}" mode="aspectFill" lazy-load></image>
    <view class="product-tags" wx:if="{{product.tags && product.tags.length > 0}}">
      <view class="product-tag" wx:for="{{product.tags}}" wx:key="index">{{item}}</view>
    </view>
  </view>
  <view class="product-info">
    <view class="product-title">{{product.title || '商品名称'}}</view>
    <view class="product-price-row">
      <view class="product-price">
        <text class="price-symbol">¥</text>
        <text class="price-value">{{product.price || '0.00'}}</text>
        <text class="price-original" wx:if="{{product.originalPrice}}">¥{{product.originalPrice}}</text>
      </view>
      <view class="product-sales" wx:if="{{mode === 'list'}}">销量 {{product.sales || 0}}</view>
    </view>
    <view class="product-bottom-row" wx:if="{{mode === 'list'}}">
      <view class="product-desc" wx:if="{{product.desc}}">{{product.desc}}</view>
      <view class="product-cart-btn">
        <van-icon name="cart-o" size="40rpx" color="#4CAF50" catchtap="onAddToCart" />
      </view>
    </view>
  </view>
</view> 