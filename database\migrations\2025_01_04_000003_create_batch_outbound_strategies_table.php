<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('batch_outbound_strategies', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('策略名称');
            $table->string('code', 50)->unique()->comment('策略代码');
            $table->text('description')->nullable()->comment('策略描述');
            
            $table->enum('strategy_type', [
                'fifo',         // 先进先出
                'lifo',         // 后进先出
                'fefo',         // 先过期先出
                'fmfo',         // 先生产先出
                'manual',       // 手动选择
                'quality_first', // 质量优先
                'cost_optimized', // 成本优化
                'custom'        // 自定义
            ])->comment('策略类型');
            
            $table->json('strategy_rules')->nullable()->comment('策略规则配置');
            $table->json('priority_factors')->nullable()->comment('优先级因子配置');
            
            // 适用范围
            $table->enum('scope', ['global', 'category', 'product', 'warehouse'])->default('global')->comment('适用范围');
            $table->foreignId('category_id')->nullable()->constrained('categories')->comment('分类ID');
            $table->foreignId('product_id')->nullable()->constrained('products')->comment('商品ID');
            $table->foreignId('warehouse_id')->nullable()->constrained('warehouses')->comment('仓库ID');
            
            // 策略配置
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->integer('priority')->default(0)->comment('优先级（数字越大优先级越高）');
            $table->boolean('is_default')->default(false)->comment('是否为默认策略');
            
            // 条件限制
            $table->json('conditions')->nullable()->comment('应用条件');
            $table->decimal('min_quantity', 10, 2)->nullable()->comment('最小数量限制');
            $table->decimal('max_quantity', 10, 2)->nullable()->comment('最大数量限制');
            
            $table->foreignId('created_by')->constrained('users')->comment('创建人');
            $table->timestamps();
            
            // 索引
            $table->index(['strategy_type', 'is_active']);
            $table->index(['scope', 'is_active']);
            $table->index(['category_id', 'is_active']);
            $table->index(['product_id', 'is_active']);
            $table->index(['warehouse_id', 'is_active']);
            $table->index(['priority', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('batch_outbound_strategies');
    }
}; 