<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 检查并添加可能缺失的列
            if (!Schema::hasColumn('products', 'unit_conversion_graph_id')) {
                $table->unsignedBigInteger('unit_conversion_graph_id')->nullable()->comment('单位转换图ID');
                $table->foreign('unit_conversion_graph_id')->references('id')->on('unit_conversion_graphs')->onDelete('set null');
            }
            
            // 添加显示单位字段（用于前端显示）
            if (!Schema::hasColumn('products', 'display_unit_id')) {
                $table->unsignedBigInteger('display_unit_id')->nullable()->comment('显示单位ID');
                $table->foreign('display_unit_id')->references('id')->on('units')->onDelete('set null');
            }
            
            // 产品单位设置json字段
            if (!Schema::hasColumn('products', 'unit_settings')) {
                $table->json('unit_settings')->nullable()->comment('单位设置(包括默认单位角色等)');
            }
            
            // 产品多单位启用状态
            if (!Schema::hasColumn('products', 'multi_unit_enabled')) {
                $table->boolean('multi_unit_enabled')->default(true)->comment('是否启用多单位');
            }
        });
        
        // 将现有的base_unit_id迁移到unit_settings中
        $this->migrateBaseUnitToSettings();
        
        // 设置默认的单位转换图
        $this->setDefaultConversionGraph();
    }

    /**
     * 将base_unit_id迁移到unit_settings中
     */
    private function migrateBaseUnitToSettings()
    {
        // 检查products是否有base_unit_id字段
        if (Schema::hasColumn('products', 'base_unit_id')) {
            // 获取所有产品
            $products = DB::table('products')->whereNotNull('base_unit_id')->get();
            
            foreach ($products as $product) {
                // 构建unit_settings
                $unitSettings = [
                    'base_unit_id' => $product->base_unit_id,
                    'roles' => [
                        'sale' => null,
                        'purchase' => null,
                        'inventory' => $product->base_unit_id,
                        'display' => $product->display_unit_id ?? $product->base_unit_id
                    ]
                ];
                
                // 更新记录
                DB::table('products')
                    ->where('id', $product->id)
                    ->update([
                        'unit_settings' => json_encode($unitSettings),
                        'display_unit_id' => $product->display_unit_id ?? $product->base_unit_id
                    ]);
            }
        }
    }

    /**
     * 设置默认的单位转换图
     */
    private function setDefaultConversionGraph()
    {
        // 检查是否有unit_conversion_graphs表
        if (Schema::hasTable('unit_conversion_graphs')) {
            // 获取所有产品及其基本单位
            $products = DB::table('products')
                         ->whereNotNull('base_unit_id')
                         ->get(['id', 'base_unit_id']);
            
            foreach ($products as $product) {
                // 获取单位类型
                $unitType = DB::table('units')
                             ->where('id', $product->base_unit_id)
                             ->value('type');
                
                if ($unitType) {
                    // 获取该类型的默认转换图
                    $graphId = DB::table('unit_conversion_graphs')
                                ->where('type', $unitType)
                                ->where('is_default', true)
                                ->value('id');
                    
                    if ($graphId) {
                        // 更新产品
                        DB::table('products')
                            ->where('id', $product->id)
                            ->update(['unit_conversion_graph_id' => $graphId]);
                    }
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 删除外键和列
            if (Schema::hasColumn('products', 'unit_conversion_graph_id')) {
                $table->dropForeign(['unit_conversion_graph_id']);
                $table->dropColumn('unit_conversion_graph_id');
            }
            
            if (Schema::hasColumn('products', 'display_unit_id')) {
                $table->dropForeign(['display_unit_id']);
                $table->dropColumn('display_unit_id');
            }
            
            if (Schema::hasColumn('products', 'unit_settings')) {
                $table->dropColumn('unit_settings');
            }
            
            if (Schema::hasColumn('products', 'multi_unit_enabled')) {
                $table->dropColumn('multi_unit_enabled');
            }
        });
    }
};
