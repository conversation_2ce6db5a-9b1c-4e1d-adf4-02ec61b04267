<?php

namespace App\Warehouse\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Warehouse\Models\Warehouse;
use App\Inventory\Models\Inventory;
use App\Inventory\Models\InventoryAlert;
use App\Product\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class WarehouseController extends Controller
{
    /**
     * 获取仓库列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Warehouse::query();

        // 搜索关键词
        if ($request->filled('keyword')) {
            $keyword = $request->keyword;
            $query->where(function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('code', 'like', "%{$keyword}%")
                  ->orWhere('location', 'like', "%{$keyword}%")
                  ->orWhere('manager', 'like', "%{$keyword}%");
            });
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 库存策略筛选
        if ($request->filled('policy')) {
            $query->where('inventory_policy', $request->policy);
        }

        // 排序
        $orderBy = $request->get('order_by', 'created_at');
        $direction = $request->get('direction', 'desc');
        $query->orderBy($orderBy, $direction);

        // 分页
        $perPage = $request->get('per_page', 20);
        $warehouses = $query->paginate($perPage);

        // 添加计算字段到每个仓库
        foreach ($warehouses->items() as $warehouse) {
            $warehouse->utilization_rate = $warehouse->utilization_rate;
            $warehouse->product_count = $warehouse->product_count;
            $warehouse->total_value = $warehouse->total_value;
            $warehouse->active_alerts_count = $warehouse->active_alerts_count;
            
            // 获取预警信息
            $warehouse->alerts = $warehouse->alerts()
                ->where('status', 'active')
                ->select('id', 'alert_type', 'message', 'severity')
                ->get();
        }

        return response()->json(ApiResponse::success($warehouses));
    }

    /**
     * 获取仓库统计数据
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats()
    {
        $stats = [
            'totalWarehouses' => Warehouse::count(),
            'activeWarehouses' => Warehouse::where('status', 'active')->count(),
            'totalCapacity' => Warehouse::sum('capacity'),
            'totalUsedCapacity' => Warehouse::sum('used_capacity'),
            'averageUtilization' => 0,
            'alertCount' => InventoryAlert::where('status', 'active')->count(),
            'totalValue' => 0,
        ];

        // 计算平均利用率
        $warehouses = Warehouse::where('capacity', '>', 0)->get();
        if ($warehouses->count() > 0) {
            $totalUtilization = $warehouses->sum(function ($warehouse) {
                return $warehouse->utilization_rate;
            });
            $stats['averageUtilization'] = round($totalUtilization / $warehouses->count());
        }

        // 计算总价值
        $stats['totalValue'] = Warehouse::all()->sum(function ($warehouse) {
            return $warehouse->total_value;
        });

        return response()->json(ApiResponse::success($stats));
    }
    
    /**
     * 创建仓库
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // 验证输入
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'location' => 'required|string|max:255',
            'manager' => 'nullable|string|max:255',
            'contact' => 'nullable|string|max:255',
            'capacity' => 'nullable|numeric|min:0',
            'inventory_policy' => 'nullable|in:inherit,strict,allow_negative,unlimited',
            'status' => 'nullable|in:active,warning,inactive',
            'description' => 'nullable|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        // 自动生成编码
        $code = $request->code ?: Warehouse::generateCode();
        
        // 创建仓库
        $warehouse = Warehouse::create([
            'name' => $request->name,
            'code' => $code,
            'location' => $request->location,
            'manager' => $request->manager,
            'contact' => $request->contact,
            'capacity' => $request->capacity ?: 0,
            'used_capacity' => 0,
            'status' => $request->status ?: 'active',
            'description' => $request->description,
            'inventory_policy' => $request->inventory_policy ?: 'inherit',
            'total_stock' => 0,
        ]);
        
        return response()->json(ApiResponse::success($warehouse, '仓库创建成功'), 201);
    }
    
    /**
     * 获取仓库详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $warehouse = Warehouse::with(['products', 'alerts'])->findOrFail($id);
        
        // 添加计算字段
        $warehouse->utilization_rate = $warehouse->utilization_rate;
        $warehouse->product_count = $warehouse->product_count;
        $warehouse->total_value = $warehouse->total_value;
        $warehouse->active_alerts_count = $warehouse->active_alerts_count;
        
        return response()->json(ApiResponse::success($warehouse));
    }
    
    /**
     * 更新仓库信息
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $warehouse = Warehouse::findOrFail($id);
        
        // 验证输入
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'code' => 'sometimes|required|string|max:255|unique:warehouses,code,' . $id,
            'location' => 'sometimes|required|string|max:255',
            'manager' => 'nullable|string|max:255',
            'contact' => 'nullable|string|max:255',
            'capacity' => 'nullable|numeric|min:0',
            'inventory_policy' => 'nullable|in:inherit,strict,allow_negative,unlimited',
            'status' => 'nullable|in:active,warning,inactive',
            'description' => 'nullable|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        // 更新仓库信息
        $warehouse->update($request->only([
            'name', 'code', 'location', 'manager', 'contact', 
            'capacity', 'inventory_policy', 'status', 'description'
        ]));
        
        // 检查并更新状态
        $warehouse->checkStatus();
        
        return response()->json(ApiResponse::success($warehouse, '仓库更新成功'));
    }
    
    /**
     * 删除仓库
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $warehouse = Warehouse::findOrFail($id);
        
        // 检查是否有库存
        if ($warehouse->inventories()->exists()) {
            return response()->json(ApiResponse::error('仓库中还有库存，无法删除', 400), 400);
        }
        
        $warehouse->delete();
        
        return response()->json(ApiResponse::success(null, '仓库删除成功'));
    }

    /**
     * 获取仓库库存信息
     *
     * @param int $warehouseId
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function inventory($warehouseId, Request $request)
    {
        $warehouse = Warehouse::findOrFail($warehouseId);
        
        $query = $warehouse->inventories()->with('product');
        
        // 搜索
        if ($request->filled('keyword')) {
            $keyword = $request->keyword;
            $query->whereHas('product', function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('sku', 'like', "%{$keyword}%");
            });
        }
        
        // 分页
        $perPage = $request->get('per_page', 20);
        $inventories = $query->paginate($perPage);
        
        return response()->json(ApiResponse::success($inventories));
    }

    /**
     * 批量操作
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchOperation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:delete,update_status,update_policy',
            'warehouse_ids' => 'required|array',
            'warehouse_ids.*' => 'exists:warehouses,id',
            'data' => 'sometimes|array',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $warehouseIds = $request->warehouse_ids;
        $action = $request->action;
        $data = $request->data ?: [];
        
        DB::beginTransaction();
        
        try {
            switch ($action) {
                case 'delete':
                    // 检查是否有库存
                    $hasInventory = Inventory::whereIn('warehouse_id', $warehouseIds)->exists();
                    if ($hasInventory) {
                        throw new \Exception('选中的仓库中有库存，无法删除');
                    }
                    Warehouse::whereIn('id', $warehouseIds)->delete();
                    break;
                    
                case 'update_status':
                    if (!isset($data['status'])) {
                        throw new \Exception('缺少状态参数');
                    }
                    Warehouse::whereIn('id', $warehouseIds)->update(['status' => $data['status']]);
                    break;
                    
                case 'update_policy':
                    if (!isset($data['inventory_policy'])) {
                        throw new \Exception('缺少库存策略参数');
                    }
                    Warehouse::whereIn('id', $warehouseIds)->update(['inventory_policy' => $data['inventory_policy']]);
                    break;
            }
            
            DB::commit();
            return response()->json(ApiResponse::success(null, '批量操作成功'));
            
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(ApiResponse::error($e->getMessage(), 400), 400);
        }
    }
} 