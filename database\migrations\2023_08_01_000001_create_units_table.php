<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('units', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique()->comment('单位代码，例如kg, g');
            $table->string('display_name')->comment('显示名称，例如公斤，克');
            $table->string('category')->comment('单位类别：重量、体积、数量等');
            $table->boolean('is_base')->default(false)->comment('是否为基本单位');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('units');
    }
}; 