<?php

namespace App\shop\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Product\Models\Product;
use App\Product\Services\PriceCalculationService;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PriceController extends Controller
{
    protected $priceService;

    public function __construct(PriceCalculationService $priceService)
    {
        $this->priceService = $priceService;
    }

    /**
     * 获取单个商品价格
     * 支持游客和登录用户
     */
    public function getProductPrice(Request $request, $productId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'region_id' => 'nullable|integer|exists:regions,id',
                'quantity' => 'nullable|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }

            $product = Product::find($productId);
            if (!$product) {
                return response()->json(ApiResponse::error('商品不存在', 404), 404);
            }

            // 获取当前用户（可能为null，表示游客）
            $user = Auth::guard('sanctum')->user();
            $regionId = $user?->region_id ?? $request->input('region_id');
            $quantity = $request->input('quantity', 1);

            // 计算价格
            $priceData = $this->priceService->calculatePrice($product, $user, $regionId, $quantity);

            // 添加商品基本信息
            $response = [
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'code' => $product->code,
                    'category_id' => $product->category_id,
                ],
                'pricing' => $priceData,
                'context' => [
                    'is_logged_in' => $user !== null,
                    'user_id' => $user?->id,
                    'membership_level' => $user?->membershipLevel?->name,
                    'region_id' => $regionId,
                    'quantity' => $quantity,
                    'calculated_at' => now()->toISOString(),
                ]
            ];

            return response()->json(ApiResponse::success($response));

        } catch (\Exception $e) {
            Log::error('获取商品价格失败', [
                'product_id' => $productId,
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            return response()->json(ApiResponse::error('获取价格失败'), 500);
        }
    }

    /**
     * 批量获取商品价格
     * 用于商品列表页面
     */
    public function getBatchPrices(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_ids' => 'required|array|max:50',
                'product_ids.*' => 'integer|exists:products,id',
                'region_id' => 'nullable|integer|exists:regions,id',
            ]);

            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }

            $productIds = $request->input('product_ids');
            $user = Auth::guard('sanctum')->user();
            $regionId = $user?->region_id ?? $request->input('region_id');

            // 批量计算价格
            $prices = $this->priceService->calculateBatchPrices($productIds, $user, $regionId);

            $response = [
                'prices' => $prices,
                'context' => [
                    'is_logged_in' => $user !== null,
                    'user_id' => $user?->id,
                    'membership_level' => $user?->membershipLevel?->name,
                    'region_id' => $regionId,
                    'product_count' => count($productIds),
                    'calculated_at' => now()->toISOString(),
                ]
            ];

            return response()->json(ApiResponse::success($response));

        } catch (\Exception $e) {
            Log::error('批量获取商品价格失败', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            return response()->json(ApiResponse::error('获取价格失败'), 500);
        }
    }

    /**
     * 获取用户的价格优惠信息
     * 用于显示用户可享受的优惠
     */
    public function getUserDiscountInfo(Request $request)
    {
        try {
            $user = Auth::guard('sanctum')->user();
            
            if (!$user) {
                return response()->json(ApiResponse::error('请先登录', 401), 401);
            }

            $discountInfo = [
                'membership_level' => [
                    'id' => $user->membershipLevel?->id,
                    'name' => $user->membershipLevel?->name ?? '普通会员',
                    'discount_rate' => $user->membershipLevel?->discount_rate ?? 0,
                    'privileges' => $user->membershipLevel?->privileges ?? [],
                ],
                'available_discounts' => [
                    'global_member_discount' => $user->membershipLevel?->discount_rate > 0,
                    'category_specific_discounts' => $this->getCategoryDiscounts($user),
                    'product_specific_discounts' => $this->getProductDiscounts($user),
                ],
                'upgrade_info' => $this->getUpgradeInfo($user),
            ];

            return response()->json(ApiResponse::success($discountInfo));

        } catch (\Exception $e) {
            Log::error('获取用户优惠信息失败', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            return response()->json(ApiResponse::error('获取优惠信息失败'), 500);
        }
    }

    /**
     * 价格变更通知
     * 当用户登录状态改变时，通知前端更新价格
     */
    public function priceChangeNotification(Request $request)
    {
        try {
            $user = Auth::guard('sanctum')->user();
            
            // 清除用户相关的价格缓存
            $this->priceService->clearPriceCache(null, $user?->id);

            $response = [
                'message' => '价格缓存已更新',
                'user_status' => [
                    'is_logged_in' => $user !== null,
                    'membership_level' => $user?->membershipLevel?->name,
                    'discount_rate' => $user?->membershipLevel?->discount_rate ?? 0,
                ],
                'action_required' => 'refresh_prices', // 前端需要重新获取价格
            ];

            return response()->json(ApiResponse::success($response));

        } catch (\Exception $e) {
            Log::error('价格变更通知失败', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            return response()->json(ApiResponse::error('通知失败'), 500);
        }
    }

    /**
     * 获取分类优惠信息
     */
    private function getCategoryDiscounts(User $user): array
    {
        if (!$user->membership_level_id) {
            return [];
        }

        $categoryDiscounts = \App\Product\Models\CategoryMemberDiscount::where('membership_level_id', $user->membership_level_id)
            ->where('status', true)
            ->where(function($query) {
                $query->whereNull('start_time')
                      ->orWhere('start_time', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_time')
                      ->orWhere('end_time', '>=', now());
            })
            ->with('category:id,name')
            ->get()
            ->map(function($discount) {
                return [
                    'category_id' => $discount->category_id,
                    'category_name' => $discount->category->name,
                    'discount_type' => $discount->discount_type,
                    'discount_value' => $discount->discount_value,
                    'max_discount' => $discount->max_discount,
                ];
            })
            ->toArray();

        return $categoryDiscounts;
    }

    /**
     * 获取商品优惠信息
     */
    private function getProductDiscounts(User $user): array
    {
        if (!$user->membership_level_id) {
            return [];
        }

        $productDiscounts = \App\Product\Models\ProductMemberDiscount::where('membership_level_id', $user->membership_level_id)
            ->where('status', true)
            ->where(function($query) {
                $query->whereNull('start_time')
                      ->orWhere('start_time', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_time')
                      ->orWhere('end_time', '>=', now());
            })
            ->with('product:id,name')
            ->get()
            ->map(function($discount) {
                return [
                    'product_id' => $discount->product_id,
                    'product_name' => $discount->product->name,
                    'discount_type' => $discount->discount_type,
                    'discount_value' => $discount->discount_value,
                    'max_discount' => $discount->max_discount,
                ];
            })
            ->toArray();

        return $productDiscounts;
    }

    /**
     * 获取升级信息
     */
    private function getUpgradeInfo(User $user): array
    {
        $currentLevel = $user->membershipLevel;
        if (!$currentLevel) {
            return [];
        }

        // 获取下一个等级
        $nextLevel = \App\Crm\Models\MembershipLevel::where('status', true)
            ->where('sort_order', '>', $currentLevel->sort_order)
            ->orderBy('sort_order')
            ->first();

        if (!$nextLevel) {
            return ['message' => '您已是最高等级会员'];
        }

        return [
            'next_level' => [
                'name' => $nextLevel->name,
                'discount_rate' => $nextLevel->discount_rate,
                'upgrade_points' => $nextLevel->upgrade_points,
                'upgrade_amount' => $nextLevel->upgrade_amount,
                'quick_upgrade_amount' => $nextLevel->quick_upgrade_amount,
            ],
            'progress' => [
                'current_points' => $user->member_points,
                'points_needed' => max(0, $nextLevel->upgrade_points - $user->member_points),
                'current_spend' => $user->total_spend,
                'spend_needed' => max(0, $nextLevel->upgrade_amount - $user->total_spend),
            ]
        ];
    }
} 