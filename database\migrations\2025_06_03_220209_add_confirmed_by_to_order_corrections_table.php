<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_corrections', function (Blueprint $table) {
            $table->unsignedBigInteger('confirmed_by')->nullable()->comment('确认人ID')->after('corrected_by');
            $table->foreign('confirmed_by')->references('id')->on('employees')->onDelete('set null');
            $table->index('confirmed_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_corrections', function (Blueprint $table) {
            $table->dropForeign(['confirmed_by']);
            $table->dropIndex(['confirmed_by']);
            $table->dropColumn('confirmed_by');
        });
    }
};
