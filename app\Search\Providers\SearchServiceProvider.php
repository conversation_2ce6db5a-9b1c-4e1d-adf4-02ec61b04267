<?php

namespace App\Search\Providers;

use Illuminate\Support\ServiceProvider;

class SearchServiceProvider extends ServiceProvider
{
    /**
     * 注册搜索模块服务
     *
     * @return void
     */
    public function register()
    {
        // 注册搜索服务
        $this->app->singleton('search.service', function ($app) {
            return new \App\Search\Services\SearchService();
        });
        
        // 注册搜索仓库
        $this->app->singleton('search.repository', function ($app) {
            return new \App\Search\Repositories\SearchRepository();
        });
    }

    /**
     * 启动搜索模块服务
     *
     * @return void
     */
    public function boot()
    {
        // 加载路由
        $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
    }
} 