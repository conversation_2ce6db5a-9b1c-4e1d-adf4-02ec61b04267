<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('商品名称');
            $table->decimal('price', 10, 2)->comment('商品价格');
            $table->text('description')->nullable()->comment('商品描述');
            $table->integer('stock')->default(0)->comment('基本库存量');
            $table->enum('base_unit', ['kg', 'pcs', 'g', 'lb'])->default('pcs')->comment('基本单位（如：公斤，件，克，磅）');
            $table->decimal('unit_conversion_rate', 10, 2)->default(1)->comment('单位转换率，转换为基础单位（如：1斤 = 0.5kg）');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
}; 