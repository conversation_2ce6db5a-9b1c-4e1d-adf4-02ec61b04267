/**
 * 统一的时间格式化工具
 * 格式：年-月-日 时:分:秒
 */

/**
 * 格式化时间为标准格式：YYYY-MM-DD HH:mm:ss
 * @param {string|Date} dateInput - 时间字符串或Date对象
 * @returns {string} 格式化后的时间字符串
 */
export function formatDateTime(dateInput) {
	if (!dateInput) return '暂无'
	
	let date
	if (typeof dateInput === 'string') {
		// 处理各种时间字符串格式
		date = new Date(dateInput)
	} else if (dateInput instanceof Date) {
		date = dateInput
	} else {
		return '暂无'
	}
	
	// 检查日期是否有效
	if (isNaN(date.getTime())) {
		return '暂无'
	}
	
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	const day = String(date.getDate()).padStart(2, '0')
	const hours = String(date.getHours()).padStart(2, '0')
	const minutes = String(date.getMinutes()).padStart(2, '0')
	const seconds = String(date.getSeconds()).padStart(2, '0')
	
	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/**
 * 格式化日期为标准格式：YYYY-MM-DD
 * @param {string|Date} dateInput - 时间字符串或Date对象
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(dateInput) {
	if (!dateInput) return '暂无'
	
	let date
	if (typeof dateInput === 'string') {
		date = new Date(dateInput)
	} else if (dateInput instanceof Date) {
		date = dateInput
	} else {
		return '暂无'
	}
	
	// 检查日期是否有效
	if (isNaN(date.getTime())) {
		return '暂无'
	}
	
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	const day = String(date.getDate()).padStart(2, '0')
	
	return `${year}-${month}-${day}`
}

/**
 * 格式化时间为相对时间（保持原有的相对时间逻辑，但最终显示标准格式）
 * @param {string|Date} dateInput - 时间字符串或Date对象
 * @returns {string} 格式化后的时间字符串
 */
export function formatRelativeTime(dateInput) {
	if (!dateInput) return '暂无'
	
	let date
	if (typeof dateInput === 'string') {
		date = new Date(dateInput)
	} else if (dateInput instanceof Date) {
		date = dateInput
	} else {
		return '暂无'
	}
	
	// 检查日期是否有效
	if (isNaN(date.getTime())) {
		return '暂无'
	}
	
	const now = new Date()
	const diffMs = now - date
	const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
	
	// 如果是今天，显示具体时间
	if (diffDays === 0) {
		const hours = String(date.getHours()).padStart(2, '0')
		const minutes = String(date.getMinutes()).padStart(2, '0')
		return `今天 ${hours}:${minutes}`
	}
	
	// 如果是昨天
	if (diffDays === 1) {
		const hours = String(date.getHours()).padStart(2, '0')
		const minutes = String(date.getMinutes()).padStart(2, '0')
		return `昨天 ${hours}:${minutes}`
	}
	
	// 其他情况显示完整日期时间
	return formatDateTime(date)
}

/**
 * 格式化日期范围
 * @param {string|Date} startDate - 开始日期
 * @param {string|Date} endDate - 结束日期
 * @returns {string} 格式化后的日期范围字符串
 */
export function formatDateRange(startDate, endDate) {
	const start = formatDate(startDate)
	const end = formatDate(endDate)
	
	if (start === '暂无' || end === '暂无') {
		return '日期范围无效'
	}
	
	return `${start} 至 ${end}`
}

/**
 * 获取当前时间的标准格式
 * @returns {string} 当前时间的标准格式字符串
 */
export function getCurrentDateTime() {
	return formatDateTime(new Date())
}

/**
 * 获取当前日期的标准格式
 * @returns {string} 当前日期的标准格式字符串
 */
export function getCurrentDate() {
	return formatDate(new Date())
}

// 默认导出主要的格式化函数
export default {
	formatDateTime,
	formatDate,
	formatRelativeTime,
	formatDateRange,
	getCurrentDateTime,
	getCurrentDate
} 