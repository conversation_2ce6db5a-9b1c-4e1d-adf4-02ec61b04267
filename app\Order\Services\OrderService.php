<?php

namespace App\Order\Services;

use App\Order\Models\Order;
use App\Order\Models\OrderItem;
use App\Order\Models\OrderCorrection;
use App\Product\Models\Product;
use App\Models\User;
use App\Crm\Models\UserAddress;
use App\Region\Services\RegionPriceService;
use App\Payment\Services\PaymentOfferService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderService
{
    /**
     * 区域价格服务
     *
     * @var RegionPriceService
     */
    protected $regionPriceService;

    /**
     * 支付优惠服务
     *
     * @var PaymentOfferService
     */
    protected $paymentOfferService;

    /**
     * 构造函数
     *
     * @param RegionPriceService $regionPriceService
     * @param PaymentOfferService $paymentOfferService
     */
    public function __construct(
        RegionPriceService $regionPriceService = null,
        PaymentOfferService $paymentOfferService = null
    ) {
        $this->regionPriceService = $regionPriceService ?? app(RegionPriceService::class);
        $this->paymentOfferService = $paymentOfferService ?? app(PaymentOfferService::class);
    }

    /**
     * 获取订单列表
     *
     * @param array $filters 过滤条件
     * @param User|null $user 当前用户
     * @param int $perPage 每页数量
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getOrders($filters = [], $user = null, $perPage = 20)
    {
        // 优化查询：加载必要的关联数据，包括商品信息和更正信息
        $query = Order::with([
            'user:id,name,phone,merchant_name',
            'items:id,order_id,product_id,product_name,product_sku,quantity,price,total,unit_id',
            'items.product:id,name,price',
            'items.product.images:id,product_id,url,is_main',
            'items.unit:id,name,symbol',
            // 添加合并相关关联
            'orderMerge:id,merged_order_id,original_order_ids'
        ])
        ->withCount('items as items_count') // 添加商品数量统计
        ->withCount([
            'corrections as corrections_count',
            'corrections as pending_corrections_count' => function($query) {
                $query->where('status', 'pending');
            },
            'corrections as confirmed_corrections_count' => function($query) {
                $query->where('status', 'confirmed');
            }
        ])
        ->with(['corrections' => function($query) {
            $query->latest(); // 加载所有更正记录，按时间倒序
        }])
        // 添加计算字段
        ->selectRaw('orders.*, 
            CASE 
                WHEN (SELECT COUNT(*) FROM order_corrections WHERE order_id = orders.id AND status = "confirmed") > 0 THEN "confirmed"
                WHEN (SELECT COUNT(*) FROM order_corrections WHERE order_id = orders.id AND status = "cancelled") > 0 THEN "cancelled"
                ELSE "none"
            END as correction_status,
            CASE 
                WHEN (SELECT COUNT(*) FROM order_corrections WHERE order_id = orders.id AND status = "confirmed") > 0 THEN "已更正"
                WHEN (SELECT COUNT(*) FROM order_corrections WHERE order_id = orders.id AND status = "cancelled") > 0 THEN "已取消"
                ELSE "未更正"
            END as correction_status_text,
            CASE 
                WHEN (SELECT COUNT(*) FROM order_corrections WHERE order_id = orders.id AND status = "confirmed") > 0 THEN 1
                ELSE 0
            END as is_corrected
        ');
        
        // 状态筛选
        if (isset($filters['status']) && !empty($filters['status'])) {
            if ($filters['status'] === 'merged') {
                // 显示被合并的原订单
                $query->where('is_merged_from', true);
            } else {
                $query->where('status', $filters['status']);
            }
        }

        // 合并订单筛选 - 默认不显示被合并的原订单
        if (!isset($filters['show_merged_orders']) || !$filters['show_merged_orders']) {
            $query->where('is_merged_from', false);
        }
        
        // 只看可合并订单筛选
        if (isset($filters['only_mergeable']) && $filters['only_mergeable']) {
            $this->applyMergeableFilter($query);
        }
        
        // 关键词搜索
        if (isset($filters['keyword']) && !empty($filters['keyword'])) {
            $keyword = $filters['keyword'];
            $query->where(function($q) use ($keyword) {
                $q->where('order_no', 'like', "%{$keyword}%")
                  ->orWhere('contact_name', 'like', "%{$keyword}%")
                  ->orWhere('contact_phone', 'like', "%{$keyword}%");
            });
        }
        
        // 商家名称筛选
        if (isset($filters['merchant_name']) && !empty($filters['merchant_name'])) {
            $merchantName = $filters['merchant_name'];
            $query->whereHas('user', function($q) use ($merchantName) {
                $q->where('merchant_name', 'like', "%{$merchantName}%");
            });
        }
        
        // 日期范围筛选 - 支持多种日期字段
        if (isset($filters['start_date']) && !empty($filters['start_date']) && 
            isset($filters['end_date']) && !empty($filters['end_date'])) {
            // 确保日期格式正确，添加时间部分
            $startDateTime = $filters['start_date'] . ' 00:00:00';
            $endDateTime = $filters['end_date'] . ' 23:59:59';
            $query->whereBetween('created_at', [$startDateTime, $endDateTime]);
            
            // 添加调试日志
            Log::info('订单日期筛选', [
                'start_date' => $filters['start_date'],
                'end_date' => $filters['end_date'],
                'start_datetime' => $startDateTime,
                'end_datetime' => $endDateTime
            ]);
        }
        
        // 支持按送达日期筛选
        if (isset($filters['delivery_date']) && !empty($filters['delivery_date'])) {
            $query->whereDate('delivery_date', $filters['delivery_date']);
        }
        
        // 更正状态筛选
        if (isset($filters['correction_status']) && !empty($filters['correction_status'])) {
            switch ($filters['correction_status']) {
                case 'none':
                    $query->doesntHave('corrections');
                    break;
                case 'confirmed':
                    $query->whereHas('corrections', function($q) {
                        $q->where('status', 'confirmed');
                    });
                    break;
                case 'cancelled':
                    $query->whereHas('corrections', function($q) {
                        $q->where('status', 'cancelled');
                    });
                    break;
                case 'has_corrections':
                    $query->has('corrections');
                    break;
            }
        }

        // 更正类型筛选
        if (isset($filters['correction_type']) && !empty($filters['correction_type'])) {
            $query->whereHas('corrections', function($q) use ($filters) {
                $q->where('correction_type', $filters['correction_type']);
            });
        }
        
        // 支持只显示可更正的订单（所有订单都可以更正）
        if (isset($filters['correctable']) && $filters['correctable']) {
            // 移除状态限制，所有订单都可以更正
            // $query->where('status', 'delivered');
        }
        
        // 过滤掉取消的订单
        if (isset($filters['exclude_cancelled']) && $filters['exclude_cancelled']) {
            $query->where('status', '!=', 'cancelled');
        }

        // 包含更正信息（前端请求的特殊参数）
        if (isset($filters['with_corrections']) && $filters['with_corrections']) {
            // 已经在上面的with中加载了corrections关联
        }

        // 包含付款链接信息
        if (isset($filters['with_payment_links']) && $filters['with_payment_links']) {
            $query->with(['paymentLinks' => function($q) {
                $q->latest();
            }]);
        }

        // 计算更正状态（前端请求的特殊参数）
        if (isset($filters['calculate_correction_status']) && $filters['calculate_correction_status']) {
            // 已经在上面的selectRaw中计算了correction_status字段
        }
        
        // 处理前端传递的date_range参数
        if (isset($filters['date_range']) && !empty($filters['date_range'])) {
            $dateRange = $filters['date_range'];
            $now = now();
            
            switch ($dateRange) {
                case 'today':
                    $query->whereDate('created_at', $now->toDateString());
                    break;
                case 'week':
                    $query->whereBetween('created_at', [
                        $now->startOfWeek()->toDateTimeString(),
                        $now->endOfWeek()->toDateTimeString()
                    ]);
                    break;
                case 'month':
                    $query->whereBetween('created_at', [
                        $now->startOfMonth()->toDateTimeString(),
                        $now->endOfMonth()->toDateTimeString()
                    ]);
                    break;
            }
        }
        
        // 排序处理
        if (isset($filters['sort']) && !empty($filters['sort'])) {
            switch ($filters['sort']) {
                case 'created_at_desc':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'created_at_asc':
                    $query->orderBy('created_at', 'asc');
                    break;
                case 'total_desc':
                    $query->orderBy('total', 'desc');
                    break;
                case 'total_asc':
                    $query->orderBy('total', 'asc');
                    break;
                default:
                    $query->latest('created_at'); // 默认按创建时间倒序
                    break;
            }
        } else {
            $query->latest('created_at'); // 默认按创建时间倒序
        }
        
        // 权限控制逻辑
        if ($user) {
            // 检查是否是员工模型（Employee）
            if ($user instanceof \App\Employee\Models\Employee) {
                // 员工系统：员工可以查看所有订单
                // 如果员工有角色限制，可以在这里添加
                $allowedEmployeeRoles = ['admin', 'manager', 'staff', 'warehouse_manager', 'crm_agent'];
                
                // 如果员工有role属性且不在允许的角色中，限制访问
                if (isset($user->role) && !empty($user->role) && !in_array($user->role, $allowedEmployeeRoles)) {
                    $query->whereRaw('1 = 0');
                }
                // 否则允许员工查看所有订单（包括没有role属性的员工）
            } else {
                // 普通用户系统：根据角色控制访问权限
                $adminRoles = ['admin', 'manager', 'staff', 'warehouse_manager', 'crm_agent', 'crm'];
                
                // 如果用户有管理员角色，允许查看所有订单
                if (isset($user->role) && in_array($user->role, $adminRoles)) {
                    // 管理员可以查看所有订单，不添加任何限制
                } else {
                    // 普通用户只能查看自己的订单
                    $query->where('user_id', $user->id);
                }
            }
        } else {
            // 如果没有传递用户参数，为了安全起见，不返回任何订单
            // 但在测试环境或特殊情况下，可能需要允许查看所有订单
            // 这里我们允许在没有用户时查看所有订单（用于测试和管理界面）
            // 在生产环境中，建议总是传递用户参数
            $query->whereRaw('1 = 0'); // 返回空结果
        }
        
        // 添加索引优化：按创建时间倒序
        return $query->paginate($perPage);
    }
    
    /**
     * 应用可合并订单筛选
     */
    private function applyMergeableFilter($query)
    {
        // 只显示待付款状态的订单
        $query->where('status', 'pending')
              ->where('is_merged_from', false)
              ->whereDate('created_at', now()->toDateString()) // 只看当日订单
              ->whereHas('user') // 确保有用户信息
              ->whereExists(function($subQuery) {
                  // 存在同一用户的其他待付款订单
                  $subQuery->select(DB::raw(1))
                           ->from('orders as o2')
                           ->whereRaw('o2.user_id = orders.user_id')
                           ->whereRaw('o2.id != orders.id')
                           ->where('o2.status', 'pending')
                           ->where('o2.is_merged_from', false)
                           ->whereDate('o2.created_at', now()->toDateString());
              });
    }
    
    /**
     * 检查订单是否可合并
     */
    public function isOrderMergeable($order): bool
    {
        // 1. 必须是待付款状态
        if ($order->status !== 'pending') {
            return false;
        }
        
        // 2. 不能已经被合并过
        if ($order->is_merged_from) {
            return false;
        }
        
        // 3. 必须是当日订单
        if (!$order->created_at->isToday()) {
            return false;
        }
        
        // 4. 必须有用户信息
        if (!$order->user_id) {
            return false;
        }
        
        // 5. 同一用户当日必须有其他待付款订单
        $sameUserOrders = Order::where('user_id', $order->user_id)
            ->where('id', '!=', $order->id)
            ->where('status', 'pending')
            ->where('is_merged_from', false)
            ->whereDate('created_at', now()->toDateString())
            ->count();
            
        return $sameUserOrders > 0;
    }
    
    /**
     * 创建订单
     *
     * @param array $data 订单数据
     * @param User $user 用户
     * @return Order 创建的订单
     */
    public function createOrder(array $data, User $user)
    {
        DB::beginTransaction();
        try {
            // 获取订单区域ID（如果有）
            $regionId = $data['region_id'] ?? null;
            
            // 使用商品模型的价格计算方法计算订单价格
            $calculatedItems = [];
            $subtotal = 0;
            $totalDiscount = 0;
            $allDiscountInfo = [];
            
            foreach ($data['items'] as $item) {
                $product = Product::findOrFail($item['product_id']);
                $quantity = $item['quantity'];

                // 调试：记录商品名称信息
                Log::info('🔍 订单商品名称调试', [
                    'product_id' => $product->id,
                    'database_name' => $product->name,
                    'requested_quantity' => $quantity,
                    'current_stock' => $product->stock,
                    'user_id' => $user->id
                ]);

                // 使用商品模型的价格计算方法
                $priceInfo = $product->calculatePrice($user, $regionId, $quantity);
                
                // 🚨 修复：使用库存策略检查而不是严格的可用性检查
                $stockCheck = $product->checkStockWithPolicy($quantity);
                if (!$stockCheck['allowed']) {
                    Log::warning('🚨 商品库存不足', [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'requested_quantity' => $quantity,
                        'current_stock' => $product->stock,
                        'inventory_policy' => $product->inventory_policy,
                        'error_message' => $stockCheck['message']
                    ]);
                    throw new \Exception($stockCheck['message']);
                }
                
                // 如果有库存警告，记录到日志
                if ($stockCheck['warning']) {
                    Log::warning('📋 订单创建库存警告', [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'requested_quantity' => $quantity,
                        'current_stock' => $product->stock,
                        'inventory_policy' => $product->inventory_policy,
                        'warning' => $stockCheck['warning']
                    ]);
                }

                // 确定使用的单位ID：优先使用传入的单位ID，否则必须使用销售单位
                $unitId = $item['unit_id'] ?? null;
                if (!$unitId) {
                    $saleUnit = $product->getSaleDefaultUnit();
                    if (!$saleUnit) {
                        throw new \Exception("商品 {$product->name} 未设置销售单位，无法创建订单");
                    }
                    $unitId = $saleUnit->id;
                }

                $calculatedItems[] = [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku ?? '',
                    'quantity' => $quantity,
                    'base_price' => $priceInfo['base_price'],
                    'final_price' => $priceInfo['final_price'],
                    'original_price' => $priceInfo['base_price'], // 修复：使用base_price作为original_price
                    'price_type' => $priceInfo['price_type'],
                    'item_total' => $priceInfo['item_total'],
                    'discount_amount' => $priceInfo['total_discount'] * $quantity,
                    'discount_info' => $priceInfo['discount_info'],
                    'unit_id' => $unitId,
                    'region_id' => $regionId,
                ];

                $subtotal += $priceInfo['item_total'];
                $totalDiscount += $priceInfo['total_discount'] * $quantity;
                $allDiscountInfo = array_merge($allDiscountInfo, $priceInfo['discount_info']);
            }
            
            // 计算支付优惠（基于各种优惠后的最终价格）
            $paymentOfferInfo = $this->paymentOfferService->calculatePaymentOffer(
                $data['payment_method'],
                $subtotal, // 使用各种优惠后的小计作为基础金额
                true // 启用叠加优惠
            );
            
            $paymentDiscount = $paymentOfferInfo['offer_amount'] ?? 0;
            $finalTotal = max(0, $subtotal - $paymentDiscount); // 最终总价 = 优惠后小计 - 支付优惠
            
            Log::info('💰 订单支付优惠计算', [
                'payment_method' => $data['payment_method'],
                'original_total' => $subtotal + $totalDiscount, // 原始总价（基本单价）
                'subtotal_after_product_discount' => $subtotal, // 商品优惠后小计
                'product_discount' => $totalDiscount, // 商品优惠金额
                'payment_offer_info' => $paymentOfferInfo,
                'payment_discount' => $paymentDiscount, // 支付优惠金额
                'final_total' => $finalTotal // 最终应付金额
            ]);
            
            // 创建订单数据
            $orderData = [
                'user_id' => $user->id,
                'order_no' => Order::generateOrderNo(),
                'total' => round($finalTotal, 2), // 最终总价（已扣除支付优惠）
                'subtotal' => round($subtotal, 2), // 商品小计
                'discount' => round($totalDiscount, 2), // 商品优惠（会员、区域等）
                'payment_discount' => round($paymentDiscount, 2), // 支付优惠
                'payment_discount_info' => $paymentOfferInfo, // 支付优惠详情
                'original_total' => round($subtotal + $totalDiscount, 2), // 原始总价（商品优惠前）
                'status' => 'pending',
                'payment_method' => $data['payment_method'],
                'notes' => $data['notes'] ?? '',
                'region_id' => $regionId, // 保存区域ID
                'pricing_info' => $allDiscountInfo, // 保存价格计算详情（模型会自动转换为JSON）
            ];
            
            // 处理地址信息
            if (isset($data['user_address_id'])) {
                // 通过用户地址ID关联
                $orderData['user_address_id'] = $data['user_address_id'];
                
                // 查找地址并填充其他地址字段（冗余，方便查询）
                $userAddress = UserAddress::findOrFail($data['user_address_id']);
                $orderData['shipping_address'] = $userAddress->getFullAddressAttribute();
                $orderData['contact_name'] = $userAddress->contact_name;
                $orderData['contact_phone'] = $userAddress->contact_phone;
            } else {
                // 直接使用提交的地址信息
                $orderData['shipping_address'] = $data['shipping_address'] ?? '';
                $orderData['contact_name'] = $data['contact_name'] ?? '';
                $orderData['contact_phone'] = $data['contact_phone'] ?? '';
            }
            
            // 创建订单
            $order = Order::create($orderData);
            
            // 如果是货到付款订单
            if ($data['payment_method'] === 'cod') {
                $order->is_cod = true;
                $order->cod_status = 'unpaid'; // 初始状态：未支付
                $order->save();
                
                // 记录货到付款日志
                Log::info('货到付款订单创建', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'client_id' => $user->id,
                    'client_name' => $user->name,
                    'total' => $order->total,
                    'pricing_info' => $allDiscountInfo
                ]);
            }
            
            // 创建订单明细
            foreach ($calculatedItems as $item) {
                $orderItem = new OrderItem([
                    'product_id' => $item['product_id'],
                    'product_name' => $item['product_name'],
                    'product_sku' => $item['product_sku'],
                    'quantity' => $item['quantity'],
                    'price' => $item['final_price'],
                    'original_price' => $item['original_price'],
                    'total' => $item['item_total'],
                    'unit_id' => $item['unit_id'],
                    'region_id' => $item['region_id'],
                ]);
                
                $order->items()->save($orderItem);
                
                // 扣减库存
                $product = Product::find($item['product_id']);
                $reduceResult = $product->reduceStockWithPolicy($item['quantity'], $item['unit_id'] ?? null);
                
                // 记录库存扣减结果
                Log::info('📦 订单库存扣减', [
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'quantity_reduced' => $item['quantity'],
                    'inventory_policy' => $product->inventory_policy,
                    'reduce_result' => $reduceResult,
                    'stock_before' => $product->stock + $item['quantity'], // 扣减前的库存
                    'stock_after' => $product->stock // 扣减后的库存
                ]);
                
                // 如果库存扣减失败，记录错误但不回滚（因为前面已经检查过了）
                if (!$reduceResult['success']) {
                    Log::error('📦 订单库存扣减失败', [
                        'order_id' => $order->id,
                        'product_id' => $product->id,
                        'error_message' => $reduceResult['message'],
                        'inventory_policy' => $product->inventory_policy
                    ]);
                }
                
                // 如果有警告，记录到日志
                if ($reduceResult['warning']) {
                    Log::warning('📦 订单库存扣减警告', [
                        'order_id' => $order->id,
                        'product_id' => $product->id,
                        'warning' => $reduceResult['warning'],
                        'inventory_policy' => $product->inventory_policy
                    ]);
                }
            }
            
            // 自动创建配送记录并分配配送员
            if ($user->default_employee_deliverer_id) {
                // 用户有绑定默认配送员，直接分配
                $deliverer = \App\Delivery\Models\Deliverer::where('employee_id', $user->default_employee_deliverer_id)->first();
                if ($deliverer) {
                    \App\Delivery\Models\Delivery::create([
                        'order_id' => $order->id,
                        'status' => 'pending',
                        'deliverer_id' => $deliverer->id,
                    ]);
                    
                    Log::info('新订单自动分配给默认配送员', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'user_id' => $user->id,
                        'employee_id' => $user->default_employee_deliverer_id,
                        'deliverer_id' => $deliverer->id
                    ]);
                }
            } else {
                // 用户没有绑定配送员，创建未分配的配送记录
                \App\Delivery\Models\Delivery::create([
                    'order_id' => $order->id,
                    'status' => 'pending',
                    'deliverer_id' => null, // 暂时未分配配送员
                ]);
                
                Log::info('新订单创建，等待分配配送员', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'reason' => '用户未绑定默认配送员'
                ]);
            }
            
            DB::commit();
            
            // 加载关联
            $order->load(['items', 'userAddress']);
            return $order;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建订单失败', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }
    
    /**
     * 更新订单状态
     *
     * @param int $orderId 订单ID
     * @param string $status 新状态
     * @param User $user 操作用户
     * @return Order 更新后的订单
     */
    public function updateOrderStatus($orderId, $status, User $user)
    {
        $order = Order::findOrFail($orderId);
        
        // 验证状态转换是否有效
        switch ($status) {
            case 'paid':
                if (!$order->canBePaid()) {
                    throw new \Exception('该订单当前状态不允许标记为已支付');
                }
                $order->paid_at = now();
                break;
                
            case 'shipped':
                if (!$order->canBeShipped()) {
                    throw new \Exception('该订单当前状态不允许标记为已发货');
                }
                $order->shipped_at = now();
                break;
                
            case 'delivered':
                if (!$order->canBeDelivered()) {
                    throw new \Exception('该订单当前状态不允许标记为已送达');
                }
                $order->delivered_at = now();
                break;
                
            case 'cancelled':
                if (!$order->canBeCancelled()) {
                    throw new \Exception('该订单当前状态不允许取消');
                }
                $order->cancelled_at = now();
                
                // 恢复库存
                foreach ($order->items as $item) {
                    if ($item->product) {
                        $item->product->addStock($item->quantity, $item->unit_id);
                    }
                }
                break;
                
            default:
                throw new \Exception('无效的订单状态');
        }
        
        $order->status = $status;
        $order->save();
        
        // 记录状态变更日志
        Log::info('订单状态更新', [
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'old_status' => $order->getOriginal('status'),
            'new_status' => $status,
            'updated_by' => $user ? "{$user->id}:{$user->name}" : 'system'
        ]);
        
        return $order;
    }
    
    /**
     * 取消订单
     *
     * @param int $orderId 订单ID
     * @param User $user 操作用户
     * @param string $reason 取消原因
     * @return Order 取消后的订单
     */
    public function cancelOrder($orderId, User $user, $reason = '')
    {
        return $this->updateOrderStatus($orderId, 'cancelled', $user);
    }
    
    /**
     * 获取订单详情
     *
     * @param int $orderId 订单ID
     * @param User|null $user 请求用户
     * @return Order 订单详情
     */
    public function getOrderDetail($orderId, $user = null)
    {
        $order = Order::with(['items.product', 'items.unit', 'user', 'delivery'])
                     ->findOrFail($orderId);
        
        // 如果不是管理员，验证是否为订单所有者
        if ($user && !in_array($user->role, ['admin', 'merchant']) && $order->user_id !== $user->id) {
            throw new \Exception('无权查看此订单');
        }
        
        return $order;
    }
} 