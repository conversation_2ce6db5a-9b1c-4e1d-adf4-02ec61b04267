<?php

use App\Product\Http\Controllers\ProductController;
use App\Product\Http\Controllers\CategoryController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'products', 'middleware' => ['web', 'auth']], function () {
    Route::get('/', [ProductController::class, 'index'])->name('products.index');
    Route::post('/', [ProductController::class, 'store'])->name('products.store');
    Route::get('/{id}', [ProductController::class, 'show'])->name('products.show');
    Route::put('/{id}', [ProductController::class, 'update'])->name('products.update');
    Route::delete('/{id}', [ProductController::class, 'destroy'])->name('products.destroy');
    Route::put('/{id}/status', [ProductController::class, 'updateStatus'])->name('products.update-status');
    Route::get('/search/all', [ProductController::class, 'search'])->name('products.search');
});

Route::group(['prefix' => 'categories', 'middleware' => ['web', 'auth']], function () {
    Route::get('/', [CategoryController::class, 'index'])->name('categories.index');
    Route::post('/', [CategoryController::class, 'create'])->name('categories.create');
    Route::get('/{id}', [CategoryController::class, 'show'])->name('categories.show');
    Route::put('/{id}', [CategoryController::class, 'update'])->name('categories.update');
    Route::delete('/{id}', [CategoryController::class, 'destroy'])->name('categories.destroy');
}); 