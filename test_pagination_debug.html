<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页功能调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .test-result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .warning {
            color: #ffc107;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .pagination-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .api-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 分页功能调试测试</h1>
    
    <div class="test-section">
        <div class="test-title">📊 当前分页状态</div>
        <div class="pagination-info">
            <div>当前页: <span id="current-page">-</span></div>
            <div>每页数量: <span id="page-size">-</span></div>
            <div>总数: <span id="total">-</span></div>
            <div>总页数: <span id="total-pages">-</span></div>
            <div>当前页商品数: <span id="current-count">-</span></div>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">🎮 分页操作测试</div>
        <button onclick="testPageChange(1)">第1页</button>
        <button onclick="testPageChange(2)">第2页</button>
        <button onclick="testPageChange(3)">第3页</button>
        <button onclick="testSizeChange(10)">10条/页</button>
        <button onclick="testSizeChange(20)">20条/页</button>
        <button onclick="testSizeChange(50)">50条/页</button>
        <div class="test-result" id="operation-result"></div>
    </div>

    <div class="test-section">
        <div class="test-title">📡 API请求日志</div>
        <div class="api-log" id="api-log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div class="test-section">
        <div class="test-title">📋 商品列表预览</div>
        <div id="products-preview"></div>
    </div>

    <script>
        // 模拟分页数据
        let paginationState = {
            page: 1,
            pageSize: 20,
            total: 0
        };

        let apiLog = [];

        // 更新分页状态显示
        function updatePaginationDisplay() {
            document.getElementById('current-page').textContent = paginationState.page;
            document.getElementById('page-size').textContent = paginationState.pageSize;
            document.getElementById('total').textContent = paginationState.total;
            document.getElementById('total-pages').textContent = Math.ceil(paginationState.total / paginationState.pageSize);
        }

        // 添加API日志
        function addApiLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            apiLog.push(logEntry);
            
            const logElement = document.getElementById('api-log');
            logElement.innerHTML = apiLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            apiLog = [];
            document.getElementById('api-log').innerHTML = '';
        }

        // 模拟API请求
        async function mockApiRequest(params) {
            addApiLog(`发送API请求: ${JSON.stringify(params)}`);
            
            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 300));
            
            // 模拟响应数据
            const mockResponse = {
                data: {
                    current_page: params.page || 1,
                    per_page: params.limit || 20,
                    total: 156, // 模拟总数
                    last_page: Math.ceil(156 / (params.limit || 20)),
                    from: ((params.page || 1) - 1) * (params.limit || 20) + 1,
                    to: Math.min((params.page || 1) * (params.limit || 20), 156),
                    data: Array.from({ length: Math.min(params.limit || 20, 156 - ((params.page || 1) - 1) * (params.limit || 20)) }, (_, i) => ({
                        id: ((params.page || 1) - 1) * (params.limit || 20) + i + 1,
                        name: `商品 ${((params.page || 1) - 1) * (params.limit || 20) + i + 1}`,
                        price: Math.random() * 100 + 10
                    }))
                }
            };
            
            addApiLog(`API响应: 当前页=${mockResponse.data.current_page}, 每页=${mockResponse.data.per_page}, 总数=${mockResponse.data.total}, 商品数=${mockResponse.data.data.length}`);
            
            return mockResponse;
        }

        // 测试页码变更
        async function testPageChange(page) {
            addApiLog(`测试页码变更: ${paginationState.page} -> ${page}`, 'info');
            
            try {
                const response = await mockApiRequest({
                    page: page,
                    limit: paginationState.pageSize
                });
                
                // 更新分页状态
                paginationState.page = response.data.current_page;
                paginationState.total = response.data.total;
                
                updatePaginationDisplay();
                updateProductsPreview(response.data.data);
                
                document.getElementById('operation-result').innerHTML = 
                    `<span class="success">✅ 页码变更成功: 第${page}页</span>`;
                    
            } catch (error) {
                addApiLog(`页码变更失败: ${error.message}`, 'error');
                document.getElementById('operation-result').innerHTML = 
                    `<span class="error">❌ 页码变更失败: ${error.message}</span>`;
            }
        }

        // 测试页面大小变更
        async function testSizeChange(size) {
            addApiLog(`测试页面大小变更: ${paginationState.pageSize} -> ${size}`, 'info');
            
            try {
                const response = await mockApiRequest({
                    page: 1, // 重置到第一页
                    limit: size
                });
                
                // 更新分页状态
                paginationState.page = 1;
                paginationState.pageSize = size;
                paginationState.total = response.data.total;
                
                updatePaginationDisplay();
                updateProductsPreview(response.data.data);
                
                document.getElementById('operation-result').innerHTML = 
                    `<span class="success">✅ 页面大小变更成功: ${size}条/页</span>`;
                    
            } catch (error) {
                addApiLog(`页面大小变更失败: ${error.message}`, 'error');
                document.getElementById('operation-result').innerHTML = 
                    `<span class="error">❌ 页面大小变更失败: ${error.message}</span>`;
            }
        }

        // 更新商品预览
        function updateProductsPreview(products) {
            const previewElement = document.getElementById('products-preview');
            
            if (products && products.length > 0) {
                const productList = products.map(product => 
                    `<div style="padding: 5px; border-bottom: 1px solid #eee;">
                        ID: ${product.id} | 名称: ${product.name} | 价格: ¥${product.price.toFixed(2)}
                    </div>`
                ).join('');
                
                previewElement.innerHTML = `
                    <div style="margin-bottom: 10px;">显示 ${products.length} 个商品:</div>
                    ${productList}
                `;
                
                document.getElementById('current-count').textContent = products.length;
            } else {
                previewElement.innerHTML = '<div style="color: #999;">暂无商品数据</div>';
                document.getElementById('current-count').textContent = '0';
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            addApiLog('分页功能调试测试初始化');
            updatePaginationDisplay();
            
            // 加载第一页数据
            testPageChange(1);
        });
    </script>
</body>
</html>
