<?php

namespace App\WechatMp\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;

class WechatMpServiceProvider extends ServiceProvider
{
    /**
     * 注册应用服务
     *
     * @return void
     */
    public function register()
    {
        // 注册服务
        $this->app->singleton('wechatmp.service', function ($app) {
            return new \App\WechatMp\Services\WechatMpService();
        });
    }

    /**
     * 引导应用服务
     *
     * @return void
     */
    public function boot()
    {
        // 加载API路由
        $this->loadRoutesFrom(__DIR__.'/../routes/api.php');
        
        // 加载Web路由
        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');
    }
} 