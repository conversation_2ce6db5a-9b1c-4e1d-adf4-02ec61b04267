<?php

namespace App\Order\Models;

use App\Product\Models\Product;
use App\Unit\Models\Unit;
use App\Region\Models\Region;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'product_id',
        'product_name',
        'product_sku',
        'quantity',
        'price',
        'original_price',
        'total',
        'unit',
        'unit_id',
        'region_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'original_price' => 'decimal:2',
        'total' => 'decimal:2',
    ];

    /**
     * Get the order that owns the item.
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the product that is associated with the item.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 获取订单项的单位
     */
    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * 获取订单项的区域
     */
    public function region()
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * 获取单位的显示名称
     *
     * @return string
     */
    public function getUnitNameAttribute()
    {
        return $this->unit ? $this->unit->name : '';
    }

    /**
     * 获取单位的符号
     *
     * @return string
     */
    public function getUnitSymbolAttribute()
    {
        return $this->unit ? $this->unit->symbol : '';
    }
} 