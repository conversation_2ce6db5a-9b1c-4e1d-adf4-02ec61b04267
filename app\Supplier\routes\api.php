<?php

use App\Supplier\Http\Controllers\SupplierController;
use Illuminate\Support\Facades\Route;

Route::prefix('api/suppliers')->middleware(['auth:sanctum'])->group(function () {
    Route::get('/', [SupplierController::class, 'index']);
    Route::post('/', [SupplierController::class, 'store']);
    Route::get('/{id}', [SupplierController::class, 'show']);
    Route::put('/{id}', [SupplierController::class, 'update']);
    Route::delete('/{id}', [SupplierController::class, 'destroy']);
    Route::get('/{id}/purchase-history', [SupplierController::class, 'purchaseHistory']);
}); 