// TabBar图标生成脚本
// 使用方法：在此目录下运行 node 生成图标.js

const fs = require('fs');
const { createCanvas } = require('canvas');

// 如果没有安装canvas，请运行：npm install canvas

function createIcon(emoji, bgColor, size = 81) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // 绘制背景
    ctx.fillStyle = bgColor;
    ctx.fillRect(0, 0, size, size);
    
    // 绘制emoji
    ctx.font = `${size * 0.6}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillStyle = 'white';
    ctx.fillText(emoji, size / 2, size / 2);
    
    return canvas;
}

function saveIcon(filename, emoji, bgColor) {
    const canvas = createIcon(emoji, bgColor);
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(filename, buffer);
    console.log(`✅ 已生成: ${filename}`);
}

// 生成所有图标
const icons = [
    { name: 'home', emoji: '🏠', label: '首页' },
    { name: 'analytics', emoji: '📊', label: '行为分析' },
    { name: 'order', emoji: '📝', label: '代客下单' },
    { name: 'client', emoji: '👥', label: '客户' },
    { name: 'orders', emoji: '📋', label: '订单' },
    { name: 'profile', emoji: '👤', label: '我的' }
];

console.log('🎨 开始生成TabBar图标...\n');

icons.forEach(icon => {
    // 生成普通状态图标（灰色）
    saveIcon(`${icon.name}.png`, icon.emoji, '#999999');
    
    // 生成选中状态图标（蓝色）
    saveIcon(`${icon.name}-active.png`, icon.emoji, '#007AFF');
    
    console.log(`📱 ${icon.label} 图标生成完成\n`);
});

console.log('🎉 所有图标生成完成！');
console.log('📋 请将生成的PNG文件放到 static/tabbar/ 目录下');
console.log('🔄 然后重新运行UniApp项目即可看到底部导航栏');

// 如果没有安装canvas模块的备用方案
process.on('uncaughtException', (error) => {
    if (error.message.includes('canvas')) {
        console.log('❌ 缺少canvas模块');
        console.log('📦 请先安装：npm install canvas');
        console.log('🌐 或者使用图标生成器.html文件在浏览器中生成图标');
    } else {
        console.error('错误:', error.message);
    }
}); 