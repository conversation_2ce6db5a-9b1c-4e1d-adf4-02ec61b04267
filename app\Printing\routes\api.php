<?php

use Illuminate\Support\Facades\Route;
use App\Printing\Http\Controllers\PrintController;

/*
|--------------------------------------------------------------------------
| Printing API 路由
|--------------------------------------------------------------------------
|
| Printing模块的API路由定义
|
*/

// 需要认证的打印路由
Route::middleware(['auth:sanctum', 'employee.role:admin,manager'])->group(function () {
    // 打印管理路由
    Route::prefix('print')->group(function () {
        // 订单打印（原有功能）
        Route::post('/order/{orderId}', [PrintController::class, 'printOrder']);
        Route::post('/orders/batch', [PrintController::class, 'batchPrintOrders']);
        
        // 获取C-Lodop配置
        Route::get('/config', [PrintController::class, 'getConfig']);
        
        // 设置默认打印机
        Route::post('/printers/default', [PrintController::class, 'setDefaultPrinter']);
        
        // 打印机管理
        Route::get('/printers', [PrintController::class, 'printers']);
        Route::get('/printer/status', [PrintController::class, 'printerStatus']);
        
        // 打印预览和脚本生成
        Route::post('/preview', [PrintController::class, 'preview']);
        Route::post('/script', [PrintController::class, 'script']);
    });
});

// 打印预览路由（不需要认证）
Route::get('/print/preview/{filename}', function ($filename) {
    $path = storage_path('app/temp/' . $filename);
    
    if (!file_exists($path)) {
        abort(404, '预览文件不存在');
    }
    
    $content = file_get_contents($path);
    
    return response($content)
        ->header('Content-Type', 'text/html; charset=utf-8');
})->where('filename', '[a-zA-Z0-9_\-\.]+');

// 使用employee-api guard，确保认证返回的是员工ID
Route::group(['middleware' => ['auth:sanctum'], 'prefix' => 'api'], function () {
    Route::prefix('print')->group(function () {
        // 测试认证
        Route::get('test-auth', [PrintController::class, 'testAuth']);
        
        // 基础打印功能
        Route::post('order/{orderId}/delivery', [PrintController::class, 'printDelivery']);
        
        // 打印回调和状态管理
        Route::post('completed', [PrintController::class, 'printCompleted']);
        Route::post('failed', [PrintController::class, 'printFailed']);
        Route::post('batch-callback', [PrintController::class, 'batchPrintCallback']);
        
        // 打印状态查询
        Route::get('order/{orderId}/status', [PrintController::class, 'orderPrintStatus']);
        Route::post('orders/batch-status', [PrintController::class, 'batchGetOrderPrintStatus']);
        Route::get('pending', [PrintController::class, 'pendingPrints']);
        Route::get('record/{recordId}', [PrintController::class, 'printRecord']);
        
        // 重新打印
        Route::post('order/{orderId}/reprint', [PrintController::class, 'reprintOrder']);
    });
}); 