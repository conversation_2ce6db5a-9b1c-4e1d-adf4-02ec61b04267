/**
 * 加载状态管理器
 * 统一管理页面加载状态，避免多个loading冲突
 */
class LoadingManager {
	constructor() {
		this.loadingStates = new Map() // 存储各个页面/组件的加载状态
		this.loadingQueue = [] // 加载队列
		this.isGlobalLoading = false // 全局加载状态
	}
	
	/**
	 * 显示加载提示
	 * @param {string} key 加载标识
	 * @param {object} options 加载选项
	 */
	showLoading(key = 'default', options = {}) {
		const defaultOptions = {
			title: '加载中...',
			mask: true,
			duration: 0
		}
		
		const loadingOptions = { ...defaultOptions, ...options }
		
		// 记录加载状态
		this.loadingStates.set(key, {
			...loadingOptions,
			startTime: Date.now()
		})
		
		// 如果没有全局加载，显示loading
		if (!this.isGlobalLoading) {
			this.isGlobalLoading = true
			uni.showLoading(loadingOptions)
		}
	}
	
	/**
	 * 隐藏加载提示
	 * @param {string} key 加载标识
	 */
	hideLoading(key = 'default') {
		// 移除加载状态
		this.loadingStates.delete(key)
		
		// 如果没有其他加载状态，隐藏loading
		if (this.loadingStates.size === 0 && this.isGlobalLoading) {
			this.isGlobalLoading = false
			uni.hideLoading()
		}
	}
	
	/**
	 * 检查是否正在加载
	 * @param {string} key 加载标识
	 */
	isLoading(key = 'default') {
		return this.loadingStates.has(key)
	}
	
	/**
	 * 获取加载状态
	 * @param {string} key 加载标识
	 */
	getLoadingState(key = 'default') {
		return this.loadingStates.get(key)
	}
	
	/**
	 * 清除所有加载状态
	 */
	clearAll() {
		this.loadingStates.clear()
		if (this.isGlobalLoading) {
			this.isGlobalLoading = false
			uni.hideLoading()
		}
	}
	
	/**
	 * 显示Toast提示
	 * @param {string} title 提示内容
	 * @param {string} icon 图标类型
	 * @param {number} duration 持续时间
	 */
	showToast(title, icon = 'none', duration = 2000) {
		// 先隐藏loading，再显示toast
		if (this.isGlobalLoading) {
			uni.hideLoading()
			setTimeout(() => {
				uni.showToast({ title, icon, duration })
				// 如果还有加载状态，延迟重新显示loading
				if (this.loadingStates.size > 0) {
					setTimeout(() => {
						if (this.loadingStates.size > 0) {
							const firstState = this.loadingStates.values().next().value
							uni.showLoading(firstState)
						}
					}, duration)
				} else {
					this.isGlobalLoading = false
				}
			}, 100)
		} else {
			uni.showToast({ title, icon, duration })
		}
	}
}

// 创建全局实例
const loadingManager = new LoadingManager()

export default loadingManager 