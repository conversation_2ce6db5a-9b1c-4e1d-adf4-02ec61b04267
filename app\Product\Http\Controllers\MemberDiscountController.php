<?php

namespace App\Product\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Product\Models\ProductMemberDiscount;
use App\Product\Models\CategoryMemberDiscount;
use App\Product\Models\Product;
use App\Product\Models\Category;
use App\Crm\Models\MembershipLevel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class MemberDiscountController extends Controller
{
    /**
     * 获取商品会员折扣列表
     */
    public function getProductMemberDiscounts(Request $request)
    {
        try {
            $query = ProductMemberDiscount::with(['product', 'membershipLevel']);
            
            // 筛选条件
            if ($request->has('membership_level_id')) {
                $query->where('membership_level_id', $request->membership_level_id);
            }
            
            if ($request->has('product_id')) {
                $query->where('product_id', $request->product_id);
            }
            
            if ($request->has('status')) {
                $query->where('status', $request->status === 'true');
            }
            
            if ($request->has('keyword')) {
                $keyword = $request->keyword;
                $query->whereHas('product', function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('sku', 'like', "%{$keyword}%");
                });
            }
            
            // 分页
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 20);
            
            $discounts = $query->orderBy('created_at', 'desc')
                              ->paginate($limit, ['*'], 'page', $page);
            
            return response()->json(ApiResponse::success($discounts));
        } catch (\Exception $e) {
            Log::error('获取商品会员折扣列表失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取数据失败', 500), 500);
        }
    }
    
    /**
     * 创建商品会员折扣
     */
    public function createProductMemberDiscount(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|integer|exists:products,id',
                'membership_level_id' => 'required|integer|exists:membership_levels,id',
                'discount_type' => 'required|in:fixed_amount',
                'discount_value' => 'required|numeric|min:0',
                'max_discount' => 'nullable|numeric|min:0',
                'status' => 'nullable|boolean',
                'start_time' => 'nullable|date',
                'end_time' => 'nullable|date|after_or_equal:start_time',
                'description' => 'nullable|string|max:500',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            // 检查是否已存在相同的折扣规则
            $existing = ProductMemberDiscount::where('product_id', $request->product_id)
                                           ->where('membership_level_id', $request->membership_level_id)
                                           ->first();
            
            if ($existing) {
                return response()->json(ApiResponse::error('该商品已存在相同等级的会员折扣', 422), 422);
            }
            
            $data = $request->all();
            $data['status'] = $data['status'] ?? true;
            
            $discount = ProductMemberDiscount::create($data);
            $discount->load(['product', 'membershipLevel']);
            
            return response()->json(ApiResponse::success($discount, '创建成功'));
        } catch (\Exception $e) {
            Log::error('创建商品会员折扣失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('创建失败', 500), 500);
        }
    }
    
    /**
     * 更新商品会员折扣
     */
    public function updateProductMemberDiscount(Request $request, $id)
    {
        try {
            $discount = ProductMemberDiscount::findOrFail($id);
            
            $validator = Validator::make($request->all(), [
                'discount_type' => 'sometimes|in:fixed_amount',
                'discount_value' => 'sometimes|numeric|min:0',
                'max_discount' => 'nullable|numeric|min:0',
                'status' => 'nullable|boolean',
                'start_time' => 'nullable|date',
                'end_time' => 'nullable|date|after_or_equal:start_time',
                'description' => 'nullable|string|max:500',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            $discount->update($request->all());
            $discount->load(['product', 'membershipLevel']);
            
            return response()->json(ApiResponse::success($discount, '更新成功'));
        } catch (\Exception $e) {
            Log::error('更新商品会员折扣失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('更新失败', 500), 500);
        }
    }
    
    /**
     * 删除商品会员折扣
     */
    public function deleteProductMemberDiscount($id)
    {
        try {
            $discount = ProductMemberDiscount::findOrFail($id);
            $discount->delete();
            
            return response()->json(ApiResponse::success(null, '删除成功'));
        } catch (\Exception $e) {
            Log::error('删除商品会员折扣失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('删除失败', 500), 500);
        }
    }
    
    /**
     * 获取分类会员折扣列表
     */
    public function getCategoryMemberDiscounts(Request $request)
    {
        try {
            $query = CategoryMemberDiscount::with(['category', 'membershipLevel']);
            
            // 筛选条件
            if ($request->has('membership_level_id')) {
                $query->where('membership_level_id', $request->membership_level_id);
            }
            
            if ($request->has('category_id')) {
                $query->where('category_id', $request->category_id);
            }
            
            if ($request->has('status')) {
                $query->where('status', $request->status === 'true');
            }
            
            // 分页
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 20);
            
            $discounts = $query->orderBy('created_at', 'desc')
                              ->paginate($limit, ['*'], 'page', $page);
            
            return response()->json(ApiResponse::success($discounts));
        } catch (\Exception $e) {
            Log::error('获取分类会员折扣列表失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取数据失败', 500), 500);
        }
    }
    
    /**
     * 创建分类会员折扣
     */
    public function createCategoryMemberDiscount(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'category_id' => 'required|integer|exists:categories,id',
                'membership_level_id' => 'required|integer|exists:membership_levels,id',
                'discount_type' => 'required|in:fixed_amount',
                'discount_value' => 'required|numeric|min:0',
                'max_discount' => 'nullable|numeric|min:0',
                'status' => 'nullable|boolean',
                'start_time' => 'nullable|date',
                'end_time' => 'nullable|date|after_or_equal:start_time',
                'description' => 'nullable|string|max:500',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            // 检查是否已存在相同的折扣规则
            $existing = CategoryMemberDiscount::where('category_id', $request->category_id)
                                            ->where('membership_level_id', $request->membership_level_id)
                                            ->first();
            
            if ($existing) {
                return response()->json(ApiResponse::error('该分类已存在相同等级的会员折扣', 422), 422);
            }
            
            $data = $request->all();
            $data['status'] = $data['status'] ?? true;
            
            $discount = CategoryMemberDiscount::create($data);
            $discount->load(['category', 'membershipLevel']);
            
            return response()->json(ApiResponse::success($discount, '创建成功'));
        } catch (\Exception $e) {
            Log::error('创建分类会员折扣失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('创建失败', 500), 500);
        }
    }
    
    /**
     * 更新分类会员折扣
     */
    public function updateCategoryMemberDiscount(Request $request, $id)
    {
        try {
            $discount = CategoryMemberDiscount::findOrFail($id);
            
            $validator = Validator::make($request->all(), [
                'discount_type' => 'sometimes|in:fixed_amount',
                'discount_value' => 'sometimes|numeric|min:0',
                'max_discount' => 'nullable|numeric|min:0',
                'status' => 'nullable|boolean',
                'start_time' => 'nullable|date',
                'end_time' => 'nullable|date|after_or_equal:start_time',
                'description' => 'nullable|string|max:500',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            $discount->update($request->all());
            $discount->load(['category', 'membershipLevel']);
            
            return response()->json(ApiResponse::success($discount, '更新成功'));
        } catch (\Exception $e) {
            Log::error('更新分类会员折扣失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('更新失败', 500), 500);
        }
    }
    
    /**
     * 删除分类会员折扣
     */
    public function deleteCategoryMemberDiscount($id)
    {
        try {
            $discount = CategoryMemberDiscount::findOrFail($id);
            $discount->delete();
            
            return response()->json(ApiResponse::success(null, '删除成功'));
        } catch (\Exception $e) {
            Log::error('删除分类会员折扣失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('删除失败', 500), 500);
        }
    }
    
    /**
     * 批量设置商品会员折扣
     */
    public function batchSetProductMemberDiscounts(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_ids' => 'required|array',
                'product_ids.*' => 'integer|exists:products,id',
                'membership_level_id' => 'required|integer|exists:membership_levels,id',
                'discount_type' => 'required|in:fixed_amount',
                'discount_value' => 'required|numeric|min:0',
                'max_discount' => 'nullable|numeric|min:0',
                'status' => 'nullable|boolean',
                'start_time' => 'nullable|date',
                'end_time' => 'nullable|date|after_or_equal:start_time',
                'description' => 'nullable|string|max:500',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            $data = $request->except('product_ids');
            $data['status'] = $data['status'] ?? true;
            $productIds = $request->product_ids;
            
            $created = 0;
            $updated = 0;
            $errors = [];
            
            foreach ($productIds as $productId) {
                try {
                    $existing = ProductMemberDiscount::where('product_id', $productId)
                                                   ->where('membership_level_id', $data['membership_level_id'])
                                                   ->first();
                    
                    if ($existing) {
                        $existing->update($data);
                        $updated++;
                    } else {
                        $data['product_id'] = $productId;
                        ProductMemberDiscount::create($data);
                        $created++;
                    }
                } catch (\Exception $e) {
                    $errors[] = "商品ID {$productId}: " . $e->getMessage();
                }
            }
            
            $result = [
                'created' => $created,
                'updated' => $updated,
                'errors' => $errors
            ];
            
            return response()->json(ApiResponse::success($result, '批量设置完成'));
        } catch (\Exception $e) {
            Log::error('批量设置商品会员折扣失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('批量设置失败', 500), 500);
        }
    }
    
    /**
     * 批量设置分类会员折扣
     */
    public function batchSetCategoryMemberDiscounts(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'category_ids' => 'required|array',
                'category_ids.*' => 'integer|exists:categories,id',
                'membership_level_id' => 'required|integer|exists:membership_levels,id',
                'discount_type' => 'required|in:fixed_amount',
                'discount_value' => 'required|numeric|min:0',
                'max_discount' => 'nullable|numeric|min:0',
                'status' => 'nullable|boolean',
                'start_time' => 'nullable|date',
                'end_time' => 'nullable|date|after_or_equal:start_time',
                'description' => 'nullable|string|max:500',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error('验证失败', 422, $validator->errors()->toArray()), 422);
            }
            
            $data = $request->except('category_ids');
            $data['status'] = $data['status'] ?? true;
            $categoryIds = $request->category_ids;
            
            $created = 0;
            $updated = 0;
            $errors = [];
            
            foreach ($categoryIds as $categoryId) {
                try {
                    $existing = CategoryMemberDiscount::where('category_id', $categoryId)
                                                     ->where('membership_level_id', $data['membership_level_id'])
                                                     ->first();
                    
                    if ($existing) {
                        $existing->update($data);
                        $updated++;
                    } else {
                        $data['category_id'] = $categoryId;
                        CategoryMemberDiscount::create($data);
                        $created++;
                    }
                } catch (\Exception $e) {
                    $errors[] = "分类ID {$categoryId}: " . $e->getMessage();
                }
            }
            
            $result = [
                'created' => $created,
                'updated' => $updated,
                'errors' => $errors
            ];
            
            return response()->json(ApiResponse::success($result, '批量设置完成'));
        } catch (\Exception $e) {
            Log::error('批量设置分类会员折扣失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('批量设置失败', 500), 500);
        }
    }
} 