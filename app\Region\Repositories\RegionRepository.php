<?php

namespace App\Region\Repositories;

use App\Region\Models\Region;
use Illuminate\Pagination\LengthAwarePaginator;

class RegionRepository
{
    /**
     * 获取区域列表（分页）
     *
     * @param array $filters 过滤条件
     * @param int $perPage 每页数量
     * @return LengthAwarePaginator
     */
    public function getRegions($filters = [], $perPage = 10)
    {
        $query = Region::query();
        
        // 名称或代码搜索
        if (!empty($filters['keyword'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['keyword'] . '%')
                  ->orWhere('code', 'like', '%' . $filters['keyword'] . '%');
            });
        }
        
        // 状态筛选
        if (isset($filters['status']) && $filters['status'] !== '') {
            $query->where('status', $filters['status']);
        }
        
        // 父级ID筛选
        if (isset($filters['parent_id']) && $filters['parent_id'] !== '') {
            $query->where('parent_id', $filters['parent_id']);
        }
        
        // 级别筛选
        if (isset($filters['level']) && $filters['level'] !== '') {
            $query->where('level', $filters['level']);
        }
        
        // 排序
        $query->orderBy('sort', 'asc')->orderBy('id', 'asc');
        
        return $query->paginate($perPage);
    }
    
    /**
     * 查找区域
     *
     * @param int $id
     * @return Region|null
     */
    public function find($id)
    {
        return Region::find($id);
    }
    
    /**
     * 查找区域或抛出异常
     *
     * @param int $id
     * @return Region
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public function findOrFail($id)
    {
        return Region::findOrFail($id);
    }
    
    /**
     * 创建区域
     *
     * @param array $data
     * @return Region
     */
    public function create(array $data)
    {
        return Region::create($data);
    }
    
    /**
     * 更新区域
     *
     * @param Region $region
     * @param array $data
     * @return Region
     */
    public function update(Region $region, array $data)
    {
        $region->update($data);
        return $region;
    }
    
    /**
     * 删除区域
     *
     * @param Region $region
     * @return bool
     */
    public function delete(Region $region)
    {
        return $region->delete();
    }
    
    /**
     * 根据代码获取区域
     *
     * @param string $code
     * @return Region|null
     */
    public function findByCode($code)
    {
        return Region::where('code', $code)->first();
    }
    
    /**
     * 获取所有区域
     *
     * @param array $filters 过滤条件
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllRegions($filters = [])
    {
        $query = Region::query();
        
        // 状态筛选
        if (isset($filters['status']) && $filters['status'] !== '') {
            $query->where('status', $filters['status']);
        }
        
        // 级别筛选
        if (isset($filters['level']) && $filters['level'] !== '') {
            $query->where('level', $filters['level']);
        }
        
        // 排序
        $query->orderBy('sort', 'asc')->orderBy('id', 'asc');
        
        return $query->get();
    }
} 