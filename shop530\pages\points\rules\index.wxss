/* pages/points/rules/index.wxss - 积分规则页面样式 */

.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 20rpx;
}

/* ==================== 页面头部 ==================== */
.header {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  padding: 60rpx 40rpx 40rpx;
  color: white;
  text-align: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* ==================== 规则内容 ==================== */
.rules-content {
  padding: 0 30rpx;
}

/* 规则分组 */
.rule-section {
  background: white;
  border-radius: 16rpx;
  margin-top: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #f8f9fa;
  border-bottom: 2rpx solid #eee;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 规则项 */
.rule-item {
  border-bottom: 2rpx solid #f0f0f0;
}

.rule-item:last-child {
  border-bottom: none;
}

.rule-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: white;
  transition: background-color 0.3s;
}

.rule-header:active {
  background-color: #f8f9fa;
}

.rule-name {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.rule-points {
  font-size: 26rpx;
  color: #4CAF50;
  font-weight: bold;
  margin-right: 20rpx;
}

.expand-icon {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.rule-detail {
  padding: 0 30rpx 30rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  background: #fafafa;
  border-top: 2rpx solid #f0f0f0;
}

/* ==================== 特殊说明 ==================== */
.notice-list {
  padding: 30rpx;
}

.notice-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
  margin-bottom: 16rpx;
  padding-left: 20rpx;
  position: relative;
}

.notice-item:last-child {
  margin-bottom: 0;
}

/* ==================== 操作按钮 ==================== */
.action-buttons {
  padding: 40rpx 30rpx 60rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.btn-primary {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  border-radius: 50rpx;
  height: 88rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.3);
}

.btn-primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);
}

.btn-secondary {
  background: #2196F3;
  color: white;
  border-radius: 50rpx;
  height: 88rpx;
  font-size: 30rpx;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(33, 150, 243, 0.3);
}

.btn-secondary:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 12rpx rgba(33, 150, 243, 0.3);
}

.btn-outline {
  background: white;
  color: #4CAF50;
  border: 2rpx solid #4CAF50;
  border-radius: 50rpx;
  height: 88rpx;
  font-size: 30rpx;
}

.btn-outline:active {
  background: #f8f9fa;
  transform: translateY(2rpx);
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 320px) {
  .header-title {
    font-size: 32rpx;
  }
  
  .header-subtitle {
    font-size: 24rpx;
  }
  
  .rule-name {
    font-size: 28rpx;
  }
  
  .rule-points {
    font-size: 24rpx;
  }
} 