<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('crm_agents', function (Blueprint $table) {
            // 先删除外键约束
            $table->dropForeign(['user_id']);
            
            // 重命名列
            $table->renameColumn('user_id', 'employee_id');
        });
        
        // 重新添加外键约束
        Schema::table('crm_agents', function (Blueprint $table) {
            $table->foreign('employee_id')
                  ->references('id')
                  ->on('employees')
                  ->onDelete('cascade');
        });
        
        // 更新注释
        DB::statement('ALTER TABLE `crm_agents` CHANGE `employee_id` `employee_id` BIGINT UNSIGNED NOT NULL COMMENT "关联的员工ID"');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('crm_agents', function (Blueprint $table) {
            // 先删除外键约束
            $table->dropForeign(['employee_id']);
            
            // 重命名列
            $table->renameColumn('employee_id', 'user_id');
        });
        
        // 重新添加外键约束
        Schema::table('crm_agents', function (Blueprint $table) {
            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade');
        });
        
        // 更新注释
        DB::statement('ALTER TABLE `crm_agents` CHANGE `user_id` `user_id` BIGINT UNSIGNED NOT NULL COMMENT "关联的用户ID"');
    }
};
