# 生鲜配送CRM管理系统 - UniApp前端

## 项目简介

这是一个基于UniApp开发的生鲜配送CRM管理系统前端项目，专为员工使用而设计。系统提供了完整的客户关系管理功能，包括代客下单、客户管理、订单管理等核心业务功能。

## 技术栈

- **框架**: UniApp (Vue 2)
- **UI组件**: 官方组件 (无第三方UI框架)
- **状态管理**: Vuex (可选)
- **网络请求**: 封装的request工具类
- **编译目标**: APP (Android/iOS)

## 核心功能

### 🔐 员工认证系统
- 员工登录/登出
- 记住密码功能
- Token自动管理
- 登录状态检查

### 📝 代客下单 (核心功能)
- 客户选择与搜索
- 商品选择与数量管理
- 收货地址选择
- 支付方式配置
- 订单信息填写
- 一键提交订单

### 👥 客户管理
- 客户列表展示
- 客户详情查看
- 客户统计数据
- 订单历史记录
- 跟进记录管理
- 地址信息管理
- 快捷拨号功能

### 📋 订单管理
- 订单列表与筛选
- 订单状态管理
- 订单详情查看
- 状态流程跟踪
- 订单操作 (确认/发货/送达/取消)

### ⚙️ 个人中心
- 员工信息展示
- 工作统计数据
- 密码修改
- 系统设置
- 缓存管理
- 帮助与反馈

## 项目结构

```
crm-uniapp/
├── pages/                      # 页面目录
│   ├── login/                  # 登录页面
│   │   └── login.vue
│   ├── index/                  # 首页
│   │   └── index.vue
│   ├── proxy-order/            # 代客下单
│   │   ├── proxy-order.vue     # 主页面
│   │   ├── select-client.vue   # 选择客户
│   │   └── select-product.vue  # 选择商品
│   ├── clients/                # 客户管理
│   │   ├── clients.vue         # 客户列表
│   │   └── detail.vue          # 客户详情
│   ├── orders/                 # 订单管理
│   │   ├── orders.vue          # 订单列表
│   │   └── detail.vue          # 订单详情
│   └── profile/                # 个人中心
│       └── profile.vue
├── api/                        # API接口
│   ├── auth.js                 # 认证相关
│   ├── client.js               # 客户相关
│   ├── order.js                # 订单相关
│   └── product.js              # 商品相关
├── utils/                      # 工具类
│   ├── config.js               # 配置文件
│   └── request.js              # 网络请求
├── static/                     # 静态资源
│   └── tabbar/                 # 底部导航图标
├── pages.json                  # 页面配置
├── manifest.json               # 应用配置
└── README.md                   # 项目说明
```

## 页面配置

### 底部导航 (TabBar)
- 首页 - 数据概览和快捷操作
- 代客下单 - 核心业务功能
- 客户 - 客户管理
- 订单 - 订单管理
- 我的 - 个人中心

### 页面路由
- `/pages/login/login` - 员工登录
- `/pages/index/index` - 首页仪表板
- `/pages/proxy-order/proxy-order` - 代客下单主页
- `/pages/proxy-order/select-client` - 选择客户
- `/pages/proxy-order/select-product` - 选择商品
- `/pages/clients/clients` - 客户列表
- `/pages/clients/detail` - 客户详情
- `/pages/orders/orders` - 订单列表
- `/pages/orders/detail` - 订单详情
- `/pages/profile/profile` - 个人中心

## API接口设计

### 认证接口 (auth.js)
```javascript
// 员工登录
login(data) // POST /employee/auth/login

// 获取当前员工信息
getProfile() // GET /employee/auth/me

// 员工登出
logout() // POST /employee/auth/logout
```

### 客户接口 (client.js)
```javascript
// 获取客户列表
getClientList(params) // GET /crm/users

// 获取客户详情
getClientDetail(id) // GET /crm/users/{id}

// 根据手机号查找客户
findClientByPhone(phone) // GET /crm/users/by-phone/{phone}

// 获取客户地址列表
getClientAddresses(userId) // GET /crm/user-addresses/user/{userId}

// 获取客户订单列表
getClientOrders(userId, params) // GET /crm/users/{userId}/orders

// 获取客户统计信息
getClientStatistics(userId) // GET /crm/users/{userId}/statistics
```

### 订单接口 (order.js)
```javascript
// 代客下单
createProxyOrder(data) // POST /orders/proxy

// 获取订单列表
getOrderList(params) // GET /orders

// 获取订单详情
getOrderDetail(id) // GET /orders/{id}

// 更新订单状态
updateOrderStatus(id, status) // PUT /orders/{id}/status

// 取消订单
cancelOrder(id) // PUT /orders/{id}/cancel
```

### 商品接口 (product.js)
```javascript
// 获取商品列表
getProductList(params) // GET /products

// 获取商品详情
getProductDetail(id) // GET /products/{id}

// 搜索商品
searchProducts(keyword) // GET /products

// 获取商品分类
getCategories() // GET /categories
```

## 核心特性

### 🎨 现代化UI设计
- 渐变色彩搭配
- 卡片式布局
- 圆角设计
- 阴影效果
- 响应式交互

### 📱 移动端优化
- 专为APP设计
- 触摸友好交互
- 状态栏适配
- 下拉刷新
- 上拉加载更多

### ⚡ 性能优化
- 分页加载
- 防抖搜索
- 图片懒加载
- 缓存管理
- 网络请求优化

### 🔒 安全特性
- Token自动管理
- 请求拦截器
- 错误处理
- 登录状态检查
- 数据加密传输

### 🛠️ 开发特性
- 模块化架构
- 统一错误处理
- 配置文件管理
- 工具类封装
- 代码注释完整

## 业务流程

### 代客下单流程
1. 员工登录系统
2. 选择或搜索客户
3. 选择客户收货地址
4. 添加订单商品
5. 配置支付和配送方式
6. 填写订单备注
7. 确认并提交订单

### 订单管理流程
1. 查看订单列表
2. 筛选订单状态
3. 查看订单详情
4. 执行订单操作：
   - 待付款 → 确认订单
   - 已付款 → 发货
   - 已发货 → 确认送达
   - 取消订单

### 客户管理流程
1. 查看客户列表
2. 搜索特定客户
3. 查看客户详情
4. 查看客户统计
5. 添加跟进记录
6. 为客户下单

## 配置说明

### 环境配置 (utils/config.js)
```javascript
// API地址配置
baseUrl: 'http://localhost:8000/api'

// 存储键名配置
storageKeys: {
    token: 'crm_token',
    userInfo: 'crm_user_info',
    employeeInfo: 'crm_employee_info'
}

// 角色映射
roles: {
    admin: '管理员',
    manager: '经理',
    crm_agent: 'CRM专员',
    // ...
}
```

### 网络请求配置 (utils/request.js)
- 自动添加Token
- 请求/响应拦截
- 错误统一处理
- 加载状态管理
- 超时处理

## 部署说明

### 开发环境
1. 安装HBuilderX
2. 导入项目
3. 配置API地址
4. 运行到模拟器或真机

### 生产环境
1. 修改API地址为生产环境
2. 配置应用图标和启动页
3. 设置应用权限
4. 打包发布到应用商店

## 注意事项

1. **API地址配置**: 需要根据实际后端地址修改`utils/config.js`中的`baseUrl`
2. **图标资源**: 需要准备底部导航栏图标文件
3. **权限配置**: 需要在`manifest.json`中配置相应的系统权限
4. **兼容性**: 建议在多种设备上测试兼容性
5. **性能优化**: 大量数据时建议启用虚拟列表

## 后续扩展

### 功能扩展
- [ ] 数据统计图表
- [ ] 消息推送
- [ ] 离线数据同步
- [ ] 语音输入
- [ ] 扫码功能
- [ ] 地图导航

### 技术优化
- [ ] 升级到Vue 3
- [ ] 引入TypeScript
- [ ] 添加单元测试
- [ ] 性能监控
- [ ] 错误上报

## 联系方式

如有问题或建议，请联系开发团队。

---

© 2024 生鲜配送CRM系统 - UniApp前端项目 