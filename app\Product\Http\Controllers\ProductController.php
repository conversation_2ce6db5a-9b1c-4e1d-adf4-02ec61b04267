<?php

namespace App\Product\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Product\Models\Category;
use App\Product\Models\Product;
use App\Product\Services\ProductService;
use App\Unit\Models\Unit;
use App\Product\Models\ProductImage;
use App\Product\Models\ProductUnit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class ProductController extends Controller
{
    /**
     * 产品服务
     */
    protected $productService;
    
    /**
     * 构造函数
     */
    public function __construct(ProductService $productService)
    {
        $this->productService = $productService;
    }
    
    /**
     * 获取商品列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // 记录请求参数
        Log::info('获取商品列表请求', [
            'request_params' => $request->all(),
        ]);
        
        try {
            // 构建过滤条件
            $filters = [
                'category_id' => $request->input('category_id'),
                'keyword' => $request->input('keyword'),
                'status' => $request->input('status'),
                'sort_field' => $request->input('sort_field', 'id'),
                'sort_order' => $request->input('sort_order', 'desc'),
            ];
            
            // 如果请求包含单位信息，添加相关过滤条件
            if ($request->has('with_units') && $request->input('with_units')) {
                $filters['include_warehouses'] = true;
                $filters['with_inventory'] = true;
            }
            
            // 通过服务层获取商品列表
            $products = $this->productService->getProducts($filters, $request->input('limit', 10));
            
            // 获取当前用户（可能为null，表示游客）
            $user = \Illuminate\Support\Facades\Auth::guard('sanctum')->user();
            $regionId = $user?->region_id ?? $request->input('region_id');
            
            // 检查是否需要价格信息
            $withPrices = $request->input('with_prices', false);
            
            // 如果请求包含价格信息，为每个商品计算价格
            if ($withPrices) {
                $priceService = app(\App\Product\Services\PriceCalculationService::class);
                
                $products->getCollection()->transform(function ($product) use ($priceService, $user, $regionId) {
                    // 计算价格
                    $priceData = $priceService->calculatePrice($product, $user, $regionId, 1);
                    
                    // 添加价格信息到商品数据
                    $product->pricing = $priceData;
                    $product->price_display = $product->getPriceDisplay($user, $regionId);
                    
                    return $product;
                });
            }
            
            // 如果请求包含单位信息，为每个商品添加单位数据
            if ($request->has('with_units') && $request->input('with_units')) {
                $products->getCollection()->transform(function ($product) {
                    // 添加所有单位信息
                    $product->available_units = $product->getAllUnits();
                    return $product;
                });
            }
            
            Log::info('商品列表查询结果', [
                'count' => count($products->items()),
                'total' => $products->total(),
                'current_page' => $products->currentPage(),
                'per_page' => $products->perPage(),
                'with_prices' => $withPrices,
                'user_id' => $user?->id,
                'region_id' => $regionId
            ]);
            
            return response()->json(ApiResponse::success($products));
        } catch (\Exception $e) {
            Log::error('获取商品列表失败', [
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取商品列表失败: ' . $e->getMessage()), 500);
        }
    }
    
    /**
     * 创建商品
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // 添加日志记录
        Log::info('创建商品请求', [
            'request_data' => $request->all()
        ]);
        
        // 验证输入
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|min:2|max:100',
            'code' => 'nullable|string|max:50|unique:products,code',
            'description' => 'nullable|string|max:500',
            'category_id' => 'required|exists:categories,id',
            'status' => 'nullable|in:0,1',
            'sale_price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'base_unit_id' => 'required|exists:units,id',
            'sale_unit_id' => 'nullable|exists:units,id',
            'purchase_unit_id' => 'nullable|exists:units,id',
            'sale_conversion_factor' => 'nullable|numeric|min:0.01',
            'purchase_conversion_factor' => 'nullable|numeric|min:0.01',
            'initial_stock' => 'nullable|numeric|min:0',
            'min_sale_quantity' => 'nullable|numeric|min:0.01',
        ]);
        
        if ($validator->fails()) {
            Log::error('商品创建验证失败', [
                'errors' => $validator->errors()->toArray()
            ]);
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        // 构建商品数据
        $productData = $request->all();
        
        // 提取单位相关数据，稍后处理
        $saleUnitId = $request->input('sale_unit_id');
        $purchaseUnitId = $request->input('purchase_unit_id');
        $saleConversionFactor = $request->input('sale_conversion_factor', 1.0);
        $purchaseConversionFactor = $request->input('purchase_conversion_factor', 1.0);
        
        // 自动生成商品编码
        if (!isset($productData['code']) || empty($productData['code'])) {
            $productData['code'] = Product::generateProductCode();
            Log::info('自动生成商品编码', ['code' => $productData['code']]);
        }
        
        // 设置默认值
        $productData['status'] = $productData['status'] ?? 1; // 默认上架
        $productData['stock'] = 0; // 初始为0，后续通过库存记录计算
        $productData['price'] = $productData['sale_price']; // 兼容旧字段
        
        // 移除不属于products表的字段
        $fieldsToRemove = [
            'sale_unit_id', 
            'purchase_unit_id', 
            'sale_conversion_factor', 
            'purchase_conversion_factor',
            'initial_stock',
        ];
        foreach ($fieldsToRemove as $field) {
            unset($productData[$field]);
        }
        
        // 处理图片上传
        if ($request->hasFile('cover')) {
            // 保存图片到公共目录
            $path = $request->file('cover')->store('products', 'public');
            // 生成完整的URL，包含域名
            $fullUrl = asset('storage/' . $path);
            $productData['cover_url'] = $fullUrl;
            
            Log::info('上传了新的图片文件', [
                'path' => $path, 
                'storage_path' => storage_path('app/public/' . $path),
                'public_url' => $fullUrl
            ]);
        } 
        // 如果没有文件上传但提供了cover_url，直接使用
        else if ($request->has('cover_url') && !empty($request->input('cover_url'))) {
            $coverUrl = $request->input('cover_url');
            
            // 处理cover_url为JSON字符串格式的数组的情况
            if (is_string($coverUrl) && (str_starts_with($coverUrl, '[') || str_starts_with($coverUrl, '["'))) {
                try {
                    $decodedUrl = json_decode($coverUrl, true);
                    if (is_array($decodedUrl) && !empty($decodedUrl)) {
                        Log::warning('cover_url是JSON数组字符串，尝试解析', ['cover_url' => $coverUrl, 'decoded' => $decodedUrl]);
                        $coverUrl = $decodedUrl[0] ?? null;
                    }
                } catch (\Exception $e) {
                    Log::error('解析cover_url JSON失败', ['error' => $e->getMessage(), 'cover_url' => $coverUrl]);
                }
            }
            // 处理cover_url为数组的情况
            else if (is_array($coverUrl)) {
                Log::warning('cover_url是数组格式，将使用第一个元素', ['cover_url' => $coverUrl]);
                if (!empty($coverUrl) && isset($coverUrl[0]) && is_string($coverUrl[0])) {
                    $coverUrl = $coverUrl[0];
                } else {
                    $coverUrl = null;
                }
            }
            
            // 检查是否为blob URL，如果是则忽略
            if ($coverUrl && is_string($coverUrl) && strpos($coverUrl, 'blob:') === 0) {
                Log::warning('检测到blob URL，这是临时URL无法使用', ['url' => $coverUrl]);
                $coverUrl = null;
            }
            
            if ($coverUrl) {
                $productData['cover_url'] = $coverUrl;
                Log::info('使用提供的图片URL', ['url' => $coverUrl]);
            } else {
                unset($productData['cover_url']);
            }
        }
        
        // 创建商品前记录所有将要创建的字段
        Log::info('将要创建的商品数据', [
            'product_data' => $productData
        ]);
        
        // 创建商品
        $product = Product::create($productData);
        
        // 处理销售单位和采购单位关联
        $this->handleProductUnits($product, $saleUnitId, $purchaseUnitId, $saleConversionFactor, $purchaseConversionFactor);
        
        // 处理初始库存
        $initialStock = $request->input('initial_stock');
        if ($initialStock && $initialStock > 0) {
            try {
                // 获取默认仓库（如果没有则创建一个）
                $defaultWarehouse = \App\Warehouse\Models\Warehouse::firstOrCreate(
                    ['location' => '默认仓库'],
                    ['total_stock' => 0]
                );
                
                // 使用InventoryService创建库存记录
                $inventoryService = new \App\Warehouse\Services\InventoryService();
                $result = $inventoryService->initializeOrUpdateStock(
                    $product->id, 
                    $defaultWarehouse->id, 
                    $initialStock, 
                    $product->base_unit_id
                );
                
                if ($result['success']) {
                    // 更新商品总库存
                    $product->updateTotalStock();
                    
                    Log::info('初始库存创建成功', [
                        'product_id' => $product->id,
                        'warehouse_id' => $defaultWarehouse->id,
                        'initial_stock' => $initialStock,
                        'unit_id' => $product->base_unit_id,
                        'updated_product_stock' => $product->fresh()->stock
                    ]);
                } else {
                    Log::warning('初始库存创建失败', [
                        'product_id' => $product->id,
                        'error' => $result['message']
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('处理初始库存时发生错误', [
                    'product_id' => $product->id,
                    'initial_stock' => $initialStock,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        // 记录创建后的商品数据
        Log::info('创建后的商品数据', [
            'product' => $product->toArray()
        ]);
        
        // 测试：验证商品单位数据
        try {
            $testUnits = $product->getAllUnits();
            Log::info('测试：获取商品所有单位', [
                'product_id' => $product->id,
                'units' => $testUnits
            ]);
            
            // 直接查询数据库中的原始数据
            $rawProductUnits = DB::table('product_units')
                ->where('product_id', $product->id)
                ->get();
            Log::info('测试：数据库中的原始单位数据', [
                'product_id' => $product->id,
                'raw_data' => $rawProductUnits->toArray()
            ]);
            
            // 检查库存记录
            $inventoryRecords = \App\Inventory\Models\Inventory::where('product_id', $product->id)->get();
            Log::info('测试：商品的库存记录', [
                'product_id' => $product->id,
                'inventory_count' => $inventoryRecords->count(),
                'inventory_data' => $inventoryRecords->map(function($inv) {
                    return [
                        'id' => $inv->id,
                        'warehouse_id' => $inv->warehouse_id,
                        'stock' => $inv->stock,
                        'unit_id' => $inv->unit_id
                    ];
                })->toArray()
            ]);
        } catch (\Exception $e) {
            Log::error('测试获取商品单位失败', [
                'product_id' => $product->id,
                'error' => $e->getMessage()
            ]);
        }
        
        return response()->json(ApiResponse::success($product, '商品创建成功'), 201);
    }
    
    /**
     * 获取商品详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $product = Product::with(['category', 'baseUnit', 'images'])->findOrFail($id);
            
            // 获取销售单位和采购单位信息
            $saleUnit = ProductUnit::with('unit')
                ->where('product_id', $id)
                ->whereJsonContains('roles', 'sales')
                ->first();
                
            $purchaseUnit = ProductUnit::with('unit')
                ->where('product_id', $id)
                ->whereJsonContains('roles', 'purchase')
                ->first();
            
            // 构建返回数据
            $productData = $product->toArray();
            
            // 添加单位信息
            $productData['sale_unit_id'] = $saleUnit ? $saleUnit->unit_id : null;
            $productData['sale_unit'] = $saleUnit ? $saleUnit->unit : null;
            $productData['sale_conversion_factor'] = $saleUnit ? $saleUnit->conversion_factor : 1.0;
            
            $productData['purchase_unit_id'] = $purchaseUnit ? $purchaseUnit->unit_id : null;
            $productData['purchase_unit'] = $purchaseUnit ? $purchaseUnit->unit : null;
            $productData['purchase_conversion_factor'] = $purchaseUnit ? $purchaseUnit->conversion_factor : 1.0;
            
            return response()->json(ApiResponse::success($productData));
        } catch (\Exception $e) {
            Log::error('获取商品详情失败', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取商品详情失败'), 500);
        }
    }
    
    /**
     * 更新商品
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // 添加日志记录
        Log::info('更新商品请求', [
            'id' => $id,
            'request_data' => $request->all()
        ]);
        
        $product = Product::findOrFail($id);
        
        // 验证输入
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'price' => 'sometimes|numeric|min:0',
            'sale_price' => 'sometimes|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'description' => 'nullable|string',
            'stock' => 'sometimes|numeric|min:0',
            'base_unit_id' => 'sometimes|exists:units,id',
            'category_id' => 'nullable|exists:categories,id',
            'status' => 'sometimes|in:0,1',
            'sale_unit_id' => 'nullable|exists:units,id',
            'purchase_unit_id' => 'nullable|exists:units,id',
            'sale_conversion_factor' => 'nullable|numeric|min:0.01',
            'purchase_conversion_factor' => 'nullable|numeric|min:0.01',
            'min_sale_quantity' => 'nullable|numeric|min:0.01',
        ]);
        
        if ($validator->fails()) {
            Log::error('商品更新验证失败', [
                'errors' => $validator->errors()->toArray()
            ]);
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        // 获取旧的基本单位ID
        $oldBaseUnitId = $product->base_unit_id;
        
        // 处理图片上传和图片URL
        $updateData = $request->all();
        
        // 处理图片数据
        if (isset($updateData['images']) && is_array($updateData['images'])) {
            try {
                // 保存商品图片
                Log::info('处理商品图片', [
                    'images_count' => count($updateData['images'])
                ]);
                
                $product->saveImages($updateData['images']);
                
                // 从更新数据中移除images字段，避免尝试直接更新到product表
                unset($updateData['images']);
            } catch (\Exception $e) {
                Log::error('保存商品图片失败', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
        
        // 提取单位相关数据，稍后处理
        $saleUnitId = $request->input('sale_unit_id');
        $purchaseUnitId = $request->input('purchase_unit_id');
        $saleConversionFactor = $request->input('sale_conversion_factor', 1.0);
        $purchaseConversionFactor = $request->input('purchase_conversion_factor', 1.0);
        
        // 移除不属于products表的字段
        $fieldsToRemove = [
            'sale_unit_id', 
            'purchase_unit_id', 
            'sale_conversion_factor', 
            'purchase_conversion_factor',
            'current_stock', // 当前库存是只读的，不应该直接更新
        ];
        foreach ($fieldsToRemove as $field) {
            unset($updateData[$field]);
        }
        
        // 兼容性处理：如果有sale_price，同时更新price字段
        if (isset($updateData['sale_price'])) {
            $updateData['price'] = $updateData['sale_price'];
        }
        
        // 更新商品前记录所有将要更新的字段
        Log::info('将要更新的字段', [
            'update_data' => $updateData
        ]);
        
        // 更新商品
        $product->update($updateData);
        
        // 处理销售单位和采购单位关联更新
        $this->updateProductUnits($product, $saleUnitId, $purchaseUnitId, $saleConversionFactor, $purchaseConversionFactor);
        
        // 记录更新后的商品数据
        Log::info('更新后的商品数据', [
            'product' => $product->refresh()->toArray()
        ]);
        
        // 如果基本单位改变，需要重新计算库存
        if ($request->has('base_unit_id') && $request->base_unit_id != $oldBaseUnitId) {
            // 获取新旧基本单位
            $oldBaseUnit = Unit::find($oldBaseUnitId);
            $newBaseUnit = Unit::find($product->base_unit_id);
            
            if ($oldBaseUnit && $newBaseUnit) {
                Log::info('商品基本单位变更，将调整库存', [
                    'product_id' => $product->id,
                    'old_unit' => $oldBaseUnit->name,
                    'new_unit' => $newBaseUnit->name
                ]);
                
                // 重新计算所有仓库的库存
                foreach ($product->inventories as $inventory) {
                    $stockInOldBase = $inventory->stock;
                    
                    // 使用unit_id直接查找而不是通过unit字符串
                    if ($inventory->unit_id && $inventory->unit_id != $oldBaseUnitId) {
                        // 通过辅助单位关系获取转换率
                        $auxiliaryUnit = $product->units()
                            ->where('unit_id', $inventory->unit_id)
                            ->first();
                            
                        if ($auxiliaryUnit) {
                            // 转换为旧基本单位
                            $stockInOldBase = $stockInOldBase * $auxiliaryUnit->pivot->conversion_factor;
                        }
                    }
                    
                    // 从旧基本单位转换为新基本单位
                    $stockInNewBase = $stockInOldBase;
                    if ($oldBaseUnitId != $product->base_unit_id) {
                        // 使用UnitService进行单位转换
                        try {
                            $result = app(\App\Unit\Services\UnitService::class)
                                ->convertValue($stockInOldBase, $oldBaseUnit, $newBaseUnit);
                            $stockInNewBase = $result;
                        } catch (\Exception $e) {
                            \Illuminate\Support\Facades\Log::error('基本单位转换失败', [
                                'product_id' => $product->id,
                                'old_base_unit_id' => $oldBaseUnitId,
                                'new_base_unit_id' => $product->base_unit_id,
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                    
                    // 转换为原始单位
                    $stockInCurrentUnit = $stockInNewBase;
                    if ($inventory->unit_id && $inventory->unit_id != $product->base_unit_id) {
                        // 通过辅助单位关系获取转换率
                        $auxiliaryUnit = $product->units()
                            ->where('unit_id', $inventory->unit_id)
                            ->first();
                            
                        if ($auxiliaryUnit) {
                            $stockInCurrentUnit = $stockInNewBase / $auxiliaryUnit->pivot->conversion_factor;
                        }
                    }
                    
                    // 更新库存
                    $inventory->stock = $stockInCurrentUnit;
                    $inventory->save();
                    
                    Log::info('更新仓库库存', [
                        'inventory_id' => $inventory->id,
                        'old_stock' => $stockInOldBase,
                        'new_stock' => $stockInCurrentUnit
                    ]);
                }
                
                // 更新产品总库存
                $this->recalculateProductStock($product);
            }
        }
        
        return response()->json(ApiResponse::success($product, '商品更新成功'));
    }
    
    /**
     * 重新计算产品总库存（基于各仓库库存）
     * 
     * @param Product $product
     * @return void
     */
    private function recalculateProductStock(Product $product)
    {
        // 更新产品总库存 - 转换所有仓库库存到基本单位
        $totalStock = 0;
        foreach ($product->inventories as $inventory) {
            $baseStock = $inventory->stock;
            
            // 使用unit_id替代通过unit字符串查找
            if ($inventory->unit_id && $inventory->unit_id != $product->base_unit_id) {
                // 使用产品的getUnitConversionRate方法获取转换系数
                $conversionFactor = $product->getUnitConversionRate($inventory->unit_id, $product->base_unit_id);
                if ($conversionFactor !== null) {
                    $baseStock = $baseStock * $conversionFactor;
                }
            }
            
            $totalStock += $baseStock;
        }
        
        $product->stock = $totalStock;
        $product->save();
        
        Log::info('更新商品总库存', [
            'product_id' => $product->id,
            'new_total_stock' => $totalStock
        ]);
    }

    /**
     * 删除商品
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $product = Product::findOrFail($id);
            
            // 检查是否有订单关联
            $orderItemsCount = \App\Order\Models\OrderItem::where('product_id', $id)->count();
            if ($orderItemsCount > 0) {
                return response()->json(ApiResponse::error(
                    "无法删除商品：该商品已被 {$orderItemsCount} 个订单使用，不能删除", 
                    422
                ), 422);
            }
            
            // 检查是否有采购订单关联
            $purchaseItemsCount = \App\Purchase\Models\PurchaseItem::where('product_id', $id)->count();
            if ($purchaseItemsCount > 0) {
                return response()->json(ApiResponse::error(
                    "无法删除商品：该商品已被 {$purchaseItemsCount} 个采购订单使用，不能删除", 
                    422
                ), 422);
            }
            
            DB::beginTransaction();
            
            try {
                // 记录删除前的信息
                Log::info('开始删除商品', [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_code' => $product->code
                ]);
                
                // 1. 删除商品图片
                $deletedImagesCount = 0;
                if ($product->images()->exists()) {
                    $images = $product->images()->get();
                    foreach ($images as $image) {
                        try {
                            $image->delete(); // ProductImage模型应该处理文件删除
                            $deletedImagesCount++;
                        } catch (\Exception $e) {
                            Log::warning('删除商品图片失败', [
                                'product_id' => $product->id,
                                'image_id' => $image->id,
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                }
                
                // 2. 删除商品单位关联
                $deletedUnitsCount = ProductUnit::where('product_id', $id)->count();
                ProductUnit::where('product_id', $id)->delete();
                
                // 3. 删除库存记录
                $deletedInventoriesCount = \App\Inventory\Models\Inventory::where('product_id', $id)->count();
                \App\Inventory\Models\Inventory::where('product_id', $id)->delete();
                
                // 4. 删除区域价格
                $deletedRegionPricesCount = 0;
                if (class_exists('\App\Region\Models\RegionPrice')) {
                    $deletedRegionPricesCount = \App\Region\Models\RegionPrice::where('product_id', $id)->count();
                    \App\Region\Models\RegionPrice::where('product_id', $id)->delete();
                }
                
                // 5. 删除库存批次（如果存在）
                $deletedBatchesCount = 0;
                if (class_exists('\App\Inventory\Models\InventoryBatch')) {
                    // 通过库存记录删除批次
                    $inventoryIds = \App\Inventory\Models\Inventory::where('product_id', $id)->pluck('id');
                    if ($inventoryIds->isNotEmpty()) {
                        $deletedBatchesCount = \App\Inventory\Models\InventoryBatch::whereIn('inventory_id', $inventoryIds)->count();
                        \App\Inventory\Models\InventoryBatch::whereIn('inventory_id', $inventoryIds)->delete();
                    }
                }
                
                // 6. 最后删除商品本身
                $product->delete();
                
                DB::commit();
                
                // 记录删除成功的信息
                Log::info('商品删除成功', [
                    'product_id' => $id,
                    'deleted_images' => $deletedImagesCount,
                    'deleted_units' => $deletedUnitsCount,
                    'deleted_inventories' => $deletedInventoriesCount,
                    'deleted_region_prices' => $deletedRegionPricesCount,
                    'deleted_batches' => $deletedBatchesCount
                ]);
                
                return response()->json(ApiResponse::success([
                    'deleted_product' => [
                        'id' => $id,
                        'name' => $product->name,
                        'code' => $product->code
                    ],
                    'cleanup_summary' => [
                        'images' => $deletedImagesCount,
                        'units' => $deletedUnitsCount,
                        'inventories' => $deletedInventoriesCount,
                        'region_prices' => $deletedRegionPricesCount,
                        'batches' => $deletedBatchesCount
                    ]
                ], '商品及相关数据删除成功'));
                
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
            
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(ApiResponse::error('商品不存在', 404), 404);
        } catch (\Exception $e) {
            Log::error('删除商品失败', [
                'product_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('删除商品失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 更新商品状态
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus($id, Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'required|in:0,1',
                'sort' => 'sometimes|nullable|integer|min:0' // 添加排序字段验证
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $status = $request->input('status');
            
            try {
                // 使用ProductService更新状态，会自动进行销售单位检查
                $product = $this->productService->updateProductStatus($id, $status);
                
                // 更新排序字段（如果提供）
                if ($request->has('sort')) {
                    $product->sort = $request->input('sort');
                    $product->save();
                }
                
                return response()->json(ApiResponse::success($product, '商品状态更新成功'));
            } catch (\Exception $e) {
                // 捕获特定的销售单位检查异常
                Log::warning('商品状态更新失败', ['id' => $id, 'error' => $e->getMessage()]);
                return response()->json(ApiResponse::error($e->getMessage()), 422);
            }
        } catch (\Exception $e) {
            Log::error('更新商品状态失败', ['id' => $id, 'error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('更新商品状态失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 获取商品分类列表
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function categories()
    {
        try {
            $categories = Category::where('status', 1)
                ->orderBy('sort', 'asc')
                ->orderBy('id', 'asc')
                ->get();
                
            return response()->json(ApiResponse::success($categories, '获取分类列表成功'));
        } catch (\Exception $e) {
            Log::error('获取商品分类列表出错', [
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取分类列表失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 搜索产品
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        try {
            // 验证请求参数
            $request->validate([
                'keyword' => 'required|string|min:1',
                'per_page' => 'nullable|integer|min:1|max:100',
                'category_id' => 'nullable|integer|exists:categories,id',
            ]);

            $keyword = $request->input('keyword');
            $perPage = $request->input('per_page', 15);
            $categoryId = $request->input('category_id');

            // 使用传统数据库查询，参考用户搜索的实现方式
            $query = Product::with(['category']);
            
            // 关键词搜索
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('code', 'like', "%{$keyword}%")
                  ->orWhere('description', 'like', "%{$keyword}%");
            });
            
            // 如果指定了分类ID，添加过滤
            if ($categoryId) {
                $query->where('category_id', $categoryId);
            }
            
            // 只搜索状态为1（上架）的商品
            $query->where('status', 1);
            
            // 排序：优先显示名称匹配度高的商品
            $query->orderByRaw("CASE 
                WHEN name LIKE ? THEN 1 
                WHEN name LIKE ? THEN 2 
                WHEN code LIKE ? THEN 3 
                ELSE 4 
            END", [
                $keyword,
                "%{$keyword}%", 
                "%{$keyword}%"
            ]);
            
            // 分页获取结果
            $products = $query->paginate($perPage);
            
            // 格式化返回数据，参考用户搜索的格式化方式
            $formattedProducts = collect($products->items())->map(function($product) {
                // 安全获取单位名称
                $unitName = '件'; // 默认单位
                try {
                    if ($product->base_unit_id) {
                        $baseUnit = \App\Unit\Models\Unit::find($product->base_unit_id);
                        if ($baseUnit && $baseUnit->name) {
                            $unitName = $baseUnit->name;
                        }
                    }
                } catch (\Exception $e) {
                    Log::warning('获取商品单位失败', [
                        'product_id' => $product->id,
                        'base_unit_id' => $product->base_unit_id,
                        'error' => $e->getMessage()
                    ]);
                }
                
                return [
                    'id' => $product->id,
                    'name' => $product->name ?? '',
                    'price' => $product->price ?? 0,
                    'stock' => $product->stock ?? 0,
                    'description' => $product->description ?? '',
                    'category_id' => $product->category_id ?? null,
                    'category_name' => $product->category ? $product->category->name : null,
                    'cover_url' => $product->cover_url ?? null,
                    'status' => $product->status ?? 1,
                    'unit' => $unitName,
                    'code' => $product->code ?? '',
                ];
            });

            return response()->json([
                'data' => $formattedProducts,
                'total' => $products->total(),
                'current_page' => $products->currentPage(),
                'per_page' => $products->perPage(),
                'last_page' => $products->lastPage(),
            ]);
        } catch (\Exception $e) {
            Log::error('产品搜索失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            
            return response()->json([
                'message' => '搜索产品失败: ' . $e->getMessage(),
                'status' => 'error'
            ], 500);
        }
    }

    /**
     * 生成商品编码
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateCode(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'category_id' => 'required|exists:categories,id'
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $categoryId = $request->input('category_id');
            $code = Product::generateProductCodeByCategory($categoryId);
            
            return response()->json(ApiResponse::success(['code' => $code], '商品编码生成成功'));
        } catch (\Exception $e) {
            Log::error('生成商品编码失败', [
                'error' => $e->getMessage(),
                'category_id' => $request->input('category_id')
            ]);
            return response()->json(ApiResponse::error('生成商品编码失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 处理商品单位关联
     *
     * @param Product $product
     * @param int|null $saleUnitId
     * @param int|null $purchaseUnitId
     * @param float $saleConversionFactor
     * @param float $purchaseConversionFactor
     * @return void
     */
    private function handleProductUnits(Product $product, $saleUnitId, $purchaseUnitId, $saleConversionFactor, $purchaseConversionFactor)
    {
        Log::info('开始处理商品单位关联', [
            'product_id' => $product->id,
            'sale_unit_id' => $saleUnitId,
            'purchase_unit_id' => $purchaseUnitId,
            'sale_conversion_factor' => $saleConversionFactor,
            'purchase_conversion_factor' => $purchaseConversionFactor
        ]);
        
        // 处理销售单位关联
        if ($saleUnitId) {
            $saleUnit = ProductUnit::updateOrCreate(
                [
                    'product_id' => $product->id,
                    'unit_id' => $saleUnitId
                ],
                [
                    'conversion_factor' => $saleConversionFactor,
                    'roles' => ['sales'],
                    'role_priority' => ['sales' => 10],
                    'is_default' => true,
                    'is_active' => true
                ]
            );
            
            Log::info('销售单位关联创建成功', [
                'product_id' => $product->id,
                'unit_id' => $saleUnitId,
                'conversion_factor' => $saleConversionFactor,
                'product_unit_id' => $saleUnit->id,
                'roles' => $saleUnit->roles
            ]);
        } else {
            Log::info('未提供销售单位ID，跳过销售单位关联创建', [
                'product_id' => $product->id
            ]);
        }

        // 处理采购单位关联
        if ($purchaseUnitId) {
            $purchaseUnit = ProductUnit::updateOrCreate(
                [
                    'product_id' => $product->id,
                    'unit_id' => $purchaseUnitId
                ],
                [
                    'conversion_factor' => $purchaseConversionFactor,
                    'roles' => ['purchase'],
                    'role_priority' => ['purchase' => 10],
                    'is_default' => true,
                    'is_active' => true
                ]
            );
            
            Log::info('采购单位关联创建成功', [
                'product_id' => $product->id,
                'unit_id' => $purchaseUnitId,
                'conversion_factor' => $purchaseConversionFactor,
                'product_unit_id' => $purchaseUnit->id,
                'roles' => $purchaseUnit->roles
            ]);
        } else {
            Log::info('未提供采购单位ID，跳过采购单位关联创建', [
                'product_id' => $product->id
            ]);
        }
        
        // 验证创建结果
        $allProductUnits = ProductUnit::where('product_id', $product->id)->get();
        Log::info('商品单位关联创建完成，当前所有关联', [
            'product_id' => $product->id,
            'total_units' => $allProductUnits->count(),
            'units' => $allProductUnits->map(function($pu) {
                return [
                    'id' => $pu->id,
                    'unit_id' => $pu->unit_id,
                    'roles' => $pu->roles,
                    'conversion_factor' => $pu->conversion_factor,
                    'is_active' => $pu->is_active
                ];
            })->toArray()
        ]);
    }

    /**
     * 更新商品单位关联
     *
     * @param Product $product
     * @param int|null $saleUnitId
     * @param int|null $purchaseUnitId
     * @param float $saleConversionFactor
     * @param float $purchaseConversionFactor
     * @return void
     */
    private function updateProductUnits(Product $product, $saleUnitId, $purchaseUnitId, $saleConversionFactor, $purchaseConversionFactor)
    {
        // 处理销售单位关联
        if ($saleUnitId) {
            ProductUnit::updateOrCreate(
                [
                    'product_id' => $product->id,
                    'unit_id' => $saleUnitId
                ],
                [
                    'conversion_factor' => $saleConversionFactor,
                    'roles' => ['sales'],
                    'role_priority' => ['sales' => 10],
                    'is_default' => true,
                    'is_active' => true
                ]
            );
            
            Log::info('更新销售单位关联', [
                'product_id' => $product->id,
                'unit_id' => $saleUnitId,
                'conversion_factor' => $saleConversionFactor
            ]);
        }

        // 处理采购单位关联
        if ($purchaseUnitId) {
            ProductUnit::updateOrCreate(
                [
                    'product_id' => $product->id,
                    'unit_id' => $purchaseUnitId
                ],
                [
                    'conversion_factor' => $purchaseConversionFactor,
                    'roles' => ['purchase'],
                    'role_priority' => ['purchase' => 10],
                    'is_default' => true,
                    'is_active' => true
                ]
            );
            
            Log::info('更新采购单位关联', [
                'product_id' => $product->id,
                'unit_id' => $purchaseUnitId,
                'conversion_factor' => $purchaseConversionFactor
            ]);
        }
    }

    /**
     * 批量删除商品
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchDestroy(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:products,id'
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $ids = $request->input('ids');
        $results = [
            'success' => [],
            'failed' => [],
            'skipped' => []
        ];
        
        foreach ($ids as $id) {
            try {
                // 检查是否可以删除
                $checkResult = $this->checkCanDelete($id);
                if (!$checkResult['can_delete']) {
                    $results['skipped'][] = [
                        'id' => $id,
                        'reason' => $checkResult['reason']
                    ];
                    continue;
                }
                
                // 执行删除
                $deleteResponse = $this->destroy($id);
                $deleteData = json_decode($deleteResponse->getContent(), true);
                
                if ($deleteResponse->getStatusCode() === 200) {
                    $results['success'][] = [
                        'id' => $id,
                        'data' => $deleteData['data']
                    ];
                } else {
                    $results['failed'][] = [
                        'id' => $id,
                        'error' => $deleteData['message'] ?? '删除失败'
                    ];
                }
                
            } catch (\Exception $e) {
                $results['failed'][] = [
                    'id' => $id,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        $summary = [
            'total' => count($ids),
            'success_count' => count($results['success']),
            'failed_count' => count($results['failed']),
            'skipped_count' => count($results['skipped'])
        ];
        
        Log::info('批量删除商品完成', [
            'summary' => $summary,
            'results' => $results
        ]);
        
        return response()->json(ApiResponse::success([
            'summary' => $summary,
            'results' => $results
        ], "批量删除完成：成功 {$summary['success_count']} 个，失败 {$summary['failed_count']} 个，跳过 {$summary['skipped_count']} 个"));
    }
    
    /**
     * 检查商品是否可以删除
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkCanDelete($id)
    {
        try {
            $product = Product::findOrFail($id);
            
            // 检查是否有关联的订单项
            $hasOrderItems = $product->orderItems()->exists();
            
            // 检查是否有库存记录
            $hasInventoryRecords = $product->inventoryRecords()->exists();
            
            $canDelete = !$hasOrderItems && !$hasInventoryRecords;
            
            $reasons = [];
            if ($hasOrderItems) {
                $reasons[] = '商品已被订单使用';
            }
            if ($hasInventoryRecords) {
                $reasons[] = '商品有库存记录';
            }
            
            return response()->json(ApiResponse::success([
                'can_delete' => $canDelete,
                'reasons' => $reasons
            ]));
            
        } catch (\Exception $e) {
            Log::error('检查商品删除权限失败', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('检查失败'), 500);
        }
    }
    
    /**
     * 检查商品是否可以删除（API接口）
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function canDelete($id)
    {
        $result = $this->checkCanDelete($id);
        
        if ($result['can_delete']) {
            return response()->json(ApiResponse::success($result, '商品可以删除'));
        } else {
            return response()->json(ApiResponse::error($result['reason'], 422, $result['details']), 422);
        }
    }

    /**
     * 获取商品价格信息
     * 根据用户和区域计算最终价格
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPrice(Request $request, $id)
    {
        try {
            $product = Product::findOrFail($id);
            
            // 获取当前用户
            $user = auth('sanctum')->user();
            
            // 获取区域ID
            $regionId = $request->input('region_id');
            
            // 获取数量
            $quantity = $request->input('quantity', 1);
            
            // 计算价格
            $priceInfo = $product->calculatePrice($user, $regionId, $quantity);
            
            return response()->json(ApiResponse::success($priceInfo));
            
        } catch (\Exception $e) {
            Log::error('获取商品价格失败', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取价格失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 获取商品价格展示信息
     * 用于前端显示
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPriceDisplay(Request $request, $id)
    {
        try {
            $product = Product::findOrFail($id);
            
            // 获取当前用户
            $user = auth('sanctum')->user();
            
            // 获取区域ID
            $regionId = $request->input('region_id');
            
            // 获取价格展示信息
            $priceDisplay = $product->getPriceDisplay($user, $regionId);
            
            return response()->json(ApiResponse::success($priceDisplay));
            
        } catch (\Exception $e) {
            Log::error('获取商品价格展示信息失败', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取价格展示信息失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 获取商品完整信息
     * 包含价格、库存、可用性等
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFullInfo(Request $request, $id)
    {
        try {
            $product = Product::findOrFail($id);
            
            // 获取当前用户
            $user = auth('sanctum')->user();
            
            // 获取区域ID
            $regionId = $request->input('region_id');
            
            // 获取数量
            $quantity = $request->input('quantity', 1);
            
            // 获取完整信息
            $fullInfo = $product->getFullInfo($user, $regionId, $quantity);
            
            return response()->json(ApiResponse::success($fullInfo));
            
        } catch (\Exception $e) {
            Log::error('获取商品完整信息失败', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取商品完整信息失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 批量获取商品价格信息
     * 用于购物车、订单确认等场景
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchGetPrices(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'items' => 'required|array',
                'items.*.product_id' => 'required|integer|exists:products,id',
                'items.*.quantity' => 'required|integer|min:1',
                'region_id' => 'nullable|integer|exists:regions,id'
            ]);

            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }

            // 获取当前用户
            $user = auth('sanctum')->user();
            
            // 获取区域ID
            $regionId = $request->input('region_id');
            
            $items = $request->input('items');
            $results = [];
            $totalAmount = 0;
            $totalDiscount = 0;

            foreach ($items as $item) {
                $product = Product::findOrFail($item['product_id']);
                $quantity = $item['quantity'];
                
                // 计算价格
                $priceInfo = $product->calculatePrice($user, $regionId, $quantity);
                
                // 检查可用性
                $availability = $product->checkAvailability($quantity, $regionId);
                
                $results[] = [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'quantity' => $quantity,
                    'pricing' => $priceInfo,
                    'availability' => $availability
                ];
                
                if ($availability['available']) {
                    $totalAmount += $priceInfo['item_total'];
                    $totalDiscount += $priceInfo['total_discount'] * $quantity;
                }
            }

            return response()->json(ApiResponse::success([
                'items' => $results,
                'summary' => [
                    'total_amount' => round($totalAmount, 2),
                    'total_discount' => round($totalDiscount, 2),
                    'original_amount' => round($totalAmount + $totalDiscount, 2)
                ],
                'context' => [
                    'user_id' => $user ? $user->id : null,
                    'region_id' => $regionId,
                    'calculated_at' => now()->toISOString()
                ]
            ]));
            
        } catch (\Exception $e) {
            Log::error('批量获取商品价格失败', [
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('批量获取价格失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 获取商品统计数据
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats()
    {
        try {
            // 获取商品总数
            $totalProducts = Product::count();
            
            // 获取在售商品数量
            $activeProducts = Product::where('status', 1)->count();
            
            // 获取低库存商品数量（假设库存小于等于10为低库存）
            $lowStockProducts = Product::where('stock', '<=', 10)->where('stock', '>', 0)->count();
            
            // 获取缺货商品数量
            $outOfStockProducts = Product::where('stock', '<=', 0)->count();
            
            // 获取库存总价值
            $totalValue = Product::where('status', 1)
                ->selectRaw('SUM(stock * sale_price) as total_value')
                ->value('total_value') ?? 0;
            
            // 获取平均价格
            $averagePrice = Product::where('status', 1)
                ->avg('sale_price') ?? 0;
            
            $stats = [
                'totalProducts' => $totalProducts,
                'activeProducts' => $activeProducts,
                'lowStockProducts' => $lowStockProducts,
                'outOfStockProducts' => $outOfStockProducts,
                'totalValue' => round($totalValue, 2),
                'averagePrice' => round($averagePrice, 2),
            ];
            
            return response()->json(ApiResponse::success($stats, '获取统计数据成功'));
        } catch (\Exception $e) {
            Log::error('获取商品统计数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(ApiResponse::error('获取统计数据失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 获取商品积分设置
     *
     * @param int $id 商品ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPointsSettings($id)
    {
        try {
            $product = Product::findOrFail($id);
            
            // 检查积分字段是否存在
            if (!Schema::hasColumn('products', 'points_reward_enabled')) {
                return response()->json(ApiResponse::error('积分功能尚未启用，请先运行数据库迁移'), 500);
            }
            
            $pointsSettings = [
                'points_enabled' => $product->points_reward_enabled ?? true,
                'points_type' => ($product->points_reward_type === 'rate') ? 'percentage' : ($product->points_reward_type ?? 'fixed'),
                'points_min_amount' => $product->points_min_amount ?? 0,
                'points_max_per_order' => $product->points_reward_max ?? 0,
            ];
            
            // 根据积分类型设置对应的值
            if ($product->points_reward_type === 'fixed') {
                $pointsSettings['points_value'] = $product->points_reward_fixed ?? 1;
                $pointsSettings['points_ratio'] = 100; // 固定积分模式也提供默认比例值
            } else {
                // 按比例积分模式
                $pointsSettings['points_value'] = 1; // 按比例模式也提供默认固定值
                
                // 获取数据库中的比例值
                $rate = $product->points_reward_rate;
                
                // 处理数据库中的比例值，转换为前端显示格式
                if ($rate !== null && $rate > 0) {
                    $floatRate = (float)$rate;
                    // 数据库存储的是小数比例值，需要转换为前端显示格式
                    // 例如：数据库存储0.01，前端显示100（100元获得1积分）
                    $pointsSettings['points_ratio'] = round(1 / $floatRate);
                } else {
                    // 如果是rate类型但rate为null或0，给一个合理的默认值
                    $pointsSettings['points_ratio'] = 100; // 默认100元获得1积分
                }
            }
            
            Log::info('获取商品积分设置', [
                'product_id' => $id,
                'settings' => $pointsSettings
            ]);
            
            return response()->json(ApiResponse::success($pointsSettings));
        } catch (\Exception $e) {
            Log::error('获取商品积分设置失败', [
                'product_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(ApiResponse::error('获取积分设置失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 更新商品积分设置
     *
     * @param Request $request
     * @param int $id 商品ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePointsSettings(Request $request, $id)
    {
        try {
            $product = Product::findOrFail($id);
            
            // 检查积分字段是否存在
            if (!Schema::hasColumn('products', 'points_reward_enabled')) {
                return response()->json(ApiResponse::error('积分功能尚未启用，请先运行数据库迁移'), 500);
            }
            
            // 验证输入
            $validator = Validator::make($request->all(), [
                'points_enabled' => 'required|boolean',
                'points_type' => 'required|in:percentage,fixed',
                'points_value' => 'required_if:points_type,fixed|nullable|numeric|min:0',
                'points_ratio' => 'required_if:points_type,percentage|nullable|integer|min:1',
                'points_min_amount' => 'nullable|numeric|min:0',
                'points_max_per_order' => 'nullable|integer|min:0',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $data = $request->all();
            
            // 更新积分设置
            $product->points_reward_enabled = $data['points_enabled'];
            $product->points_min_amount = $data['points_min_amount'] ?? 0;
            $product->points_reward_max = $data['points_max_per_order'] ?? null;
            
            if ($data['points_type'] === 'fixed') {
                // 固定积分模式
                $product->points_reward_type = 'fixed';
                $product->points_reward_fixed = $data['points_value'] ?? 1;
                $product->points_reward_rate = 0;
            } else {
                // 按比例积分模式
                $product->points_reward_type = 'rate';
                $product->points_reward_fixed = 0;
                
                // 根据积分换算比例计算比率（例如：100元获得1积分，比率为0.01）
                $ratio = $data['points_ratio'] ?? 100;
                $product->points_reward_rate = 1 / $ratio;
            }
            
            $product->save();
            
            Log::info('更新商品积分设置成功', [
                'product_id' => $id,
                'settings' => $data
            ]);
            
            return response()->json(ApiResponse::success([
                'message' => '积分设置更新成功',
                'settings' => $product->getPointsRewardConfig()
            ]));
        } catch (\Exception $e) {
            Log::error('更新商品积分设置失败', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('更新积分设置失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 批量更新商品积分设置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchUpdatePointsSettings(Request $request)
    {
        try {
            // 验证输入
            $validator = Validator::make($request->all(), [
                'product_ids' => 'required|array|min:1',
                'product_ids.*' => 'integer|exists:products,id',
                'settings' => 'required|array',
                'settings.points_enabled' => 'required|boolean',
                'settings.points_type' => 'required|in:percentage,fixed',
                'settings.points_value' => 'required_if:settings.points_type,fixed|nullable|numeric|min:0',
                'settings.points_ratio' => 'required_if:settings.points_type,percentage|nullable|integer|min:1',
                'settings.points_min_amount' => 'nullable|numeric|min:0',
                'settings.points_max_per_order' => 'nullable|integer|min:0',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $productIds = $request->input('product_ids');
            $settings = $request->input('settings');
            
            DB::beginTransaction();
            
            $products = Product::whereIn('id', $productIds)->get();
            
            foreach ($products as $product) {
                $product->points_reward_enabled = $settings['points_enabled'];
                $product->points_min_amount = $settings['points_min_amount'] ?? 0;
                $product->points_reward_max = $settings['points_max_per_order'] ?? null;
                
                if ($settings['points_type'] === 'fixed') {
                    // 固定积分模式
                    $product->points_reward_type = 'fixed';
                    $product->points_reward_fixed = $settings['points_value'] ?? 1;
                    $product->points_reward_rate = 0;
                } else {
                    // 按比例积分模式
                    $product->points_reward_type = 'rate';
                    $product->points_reward_fixed = 0;
                    
                    // 根据积分换算比例计算比率（例如：100元获得1积分，比率为0.01）
                    $ratio = $settings['points_ratio'] ?? 100;
                    $product->points_reward_rate = 1 / $ratio;
                }
                
                $product->save();
            }
            
            DB::commit();
            
            Log::info('批量更新商品积分设置成功', [
                'product_ids' => $productIds,
                'settings' => $settings,
                'updated_count' => count($products)
            ]);
            
            return response()->json(ApiResponse::success([
                'message' => "成功更新 " . count($products) . " 个商品的积分设置",
                'updated_count' => count($products)
            ]));
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('批量更新商品积分设置失败', [
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('批量更新积分设置失败: ' . $e->getMessage()), 500);
        }
    }
} 