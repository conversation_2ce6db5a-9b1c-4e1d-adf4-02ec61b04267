<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CheckProductStatus extends Command
{
    protected $signature = 'check:product-status';
    protected $description = '检查商品状态和分类情况';

    public function handle()
    {
        $this->info("=== 商品状态统计 ===");
        
        $statusStats = DB::table('products')
            ->selectRaw('status, count(*) as count')
            ->groupBy('status')
            ->get();
            
        foreach ($statusStats as $stat) {
            $this->line("状态 {$stat->status}: {$stat->count} 个商品");
        }
        
        $this->info("\n=== 分类关联统计 ===");
        $withCategory = DB::table('products')->whereNotNull('category_id')->count();
        $withoutCategory = DB::table('products')->whereNull('category_id')->count();
        
        $this->line("有分类的商品: {$withCategory}");
        $this->line("无分类的商品: {$withoutCategory}");
        
        $this->info("\n=== 各分类商品数量 ===");
        $categoryStats = DB::table('products')
            ->whereNotNull('category_id')
            ->groupBy('category_id')
            ->selectRaw('category_id, count(*) as count')
            ->orderBy('category_id')
            ->get();
            
        foreach ($categoryStats as $stat) {
            $this->line("分类ID {$stat->category_id}: {$stat->count} 个商品");
        }
        
        $this->info("\n=== 上架且有分类的商品 ===");
        $activeWithCategory = DB::table('products')
            ->where('status', 'active')
            ->whereNotNull('category_id')
            ->count();
            
        $this->line("上架且有分类的商品: {$activeWithCategory}");
        
        $this->info("\n=== 销售单位检查 ===");
        $withSalesUnit = DB::table('products as p')
            ->join('product_units as pu', 'p.id', '=', 'pu.product_id')
            ->where('pu.has_sales_role', 1)
            ->where('pu.is_active', 1)
            ->count();
            
        $this->line("有销售单位的商品: {$withSalesUnit}");
        
        return 0;
    }
} 