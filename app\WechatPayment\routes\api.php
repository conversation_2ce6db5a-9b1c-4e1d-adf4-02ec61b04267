<?php

use Illuminate\Support\Facades\Route;
use App\WechatPayment\Http\Controllers\WechatServiceProviderController;
use App\WechatPayment\Http\Controllers\WechatServicePaymentController;
use App\WechatPayment\Http\Controllers\WechatServiceRefundController;
use App\WechatPayment\Http\Controllers\WechatSubMerchantController;

/*
|--------------------------------------------------------------------------
| WechatPayment Module Routes
|--------------------------------------------------------------------------
|
| 微信支付模块路由配置
|
*/

// 微信支付模块API路由
Route::prefix('api/wechat-payment')->group(function () {
    // 服务商配置相关API
    Route::get('/service-provider', [WechatServiceProviderController::class, 'index']);
    Route::get('/service-provider/options', [WechatServiceProviderController::class, 'options']);
    Route::post('/service-provider', [WechatServiceProviderController::class, 'store']);
    Route::get('/service-provider/{id}', [WechatServiceProviderController::class, 'show']);
    Route::put('/service-provider/{id}', [WechatServiceProviderController::class, 'update']);
    Route::delete('/service-provider/{id}', [WechatServiceProviderController::class, 'destroy']);
    
    // 子商户相关API
    Route::get('/sub-merchant', [WechatSubMerchantController::class, 'index']);
    Route::post('/sub-merchant', [WechatSubMerchantController::class, 'store']);
    Route::get('/sub-merchant/{id}', [WechatSubMerchantController::class, 'show']);
    Route::put('/sub-merchant/{id}', [WechatSubMerchantController::class, 'update']);
    Route::delete('/sub-merchant/{id}', [WechatSubMerchantController::class, 'destroy']);
    
    // 支付记录相关API
    Route::get('/record', [WechatServicePaymentController::class, 'records']);
    
    // 退款记录相关API
    Route::get('/refund', [WechatServiceRefundController::class, 'index']);
    Route::post('/refund', [WechatServiceRefundController::class, 'refund']);
    Route::get('/refund/{id}', [WechatServiceRefundController::class, 'show']);
    Route::get('/refund-status', [WechatServiceRefundController::class, 'queryRefundStatus']);
    
    // 支付相关API
    Route::middleware(['auth:sanctum'])->group(function () {
        Route::post('/mini-app-pay', [WechatServicePaymentController::class, 'miniAppPay']);
        Route::get('/order-status', [WechatServicePaymentController::class, 'queryOrderStatus']);
    });
    
    // 支付回调API（无需认证）
    Route::post('/notify/pay', [WechatServicePaymentController::class, 'payNotify']);
    Route::post('/notify/refund', [WechatServicePaymentController::class, 'refundNotify']);
}); 