# 购物车定时器错误修复

## 错误详情
```
TypeError: Cannot read property 'update_5' of undefined
at _callee8$ (index.js:723)
at updateItemQuantity (index.js:768)
```

## 问题分析
1. `this._timers` 对象在某些情况下未正确初始化
2. 异步方法中的上下文可能丢失
3. 页面生命周期中的初始化可能被跳过

## 修复措施

### 1. 强化初始化检查
```javascript
// 在 onLoad 中初始化所有定时器对象
onLoad() {
  this._timers = {};
  this._debugTimer = null;
  this.tabBarUpdateTimer = null;
}
```

### 2. 创建安全的初始化方法
```javascript
ensureTimersInitialized() {
  try {
    if (!this._timers || typeof this._timers !== 'object') {
      this._timers = {};
      console.log('🔧 重新初始化 _timers 对象');
    }
    if (this._debugTimer === undefined) {
      this._debugTimer = null;
    }
  } catch (error) {
    console.error('❌ 定时器初始化失败:', error);
    this._timers = {};
    this._debugTimer = null;
  }
}
```

### 3. 修复异步上下文问题
```javascript
// 保存当前上下文，防止 this 绑定丢失
const self = this;
const timerKey = `update_${itemId}`;

this._timers[timerKey] = setTimeout(async () => {
  // 使用保存的上下文
  if (!self._timers) {
    self._timers = {};
  }
  // ... 其他逻辑
});
```

### 4. 添加备选更新方法
```javascript
// 简化的数量更新方法（不使用定时器）
async simpleUpdateQuantity(index, itemId, quantity) {
  try {
    if (!isLoggedIn()) {
      wx.showToast({ title: '请先登录', icon: 'none' });
      return;
    }
    await cartManager.updateQuantity(itemId, quantity);
    await this.loadCartData();
    wx.showToast({ title: '更新成功', icon: 'success' });
  } catch (error) {
    wx.showToast({ title: '更新失败', icon: 'error' });
  }
}
```

### 5. 添加错误处理机制
```javascript
// 在所有调用处添加 try-catch
try {
  this.updateItemQuantity(index, item.id, newQty);
} catch (error) {
  console.warn('主要更新方法失败，使用简化方法:', error);
  this.simpleUpdateQuantity(index, item.id, newQty);
}
```

### 3. 在关键方法中添加安全检查
```javascript
async updateItemQuantity(index, itemId, quantity) {
  console.log('🚀 updateItemQuantity 开始执行');
  
  try {
    // 登录状态检查
    if (!isLoggedIn()) {
      wx.showToast({ title: '请先登录', icon: 'none' });
      return;
    }

    // 确保定时器对象初始化
    this.ensureTimersInitialized();
    
    // 继续执行业务逻辑...
  } catch (initError) {
    console.error('❌ 初始化检查失败:', initError);
    wx.showToast({ title: '系统错误', icon: 'error' });
    return;
  }
}
```

### 4. 添加登录状态检查
为所有购物车操作方法添加登录状态检查：
- `onQuantityDecrease()`
- `onQuantityIncrease()`
- `onQuantityInput()`
- `onQuantityChange()`
- `updateItemQuantity()`

## 测试步骤

### 1. 未登录状态测试
1. 确保用户未登录
2. 进入购物车页面
3. 尝试点击加减按钮
4. 预期：显示"请先登录"提示，不出现错误

### 2. 登录状态测试
1. 用户登录
2. 添加商品到购物车
3. 在购物车页面修改数量
4. 预期：正常更新，不出现 `_timers` 错误

### 3. 状态切换测试
1. 在登录状态下操作购物车
2. 退出登录
3. 再次尝试操作
4. 预期：显示登录提示，不出现错误

### 4. 异常情况测试
1. 网络断开时操作
2. 快速连续点击
3. 页面切换时操作
4. 预期：有适当的错误处理，不崩溃

## 调试信息

修复后的代码会输出详细的调试信息：
```
🚀 updateItemQuantity 开始执行
✅ 初始化检查完成
🔧 重新初始化 _timers 对象
✅ 定时器对象检查完成
```

## 预期结果

1. ✅ 不再出现 `Cannot read property 'update_5' of undefined` 错误
2. ✅ 未登录状态下有友好的提示
3. ✅ 登录状态下功能正常工作
4. ✅ 有完善的错误处理和调试信息

## 如果问题仍然存在

如果错误仍然出现，请检查：
1. 控制台中的详细调试信息
2. 错误发生的具体操作步骤
3. 用户的登录状态
4. 网络连接状态

并提供完整的错误堆栈和操作步骤以便进一步诊断。
