<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * 优化配送系统的数据关系
     * 1. 明确deliveries表中配送员关联
     * 2. 修复users表中默认配送员关联
     */
    public function up(): void
    {
        // 第一步：修复deliveries表中的配送员关联
        if (Schema::hasTable('deliveries') && Schema::hasColumn('deliveries', 'deliverer_id')) {
            // 添加employee_deliverer_id字段（如果尚未存在）
            if (!Schema::hasColumn('deliveries', 'employee_deliverer_id')) {
                Schema::table('deliveries', function (Blueprint $table) {
                    // 先删除可能存在的外键约束
                    $constraints = DB::select("
                        SELECT CONSTRAINT_NAME 
                        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                        WHERE TABLE_NAME = 'deliveries' 
                        AND COLUMN_NAME = 'deliverer_id'
                        AND CONSTRAINT_NAME != 'PRIMARY'
                        AND REFERENCED_TABLE_NAME IS NOT NULL
                    ");
                    
                    foreach ($constraints as $constraint) {
                        if (isset($constraint->CONSTRAINT_NAME)) {
                            try {
                                // 使用DB::statement直接执行DROP FOREIGN KEY语句
                                DB::statement('ALTER TABLE deliveries DROP FOREIGN KEY ' . $constraint->CONSTRAINT_NAME);
                            } catch (\Exception $e) {
                                // 忽略错误继续执行
                                continue;
                            }
                        }
                    }
                    
                    // 添加employee_deliverer_id字段
                    $table->unsignedBigInteger('employee_deliverer_id')->nullable()->after('status');
                });
                
                // 迁移现有数据 - 将deliverer_id中的数据复制到employee_deliverer_id
                DB::statement('UPDATE deliveries SET employee_deliverer_id = deliverer_id');
            } else {
                // 确保外键约束正确
                $constraints = DB::select("
                    SELECT CONSTRAINT_NAME 
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                    WHERE TABLE_NAME = 'deliveries' 
                    AND COLUMN_NAME IN ('deliverer_id', 'employee_deliverer_id')
                    AND CONSTRAINT_NAME != 'PRIMARY'
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                ");
                
                foreach ($constraints as $constraint) {
                    if (isset($constraint->CONSTRAINT_NAME)) {
                        try {
                            // 使用DB::statement直接执行DROP FOREIGN KEY语句
                            DB::statement('ALTER TABLE deliveries DROP FOREIGN KEY ' . $constraint->CONSTRAINT_NAME);
                        } catch (\Exception $e) {
                            // 忽略错误继续执行
                            continue;
                        }
                    }
                }
            }
            
            // 添加外键约束
            Schema::table('deliveries', function (Blueprint $table) {
                // 为employee_deliverer_id添加外键约束
                $table->foreign('employee_deliverer_id')
                    ->references('id')
                    ->on('employees')
                    ->onDelete('set null');
                
                // 重新为deliverer_id添加外键约束，指向deliverers表
                $table->foreign('deliverer_id')
                    ->references('id')
                    ->on('deliverers')
                    ->onDelete('set null');
            });
            
            // 清空deliverer_id字段，因为现在它应该关联到deliverers表
            DB::statement('UPDATE deliveries SET deliverer_id = NULL');
        }
        
        // 第二步：修复users表中的default_deliverer_id关联
        if (Schema::hasTable('users') && Schema::hasColumn('users', 'default_deliverer_id')) {
            // 先禁用外键检查
            DB::statement('SET FOREIGN_KEY_CHECKS=0');
            
            try {
                // 直接使用原生SQL删除可能存在的外键约束
                $constraints = DB::select("
                    SELECT CONSTRAINT_NAME 
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                    WHERE TABLE_NAME = 'users' 
                    AND COLUMN_NAME = 'default_deliverer_id'
                    AND CONSTRAINT_NAME != 'PRIMARY'
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                ");
                
                // 删除外键约束
                foreach ($constraints as $constraint) {
                    if (isset($constraint->CONSTRAINT_NAME)) {
                        try {
                            DB::statement('ALTER TABLE users DROP FOREIGN KEY ' . $constraint->CONSTRAINT_NAME);
                        } catch (\Exception $e) {
                            // 忽略错误并继续
                            continue;
                        }
                    }
                }
                
                // 添加新的default_employee_deliverer_id字段（如果尚未存在）
                if (!Schema::hasColumn('users', 'default_employee_deliverer_id')) {
                    Schema::table('users', function (Blueprint $table) {
                        $table->unsignedBigInteger('default_employee_deliverer_id')->nullable()->after('default_deliverer_id');
                    });
                    
                    // 迁移现有数据
                    DB::statement('UPDATE users SET default_employee_deliverer_id = default_deliverer_id');
                }
                
                // 清空default_deliverer_id字段，因为它将用于引用deliverers表
                DB::statement('UPDATE users SET default_deliverer_id = NULL');
                
                // 添加新的外键约束
                Schema::table('users', function (Blueprint $table) {
                    // default_employee_deliverer_id指向employees表
                    $table->foreign('default_employee_deliverer_id')
                        ->references('id')
                        ->on('employees')
                        ->onDelete('set null');
                    
                    // default_deliverer_id指向deliverers表
                    $table->foreign('default_deliverer_id')
                        ->references('id')
                        ->on('deliverers')
                        ->onDelete('set null');
                });
            } finally {
                // 重新启用外键检查
                DB::statement('SET FOREIGN_KEY_CHECKS=1');
            }
        }
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        // 禁用外键检查
        DB::statement('SET FOREIGN_KEY_CHECKS=0');
        
        try {
            // 回滚users表的修改
            if (Schema::hasTable('users')) {
                if (Schema::hasColumn('users', 'default_employee_deliverer_id')) {
                    // 先通过原生SQL删除外键约束
                    $constraints = DB::select("
                        SELECT CONSTRAINT_NAME 
                        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                        WHERE TABLE_NAME = 'users' 
                        AND COLUMN_NAME IN ('default_employee_deliverer_id', 'default_deliverer_id')
                        AND CONSTRAINT_NAME != 'PRIMARY'
                        AND REFERENCED_TABLE_NAME IS NOT NULL
                    ");
                    
                    foreach ($constraints as $constraint) {
                        if (isset($constraint->CONSTRAINT_NAME)) {
                            try {
                                DB::statement('ALTER TABLE users DROP FOREIGN KEY ' . $constraint->CONSTRAINT_NAME);
                            } catch (\Exception $e) {
                                // 忽略错误并继续
                                continue;
                            }
                        }
                    }
                    
                    // 将default_employee_deliverer_id的数据移回default_deliverer_id
                    DB::statement('UPDATE users SET default_deliverer_id = default_employee_deliverer_id WHERE default_deliverer_id IS NULL');
                    
                    // 删除default_employee_deliverer_id字段
                    Schema::table('users', function (Blueprint $table) {
                        $table->dropColumn('default_employee_deliverer_id');
                    });
                    
                    // 重新添加default_deliverer_id指向users表的外键约束(原始设计)
                    Schema::table('users', function (Blueprint $table) {
                        $table->foreign('default_deliverer_id')
                            ->references('id')
                            ->on('users')
                            ->onDelete('set null');
                    });
                }
            }
            
            // 回滚deliveries表的修改
            if (Schema::hasTable('deliveries')) {
                if (Schema::hasColumn('deliveries', 'employee_deliverer_id')) {
                    // 先通过原生SQL删除外键约束
                    $constraints = DB::select("
                        SELECT CONSTRAINT_NAME 
                        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                        WHERE TABLE_NAME = 'deliveries' 
                        AND COLUMN_NAME IN ('employee_deliverer_id', 'deliverer_id')
                        AND CONSTRAINT_NAME != 'PRIMARY'
                        AND REFERENCED_TABLE_NAME IS NOT NULL
                    ");
                    
                    foreach ($constraints as $constraint) {
                        if (isset($constraint->CONSTRAINT_NAME)) {
                            try {
                                DB::statement('ALTER TABLE deliveries DROP FOREIGN KEY ' . $constraint->CONSTRAINT_NAME);
                            } catch (\Exception $e) {
                                // 忽略错误并继续
                                continue;
                            }
                        }
                    }
                    
                    // 将employee_deliverer_id的数据移回deliverer_id
                    DB::statement('UPDATE deliveries SET deliverer_id = employee_deliverer_id WHERE deliverer_id IS NULL');
                    
                    // 删除employee_deliverer_id字段
                    Schema::table('deliveries', function (Blueprint $table) {
                        $table->dropColumn('employee_deliverer_id');
                    });
                    
                    // 重新添加deliverer_id指向employees表的外键约束
                    if (Schema::hasColumn('deliveries', 'deliverer_id')) {
                        Schema::table('deliveries', function (Blueprint $table) {
                            $table->foreign('deliverer_id')
                                ->references('id')
                                ->on('employees')
                                ->onDelete('set null');
                        });
                    }
                }
            }
        } finally {
            // 重新启用外键检查
            DB::statement('SET FOREIGN_KEY_CHECKS=1');
        }
    }
};
