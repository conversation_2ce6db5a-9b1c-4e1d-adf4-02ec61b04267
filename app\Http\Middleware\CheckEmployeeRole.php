<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Employee\Http\Middleware\CheckEmployeeRole as EmployeeCheckEmployeeRole;

/**
 * 员工角色检查中间件（代理类）
 * 
 * 该类作为App\Employee\Http\Middleware\CheckEmployeeRole的代理
 * 用于保持向后兼容性
 */
class CheckEmployeeRole
{
    /**
     * 代理的中间件实例
     *
     * @var \App\Employee\Http\Middleware\CheckEmployeeRole
     */
    protected $middleware;

    /**
     * 创建一个新的中间件实例
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware = new EmployeeCheckEmployeeRole();
    }

    /**
     * 处理传入的请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  mixed  ...$roles
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        return $this->middleware->handle($request, $next, ...$roles);
    }
} 