<?php

namespace App\Upload\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Upload\Services\UploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class UploadController extends Controller
{
    /**
     * 上传服务
     *
     * @var UploadService
     */
    protected $uploadService;
    
    /**
     * 构造函数
     *
     * @param UploadService $uploadService
     */
    public function __construct(UploadService $uploadService)
    {
        $this->uploadService = $uploadService;
    }
    
    /**
     * 处理轮播图上传
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadBannerImage(Request $request)
    {
        try {
            // 获取验证规则
            $config = config('upload.validation.banner', [
                'image' => 'required|file|image|max:5120', // 允许最大5MB
            ]);
            
            // 只使用image字段作为验证规则
            $rules = ['image' => $config['image'] ?? 'required|file|image|max:5120'];
            
            // 验证文件
            $validator = Validator::make($request->all(), $rules);
            
            if ($validator->fails()) {
                Log::warning('轮播图验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'client_ip' => $request->ip()
                ]);
                
                return response()->json([
                    'code' => 422,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 422);
            }
            
            if (!$request->hasFile('image')) {
                return response()->json([
                    'code' => 422,
                    'message' => '未上传图片',
                    'data' => null
                ], 422);
            }
            
            // 使用配置创建上传服务实例，确保使用正确的存储驱动（如COS）
            $uploadService = UploadService::createFromConfig();
            $result = $uploadService->uploadBannerImage(
                $request->file('image'),
                $this->getUploadOptions($request)
            );
            
            if (!$result) {
                Log::error('轮播图上传失败');
                return response()->json([
                    'code' => 500,
                    'message' => '上传失败',
                    'data' => null
                ], 500);
            }
            
            // 创建Banner记录
            $banner = new \App\Models\Banner();
            $banner->title = $request->input('title', '轮播图');
            $banner->image_url = $result['url'];
            $banner->link_url = $request->input('link_url');
            $banner->sort_order = $request->input('sort_order', 0);
            $banner->is_active = $request->input('is_active', 1);
            $banner->save();
            
            // 返回上传结果
            return response()->json([
                'code' => 200,
                'message' => '上传成功',
                'data' => [
                    'id' => $banner->id,
                    'url' => $result['url'],
                    'path' => $result['path'],
                    'title' => $banner->title,
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('轮播图上传异常', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '上传异常: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 通用图片上传处理
     */
    public function uploadImage(Request $request)
    {
        return $this->uploadBannerImage($request);
    }
    
    /**
     * 上传商品图片
     */
    public function uploadProductImage(Request $request)
    {
        try {
            // 获取验证规则
            $config = config('upload.validation.product', [
                'image' => 'required|file|image|max:5120', // 允许最大5MB
            ]);
            
            // 只使用image字段作为验证规则
            $rules = ['image' => $config['image'] ?? 'required|file|image|max:5120'];
            
            // 验证文件
            $validator = Validator::make($request->all(), $rules);
            
            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 422);
            }
            
            if (!$request->hasFile('image')) {
                return response()->json([
                    'code' => 400,
                    'message' => '未找到上传文件',
                    'data' => null
                ], 400);
            }
            
            // 使用配置创建上传服务实例，与分类上传保持一致
            $uploadService = UploadService::createFromConfig();
            $result = $uploadService->uploadProductImage(
                $request->file('image'),
                $this->getUploadOptions($request)
            );
            
            if (!$result) {
                return response()->json([
                    'code' => 500,
                    'message' => '商品图片上传失败',
                    'data' => null
                ], 500);
            }
            
            return response()->json([
                'code' => 200,
                'message' => '商品图片上传成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('商品图片上传失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'client_ip' => $request->ip()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '图片上传失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 上传分类图片
     */
    public function uploadCategoryImage(Request $request)
    {
        try {
            // 记录请求信息
            Log::info('收到分类图片上传请求', [
                'has_file' => $request->hasFile('image'),
                'request_data' => $request->all(),
                'files' => $request->allFiles() ? array_keys($request->allFiles()) : [],
                'client_ip' => $request->ip()
            ]);
            
            // 获取验证规则
            $config = config('upload.validation.category', [
                'image' => 'required|file|image|max:2048', // 允许最大2MB
            ]);
            
            // 只使用image字段作为验证规则
            $rules = ['image' => $config['image'] ?? 'required|file|image|max:2048'];
            
            // 验证文件
            $validator = Validator::make($request->all(), $rules);
            
            if ($validator->fails()) {
                Log::warning('分类图片验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'client_ip' => $request->ip()
                ]);
                
                return response()->json([
                    'code' => 422,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 422);
            }
            
            if (!$request->hasFile('image')) {
                Log::warning('未找到上传文件', [
                    'request_data' => $request->all(),
                    'files' => $request->allFiles(),
                    'client_ip' => $request->ip()
                ]);
                
                return response()->json([
                    'code' => 400,
                    'message' => '未找到上传文件',
                    'data' => null
                ], 400);
            }
            
            // 检查是否为图标上传
            $isIcon = $request->has('is_icon') && $request->is_icon === 'true';
            
            // 准备上传选项
            $options = $this->getUploadOptions($request);
            $options['is_icon'] = $isIcon;
            
            // 记录上传信息
            Log::info('处理分类图片上传', [
                'isIcon' => $isIcon,
                'original_filename' => $request->file('image')->getClientOriginalName(),
                'file_size' => $request->file('image')->getSize(),
                'mime_type' => $request->file('image')->getMimeType(),
                'options' => $options
            ]);
            
            // 使用上传服务上传
            $uploadService = UploadService::createFromConfig();
            $result = $uploadService->uploadCategoryImage(
                $request->file('image'),
                $options
            );
            
            if (!$result) {
                Log::error('上传服务返回空结果', [
                    'options' => $options,
                    'client_ip' => $request->ip()
                ]);
                
                return response()->json([
                    'code' => 500,
                    'message' => '分类图片上传失败',
                    'data' => null
                ], 500);
            }
            
            // 日志消息
            $logMessage = $isIcon ? '分类图标上传成功' : '分类图片上传成功';
            Log::info($logMessage, ['result' => $result]);
            
            // 返回成功响应
            return response()->json([
                'code' => 200,
                'message' => $logMessage,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('分类图片上传失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'client_ip' => $request->ip()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '图片上传失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 上传用户头像
     */
    public function uploadAvatarImage(Request $request)
    {
        try {
            // 获取验证规则
            $config = config('upload.validation.avatar', [
                'image' => 'required|file|image|max:2048', // 允许最大2MB
            ]);
            
            // 只使用image字段作为验证规则
            $rules = ['image' => $config['image'] ?? 'required|file|image|max:2048'];
            
            // 验证文件
            $validator = Validator::make($request->all(), $rules);
            
            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 422);
            }
            
            if (!$request->hasFile('image')) {
                return response()->json([
                    'code' => 400,
                    'message' => '未找到上传文件',
                    'data' => null
                ], 400);
            }
            
            // 使用上传服务上传
            $result = $this->uploadService->uploadAvatarImage(
                $request->file('image'),
                $this->getUploadOptions($request)
            );
            
            if (!$result) {
                return response()->json([
                    'code' => 500,
                    'message' => '头像上传失败',
                    'data' => null
                ], 500);
            }
            
            return response()->json([
                'code' => 200,
                'message' => '头像上传成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('头像上传失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'client_ip' => $request->ip()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '头像上传失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取上传选项
     *
     * @param Request $request
     * @return array
     */
    protected function getUploadOptions(Request $request)
    {
        $options = [];
        
        // 提取可能的选项
        if ($request->has('filename')) {
            $options['filename'] = $request->input('filename');
        }
        
        if ($request->has('prefix')) {
            $options['prefix'] = $request->input('prefix');
        }
        
        if ($request->has('subtype')) {
            $options['subtype'] = $request->input('subtype');
        }
        
        // 对象ID，可用于生成文件前缀
        if ($request->has('object_id')) {
            $options['name_prefix'] = $request->input('object_id');
        }
        
        return $options;
    }
} 