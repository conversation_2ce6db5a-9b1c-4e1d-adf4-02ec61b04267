<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_links', function (Blueprint $table) {
            $table->string('id', 64)->primary()->comment('付款链接唯一ID');
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade')->comment('关联订单ID');
            $table->uuid('correction_id')->nullable();
            $table->decimal('amount', 10, 2);
            $table->enum('payment_type', ['cod', 'supplement'])->comment('付款类型：货到付款、补款');
            $table->enum('status', ['active', 'processing', 'paid', 'expired', 'cancelled'])->default('active');
            $table->string('qr_code_url')->nullable()->comment('二维码图片URL');
            $table->string('short_url')->nullable()->comment('短链接');
            $table->timestamp('expires_at')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->string('payment_method', 50)->nullable()->comment('支付方式：wechat, alipay, cash等');
            $table->string('transaction_id', 100)->nullable()->comment('第三方支付交易号');
            $table->decimal('payment_amount', 10, 2)->nullable()->comment('实际支付金额');
            $table->integer('reminder_count')->default(0)->comment('提醒次数');
            $table->timestamp('last_reminder_at')->nullable();
            $table->text('notes')->nullable();
            $table->string('failure_reason')->nullable()->comment('支付失败原因');
            $table->string('idempotency_key', 100)->nullable()->comment('幂等性键');
            $table->timestamps();
            
            $table->index(['order_id', 'payment_type']);
            $table->index('correction_id');
            $table->index('status');
            $table->index('expires_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_links');
    }
}; 