<?php

namespace App\Crm\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MembershipLevel extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'upgrade_points',
        'upgrade_amount',
        'quick_upgrade_amount',
        'discount_rate',
        'is_default',
        'icon',
        'sort_order',
        'status',
        'privileges',
    ];

    /**
     * 需要进行类型转换的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'upgrade_points' => 'integer',
        'upgrade_amount' => 'decimal:2',
        'quick_upgrade_amount' => 'decimal:2',
        'discount_rate' => 'decimal:2',
        'is_default' => 'boolean',
        'sort_order' => 'integer',
        'status' => 'boolean',
        'privileges' => 'json',
    ];

    /**
     * 获取使用此会员等级的用户
     */
    public function users()
    {
        return $this->hasMany(User::class, 'membership_level_id');
    }

    /**
     * 设置为默认会员等级
     */
    public function setAsDefault()
    {
        // 先将所有会员等级的默认状态设置为false
        static::query()->update(['is_default' => false]);
        
        // 将当前会员等级设置为默认
        $this->update(['is_default' => true]);
        
        return $this;
    }
    
    /**
     * 获取默认会员等级
     */
    public static function getDefault()
    {
        return static::where('is_default', true)->first() ?? static::orderBy('upgrade_points')->first();
    }
    
    /**
     * 根据积分获取对应的会员等级
     */
    public static function getLevelByPoints($points)
    {
        return static::where('status', true)
            ->where('upgrade_points', '<=', $points)
            ->orderByDesc('upgrade_points')
            ->first() ?? static::getDefault();
    }
    
    /**
     * 根据累计消费金额获取对应的会员等级
     */
    public static function getLevelByTotalConsumption($amount)
    {
        return static::where('status', true)
            ->where('upgrade_amount', '<=', $amount)
            ->orderByDesc('upgrade_amount')
            ->first() ?? static::getDefault();
    }
    
    /**
     * 根据单笔订单金额获取可直接升级的会员等级
     */
    public static function getLevelBySingleOrder($amount)
    {
        return static::where('status', true)
            ->where('quick_upgrade_amount', '>', 0)  // 必须有单笔订单要求
            ->where('quick_upgrade_amount', '<=', $amount)
            ->orderByDesc('quick_upgrade_amount')
            ->first();
    }
    
    /**
     * 综合判断用户应该属于哪个会员等级
     * 检查积分、累计消费、单笔订单三个条件，取最高等级
     */
    public static function getAppropriateLevel($points, $totalSpend, $largestOrder)
    {
        // 获取符合积分条件的最高等级
        $pointsLevel = static::where('status', true)
            ->where('upgrade_points', '<=', $points)
            ->orderByDesc('upgrade_points')
            ->first();
            
        // 获取符合累计消费条件的最高等级
        $spendLevel = static::where('status', true)
            ->where('upgrade_amount', '<=', $totalSpend)
            ->orderByDesc('upgrade_amount')
            ->first();
            
        // 获取符合单笔订单条件的最高等级
        $orderLevel = static::where('status', true)
            ->where('quick_upgrade_amount', '>', 0)
            ->where('quick_upgrade_amount', '<=', $largestOrder)
            ->orderByDesc('quick_upgrade_amount')
            ->first();
        
        // 取三者中等级最高的（sort_order最大的）
        $levels = collect([$pointsLevel, $spendLevel, $orderLevel])
            ->filter()  // 过滤null值
            ->sortByDesc('sort_order');
            
        return $levels->first() ?? static::getDefault();
    }
} 