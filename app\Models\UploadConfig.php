<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UploadConfig extends Model
{
    use HasFactory;
    
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'upload_configs';
    
    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'key',
        'value',
        'group',
        'type',
        'description',
    ];
    
    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [];
    
    /**
     * 获取配置值（带类型转换）
     *
     * @param mixed $value
     * @return mixed
     */
    public function getValueAttribute($value)
    {
        // 根据类型转换值
        switch ($this->type) {
            case 'boolean':
                return (boolean) $value;
            case 'number':
                return (float) $value;
            case 'integer':
                return (int) $value;
            case 'json':
            case 'array':
                return json_decode($value, true);
            default:
                return $value;
        }
    }
    
    /**
     * 设置配置值（处理数组和对象）
     *
     * @param mixed $value
     * @return void
     */
    public function setValueAttribute($value)
    {
        // 转换数组和对象为JSON字符串
        if (is_array($value) || is_object($value)) {
            $this->attributes['value'] = json_encode($value, JSON_UNESCAPED_UNICODE);
            
            if (!$this->type) {
                $this->attributes['type'] = 'json';
            }
        } else {
            $this->attributes['value'] = $value;
        }
    }
    
    /**
     * 按组获取配置
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $group
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }
    
    /**
     * 获取指定键的配置
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $key
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByKey($query, $key)
    {
        return $query->where('key', $key);
    }
    
    /**
     * 获取指定键的配置值
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getValue($key, $default = null)
    {
        $config = static::byKey($key)->first();
        
        if (!$config) {
            return $default;
        }
        
        return $config->value;
    }
    
    /**
     * 设置配置值
     *
     * @param string $key
     * @param mixed $value
     * @param string $group
     * @param string $type
     * @param string|null $description
     * @return static
     */
    public static function setValue($key, $value, $group = 'basic', $type = null, $description = null)
    {
        // 自动确定类型
        if ($type === null) {
            $type = 'string';
            
            if (is_bool($value)) {
                $type = 'boolean';
            } elseif (is_int($value)) {
                $type = 'integer';
            } elseif (is_float($value)) {
                $type = 'number';
            } elseif (is_array($value) || is_object($value)) {
                $type = 'json';
            }
        }
        
        // 更新或创建配置
        return static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'group' => $group,
                'type' => $type,
                'description' => $description,
            ]
        );
    }
    
    /**
     * 批量设置配置
     *
     * @param array $configs 配置数组，格式为[key => value]
     * @param string $group 配置组
     * @return array 更新或创建的配置集合
     */
    public static function setValues(array $configs, $group = 'basic')
    {
        $result = [];
        
        foreach ($configs as $key => $value) {
            $result[$key] = static::setValue($key, $value, $group);
        }
        
        return $result;
    }
    
    /**
     * 按组获取所有配置值
     *
     * @param string $group 配置组
     * @return array 格式为[key => value]的配置数组
     */
    public static function getGroupValues($group)
    {
        $configs = static::byGroup($group)->get();
        $result = [];
        
        foreach ($configs as $config) {
            $result[$config->key] = $config->value;
        }
        
        return $result;
    }
}
