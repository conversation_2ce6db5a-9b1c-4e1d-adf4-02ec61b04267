<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 如果表存在则删除
        if (Schema::hasTable('unit_conversions')) {
            Schema::dropIfExists('unit_conversions');
        }
    }

    /**
     * Reverse the migrations.
     * 注意：这个down方法不会重新创建表，因为我们已经迁移到了新的图论模型
     */
    public function down(): void
    {
        // 这里不做任何操作，因为我们已经迁移到了新系统
    }
}; 