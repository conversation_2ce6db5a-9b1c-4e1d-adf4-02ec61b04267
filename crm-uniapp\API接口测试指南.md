# CRM UniApp API接口测试指南

## 概述
本文档用于测试CRM UniApp前端与Laravel后端的API接口连接情况。

## 当前接口配置

### 基础配置
- **API基础地址**: `http://localhost:8000/api`（在 `utils/config.js` 中配置）
- **认证方式**: Bearer <PERSON>ken（Sanctum）

### 主要接口列表

#### 1. 员工认证接口
```
POST /api/employee/auth/login     # 员工登录
GET  /api/employee/auth/me        # 获取当前员工信息
POST /api/employee/auth/logout    # 员工登出
```

#### 2. 客户管理接口（有权限边界）
```
GET  /api/users                   # 获取客户列表（根据员工角色过滤）
GET  /api/users/search            # 搜索客户（根据员工角色过滤）
GET  /api/users/{id}              # 获取客户详情
POST /api/users                   # 创建客户
PUT  /api/users/{id}              # 更新客户信息
DELETE /api/users/{id}            # 删除客户
```

#### 3. 订单相关接口
```
GET  /api/users/{id}/orders       # 获取客户订单历史
GET  /api/users/{id}/statistics   # 获取客户统计信息
```

## 权限边界说明

### 重要：不同角色看到的数据不同
- **管理员/经理（admin/manager）**: 可以看到所有客户
- **CRM专员（crm_agent）**: 只能看到分配给自己的客户（`crm_agent_id = 当前员工ID`）
- **配送员（delivery）**: 只能看到配送过的订单相关客户
- **其他角色**: 根据具体权限配置

## 测试步骤

### 第一步：测试基础连接
1. 打开浏览器访问：`http://localhost:8000/api/test`
2. 应该返回：`{"status":"ok","message":"API正常工作"}`

### 第二步：测试员工登录
1. 在UniApp中进入登录页面
2. 输入员工账号密码
3. 查看控制台日志，确认登录请求和响应
4. **重点检查**：响应中的员工角色信息

### 第三步：测试客户列表权限
1. 登录成功后，进入客户管理页面
2. 查看控制台日志中的API响应格式
3. **权限验证**：
   - 如果是CRM专员，应该只看到分配给自己的客户
   - 如果是管理员，应该看到所有客户
   - 记录返回的客户数量和ID

### 第四步：测试不同角色的数据边界
1. 使用不同角色的账号分别登录
2. 对比各角色看到的客户列表差异
3. 验证权限隔离是否正确

## 权限测试用例

### 测试用例1：CRM专员权限边界
**前置条件**：
- 数据库中存在CRM专员账号（role = 'crm_agent'）
- 存在分配给该专员的客户（crm_agent_id = 专员ID）
- 存在未分配或分配给其他专员的客户

**测试步骤**：
1. 使用CRM专员账号登录
2. 访问客户列表API：`GET /api/users`
3. 检查返回结果

**预期结果**：
- 只返回 `crm_agent_id = 当前专员ID` 的客户
- 不返回未分配或分配给其他专员的客户

### 测试用例2：管理员权限验证
**前置条件**：
- 数据库中存在管理员账号（role = 'admin'）

**测试步骤**：
1. 使用管理员账号登录
2. 访问客户列表API：`GET /api/users`
3. 检查返回结果

**预期结果**：
- 返回所有客户，无权限过滤

### 测试用例3：权限不足处理
**测试步骤**：
1. 使用无权限的账号尝试访问客户列表
2. 或者使用过期的Token访问

**预期结果**：
- 返回403（权限不足）或401（未授权）错误
- 前端正确处理错误并显示相应提示

## 常见问题排查

### 1. 404错误 - 接口不存在
**现象**: 请求返回404状态码
**原因**: 
- 路由配置错误
- 接口地址不正确
- Laravel路由缓存问题

**解决方案**:
```bash
# 清除Laravel路由缓存
php artisan route:clear
php artisan route:cache

# 查看所有路由
php artisan route:list
```

### 2. 401错误 - 未授权
**现象**: 请求返回401状态码
**原因**: 
- Token过期或无效
- 认证中间件配置问题

**解决方案**:
- 重新登录获取新Token
- 检查Token是否正确存储和发送

### 3. 403错误 - 权限不足
**现象**: 请求返回403状态码
**原因**: 
- 员工角色权限不足
- 中间件权限检查失败

**解决方案**:
- 确认员工角色是否有相应权限
- 检查后端权限中间件配置

### 4. 500错误 - 服务器内部错误
**现象**: 请求返回500状态码
**原因**: 
- 后端代码错误
- 数据库连接问题
- 依赖关系错误

**解决方案**:
- 查看Laravel日志：`storage/logs/laravel.log`
- 检查数据库连接
- 确认模型关联关系

### 5. 权限边界问题
**现象**: CRM专员看到了不应该看到的客户数据
**原因**: 
- 客户的crm_agent_id字段未正确设置
- 后端权限过滤逻辑有误
- 数据库数据不一致

**解决方案**:
```sql
-- 检查客户分配情况
SELECT id, name, phone, crm_agent_id FROM users WHERE crm_agent_id IS NOT NULL;

-- 检查CRM专员信息
SELECT id, name, role FROM employees WHERE role = 'crm_agent';

-- 验证数据一致性
SELECT u.id, u.name, u.crm_agent_id, e.name as agent_name 
FROM users u 
LEFT JOIN employees e ON u.crm_agent_id = e.id 
WHERE u.crm_agent_id IS NOT NULL;
```

## 后端路由验证

### 查看所有API路由
```bash
cd /path/to/laravel-project
php artisan route:list --path=api
```

### 重点检查的路由
```bash
# 查看用户相关路由
php artisan route:list --path=api/users

# 查看员工认证路由
php artisan route:list --path=api/employee
```

### 验证权限中间件
```bash
# 查看带权限中间件的路由
php artisan route:list | grep "employee.role"
```

## 数据格式说明

### 客户列表API响应格式
根据后端 `UserController::index()` 方法，响应格式为：
```json
{
  "data": [
    {
      "id": 1,
      "name": "客户姓名",
      "phone": "手机号",
      "email": "邮箱",
      "merchant_name": "商户名称",
      "status": "状态",
      "crm_agent_id": 1,
      "created_at": "创建时间",
      "updated_at": "更新时间",
      "membership_level": {
        "id": 1,
        "name": "会员等级名称"
      },
      "crm_agent": {
        "id": 1,
        "name": "CRM专员姓名"
      }
    }
  ],
  "total": 100,
  "current_page": 1,
  "per_page": 20
}
```

### 员工登录API响应格式
```json
{
  "status": "success",
  "message": "登录成功",
  "data": {
    "employee": {
      "id": 1,
      "name": "员工姓名",
      "username": "用户名",
      "role": "角色"
    },
    "token": "认证Token"
  }
}
```

## 数据库验证命令

### 检查员工数据
```sql
-- 查看所有员工及其角色
SELECT id, name, username, role FROM employees;

-- 查看CRM专员
SELECT id, name, username FROM employees WHERE role = 'crm_agent';
```

### 检查客户分配
```sql
-- 查看客户分配情况
SELECT 
    u.id,
    u.name as client_name,
    u.phone,
    u.crm_agent_id,
    e.name as agent_name
FROM users u
LEFT JOIN employees e ON u.crm_agent_id = e.id
ORDER BY u.crm_agent_id;

-- 统计各CRM专员的客户数量
SELECT 
    e.name as agent_name,
    COUNT(u.id) as client_count
FROM employees e
LEFT JOIN users u ON e.id = u.crm_agent_id
WHERE e.role = 'crm_agent'
GROUP BY e.id, e.name;
```

## 调试技巧

### 1. 启用详细日志
在 `utils/request.js` 中添加更多日志：
```javascript
console.log('请求URL:', config.baseURL + config.url)
console.log('请求参数:', config.data)
console.log('请求头:', config.header)
console.log('当前用户角色:', uni.getStorageSync('userInfo')?.role)
```

### 2. 使用Postman测试权限
```bash
# 1. 先登录获取Token
POST http://localhost:8000/api/employee/auth/login
{
  "username": "crm_agent_username",
  "password": "password"
}

# 2. 使用Token访问客户列表
GET http://localhost:8000/api/users
Authorization: Bearer {token}
```

### 3. 检查网络请求
在浏览器开发者工具的Network标签中查看实际的HTTP请求和响应。

### 4. 验证权限逻辑
在后端UserController中添加调试日志：
```php
Log::info('当前员工信息', [
    'employee_id' => $currentEmployee->id,
    'role' => $currentEmployee->role,
    'query_conditions' => $users->toSql()
]);
```

## 联系支持
如果遇到无法解决的问题，请提供：
1. 错误信息截图
2. 控制台日志
3. 网络请求详情
4. Laravel日志相关内容
5. **当前登录员工的角色信息**
6. **预期看到的客户数据范围** 