<?php

namespace App\Crm\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Crm\Models\Feedback;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class FeedbackController extends Controller
{
    /**
     * 获取反馈列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $query = Feedback::with(['user', 'responder']);
        
        // 添加查询条件
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }
        
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }
        
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('subject', 'like', "%{$keyword}%")
                  ->orWhere('content', 'like', "%{$keyword}%");
            });
        }
        
        // 普通用户只能查看自己的反馈
        if ($user->role !== 'admin') {
            $query->where('user_id', $user->id);
        }
        
        // 分页
        $limit = $request->input('limit', 10);
        $feedbacks = $query->orderBy('created_at', 'desc')->paginate($limit);
        
        return response()->json(ApiResponse::success($feedbacks));
    }
    
    /**
     * 创建反馈
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        // 验证输入
        $validator = Validator::make($request->all(), [
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|in:complaint,suggestion,inquiry,other',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        // 创建反馈
        $feedback = Feedback::create([
            'user_id' => Auth::id(),
            'subject' => $request->subject,
            'content' => $request->content,
            'type' => $request->type,
            'status' => 'pending',
        ]);
        
        return response()->json(ApiResponse::success($feedback, '反馈提交成功'), 201);
    }
    
    /**
     * 获取反馈详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $user = Auth::user();
        $feedback = Feedback::with(['user', 'responder'])->findOrFail($id);
        
        // 普通用户只能查看自己的反馈
        if ($user->role !== 'admin' && $feedback->user_id !== $user->id) {
            return response()->json(ApiResponse::error('无权查看此反馈', 403), 403);
        }
        
        return response()->json(ApiResponse::success($feedback));
    }
    
    /**
     * 更新反馈
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $user = Auth::user();
        $feedback = Feedback::findOrFail($id);
        
        // 验证输入
        $validator = Validator::make($request->all(), [
            'status' => 'sometimes|in:pending,processing,resolved,closed',
            'response' => 'sometimes|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        // 只有管理员和CRM管理员可以更新状态和回复
        if ($user->role !== 'admin') {
            return response()->json(ApiResponse::error('无权更新反馈', 403), 403);
        }
        
        // 更新反馈
        $updateData = [];
        
        if ($request->has('status')) {
            $updateData['status'] = $request->status;
            
            // 如果状态改为已解决，记录解决时间
            if ($request->status === 'resolved' && $feedback->status !== 'resolved') {
                $updateData['resolved_at'] = now();
            }
        }
        
        if ($request->has('response')) {
            $updateData['response'] = $request->response;
            $updateData['response_by'] = $user->id;
        }
        
        $feedback->update($updateData);
        
        return response()->json(ApiResponse::success($feedback, '反馈更新成功'));
    }
    
    /**
     * 删除反馈
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $user = Auth::user();
        $feedback = Feedback::findOrFail($id);
        
        // 只有管理员和CRM管理员可以删除反馈
        if ($user->role !== 'admin') {
            return response()->json(ApiResponse::error('无权删除反馈', 403), 403);
        }
        
        $feedback->delete();
        
        return response()->json(ApiResponse::success(null, '反馈删除成功'));
    }
    
    /**
     * 获取反馈统计数据
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics()
    {
        $user = Auth::user();
        $query = Feedback::query();
        
        // 普通用户只能查看自己的统计
        if ($user->role !== 'admin') {
            $query->where('user_id', $user->id);
        }
        
        $statistics = [
            'total' => $query->count(),
            'pending' => (clone $query)->where('status', 'pending')->count(),
            'processing' => (clone $query)->where('status', 'processing')->count(),
            'resolved' => (clone $query)->where('status', 'resolved')->count(),
            'closed' => (clone $query)->where('status', 'closed')->count(),
            'by_type' => [
                'complaint' => (clone $query)->where('type', 'complaint')->count(),
                'suggestion' => (clone $query)->where('type', 'suggestion')->count(),
                'inquiry' => (clone $query)->where('type', 'inquiry')->count(),
                'other' => (clone $query)->where('type', 'other')->count(),
            ],
        ];
        
        return response()->json(ApiResponse::success($statistics));
    }
} 