<?php

namespace App\Points\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PointsOrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'points_order_id',
        'points_product_id',
        'product_name',
        'product_image',
        'points_price',
        'cash_price',
        'quantity',
        'total_points',
        'total_cash',
    ];

    protected $casts = [
        'points_price' => 'integer',
        'cash_price' => 'decimal:2',
        'quantity' => 'integer',
        'total_points' => 'integer',
        'total_cash' => 'decimal:2',
    ];

    /**
     * 关联积分订单
     */
    public function order()
    {
        return $this->belongsTo(PointsOrder::class, 'points_order_id');
    }

    /**
     * 关联积分商品
     */
    public function pointsProduct()
    {
        return $this->belongsTo(PointsProduct::class);
    }

    /**
     * 计算总积分
     */
    public function calculateTotalPoints(): int
    {
        return $this->points_price * $this->quantity;
    }

    /**
     * 计算总现金
     */
    public function calculateTotalCash(): float
    {
        return $this->cash_price * $this->quantity;
    }

    /**
     * 更新总计
     */
    public function updateTotals(): void
    {
        $this->update([
            'total_points' => $this->calculateTotalPoints(),
            'total_cash' => $this->calculateTotalCash(),
        ]);
    }

    /**
     * 获取商品类型（从关联商品获取）
     */
    public function getProductCategoryAttribute(): ?string
    {
        return $this->pointsProduct?->category;
    }

    /**
     * 是否为虚拟商品
     */
    public function isVirtualProduct(): bool
    {
        return $this->getProductCategoryAttribute() === PointsProduct::CATEGORY_VIRTUAL;
    }

    /**
     * 是否为实物商品
     */
    public function isPhysicalProduct(): bool
    {
        return $this->getProductCategoryAttribute() === PointsProduct::CATEGORY_PHYSICAL;
    }

    /**
     * 是否为优惠券
     */
    public function isCouponProduct(): bool
    {
        return $this->getProductCategoryAttribute() === PointsProduct::CATEGORY_COUPON;
    }
} 