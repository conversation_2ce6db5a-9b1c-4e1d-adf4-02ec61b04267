<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AnalyzeCategoryMapping extends Command
{
    protected $signature = 'analyze:category-mapping';
    protected $description = '分析老数据库和新数据库的分类映射关系';

    public function handle()
    {
        $this->info("正在分析分类映射关系...");
        
        try {
            // 查看老数据库分类表结构
            $this->info("=== 老数据库分类表 (zjhj_bd_goods_cats) ===");
            $oldCatColumns = DB::connection('mysql_old')
                ->select("DESCRIBE zjhj_bd_goods_cats");
            
            $this->info("字段结构:");
            foreach ($oldCatColumns as $column) {
                $this->line("  {$column->Field} - {$column->Type}");
            }
            
            // 查看老数据库分类数据
            $this->info("\n=== 老数据库分类数据示例 ===");
            $oldCategories = DB::connection('mysql_old')
                ->table('zjhj_bd_goods_cats')
                ->where('is_delete', 0)
                ->select('id', 'name', 'parent_id', 'sort')
                ->orderBy('parent_id')
                ->orderBy('sort')
                ->limit(10)
                ->get();
            
            $this->info("老数据库分类总数: " . DB::connection('mysql_old')->table('zjhj_bd_goods_cats')->where('is_delete', 0)->count());
            $this->info("前10个分类:");
            foreach ($oldCategories as $cat) {
                $parentInfo = $cat->parent_id ? " (父级ID: {$cat->parent_id})" : " (顶级分类)";
                $this->line("  ID: {$cat->id} - {$cat->name}{$parentInfo}");
            }
            
            // 查看新数据库分类
            $this->info("\n=== 新数据库分类表 (categories) ===");
            $newCategories = DB::table('categories')
                ->select('id', 'name', 'parent_id')
                ->orderBy('parent_id')
                ->orderBy('sort')
                ->get();
            
            $this->info("新数据库分类总数: " . $newCategories->count());
            $this->info("前10个分类:");
            foreach ($newCategories->take(10) as $cat) {
                $parentInfo = $cat->parent_id ? " (父级ID: {$cat->parent_id})" : " (顶级分类)";
                $this->line("  ID: {$cat->id} - {$cat->name}{$parentInfo}");
            }
            
            // 分析分类名称匹配情况
            $this->info("\n=== 分类名称匹配分析 ===");
            $oldCategoryNames = DB::connection('mysql_old')
                ->table('zjhj_bd_goods_cats')
                ->where('is_delete', 0)
                ->pluck('name', 'id');
            
            $newCategoryNames = DB::table('categories')
                ->pluck('name', 'id');
            
            $matched = 0;
            $unmatched = [];
            
            foreach ($oldCategoryNames as $oldId => $oldName) {
                if ($newCategoryNames->contains($oldName)) {
                    $matched++;
                } else {
                    $unmatched[] = $oldName;
                }
            }
            
            $this->info("✅ 可以匹配的分类: {$matched} 个");
            $this->info("❌ 无法匹配的分类: " . count($unmatched) . " 个");
            
            if (count($unmatched) > 0) {
                $this->warn("无法匹配的分类（前5个）:");
                foreach (array_slice($unmatched, 0, 5) as $name) {
                    $this->line("  - {$name}");
                }
            }
            
            // 查看商品与分类的关联关系
            $this->info("\n=== 商品分类关联分析 ===");
            $productCategoryField = $this->findCategoryFieldInProducts();
            
            if ($productCategoryField) {
                $this->info("商品表中的分类字段: {$productCategoryField}");
                
                // 显示商品分类关联示例
                $sampleProducts = DB::connection('mysql_old')
                    ->table('zjhj_bd_goods_warehouse')
                    ->where('is_delete', 0)
                    ->whereNotNull($productCategoryField)
                    ->select('id', 'name', $productCategoryField)
                    ->limit(5)
                    ->get();
                
                $this->info("商品分类关联示例:");
                foreach ($sampleProducts as $product) {
                    $categoryId = $product->$productCategoryField;
                    $categoryName = $oldCategoryNames->get($categoryId, '未知分类');
                    $this->line("  商品: {$product->name} → 分类ID: {$categoryId} ({$categoryName})");
                }
            }
            
        } catch (\Exception $e) {
            $this->error("分析失败: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 查找商品表中的分类字段
     */
    private function findCategoryFieldInProducts()
    {
        $columns = DB::connection('mysql_old')
            ->select("DESCRIBE zjhj_bd_goods_warehouse");
        
        foreach ($columns as $column) {
            $fieldName = strtolower($column->Field);
            if (strpos($fieldName, 'cat') !== false) {
                return $column->Field;
            }
        }
        
        return null;
    }
} 