<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('points_products', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id')->nullable()->comment('关联普通商品ID');
            $table->string('name')->comment('积分商品名称');
            $table->text('description')->nullable()->comment('商品描述');
            $table->string('image')->nullable()->comment('商品图片');
            $table->unsignedInteger('points_price')->default(0)->comment('积分价格');
            $table->decimal('cash_price', 10, 2)->default(0)->comment('现金价格');
            $table->unsignedInteger('stock_quantity')->default(0)->comment('库存数量');
            $table->enum('exchange_type', ['pure_points', 'mixed_payment'])->default('pure_points')->comment('兑换类型');
            $table->enum('category', ['physical', 'virtual', 'coupon'])->default('physical')->comment('商品分类');
            $table->boolean('status')->default(true)->comment('状态：上架/下架');
            $table->unsignedInteger('sort_order')->default(0)->comment('排序');
            $table->unsignedBigInteger('membership_level_id')->nullable()->comment('限制会员等级ID');
            $table->unsignedInteger('daily_limit')->nullable()->comment('每日限兑数量');
            $table->unsignedInteger('total_limit')->nullable()->comment('总限兑数量');
            $table->timestamp('start_time')->nullable()->comment('兑换开始时间');
            $table->timestamp('end_time')->nullable()->comment('兑换结束时间');
            $table->unsignedInteger('exchanged_count')->default(0)->comment('已兑换次数');
            $table->timestamps();

            $table->index(['status', 'start_time', 'end_time']);
            $table->index(['category', 'exchange_type']);
            $table->index('sort_order');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('set null');
            $table->foreign('membership_level_id')->references('id')->on('membership_levels')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('points_products');
    }
}; 