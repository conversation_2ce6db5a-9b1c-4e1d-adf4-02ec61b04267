<?php

namespace App\Unit\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Unit\Models\Unit;
use App\Unit\Models\UnitConversionGraph;
use App\Unit\Models\UnitConversionEdge;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Unit\Services\UnitService;
use Illuminate\Http\JsonResponse;

class UnitConversionController extends Controller
{
    /**
     * 单位服务
     *
     * @var UnitService
     */
    protected $unitService;

    /**
     * 构造函数
     *
     * @param UnitService $unitService
     */
    public function __construct(UnitService $unitService)
    {
        $this->unitService = $unitService;
    }

    /**
     * 获取单位转换列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $unitType = $request->get('unit_type');
        
        $query = UnitConversionEdge::with(['fromUnit', 'toUnit', 'graph']);
        
        if ($unitType) {
            $query->whereHas('fromUnit', function($q) use ($unitType) {
                $q->where('type', $unitType);
            });
        }
        
        $conversions = $query->paginate($perPage);
        
        return response()->json(ApiResponse::success($conversions));
    }

    /**
     * 创建单位转换
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'from_unit_id' => 'required|exists:units,id',
            'to_unit_id' => 'required|exists:units,id|different:from_unit_id',
            'conversion_factor' => 'required|numeric|gt:0',
            'is_bidirectional' => 'sometimes|boolean',
            'graph_id' => 'sometimes|exists:unit_conversion_graphs,id',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        // 获取单位
        $fromUnit = Unit::findOrFail($request->from_unit_id);
        $toUnit = Unit::findOrFail($request->to_unit_id);
        
        // 检查单位类型是否匹配
        if ($fromUnit->type !== $toUnit->type) {
            return response()->json(
                ApiResponse::error('无法在不同类型的单位之间创建转换关系', 422), 
                422
            );
        }
        
        // 获取或创建图
        $graphId = $request->graph_id;
        
        if (!$graphId) {
            // 获取默认图
            $graph = UnitConversionGraph::where('type', $fromUnit->type)
                                      ->where('is_default', true)
                                      ->first();
                                      
            if (!$graph) {
                // 创建新图
                $graph = UnitConversionGraph::create([
                    'name' => $fromUnit->type . ' 默认转换图',
                    'type' => $fromUnit->type,
                    'is_default' => true
                ]);
            }
            
            $graphId = $graph->id;
        }
        
        // 检查是否已存在相同的转换关系
        $existing = UnitConversionEdge::where('graph_id', $graphId)
                                     ->where('from_unit_id', $fromUnit->id)
                                     ->where('to_unit_id', $toUnit->id)
                                     ->first();
                                     
        if ($existing) {
            return response()->json(
                ApiResponse::error('已存在相同的转换关系', 422), 
                422
            );
        }
        
        // 创建转换关系
        $isBidirectional = $request->input('is_bidirectional', false);
        
        if ($isBidirectional) {
            $edges = UnitConversionEdge::createBidirectional(
                $graphId,
                $fromUnit->id,
                $toUnit->id,
                $request->conversion_factor
            );
            
            return response()->json(ApiResponse::success([
                'forward' => $edges[0],
                'reverse' => $edges[1]
            ], '双向转换关系创建成功'));
        } else {
            $edge = UnitConversionEdge::create([
                'graph_id' => $graphId,
                'from_unit_id' => $fromUnit->id,
                'to_unit_id' => $toUnit->id,
                'conversion_factor' => $request->conversion_factor,
                'is_bidirectional' => false
            ]);
            
            return response()->json(ApiResponse::success($edge, '转换关系创建成功'));
        }
    }

    /**
     * 获取单位转换详情
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        $conversion = UnitConversionEdge::with(['fromUnit', 'toUnit', 'graph'])
                                      ->findOrFail($id);
        
        return response()->json(ApiResponse::success($conversion));
    }

    /**
     * 更新单位转换
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'conversion_factor' => 'required|numeric|gt:0'
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $conversion = UnitConversionEdge::findOrFail($id);
        
        // 如果是双向转换，也更新反向关系
        if ($conversion->is_bidirectional) {
            $reverseConversion = UnitConversionEdge::where('graph_id', $conversion->graph_id)
                                                  ->where('from_unit_id', $conversion->to_unit_id)
                                                  ->where('to_unit_id', $conversion->from_unit_id)
                                                  ->first();
                                                  
            if ($reverseConversion) {
                $reverseConversion->update([
                    'conversion_factor' => 1 / $request->conversion_factor
                ]);
            }
        }
        
        $conversion->update([
            'conversion_factor' => $request->conversion_factor
        ]);
        
        return response()->json(ApiResponse::success($conversion, '转换关系更新成功'));
    }

    /**
     * 删除单位转换
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        $conversion = UnitConversionEdge::findOrFail($id);
        
        // 如果是双向转换，也删除反向关系
        if ($conversion->is_bidirectional) {
            UnitConversionEdge::where('graph_id', $conversion->graph_id)
                            ->where('from_unit_id', $conversion->to_unit_id)
                            ->where('to_unit_id', $conversion->from_unit_id)
                            ->delete();
        }
        
        $conversion->delete();
        
        return response()->json(ApiResponse::success(null, '转换关系删除成功'));
    }

    /**
     * 计算单位换算
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function calculate(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'value' => 'required|numeric',
                'from_unit_id' => 'required|exists:units,id',
                'to_unit_id' => 'required|exists:units,id',
            ]);
    
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
    
            $value = $request->value;
            $fromUnitId = $request->from_unit_id;
            $toUnitId = $request->to_unit_id;
            
            $fromUnit = Unit::findOrFail($fromUnitId);
            $toUnit = Unit::findOrFail($toUnitId);
            
            $result = $this->unitService->convertValue($value, $fromUnit, $toUnit);
            
            return response()->json(ApiResponse::success([
                'original_value' => $value,
                'converted_value' => $result,
                'from_unit' => [
                    'id' => $fromUnit->id,
                    'name' => $fromUnit->name,
                    'display_name' => $fromUnit->display_name,
                    'symbol' => $fromUnit->symbol
                ],
                'to_unit' => [
                    'id' => $toUnit->id,
                    'name' => $toUnit->name,
                    'display_name' => $toUnit->display_name,
                    'symbol' => $toUnit->symbol
                ]
            ], '单位转换计算成功'));
        } catch (\Exception $e) {
            Log::error('单位转换计算错误', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error($e->getMessage(), 400), 400);
        }
    }
} 