<template>
	<view class="customer-segment-container">
		<!-- 页面头部 -->
		<view class="header-section">
			<view class="header-title">
				<text class="title-text">客户细分</text>
				<text class="subtitle-text">基于RFM模型的客户分层分析</text>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-section" v-if="loading">
			<text class="loading-text">正在加载数据...</text>
		</view>

		<!-- 数据内容 -->
		<view v-else>
			<!-- RFM模型概览 -->
			<view class="rfm-overview-section">
				<view class="section-title">
					<text class="title-text">📊 RFM模型概览</text>
					<text class="subtitle">基于最近购买时间、购买频次、购买金额的客户分层</text>
				</view>
				<view class="rfm-metrics">
					<view class="metric-item">
						<view class="metric-icon recency">R</view>
						<view class="metric-info">
							<text class="metric-name">最近购买时间</text>
							<text class="metric-desc">Recency</text>
						</view>
					</view>
					<view class="metric-item">
						<view class="metric-icon frequency">F</view>
						<view class="metric-info">
							<text class="metric-name">购买频次</text>
							<text class="metric-desc">Frequency</text>
						</view>
					</view>
					<view class="metric-item">
						<view class="metric-icon monetary">M</view>
						<view class="metric-info">
							<text class="metric-name">购买金额</text>
							<text class="metric-desc">Monetary</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 客户分层结果 -->
			<view class="segments-section">
				<view class="section-title">
					<text class="title-text">👥 客户分层结果</text>
				</view>
				<view class="segments-grid" v-if="customerSegments.length > 0">
					<view class="segment-card" v-for="(segment, index) in customerSegments" :key="segment.uniqueKey || index" @tap="viewSegmentDetail(segment)">
						<view class="segment-header">
							<view class="segment-icon" :class="segment.level || 'default'">
								<text class="icon-text">{{ segment.icon || '👤' }}</text>
							</view>
							<view class="segment-badge" :class="segment.level || 'default'">
								<text class="badge-text">{{ segment.rfmScore || segment.rfm_score || 'N/A' }}</text>
							</view>
						</view>
						<view class="segment-content">
							<text class="segment-name">{{ segment.name || segment.segment_name || '未知分层' }}</text>
							<text class="segment-desc">{{ segment.description || '暂无描述' }}</text>
							<view class="segment-stats">
								<view class="stat-row">
									<text class="stat-label">客户数量</text>
									<text class="stat-value">{{ segment.customerCount || segment.customer_count || 0 }}人</text>
								</view>
								<view class="stat-row">
									<text class="stat-label">贡献收入</text>
									<text class="stat-value">¥{{ segment.totalRevenue || segment.total_revenue || 0 }}</text>
								</view>
								<view class="stat-row">
									<text class="stat-label">占比</text>
									<text class="stat-value">{{ segment.percentage || 0 }}%</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="empty-state" v-else>
					<text class="empty-text">暂无客户分层数据</text>
				</view>
			</view>

			<!-- 价值分布图 -->
			<view class="value-distribution-section">
				<view class="section-title">
					<text class="title-text">💰 客户价值分布</text>
				</view>
				<view class="distribution-chart">
					<view class="chart-header">
						<text class="chart-title">客户价值象限图</text>
						<text class="chart-subtitle">基于购买频次和购买金额</text>
					</view>
					<view class="quadrant-chart">
						<view class="quadrant high-value-high-freq" @tap="showQuadrantDetail('champions')">
							<text class="quadrant-label">冠军客户</text>
							<text class="quadrant-count">{{ getQuadrantCount('champions') }}人</text>
						</view>
						<view class="quadrant high-value-low-freq" @tap="showQuadrantDetail('potential')">
							<text class="quadrant-label">潜力客户</text>
							<text class="quadrant-count">{{ getQuadrantCount('potential') }}人</text>
						</view>
						<view class="quadrant low-value-high-freq" @tap="showQuadrantDetail('loyal')">
							<text class="quadrant-label">忠诚客户</text>
							<text class="quadrant-count">{{ getQuadrantCount('loyal') }}人</text>
						</view>
						<view class="quadrant low-value-low-freq" @tap="showQuadrantDetail('at-risk')">
							<text class="quadrant-label">流失风险</text>
							<text class="quadrant-count">{{ getQuadrantCount('at-risk') }}人</text>
						</view>
					</view>
					<view class="chart-axes">
						<text class="axis-label x-axis">购买频次 →</text>
						<text class="axis-label y-axis">购买金额 ↑</text>
					</view>
				</view>
			</view>

			<!-- 营销策略建议 -->
			<view class="strategy-section">
				<view class="section-title">
					<text class="title-text">🎯 营销策略建议</text>
				</view>
				<view class="strategy-list" v-if="marketingStrategies.length > 0">
					<view class="strategy-item" v-for="(strategy, index) in marketingStrategies" :key="strategy.uniqueKey || index">
						<view class="strategy-header">
							<view class="strategy-icon" :class="strategy.type || 'default'">
								<text class="icon-text">{{ strategy.icon || '🎯' }}</text>
							</view>
							<view class="strategy-info">
								<text class="strategy-title">{{ strategy.title || '营销策略' }}</text>
								<text class="strategy-target">目标：{{ strategy.target || '未指定' }}</text>
							</view>
						</view>
						<view class="strategy-content">
							<text class="strategy-desc">{{ strategy.description || '暂无描述' }}</text>
							<view class="strategy-actions" v-if="strategy.actions && strategy.actions.length > 0">
								<text class="action-item" v-for="(action, actionIndex) in strategy.actions" :key="actionIndex">
									• {{ action }}
								</text>
							</view>
						</view>
					</view>
				</view>
				<view class="empty-state" v-else>
					<text class="empty-text">暂无营销策略建议</text>
				</view>
			</view>

			<!-- 客户生命周期 -->
			<view class="lifecycle-section">
				<view class="section-title">
					<text class="title-text">🔄 客户生命周期分析</text>
				</view>
				<view class="lifecycle-flow" v-if="lifecycleStages.length > 0">
					<view class="lifecycle-stage" v-for="(stage, index) in lifecycleStages" :key="stage.uniqueKey || index">
						<view class="stage-icon" :class="stage.status || 'default'">
							<text class="icon-text">{{ stage.icon || '📊' }}</text>
						</view>
						<view class="stage-info">
							<text class="stage-name">{{ stage.name || stage.stage_name || '未知阶段' }}</text>
							<text class="stage-count">{{ stage.count || stage.customer_count || 0 }}人</text>
							<text class="stage-percentage">{{ stage.percentage || 0 }}%</text>
						</view>
						<view class="stage-arrow" v-if="index < lifecycleStages.length - 1">→</view>
					</view>
				</view>
				<view class="lifecycle-metrics" v-if="lifecycleMetrics && Object.keys(lifecycleMetrics).length > 0">
					<view class="metric-card">
						<text class="metric-title">平均生命周期</text>
						<text class="metric-value">{{ lifecycleMetrics.avgLifetime || lifecycleMetrics.avg_lifetime || 0 }}天</text>
					</view>
					<view class="metric-card">
						<text class="metric-title">客户留存率</text>
						<text class="metric-value">{{ lifecycleMetrics.retentionRate || lifecycleMetrics.retention_rate || 0 }}%</text>
					</view>
					<view class="metric-card">
						<text class="metric-title">流失率</text>
						<text class="metric-value">{{ lifecycleMetrics.churnRate || lifecycleMetrics.churn_rate || 0 }}%</text>
					</view>
				</view>
				<view class="empty-state" v-else>
					<text class="empty-text">暂无生命周期数据</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import analyticsApi from '../../api/analytics.js'

export default {
	data() {
		return {
			loading: false,
			customerSegments: [],
			marketingStrategies: [],
			lifecycleStages: [],
			lifecycleMetrics: {},
			quadrantData: {}
		}
	},
	
	onLoad() {
		this.loadCustomerSegmentAnalysis()
	},
	
	onPullDownRefresh() {
		this.refreshData()
	},
	
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 刷新数据
		async refreshData() {
			await this.loadCustomerSegmentAnalysis()
			uni.stopPullDownRefresh()
			uni.showToast({
				title: '刷新成功',
				icon: 'success',
				duration: 1500
			})
		},
		
		// 加载客户细分分析数据
		async loadCustomerSegmentAnalysis() {
			this.loading = true
			try {
				console.log('开始加载客户细分分析数据...')
				
				const response = await analyticsApi.getCustomerSegmentAnalysis()
				console.log('客户细分分析响应:', response)
				
				if (response && response.data) {
					// 处理客户分层数据
					this.customerSegments = (response.data.segments || response.data.customer_segments || []).map((item, index) => ({
						...item,
						uniqueKey: `segment-${index}-${Date.now()}`
					}))
					
					// 处理营销策略数据
					this.marketingStrategies = (response.data.strategies || response.data.marketing_strategies || []).map((item, index) => ({
						...item,
						uniqueKey: `strategy-${index}-${Date.now()}`
					}))
					
					// 处理生命周期数据
					this.lifecycleStages = (response.data.lifecycle_stages || response.data.lifecycleStages || []).map((item, index) => ({
						...item,
						uniqueKey: `lifecycle-${index}-${Date.now()}`
					}))
					
					// 处理生命周期指标
					this.lifecycleMetrics = response.data.lifecycle_metrics || response.data.lifecycleMetrics || {}
					
					// 处理象限数据
					this.quadrantData = response.data.quadrant_data || response.data.quadrantData || {}
					
					console.log('处理后的数据:', {
						customerSegments: this.customerSegments,
						marketingStrategies: this.marketingStrategies,
						lifecycleStages: this.lifecycleStages,
						lifecycleMetrics: this.lifecycleMetrics,
						quadrantData: this.quadrantData
					})
				}
				
			} catch (error) {
				console.error('加载客户细分分析数据失败:', error)
				
				// 显示具体错误信息
				let errorMessage = '加载客户细分分析数据失败'
				if (error.response) {
					if (error.response.status === 401) {
						errorMessage = '登录已过期，请重新登录'
						// 可以跳转到登录页
						setTimeout(() => {
							uni.reLaunch({ url: '/pages/login/login' })
						}, 2000)
					} else if (error.response.status === 403) {
						errorMessage = '没有权限访问该数据'
					} else {
						errorMessage = `请求失败: ${error.response.status}`
					}
				} else if (error.message) {
					errorMessage = error.message
				}
				
				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
				
				// 清空数据
				this.customerSegments = []
				this.marketingStrategies = []
				this.lifecycleStages = []
				this.lifecycleMetrics = {}
				this.quadrantData = {}
			} finally {
				this.loading = false
			}
		},
		
		// 查看分层详情
		viewSegmentDetail(segment) {
			console.log('查看分层详情:', segment)
			uni.showModal({
				title: segment.name || '客户分层详情',
				content: `${segment.description || '暂无描述'}\n\n客户数量: ${segment.customerCount || segment.customer_count || 0}人\n贡献收入: ¥${segment.totalRevenue || segment.total_revenue || 0}`,
				showCancel: false
			})
		},
		
		// 获取象限客户数量
		getQuadrantCount(quadrant) {
			if (!this.quadrantData || !this.quadrantData[quadrant]) {
				return 0
			}
			return this.quadrantData[quadrant].count || this.quadrantData[quadrant].customer_count || 0
		},
		
		// 显示象限详情
		showQuadrantDetail(quadrant) {
			const quadrantNames = {
				champions: '冠军客户',
				potential: '潜力客户',
				loyal: '忠诚客户',
				'at-risk': '流失风险客户'
			}
			
			const quadrantName = quadrantNames[quadrant] || quadrant
			const count = this.getQuadrantCount(quadrant)
			
			uni.showModal({
				title: quadrantName,
				content: `该象限共有 ${count} 位客户`,
				showCancel: false
			})
		}
	}
}
</script>

<style scoped>
.customer-segment-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 页面头部 */
.header-section {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx 32rpx 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	color: #ffffff;
}

.header-title {
	flex: 1;
}

.title-text {
	font-size: 40rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
}

.subtitle-text {
	font-size: 28rpx;
	opacity: 0.8;
}

/* 通用样式 */
.section-title {
	padding: 32rpx;
	background: #ffffff;
	border-bottom: 2rpx solid #f0f0f0;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.subtitle {
	font-size: 24rpx;
	color: #666666;
}

/* RFM概览 */
.rfm-overview-section {
	background: #ffffff;
	margin-bottom: 20rpx;
}

.rfm-metrics {
	padding: 32rpx;
	display: flex;
	justify-content: space-around;
}

.metric-item {
	text-align: center;
}

.metric-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 16rpx;
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

.metric-icon.recency {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.metric-icon.frequency {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-icon.monetary {
	background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.metric-name {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
}

.metric-desc {
	font-size: 24rpx;
	color: #666666;
}

/* 客户分层 */
.segments-section {
	background: #ffffff;
	margin-bottom: 20rpx;
}

.segments-grid {
	padding: 32rpx;
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
}

.segment-card {
	background: #f8f9fa;
	border-radius: 16rpx;
	padding: 24rpx;
	transition: all 0.3s ease;
}

.segment-card:active {
	transform: scale(0.95);
	background: #e9ecef;
}

.segment-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.segment-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.segment-icon.champion {
	background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.segment-icon.loyal {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.segment-icon.potential {
	background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.segment-icon.new {
	background: linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 100%);
}

.segment-icon.at-risk {
	background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
}

.segment-icon.lost {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.icon-text {
	font-size: 28rpx;
}

.segment-badge {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 600;
	color: #ffffff;
}

.segment-badge.champion {
	background: #FFD700;
	color: #333333;
}

.segment-badge.loyal {
	background: #4facfe;
}

.segment-badge.potential {
	background: #43e97b;
}

.segment-badge.new {
	background: #a8e6cf;
	color: #333333;
}

.segment-badge.at-risk {
	background: #ffa726;
}

.segment-badge.lost {
	background: #ff6b6b;
}

.badge-text {
	font-size: 20rpx;
}

.segment-name {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.segment-desc {
	display: block;
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 16rpx;
}

.stat-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666666;
}

.stat-value {
	font-size: 24rpx;
	font-weight: 600;
	color: #007AFF;
}

/* 价值分布 */
.value-distribution-section {
	background: #ffffff;
	margin-bottom: 20rpx;
}

.distribution-chart {
	padding: 32rpx;
}

.chart-header {
	text-align: center;
	margin-bottom: 32rpx;
}

.chart-title {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.chart-subtitle {
	font-size: 24rpx;
	color: #666666;
}

.quadrant-chart {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 2rpx;
	height: 400rpx;
	border-radius: 12rpx;
	overflow: hidden;
}

.quadrant {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 24rpx;
	transition: all 0.3s ease;
}

.quadrant:active {
	transform: scale(0.95);
}

.high-value-high-freq {
	background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.high-value-low-freq {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.low-value-high-freq {
	background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
}

.low-value-low-freq {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.quadrant-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 8rpx;
}

.quadrant-count {
	font-size: 24rpx;
	color: #ffffff;
}

.chart-axes {
	position: relative;
	margin-top: 16rpx;
}

.axis-label {
	position: absolute;
	font-size: 24rpx;
	color: #666666;
}

.x-axis {
	bottom: 0;
	right: 0;
}

.y-axis {
	top: 0;
	left: 0;
	transform: rotate(90deg);
	transform-origin: left bottom;
}

/* 营销策略 */
.strategy-section {
	background: #ffffff;
	margin-bottom: 20rpx;
}

.strategy-list {
	padding: 32rpx;
}

.strategy-item {
	background: #f8f9fa;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
}

.strategy-header {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.strategy-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.strategy-icon.maintain {
	background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.strategy-icon.activate {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.strategy-icon.recover {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.strategy-info {
	flex: 1;
}

.strategy-title {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
}

.strategy-target {
	font-size: 24rpx;
	color: #666666;
}

.strategy-desc {
	display: block;
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 16rpx;
}

.strategy-actions {
	
}

.action-item {
	display: block;
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 8rpx;
	line-height: 1.5;
}

/* 生命周期 */
.lifecycle-section {
	background: #ffffff;
	margin-bottom: 20rpx;
}

.lifecycle-flow {
	padding: 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.lifecycle-stage {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.stage-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 16rpx;
	font-size: 36rpx;
}

.stage-icon.new {
	background: linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 100%);
}

.stage-icon.active {
	background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stage-icon.dormant {
	background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
}

.stage-icon.churned {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stage-name {
	display: block;
	font-size: 24rpx;
	color: #333333;
	margin-bottom: 4rpx;
}

.stage-count {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #007AFF;
	margin-bottom: 4rpx;
}

.stage-percentage {
	font-size: 20rpx;
	color: #666666;
}

.stage-arrow {
	font-size: 32rpx;
	color: #007AFF;
	margin: 0 16rpx;
}

.lifecycle-metrics {
	display: flex;
	padding: 0 32rpx 32rpx;
}

.metric-card {
	flex: 1;
	text-align: center;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-right: 16rpx;
}

.metric-card:last-child {
	margin-right: 0;
}

.metric-title {
	display: block;
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 8rpx;
}

.metric-value {
	font-size: 36rpx;
	font-weight: 600;
	color: #007AFF;
}

/* 空状态 */
.empty-state {
	padding: 32rpx;
	text-align: center;
}

.empty-text {
	font-size: 24rpx;
	color: #666666;
}

/* 加载状态 */
.loading-section {
	padding: 32rpx;
	text-align: center;
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}
</style> 