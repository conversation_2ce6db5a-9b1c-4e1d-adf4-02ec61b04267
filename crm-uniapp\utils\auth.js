import config from './config.js'

class AuthManager {
	constructor() {
		this.isCheckingAuth = false
	}
	
	/**
	 * 获取token
	 */
	getToken() {
		return uni.getStorageSync(config.storageKeys.token) || ''
	}
	
	/**
	 * 获取用户信息
	 */
	getUserInfo() {
		return uni.getStorageSync(config.storageKeys.employeeInfo) || null
	}
	
	/**
	 * 检查是否已登录
	 */
	isLoggedIn() {
		const token = this.getToken()
		return !!token
	}
	
	/**
	 * 检查token是否过期
	 */
	isTokenExpired() {
		try {
			const employeeInfo = this.getUserInfo()
			if (!employeeInfo || !employeeInfo.token_expires_at) {
				return false // 如果没有过期时间信息，假设未过期
			}
			
			const expiresAt = new Date(employeeInfo.token_expires_at).getTime()
			const now = new Date().getTime()
			
			return now >= expiresAt
		} catch (error) {
			console.error('检查token过期时间失败:', error)
			return false
		}
	}
	
	/**
	 * 检查认证状态
	 * @param {boolean} showToast 是否显示提示信息
	 * @returns {boolean} 是否已认证
	 */
	checkAuth(showToast = false) {
		// 检查token是否存在
		if (!this.isLoggedIn()) {
			if (showToast) {
				uni.showToast({
					title: '请先登录',
					icon: 'none',
					duration: 2000
				})
			}
			return false
		}
		
		// 检查token是否过期
		if (this.isTokenExpired()) {
			if (showToast) {
				uni.showToast({
					title: '登录已过期，请重新登录',
					icon: 'none',
					duration: 2000
				})
			}
			this.logout()
			return false
		}
		
		return true
	}
	
	/**
	 * 页面认证检查（用于页面onLoad）
	 * @param {string} pageName 页面名称，用于日志
	 * @returns {boolean} 是否通过认证
	 */
	async checkPageAuth(pageName = '当前页面') {
		if (this.isCheckingAuth) {
			return false
		}
		
		this.isCheckingAuth = true
		
		try {
			console.log(`${pageName} - 开始认证检查`)
			
			if (!this.checkAuth()) {
				console.log(`${pageName} - 认证失败，跳转登录页`)
				this.redirectToLogin()
				return false
			}
			
			console.log(`${pageName} - 认证通过`)
			return true
		} finally {
			this.isCheckingAuth = false
		}
	}
	
	/**
	 * 跳转到登录页
	 */
	redirectToLogin() {
		// 检查当前是否已经在登录页
		const pages = getCurrentPages()
		const currentPage = pages[pages.length - 1]
		const currentRoute = currentPage ? currentPage.route : ''
		
		if (currentRoute === 'pages/login/login') {
			console.log('当前已在登录页，无需跳转')
			return
		}
		
		uni.reLaunch({
			url: '/pages/login/login'
		})
	}
	
	/**
	 * 登出
	 */
	logout() {
		// 清除存储的认证信息
		uni.removeStorageSync(config.storageKeys.token)
		uni.removeStorageSync(config.storageKeys.userInfo)
		uni.removeStorageSync(config.storageKeys.employeeInfo)
		
		console.log('用户已登出')
	}
	
	/**
	 * 保存登录信息
	 * @param {string} token 访问令牌
	 * @param {object} employeeInfo 员工信息
	 */
	saveLoginInfo(token, employeeInfo) {
		uni.setStorageSync(config.storageKeys.token, token)
		uni.setStorageSync(config.storageKeys.employeeInfo, employeeInfo)
		
		console.log('登录信息已保存')
	}
	
	/**
	 * 获取用户角色
	 */
	getUserRole() {
		const userInfo = this.getUserInfo()
		return userInfo ? userInfo.role : null
	}
	
	/**
	 * 检查用户权限
	 * @param {string|array} roles 允许的角色
	 * @returns {boolean} 是否有权限
	 */
	hasRole(roles) {
		const userRole = this.getUserRole()
		if (!userRole) return false
		
		if (typeof roles === 'string') {
			return userRole === roles
		}
		
		if (Array.isArray(roles)) {
			return roles.includes(userRole)
		}
		
		return false
	}
	
	/**
	 * 检查是否为管理员
	 */
	isAdmin() {
		return this.hasRole(['admin', 'manager'])
	}
}

// 创建全局实例
const authManager = new AuthManager()

export default authManager 