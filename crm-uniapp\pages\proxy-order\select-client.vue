<template>
	<view class="select-client-container">
		<!-- 固定搜索栏 -->
		<view class="search-header">
			<!-- 搜索框 -->
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input 
					class="search-input" 
					type="text" 
					placeholder="搜索商户名称、联系人或手机号" 
					v-model="searchKeyword"
					@input="onSearchInput"
					@confirm="onSearchConfirm"
				/>
				<button class="clear-btn" @tap="clearSearch" v-if="searchKeyword">
					<text class="clear-icon">×</text>
				</button>
			</view>
			
			<!-- 快速筛选 -->
			<view class="filter-section">
				<scroll-view class="filter-scroll" scroll-x="true">
					<view class="filter-item" :class="{ active: filterType === 'all' }" @tap="setFilter('all')">
						<text class="filter-text">全部</text>
					</view>
					<view class="filter-item" :class="{ active: filterType === 'vip' }" @tap="setFilter('vip')">
						<text class="filter-text">VIP客户</text>
					</view>
					<view class="filter-item" :class="{ active: filterType === 'new' }" @tap="setFilter('new')">
						<text class="filter-text">新客户</text>
					</view>
					<view class="filter-item" :class="{ active: filterType === 'active' }" @tap="setFilter('active')">
						<text class="filter-text">活跃客户</text>
					</view>
				</scroll-view>
			</view>
		</view>
		
		<!-- 客户列表 -->
		<scroll-view 
			class="client-list" 
			scroll-y="true" 
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="onRefresh"
			:refresher-triggered="refreshing"
		>
			<view 
				class="client-card" 
				v-for="client in clientList" 
				:key="client.id"
				@tap="selectClient(client)"
			>
				<!-- 客户头像和基本信息 -->
				<view class="client-header">
					<view class="client-avatar">
						<text class="avatar-text">{{ getAvatarText(client) }}</text>
						<view class="avatar-badge" v-if="client.membership_level">VIP</view>
					</view>
					<view class="client-main-info">
						<view class="client-name-row">
							<text class="client-name">{{ client.merchant_name || client.name || '未知商户' }}</text>
							<view class="client-tags">
								<text class="tag new-tag" v-if="isNewClient(client)">新</text>
								<text class="tag vip-tag" v-if="client.membership_level">VIP</text>
							</view>
						</view>
						<text class="client-contact" v-if="client.merchant_name && client.name">👤 {{ client.name }}</text>
						<text class="client-phone">📱 {{ client.phone }}</text>
					</view>
					<view class="select-action">
						<text class="select-text">选择</text>
						<text class="arrow-icon">›</text>
					</view>
				</view>
				
				<!-- 客户统计信息 -->
				<view class="client-stats" v-if="client.order_count > 0 || client.total_amount > 0">
					<view class="stat-item">
						<text class="stat-label">订单数</text>
						<text class="stat-value">{{ client.order_count || 0 }}笔</text>
					</view>
					<view class="stat-item">
						<text class="stat-label">消费额</text>
						<text class="stat-value">¥{{ formatAmount(client.total_amount) }}</text>
					</view>
					<view class="stat-item" v-if="client.last_order_date">
						<text class="stat-label">最近下单</text>
						<text class="stat-value">{{ formatDate(client.last_order_date) }}</text>
					</view>
				</view>
			</view>
			
			<!-- 加载更多指示器 -->
			<view class="load-more-section" v-if="hasMore && clientList.length > 0">
				<view class="load-more-indicator" v-if="listLoading">
					<view class="loading-spinner"></view>
					<text class="loading-text">加载中...</text>
				</view>
				<button class="load-more-btn" @tap="loadMore" v-else>
					<text class="load-more-text">加载更多</text>
				</button>
			</view>
			
			<!-- 没有更多数据 -->
			<view class="no-more-section" v-if="!hasMore && clientList.length > 0">
				<text class="no-more-text">已显示全部客户</text>
			</view>
		</scroll-view>
		
		<!-- 初始加载状态 -->
		<view class="loading-section" v-if="listLoading && clientList.length === 0">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">正在加载客户列表...</text>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-section" v-if="!listLoading && clientList.length === 0">
			<view class="empty-content">
				<text class="empty-icon">{{ searchKeyword ? '🔍' : '👥' }}</text>
				<text class="empty-title">{{ searchKeyword ? '未找到相关客户' : '暂无客户数据' }}</text>
				<text class="empty-desc" v-if="searchKeyword">请尝试其他关键词搜索</text>
				<text class="empty-desc" v-else>请先添加客户信息</text>
				<button class="empty-action" @tap="refreshData" v-if="!searchKeyword">
					<text class="retry-text">重新加载</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
import clientApi from '../../api/client.js'
import pageMixin from '../../utils/page-mixin.js'
import { formatDateTime, formatDate } from '../../utils/date-formatter.js'

export default {
	mixins: [pageMixin],
	
	data() {
		return {
			// 覆盖混入的缓存配置
			cacheType: 'clients',
			cacheKey: 'select_list',
			
			// 客户列表数据（使用混入的listData）
			clientList: [],
			
			// 筛选状态
			filterType: 'all',
			filterOptions: [
				{ key: 'all', label: '全部', icon: '👥' },
				{ key: 'vip', label: 'VIP', icon: '👑' },
				{ key: 'new', label: '新客户', icon: '🆕' },
				{ key: 'active', label: '活跃', icon: '🔥' }
			]
		}
	},
	
	computed: {
		// 当前筛选选项
		currentFilterOption() {
			return this.filterOptions.find(option => option.key === this.filterType) || this.filterOptions[0]
		}
	},
	
	// 页面生命周期回调
	onPageLoad() {
		console.log('选择客户页面初始化')
		this.loadInitialData()
	},
	
	onPageShow() {
		console.log('选择客户页面显示')
		// 混入会自动处理是否需要刷新
	},
	
	methods: {
		/**
		 * 加载初始数据
		 */
		async loadInitialData() {
			try {
				this.showPageLoading('加载客户列表...')
				await this.loadData(true)
			} catch (error) {
				this.handlePageError(error, '加载客户列表')
			} finally {
				this.hidePageLoading()
			}
		},
		
		/**
		 * 加载数据（混入要求的方法）
		 */
		async loadData(isRefresh = false) {
			const params = {
				page: isRefresh ? 1 : this.currentPage,
				per_page: this.pageSize
			}
			
			// 添加筛选条件
			if (this.filterType && this.filterType !== 'all') {
				params.filter = this.filterType
			}
			
			// 使用缓存加载数据
			const response = await this.loadWithCache(
				() => clientApi.getClientList(params),
				params,
				5 * 60 * 1000 // 5分钟缓存
			)
			
			// 处理响应数据
			this.handleDataResponse(response, isRefresh)
		},
		
		/**
		 * 搜索数据（混入要求的方法）
		 */
		async searchData(keyword) {
			if (!keyword || !keyword.trim()) {
				// 清空搜索，重新加载列表
				this.clientList = []
				await this.loadData(true)
				return
			}
			
			// 搜索不使用缓存，确保结果实时性
			const response = await clientApi.searchClients(keyword.trim())
			
			// 处理搜索结果
			this.handleSearchResponse(response)
		},
		
		/**
		 * 处理数据响应
		 */
		handleDataResponse(response, isRefresh) {
			let newClients = []
			
			// 解析响应数据
			if (response.data && Array.isArray(response.data)) {
				newClients = response.data
			} else if (response.data && response.data.data && Array.isArray(response.data.data)) {
				newClients = response.data.data
			} else {
				newClients = response.data || []
			}
			
			// 添加调试日志
			console.log('原始客户数据:', newClients)
			if (newClients.length > 0) {
				console.log('第一个客户数据结构:', newClients[0])
			}
			
			// 更新客户列表
			this.setListData(newClients, isRefresh)
			this.clientList = this.listData
			
			// 更新分页状态
			this.updatePagination(response)
			
			console.log(`客户数据加载完成: ${newClients.length} 条`)
		},
		
		/**
		 * 处理搜索响应
		 */
		handleSearchResponse(response) {
			let searchResults = []
			
			// 解析搜索结果
			if (response.data && Array.isArray(response.data)) {
				searchResults = response.data
			} else if (response.data && response.data.data && Array.isArray(response.data.data)) {
				searchResults = response.data.data
			} else {
				searchResults = response.data || []
			}
			
			// 添加调试日志
			console.log('搜索结果数据:', searchResults)
			
			// 搜索结果直接替换列表
			this.clientList = searchResults
			this.listData = searchResults
			this.pageEmpty = searchResults.length === 0
			this.hasMore = false // 搜索结果不支持分页
			
			console.log(`搜索完成: ${searchResults.length} 条结果`)
		},
		
		/**
		 * 处理搜索输入
		 */
		onSearchInput(e) {
			const value = e.detail.value
			this.handleSearchInput(value)
		},
		
		/**
		 * 搜索确认
		 */
		onSearchConfirm() {
			this.performSearch()
		},
		
		/**
		 * 清除搜索
		 */
		clearSearch() {
			this.searchKeyword = ''
			this.refreshData()
		},
		
		/**
		 * 切换筛选条件
		 */
		async setFilter(filterKey) {
			if (this.filterType === filterKey) return
			
			console.log(`切换筛选: ${this.filterType} -> ${filterKey}`)
			
			this.filterType = filterKey
			this.searchKeyword = ''
			
			// 清除相关缓存
			this.clearPageCache()
			
			// 重新加载数据
			try {
				this.showListLoading()
				this.currentPage = 1
				this.hasMore = true
				this.clientList = []
				
				await this.loadData(true)
				
				// 显示切换反馈
				const option = this.currentFilterOption
				this.showToast(`已切换到：${option.label}`, 'none', 1000)
				
			} catch (error) {
				this.handleListError(error, '切换筛选')
			} finally {
				this.hideListLoading()
			}
		},
		
		/**
		 * 选择客户
		 */
		selectClient(client) {
			// 通过事件总线或页面参数传递选中的客户信息
			const pages = getCurrentPages()
			const prevPage = pages[pages.length - 2]
			
			if (prevPage) {
				// 调用上一页的方法来接收选中的客户
				if (prevPage.$vm && prevPage.$vm.onClientSelected) {
					prevPage.$vm.onClientSelected(client)
				}
			}
			
			// 返回上一页
			uni.navigateBack()
		},
		
		/**
		 * 跳转到添加客户页面
		 */
		goToAddClient() {
			uni.navigateTo({
				url: '/pages/clients/add-client'
			})
		},
		
		/**
		 * 获取客户头像文字
		 */
		getAvatarText(client) {
			if (client.merchant_name) {
				return client.merchant_name.charAt(0).toUpperCase()
			}
			if (client.name) {
				return client.name.charAt(0).toUpperCase()
			}
			return '客'
		},
		
		/**
		 * 判断是否为新客户
		 */
		isNewClient(client) {
			if (!client.created_at && !client.register_time && !client.registration_date) {
				console.log('客户无创建时间:', client.name || client.merchant_name)
				return false
			}
			
			// 尝试多个可能的时间字段
			const timeField = client.created_at || client.register_time || client.registration_date
			const createTime = new Date(timeField)
			
			// 检查日期是否有效
			if (isNaN(createTime.getTime())) {
				console.log('客户时间格式无效:', timeField, client.name || client.merchant_name)
				return false
			}
			
			const now = new Date()
			const diffDays = (now - createTime) / (1000 * 60 * 60 * 24)
			const isNew = diffDays <= 30 // 30天内注册的为新客户
			
			if (isNew) {
				console.log('新客户:', client.name || client.merchant_name, '注册时间:', timeField, '天数差:', diffDays)
			}
			
			return isNew
		},
		
		/**
		 * 格式化金额
		 */
		formatAmount(amount) {
			if (!amount) return '0.00'
			return parseFloat(amount).toLocaleString('zh-CN', {
				minimumFractionDigits: 2,
				maximumFractionDigits: 2
			})
		},
		
		/**
		 * 格式化日期
		 */
		formatDate(dateStr) {
			return formatDateTime(dateStr)
		},
		
		// 处理下拉刷新
		onRefresh() {
			this.refreshing = true
			this.refreshData()
		},
		
		// 刷新客户列表
		refreshData() {
			this.currentPage = 1
			this.hasMore = true
			this.clientList = []
			this.loadData(true).finally(() => {
				this.refreshing = false
			})
		},
		
		// 处理加载更多
		loadMore() {
			this.loadMoreData()
		}
	}
};
</script>

<style scoped>
.select-client-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-top: 140rpx; /* 减少padding，因为删除了统计栏 */
}

/* 固定搜索栏 */
.search-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	z-index: 999;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 搜索框 */
.search-box {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 16rpx;
	padding: 0 24rpx;
	height: 80rpx;
	margin: 20rpx 32rpx; /* 减少上下边距 */
}

.search-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
	opacity: 0.6;
}

.search-input {
	flex: 1;
	font-size: 32rpx;
	color: #333333;
}

.clear-btn {
	background: none;
	border: none;
	padding: 0;
	margin-left: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 40rpx;
	height: 40rpx;
}

.clear-icon {
	font-size: 32rpx;
	color: #666666;
}

/* 快速筛选 */
.filter-section {
	padding: 0 32rpx 20rpx; /* 减少底部边距 */
}

.filter-scroll {
	white-space: nowrap;
}

.filter-item {
	display: inline-block;
	background: #f8f9fa;
	border-radius: 20rpx;
	padding: 16rpx 32rpx;
	margin-right: 16rpx;
	transition: all 0.3s ease;
}

.filter-item.active {
	background: #007AFF;
}

.filter-text {
	font-size: 28rpx;
	color: #666666;
}

.filter-item.active .filter-text {
	color: #ffffff;
}

/* 客户列表 */
.client-list {
	padding: 0 32rpx 32rpx;
	margin-top: 0; /* 移除margin-top，因为容器已经有padding-top */
}

.client-card {
	background: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
	transition: all 0.3s ease;
}

.client-card:active {
	transform: scale(0.98);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

/* 客户头像和基本信息 */
.client-header {
	display: flex;
	align-items: center;
	padding: 32rpx;
}

.client-avatar {
	position: relative;
	width: 100rpx;
	height: 100rpx;
	border-radius: 50rpx;
	background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 32rpx;
	flex-shrink: 0;
}

.avatar-text {
	color: #ffffff;
	font-size: 36rpx;
	font-weight: 600;
}

.avatar-badge {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	background: #FF9500;
	color: #ffffff;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	transform: scale(0.8);
}

.client-main-info {
	flex: 1;
	min-width: 0;
}

.client-name-row {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.client-name {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
	margin-right: 16rpx;
	flex-shrink: 0;
}

.client-tags {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}

.tag {
	font-size: 24rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	margin-left: 8rpx;
	margin-bottom: 4rpx;
}

.new-tag {
	background: #007AFF;
	color: #ffffff;
}

.vip-tag {
	background: #FF9500;
	color: #ffffff;
}

.client-contact {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.client-phone {
	font-size: 28rpx;
	color: #666666;
}

.select-action {
	display: flex;
	align-items: center;
	margin-left: 16rpx;
	flex-shrink: 0;
}

.select-text {
	font-size: 28rpx;
	color: #007AFF;
	margin-right: 8rpx;
}

.arrow-icon {
	font-size: 32rpx;
	color: #007AFF;
	font-weight: bold;
}

/* 客户统计信息 */
.client-stats {
	display: flex;
	align-items: center;
	padding: 24rpx 32rpx;
	background: #f8f9fa;
	border-top: 2rpx solid #f0f0f0;
}

.stat-item {
	flex: 1;
	text-align: center;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 4rpx;
}

.stat-value {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

/* 加载更多 */
.load-more-section {
	padding: 32rpx;
	text-align: center;
}

.load-more-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid #f0f0f0;
	border-top: 4rpx solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-right: 16rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}

.load-more-btn {
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 16rpx;
	padding: 24rpx 48rpx;
	font-size: 28rpx;
	color: #666666;
}

.load-more-btn:active {
	background: #e0e0e0;
}

.no-more-section {
	padding: 32rpx;
	text-align: center;
}

.no-more-text {
	font-size: 28rpx;
	color: #999999;
}

/* 初始加载状态 */
.loading-section {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 120rpx 32rpx;
	margin-top: 0; /* 移除margin-top，因为容器已经有padding-top */
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
}

/* 空状态 */
.empty-section {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 120rpx 32rpx;
	margin-top: 0; /* 移除margin-top，因为容器已经有padding-top */
}

.empty-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 32rpx;
	opacity: 0.3;
}

.empty-title {
	font-size: 32rpx;
	color: #666666;
	margin-bottom: 16rpx;
}

.empty-desc {
	font-size: 28rpx;
	color: #999999;
	margin-bottom: 32rpx;
}

.empty-action {
	background: #007AFF;
	color: #ffffff;
	border: none;
	border-radius: 16rpx;
	padding: 24rpx 48rpx;
	font-size: 28rpx;
}

.retry-text {
	color: #ffffff;
}
</style> 