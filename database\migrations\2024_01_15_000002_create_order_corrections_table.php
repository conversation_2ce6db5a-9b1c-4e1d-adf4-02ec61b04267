<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_corrections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade')->comment('原订单ID');
            $table->string('correction_no', 32)->unique()->comment('更正单号');
            $table->enum('status', ['pending', 'confirmed', 'cancelled'])->default('pending')->comment('更正状态');
            $table->decimal('original_total', 10, 2)->comment('原订单金额');
            $table->decimal('corrected_total', 10, 2)->comment('更正后金额');
            $table->decimal('difference_amount', 10, 2)->comment('差额（正数=补款，负数=退款）');
            $table->enum('correction_type', ['increase', 'decrease', 'no_change'])->comment('更正类型');
            $table->text('correction_reason')->nullable()->comment('更正原因');
            $table->foreignId('corrected_by')->nullable()->constrained('employees')->onDelete('set null')->comment('更正操作员ID');
            $table->timestamp('corrected_at')->nullable()->comment('更正时间');
            $table->timestamp('confirmed_at')->nullable()->comment('确认时间');
            $table->timestamps();
            
            $table->index(['order_id', 'status']);
            $table->index('correction_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_corrections');
    }
}; 