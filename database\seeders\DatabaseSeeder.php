<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);
        
        $this->call([
            // 基础配置数据
            MembershipLevelSeeder::class,
            UnitSystemSeeder::class,
            PaymentDiscountSeeder::class,
            
            // 地区数据（如果需要）
            // SichuanRegionSeeder::class,
            
            // 基础分类数据
            CategorySeeder::class,
            
            // 数据库迁移记录
            InsertMigrationRecordSeeder::class,
        ]);
    }
}
