<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('deliverers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('delivery_area')->nullable()->comment('配送区域');
            $table->integer('max_orders')->default(10)->comment('最大接单量');
            $table->decimal('rating', 3, 2)->default(5.00)->comment('评分');
            $table->enum('status', ['available', 'busy', 'offline'])->default('offline')->comment('状态：空闲、忙碌、离线');
            $table->string('working_hours')->nullable()->comment('工作时间, 如: 09:00-18:00');
            $table->string('transportation')->nullable()->comment('交通工具');
            $table->decimal('last_location_lat', 10, 7)->nullable()->comment('最后位置纬度');
            $table->decimal('last_location_lng', 10, 7)->nullable()->comment('最后位置经度');
            $table->timestamp('last_active_at')->nullable()->comment('最后活跃时间');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('deliverers');
    }
}; 