<?php

namespace App\Product\Services;

use App\Product\Models\Product;
use App\Models\User;
use App\Region\Models\Region;
use App\Region\Models\RegionPrice;
use App\Product\Models\CategoryRegionPrice;
use App\Product\Models\CategoryMemberDiscount;
use App\Product\Models\ProductMemberDiscount;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class PriceCalculationService
{
    /**
     * 计算商品价格（支持游客和登录用户）
     * 统一的价格计算入口，解决多套系统冲突问题
     *
     * @param Product $product 商品
     * @param User|null $user 用户（null表示游客）
     * @param int|null $regionId 区域ID
     * @param int $quantity 数量
     * @param array $options 额外选项
     * @return array 价格信息
     */
    public function calculatePrice(Product $product, ?User $user = null, ?int $regionId = null, int $quantity = 1, array $options = [])
    {
        // 使用分层缓存策略
        $baseCacheKey = $this->generateBaseCacheKey($product->id, $regionId);
        $userCacheKey = $this->generateUserCacheKey($product->id, $user?->id, $regionId, $quantity);
        
        // 先尝试获取用户特定缓存
        $priceData = Cache::get($userCacheKey);
        if ($priceData) {
            return $priceData;
        }
        
        // 如果没有用户特定缓存，尝试获取基础缓存
        $basePrice = Cache::get($baseCacheKey);
        if ($basePrice && !$user) {
            // 游客用户可以直接使用基础缓存
            return $this->finalizePrice($basePrice, $quantity);
        }
        
        // 计算价格
        $priceData = $this->performPriceCalculation($product, $user, $regionId, $quantity);
        
        // 分层缓存结果
        if (!$user) {
            // 游客价格缓存更长时间
            Cache::put($baseCacheKey, $priceData, 1800); // 30分钟
        }
        
        // 用户特定价格缓存较短时间
        Cache::put($userCacheKey, $priceData, 600); // 10分钟
        
        return $priceData;
    }
    
    /**
     * 执行价格计算 - 统一的价格计算逻辑
     * 修复优先级：基础价格 -> 区域价格 -> 会员折扣（始终应用）
     */
    private function performPriceCalculation(Product $product, ?User $user, ?int $regionId, int $quantity): array
    {
        $basePrice = $product->price;
        $currentPrice = $basePrice;
        $discountInfo = [];
        $priceType = 'base';
        $hasRegionPrice = false;
        
        // 第一步：确定基础价格（区域价格优先）
        $regionPriceInfo = $this->getRegionPriceInfo($product, $regionId);
        if ($regionPriceInfo) {
            $currentPrice = $regionPriceInfo['price'];
            $discountInfo[] = $regionPriceInfo['discount_info'];
            $priceType = 'region';
            $hasRegionPrice = true;
        }
        
        // 第二步：应用分类折扣（只有在没有区域价格时，分类区域折扣才适用）
        if ($product->category_id) {
            $categoryDiscountInfo = $this->getCategoryDiscountInfo($product, $user, $regionId, $currentPrice, $hasRegionPrice);
            if ($categoryDiscountInfo) {
                $currentPrice = $categoryDiscountInfo['final_price'];
                $discountInfo = array_merge($discountInfo, $categoryDiscountInfo['discount_info']);
                $priceType = $categoryDiscountInfo['price_type'];
            }
        }
        
        // 第三步：应用会员折扣（在当前价格基础上，无论是否有区域价格都适用）
        $memberDiscountInfo = $this->getMemberDiscountInfo($product, $user, $currentPrice);
        if ($memberDiscountInfo) {
            $currentPrice = $memberDiscountInfo['final_price'];
            $discountInfo = array_merge($discountInfo, $memberDiscountInfo['discount_info']);
            $priceType = $memberDiscountInfo['price_type'];
        }
        
        $finalData = [
            'base_price' => $basePrice,
            'final_price' => $currentPrice,
            'total_discount' => $basePrice - $currentPrice,
            'price_type' => $priceType,
            'discount_info' => $discountInfo,
            'user_type' => $user ? 'member' : 'guest',
            'region_id' => $regionId,
            'price_labels' => $this->generatePriceLabels($discountInfo, $basePrice - $currentPrice)
        ];
        
        return $this->finalizePrice($finalData, $quantity);
    }
    
    /**
     * 获取区域价格信息
     */
    private function getRegionPriceInfo(Product $product, ?int $regionId): ?array
    {
        if (!$regionId) {
            return null;
        }
        
        $regionPrice = $this->getValidRegionPrice($product->id, $regionId);
        if (!$regionPrice) {
            return null;
        }
        
        // 检查区域价格库存限制（注意：这是价格表的库存限制，不是商品实际库存）
        if ($regionPrice->stock !== null && $regionPrice->stock < 1) {
            throw new \Exception("商品 {$product->name} 在当前区域的价格库存限额已用完");
        }
        
        return [
            'price' => $regionPrice->price,
            'discount_info' => [
                'type' => 'region_price',
                'name' => '区域价格',
                'original_price' => $product->price,
                'final_price' => $regionPrice->price,
                'discount_amount' => $product->price - $regionPrice->price,
                'region_id' => $regionId
            ]
        ];
    }
    
    /**
     * 获取会员折扣信息（统一处理所有会员折扣）
     * 优化：确保用户总是享受最优惠的价格
     * 修复：默认会员等级如果没有设置折扣金额应该不处理
     */
    private function getMemberDiscountInfo(Product $product, ?User $user, float $currentPrice): ?array
    {
        if (!$user || !$user->membership_level_id) {
            return null;
        }
        
        $finalPrice = $currentPrice;
        $discountInfo = [];
        $bestDiscount = 0;
        $bestDiscountInfo = null;
        
        // 1. 检查商品特定会员折扣
        $productMemberDiscount = $this->getValidProductMemberDiscount($product->id, $user->membership_level_id);
        if ($productMemberDiscount) {
            $discountAmount = $this->calculateDiscountAmount(
                $currentPrice,
                $productMemberDiscount->discount_type,
                $productMemberDiscount->discount_value,
                $productMemberDiscount->max_discount
            );
            
            if ($discountAmount > $bestDiscount) {
                $bestDiscount = $discountAmount;
                $bestDiscountInfo = [
                    'type' => 'product_member_discount',
                    'name' => '商品会员减免',
                    'discount_amount' => $discountAmount,
                    'discount_type' => $productMemberDiscount->discount_type,
                    'discount_value' => $productMemberDiscount->discount_value
                ];
            }
        }
        
        // 2. 检查全局会员减免（排除默认会员等级或折扣金额为0的情况）
        if ($user->membershipLevel && 
            $user->membershipLevel->discount_rate > 0 && 
            !$user->membershipLevel->is_default) {
            
            $discountAmount = min($user->membershipLevel->discount_rate, $currentPrice);
            
            if ($discountAmount > $bestDiscount) {
                $bestDiscount = $discountAmount;
                $bestDiscountInfo = [
                    'type' => 'global_member_discount',
                    'name' => $user->membershipLevel->name . '减免',
                    'discount_amount' => $discountAmount,
                    'discount_type' => 'fixed_amount',
                    'discount_value' => $user->membershipLevel->discount_rate
                ];
            }
        }
        
        // 应用最优折扣
        if ($bestDiscountInfo) {
            $finalPrice = max(0, $currentPrice - $bestDiscount);
            $discountInfo[] = $bestDiscountInfo;
            
            return [
                'final_price' => $finalPrice,
                'discount_info' => $discountInfo,
                'price_type' => 'member_discount'
            ];
        }
        
        return null;
    }
    
    /**
     * 获取分类折扣信息
     * 修正逻辑：分类区域折扣只有在没有区域价格时才适用，分类会员折扣总是适用
     */
    private function getCategoryDiscountInfo(Product $product, ?User $user, ?int $regionId, float $currentPrice, bool $hasRegionPrice): ?array
    {
        $finalPrice = $currentPrice;
        $discountInfo = [];
        $totalDiscount = 0;
        
        // 1. 分类区域折扣（只有在没有区域价格时才适用）
        if ($regionId && !$hasRegionPrice) {
            $categoryRegionPrice = $this->getValidCategoryRegionPrice($product->category_id, $regionId);
            if ($categoryRegionPrice) {
                $discountAmount = $this->calculateDiscountAmount(
                    $finalPrice,
                    $categoryRegionPrice->discount_type,
                    $categoryRegionPrice->discount_value,
                    $categoryRegionPrice->max_discount
                );
                
                $finalPrice = max(0, $finalPrice - $discountAmount);
                $totalDiscount += $discountAmount;
                $discountInfo[] = [
                    'type' => 'category_region_discount',
                    'name' => '分类区域优惠',
                    'discount_amount' => $discountAmount,
                    'discount_type' => $categoryRegionPrice->discount_type,
                    'discount_value' => $categoryRegionPrice->discount_value
                ];
            }
        }
        
        // 2. 分类会员折扣（总是适用，无论是否有区域价格）
        if ($user && $user->membership_level_id) {
            $categoryMemberDiscount = $this->getValidCategoryMemberDiscount($product->category_id, $user->membership_level_id);
            if ($categoryMemberDiscount) {
                $discountAmount = $this->calculateDiscountAmount(
                    $finalPrice,
                    $categoryMemberDiscount->discount_type,
                    $categoryMemberDiscount->discount_value,
                    $categoryMemberDiscount->max_discount
                );
                
                $finalPrice = max(0, $finalPrice - $discountAmount);
                $totalDiscount += $discountAmount;
                $discountInfo[] = [
                    'type' => 'category_member_discount',
                    'name' => '分类会员减免',
                    'discount_amount' => $discountAmount,
                    'discount_type' => $categoryMemberDiscount->discount_type,
                    'discount_value' => $categoryMemberDiscount->discount_value
                ];
            }
        }
        
        if ($totalDiscount > 0) {
            return [
                'final_price' => $finalPrice,
                'discount_info' => $discountInfo,
                'price_type' => 'category_discount'
            ];
        }
        
        return null;
    }
    
    /**
     * 获取有效的商品区域价格
     */
    private function getValidRegionPrice(int $productId, int $regionId): ?RegionPrice
    {
        return RegionPrice::where('product_id', $productId)
            ->where('region_id', $regionId)
            ->where('status', true)
            ->where(function($query) {
                $query->whereNull('start_date')
                      ->orWhere('start_date', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_date')
                      ->orWhere('end_date', '>=', now());
            })
            ->first();
    }
    
    /**
     * 获取有效的商品会员折扣
     */
    private function getValidProductMemberDiscount(int $productId, int $membershipLevelId): ?ProductMemberDiscount
    {
        return ProductMemberDiscount::where('product_id', $productId)
            ->where('membership_level_id', $membershipLevelId)
            ->where('status', true)
            ->where(function($query) {
                $query->whereNull('start_time')
                      ->orWhere('start_time', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_time')
                      ->orWhere('end_time', '>=', now());
            })
            ->first();
    }
    
    /**
     * 获取有效的分类区域价格
     */
    private function getValidCategoryRegionPrice(int $categoryId, int $regionId): ?CategoryRegionPrice
    {
        return CategoryRegionPrice::where('category_id', $categoryId)
            ->where('region_id', $regionId)
            ->where('status', true)
            ->where(function($query) {
                $query->whereNull('start_time')
                      ->orWhere('start_time', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_time')
                      ->orWhere('end_time', '>=', now());
            })
            ->first();
    }
    
    /**
     * 获取有效的分类会员折扣
     */
    private function getValidCategoryMemberDiscount(int $categoryId, int $membershipLevelId): ?CategoryMemberDiscount
    {
        return CategoryMemberDiscount::where('category_id', $categoryId)
            ->where('membership_level_id', $membershipLevelId)
            ->where('status', true)
            ->where(function($query) {
                $query->whereNull('start_time')
                      ->orWhere('start_time', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_time')
                      ->orWhere('end_time', '>=', now());
            })
            ->first();
    }
    
    /**
     * 计算优惠金额 - 简化版本，只支持固定金额
     */
    private function calculateDiscountAmount(float $price, string $discountType, float $discountValue, ?float $maxDiscount = null): float
    {
        // 既然整个系统只支持固定金额，简化逻辑
        if ($discountType !== 'fixed_amount') {
            Log::warning("检测到非固定金额折扣类型: {$discountType}，系统只支持固定金额");
            return 0;
        }
        
        $discountAmount = $discountValue;
        
        // 应用最大优惠限制
        if ($maxDiscount !== null) {
            $discountAmount = min($discountAmount, $maxDiscount);
        }
        
        // 确保优惠金额不超过商品价格
        return min($discountAmount, $price);
    }
    
    /**
     * 生成价格标签
     */
    private function generatePriceLabels(array $discountInfo, float $totalDiscount): array
    {
        $labels = [];
        
        if ($totalDiscount > 0) {
            foreach ($discountInfo as $discount) {
                switch ($discount['type']) {
                    case 'region_price':
                        $labels[] = '区域价';
                        break;
                    case 'product_member_discount':
                        $labels[] = '会员价';
                        break;
                    case 'global_member_discount':
                        $labels[] = '会员减免';
                        break;
                    case 'category_region_discount':
                        $labels[] = '分类优惠';
                        break;
                    case 'category_member_discount':
                        $labels[] = '分类会员价';
                        break;
                }
            }
            
            // 如果有折扣但没有具体标签，添加通用标签
            if (empty($labels)) {
                $labels[] = '优惠价';
            }
        }
        
        return array_unique($labels);
    }

    /**
     * 最终化价格信息，添加数量计算
     */
    private function finalizePrice(array $priceData, int $quantity): array
    {
        $priceData['quantity'] = $quantity;
        $priceData['item_total'] = round($priceData['final_price'] * $quantity, 2);
        $priceData['final_price'] = round($priceData['final_price'], 2);
        $priceData['total_discount'] = round($priceData['total_discount'], 2);
        
        return $priceData;
    }
    
    /**
     * 生成基础缓存键
     */
    private function generateBaseCacheKey(int $productId, ?int $regionId): string
    {
        return sprintf('price_base:%d:region_%s', $productId, $regionId ?? 'default');
    }
    
    /**
     * 生成用户缓存键
     */
    private function generateUserCacheKey(int $productId, ?int $userId, ?int $regionId, int $quantity): string
    {
        return sprintf(
            'price_user:%d:user_%s:region_%s:qty_%d',
            $productId,
            $userId ?? 'guest',
            $regionId ?? 'default',
            $quantity
        );
    }
    
    /**
     * 批量计算商品价格 - 性能优化版本
     */
    public function calculateBatchPrices(array $productIds, ?User $user = null, ?int $regionId = null): array
    {
        // 1. 批量预加载所有需要的数据
        $products = Product::whereIn('id', $productIds)->get()->keyBy('id');
        
        // 2. 批量查询所有价格相关数据
        $priceData = $this->batchLoadPriceData($productIds, $user, $regionId);
        
        $prices = [];
        foreach ($productIds as $productId) {
            $product = $products->get($productId);
            if (!$product) {
                continue;
            }
            
            try {
                // 使用预加载的数据计算价格
                $prices[$productId] = $this->calculatePriceWithPreloadedData(
                    $product, 
                    $user, 
                    $regionId, 
                    1, 
                    $priceData
                );
            } catch (\Exception $e) {
                Log::warning("批量计算商品价格失败", [
                    'product_id' => $productId,
                    'error' => $e->getMessage()
                ]);
                // 返回基础价格作为后备
                $prices[$productId] = $this->getFallbackPrice($product, $user, $regionId);
            }
        }
        
        return $prices;
    }
    
    /**
     * 批量预加载价格数据
     */
    private function batchLoadPriceData(array $productIds, ?User $user, ?int $regionId): array
    {
        $data = [
            'region_prices' => [],
            'product_member_discounts' => [],
            'category_region_prices' => [],
            'category_member_discounts' => []
        ];
        
        // 获取所有商品的分类ID
        $categoryIds = Product::whereIn('id', $productIds)->pluck('category_id')->unique()->filter();
        
        // 批量查询区域价格
        if ($regionId) {
            $regionPrices = RegionPrice::whereIn('product_id', $productIds)
                ->where('region_id', $regionId)
                ->where('status', true)
                ->where(function($query) {
                    $query->whereNull('start_date')->orWhere('start_date', '<=', now());
                })
                ->where(function($query) {
                    $query->whereNull('end_date')->orWhere('end_date', '>=', now());
                })
                ->get()
                ->keyBy('product_id');
            $data['region_prices'] = $regionPrices;
            
            // 批量查询分类区域价格
            if ($categoryIds->isNotEmpty()) {
                $categoryRegionPrices = CategoryRegionPrice::whereIn('category_id', $categoryIds)
                    ->where('region_id', $regionId)
                    ->where('status', true)
                    ->where(function($query) {
                        $query->whereNull('start_time')->orWhere('start_time', '<=', now());
                    })
                    ->where(function($query) {
                        $query->whereNull('end_time')->orWhere('end_time', '>=', now());
                    })
                    ->get()
                    ->keyBy('category_id');
                $data['category_region_prices'] = $categoryRegionPrices;
            }
        }
        
        // 批量查询会员折扣
        if ($user && $user->membership_level_id) {
            $productMemberDiscounts = ProductMemberDiscount::whereIn('product_id', $productIds)
                ->where('membership_level_id', $user->membership_level_id)
                ->where('status', true)
                ->where(function($query) {
                    $query->whereNull('start_time')->orWhere('start_time', '<=', now());
                })
                ->where(function($query) {
                    $query->whereNull('end_time')->orWhere('end_time', '>=', now());
                })
                ->get()
                ->keyBy('product_id');
            $data['product_member_discounts'] = $productMemberDiscounts;
            
            // 批量查询分类会员折扣
            if ($categoryIds->isNotEmpty()) {
                $categoryMemberDiscounts = CategoryMemberDiscount::whereIn('category_id', $categoryIds)
                    ->where('membership_level_id', $user->membership_level_id)
                    ->where('status', true)
                    ->where(function($query) {
                        $query->whereNull('start_time')->orWhere('start_time', '<=', now());
                    })
                    ->where(function($query) {
                        $query->whereNull('end_time')->orWhere('end_time', '>=', now());
                    })
                    ->get()
                    ->keyBy('category_id');
                $data['category_member_discounts'] = $categoryMemberDiscounts;
            }
        }
        
        return $data;
    }
    
    /**
     * 使用预加载数据计算价格
     */
    private function calculatePriceWithPreloadedData(Product $product, ?User $user, ?int $regionId, int $quantity, array $priceData): array
    {
        $basePrice = $product->price;
        $currentPrice = $basePrice;
        $discountInfo = [];
        $priceType = 'base';
        $hasRegionPrice = false;
        
        // 第一步：确定基础价格（区域价格优先）
        $regionPrice = $priceData['region_prices'][$product->id] ?? null;
        if ($regionPrice) {
            $currentPrice = $regionPrice->price;
            $discountInfo[] = [
                'type' => 'region_price',
                'name' => '区域价格',
                'original_price' => $product->price,
                'final_price' => $regionPrice->price,
                'discount_amount' => $product->price - $regionPrice->price,
                'region_id' => $regionId
            ];
            $priceType = 'region';
            $hasRegionPrice = true;
        }
        
        // 第二步：应用分类折扣
        if ($product->category_id) {
            $categoryDiscountInfo = $this->getCategoryDiscountInfoWithPreloadedData(
                $product, 
                $user, 
                $regionId, 
                $currentPrice, 
                $hasRegionPrice, 
                $priceData
            );
            if ($categoryDiscountInfo) {
                $currentPrice = $categoryDiscountInfo['final_price'];
                $discountInfo = array_merge($discountInfo, $categoryDiscountInfo['discount_info']);
                $priceType = $categoryDiscountInfo['price_type'];
            }
        }
        
        // 第三步：应用会员折扣
        $memberDiscountInfo = $this->getMemberDiscountInfoWithPreloadedData($product, $user, $currentPrice, $priceData);
        if ($memberDiscountInfo) {
            $currentPrice = $memberDiscountInfo['final_price'];
            $discountInfo = array_merge($discountInfo, $memberDiscountInfo['discount_info']);
            $priceType = $memberDiscountInfo['price_type'];
        }
        
        $finalData = [
            'base_price' => $basePrice,
            'final_price' => $currentPrice,
            'total_discount' => $basePrice - $currentPrice,
            'price_type' => $priceType,
            'discount_info' => $discountInfo,
            'user_type' => $user ? 'member' : 'guest',
            'region_id' => $regionId,
            'price_labels' => $this->generatePriceLabels($discountInfo, $basePrice - $currentPrice)
        ];
        
        return $this->finalizePrice($finalData, $quantity);
    }
    
    /**
     * 使用预加载数据获取分类折扣信息
     */
    private function getCategoryDiscountInfoWithPreloadedData(Product $product, ?User $user, ?int $regionId, float $currentPrice, bool $hasRegionPrice, array $priceData): ?array
    {
        $finalPrice = $currentPrice;
        $discountInfo = [];
        $totalDiscount = 0;
        
        // 1. 分类区域折扣（只有在没有区域价格时才适用）
        if ($regionId && !$hasRegionPrice) {
            $categoryRegionPrice = $priceData['category_region_prices'][$product->category_id] ?? null;
            if ($categoryRegionPrice) {
                $discountAmount = $this->calculateDiscountAmount(
                    $finalPrice,
                    $categoryRegionPrice->discount_type,
                    $categoryRegionPrice->discount_value,
                    $categoryRegionPrice->max_discount
                );
                
                $finalPrice = max(0, $finalPrice - $discountAmount);
                $totalDiscount += $discountAmount;
                $discountInfo[] = [
                    'type' => 'category_region_discount',
                    'name' => '区域优惠',
                    'discount_amount' => $discountAmount,
                    'discount_type' => $categoryRegionPrice->discount_type,
                    'discount_value' => $categoryRegionPrice->discount_value
                ];
            }
        }
        
        // 2. 分类会员折扣（总是适用）
        if ($user && $user->membership_level_id) {
            $categoryMemberDiscount = $priceData['category_member_discounts'][$product->category_id] ?? null;
            if ($categoryMemberDiscount) {
                $discountAmount = $this->calculateDiscountAmount(
                    $finalPrice,
                    $categoryMemberDiscount->discount_type,
                    $categoryMemberDiscount->discount_value,
                    $categoryMemberDiscount->max_discount
                );
                
                $finalPrice = max(0, $finalPrice - $discountAmount);
                $totalDiscount += $discountAmount;
                $discountInfo[] = [
                    'type' => 'category_member_discount',
                    'name' => '分类会员优惠',
                    'discount_amount' => $discountAmount,
                    'discount_type' => $categoryMemberDiscount->discount_type,
                    'discount_value' => $categoryMemberDiscount->discount_value
                ];
            }
        }
        
        if ($totalDiscount > 0) {
            return [
                'final_price' => $finalPrice,
                'discount_info' => $discountInfo,
                'price_type' => count($discountInfo) > 1 ? 'category_combined' : $discountInfo[0]['type']
            ];
        }
        
        return null;
    }
    
    /**
     * 使用预加载数据获取会员折扣信息
     */
    private function getMemberDiscountInfoWithPreloadedData(Product $product, ?User $user, float $currentPrice, array $priceData): ?array
    {
        if (!$user || !$user->membership_level_id) {
            return null;
        }
        
        $finalPrice = $currentPrice;
        $discountInfo = [];
        $bestDiscount = 0;
        $bestDiscountInfo = null;
        
        // 1. 检查商品特定会员折扣
        $productMemberDiscount = $priceData['product_member_discounts'][$product->id] ?? null;
        if ($productMemberDiscount) {
            $discountAmount = $this->calculateDiscountAmount(
                $currentPrice,
                $productMemberDiscount->discount_type,
                $productMemberDiscount->discount_value,
                $productMemberDiscount->max_discount
            );
            
            if ($discountAmount > $bestDiscount) {
                $bestDiscount = $discountAmount;
                $bestDiscountInfo = [
                    'type' => 'product_member_discount',
                    'name' => '商品会员减免',
                    'discount_amount' => $discountAmount,
                    'discount_type' => $productMemberDiscount->discount_type,
                    'discount_value' => $productMemberDiscount->discount_value
                ];
            }
        }
        
        // 2. 检查全局会员减免
        if ($user->membershipLevel && 
            $user->membershipLevel->discount_rate > 0 && 
            !$user->membershipLevel->is_default) {
            
            $discountAmount = min($user->membershipLevel->discount_rate, $currentPrice);
            
            if ($discountAmount > $bestDiscount) {
                $bestDiscount = $discountAmount;
                $bestDiscountInfo = [
                    'type' => 'global_member_discount',
                    'name' => $user->membershipLevel->name . '减免',
                    'discount_amount' => $discountAmount,
                    'discount_type' => 'fixed_amount',
                    'discount_value' => $user->membershipLevel->discount_rate
                ];
            }
        }
        
        // 应用最优折扣
        if ($bestDiscountInfo) {
            $finalPrice = max(0, $currentPrice - $bestDiscount);
            $discountInfo[] = $bestDiscountInfo;
            
            return [
                'final_price' => $finalPrice,
                'discount_info' => $discountInfo,
                'price_type' => 'member_discount'
            ];
        }
        
        return null;
    }
    
    /**
     * 获取后备价格
     */
    private function getFallbackPrice(Product $product, ?User $user, ?int $regionId): array
    {
        return [
            'base_price' => $product->price,
            'final_price' => $product->price,
            'total_discount' => 0,
            'price_type' => 'base',
            'discount_info' => [],
            'user_type' => $user ? 'member' : 'guest',
            'region_id' => $regionId,
            'quantity' => 1,
            'item_total' => $product->price,
            'price_labels' => []
        ];
    }
    
    /**
     * 清除价格缓存
     */
    public function clearPriceCache(?int $productId = null, ?int $userId = null): void
    {
        if ($productId && $userId) {
            // 清除特定商品和用户的缓存
            $pattern = "price_v2:{$productId}:user_{$userId}:*";
        } elseif ($productId) {
            // 清除特定商品的所有缓存
            $pattern = "price_v2:{$productId}:*";
        } elseif ($userId) {
            // 清除特定用户的所有缓存
            $pattern = "price_v2:*:user_{$userId}:*";
        } else {
            // 清除所有价格缓存
            $pattern = "price_v2:*";
        }
        
        // 这里需要根据实际的缓存驱动实现清除逻辑
        Cache::flush(); // 简单实现，实际可以更精确
    }
    
    /**
     * 获取价格计算摘要（用于调试）
     */
    public function getPriceCalculationSummary(Product $product, ?User $user = null, ?int $regionId = null): array
    {
        $summary = [
            'product_id' => $product->id,
            'product_name' => $product->name,
            'base_price' => $product->price,
            'user_info' => [
                'is_guest' => !$user,
                'user_id' => $user?->id,
                'membership_level' => $user?->membershipLevel?->name,
                'membership_discount' => $user?->membershipLevel?->discount_rate ?? 0
            ],
            'region_id' => $regionId,
            'available_discounts' => []
        ];
        
        // 检查可用的折扣
        if ($regionId) {
            $regionPrice = $this->getValidRegionPrice($product->id, $regionId);
            if ($regionPrice) {
                $summary['available_discounts']['region_price'] = [
                    'type' => 'direct_price',
                    'price' => $regionPrice->price,
                    'discount' => $product->price - $regionPrice->price
                ];
            }
            
            if ($product->category_id) {
                $categoryRegionPrice = $this->getValidCategoryRegionPrice($product->category_id, $regionId);
                if ($categoryRegionPrice) {
                    $summary['available_discounts']['category_region_discount'] = [
                        'type' => $categoryRegionPrice->discount_type,
                        'value' => $categoryRegionPrice->discount_value,
                        'max_discount' => $categoryRegionPrice->max_discount
                    ];
                }
            }
        }
        
        if ($user && $user->membership_level_id) {
            $productMemberDiscount = $this->getValidProductMemberDiscount($product->id, $user->membership_level_id);
            if ($productMemberDiscount) {
                $summary['available_discounts']['product_member_discount'] = [
                    'type' => $productMemberDiscount->discount_type,
                    'value' => $productMemberDiscount->discount_value,
                    'max_discount' => $productMemberDiscount->max_discount
                ];
            }
            
            if ($product->category_id) {
                $categoryMemberDiscount = $this->getValidCategoryMemberDiscount($product->category_id, $user->membership_level_id);
                if ($categoryMemberDiscount) {
                    $summary['available_discounts']['category_member_discount'] = [
                        'type' => $categoryMemberDiscount->discount_type,
                        'value' => $categoryMemberDiscount->discount_value,
                        'max_discount' => $categoryMemberDiscount->max_discount
                    ];
                }
            }
        }
        
        return $summary;
    }
} 
