/**
 * 搜索工具类
 */

// 内存缓存，减少对Storage的频繁访问
const memoryCache = new Map();

// 缓存管理器
const cacheManager = {
  /**
   * 获取缓存数据
   * @param {string} key - 缓存键
   * @returns {any|null} - 缓存数据或null
   */
  get: (key) => {
    try {
      // 先尝试从内存缓存获取
      if (memoryCache.has(key)) {
        const cache = memoryCache.get(key);
        if (cache && cache.expiry > Date.now()) {
          return cache.data;
        } else {
          // 过期则删除内存缓存
          memoryCache.delete(key);
        }
      }
      
      // 从Storage获取
      const data = wx.getStorageSync(key);
      if (!data) return null;
      
      try {
        const cache = JSON.parse(data);
        if (cache && cache.expiry > Date.now()) {
          // 有效数据存入内存缓存
          memoryCache.set(key, cache);
          return cache.data;
        }
        // 过期则删除Storage缓存
        wx.removeStorageSync(key);
        return null;
      } catch (parseError) {
        console.error('缓存数据解析失败', parseError);
        // 无效数据删除
        wx.removeStorageSync(key);
        return null;
      }
    } catch (e) {
      console.error('缓存读取失败', e);
      return null;
    }
  },
  
  /**
   * 设置缓存数据
   * @param {string} key - 缓存键
   * @param {any} data - 缓存数据
   * @param {number} ttl - 过期时间（毫秒）
   */
  set: (key, data, ttl = 3600000) => {
    try {
      const cacheItem = {
        data,
        expiry: Date.now() + ttl
      };
      
      // 同时更新内存缓存和Storage缓存
      memoryCache.set(key, cacheItem);
      
      wx.setStorageSync(key, JSON.stringify(cacheItem));
    } catch (e) {
      console.error('缓存写入失败', e);
    }
  },
  
  /**
   * 删除缓存数据
   * @param {string} key - 缓存键
   */
  remove: (key) => {
    try {
      // 同时删除内存缓存和Storage缓存
      memoryCache.delete(key);
      wx.removeStorageSync(key);
    } catch (e) {
      console.error('缓存删除失败', e);
    }
  },
  
  /**
   * 清除所有搜索相关缓存
   */
  clearSearchCache: () => {
    try {
      // 清除内存缓存中的搜索相关项
      for (const key of memoryCache.keys()) {
        if (key.startsWith('search_') || key.startsWith('suggestions_')) {
          memoryCache.delete(key);
        }
      }
      
      // 清除Storage中的搜索相关项
      const keys = wx.getStorageInfoSync().keys;
      keys.forEach(key => {
        if (key.startsWith('search_') || key.startsWith('suggestions_')) {
          wx.removeStorageSync(key);
        }
      });
    } catch (e) {
      console.error('清除搜索缓存失败', e);
    }
  },
  
  /**
   * 清理过期缓存
   */
  clearExpired: () => {
    try {
      const now = Date.now();
      
      // 清理内存缓存
      for (const [key, value] of memoryCache.entries()) {
        if (value.expiry < now) {
          memoryCache.delete(key);
        }
      }
      
      // 清理Storage缓存
      const keys = wx.getStorageInfoSync().keys;
      keys.forEach(key => {
        if (key.startsWith('search_') || key.startsWith('suggestions_')) {
          try {
            const data = wx.getStorageSync(key);
            if (data) {
              const cache = JSON.parse(data);
              if (cache && cache.expiry < now) {
                wx.removeStorageSync(key);
              }
            }
          } catch (e) {
            // 解析失败，直接删除
            wx.removeStorageSync(key);
          }
        }
      });
    } catch (e) {
      console.error('清理过期缓存失败', e);
    }
  }
};

/**
 * 防抖函数
 * @param {Function} fn - 要执行的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} - 防抖处理后的函数
 */
const debounce = (fn, delay = 300) => {
  let timer = null;
  return function() {
    const context = this;
    const args = arguments;
    
    if (timer) clearTimeout(timer);
    
    timer = setTimeout(() => {
      fn.apply(context, args);
    }, delay);
  };
};

/**
 * 高亮搜索关键词
 * @param {string} text - 原始文本
 * @param {string} keyword - 关键词
 * @param {string} color - 高亮颜色
 * @returns {string} - 包含高亮标签的HTML字符串
 */
const highlightKeyword = (text, keyword, color = '#ff4d4f') => {
  if (!text || !keyword) return text;
  
  const regex = new RegExp(`(${keyword})`, 'gi');
  return text.replace(regex, `<text style="color: ${color}">$1</text>`);
};

/**
 * 搜索历史管理
 */
const searchHistory = {
  /**
   * 获取搜索历史
   * @param {number} limit - 限制数量
   * @returns {Array} - 搜索历史数组
   */
  get: (limit = 10) => {
    try {
      const history = wx.getStorageSync('searchHistory') || [];
      return Array.isArray(history) ? history.slice(0, limit) : [];
    } catch (e) {
      console.error('获取搜索历史失败', e);
      return [];
    }
  },
  
  /**
   * 添加搜索历史
   * @param {string} keyword - 搜索关键词
   * @param {number} limit - 历史记录最大数量
   */
  add: (keyword, limit = 10) => {
    if (!keyword) return;
    
    try {
      let history = wx.getStorageSync('searchHistory') || [];
      if (!Array.isArray(history)) history = [];
      
      // 移除重复项
      history = history.filter(item => item !== keyword);
      
      // 添加到头部
      history.unshift(keyword);
      
      // 限制数量
      if (history.length > limit) {
        history = history.slice(0, limit);
      }
      
      wx.setStorageSync('searchHistory', history);
    } catch (e) {
      console.error('添加搜索历史失败', e);
    }
  },
  
  /**
   * 清除搜索历史
   */
  clear: () => {
    try {
      wx.removeStorageSync('searchHistory');
    } catch (e) {
      console.error('清除搜索历史失败', e);
    }
  }
};

// 导出模块
module.exports = {
  cacheManager,
  debounce,
  highlightKeyword,
  searchHistory
}; 