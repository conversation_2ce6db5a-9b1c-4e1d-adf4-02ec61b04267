<?php

namespace App\Cart\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Cart\Services\CartService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class CartController extends Controller
{
    protected $cartService;
    
    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;
    }
    
    /**
     * 获取购物车列表
     */
    public function index(Request $request)
    {
        $cartData = $this->cartService->getCartItems();
        
        return response()->json([
            'code' => 200,
            'message' => '获取购物车成功',
            'data' => $cartData
        ]);
    }
    
    /**
     * 获取购物车商品数量
     */
    public function count(Request $request)
    {
        try {
            $count = $this->cartService->getCartItemsCount();
            
            return response()->json([
                'code' => 200,
                'message' => '获取购物车数量成功',
                'data' => $count
            ]);
        } catch (\Exception $e) {
            Log::error('获取购物车数量失败', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '获取购物车数量失败',
                'data' => 0
            ], 500);
        }
    }
    
    /**
     * 添加商品到购物车
     */
    public function store(Request $request)
    {
        // 打印请求数据和用户ID
        Log::info('添加购物车请求', [
            'user_id' => auth()->id() ?? null,
            'data' => $request->all()
        ]);
        
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|integer|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'sku_id' => 'nullable|integer|exists:product_skus,id',
            'unit_id' => 'nullable|integer|exists:units,id',
        ]);
        
        if ($validator->fails()) {
            Log::error('添加购物车验证失败', [
                'errors' => $validator->errors()->toArray()
            ]);
            
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $productId = $request->input('product_id');
        $quantity = $request->input('quantity', 1);
        $skuId = $request->input('sku_id');
        $unitId = $request->input('unit_id');
        
        try {
            $cartItem = $this->cartService->addToCart($productId, $quantity, $skuId, $unitId);
            
            Log::info('添加购物车成功', [
                'cart_item' => $cartItem
            ]);
            
            // 检查是否有数量调整
            $message = '商品已加入购物车';
            $product = \App\Product\Models\Product::find($productId);
            $minSaleQuantity = $product->min_sale_quantity ?? 1;
            
            if ($quantity < $minSaleQuantity && $cartItem->quantity >= $minSaleQuantity) {
                $unit = $product->getSaleDefaultUnit();
                $unitName = $unit ? $unit->name : '件';
                $message = "该商品最小起购量为{$minSaleQuantity}{$unitName}，已为您调整数量";
            }
            
            return response()->json([
                'code' => 200,
                'message' => $message,
                'data' => [
                    'cart_item' => $cartItem,
                    'adjusted_quantity' => $cartItem->quantity,
                    'original_quantity' => $quantity,
                    'min_sale_quantity' => $minSaleQuantity
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('添加购物车异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '添加购物车失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新购物车商品数量
     * 🔥 新增：处理数量低于最小起购数量时的删除逻辑
     */
    public function updateItem(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'quantity' => 'required|integer|min:1',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $quantity = $request->input('quantity');
        
        try {
            $cartItem = $this->cartService->updateItemQuantity($id, $quantity);
            
            // 🔥 新增：检查商品是否被删除
            if ($cartItem === null) {
                // 商品因数量低于最小起购数量而被删除
                return response()->json([
                    'code' => 200,
                    'message' => '商品数量低于最小起购量，已从购物车中移除',
                    'data' => [
                        'deleted' => true,
                        'reason' => 'below_min_quantity'
                    ]
                ]);
            }
            
            return response()->json([
                'code' => 200,
                'message' => '购物车商品数量已更新',
                'data' => [
                    'cart_item' => $cartItem,
                    'deleted' => false
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 404,
                'message' => '购物车商品不存在'
            ], 404);
        }
    }
    
    /**
     * 从购物车中移除商品
     */
    public function removeItem(Request $request, $id)
    {
        try {
            $result = $this->cartService->removeItem($id);
            
            return response()->json([
                'code' => 200,
                'message' => '商品已从购物车移除',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 404,
                'message' => '购物车商品不存在'
            ], 404);
        }
    }
    
    /**
     * 切换商品选中状态
     */
    public function toggleSelectItem(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'is_selected' => 'required|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $isSelected = $request->input('is_selected');
        
        try {
            $cartItem = $this->cartService->toggleItemSelected($id, $isSelected);
            
            return response()->json([
                'code' => 200,
                'message' => '商品选中状态已更新',
                'data' => $cartItem
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 404,
                'message' => '购物车商品不存在'
            ], 404);
        }
    }
    
    /**
     * 全选/取消全选
     */
    public function toggleSelectAll(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'is_selected' => 'required|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $isSelected = $request->input('is_selected');
        
        $result = $this->cartService->toggleAllSelected($isSelected);
        
        return response()->json([
            'code' => 200,
            'message' => $isSelected ? '已全选购物车商品' : '已取消全选',
            'data' => $result
        ]);
    }
    
    /**
     * 清空购物车
     */
    public function clear(Request $request)
    {
        $result = $this->cartService->clearCart();
        
        return response()->json([
            'code' => 200,
            'message' => '购物车已清空',
            'data' => $result
        ]);
    }
    
    /**
     * 合并本地购物车
     */
    public function merge(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array',
            'items.*.id' => 'required|integer|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.sku_id' => 'nullable|integer',
            'items.*.unit_id' => 'nullable|integer',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $localCartItems = $request->input('items');
        
        $result = $this->cartService->mergeLocalCart($localCartItems);
        
        return response()->json([
            'code' => 200,
            'message' => '本地购物车已合并',
            'data' => $result
        ]);
    }
    
    /**
     * 管理后台 - 获取所有用户的购物车列表
     */
    public function adminIndex(Request $request)
    {
        // 这里只是示例，实际实现可能需要根据您的业务需求调整
        $carts = \App\Cart\Models\Cart::with(['user', 'items.product'])->paginate(15);
        
        return view('admin.cart.index', compact('carts'));
    }
    
    /**
     * 管理后台 - 获取特定用户的购物车
     */
    public function adminUserCart(Request $request, $userId)
    {
        // 这里只是示例，实际实现可能需要根据您的业务需求调整
        $user = \App\Models\User::findOrFail($userId);
        $cart = \App\Cart\Models\Cart::where('user_id', $userId)->with(['items.product'])->first();
        
        return view('admin.cart.user', compact('user', 'cart'));
    }
} 