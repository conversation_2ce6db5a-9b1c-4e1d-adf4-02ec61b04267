<?php

namespace App\Crm\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Crm\Models\MembershipLevel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;

class MembershipLevelController extends Controller
{
    /**
     * 获取所有会员等级
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        // 只有管理员和CRM相关人员可以查看所有会员等级
        $currentEmployee = auth('sanctum')->user();
        
        if (!$currentEmployee || !in_array($currentEmployee->role, ['admin', 'manager', 'crm_agent'])) {
            return response()->json(ApiResponse::error('没有权限查看会员等级列表', 403), 403);
        }
        
        $levels = MembershipLevel::orderBy('sort_order')->get();
        
        return response()->json(ApiResponse::success($levels));
    }

    /**
     * 存储新创建的会员等级
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:membership_levels',
            'description' => 'nullable|string',
            'upgrade_points' => 'required|integer|min:0',
            'upgrade_amount' => 'nullable|numeric|min:0',
            'quick_upgrade_amount' => 'nullable|numeric|min:0',
            'discount_rate' => 'required|numeric|min:0|max:9999.99',
            'icon' => 'nullable|string',
            'sort_order' => 'integer|min:0',
            'status' => 'boolean',
            'privileges' => 'nullable|json',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        $level = MembershipLevel::create($request->all());
        
        // 如果设置为默认等级
        if ($request->has('is_default') && $request->is_default) {
            $level->setAsDefault();
        }

        return response()->json(ApiResponse::success($level, '会员等级创建成功'), 201);
    }

    /**
     * 获取会员等级详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $currentEmployee = auth('sanctum')->user();
        
        if (!$currentEmployee || !in_array($currentEmployee->role, ['admin', 'manager', 'crm_agent'])) {
            return response()->json(ApiResponse::error('没有权限查看会员等级详情', 403), 403);
        }
        
        $level = MembershipLevel::findOrFail($id);
        
        return response()->json(ApiResponse::success($level));
    }

    /**
     * 更新指定的会员等级
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $level = MembershipLevel::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255', Rule::unique('membership_levels')->ignore($id)],
            'description' => 'nullable|string',
            'upgrade_points' => 'required|integer|min:0',
            'upgrade_amount' => 'nullable|numeric|min:0',
            'quick_upgrade_amount' => 'nullable|numeric|min:0',
            'discount_rate' => 'required|numeric|min:0|max:9999.99',
            'icon' => 'nullable|string',
            'sort_order' => 'integer|min:0',
            'status' => 'boolean',
            'privileges' => 'nullable|json',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        $level->update($request->all());
        
        // 如果设置为默认等级
        if ($request->has('is_default') && $request->is_default) {
            $level->setAsDefault();
        }

        return response()->json(ApiResponse::success($level, '会员等级更新成功'));
    }

    /**
     * 删除指定的会员等级
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $level = MembershipLevel::findOrFail($id);
        
        // 检查是否为默认等级
        if ($level->is_default) {
            return response()->json(ApiResponse::error('无法删除默认会员等级', 422), 422);
        }
        
        // 检查是否有用户使用该等级
        if ($level->users()->count() > 0) {
            return response()->json(ApiResponse::error('该会员等级下有用户，无法删除', 422), 422);
        }
        
        $level->delete();
        
        return response()->json(ApiResponse::success(null, '会员等级删除成功'));
    }
    
    /**
     * 设置默认会员等级
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function setDefault($id)
    {
        $level = MembershipLevel::findOrFail($id);
        $level->setAsDefault();
        
        return response()->json(ApiResponse::success($level, '默认会员等级设置成功'));
    }
} 