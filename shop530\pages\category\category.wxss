/* 分类页面样式 - 完全重构版 */

/* 页面容器 */
.category-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  overflow: hidden;
}

/* 搜索容器 */
.search-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 8rpx 24rpx;
  background-color: var(--primary-color, #07c160);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  height: auto;
  box-sizing: border-box;
}

/* 内容区域 */
.content-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  margin-top: 100rpx; /* 为搜索框预留空间 */
}

/* 左侧分类导航 */
.category-nav {
  width: 25%;
  height: 100%;
  background-color: #f8f8f8;
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 5;
}

/* 快捷入口 */
.quick-entries {
  display: flex;
  flex-direction: column;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.quick-entry {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  font-size: 13px;
  color: #666;
  position: relative;
}

.quick-entry.active {
  color: #07c160;
  background-color: #fff;
}

.quick-entry.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background-color: #07c160;
  border-radius: 0 3px 3px 0;
}

/* 分类列表 */
.category-list {
  padding: 5px 0;
}

.category-item {
  padding: 12px 10px;
  position: relative;
  transition: all 0.3s ease;
}

.category-item.active {
  color: #07c160;
  background-color: #fff;
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background-color: #07c160;
  border-radius: 0 3px 3px 0;
}

.category-main {
  display: flex;
  align-items: center;
  font-size: 14px;
  position: relative;
}

.category-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}

.expand-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.expand-icon .arrow {
  width: 6px;
  height: 6px;
  border-right: 1px solid #999;
  border-bottom: 1px solid #999;
  transform: rotate(45deg);
  transition: transform 0.3s ease;
}

.expand-icon.expanded .arrow {
  transform: rotate(-135deg);
}

/* 子分类容器 */
.subcategory-container {
  overflow: hidden;
  transition: height 0.3s ease;
  background-color: #f0f0f0;
}

.subcategory-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  font-size: 13px;
  color: #666;
  position: relative;
}

.subcategory-item.active {
  color: #07c160;
  background-color: #e8f7ef;
}

.subcategory-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}

/* 右侧商品区域 */
.product-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #fff;
}

/* 分类标题 */
.category-title {
  padding: 12px 15px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #eee;
}

/* 三级分类容器 */
.third-category-container {
  background-color: #fff;
  border-bottom: 1px solid #eee;
  padding: 8rpx 0;
}

.third-category-scroll {
  white-space: nowrap;
  height: 56rpx;
}

.third-category-list {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  gap: 16rpx;
}

.third-category-item {
  flex-shrink: 0;
  padding: 8rpx 20rpx;
  background-color: #f8f8f8;
  border-radius: 28rpx;
  border: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}

.third-category-item.active {
  background-color: #e8f4fd;
  border-color: #07c160;
  color: #07c160;
}

.third-category-name {
  font-size: 24rpx;
  color: #666;
  white-space: nowrap;
}

.third-category-item.active .third-category-name {
  color: #07c160;
  font-weight: 500;
}

/* 排序栏 */
.sort-bar {
  display: flex;
  height: 40px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.sort-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  color: #666;
  position: relative;
}

.sort-item.active {
  color: #07c160;
}

.sort-icon, .price-sort-icon, .filter-icon {
  width: 12px;
  height: 12px;
  margin-left: 3px;
  position: relative;
}

.price-sort-icon::before,
.price-sort-icon::after {
  content: '';
  position: absolute;
  left: 50%;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
}

.price-sort-icon::before {
  top: 0;
  border-bottom: 4px solid #ccc;
  transform: translateX(-50%);
}

.price-sort-icon::after {
  bottom: 0;
  border-top: 4px solid #ccc;
  transform: translateX(-50%);
}

.price-sort-icon.asc::before {
  border-bottom-color: #07c160;
}

.price-sort-icon.desc::after {
  border-top-color: #07c160;
}

/* 商品列表 */
.product-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.product-list-items {
  display: flex;
  flex-direction: column;
}

.product-item {
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.product-item:last-child {
  margin-bottom: 0;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #07c160;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border-width: 1px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #999;
}

/* 加载更多 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
  font-size: 13px;
  color: #999;
}

.loading-more-text {
  margin-left: 6px;
}

/* 没有更多 */
.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
  font-size: 13px;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-top: 20rpx;
}

/* 购物车按钮 */
.cart-button {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #4CAF50;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
}

/* 购物车徽章 */
.cart-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #ff6b35;
  color: #fff;
  font-size: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  box-sizing: border-box;
}

/* 购物车动画 */
.cart-animation {
  position: fixed;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cart-animation-image {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .category-page {
    --text-color: #f0f0f0;
    --text-secondary: #aaa;
    --border-color: #333;
    --background-color: #222;
    --background-secondary: #1a1a1a;
    --active-background: #0a3d1d;
  }
}

/* 适配不同设备尺寸 */
@media screen and (min-width: 768px) {
  .category-nav {
    width: 20%;
  }
  
  .category-item-content {
    padding: 28rpx 16rpx;
  }
  
  .category-name {
    font-size: 30rpx;
  }
  
  .subcategory-item {
    padding: 24rpx 16rpx 24rpx 28rpx;
  }
  
  .subcategory-name {
    font-size: 26rpx;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-list {
  animation: fadeIn 0.3s ease-out;
}

/* 添加登录按钮样式 */
.login-button {
  margin-top: 20rpx;
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #ffffff;
  background-color: #00C853;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 200, 83, 0.3);
} 