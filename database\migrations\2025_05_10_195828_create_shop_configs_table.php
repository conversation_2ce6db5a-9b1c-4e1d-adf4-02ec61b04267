<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shop_configs', function (Blueprint $table) {
            $table->id();
            $table->string('key', 100)->unique()->comment('配置键名');
            $table->text('value')->nullable()->comment('配置值');
            $table->string('group', 50)->default('general')->comment('配置分组');
            $table->string('title', 100)->nullable()->comment('配置标题');
            $table->string('description')->nullable()->comment('配置描述');
            $table->string('type', 30)->default('text')->comment('配置类型: text, number, textarea, select, image, switch');
            $table->json('options')->nullable()->comment('配置选项，用于select类型');
            $table->boolean('is_system')->default(false)->comment('是否系统配置');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shop_configs');
    }
};
