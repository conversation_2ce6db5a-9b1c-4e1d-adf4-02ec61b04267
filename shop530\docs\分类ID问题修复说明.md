# 分类ID问题修复说明

## 问题描述
分类页面商品数据完全错误，原因是分类ID字段混乱，导致API调用时传递了错误的分类ID。

## 问题分析

### 原有问题
1. **字段混乱**：同时使用了`id`、`categoryId`、`currentCategoryId`等字段
2. **数据结构不统一**：主分类和子分类的ID字段处理不一致
3. **API调用错误**：使用了UI生成的`id`而不是实际的`categoryId`进行API调用
4. **页面参数处理错误**：没有正确处理从其他页面传入的分类ID

### 具体表现
- 点击分类后显示的商品与分类不匹配
- 页面跳转时指定分类ID无效
- 子分类切换时数据错乱

## 修复方案

### 1. 统一数据结构
```javascript
// 主分类结构
{
  id: "main_123",           // UI唯一标识，用于列表渲染
  categoryId: 123,          // 实际分类ID，用于API调用
  name: "水果",
  hasChildren: true,
  isSubCategory: false,
  children: [...]
}

// 子分类结构  
{
  id: "sub_456_123_0",      // UI唯一标识
  categoryId: 456,          // 实际分类ID，用于API调用
  name: "苹果",
  isSubCategory: true,
  parentCategoryId: 123     // 父分类的实际ID
}
```

### 2. 修复关键逻辑

#### 2.1 页面参数处理
```javascript
onLoad(options) {
  // 保存页面参数传入的分类ID
  const targetCategoryId = options.categoryId ? parseInt(options.categoryId) : null;
  this.setData({ targetCategoryId });
}
```

#### 2.2 初始分类选择
```javascript
selectInitialCategory(categories) {
  // 优先使用页面参数指定的分类ID
  const targetCategoryId = this.data.targetCategoryId;
  if (targetCategoryId) {
    // 在主分类和子分类中查找匹配的分类
    // ...查找逻辑
  }
  // 如果没找到，使用默认分类
}
```

#### 2.3 API调用修复
```javascript
async loadProducts(reset = false) {
  // 严格检查分类ID
  if (!currentCategory?.categoryId) {
    throw new Error('无效的分类ID');
  }
  
  // 使用实际的分类ID进行API调用
  const categoryId = currentCategory.categoryId;
  
  const result = await api.getCategoryProducts({
    categoryId,  // 确保传递正确的分类ID
    page,
    pageSize: this.data.pageSize,
    include_children: !currentCategory.isSubCategory
  });
}
```

#### 2.4 分类切换修复
```javascript
async switchToCategory(categoryIndex, category) {
  // 严格检查分类ID
  if (!category?.categoryId) {
    logger.error('❌ 分类ID无效', category);
    return;
  }
  
  // 统一使用categoryId
  const currentCategory = {
    ...category,
    categoryId: category.categoryId  // 不再使用fallback
  };
}
```

### 3. 调试日志增强
增加了详细的日志输出，包括：
- 分类加载过程中的ID映射
- API调用时的参数详情
- 分类切换时的数据结构
- 错误情况的详细信息

## 测试验证

### 测试场景
1. **直接访问分类页**：应该显示默认分类的商品
2. **从其他页面跳转**：`/pages/category/category?categoryId=123` 应该正确显示指定分类
3. **主分类切换**：点击主分类应该显示该分类下的所有商品
4. **子分类切换**：点击子分类应该只显示该子分类的商品
5. **分类展开/收起**：不应该影响当前显示的商品数据

### 验证方法
1. 查看控制台日志，确认传递给API的`categoryId`正确
2. 检查网络请求，确认`category_id`参数正确
3. 验证商品数据与选中分类匹配

## 关键修复点

1. **数据结构统一**：明确区分UI标识(`id`)和业务标识(`categoryId`)
2. **严格校验**：在所有关键操作前检查`categoryId`是否有效
3. **页面参数支持**：正确处理从URL传入的分类ID
4. **错误处理**：当分类ID无效时给出明确提示
5. **日志完善**：添加详细的调试信息便于问题排查

## 预期效果
修复后，分类页面应该：
- 正确显示每个分类对应的商品
- 支持通过URL参数指定初始分类
- 分类切换响应迅速且数据准确
- 主分类和子分类切换逻辑清晰 