<?php

namespace App\Supplier\Models;

use App\Purchase\Models\PurchaseOrder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Supplier extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'contact_person',
        'contact_phone',
        'email',
        'address',
        'credit_limit',
        'current_debt',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'credit_limit' => 'decimal:2',
        'current_debt' => 'decimal:2',
    ];

    /**
     * 获取供应商的采购订单
     */
    public function purchaseOrders()
    {
        return $this->hasMany(PurchaseOrder::class);
    }

    /**
     * 获取可用信用额度
     */
    public function getAvailableCreditAttribute()
    {
        return $this->credit_limit - $this->current_debt;
    }
} 