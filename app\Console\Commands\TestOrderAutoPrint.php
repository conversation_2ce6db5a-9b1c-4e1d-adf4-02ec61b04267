<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Order\Models\Order;
use App\Order\Models\OrderItem;
use App\Product\Models\Product;
use App\FlyCloud\Services\FlyCloudService;
use App\FlyCloud\Models\FlyCloudPrinter;
use App\FlyCloud\Models\WarehousePrinterBinding;
use App\Warehouse\Models\Warehouse;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class TestOrderAutoPrint extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:test-auto-print {--create : 创建测试订单} {--order_id= : 测试指定订单}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试订单自动分单打印完整流程';

    protected FlyCloudService $flyCloudService;

    public function __construct(FlyCloudService $flyCloudService)
    {
        parent::__construct();
        $this->flyCloudService = $flyCloudService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 开始测试订单自动分单打印系统...');
        
        // 检查系统配置
        $this->checkSystemConfiguration();
        
        if ($this->option('create')) {
            $this->createTestOrder();
        } elseif ($this->option('order_id')) {
            $this->testExistingOrder($this->option('order_id'));
        } else {
            $this->testLatestOrder();
        }
    }

    protected function checkSystemConfiguration()
    {
        $this->info("\n📋 检查系统配置...");
        
        // 检查自动打印配置
        $autoPrintEnabled = config('flycloud.auto_print_enabled', false);
        $this->info("自动打印状态: " . ($autoPrintEnabled ? '✅ 已启用' : '❌ 已禁用'));
        
        // 检查触发配置
        $triggers = config('flycloud.auto_print_triggers', []);
        $this->info("触发时机: " . json_encode($triggers));
        
        // 检查分配策略
        $strategy = config('flycloud.warehouse_assignment.assignment_strategy', 'product');
        $this->info("仓库分配策略: {$strategy}");
        
        // 检查默认仓库
        $defaultWarehouse = config('flycloud.warehouse_assignment.default_warehouse_id');
        $this->info("默认仓库ID: {$defaultWarehouse}");
        
        // 检查打印机数量
        $printerCount = FlyCloudPrinter::active()->count();
        $this->info("活跃打印机数量: {$printerCount}");
        
        // 检查绑定关系
        $bindingCount = WarehousePrinterBinding::active()->count();
        $this->info("仓库打印机绑定数量: {$bindingCount}");
        
        if ($printerCount === 0) {
            $this->warn("⚠️  没有配置飞蛾云打印机，请先添加打印机");
        }
        
        if ($bindingCount === 0) {
            $this->warn("⚠️  没有配置仓库打印机绑定关系，可能会使用全局默认打印机");
        }
    }

    protected function createTestOrder()
    {
        $this->info("\n🛒 创建测试订单...");
        
        try {
            DB::beginTransaction();
            
            // 获取测试用户
            $user = User::first();
            if (!$user) {
                $this->error("没有找到用户，请先创建用户");
                return;
            }
            
            // 获取测试商品
            $products = Product::take(3)->get();
            if ($products->isEmpty()) {
                $this->error("没有找到商品，请先创建商品");
                return;
            }
            
            // 创建订单
            $order = Order::create([
                'user_id' => $user->id,
                'order_no' => Order::generateOrderNo(),
                'status' => 'pending',
                'total' => 0, // 稍后计算
                'subtotal' => 0,
                'shipping_address' => '测试地址，XX市XX区XX街道123号',
                'contact_name' => '测试联系人',
                'contact_phone' => '13800138000',
                'payment_method' => 'wechat',
                'notes' => '这是一个自动打印测试订单'
            ]);
            
            $total = 0;
            
            // 创建订单项
            foreach ($products as $product) {
                $quantity = rand(1, 3);
                $price = $product->price ?? 10.00;
                $itemTotal = $price * $quantity;
                $total += $itemTotal;
                
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku ?? 'SKU-' . $product->id,
                    'quantity' => $quantity,
                    'price' => $price,
                    'unit' => $product->getSaleDefaultUnit()?->name ?? '',
                    'total' => $itemTotal
                ]);
            }
            
            // 更新订单总金额
            $order->update(['total' => $total, 'subtotal' => $total]);
            
            DB::commit();
            
            $this->info("✅ 测试订单创建成功！");
            $this->info("订单ID: {$order->id}");
            $this->info("订单号: {$order->order_no}");
            $this->info("商品数量: " . $products->count());
            $this->info("订单总额: ¥{$total}");
            
            // 测试订单
            $this->testOrderPrint($order);
            
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("创建测试订单失败: " . $e->getMessage());
        }
    }

    protected function testExistingOrder($orderId)
    {
        $this->info("\n🔍 测试现有订单 #{$orderId}...");
        
        try {
            $order = Order::with(['items.product'])->findOrFail($orderId);
            $this->testOrderPrint($order);
        } catch (\Exception $e) {
            $this->error("找不到订单 #{$orderId}: " . $e->getMessage());
        }
    }

    protected function testLatestOrder()
    {
        $this->info("\n🔍 测试最新订单...");
        
        $order = Order::with(['items.product'])
            ->whereNotNull('order_no')
            ->orderBy('created_at', 'desc')
            ->first();
            
        if (!$order) {
            $this->warn("没有找到订单，创建一个测试订单？");
            if ($this->confirm('是否创建测试订单？')) {
                $this->createTestOrder();
            }
            return;
        }
        
        $this->testOrderPrint($order);
    }

    protected function testOrderPrint(Order $order)
    {
        $this->info("\n📄 测试订单打印流程...");
        
        // 重新加载订单以确保包含所有关联数据
        $order = Order::with(['items.product', 'user'])->find($order->id);
        
        if (!$order) {
            $this->error("无法加载订单数据");
            return;
        }
        
        $this->info("订单ID: {$order->id}");
        $this->info("订单号: {$order->order_no}");
        $this->info("商品数量: " . $order->items->count());
        
        // 显示订单详细信息
        $this->info("\n👤 订单详细信息:");
        if ($order->user) {
            $customerName = $order->user->name ?? $order->user->nickname ?? '未知用户';
            $this->info("下单人: {$customerName}");
            if ($order->user->phone) {
                $this->info("下单手机: {$order->user->phone}");
            }
        }
        $this->info("下单时间: " . ($order->created_at ? $order->created_at->format('Y-m-d H:i:s') : '未知'));
        if ($order->payment_method) {
            $this->info("支付方式: {$order->payment_method}");
        }
        $this->info("收货人: " . ($order->contact_name ?? '未设置'));
        $this->info("收货电话: " . ($order->contact_phone ?? '未设置'));
        $this->info("收货地址: " . ($order->shipping_address ?? '未设置'));
        if ($order->notes) {
            $this->info("订单备注: {$order->notes}");
        }
        
        if ($order->items->isEmpty()) {
            $this->warn("❌ 订单没有商品项");
            return;
        }
        
        // 1. 检查仓库分配
        $this->info("\n🏪 检查仓库分配...");
        $warehouseIds = $this->flyCloudService->getOrderWarehouses($order);
        
        if (empty($warehouseIds)) {
            $this->warn("❌ 订单商品无仓库分配");
            
            // 详细分析每个商品的仓库分配情况
            $this->info("\n🔍 商品仓库分配详情:");
            foreach ($order->items as $item) {
                $warehouseId = $this->getItemWarehouseId($item);
                $productName = $item->product_name ?? ($item->product->name ?? '未知商品');
                $this->info("  - {$productName}: 仓库 " . ($warehouseId ?? '未分配'));
            }
            return;
        }
        
        $this->info("涉及仓库: " . implode(', ', $warehouseIds));
        
        // 2. 检查每个仓库的商品分组和小票内容预览
        foreach ($warehouseIds as $warehouseId) {
            $this->info("\n📦 仓库 {$warehouseId} 商品分析:");
            
            $warehouseItems = [];
            foreach ($order->items as $item) {
                if ($this->getItemWarehouseId($item) === $warehouseId) {
                    $warehouseItems[] = $item;
                    $unit = $this->getItemUnit($item);
                    $unitDisplay = $unit ? "({$unit})" : '';
                    $this->info("  - {$item->product_name} x{$item->quantity}{$unitDisplay} (¥{$item->price})");
                }
            }
            
            // 显示生成的小票内容预览
            if (!empty($warehouseItems)) {
                $this->info("\n📄 仓库 {$warehouseId} 小票内容预览:");
                $content = $this->generateWarehouseReceiptPreview($order, $warehouseItems, $warehouseId);
                $this->line($content);
            }
            
            // 检查打印机绑定
            $bindings = $this->flyCloudService->getWarehousePrinterBindings($warehouseId);
            if (empty($bindings)) {
                $this->warn("  ⚠️  仓库 {$warehouseId} 无绑定打印机，将跳过打印");
            } else {
                foreach ($bindings as $binding) {
                    $status = $binding['printer']['status'] ?? 'unknown';
                    $icon = $status === 'online' ? '✅' : '❌';
                    $this->info("  {$icon} 打印机: {$binding['printer']['name']} ({$status})");
                }
            }
        }
        
        // 3. 执行分单打印测试
        $this->info("\n🖨️  执行分单打印测试...");
        
        try {
            $results = $this->flyCloudService->printOrderByWarehouses($order, [
                'print_type' => 'order',
                'copies' => 1,
                'test_mode' => true
            ]);
            
            $this->info("\n📊 打印结果统计:");
            $successCount = 0;
            $failCount = 0;
            $skippedCount = 0;
            
            // 检查哪些仓库有绑定打印机
            $allWarehouses = $this->flyCloudService->getOrderWarehouses($order);
            $processedWarehouses = array_keys($results);
            $skippedWarehouses = array_diff($allWarehouses, $processedWarehouses);
            
            foreach ($results as $warehouseId => $result) {
                $success = $result['success'] ?? false;
                $message = $result['message'] ?? '未知状态';
                $icon = $success ? '✅' : '❌';
                
                $this->info("  {$icon} 仓库 {$warehouseId}: {$message}");
                
                if ($success) {
                    $successCount++;
                    if (isset($result['printer_sn'])) {
                        $this->info("    📡 打印机: {$result['printer_sn']}");
                    }
                    if (isset($result['items_count'])) {
                        $this->info("    📦 商品数: {$result['items_count']}");
                    }
                } else {
                    $failCount++;
                }
            }
            
            // 显示跳过的仓库
            foreach ($skippedWarehouses as $warehouseId) {
                $this->info("  ⏭️  仓库 {$warehouseId}: 跳过（无打印机绑定）");
                $skippedCount++;
            }
            
            $this->info("\n📈 总结:");
            $this->info("总仓库数: " . count($allWarehouses));
            $this->info("处理打印: {$successCount}");
            $this->info("打印失败: {$failCount}");
            $this->info("跳过仓库: {$skippedCount}");
            
            if ($failCount === 0 && $successCount > 0) {
                $this->info("🎉 所有有绑定打印机的仓库分单打印成功！");
            } elseif ($successCount === 0 && $skippedCount > 0) {
                $this->warn("⚠️  所有仓库都没有绑定打印机，已全部跳过");
            } elseif ($failCount > 0) {
                $this->warn("⚠️  部分仓库打印失败，请检查打印机状态");
            }
            
        } catch (\Exception $e) {
            $this->error("分单打印测试失败: " . $e->getMessage());
        }
    }

    /**
     * 生成仓库小票内容预览（用于测试命令显示）
     */
    protected function generateWarehouseReceiptPreview($order, array $items, int $warehouseId): string
    {
        $preview = "";
        $preview .= "        万家生鲜\n";
        $preview .= "        仓库分单\n";
        $preview .= "================================\n";
        $preview .= "订单号: " . ($order->order_no ?? '') . "\n";
        $preview .= "仓库ID: " . $warehouseId . "\n";
        $preview .= "分单时间: " . now()->format('Y-m-d H:i:s') . "\n";
        $preview .= "下单时间: " . ($order->created_at ? $order->created_at->format('Y-m-d H:i:s') : '') . "\n";
        
        // 下单人信息
        if ($order->user) {
            $customerName = $order->user->name ?? $order->user->nickname ?? null;
            if ($customerName) {
                $preview .= "下单人: " . $customerName . "\n";
            }
            if ($order->user->phone) {
                $preview .= "下单手机: " . $order->user->phone . "\n";
            }
        }
        
        // 支付信息
        if ($order->payment_method) {
            $preview .= "支付方式: " . $order->payment_method . "\n";
        }
        
        $preview .= "--------------------------------\n";
        
        // 商品列表
        $warehouseTotal = 0;
        foreach ($items as $item) {
            $productName = $item->product_name ?? ($item->product->name ?? '商品');
            $price = $item->price ?? 0;
            $quantity = $item->quantity ?? 1;
            $subtotal = $price * $quantity;
            $warehouseTotal += $subtotal;
            
            $unit = $this->getItemUnit($item);
            $unitDisplay = $unit ? "({$unit})" : '';
            
            $preview .= $productName . "\n";
            $preview .= "数量: " . $quantity . $unitDisplay . " x ¥" . number_format($price, 2) . "\n";
            $preview .= "                      小计: ¥" . number_format($subtotal, 2) . "\n\n";
        }
        
        $preview .= "--------------------------------\n";
        $preview .= "商品数量: " . count($items) . "\n";
        $preview .= "                  仓库小计: ¥" . number_format($warehouseTotal, 2) . "\n";
        $preview .= "--------------------------------\n";
        $preview .= "收货信息\n";
        $preview .= "收货人: " . ($order->contact_name ?? '') . "\n";
        $preview .= "联系电话: " . ($order->contact_phone ?? '') . "\n";
        $preview .= "收货地址: " . ($order->shipping_address ?? '') . "\n";
        
        if ($order->notes) {
            $preview .= "订单备注: " . $order->notes . "\n";
        }
        
        $preview .= "================================\n";
        $preview .= "        请按此单拣货\n";
        $preview .= "    打印时间: " . now()->format('Y-m-d H:i:s') . "\n";
        
        return $preview;
    }

    /**
     * 获取商品的仓库ID（复制自FlyCloudService逻辑）
     */
    protected function getItemWarehouseId($item): ?int
    {
        $strategy = config('flycloud.warehouse_assignment.assignment_strategy', 'product');
        
        if (isset($item->warehouse_id) && $item->warehouse_id) {
            return $item->warehouse_id;
        }
        
        switch ($strategy) {
            case 'inventory':
                return $this->getWarehouseByInventory($item);
            case 'region':
                return $this->getWarehouseByRegion($item);
            case 'product':
            default:
                return $this->getWarehouseByProduct($item);
        }
    }
    
    protected function getWarehouseByProduct($item): ?int
    {
        if ($item->product && isset($item->product->warehouse_id) && $item->product->warehouse_id) {
            return $item->product->warehouse_id;
        }
        
        return config('flycloud.warehouse_assignment.default_warehouse_id', 1);
    }
    
    protected function getWarehouseByInventory($item): ?int
    {
        // 简化版本，直接回退到商品默认仓库
        return $this->getWarehouseByProduct($item);
    }
    
    protected function getWarehouseByRegion($item): ?int
    {
        // 简化版本，直接回退到库存策略
        return $this->getWarehouseByInventory($item);
    }

    protected function getItemUnit($item)
    {
        try {
            // 1. 优先使用订单项的单位信息
            if (isset($item->unit_name) && $item->unit_name) {
                return $item->unit_name;
            }
            
            if (isset($item->unit_symbol) && $item->unit_symbol) {
                return $item->unit_symbol;
            }
            
            // 2. 从关联的单位模型获取
            if ($item->unit) {
                return $item->unit->symbol ?? $item->unit->name ?? null;
            }
            
            // 3. 从商品获取基本单位
            if ($item->product && $item->product->baseUnit) {
                return $item->product->baseUnit->symbol ?? $item->product->baseUnit->name ?? null;
            }
            
            // 4. 通过unit_id加载单位信息
            if ($item->unit_id) {
                $unit = \App\Unit\Models\Unit::find($item->unit_id);
                if ($unit) {
                    return $unit->symbol ?? $unit->name ?? null;
                }
            }
            
            return null;
        } catch (\Exception $e) {
            return null;
        }
    }
}
