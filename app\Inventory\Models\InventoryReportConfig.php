<?php

namespace App\Inventory\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryReportConfig extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'name',
        'code',
        'type',
        'description',
        'config',
        'filters',
        'columns',
        'charts',
        'access_level',
        'allowed_roles',
        'is_scheduled',
        'schedule_cron',
        'auto_email',
        'email_recipients',
        'is_active',
        'created_by',
    ];

    /**
     * 应该被转换的属性
     */
    protected $casts = [
        'config' => 'array',
        'filters' => 'array',
        'columns' => 'array',
        'charts' => 'array',
        'allowed_roles' => 'array',
        'email_recipients' => 'array',
        'is_scheduled' => 'boolean',
        'auto_email' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * 获取创建者
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取报表实例
     */
    public function instances()
    {
        return $this->hasMany(InventoryReportInstance::class, 'config_id');
    }

    /**
     * 获取最新的报表实例
     */
    public function latestInstance()
    {
        return $this->hasOne(InventoryReportInstance::class, 'config_id')->latest();
    }

    /**
     * 获取报表类型文本
     * 
     * @return string
     */
    public function getTypeTextAttribute()
    {
        return match($this->type) {
            'status' => '库存状态报表',
            'movement' => '库存变动报表',
            'analysis' => '库存分析报表',
            'forecast' => '库存预测报表',
            default => '未知类型'
        };
    }

    /**
     * 获取访问级别文本
     * 
     * @return string
     */
    public function getAccessLevelTextAttribute()
    {
        return match($this->access_level) {
            'public' => '公开',
            'internal' => '内部',
            'restricted' => '受限',
            default => '未知'
        };
    }

    /**
     * 检查用户是否有权限访问此报表
     * 
     * @param User $user
     * @return bool
     */
    public function canAccess(User $user)
    {
        if (!$this->is_active) {
            return false;
        }

        switch ($this->access_level) {
            case 'public':
                return true;

            case 'internal':
                return true; // 所有内部用户都可以访问

            case 'restricted':
                if (!$this->allowed_roles) {
                    return false;
                }
                
                // 检查用户角色是否在允许列表中
                $userRoles = $user->roles->pluck('name')->toArray();
                return !empty(array_intersect($userRoles, $this->allowed_roles));

            default:
                return false;
        }
    }

    /**
     * 作用域：活跃的配置
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按类型过滤
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 作用域：定时报表
     */
    public function scopeScheduled($query)
    {
        return $query->where('is_scheduled', true);
    }

    /**
     * 作用域：用户可访问的报表
     */
    public function scopeAccessibleBy($query, User $user)
    {
        return $query->where(function($q) use ($user) {
            $q->where('access_level', 'public')
              ->orWhere('access_level', 'internal')
              ->orWhere(function($subQ) use ($user) {
                  $subQ->where('access_level', 'restricted');
                  
                  if ($user->roles->isNotEmpty()) {
                      $userRoles = $user->roles->pluck('name')->toArray();
                      $subQ->where(function($roleQ) use ($userRoles) {
                          foreach ($userRoles as $role) {
                              $roleQ->orWhereJsonContains('allowed_roles', $role);
                          }
                      });
                  }
              });
        });
    }
} 