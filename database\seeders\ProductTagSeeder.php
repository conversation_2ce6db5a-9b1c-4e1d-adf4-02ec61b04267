<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ProductTagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();
        
        $tags = [
            [
                'name' => '热销',
                'slug' => 'hot',
                'color' => '#ffffff',
                'background_color' => '#ff4444',
                'icon' => 'fire',
                'description' => '热销商品标签',
                'sort_order' => 1,
                'is_active' => true,
                'show_in_filter' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => '新品',
                'slug' => 'new',
                'color' => '#ffffff',
                'background_color' => '#00aa44',
                'icon' => 'new',
                'description' => '新品商品标签',
                'sort_order' => 2,
                'is_active' => true,
                'show_in_filter' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => '推荐',
                'slug' => 'recommend',
                'color' => '#ffffff',
                'background_color' => '#4CAF50',
                'icon' => 'like',
                'description' => '推荐商品标签',
                'sort_order' => 3,
                'is_active' => true,
                'show_in_filter' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => '特价',
                'slug' => 'promotion',
                'color' => '#ffffff',
                'background_color' => '#ff9800',
                'icon' => 'discount',
                'description' => '特价商品标签',
                'sort_order' => 4,
                'is_active' => true,
                'show_in_filter' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        // 插入标签数据，如果已存在则跳过
        foreach ($tags as $tag) {
            DB::table('product_tags')->updateOrInsert(
                ['slug' => $tag['slug']],
                $tag
            );
        }
    }
} 