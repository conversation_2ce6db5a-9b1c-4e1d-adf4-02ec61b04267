<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Points\Models\PointsRule;

// 引导Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "检查积分规则表...\n";

try {
    // 检查是否存在签到规则
    $signinRule = PointsRule::where('rule_type', 'signin')->first();
    
    if ($signinRule) {
        echo "✅ 签到规则已存在: {$signinRule->name} ({$signinRule->points_amount}积分)\n";
    } else {
        echo "❌ 签到规则不存在，正在创建...\n";
        
        // 创建签到规则
        $rule = PointsRule::create([
            'name' => '每日签到',
            'rule_type' => 'signin',
            'points_amount' => 10,
            'max_times_per_day' => 1,
            'max_times_total' => null,
            'status' => true,
            'description' => '每日签到获得10积分',
            'conditions' => null,
        ]);
        
        echo "✅ 签到规则创建成功: {$rule->name} ({$rule->points_amount}积分)\n";
    }
    
    // 检查所有积分规则
    $allRules = PointsRule::all();
    echo "\n📋 当前所有积分规则:\n";
    foreach ($allRules as $rule) {
        $status = $rule->status ? '启用' : '禁用';
        echo "  - {$rule->name} ({$rule->rule_type}): {$rule->points_amount}积分 [{$status}]\n";
    }
    
    // 测试签到规则查询
    echo "\n🔍 测试签到规则查询:\n";
    $validSigninRules = PointsRule::valid()->byType('signin')->get();
    echo "  有效签到规则数量: " . $validSigninRules->count() . "\n";
    
    foreach ($validSigninRules as $rule) {
        echo "  - {$rule->name}: {$rule->points_amount}积分\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n✅ 检查完成\n"; 