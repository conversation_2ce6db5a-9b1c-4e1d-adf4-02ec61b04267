<!-- 万象生鲜商品卡片组件 - 根据设计图重新构建 -->
<view class="product-card {{cardClassNames}} {{customClass}} {{product.out_of_stock ? 'out-of-stock' : ''}}" bindtap="onProductTap" data-product="{{product}}">
  
  <!-- 商品图片区域 -->
  <view class="product-image-wrapper">
    <lazy-image
      src="{{product.image || ''}}"
      mode="aspectFill"
      width="100%"
      height="{{imageHeight || '240rpx'}}"
      custom-class="product-image"
      bind:load="onImageLoad"
      bind:error="onImageError"
      bind:tap="onImageTap"
    />
    
    <!-- 缺货遮罩 -->
    <view class="out-of-stock-overlay" wx:if="{{product.out_of_stock}}">
      <view class="out-of-stock-text">缺货</view>
    </view>
    
    <!-- 商品标签 - 左上角显示 -->
    <view class="product-tags-container" wx:if="{{product.tags && product.tags.length > 0}}">
      <van-tag 
        wx:for="{{product.tags}}" 
        wx:key="index"
        wx:for-item="tag"
        wx:for-index="tagIndex"
        type="warning"
        size="small"
        round
        custom-class="product-tag {{tag.type ? 'product-tag-' + tag.type : 'product-tag-default'}}"
      >
        {{tag.name || tag}}
      </van-tag>
    </view>
  </view>
  
  <!-- 商品信息区域 -->
  <view class="product-info">
    <!-- 商品标签 - 在商品信息区域显示 -->
    <view class="product-tags-container-info" wx:if="{{product.tags && product.tags.length > 0 && showTags}}">
      <van-tag 
        wx:for="{{product.tags}}" 
        wx:key="index"
        wx:for-item="tag"
        wx:for-index="tagIndex"
        type="warning"
        size="small"
        round
        custom-class="product-tag {{tag.type ? 'product-tag-' + tag.type : 'product-tag-default'}}"
      >
        {{tag.name || tag}}
      </van-tag>
    </view>
    
    <!-- 商品标题 -->
    <view class="product-title">{{product.name}}</view>
    
    <!-- 商品副标题 -->
    <view class="product-subtitle" wx:if="{{product.subtitle}}">
      {{product.subtitle}}
    </view>
    
    <!-- 价格和操作区域 -->
    <view class="product-footer">
      <!-- 左侧价格区域 -->
      <view class="price-section">
        <price-display
          productId="{{product.id || 0}}"
          quantity="{{1}}"
          price="{{product.price || '0.00'}}"
          unit="{{product.unit || ''}}"
          className="product-price"
          loginText="登录查看"
          showLoginIcon="{{false}}"
          showPriceLabels="{{true}}"
          showDiscountInfo="{{true}}"
          showMemberUpgradeTip="{{false}}"
          forceShowPrice="{{false}}"
          bind:priceLoaded="onPriceLoaded"
          bind:priceError="onPriceError"
        />

        <!-- 最小起购数量标记 -->
        <view class="min-quantity-badge" wx:if="{{product.min_sale_quantity && product.min_sale_quantity > 1}}">
          最少{{product.min_sale_quantity}}{{product.unit || '件'}}
        </view>
        <view class="supplier-info" wx:if="{{product.supplier}}">{{product.supplier}}</view>
      </view>
      
      <!-- 右侧购物车控制区域 -->
      <view class="cart-control-section" wx:if="{{showAddCart}}">
        
        <!-- 缺货状态显示 -->
        <view wx:if="{{product.out_of_stock}}" class="out-of-stock-button">
          缺货
        </view>
        
        <!-- 当购物车中没有该商品时，显示加购按钮 -->
        <view 
          wx:elif="{{cartQuantity === 0}}"
          class="add-cart-button"
          catchtap="onAddToCart"
          bindlongpress="onShowNumberKeyboard"
          data-product="{{product}}"
        >
          <van-icon name="plus" size="32rpx" color="#fff" />
        </view>
        
        <!-- 当购物车中有该商品时，显示数量控制器 -->
        <view wx:else class="quantity-controller">
          <!-- 减少按钮 -->
          <view 
            class="quantity-btn decrease-btn"
            catchtap="onDecreaseQuantity"
            data-product="{{product}}"
          >
            <van-icon name="minus" size="24rpx" color="#666" />
          </view>
          
          <!-- 数量输入 -->
          <input
            class="quantity-input"
            type="number"
            value="{{cartQuantity}}"
            bindblur="onQuantityInput"
            bindfocus="onQuantityFocus"
            data-product="{{product}}"
            maxlength="3"
            placeholder="{{cartQuantity}}"
          />
          
          <!-- 增加按钮 -->
          <view 
            class="quantity-btn increase-btn"
            catchtap="onIncreaseQuantity"
            data-product="{{product}}"
          >
            <van-icon name="plus" size="24rpx" color="#fff" />
          </view>
        </view>
        
      </view>
    </view>
  </view>
  
</view>

<!-- 数字键盘组件 -->
<number-keyboard
  wx:if="{{enableNumberKeyboard}}"
  show="{{showNumberKeyboard}}"
  product="{{currentProduct}}"
  default-quantity="{{cartQuantity || minQuantity || 1}}"
  max-quantity="{{maxQuantity || 999}}"
  min-quantity="{{minQuantity || 1}}"
  bind:confirm="onNumberKeyboardConfirm"
  bind:hide="onNumberKeyboardHide"
  bind:show="onNumberKeyboardShow"
/> 