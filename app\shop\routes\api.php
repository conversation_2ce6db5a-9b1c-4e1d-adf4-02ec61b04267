<?php

use Illuminate\Support\Facades\Route;
use App\shop\Http\Controllers\SmsController;
use App\shop\Http\Controllers\SmsConfigController;
use App\shop\Http\Controllers\AuthController;
use App\shop\Http\Controllers\BannerController;
use App\shop\Http\Controllers\PublicController;

/*
|--------------------------------------------------------------------------
| Shop模块 API 路由
|--------------------------------------------------------------------------
|
| 这里定义所有Shop模块的API路由
| 注意：区域相关的API已移至Region模块，路径为 /api/regions/*
|
*/

// 将所有Shop模块的路由放在api/shop前缀下
Route::prefix('api/shop')->group(function () {
    // 身份验证API
    Route::prefix('auth')->group(function () {
        // 用户注册
        Route::post('/register', [AuthController::class, 'register'])->name('api.shop.auth.register');
        // 用户登录
        Route::post('/login', [AuthController::class, 'login'])->name('api.shop.auth.login');
        
        // 需要认证的路由
        Route::middleware('auth:sanctum')->group(function () {
            // 用户登出
            Route::post('/logout', [AuthController::class, 'logout'])->name('api.shop.auth.logout');
            // 获取当前用户信息
            Route::get('/user', [AuthController::class, 'user'])->name('api.shop.auth.user');
        });
    });

    // 前端API - 短信验证
    Route::prefix('sms')->group(function () {
        // 获取短信验证状态
        Route::get('/status', [SmsController::class, 'getStatus'])->name('api.shop.sms.status');
        // 发送短信验证码
        Route::post('/send', [SmsController::class, 'send'])->name('api.shop.sms.send');
        // 验证短信验证码
        Route::post('/verify', [SmsController::class, 'verify'])->name('api.shop.sms.verify');
    });

    // 管理后台API - 短信配置
    Route::prefix('admin/sms')->middleware(['auth:sanctum', 'employee.role:admin,manager'])->group(function () {
        // 获取短信配置
        Route::get('/config', [SmsConfigController::class, 'getConfig'])->name('api.shop.admin.sms.config.get');
        // 保存短信配置
        Route::post('/config', [SmsConfigController::class, 'saveConfig'])->name('api.shop.admin.sms.config.save');
        // 发送测试短信
        Route::post('/test-send', [SmsController::class, 'testSend'])->name('api.shop.admin.sms.test');
    });

    // 轮播图管理
    Route::prefix('banners')->group(function () {
        // 获取前端显示的轮播图
        Route::get('/', [BannerController::class, 'index'])->name('api.shop.banners.index');
        
        // 管理后台相关路由
        Route::middleware(['auth:sanctum', 'employee.role:admin,manager'])->group(function () {
            // 获取所有轮播图
            Route::get('/all', [BannerController::class, 'all'])->name('api.shop.banners.all');
            
            // 创建轮播图
            Route::post('/', [BannerController::class, 'store'])->name('api.shop.banners.store');
            
            // 获取轮播图详情
            Route::get('/{id}', [BannerController::class, 'show'])->name('api.shop.banners.show');
            
            // 更新轮播图
            Route::put('/{id}', [BannerController::class, 'update'])->name('api.shop.banners.update');
            
            // 删除轮播图
            Route::delete('/{id}', [BannerController::class, 'destroy'])->name('api.shop.banners.destroy');
            
            // 更新轮播图状态
            Route::put('/{id}/status', [BannerController::class, 'updateStatus'])->name('api.shop.banners.updateStatus');
        });
    });
    
    // 注意：区域管理API已移至Region模块
    // 新的API路径为：/api/regions/*
    // 如需访问区域相关功能，请使用Region模块的API
    
    // 公共API
    Route::prefix('public')->group(function () {
        // 获取商城基本信息
        Route::get('/info', [PublicController::class, 'getShopInfo'])->name('api.shop.public.info');
        
        // 获取商城配置
        Route::get('/config', [PublicController::class, 'getShopConfig'])->name('api.shop.public.config');
        
        // 获取支付方式
        Route::get('/payment-methods', [PublicController::class, 'getPaymentMethods'])->name('api.shop.public.payment_methods');
        
        // 获取轮播图 - 公开API，无需认证
        Route::get('/banners', [PublicController::class, 'banners'])->name('api.shop.public.banners');
    });
});