<?php

use App\Supplier\Http\Controllers\SupplierController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'suppliers', 'middleware' => ['web', 'auth']], function () {
    Route::get('/', [SupplierController::class, 'index'])->name('suppliers.index');
    Route::post('/', [SupplierController::class, 'store'])->name('suppliers.store');
    Route::get('/{id}', [SupplierController::class, 'show'])->name('suppliers.show');
    Route::put('/{id}', [SupplierController::class, 'update'])->name('suppliers.update');
    Route::delete('/{id}', [SupplierController::class, 'destroy'])->name('suppliers.destroy');
}); 