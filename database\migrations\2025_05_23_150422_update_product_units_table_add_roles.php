<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 第一步：添加新列
        Schema::table('product_units', function (Blueprint $table) {
            // 检查并添加可能缺失的列
            if (!Schema::hasColumn('product_units', 'roles')) {
                $table->json('roles')->nullable()->comment('单位角色(销售/采购/库存等)');
            }
            
            if (!Schema::hasColumn('product_units', 'role_priority')) {
                $table->json('role_priority')->nullable()->comment('角色优先级');
            }
            
            if (!Schema::hasColumn('product_units', 'is_active')) {
                $table->boolean('is_active')->default(true)->comment('是否启用');
            }
            
            // 修改列名
            if (Schema::hasColumn('product_units', 'conversion_rate') && !Schema::hasColumn('product_units', 'conversion_factor')) {
                $table->renameColumn('conversion_rate', 'conversion_factor');
            }
        });
        
        // 第二步：迁移旧的单位标识到新的角色系统
        $this->migrateOldUnitRoles();
    }

    /**
     * 将旧的单位角色标识迁移到新的角色系统
     */
    private function migrateOldUnitRoles()
    {
        // 检查是否有旧的角色字段
        if (Schema::hasColumn('product_units', 'is_sale_default') || 
            Schema::hasColumn('product_units', 'is_purchase_default') || 
            Schema::hasColumn('product_units', 'is_inventory_default')) {
            
            // 获取所有产品单位
            $productUnits = DB::table('product_units')->get();
            
            foreach ($productUnits as $unit) {
                $roles = [];
                $rolePriority = [];
                
                // 检查并添加销售角色
                if (Schema::hasColumn('product_units', 'is_sale_default') && $unit->is_sale_default) {
                    $roles[] = 'sale';
                    $rolePriority['sale'] = 10;
                }
                
                // 检查并添加采购角色
                if (Schema::hasColumn('product_units', 'is_purchase_default') && $unit->is_purchase_default) {
                    $roles[] = 'purchase';
                    $rolePriority['purchase'] = 20;
                }
                
                // 检查并添加库存角色
                if (Schema::hasColumn('product_units', 'is_inventory_default') && $unit->is_inventory_default) {
                    $roles[] = 'inventory';
                    $rolePriority['inventory'] = 30;
                }
                
                // 更新记录
                if (!empty($roles)) {
                    DB::table('product_units')
                        ->where('id', $unit->id)
                        ->update([
                            'roles' => json_encode($roles),
                            'role_priority' => json_encode($rolePriority)
                        ]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_units', function (Blueprint $table) {
            // 我们不会撤销这些更改，因为我们不希望丢失数据
        });
    }
};
