/**
 * 环境变量工具函数
 */

// 判断当前环境是否为开发环境
export const isDev = process.env.NODE_ENV === 'development';

// 判断当前环境是否为生产环境
export const isProd = process.env.NODE_ENV === 'production';

// 判断当前环境是否为测试环境
export const isTest = process.env.NODE_ENV === 'test';

// 获取当前环境的API基础URL
export const getBaseUrl = () => {
  if (isDev) {
    return process.env.VUE_APP_API_BASE_URL || '/api';
  }
  return process.env.VUE_APP_API_BASE_URL || '';
};

// 获取当前环境
export const getEnv = () => process.env.NODE_ENV; 