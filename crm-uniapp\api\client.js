import request from '../utils/request.js'

// 客户相关API
export default {
	// 获取客户列表
	getClientList(params = {}) {
		return request.get('/crm/users', params)
	},
	
	// 获取客户详情
	getClientDetail(clientId) {
		return request.get(`/crm/users/${clientId}`)
	},
	
	// 获取客户统计信息
	getClientStats(clientId) {
		return request.get(`/crm/users/${clientId}/statistics`)
	},
	
	// 获取客户订单列表
	getClientOrders(clientId, params = {}) {
		return request.get(`/crm/users/${clientId}/orders`, { params })
	},
	
	// 获取客户地址列表
	getClientAddresses(clientId) {
		return request.get(`/crm/users/${clientId}/addresses`)
	},
	
	// 获取客户跟进记录
	getClientFollowUps(clientId) {
		return request.get(`/crm/users/${clientId}/follow-ups`)
	},
	
	// 创建客户
	createClient(data) {
		return request.post('/crm/users', data)
	},
	
	// 更新客户信息
	updateClient(clientId, data) {
		return request.put(`/crm/users/${clientId}`, data)
	},
	
	// 删除客户
	deleteClient(clientId) {
		return request.delete(`/crm/users/${clientId}`)
	},
	
	// 搜索客户
	searchClients(keyword) {
		return request.get('/crm/users/search', { 
			params: { keyword } 
		})
	},
	
	// 根据手机号查找客户
	findByPhone(phone) {
		return request.get(`/crm/users/by-phone/${phone}`)
	},
	
	// 分配客户给CRM专员
	assignAgent(clientId, agentId) {
		return request.post(`/crm/users/${clientId}/assign-agent`, { agent_id: agentId })
	},
	
	// 更新客户状态
	updateClientStatus(clientId, status) {
		return request.put(`/crm/users/${clientId}/status`, { status })
	},
	
	// 更新客户余额
	updateClientBalance(clientId, balance) {
		return request.put(`/crm/users/${clientId}/balance`, { balance })
	},
	
	// 更新客户积分
	updateClientPoints(clientId, points) {
		return request.put(`/crm/users/${clientId}/points`, { points })
	},
	
	// 更新客户会员等级
	updateMembershipLevel(clientId, levelId) {
		return request.put(`/crm/users/${clientId}/membership-level`, { level_id: levelId })
	},
	
	// 刷新客户会员等级
	refreshMembershipLevel(clientId) {
		return request.post(`/crm/users/${clientId}/refresh-level`)
	}
} 