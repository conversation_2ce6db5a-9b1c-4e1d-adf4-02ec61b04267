<?php

namespace App\Unit\Models;

use App\Product\Models\Product;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Unit\Services\UnitService;

class Unit extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name', 'display_name', 'symbol', 'type', 
        'base_unit_id', 'conversion_factor', 
        'sort_order', 'is_visible', 'description'
    ];

    /**
     * 数据类型转换
     *
     * @var array
     */
    protected $casts = [
        'conversion_factor' => 'decimal:10',
        'is_visible' => 'boolean',
    ];

    /**
     * 基本单位关系
     */
    public function baseUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'base_unit_id');
    }

    /**
     * 派生单位
     */
    public function derivedUnits(): HasMany
    {
        return $this->hasMany(Unit::class, 'base_unit_id');
    }

    /**
     * 产品单位关系
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_units')
                    ->withPivot(['is_default', 'conversion_factor', 'roles', 'role_priority', 'is_active'])
                    ->withTimestamps();
    }

    /**
     * 单位属性
     */
    public function properties(): HasMany
    {
        return $this->hasMany(UnitProperty::class);
    }

    /**
     * 作为源单位的边
     */
    public function outgoingEdges(): HasMany
    {
        return $this->hasMany(UnitConversionEdge::class, 'from_unit_id');
    }

    /**
     * 作为目标单位的边
     */
    public function incomingEdges(): HasMany
    {
        return $this->hasMany(UnitConversionEdge::class, 'to_unit_id');
    }

    /**
     * 转换单位
     * 
     * @param float $value 值
     * @param Unit $toUnit 目标单位
     * @return float 转换后的值
     */
    public function convertValue($value, Unit $toUnit)
    {
        // 直接使用单位转换服务进行转换
        return app(UnitService::class)->convertValue($value, $this, $toUnit);
    }

    /**
     * 获取格式化的值与单位
     * 
     * @param float $value 值
     * @param int $decimals 小数位数
     * @return string 格式化后的字符串
     */
    public function formatValue($value, $decimals = 2): string
    {
        return number_format($value, $decimals) . ' ' . $this->symbol;
    }
    
    /**
     * 检查单位是否被使用
     * 
     * @return bool
     */
    public function isInUse(): bool
    {
        return 
            $this->products()->exists() || 
            $this->outgoingEdges()->exists() || 
            $this->incomingEdges()->exists() ||
            $this->derivedUnits()->exists();
    }
} 