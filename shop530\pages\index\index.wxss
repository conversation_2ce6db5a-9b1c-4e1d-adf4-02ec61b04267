/* pages/index/index.wxss - 首页样式 */

/* 基础样式变量 */
:root {
  --status-bar-height: 44px; /* 默认值，会被JS动态设置 */
  --banner-height: 600rpx; /* 更新为600rpx */
  --primary-color: #4CAF50;
  --secondary-color: #ff6b35;
  --text-color: #333;
  --bg-color: #ffffff; /* 改为白色背景，避免透视问题 */
}

/* 主容器 */
.home-container {
  min-height: 100vh;
  background: var(--bg-color);
  overflow-x: hidden;
  position: relative;
  /* 性能优化 - 移除contain属性，因为它会影响内部fixed定位 */
  /* contain: layout style paint; */
  will-change: scroll-position;
  /* 确保容器完全不透明 */
  opacity: 1;
  /* 防止内容穿透 */
  isolation: isolate;
}

/* 搜索容器 */
.search-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 8rpx 24rpx;
  background-color: var(--primary-color);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 1;
  padding-top: 160rpx; /* 为固定的标题和搜索框预留空间 */
}

/* ===== 分层轮播图区域 ===== */
.banner-section-layered {
  position: relative;
  width: 100%;
  height: 600rpx; /* 修正为600rpx，与CSS变量保持一致 */
  overflow: hidden;
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
  margin-top: -160rpx; /* 负边距让轮播图延伸到标题下方 */
  padding-top: 160rpx; /* 内边距确保内容不被遮挡 */
  z-index: 1; /* 设置较低的层级，让分类网格能覆盖在上方 */
  animation: bannerFadeIn 0.6s ease-out;
}

/* 分层设计 */
.banner-layer {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 上层 - 显示图片上部分，模糊效果 */
.banner-layer-top {
  z-index: 1;
  clip-path: polygon(0 0, 100% 0, 100% 35%, 0 25%);
}

.banner-swiper-top {
  width: 100%;
  height: 100%;
}

.banner-item-top {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.banner-image-top {
  width: 100%;
  height: 100%;
  opacity: 0.6;
  filter: blur(8rpx);
  transform: scale(1.05);
  transition: all 0.4s ease-in-out;
}

/* 中层 - 显示图片中部分，主要内容 */
.banner-layer-middle {
  z-index: 2;
  clip-path: polygon(0 20%, 100% 30%, 100% 80%, 0 75%);
}

.banner-swiper-middle {
  width: 100%;
  height: 100%;
}

.banner-item-middle {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.banner-image-middle {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.banner-item-middle:active .banner-image-middle {
  transform: scale(0.98);
}

/* 下层 - 显示图片下部分，装饰效果 */
.banner-layer-bottom {
  z-index: 0;
  clip-path: polygon(0 70%, 100% 75%, 100% 100%, 0 100%);
}

.banner-swiper-bottom {
  width: 100%;
  height: 100%;
}

.banner-item-bottom {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.banner-image-bottom {
  width: 100%;
  height: 100%;
  opacity: 0.4;
  filter: blur(12rpx);
  transform: scale(1.1);
  transition: all 0.5s ease-in-out;
}

/* ===== 轮播图占位样式 ===== */
.banner-placeholder {
  width: 100%;
  height: 100%;
  background: transparent;
}

/* ===== 固定标题栏背景层 ===== */
.fixed-header-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 98;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  pointer-events: none;
  opacity: 0;
  transform: translateY(-20rpx);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 显示状态 */
.fixed-header-bg.show {
  opacity: 1;
  transform: translateY(0);
}

/* 隐藏状态 */
.fixed-header-bg.hide {
  opacity: 0;
  transform: translateY(-20rpx);
}

/* ===== 简单标题栏 - 固定定位，不随页面滑动 ===== */
.simple-title-bar {
  position: fixed;
  left: 30rpx;
  right: 0;
  z-index: 100;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  pointer-events: none;
  gap: 16rpx; /* 标题和配送时间之间的间距 */
}

.simple-title-text {
  color: #333333;
  font-size: 36rpx;
  font-weight: 700;
  text-align: center;
  line-height: 44rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 248, 248, 0.9)); /* 微妙渐变背景 */
  padding: 8rpx 24rpx;
  border-radius: 50rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1), 0 1rpx 3rpx rgba(255, 255, 255, 0.8) inset; /* 内外阴影 */
  border: 1rpx solid rgba(255, 255, 255, 0.6); /* 微妙边框 */
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.9); /* 文字阴影 */
}

.delivery-time-text {
  color: #ff6b35; /* 高亮橙色文字 */
  font-size: 36rpx; /* 与标题字体大小一致 */
  font-weight: 500; /* 稍微加粗 */
  line-height: 44rpx;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.2), rgba(255, 165, 0, 0.3)); /* 渐变高亮背景 */
  padding: 8rpx 24rpx;
  border-radius: 50rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3); /* 橙色阴影 */
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8); /* 文字阴影增强可读性 */
  border: 2rpx solid rgba(255, 107, 53, 0.4); /* 高亮边框 */
  animation: delivery-glow 2s ease-in-out infinite alternate; /* 呼吸灯效果 */
}

/* 配送时间高亮动画 */
@keyframes delivery-glow {
  0% {
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
    border-color: rgba(255, 107, 53, 0.4);
  }
  100% {
    box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.5);
    border-color: rgba(255, 107, 53, 0.6);
  }
}

/* ===== 服务特色区域 ===== */
.service-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: #fff; /* 改回白色背景 */
  padding: 24rpx 20rpx; /* 保持内边距 */
  margin: 40rpx 30rpx 20rpx 30rpx; /* 保持边距 */
  border-radius: 12rpx; /* 保持圆角 */
  border: 1rpx solid #f0f0f0; /* 添加边框 */
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04); /* 恢复阴影效果 */
  position: relative;
  z-index: 9; /* 设置层级，低于分类网格但高于轮播图 */
}

.service-item {
  display: flex;
  flex-direction: row; /* 改为水平排列 */
  align-items: center; /* 垂直居中对齐 */
  text-align: center;
  gap: 8rpx; /* 图标和文字之间的间距 */
}

.service-text {
  font-size: 22rpx; /* 保持字体大小 */
  color: #666; /* 使用中等灰色 */
  white-space: nowrap; /* 防止文字换行 */
}

/* ===== 活动通知栏 ===== */
.notice-container {
  margin: 20rpx 30rpx;
  margin-top: 0rpx; /* 移除上边距，紧跟服务特色区域 */
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.06);
}

.notice-content {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  position: relative;
}

.notice-content:active {
  background: #f8f8f8;
}

.notice-icon {
  margin-right: 20rpx;
}

.notice-text-wrapper {
  flex: 1;
  overflow: hidden;
}

.notice-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ===== 分类网格区域 ===== */
.category-section {
  margin: 20rpx 30rpx;
  margin-top: -150rpx; /* 向上移动150rpx，覆盖在轮播图上方 */
  margin-bottom: 30rpx; /* 增加底部间距，避免与下方元素重叠 */
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx 20rpx; /* 减少内边距 */
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04); /* 减少阴影强度 */
  position: relative; /* 确保层级正确 */
  z-index: 10; /* 设置较高的层级，确保在轮播图上方 */
}

.category-grid-container {
  display: flex;
  flex-wrap: wrap; /* 允许换行 */
  justify-content: space-between; /* 每行内部均匀分布 */
  align-items: flex-start;
  gap: 15rpx 0; /* 减少行间距 */
}

.category-grid-item {
  width: calc(20% - 8rpx); /* 一行5个：100% ÷ 5 = 20%，减去间距 */
  text-align: center;
  margin-bottom: 20rpx; /* 减少每个项目的底部间距 */
  transition: transform 0.2s ease;
  flex-shrink: 0; /* 防止项目收缩 */
  box-sizing: border-box; /* 确保padding和border包含在宽度内 */
}

.category-grid-item:active {
  transform: scale(0.95);
}

.category-icon-container {
  margin-bottom: 8rpx; /* 减少与文字的间距 */
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%; /* 确保容器占满父元素宽度 */
  height: 100rpx; /* 增大高度适配更大的图标 */
}

/* 分类图标圆形样式 */
.category-icon {
  border-radius: 50% !important; /* 设置为圆形 */
  overflow: hidden; /* 确保图片不会超出圆形边界 */
  background: #f8f8f8; /* 添加背景色，防止图片加载失败时显示空白 */
  transition: all 0.3s ease; /* 添加过渡动画 */
  width: 100rpx !important; /* 增大图标尺寸 */
  height: 100rpx !important; /* 增大图标尺寸 */
}

/* 分类图标悬停效果 */
.category-grid-item:active .category-icon {
  transform: scale(0.95); /* 轻微缩放效果 */
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3); /* 点击时添加主题色阴影 */
}

.category-name {
  font-size: 26rpx; /* 增大字体尺寸 */
  color: #333;
  line-height: 1.2;
  text-align: center;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 最多显示2行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%; /* 确保文字容器占满宽度 */
  word-break: break-all; /* 允许单词内换行 */
  min-height: 40rpx; /* 减少最小高度 */
  font-weight: 500; /* 增加字体粗细 */
}

/* ===== 商品Tab页区域 ===== */
.product-tabs-container {
  margin-top: 20rpx;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  min-height: 1000rpx;
}

/* Tab内容区域 */
.tab-content {
  background: #f8f8f8;
  min-height: 1000rpx;
  padding-bottom: 120rpx;
  padding-top: 20rpx; /* 减少顶部间距，从100rpx改为20rpx */
}

/* 商品分区 */
.product-section {
  margin-bottom: 40rpx;
}

/* 分区标题 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 30rpx;
  background: #fff;
}

.section-title-group {
  display: flex;
  flex-direction: column;
}

.main-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.sub-title {
  font-size: 24rpx;
  color: #999;
}

.more-button {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f0f0f0;
  transition: all 0.2s ease;
}

.more-button:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.more-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 4rpx;
}

/* ===== 商品瀑布流布局 ===== */
.product-waterfall {
  display: flex;
  padding: 0 30rpx;
  gap: 20rpx;
  background: #f8f8f8;
  /* 确保容器宽度计算正确 */
  box-sizing: border-box;
}

.waterfall-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  /* 确保两列宽度完全相等 */
  width: 0; /* 配合flex: 1使用，强制等宽 */
  min-width: 0;
}

.waterfall-left {
  /* 移除额外的margin，使用gap统一间距 */
}

.waterfall-right {
  /* 移除额外的margin，使用gap统一间距 */
}

/* ===== 回到顶部按钮 ===== */
.back-to-top {
  position: fixed;
  right: 30rpx;
  bottom: 150rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0,0,0,0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

.back-to-top:active {
  transform: scale(0.9);
  background: rgba(0,0,0,0.8);
}

/* ===== 底部占位 ===== */
.bottom-placeholder {
  height: 120rpx;
}

/* ===== 响应式设计 ===== */
@media (max-width: 350px) {
  .category-grid-item {
    width: calc(20% - 8rpx); /* 小屏幕稍微减少间距，但仍保持5个一行 */
  }
  
  .category-name {
    font-size: 20rpx; /* 小屏幕进一步减小字体 */
    min-height: 40rpx; /* 小屏幕调整最小高度 */
  }
  
  /* 小屏幕下调整clip-path的剪切区域 */
  .banner-layer-top {
    clip-path: polygon(0 0, 100% 0, 100% 40%, 0 30%);
  }
  
  .banner-layer-middle {
    clip-path: polygon(0 25%, 100% 35%, 100% 75%, 0 70%);
  }
  
  .banner-layer-bottom {
    clip-path: polygon(0 65%, 100% 70%, 100% 100%, 0 100%);
  }
}

/* ===== 深色模式适配 ===== */
@media (prefers-color-scheme: dark) {
  .home-container {
    background: #1a1a1a;
  }
  
  .service-container {
    background: #2a2a2a; /* 深色模式下使用深灰色背景 */
    border-color: #444; /* 深色模式下的边框颜色 */
  }
  
  .notice-container,
  .category-section,
  .product-tabs-container {
    background: #2a2a2a;
  }
  
  .main-title,
  .notice-text,
  .category-name {
    color: #ffffff;
  }
  
  .sub-title,
  .service-text {
    color: #cccccc;
  }
  
  .tab-content {
    background: #1a1a1a;
  }
  
  /* 深色模式骨架屏 */
  .tab-switching-skeleton {
    background: #1a1a1a;
  }
  
  .skeleton-header,
  .skeleton-product-card {
    background: #2a2a2a;
  }
  
  .skeleton-title,
  .skeleton-more {
    background: linear-gradient(90deg, #333 25%, #444 50%, #333 75%);
    background-size: 200% 100%;
  }
  
  .skeleton-product-card::before {
    background: linear-gradient(90deg, #333 25%, #444 50%, #333 75%);
    background-size: 200% 100%;
  }
  
  .empty-text {
    color: #666;
  }
  
  .banner-placeholder {
    background: transparent;
  }
}

/* ===== 标签切换骨架屏 ===== */
.tab-switching-skeleton {
  padding: 20rpx 30rpx;
  background: #f8f8f8;
  min-height: 600rpx;
}

.skeleton-section {
  margin-bottom: 40rpx;
}

.skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 30rpx;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.skeleton-title {
  width: 200rpx;
  height: 36rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 18rpx;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-more {
  width: 80rpx;
  height: 28rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 14rpx;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-products {
  padding: 0 30rpx;
}

.skeleton-product-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.skeleton-product-card {
  flex: 1;
  height: 320rpx;
  background: #fff;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
}

.skeleton-product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ===== 空状态样式 ===== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 30rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 20rpx;
}

/* ===== 内容切换动画 ===== */
.tab-actual-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 动画效果 */
@keyframes bannerFadeIn {
  from {
    opacity: 0;
    transform: scale(1.1);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.banner-section-layered,
.banner-section-normal {
  animation: bannerFadeIn 0.6s ease-out;
} 