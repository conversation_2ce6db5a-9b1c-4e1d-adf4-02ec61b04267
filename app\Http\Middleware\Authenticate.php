<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    protected function redirectTo(Request $request): ?string
    {
        // 如果是API请求或请求期望返回JSON，则不进行重定向
        if ($request->is('api/*') || $request->wantsJson() || $request->expectsJson()) {
            return null;
        }
        
        // 检查login路由是否存在
        if (app('router')->has('login')) {
            return route('login');
        }
        
        // 如果login路由不存在，仍然返回null以防止重定向
        // 这将导致抛出未认证异常，而不是重定向
        return null;
    }
}
