<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // 添加货到付款相关字段
            $table->boolean('is_cod')->default(false)->after('payment_method')->comment('是否为货到付款订单');
            $table->string('cod_status')->nullable()->after('is_cod')->comment('货到付款状态：unpaid(未支付), paid(已支付), failed(支付失败)');
            $table->timestamp('cod_paid_at')->nullable()->after('cod_status')->comment('货到付款收款时间');
            $table->string('cod_notes')->nullable()->after('cod_paid_at')->comment('货到付款备注信息');
            $table->unsignedBigInteger('cod_received_by')->nullable()->after('cod_notes')->comment('收款人ID（配送员）');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // 移除货到付款相关字段
            $table->dropColumn([
                'is_cod',
                'cod_status',
                'cod_paid_at',
                'cod_notes',
                'cod_received_by'
            ]);
        });
    }
};
