<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique()->comment('采购单号');
            $table->foreignId('supplier_id')->constrained()->comment('供应商ID');
            $table->enum('status', ['draft', 'submitted', 'approved', 'partial_received', 'received', 'canceled'])
                  ->default('draft')->comment('状态：草稿，已提交，已批准，部分收货，已收货，已取消');
            $table->date('order_date')->comment('订单日期');
            $table->date('expected_delivery_date')->nullable()->comment('预计交货日期');
            $table->foreignId('warehouse_id')->constrained()->comment('收货仓库ID');
            $table->decimal('total_amount', 12, 2)->default(0)->comment('总金额');
            $table->decimal('paid_amount', 12, 2)->default(0)->comment('已付金额');
            $table->text('notes')->nullable()->comment('备注');
            $table->foreignId('created_by')->constrained('users')->comment('创建人');
            $table->foreignId('approved_by')->nullable()->constrained('users')->comment('审批人');
            $table->timestamp('approved_at')->nullable()->comment('审批时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_orders');
    }
}; 