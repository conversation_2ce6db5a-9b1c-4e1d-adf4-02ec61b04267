<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 积分系统配置
    |--------------------------------------------------------------------------
    */

    // 积分过期时间（天）
    'points_expire_days' => 365,

    // 每日签到积分
    'signin_points' => 10,

    // 积分兑换比例（1元 = 多少积分）
    'points_per_yuan' => 100,

    // 订单完成积分奖励比例（订单金额的百分比）
    'order_points_rate' => 0.01,

    // 积分最小使用单位
    'min_points_use' => 10,

    // 单次最大使用积分数
    'max_points_use' => 10000,

    // 邀请好友奖励积分
    'invite_points' => 100,

    // 商品评价奖励积分
    'review_points' => 5,

    // 分享商品奖励积分
    'share_points' => 2,

    // 生日奖励积分
    'birthday_points' => 50,

    // 会员升级奖励积分
    'upgrade_points' => [
        'bronze' => 100,
        'silver' => 200,
        'gold' => 500,
        'diamond' => 1000,
    ],

    // 积分商品配置
    'products' => [
        // 默认排序字段
        'default_sort' => 'sort_order',
        
        // 每页显示数量
        'per_page' => 20,
        
        // 最大每页数量
        'max_per_page' => 100,
        
        // 热门商品数量
        'popular_limit' => 10,
        
        // 推荐商品数量
        'recommended_limit' => 10,
    ],

    // 积分订单配置
    'orders' => [
        // 订单号前缀
        'order_prefix' => 'PO',
        
        // 订单超时时间（分钟）
        'timeout_minutes' => 30,
        
        // 自动确认收货时间（天）
        'auto_confirm_days' => 7,
    ],

    // 积分规则配置
    'rules' => [
        // 规则类型
        'types' => [
            'signin' => '每日签到',
            'order' => '订单完成',
            'invite' => '邀请好友',
            'review' => '商品评价',
            'share' => '分享商品',
            'birthday' => '生日奖励',
            'upgrade' => '会员升级',
            'admin' => '管理员操作',
        ],
    ],

    // 积分排行榜配置
    'ranking' => [
        // 默认显示数量
        'default_limit' => 10,
        
        // 最大显示数量
        'max_limit' => 50,
        
        // 缓存时间（分钟）
        'cache_minutes' => 60,
    ],
]; 