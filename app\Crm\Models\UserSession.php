<?php

namespace App\Crm\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\User;

class UserSession extends Model
{
    protected $table = 'user_sessions';

    protected $fillable = [
        'session_id',
        'user_id',
        'start_time',
        'end_time',
        'page_count',
        'event_count',
        'duration',
        'device_info',
        'ip_address',
        'referrer',
    ];

    protected $casts = [
        'device_info' => 'array',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'page_count' => 'integer',
        'event_count' => 'integer',
        'duration' => 'integer',
    ];

    /**
     * 获取关联的用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取会话的所有行为记录
     */
    public function behaviors(): HasMany
    {
        return $this->hasMany(CustomerBehaviorAnalytics::class, 'session_id', 'session_id');
    }

    /**
     * 创建或更新会话
     */
    public static function createOrUpdate(
        string $sessionId,
        ?int $userId = null,
        array $deviceInfo = [],
        ?string $ipAddress = null,
        ?string $referrer = null
    ): self {
        return self::updateOrCreate(
            ['session_id' => $sessionId],
            [
                'user_id' => $userId,
                'start_time' => now(),
                'device_info' => $deviceInfo,
                'ip_address' => $ipAddress,
                'referrer' => $referrer,
            ]
        );
    }

    /**
     * 结束会话
     */
    public function endSession(): void
    {
        $this->update([
            'end_time' => now(),
            'duration' => now()->diffInSeconds($this->start_time),
        ]);
    }

    /**
     * 增加页面计数
     */
    public function incrementPageCount(): void
    {
        $this->increment('page_count');
    }

    /**
     * 增加事件计数
     */
    public function incrementEventCount(): void
    {
        $this->increment('event_count');
    }

    /**
     * 获取会话时长（分钟）
     */
    public function getDurationInMinutes(): int
    {
        if ($this->end_time) {
            return $this->duration ? intval($this->duration / 60) : 0;
        }
        
        return now()->diffInMinutes($this->start_time);
    }

    /**
     * 判断会话是否活跃
     */
    public function isActive(): bool
    {
        return is_null($this->end_time) && 
               now()->diffInMinutes($this->start_time) < 30; // 30分钟内视为活跃
    }

    /**
     * 按时间范围查询
     */
    public function scopeInTimeRange($query, $startTime, $endTime)
    {
        return $query->whereBetween('start_time', [$startTime, $endTime]);
    }

    /**
     * 按用户查询
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 查询活跃会话
     */
    public function scopeActive($query)
    {
        return $query->whereNull('end_time')
                    ->where('start_time', '>', now()->subMinutes(30));
    }
} 