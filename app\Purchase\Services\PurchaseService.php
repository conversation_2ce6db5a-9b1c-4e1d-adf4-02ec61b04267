<?php

namespace App\Purchase\Services;

use App\Purchase\Models\PurchaseOrder;use App\Purchase\Models\PurchaseItem;use App\Inventory\Models\InventoryTransaction;use App\Inventory\Models\InventoryTransactionType;use App\Inventory\Models\InventoryBatch;use App\Inventory\Models\Inventory;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class PurchaseService
{
    /**
     * 获取采购订单列表
     */
    public function getPurchaseOrders(array $params): LengthAwarePaginator
    {
        $query = PurchaseOrder::with(['supplier', 'warehouse', 'items.product', 'items.unit']);
        
        if (isset($params['order_number'])) {
            $query->where('order_number', 'like', "%{$params['order_number']}%");
        }
        
        if (isset($params['supplier_id'])) {
            $query->where('supplier_id', $params['supplier_id']);
        }
        
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }
        
        if (isset($params['start_date'])) {
            $query->where('order_date', '>=', $params['start_date']);
        }
        
        if (isset($params['end_date'])) {
            $query->where('order_date', '<=', $params['end_date']);
        }
        
        $orderBy = $params['order_by'] ?? 'created_at';
        $direction = $params['direction'] ?? 'desc';
        $query->orderBy($orderBy, $direction);
        
        $perPage = $params['per_page'] ?? 15;
        return $query->paginate($perPage);
    }
    
    /**
     * 创建采购订单
     */
    public function createPurchaseOrder(array $data): PurchaseOrder
    {
        DB::beginTransaction();
        
        try {
            $orderNumber = 'PO-' . date('Ymd') . '-' . rand(1000, 9999);
            
            $order = PurchaseOrder::create([
                'order_number' => $orderNumber,
                'supplier_id' => $data['supplier_id'] ?? null,
                'warehouse_id' => $data['warehouse_id'] ?? null,
                'status' => 'draft',
                'order_date' => $data['order_date'],
                'expected_delivery_date' => $data['expected_delivery_date'] ?? null,
                'notes' => $data['notes'] ?? null,
                'payment_method' => $data['payment_method'] ?? null,
                'priority' => $data['priority'] ?? 'medium',
                'shipping_fee' => $data['shipping_fee'] ?? 0,
                'tax_fee' => $data['tax_fee'] ?? 0,
                'other_fee' => $data['other_fee'] ?? 0,
                'subtotal_amount' => 0,
                'total_amount' => 0,
                'paid_amount' => 0,
                'created_by' => auth()->id() ?? 1,
            ]);
            
            $totalAmount = 0;
            
            foreach ($data['items'] as $item) {
                $totalPrice = $item['quantity'] * $item['unit_price'];
                $totalAmount += $totalPrice;
                
                $order->items()->create([
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_id' => $item['unit_id'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $totalPrice,
                    'received_quantity' => 0,
                ]);
            }
            
            // 计算总金额：商品小计 + 各种费用
            $shippingFee = $data['shipping_fee'] ?? 0;
            $taxFee = $data['tax_fee'] ?? 0;
            $otherFee = $data['other_fee'] ?? 0;
            $finalTotalAmount = $totalAmount + $shippingFee + $taxFee + $otherFee;
            
            $order->subtotal_amount = $totalAmount; // 商品小计
            $order->total_amount = $finalTotalAmount; // 最终总金额
            $order->save();
            
            DB::commit();
            
            return $order->load(['supplier', 'warehouse', 'items.product', 'items.unit']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 获取采购订单详情
     */
    public function getPurchaseOrder(int $id): PurchaseOrder
    {
        return PurchaseOrder::with([
            'supplier', 
            'warehouse', 
            'items.product', 
            'items.unit', 
            'creator', 
            'approver',
            'inventoryTransactions'
        ])->findOrFail($id);
    }
    
    /**
     * 更新采购订单
     */
    public function updatePurchaseOrder(int $id, array $data): PurchaseOrder
    {
        $order = PurchaseOrder::findOrFail($id);
        
        if ($order->status !== 'draft') {
            throw new \Exception('只有草稿状态的采购订单可以更新');
        }
        
        DB::beginTransaction();
        
        try {
            $order->update([
                'supplier_id' => $data['supplier_id'] ?? $order->supplier_id,
                'warehouse_id' => $data['warehouse_id'] ?? $order->warehouse_id,
                'order_date' => $data['order_date'] ?? $order->order_date,
                'expected_delivery_date' => $data['expected_delivery_date'] ?? $order->expected_delivery_date,
                'notes' => $data['notes'] ?? $order->notes,
            ]);
            
            if (isset($data['items']) && is_array($data['items'])) {
                $order->items()->delete();
                
                $totalAmount = 0;
                
                foreach ($data['items'] as $item) {
                    $totalPrice = $item['quantity'] * $item['unit_price'];
                    $totalAmount += $totalPrice;
                    
                    $order->items()->create([
                        'product_id' => $item['product_id'],
                        'quantity' => $item['quantity'],
                        'unit_id' => $item['unit_id'],
                        'unit_price' => $item['unit_price'],
                        'total_price' => $totalPrice,
                        'received_quantity' => 0,
                    ]);
                }
                
                $order->total_amount = $totalAmount;
                $order->save();
            }
            
            DB::commit();
            
            return $order->load(['supplier', 'warehouse', 'items.product', 'items.unit']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 提交采购订单
     */
    public function submitPurchaseOrder(int $id): PurchaseOrder
    {
        $order = PurchaseOrder::findOrFail($id);
        
        if ($order->status !== 'draft') {
            throw new \Exception('只有草稿状态的采购订单可以提交');
        }
        
        if ($order->items()->count() === 0) {
            throw new \Exception('采购订单必须包含至少一个明细项');
        }
        
        $order->status = 'submitted';
        $order->save();
        
        return $order->load(['supplier', 'warehouse', 'items.product', 'items.unit']);
    }
    
    /**
     * 审批采购订单
     */
    public function approvePurchaseOrder(int $id): PurchaseOrder
    {
        $order = PurchaseOrder::findOrFail($id);
        
        if ($order->status !== 'submitted') {
            throw new \Exception('只有已提交状态的采购订单可以审批');
        }
        
        $order->status = 'approved';
        $order->approved_by = auth()->id();
        $order->approved_at = now();
        $order->save();
        
        return $order->load(['supplier', 'warehouse', 'items.product', 'items.unit', 'approver']);
    }
    
    /**
     * 取消采购订单
     */
    public function cancelPurchaseOrder(int $id): PurchaseOrder
    {
        $order = PurchaseOrder::findOrFail($id);
        
        if (!in_array($order->status, ['draft', 'submitted', 'approved'])) {
            throw new \Exception('只有草稿、已提交或已审批状态的采购订单可以取消');
        }
        
        $receivedItems = $order->items()->where('received_quantity', '>', 0)->count();
        if ($receivedItems > 0) {
            throw new \Exception('已经有收货记录的采购订单不能取消');
        }
        
        $order->status = 'canceled';
        $order->save();
        
        return $order->load(['supplier', 'warehouse', 'items.product', 'items.unit']);
    }
    
    /**
     * 收货处理
     */
    public function receiveItems(int $id, array $data): PurchaseOrder
    {
        $order = PurchaseOrder::findOrFail($id);
        
        if ($order->status !== 'approved') {
            throw new \Exception('只有已审批状态的采购订单可以收货');
        }
        
        DB::beginTransaction();
        
        try {
            foreach ($data['items'] as $itemData) {
                $item = PurchaseItem::findOrFail($itemData['id']);
                
                if ($item->purchase_order_id != $id) {
                    throw new \Exception('订单明细不属于当前采购单');
                }
                
                $receiveQty = $itemData['receive_quantity'];
                if ($receiveQty <= 0) {
                    throw new \Exception('收货数量必须大于0');
                }
                
                // 检查收货数量不能超过采购数量
                if ($item->received_quantity + $receiveQty > $item->quantity) {
                    throw new \Exception("商品 {$item->product->name} 收货数量超过采购数量");
                }
                
                // 获取商品的仓库信息（从库存记录中获取）
                $inventory = Inventory::where('product_id', $item->product_id)->first();
                
                if (!$inventory) {
                    // 商品没有库存记录，需要先在库存管理中设置仓库
                    $productName = $item->product->name ?? "ID: {$item->product_id}";
                    
                    \Illuminate\Support\Facades\Log::warning('⚠️ 商品没有库存记录，无法收货', [
                        'product_id' => $item->product_id,
                        'product_name' => $productName,
                        'order_id' => $order->id,
                        'user_id' => auth()->id(),
                    ]);
                    
                    throw new \Exception(
                        "商品「{$productName}」还没有设置仓库存储位置！\n\n" .
                        "📍 解决方案：\n" .
                        "请先到【库存管理】→【商品库存】中为该商品分配仓库，然后再进行收货操作。\n\n" .
                        "📋 操作步骤：\n" .
                        "1️⃣ 进入库存管理模块\n" .
                        "2️⃣ 找到商品「{$productName}」\n" .
                        "3️⃣ 为商品分配存储仓库\n" .
                        "4️⃣ 返回采购模块进行收货\n\n" .
                        "💡 提示：商品必须先在库存管理中设置仓库位置才能进行收货操作。"
                    );
                }
                
                // 创建采购入库事务记录
                $transactionType = InventoryTransactionType::where('code', 'purchase_in')->first();
                if (!$transactionType) {
                    \Illuminate\Support\Facades\Log::error('❌ 找不到采购入库事务类型', [
                        'available_types' => InventoryTransactionType::pluck('code', 'id')->toArray()
                    ]);
                    throw new \Exception('找不到采购入库事务类型');
                }
                
                // 验证外键约束
                $validationErrors = [];
                
                if (!DB::table('products')->where('id', $item->product_id)->exists()) {
                    $validationErrors[] = "商品ID {$item->product_id} 不存在";
                }
                
                if (!DB::table('warehouses')->where('id', $inventory->warehouse_id)->exists()) {
                    $validationErrors[] = "仓库ID {$inventory->warehouse_id} 不存在";
                }
                
                if (!DB::table('units')->where('id', $item->unit_id)->exists()) {
                    $validationErrors[] = "单位ID {$item->unit_id} 不存在";
                }
                
                if (!DB::table('users')->where('id', auth()->id())->exists()) {
                    $validationErrors[] = "用户ID " . auth()->id() . " 不存在";
                }
                
                if (!empty($validationErrors)) {
                    \Illuminate\Support\Facades\Log::error('❌ 外键约束验证失败', [
                        'errors' => $validationErrors,
                        'transaction_data' => [
                            'transaction_type_id' => $transactionType->id,
                            'product_id' => $item->product_id,
                            'warehouse_id' => $inventory->warehouse_id,
                            'unit_id' => $item->unit_id,
                            'user_id' => auth()->id(),
                        ]
                    ]);
                    throw new \Exception('外键约束验证失败: ' . implode(', ', $validationErrors));
                }
                
                \Illuminate\Support\Facades\Log::info('📝 准备创建库存事务记录', [
                    'transaction_type_id' => $transactionType->id,
                    'product_id' => $item->product_id,
                    'warehouse_id' => $inventory->warehouse_id,
                    'quantity' => $receiveQty,
                    'unit_id' => $item->unit_id,
                    'unit_price' => $item->unit_price,
                    'user_id' => auth()->id(),
                ]);
                
                $transaction = InventoryTransaction::create([
                    'transaction_type_id' => $transactionType->id,
                    'reference_type' => PurchaseOrder::class,
                    'reference_id' => $order->id,
                    'product_id' => $item->product_id,
                    'warehouse_id' => $inventory->warehouse_id,
                    'quantity' => $receiveQty,
                    'unit_id' => $item->unit_id,
                    'unit_price' => $item->unit_price,
                    'total_amount' => $receiveQty * $item->unit_price,
                    'status' => 'completed',
                    'notes' => $itemData['notes'] ?? "采购收货 - {$order->order_number}",
                    'created_by' => auth()->id(),
                    'updated_by' => auth()->id(),
                ]);
                
                \Illuminate\Support\Facades\Log::info('✅ 库存事务记录创建成功', [
                    'transaction_id' => $transaction->id,
                    'product_id' => $item->product_id,
                    'quantity' => $receiveQty,
                    'unit_price' => $item->unit_price,
                ]);
                
                // 更新库存数量
                $inventory->stock += $receiveQty;
                $inventory->save();
                
                // 更新采购明细收货信息
                $item->received_quantity += $receiveQty;
                $item->save();
                
                // 更新商品成本价
                try {
                    $costPriceService = app(\App\Inventory\Services\CostPriceService::class);
                    $costPriceService->updateCostPriceOnInbound($transaction);
                    
                    \Illuminate\Support\Facades\Log::info('💰 成本价更新成功', [
                        'product_id' => $item->product_id,
                        'transaction_id' => $transaction->id,
                        'unit_price' => $item->unit_price,
                        'quantity' => $receiveQty,
                    ]);
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('💰 成本价更新失败', [
                        'product_id' => $item->product_id,
                        'transaction_id' => $transaction->id,
                        'error' => $e->getMessage(),
                    ]);
                }
                
                // 记录收货日志
                \Illuminate\Support\Facades\Log::info('📦 采购商品收货成功', [
                    'order_id' => $order->id,
                    'item_id' => $item->id,
                    'product_id' => $item->product_id,
                    'product_name' => $item->product->name ?? 'Unknown',
                    'receive_quantity' => $receiveQty,
                    'total_received' => $item->received_quantity,
                    'total_ordered' => $item->quantity,
                    'warehouse_id' => $inventory->warehouse_id,
                    'warehouse_name' => $inventory->warehouse->name ?? 'Unknown',
                    'new_stock' => $inventory->stock,
                    'user_id' => auth()->id(),
                ]);
            }
            
            // 检查收货状态
            $allItemsReceived = true;
            $hasReceivedItems = false;
            
            foreach ($order->items as $item) {
                if ($item->received_quantity > 0) {
                    $hasReceivedItems = true;
                }
                if ($item->received_quantity < $item->quantity) {
                    $allItemsReceived = false;
                }
            }
            
            // 更新订单状态
            if ($allItemsReceived) {
                $order->status = 'received';
            } elseif ($hasReceivedItems) {
                $order->status = 'partial_received';
            }
            
            $order->save();
            
            DB::commit();
            
            return $order->load([
                'supplier', 
                'warehouse', 
                'items.product', 
                'items.unit', 
                'items.batches',
                'inventoryTransactions'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取采购统计数据
     */
    public function getPurchaseStats(): array
    {
        $now = now();
        $startOfMonth = $now->copy()->startOfMonth();
        $startOfLastMonth = $now->copy()->subMonth()->startOfMonth();
        $endOfLastMonth = $now->copy()->subMonth()->endOfMonth();

        // 总订单数
        $totalOrders = PurchaseOrder::count();

        // 本月新订单数
        $newOrdersThisMonth = PurchaseOrder::where('created_at', '>=', $startOfMonth)->count();

        // 上月新订单数（用于计算增长率）
        $newOrdersLastMonth = PurchaseOrder::whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])->count();

        // 总采购金额
        $totalAmount = PurchaseOrder::whereIn('status', ['approved', 'completed'])->sum('total_amount');

        // 本月采购金额
        $amountThisMonth = PurchaseOrder::whereIn('status', ['approved', 'completed'])
            ->where('created_at', '>=', $startOfMonth)
            ->sum('total_amount');

        // 上月采购金额（用于计算增长率）
        $amountLastMonth = PurchaseOrder::whereIn('status', ['approved', 'completed'])
            ->whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])
            ->sum('total_amount');

        // 计算金额增长率
        $amountGrowth = 0;
        if ($amountLastMonth > 0) {
            $amountGrowth = (($amountThisMonth - $amountLastMonth) / $amountLastMonth) * 100;
        } elseif ($amountThisMonth > 0) {
            $amountGrowth = 100; // 从0增长到有金额，视为100%增长
        }

        // 待处理订单数（草稿 + 待审批）
        $pendingOrders = PurchaseOrder::whereIn('status', ['draft', 'submitted'])->count();

        // 活跃供应商数（本月有订单的供应商）
        $activeSuppliers = PurchaseOrder::where('created_at', '>=', $startOfMonth)
            ->distinct('supplier_id')
            ->count('supplier_id');

        // 总供应商数
        $totalSuppliers = \App\Supplier\Models\Supplier::count();

        return [
            'totalOrders' => $totalOrders,
            'newOrdersThisMonth' => $newOrdersThisMonth,
            'totalAmount' => round($totalAmount, 2),
            'amountGrowth' => round($amountGrowth, 2),
            'pendingOrders' => $pendingOrders,
            'activeSuppliers' => $activeSuppliers,
            'totalSuppliers' => $totalSuppliers,
        ];
    }
} 