<?php

namespace App\Purchase\Services;

use App\Purchase\Models\PurchaseOrder;use App\Purchase\Models\PurchaseItem;use App\Inventory\Models\InventoryTransaction;use App\Inventory\Models\InventoryTransactionType;use App\Inventory\Models\InventoryBatch;use App\Inventory\Models\Inventory;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class PurchaseService
{
    /**
     * 获取采购订单列表
     */
    public function getPurchaseOrders(array $params): LengthAwarePaginator
    {
        $query = PurchaseOrder::with(['supplier', 'warehouse', 'items.product', 'items.unit']);
        
        if (isset($params['order_number'])) {
            $query->where('order_number', 'like', "%{$params['order_number']}%");
        }
        
        if (isset($params['supplier_id'])) {
            $query->where('supplier_id', $params['supplier_id']);
        }
        
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }
        
        if (isset($params['start_date'])) {
            $query->where('order_date', '>=', $params['start_date']);
        }
        
        if (isset($params['end_date'])) {
            $query->where('order_date', '<=', $params['end_date']);
        }
        
        $orderBy = $params['order_by'] ?? 'created_at';
        $direction = $params['direction'] ?? 'desc';
        $query->orderBy($orderBy, $direction);
        
        $perPage = $params['per_page'] ?? 15;
        return $query->paginate($perPage);
    }
    
    /**
     * 创建采购订单
     */
    public function createPurchaseOrder(array $data): PurchaseOrder
    {
        DB::beginTransaction();
        
        try {
            $orderNumber = 'PO-' . date('Ymd') . '-' . rand(1000, 9999);
            
            $order = PurchaseOrder::create([
                'order_number' => $orderNumber,
                'supplier_id' => $data['supplier_id'],
                'warehouse_id' => $data['warehouse_id'],
                'status' => 'draft',
                'order_date' => $data['order_date'],
                'expected_delivery_date' => $data['expected_delivery_date'] ?? null,
                'notes' => $data['notes'] ?? null,
                'total_amount' => 0,
                'paid_amount' => 0,
                'created_by' => auth()->id() ?? 1,
            ]);
            
            $totalAmount = 0;
            
            foreach ($data['items'] as $item) {
                $totalPrice = $item['quantity'] * $item['unit_price'];
                $totalAmount += $totalPrice;
                
                $order->items()->create([
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_id' => $item['unit_id'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $totalPrice,
                    'received_quantity' => 0,
                ]);
            }
            
            $order->total_amount = $totalAmount;
            $order->save();
            
            DB::commit();
            
            return $order->load(['supplier', 'warehouse', 'items.product', 'items.unit']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 获取采购订单详情
     */
    public function getPurchaseOrder(int $id): PurchaseOrder
    {
        return PurchaseOrder::with([
            'supplier', 
            'warehouse', 
            'items.product', 
            'items.unit', 
            'creator', 
            'approver',
            'inventoryTransactions'
        ])->findOrFail($id);
    }
    
    /**
     * 更新采购订单
     */
    public function updatePurchaseOrder(int $id, array $data): PurchaseOrder
    {
        $order = PurchaseOrder::findOrFail($id);
        
        if ($order->status !== 'draft') {
            throw new \Exception('只有草稿状态的采购订单可以更新');
        }
        
        DB::beginTransaction();
        
        try {
            $order->update([
                'supplier_id' => $data['supplier_id'] ?? $order->supplier_id,
                'warehouse_id' => $data['warehouse_id'] ?? $order->warehouse_id,
                'order_date' => $data['order_date'] ?? $order->order_date,
                'expected_delivery_date' => $data['expected_delivery_date'] ?? $order->expected_delivery_date,
                'notes' => $data['notes'] ?? $order->notes,
            ]);
            
            if (isset($data['items']) && is_array($data['items'])) {
                $order->items()->delete();
                
                $totalAmount = 0;
                
                foreach ($data['items'] as $item) {
                    $totalPrice = $item['quantity'] * $item['unit_price'];
                    $totalAmount += $totalPrice;
                    
                    $order->items()->create([
                        'product_id' => $item['product_id'],
                        'quantity' => $item['quantity'],
                        'unit_id' => $item['unit_id'],
                        'unit_price' => $item['unit_price'],
                        'total_price' => $totalPrice,
                        'received_quantity' => 0,
                    ]);
                }
                
                $order->total_amount = $totalAmount;
                $order->save();
            }
            
            DB::commit();
            
            return $order->load(['supplier', 'warehouse', 'items.product', 'items.unit']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 提交采购订单
     */
    public function submitPurchaseOrder(int $id): PurchaseOrder
    {
        $order = PurchaseOrder::findOrFail($id);
        
        if ($order->status !== 'draft') {
            throw new \Exception('只有草稿状态的采购订单可以提交');
        }
        
        if ($order->items()->count() === 0) {
            throw new \Exception('采购订单必须包含至少一个明细项');
        }
        
        $order->status = 'pending_approval';
        $order->save();
        
        return $order->load(['supplier', 'warehouse', 'items.product', 'items.unit']);
    }
    
    /**
     * 审批采购订单
     */
    public function approvePurchaseOrder(int $id): PurchaseOrder
    {
        $order = PurchaseOrder::findOrFail($id);
        
        if ($order->status !== 'pending_approval') {
            throw new \Exception('只有待审批状态的采购订单可以审批');
        }
        
        $order->status = 'approved';
        $order->approved_by = auth()->id();
        $order->approved_at = now();
        $order->save();
        
        return $order->load(['supplier', 'warehouse', 'items.product', 'items.unit', 'approver']);
    }
    
    /**
     * 取消采购订单
     */
    public function cancelPurchaseOrder(int $id): PurchaseOrder
    {
        $order = PurchaseOrder::findOrFail($id);
        
        if (!in_array($order->status, ['draft', 'pending_approval', 'approved'])) {
            throw new \Exception('只有草稿、待审批或已审批状态的采购订单可以取消');
        }
        
        $receivedItems = $order->items()->where('received_quantity', '>', 0)->count();
        if ($receivedItems > 0) {
            throw new \Exception('已经有收货记录的采购订单不能取消');
        }
        
        $order->status = 'canceled';
        $order->save();
        
        return $order->load(['supplier', 'warehouse', 'items.product', 'items.unit']);
    }
    
    /**
     * 收货处理
     */
    public function receiveItems(int $id, array $data): PurchaseOrder
    {
        $order = PurchaseOrder::findOrFail($id);
        
        if ($order->status !== 'approved') {
            throw new \Exception('只有已审批状态的采购订单可以收货');
        }
        
        DB::beginTransaction();
        
        try {
            foreach ($data['items'] as $itemData) {
                $item = PurchaseItem::findOrFail($itemData['id']);
                
                if ($item->purchase_order_id != $id) {
                    throw new \Exception('订单明细不属于当前采购单');
                }
                
                $receiveQty = $itemData['receive_quantity'];
                if ($receiveQty <= 0) {
                    throw new \Exception('收货数量必须大于0');
                }
                
                $inventory = Inventory::firstOrCreate([
                    'product_id' => $item->product_id,
                    'warehouse_id' => $order->warehouse_id,
                ]);
                
                $transaction = InventoryTransaction::create([
                    'inventory_id' => $inventory->id,
                    'type_id' => InventoryTransactionType::where('code', 'purchase_receive')->first()->id,
                    'quantity' => $receiveQty,
                    'unit_id' => $item->unit_id,
                    'reference_type' => PurchaseOrder::class,
                    'reference_id' => $order->id,
                    'notes' => $itemData['notes'] ?? null,
                    'created_by' => auth()->id(),
                ]);
                
                $batch = $item->addBatch(
                    $inventory->id,
                    $receiveQty,
                    $item->unit_id,
                    $itemData['batch_code'] ?? null,
                    $itemData['production_date'] ?? null,
                    $itemData['expiry_date'] ?? null,
                    $itemData['notes'] ?? null,
                    auth()->id()
                );
                
                $inventory->available_quantity += $receiveQty;
                $inventory->save();
                
                $item->received_quantity += $receiveQty;
                $item->save();
            }
            
            $allItemsReceived = true;
            foreach ($order->items as $item) {
                if ($item->pending_quantity > 0) {
                    $allItemsReceived = false;
                    break;
                }
            }
            
            if ($allItemsReceived) {
                $order->status = 'completed';
                $order->save();
            }
            
            DB::commit();
            
            return $order->load([
                'supplier', 
                'warehouse', 
                'items.product', 
                'items.unit', 
                'items.batches',
                'inventoryTransactions'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
} 