<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wechat_service_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('provider_id')->comment('服务商ID')
                  ->constrained('wechat_service_provider')
                  ->onDelete('cascade');
            $table->foreignId('sub_merchant_id')->comment('子商户ID')
                  ->constrained('wechat_sub_merchants')
                  ->onDelete('cascade');
            $table->foreignId('order_id')->nullable()->comment('相关订单ID')
                  ->constrained('orders')
                  ->onDelete('set null');
            $table->string('out_trade_no')->comment('商户订单号')->unique();
            $table->string('transaction_id')->nullable()->comment('微信支付交易号');
            $table->decimal('total_fee', 10, 2)->comment('订单金额');
            $table->decimal('service_fee', 10, 2)->default(0)->comment('服务费金额');
            $table->decimal('settlement_fee', 10, 2)->default(0)->comment('结算金额');
            $table->string('trade_type')->comment('交易类型：JSAPI、NATIVE、APP等');
            $table->string('trade_state')->default('NOTPAY')->comment('交易状态');
            $table->timestamp('pay_time')->nullable()->comment('支付完成时间');
            $table->text('attach')->nullable()->comment('附加数据');
            $table->text('notify_data')->nullable()->comment('回调原始数据');
            $table->text('prepay_data')->nullable()->comment('预支付数据');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wechat_service_payments');
    }
};
