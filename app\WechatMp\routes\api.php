<?php

use Illuminate\Support\Facades\Route;
use App\WechatMp\Http\Controllers\AuthController;
use App\WechatMp\Http\Controllers\ConfigController;
use App\WechatMp\Http\Controllers\UserController;

/*
|--------------------------------------------------------------------------
| 微信小程序模块 API 路由
|--------------------------------------------------------------------------
|
| 这里定义微信小程序模块的所有API路由
|
*/

// 公开API，不需要认证
Route::prefix('api/wechat/mp')->group(function () {
    // 微信小程序配置
    Route::get('/config', [ConfigController::class, 'getMiniProgramConfig']);
    
    // 微信小程序登录
    Route::post('/login', [AuthController::class, 'wxLogin']);
    
    // 验证码登录
    Route::post('/sms/login', [AuthController::class, 'smsLogin']);
    
    // 短信验证码
    Route::post('/sms/code', [AuthController::class, 'sendSmsCode']);
    
    // 配置检查
    Route::post('/check-config', [AuthController::class, 'checkConfig']);
    
    // 初始化配置
    Route::post('/init-config', [AuthController::class, 'initWechatConfig']);
});

// 需要认证的API
Route::prefix('api/wechat/mp')->middleware('auth:sanctum')->group(function () {
    // 获取当前用户信息
    Route::get('/user', [UserController::class, 'getCurrentUser']);
    
    // 更新用户信息
    Route::post('/user/update', [UserController::class, 'updateUserInfo']);
    
    // 绑定手机号
    Route::post('/user/bind-phone', [UserController::class, 'bindPhone']);
    
    // 通过验证码绑定手机号
    Route::post('/user/bind-phone-code', [UserController::class, 'bindPhoneWithCode']);
    
    // 获取用户常购商品
    Route::get('/user/frequent-products', [UserController::class, 'getFrequentProducts']);
    
    // 用户地址管理
    Route::prefix('user/addresses')->group(function () {
        Route::get('/', [UserController::class, 'getUserAddresses']);
        Route::post('/', [UserController::class, 'createUserAddress']);
        Route::get('/{id}', [UserController::class, 'getUserAddress']);
        Route::put('/{id}', [UserController::class, 'updateUserAddress']);
        Route::delete('/{id}', [UserController::class, 'deleteUserAddress']);
        Route::put('/{id}/default', [UserController::class, 'setDefaultAddress']);
    });
});

// 微信小程序认证API
Route::prefix('wechat/mini-program')->group(function () {
    Route::post('/login', [AuthController::class, 'miniProgramLogin']);
    Route::post('/phone', [AuthController::class, 'updatePhone']);
}); 