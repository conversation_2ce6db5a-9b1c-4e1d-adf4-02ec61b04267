<!-- 图片懒加载组件 -->
<view class="lazy-image-container {{customClass}}" style="{{containerStyle}}">
  
  <!-- 占位图 -->
  <view 
    class="lazy-image-placeholder {{(currentSrc && loaded && !error) ? 'hidden' : ''}}" 
    style="{{placeholderStyle}}"
    wx:if="{{showPlaceholder}}"
  >
    <!-- 简化占位内容 -->
    <view class="placeholder-content">
      <van-icon name="photo" size="40rpx" color="#ddd"/>
    </view>
  </view>
  
  <!-- 实际图片 -->
  <image
    class="lazy-image {{currentSrc ? 'show' : 'hide'}} {{rounded ? 'rounded' : ''}}"
    src="{{currentSrc}}"
    mode="{{mode}}"
    style="{{imageStyle}}"
    lazy-load="{{lazyLoad}}"
    show-menu-by-longpress="{{showMenuByLongpress}}"
    bindload="onImageLoad"
    binderror="onImageError"
    bindtap="onImageTap"
  />
  
  <!-- 错误状态 - 使用图标代替错误图片 -->
  <view class="error-container" wx:if="{{error}}">
    <view class="error-icon">
      <van-icon name="photo-fail" size="48rpx" color="#cccccc" />
    </view>
    <text class="error-text" wx:if="{{errorText}}">{{errorText}}</text>
  </view>
  
</view> 