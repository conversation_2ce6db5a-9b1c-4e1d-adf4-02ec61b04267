// pages/points/orders/detail/index.js - 积分订单详情页
const PointsAPI = require('../../../../utils/pointsApi');
const app = getApp();

Page({
  data: {
    // 订单ID
    orderId: '',
    
    // 订单详情
    orderDetail: {},
    
    // 页面加载状态
    loading: true,
    
    // 操作状态
    actionLoading: false
  },

  onLoad(options) {
    const orderId = options.id;
    
    if (!orderId) {
      wx.showToast({
        title: '订单信息错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    this.setData({ orderId });
    this.loadOrderDetail();
  },

  onShow() {
    // 刷新订单状态
    if (this.data.orderId) {
      this.loadOrderDetail();
    }
  },

  onPullDownRefresh() {
    this.loadOrderDetail();
  },

  // ==================== 数据加载 ====================

  async loadOrderDetail() {
    wx.showLoading({ title: '加载中...' });

    try {
      const result = await PointsAPI.getPointsOrderDetail(this.data.orderId);
      this.setData({
        orderDetail: result.data,
        loading: false
      });
    } catch (error) {
      console.error('加载订单详情失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
      wx.stopPullDownRefresh();
    }
  },

  // ==================== 用户操作 ====================

  async onPayOrder() {
    const { orderDetail } = this.data;
    
    wx.showModal({
      title: '支付确认',
      content: `确认支付该订单吗？需要${PointsAPI.formatPoints(orderDetail.total_points)}`,
      success: async (res) => {
        if (res.confirm) {
          try {
            this.setData({ actionLoading: true });
            
            // 这里应该调用支付接口
            // await PointsAPI.payPointsOrder(this.data.orderId, paymentData);
            
            wx.showToast({
              title: '支付功能开发中',
              icon: 'none'
            });
            
            // 刷新订单状态
            this.loadOrderDetail();
          } catch (error) {
            console.error('支付失败:', error);
            wx.showToast({
              title: error.message || '支付失败',
              icon: 'error'
            });
          } finally {
            this.setData({ actionLoading: false });
          }
        }
      }
    });
  },

  async onCancelOrder() {
    wx.showModal({
      title: '取消订单',
      content: '确认要取消这个订单吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            this.setData({ actionLoading: true });
            
            await PointsAPI.cancelPointsOrder(this.data.orderId);
            
            wx.showToast({
              title: '订单已取消',
              icon: 'success'
            });
            
            // 刷新订单状态
            this.loadOrderDetail();
          } catch (error) {
            console.error('取消订单失败:', error);
            wx.showToast({
              title: error.message || '取消失败',
              icon: 'error'
            });
          } finally {
            this.setData({ actionLoading: false });
          }
        }
      }
    });
  },

  async onConfirmDelivery() {
    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            this.setData({ actionLoading: true });
            
            await PointsAPI.confirmPointsOrderDelivery(this.data.orderId);
            
            wx.showToast({
              title: '确认收货成功',
              icon: 'success'
            });
            
            // 刷新订单状态
            this.loadOrderDetail();
          } catch (error) {
            console.error('确认收货失败:', error);
            wx.showToast({
              title: error.message || '确认失败',
              icon: 'error'
            });
          } finally {
            this.setData({ actionLoading: false });
          }
        }
      }
    });
  },

  // ==================== 页面跳转 ====================

  goToProduct(e) {
    const productId = e.currentTarget.dataset.productId;
    wx.navigateTo({
      url: `/pages/points/product/index?id=${productId}`
    });
  },

  copyOrderNo() {
    const orderNo = this.data.orderDetail.order_no;
    
    wx.setClipboardData({
      data: orderNo,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        });
      }
    });
  },

  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    const urls = this.data.orderDetail.items?.map(item => item.image) || [url];
    
    wx.previewImage({
      current: url,
      urls: urls
    });
  },

  // ==================== 工具方法 ====================

  formatPoints(points) {
    return PointsAPI.formatPoints(points);
  },

  formatOrderStatus(status) {
    return PointsAPI.formatOrderStatus(status);
  },

  formatDate(dateString) {
    return PointsAPI.formatDateTime(dateString);
  },

  getOrderStatusColor(status) {
    const colorMap = {
      'pending': '#FF9800',
      'paid': '#2196F3', 
      'shipped': '#9C27B0',
      'completed': '#4CAF50',
      'cancelled': '#F44336'
    };
    return colorMap[status] || '#666';
  },

  getOrderActions(order) {
    const actions = [];
    
    switch (order.status) {
      case 'pending':
        actions.push(
          { key: 'cancel', name: '取消订单', type: 'danger' },
          { key: 'pay', name: '立即支付', type: 'primary' }
        );
        break;
      case 'paid':
        actions.push(
          { key: 'cancel', name: '申请退款', type: 'danger' }
        );
        break;
      case 'shipped':
        actions.push(
          { key: 'confirm', name: '确认收货', type: 'primary' }
        );
        break;
    }
    
    return actions;
  },

  onOrderAction(e) {
    const action = e.currentTarget.dataset.action;
    
    switch (action) {
      case 'pay':
        this.onPayOrder();
        break;
      case 'cancel':
        this.onCancelOrder();
        break;
      case 'confirm':
        this.onConfirmDelivery();
        break;
    }
  }
}); 