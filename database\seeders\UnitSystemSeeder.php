<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Unit\Models\Unit;
use App\Unit\Models\UnitConversionGraph;
use App\Unit\Models\UnitConversionEdge;
use App\Product\Models\Product;
use App\Product\Models\ProductUnit;
use Illuminate\Support\Facades\DB;

class UnitSystemSeeder extends Seeder
{
    /**
     * 运行数据库种子
     * 
     * @return void
     */
    public function run()
    {
        // 暂时禁用外键约束检查
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        try {
            // 创建基础单位
            $this->createBasicUnits();
            
            // 创建转换图
            $this->createConversionGraphs();
            
            // 创建示例产品
            $this->createExampleProducts();
        } finally {
            // 确保无论如何都重新启用外键约束检查
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }
    }
    
    /**
     * 创建基本单位
     */
    private function createBasicUnits()
    {
        // 使用删除而非truncate避免外键约束问题
        Unit::query()->delete();
        
        // 重量单位
        $kg = Unit::create([
            'name' => '千克',
            'display_name' => '公斤',
            'symbol' => 'kg',
            'type' => 'weight',
            'is_base_unit' => true,
            'is_visible' => true,
            'description' => '重量基本单位',
            'sort' => 10
        ]);
        
        Unit::create([
            'name' => '克',
            'display_name' => '克',
            'symbol' => 'g',
            'type' => 'weight',
            'is_base_unit' => false,
            'is_visible' => true,
            'description' => '重量单位，1/1000千克',
            'sort' => 20,
            'base_unit_id' => $kg->id
        ]);
        
        Unit::create([
            'name' => '吨',
            'display_name' => '吨',
            'symbol' => 't',
            'type' => 'weight',
            'is_base_unit' => false,
            'is_visible' => true,
            'description' => '重量单位，1000千克',
            'sort' => 30,
            'base_unit_id' => $kg->id
        ]);
        
        // 长度单位
        $m = Unit::create([
            'name' => '米',
            'display_name' => '米',
            'symbol' => 'm',
            'type' => 'length',
            'is_base_unit' => true,
            'is_visible' => true,
            'description' => '长度基本单位',
            'sort' => 10
        ]);
        
        Unit::create([
            'name' => '厘米',
            'display_name' => '厘米',
            'symbol' => 'cm',
            'type' => 'length',
            'is_base_unit' => false,
            'is_visible' => true,
            'description' => '长度单位，1/100米',
            'sort' => 20,
            'base_unit_id' => $m->id
        ]);
        
        Unit::create([
            'name' => '毫米',
            'display_name' => '毫米',
            'symbol' => 'mm',
            'type' => 'length',
            'is_base_unit' => false,
            'is_visible' => true,
            'description' => '长度单位，1/1000米',
            'sort' => 30,
            'base_unit_id' => $m->id
        ]);
        
        // 数量单位
        $pcs = Unit::create([
            'name' => '件',
            'display_name' => '件',
            'symbol' => 'pcs',
            'type' => 'quantity',
            'is_base_unit' => true,
            'is_visible' => true,
            'description' => '数量基本单位',
            'sort' => 10
        ]);
        
        Unit::create([
            'name' => '打',
            'display_name' => '打',
            'symbol' => 'dozen',
            'type' => 'quantity',
            'is_base_unit' => false,
            'is_visible' => true,
            'description' => '数量单位，12件',
            'sort' => 20,
            'base_unit_id' => $pcs->id
        ]);
        
        Unit::create([
            'name' => '盒',
            'display_name' => '盒',
            'symbol' => 'box',
            'type' => 'quantity',
            'is_base_unit' => false,
            'is_visible' => true,
            'description' => '包装单位',
            'sort' => 30,
            'base_unit_id' => $pcs->id
        ]);
    }
    
    /**
     * 创建转换图
     */
    private function createConversionGraphs()
    {
        // 使用删除而非truncate避免外键约束问题
        UnitConversionEdge::query()->delete();
        UnitConversionGraph::query()->delete();
        
        // 重量转换图
        $weightGraph = UnitConversionGraph::create([
            'name' => '标准重量转换',
            'type' => 'weight',
            'description' => '标准重量单位转换图',
            'is_default' => true,
            'is_active' => true,
        ]);
        
        // 重量单位查询
        $kg = Unit::where('symbol', 'kg')->where('type', 'weight')->first();
        $g = Unit::where('symbol', 'g')->where('type', 'weight')->first();
        $t = Unit::where('symbol', 't')->where('type', 'weight')->first();
        
        // 创建转换边
        if ($kg && $g) {
            UnitConversionEdge::create([
                'graph_id' => $weightGraph->id,
                'from_unit_id' => $kg->id,
                'to_unit_id' => $g->id,
                'conversion_factor' => 1000, // 1kg = 1000g
                'is_bidirectional' => true,
                'description' => '千克到克的转换',
                'is_active' => true,
            ]);
        }
        
        if ($kg && $t) {
            UnitConversionEdge::create([
                'graph_id' => $weightGraph->id,
                'from_unit_id' => $kg->id,
                'to_unit_id' => $t->id,
                'conversion_factor' => 0.001, // 1kg = 0.001t
                'is_bidirectional' => true,
                'description' => '千克到吨的转换',
                'is_active' => true,
            ]);
        }
        
        // 长度转换图
        $lengthGraph = UnitConversionGraph::create([
            'name' => '标准长度转换',
            'type' => 'length',
            'description' => '标准长度单位转换图',
            'is_default' => true,
            'is_active' => true,
        ]);
        
        // 长度单位查询
        $m = Unit::where('symbol', 'm')->where('type', 'length')->first();
        $cm = Unit::where('symbol', 'cm')->where('type', 'length')->first();
        $mm = Unit::where('symbol', 'mm')->where('type', 'length')->first();
        
        // 创建转换边
        if ($m && $cm) {
            UnitConversionEdge::create([
                'graph_id' => $lengthGraph->id,
                'from_unit_id' => $m->id,
                'to_unit_id' => $cm->id,
                'conversion_factor' => 100, // 1m = 100cm
                'is_bidirectional' => true,
                'description' => '米到厘米的转换',
                'is_active' => true,
            ]);
        }
        
        if ($m && $mm) {
            UnitConversionEdge::create([
                'graph_id' => $lengthGraph->id,
                'from_unit_id' => $m->id,
                'to_unit_id' => $mm->id,
                'conversion_factor' => 1000, // 1m = 1000mm
                'is_bidirectional' => true,
                'description' => '米到毫米的转换',
                'is_active' => true,
            ]);
        }
        
        // 数量转换图
        $quantityGraph = UnitConversionGraph::create([
            'name' => '标准数量转换',
            'type' => 'quantity',
            'description' => '标准数量单位转换图',
            'is_default' => true,
            'is_active' => true,
        ]);
        
        // 数量单位查询
        $pcs = Unit::where('symbol', 'pcs')->where('type', 'quantity')->first();
        $dozen = Unit::where('symbol', 'dozen')->where('type', 'quantity')->first();
        $box = Unit::where('symbol', 'box')->where('type', 'quantity')->first();
        
        // 创建转换边
        if ($pcs && $dozen) {
            UnitConversionEdge::create([
                'graph_id' => $quantityGraph->id,
                'from_unit_id' => $dozen->id,
                'to_unit_id' => $pcs->id,
                'conversion_factor' => 12, // 1打 = 12件
                'is_bidirectional' => true,
                'description' => '打到件的转换',
                'is_active' => true,
            ]);
        }
        
        if ($pcs && $box) {
            UnitConversionEdge::create([
                'graph_id' => $quantityGraph->id,
                'from_unit_id' => $box->id,
                'to_unit_id' => $pcs->id,
                'conversion_factor' => 10, // 默认1盒 = 10件
                'is_bidirectional' => true,
                'description' => '盒到件的转换',
                'is_active' => true,
            ]);
        }
    }
    
    /**
     * 创建示例产品
     */
    private function createExampleProducts()
    {
        // 只为示例商品清除product_units数据，不影响其他商品
        $product = Product::firstOrNew(['name' => '测试商品-单位系统']);
        
        if ($product->exists) {
            ProductUnit::where('product_id', $product->id)->delete();
        } else {
            $kg = Unit::where('symbol', 'kg')->first();
            
            if (!$kg) {
                return;
            }
            
            $product->fill([
                'code' => 'TEST-UNIT-001',
                'price' => 100,
                'description' => '用于测试单位系统的商品',
                'stock' => 0,
                'base_unit_id' => $kg->id,
                'status' => 1,
            ]);
            
            $product->save();
        }
        
        // 添加产品单位关联
        // 获取单位
        $kg = Unit::where('symbol', 'kg')->first();
        $g = Unit::where('symbol', 'g')->first();
        $pcs = Unit::where('symbol', 'pcs')->first();
        $box = Unit::where('symbol', 'box')->first();
        
        if ($kg && $product) {
            // 添加基本单位关联（kg）
            ProductUnit::updateOrCreate(
                ['product_id' => $product->id, 'unit_id' => $kg->id],
                [
                    'conversion_factor' => 1.0,
                    'roles' => ['inventory', 'purchase'],
                    'role_priority' => ['inventory' => 10, 'purchase' => 20],
                    'is_default' => true,
                    'is_active' => true,
                ]
            );
        }
        
        if ($g && $product) {
            // 添加辅助单位关联（g）
            ProductUnit::updateOrCreate(
                ['product_id' => $product->id, 'unit_id' => $g->id],
                [
                    'conversion_factor' => 0.001, // 1g = 0.001kg
                    'roles' => ['sales'],
                    'role_priority' => ['sales' => 10],
                    'is_default' => false,
                    'is_active' => true,
                ]
            );
        }
        
        if ($pcs && $product) {
            // 添加辅助单位关联（pcs）- 产品特定转换率
            ProductUnit::updateOrCreate(
                ['product_id' => $product->id, 'unit_id' => $pcs->id],
                [
                    'conversion_factor' => 0.25, // 假设1件 = 0.25kg
                    'roles' => ['sales', 'inventory'],
                    'role_priority' => ['sales' => 20, 'inventory' => 20],
                    'is_default' => false,
                    'is_active' => true,
                ]
            );
        }
        
        if ($box && $product) {
            // 添加辅助单位关联（box）
            ProductUnit::updateOrCreate(
                ['product_id' => $product->id, 'unit_id' => $box->id],
                [
                    'conversion_factor' => 2.5, // 假设1盒 = 2.5kg
                    'roles' => ['purchase', 'sales'],
                    'role_priority' => ['purchase' => 10, 'sales' => 30],
                    'is_default' => false,
                    'is_active' => true,
                ]
            );
        }
    }
} 