<?php

use App\Upload\Controllers\UploadController;
use App\Http\Controllers\UploadExampleController;
use App\Upload\Controllers\UploadConfigController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Upload API 路由
|--------------------------------------------------------------------------
|
| Upload模块的API路由定义
|
*/

// 上传路由组
Route::prefix('upload')->group(function () {
    // 通用图片上传
    Route::post('/image', [UploadController::class, 'uploadImage']);
    
    // 特定类型图片上传
    Route::post('/product', [UploadController::class, 'uploadProductImage']);
    Route::post('/banner', [UploadController::class, 'uploadBannerImage']);
    Route::post('/category', [UploadController::class, 'uploadCategoryImage']);
    Route::post('/avatar', [UploadController::class, 'uploadAvatarImage']);
});

// 上传示例路由
Route::prefix('upload-example')->group(function () {
    // 通用上传接口
    Route::post('/image', [UploadExampleController::class, 'uploadImage']);
    
    // 特定类型上传
    Route::post('/banner', [UploadExampleController::class, 'uploadBanner']);
    
    // 指定COS存储的商品图片上传
    Route::post('/product-cos', [UploadExampleController::class, 'uploadProductWithCos']);
    
    // 商品多图片上传
    Route::post('/product-images', [UploadExampleController::class, 'uploadProductImages']);
});

Route::middleware(['api'])->prefix('api')->group(function () {
    // 获取上传配置
    Route::get('/admin/upload/config', [UploadConfigController::class, 'getConfig'])->name('upload.config.get');
    
    // 保存上传配置
    Route::post('/admin/upload/config', [UploadConfigController::class, 'saveConfig'])->name('upload.config.save');
}); 