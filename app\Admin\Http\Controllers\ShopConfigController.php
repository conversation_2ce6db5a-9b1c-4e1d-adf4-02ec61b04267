<?php

namespace App\Admin\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\ShopConfig;
use App\shop\Services\ConfigService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ShopConfigController extends Controller
{
    /**
     * @var ConfigService
     */
    protected $configService;

    /**
     * 构造函数
     */
    public function __construct(ConfigService $configService)
    {
        $this->configService = $configService;
    }

    /**
     * 获取系统配置列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $group = $request->input('group', 'general');
        
        $configs = ShopConfig::where('group', $group)
            ->orderBy('sort_order')
            ->get();
            
        return response()->json(ApiResponse::success($configs));
    }

    /**
     * 获取配置分组
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function groups()
    {
        $groups = ShopConfig::select('group')
            ->distinct()
            ->pluck('group')
            ->toArray();
            
        return response()->json(ApiResponse::success($groups));
    }

    /**
     * 获取单个配置
     *
     * @param string $key
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $key)
    {
        $config = ShopConfig::where('key', $key)->first();
        
        if (!$config) {
            return response()->json(ApiResponse::error('配置不存在', 404), 404);
        }
        
        return response()->json(ApiResponse::success($config));
    }

    /**
     * 保存配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'key' => 'required|string|max:100|unique:shop_configs,key',
            'value' => 'nullable',
            'group' => 'required|string|max:50',
            'title' => 'required|string|max:100',
            'description' => 'nullable|string',
            'type' => 'required|string|max:30',
            'options' => 'nullable|json',
            'is_system' => 'boolean',
            'sort_order' => 'integer',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        $config = new ShopConfig($request->all());
        $config->save();
        
        // 清除缓存
        $this->configService->clearCache($config->key);
        
        return response()->json(ApiResponse::success($config, '配置创建成功'));
    }

    /**
     * 更新配置
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, int $id)
    {
        $config = ShopConfig::find($id);
        
        if (!$config) {
            return response()->json(ApiResponse::error('配置不存在', 404), 404);
        }
        
        $validator = Validator::make($request->all(), [
            'key' => "required|string|max:100|unique:shop_configs,key,{$id}",
            'value' => 'nullable',
            'group' => 'required|string|max:50',
            'title' => 'required|string|max:100',
            'description' => 'nullable|string',
            'type' => 'required|string|max:30',
            'options' => 'nullable|json',
            'is_system' => 'boolean',
            'sort_order' => 'integer',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $oldKey = $config->key;
        $config->fill($request->all());
        $config->save();
        
        // 清除缓存
        $this->configService->clearCache($oldKey);
        if ($oldKey != $config->key) {
            $this->configService->clearCache($config->key);
        }
        
        return response()->json(ApiResponse::success($config, '配置更新成功'));
    }

    /**
     * 删除配置
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(int $id)
    {
        $config = ShopConfig::find($id);
        
        if (!$config) {
            return response()->json(ApiResponse::error('配置不存在', 404), 404);
        }
        
        // 不允许删除系统配置
        if ($config->is_system) {
            return response()->json(ApiResponse::error('不能删除系统配置', 403), 403);
        }
        
        $key = $config->key;
        $config->delete();
        
        // 清除缓存
        $this->configService->clearCache($key);
        
        return response()->json(ApiResponse::success(null, '配置删除成功'));
    }

    /**
     * 批量保存配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchSave(Request $request)
    {
        $configs = $request->input('configs', []);
        
        foreach ($configs as $item) {
            if (empty($item['key'])) {
                continue;
            }
            
            $this->configService->set($item['key'], $item['value'] ?? null);
        }
        
        return response()->json(ApiResponse::success(null, '配置保存成功'));
    }
    
    /**
     * 获取微信小程序配置
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWechatMiniProgramConfig()
    {
        $config = $this->configService->getWechatMiniProgramConfig();
        
        return response()->json(ApiResponse::success($config));
    }
    
    /**
     * 保存微信小程序配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveWechatMiniProgramConfig(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'wx_mini_program_app_id' => 'required|string',
            'wx_mini_program_secret' => 'required|string',
            'wx_mini_program_token' => 'nullable|string',
            'wx_mini_program_aes_key' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $this->configService->saveWechatMiniProgramConfig($request->all());
        
        return response()->json(ApiResponse::success(null, '微信小程序配置保存成功'));
    }
} 