<?php

namespace App\Unit\Services;

use App\Product\Models\Product;
use App\Unit\Models\Unit;
use App\Unit\Models\ProductUnit;
use App\Unit\Models\UnitConversionGraph;
use App\Unit\Models\UnitConversionEdge;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProductUnitService
{
    /**
     * 单位服务
     * 
     * @var UnitService
     */
    protected $unitService;
    
    /**
     * 构造函数
     * 
     * @param UnitService $unitService
     */
    public function __construct(UnitService $unitService)
    {
        $this->unitService = $unitService;
    }
    
    /**
     * 获取产品的所有单位
     * 
     * @param Product $product
     * @return Collection
     */
    public function getProductUnits(Product $product): Collection
    {
        return ProductUnit::with('unit')
            ->where('product_id', $product->id)
            ->orderBy('is_default', 'desc')
            ->get();
    }
    
    /**
     * 获取产品的特定角色单位
     * 
     * @param Product $product
     * @param string $role 角色名称
     * @return Collection
     */
    public function getProductUnitsByRole(Product $product, string $role): Collection
    {
        $units = ProductUnit::with('unit')
            ->where('product_id', $product->id)
            ->get();
            
        return $units->filter(function($unit) use ($role) {
            return $unit->hasRole($role);
        })->sortBy(function($unit) use ($role) {
            return $unit->getRolePriority($role);
        });
    }
    
    /**
     * 获取产品角色的主要单位
     * 
     * @param Product $product
     * @param string $role 角色名称
     * @return ProductUnit|null
     */
    public function getPrimaryUnitByRole(Product $product, string $role): ?ProductUnit
    {
        $units = $this->getProductUnitsByRole($product, $role);
        return $units->first();
    }
    
    /**
     * 添加产品单位
     *
     * @param Product $product 产品
     * @param Unit $unit 单位
     * @param float $conversionFactor 转换系数
     * @param array $roles 角色数组
     * @param array $rolePriority 角色优先级
     * @param bool $isDefault 是否默认
     * @param bool $isActive 是否启用
     * @return ProductUnit
     */
    public function addProductUnit(
        Product $product,
        Unit $unit,
        float $conversionFactor,
        array $roles = [],
        array $rolePriority = [],
        bool $isDefault = false,
        bool $isActive = true
    ): ProductUnit {
        try {
            // 检查是否已存在
            $existingUnit = ProductUnit::where('product_id', $product->id)
                ->where('unit_id', $unit->id)
                ->first();
            
            if ($existingUnit) {
                return $existingUnit;
            }
            
            // 创建新的单位关联
            $productUnit = new ProductUnit();
            $productUnit->product_id = $product->id;
            $productUnit->unit_id = $unit->id;
            $productUnit->conversion_factor = $conversionFactor;
            $productUnit->roles = $roles;
            $productUnit->role_priority = $rolePriority;
            $productUnit->is_default = $isDefault;
            $productUnit->is_active = $isActive;
            $productUnit->save();
            
            // 如果设置为默认，需要取消其他同角色的默认设置
            if ($isDefault && !empty($roles)) {
                $this->updateDefaultForRoles($product->id, $unit->id, $roles);
            }
            
            return $productUnit;
        } catch (\Exception $e) {
            Log::error('添加产品单位失败', [
                'product_id' => $product->id,
                'unit_id' => $unit->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * 更新产品单位
     * 
     * @param ProductUnit $productUnit
     * @param array $data
     * @return ProductUnit
     */
    public function updateProductUnit(ProductUnit $productUnit, array $data): ProductUnit
    {
        return DB::transaction(function() use ($productUnit, $data) {
            // 如果设置为默认，先将其他单位设为非默认
            if (isset($data['is_default']) && $data['is_default']) {
                ProductUnit::where('product_id', $productUnit->product_id)
                    ->where('id', '!=', $productUnit->id)
                    ->update(['is_default' => false]);
            }
            
            $productUnit->update($data);
            return $productUnit;
        });
    }
    
    /**
     * 删除产品单位
     * 
     * @param ProductUnit $productUnit
     * @return bool
     */
    public function deleteProductUnit(ProductUnit $productUnit): bool
    {
        // 不允许删除默认单位
        if ($productUnit->is_default) {
            throw new \Exception('不能删除默认单位');
        }
        
        return $productUnit->delete();
    }
    
    /**
     * 设置产品默认单位
     * 
     * @param Product $product
     * @param Unit $unit
     * @return ProductUnit
     */
    public function setDefaultUnit(Product $product, Unit $unit): ProductUnit
    {
        return DB::transaction(function() use ($product, $unit) {
            // 先将所有单位设为非默认
            ProductUnit::where('product_id', $product->id)
                ->update(['is_default' => false]);
                
            // 查找指定单位关联
            $productUnit = ProductUnit::where('product_id', $product->id)
                ->where('unit_id', $unit->id)
                ->first();
                
            if (!$productUnit) {
                throw new \Exception('产品未关联此单位');
            }
            
            $productUnit->update(['is_default' => true]);
            return $productUnit;
        });
    }
    
    /**
     * 设置产品的所有单位
     *
     * @param Product $product 产品
     * @param array $unitsData 单位数据数组
     * @return array 设置结果
     */
    public function setProductUnits(Product $product, array $unitsData): array
    {
        try {
            DB::beginTransaction();
            
            // 获取新的单位ID列表
            $newUnitIds = array_column($unitsData, 'unit_id');
            
            // 删除不在新列表中的单位关联（排除基本单位）
            ProductUnit::where('product_id', $product->id)
                ->whereNotIn('unit_id', array_merge($newUnitIds, [$product->base_unit_id]))
                ->delete();
            
            $results = [];
            
            // 处理每个单位数据
            foreach ($unitsData as $unitData) {
                $unitId = $unitData['unit_id'];
                
                // 跳过基本单位
                if ($unitId == $product->base_unit_id) {
                    continue;
                }
                
                $unit = Unit::find($unitId);
                if (!$unit) {
                    continue;
                }
                
                $conversionFactor = $unitData['conversion_factor'];
                $roles = $unitData['roles'] ?? [];
                $rolePriority = $unitData['role_priority'] ?? [];
                $isDefault = $unitData['is_default'] ?? false;
                $isActive = $unitData['is_active'] ?? true;
                
                // 查找现有关联
                $productUnit = ProductUnit::where('product_id', $product->id)
                    ->where('unit_id', $unitId)
                    ->first();
                
                if ($productUnit) {
                    // 更新现有关联
                    $productUnit->conversion_factor = $conversionFactor;
                    $productUnit->roles = $roles;
                    $productUnit->role_priority = $rolePriority;
                    $productUnit->is_default = $isDefault;
                    $productUnit->is_active = $isActive;
                    $productUnit->save();
                    
                    $results[] = [
                        'unit_id' => $unitId,
                        'status' => 'updated',
                        'product_unit' => $productUnit
                    ];
                } else {
                    // 创建新关联
                    $productUnit = $this->addProductUnit(
                        $product,
                        $unit,
                        $conversionFactor,
                        $roles,
                        $rolePriority,
                        $isDefault,
                        $isActive
                    );
                    
                    $results[] = [
                        'unit_id' => $unitId,
                        'status' => 'created',
                        'product_unit' => $productUnit
                    ];
                }
                
                // 如果设置为默认，更新其他单位的默认状态
                if ($isDefault && !empty($roles)) {
                    $this->updateDefaultForRoles($product->id, $unitId, $roles);
                }
            }
            
            DB::commit();
            return $results;
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('设置产品单位失败', [
                'product_id' => $product->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * 更新特定角色的默认单位
     *
     * @param int $productId 产品ID
     * @param int $unitId 新默认单位ID
     * @param array $roles 角色数组
     * @return void
     */
    private function updateDefaultForRoles(int $productId, int $unitId, array $roles): void
    {
        foreach ($roles as $role) {
            ProductUnit::where('product_id', $productId)
                ->where('unit_id', '!=', $unitId)
                ->whereJsonContains('roles', $role)
                ->update(['is_default' => false]);
        }
    }
    
    /**
     * 获取特定角色的最佳单位
     *
     * @param Product $product 产品
     * @param string $role 角色名称
     * @return ProductUnit|null 最佳单位或null
     */
    public function getBestUnitForRole(Product $product, string $role): ?ProductUnit
    {
        // 首先检查标记为默认的单位
        $defaultUnit = ProductUnit::where('product_id', $product->id)
            ->where('is_active', true)
            ->where('is_default', true)
            ->whereJsonContains('roles', $role)
            ->first();
            
        if ($defaultUnit) {
            return $defaultUnit;
        }
        
        // 然后按优先级排序
        $units = ProductUnit::where('product_id', $product->id)
            ->where('is_active', true)
            ->whereJsonContains('roles', $role)
            ->get();
            
        if ($units->isEmpty()) {
            // 如果没有找到，使用基本单位
            return ProductUnit::where('product_id', $product->id)
                ->where('unit_id', $product->base_unit_id)
                ->first();
        }
        
        // 按照角色优先级排序
        $sortedUnits = $units->sortBy(function ($unit) use ($role) {
            return $unit->role_priority[$role] ?? 999;
        });
        
        return $sortedUnits->first();
    }
    
    /**
     * 转换产品数量
     * 
     * @param Product $product
     * @param float $quantity
     * @param Unit $fromUnit
     * @param Unit $toUnit
     * @return float
     */
    public function convertProductQuantity(Product $product, float $quantity, Unit $fromUnit, Unit $toUnit): float
    {
        // 如果单位相同，直接返回原值
        if ($fromUnit->id === $toUnit->id) {
            return $quantity;
        }
        
        // 查找产品特定的转换关系
        $fromProductUnit = ProductUnit::where('product_id', $product->id)
            ->where('unit_id', $fromUnit->id)
            ->first();
            
        $toProductUnit = ProductUnit::where('product_id', $product->id)
            ->where('unit_id', $toUnit->id)
            ->first();
            
        if ($fromProductUnit && $toProductUnit) {
            // 找到产品特定的转换关系，通过基本单位转换
            // 先转换到基本单位再转换到目标单位
            $quantityInBase = $quantity * $fromProductUnit->conversion_factor;
            return $quantityInBase / $toProductUnit->conversion_factor;
        }
        
        // 使用图论转换
        $unitType = $fromUnit->type;
        if ($fromUnit->type !== $toUnit->type) {
            throw new \Exception("无法在不同类型的单位之间进行转换：{$fromUnit->type} → {$toUnit->type}");
        }
        
        // 获取默认转换图
        $graph = UnitConversionGraph::where('type', $unitType)
                            ->where('is_default', true)
                            ->first();
        
        if (!$graph) {
            throw new \Exception("找不到类型 {$unitType} 的默认转换图");
        }
        
        return $this->unitService->convertUsingGraph($quantity, $fromUnit, $toUnit, $graph);
    }
} 