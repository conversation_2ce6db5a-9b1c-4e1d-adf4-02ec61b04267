<?php

namespace App\Warehouse\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Inventory\Models\Inventory;
use App\Inventory\Models\InventoryBatch;
use App\Product\Models\Product;
use App\Warehouse\Services\InventoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use App\Inventory\Models\InventoryTransactionType;

class InventorySystemController extends Controller
{
    /**
     * 检查和修复库存单位
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkAndRepairUnits(Request $request)
    {
        try {
            // 记录开始修复
            Log::info('开始检查和修复库存单位', [
                'triggered_by' => $request->user() ? $request->user()->id : 'unknown',
                'user_ip' => $request->ip()
            ]);
            
            $inventoryService = new InventoryService();
            $result = $inventoryService->checkAndRepairInventoryUnits();
            
            // 记录修复结果
            Log::info('库存单位检查和修复完成', $result);
            
            return response()->json(ApiResponse::success($result, '库存单位检查和修复完成'));
        } catch (\Exception $e) {
            Log::error('库存单位检查和修复失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ApiResponse::error('库存单位检查和修复失败: ' . $e->getMessage(), 500),
                500
            );
        }
    }
    
    /**
     * 获取库存系统概览
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSystemOverview()
    {
        try {
            // 总库存数
            $totalInventories = Inventory::count();
            
            // 总批次数
            $totalBatches = InventoryBatch::count();
            
            // 库存为0的商品数
            $zeroStockProducts = Product::where('stock', 0)->count();
            
            // 总商品数
            $totalProducts = Product::count();
            
            // 库存单位不一致的记录数
            $inconsistentUnits = 0;
            $inventories = Inventory::with('product')->get();
            foreach ($inventories as $inventory) {
                if ($inventory->product && $inventory->unit_id !== $inventory->product->base_unit_id) {
                    $inconsistentUnits++;
                }
            }
            
            $result = [
                'total_inventories' => $totalInventories,
                'total_batches' => $totalBatches,
                'total_products' => $totalProducts,
                'zero_stock_products' => $zeroStockProducts,
                'zero_stock_percentage' => $totalProducts > 0 ? round(($zeroStockProducts / $totalProducts) * 100, 2) : 0,
                'inconsistent_units' => $inconsistentUnits,
                'system_time' => now()->toDateTimeString(),
                'needs_repair' => $inconsistentUnits > 0
            ];
            
            return response()->json(ApiResponse::success($result, '库存系统概览'));
        } catch (\Exception $e) {
            Log::error('获取库存系统概览失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ApiResponse::error('获取库存系统概览失败: ' . $e->getMessage(), 500),
                500
            );
        }
    }
    
    /**
     * 运行库存单位转换命令
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function runConversionCommand(Request $request)
    {
        try {
            // 记录开始运行命令
            Log::info('开始运行库存单位转换命令', [
                'triggered_by' => $request->user() ? $request->user()->id : 'unknown',
                'user_ip' => $request->ip()
            ]);
            
            // 运行命令
            Artisan::call('inventory:convert-units');
            
            // 获取命令输出
            $output = Artisan::output();
            
            // 记录命令完成
            Log::info('库存单位转换命令完成', [
                'output' => $output
            ]);
            
            return response()->json(ApiResponse::success([
                'output' => $output
            ], '库存单位转换命令已执行'));
        } catch (\Exception $e) {
            Log::error('运行库存单位转换命令失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ApiResponse::error('运行库存单位转换命令失败: ' . $e->getMessage(), 500),
                500
            );
        }
    }
    
    /**
     * 获取库存事务类型列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTransactionTypes()
    {
        try {
            $types = InventoryTransactionType::all();
            
            // 修改响应格式，使其与前端期望的格式一致
            return response()->json([
                'data' => $types
            ]);
        } catch (\Exception $e) {
            Log::error('获取库存事务类型列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ApiResponse::error('获取库存事务类型列表失败: ' . $e->getMessage(), 500),
                500
            );
        }
    }
} 