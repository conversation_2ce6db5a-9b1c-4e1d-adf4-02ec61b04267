<?php

use App\Crm\Http\Controllers\UserController;
use App\Crm\Http\Controllers\CrmAgentController;
use App\Crm\Http\Controllers\ClientAssignmentController;
use App\Crm\Http\Controllers\ClientFollowUpController;
use App\Crm\Http\Controllers\MembershipLevelController;
use App\Crm\Http\Controllers\FeedbackController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| CRM Web 路由
|--------------------------------------------------------------------------
|
| CRM模块的Web路由定义
|
*/

// CRM管理Web路由
Route::group(['prefix' => 'admin/crm', 'middleware' => ['web', 'auth']], function () {
    // 用户管理
    Route::get('/users', [UserController::class, 'index'])->name('crm.users.index');
    Route::get('/users/create', [UserController::class, 'create'])->name('crm.users.create');
    Route::post('/users', [UserController::class, 'store'])->name('crm.users.store');
    Route::get('/users/{id}', [UserController::class, 'show'])->name('crm.users.show');
    Route::get('/users/{id}/edit', [UserController::class, 'edit'])->name('crm.users.edit');
    Route::put('/users/{id}', [UserController::class, 'update'])->name('crm.users.update');
    Route::delete('/users/{id}', [UserController::class, 'destroy'])->name('crm.users.destroy');
    
    // 会员等级管理
    Route::get('/membership-levels', [MembershipLevelController::class, 'index'])->name('crm.membership-levels.index');
    Route::get('/membership-levels/create', [MembershipLevelController::class, 'create'])->name('crm.membership-levels.create');
    Route::post('/membership-levels', [MembershipLevelController::class, 'store'])->name('crm.membership-levels.store');
    Route::get('/membership-levels/{id}', [MembershipLevelController::class, 'show'])->name('crm.membership-levels.show');
    Route::get('/membership-levels/{id}/edit', [MembershipLevelController::class, 'edit'])->name('crm.membership-levels.edit');
    Route::put('/membership-levels/{id}', [MembershipLevelController::class, 'update'])->name('crm.membership-levels.update');
    Route::delete('/membership-levels/{id}', [MembershipLevelController::class, 'destroy'])->name('crm.membership-levels.destroy');
    
    // CRM专员管理
    Route::get('/agents', [CrmAgentController::class, 'index'])->name('crm.agents.index');
    Route::get('/agents/create', [CrmAgentController::class, 'create'])->name('crm.agents.create');
    Route::post('/agents', [CrmAgentController::class, 'store'])->name('crm.agents.store');
    Route::get('/agents/{id}', [CrmAgentController::class, 'show'])->name('crm.agents.show');
    Route::get('/agents/{id}/edit', [CrmAgentController::class, 'edit'])->name('crm.agents.edit');
    Route::put('/agents/{id}', [CrmAgentController::class, 'update'])->name('crm.agents.update');
    Route::delete('/agents/{id}', [CrmAgentController::class, 'destroy'])->name('crm.agents.destroy');
    
    // 客户分配管理
    Route::get('/client-assignments', [ClientAssignmentController::class, 'index'])->name('crm.client-assignments.index');
    Route::get('/client-assignments/assign', [ClientAssignmentController::class, 'assignForm'])->name('crm.client-assignments.assign-form');
    Route::post('/client-assignments/assign', [ClientAssignmentController::class, 'assign'])->name('crm.client-assignments.assign');
    Route::get('/client-assignments/batch-assign', [ClientAssignmentController::class, 'batchAssignForm'])->name('crm.client-assignments.batch-assign-form');
    Route::post('/client-assignments/batch-assign', [ClientAssignmentController::class, 'batchAssign'])->name('crm.client-assignments.batch-assign');
    
    // 客户跟进记录
    Route::get('/follow-ups', [ClientFollowUpController::class, 'index'])->name('crm.follow-ups.index');
    Route::get('/follow-ups/create', [ClientFollowUpController::class, 'create'])->name('crm.follow-ups.create');
    Route::post('/follow-ups', [ClientFollowUpController::class, 'store'])->name('crm.follow-ups.store');
    Route::get('/follow-ups/{id}', [ClientFollowUpController::class, 'show'])->name('crm.follow-ups.show');
    Route::get('/follow-ups/{id}/edit', [ClientFollowUpController::class, 'edit'])->name('crm.follow-ups.edit');
    Route::put('/follow-ups/{id}', [ClientFollowUpController::class, 'update'])->name('crm.follow-ups.update');
    Route::delete('/follow-ups/{id}', [ClientFollowUpController::class, 'destroy'])->name('crm.follow-ups.destroy');
    
    // 客户反馈管理
    Route::get('/feedback', [FeedbackController::class, 'index'])->name('crm.feedback.index');
    Route::get('/feedback/{id}', [FeedbackController::class, 'show'])->name('crm.feedback.show');
    Route::get('/feedback/{id}/reply', [FeedbackController::class, 'replyForm'])->name('crm.feedback.reply-form');
    Route::post('/feedback/{id}/reply', [FeedbackController::class, 'reply'])->name('crm.feedback.reply');
    Route::put('/feedback/{id}/resolve', [FeedbackController::class, 'resolve'])->name('crm.feedback.resolve');
    Route::put('/feedback/{id}/close', [FeedbackController::class, 'close'])->name('crm.feedback.close');
}); 