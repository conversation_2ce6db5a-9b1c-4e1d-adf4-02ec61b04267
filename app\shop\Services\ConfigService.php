<?php

namespace App\shop\Services;

use App\Models\ShopConfig;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

class ConfigService
{
    /**
     * 缓存标识前缀
     */
    const CACHE_PREFIX = 'shop_config:';
    
    /**
     * 缓存过期时间（秒）
     */
    const CACHE_DURATION = 86400; // 24小时
    
    /**
     * 获取配置值
     *
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @param bool $useCache 是否使用缓存
     * @return mixed
     */
    public function get(string $key, $default = null, bool $useCache = true)
    {
        if ($useCache) {
            return Cache::remember(self::CACHE_PREFIX . $key, self::CACHE_DURATION, function() use ($key, $default) {
                return ShopConfig::getConfig($key, $default);
            });
        }
        
        return ShopConfig::getConfig($key, $default);
    }
    
    /**
     * 设置配置值
     *
     * @param string $key 配置键名
     * @param mixed $value 配置值
     * @param array $attributes 其他属性
     * @return \App\Models\ShopConfig
     */
    public function set(string $key, $value, array $attributes = [])
    {
        $config = ShopConfig::setConfig($key, $value, $attributes);
        
        // 更新缓存
        Cache::put(self::CACHE_PREFIX . $key, $value, self::CACHE_DURATION);
        
        return $config;
    }
    
    /**
     * 获取分组配置
     *
     * @param string $group 分组名称
     * @param bool $useCache 是否使用缓存
     * @return \Illuminate\Support\Collection
     */
    public function getGroup(string $group, bool $useCache = true)
    {
        $cacheKey = self::CACHE_PREFIX . 'group:' . $group;
        
        if ($useCache) {
            return Cache::remember($cacheKey, self::CACHE_DURATION, function() use ($group) {
                return ShopConfig::getGroupConfigs($group);
            });
        }
        
        return ShopConfig::getGroupConfigs($group);
    }
    
    /**
     * 清除配置缓存
     *
     * @param string|null $key 配置键名，为null则清除所有缓存
     * @return void
     */
    public function clearCache(?string $key = null)
    {
        if ($key) {
            Cache::forget(self::CACHE_PREFIX . $key);
        } else {
            // 清除所有配置缓存的方法取决于缓存驱动
            // 简单实现可以逐个删除常用键
            $keys = ShopConfig::pluck('key')->toArray();
            foreach ($keys as $k) {
                Cache::forget(self::CACHE_PREFIX . $k);
            }
            
            // 清除分组缓存
            $groups = ShopConfig::distinct()->pluck('group')->toArray();
            foreach ($groups as $g) {
                Cache::forget(self::CACHE_PREFIX . 'group:' . $g);
            }
        }
    }
    
    /**
     * 获取微信小程序配置
     *
     * @param bool $useCache 是否使用缓存
     * @return array
     */
    public function getWechatMiniProgramConfig(bool $useCache = true)
    {
        // 添加日志记录
        \Illuminate\Support\Facades\Log::info('尝试获取微信小程序配置', [
            'useCache' => $useCache,
            'cacheKey' => self::CACHE_PREFIX . 'group:wechat_mini_program'
        ]);
        
        // 强制清除缓存
        if ($useCache) {
            \Illuminate\Support\Facades\Cache::forget(self::CACHE_PREFIX . 'group:wechat_mini_program');
        }
        
        $configs = $this->getGroup('wechat_mini_program', false); // 强制不使用缓存
        
        // 记录查询结果
        \Illuminate\Support\Facades\Log::info('微信小程序配置查询结果', [
            'count' => $configs->count(),
            'configs' => $configs->toArray()
        ]);
        
        $result = [];
        foreach ($configs as $config) {
            $result[$config->key] = $config->value;
        }
        
        return $result;
    }
    
    /**
     * 保存微信小程序配置
     *
     * @param array $data 配置数据
     * @return void
     */
    public function saveWechatMiniProgramConfig(array $data)
    {
        $configKeys = [
            'wx_mini_program_app_id' => [
                'title' => '小程序AppID',
                'description' => '微信小程序的AppID',
                'group' => 'wechat_mini_program',
                'is_system' => true,
            ],
            'wx_mini_program_secret' => [
                'title' => '小程序Secret',
                'description' => '微信小程序的Secret',
                'group' => 'wechat_mini_program',
                'is_system' => true,
                'type' => 'password',
            ],
            'wx_mini_program_token' => [
                'title' => '小程序Token',
                'description' => '用于微信服务器验证',
                'group' => 'wechat_mini_program',
                'is_system' => true,
            ],
            'wx_mini_program_aes_key' => [
                'title' => '小程序AES Key',
                'description' => '用于消息加解密',
                'group' => 'wechat_mini_program',
                'is_system' => true,
            ],
        ];
        
        foreach ($configKeys as $key => $attributes) {
            if (isset($data[$key])) {
                $this->set($key, $data[$key], $attributes);
            }
        }
        
        // 更新.env文件中的配置（可选）
        $this->updateEnvConfig($data);
    }
    
    /**
     * 更新.env文件中的配置（可选）
     *
     * @param array $data 配置数据
     * @return void
     */
    protected function updateEnvConfig(array $data)
    {
        // 这个方法可以选择性实现
        // 更新.env文件需要小心，不推荐在生产环境中使用
        // 如果需要实现，可以使用Laravel提供的方法或第三方包
    }
} 