<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== 商品销售单位上架验证测试 ===" . PHP_EOL . PHP_EOL;

try {
    // 测试1: 创建一个没有销售单位的商品
    echo "1. 测试没有销售单位的商品验证..." . PHP_EOL;
    
    $product = new App\Product\Models\Product();
    $product->name = '测试商品无销售单位';
    $product->price = 10.50;
    $product->base_unit_id = 1; // 假设存在ID为1的单位
    
    $validation = $product->canBePublishedForSale();
    
    echo "   结果: " . ($validation['can_publish'] ? '可以上架' : '不能上架') . PHP_EOL;
    echo "   信息: " . $validation['message'] . PHP_EOL;
    echo "   缺少的条件: " . implode(', ', $validation['missing_requirements']) . PHP_EOL;
    echo PHP_EOL;
    
    // 测试2: 尝试上架没有销售单位的商品
    echo "2. 测试尝试上架没有销售单位的商品..." . PHP_EOL;
    
    try {
        $product->status = 1; // 尝试设置为上架状态
        $product->validateForPublishing();
        echo "   结果: 验证通过，可以上架" . PHP_EOL;
    } catch (Exception $e) {
        echo "   结果: 验证失败 - " . $e->getMessage() . PHP_EOL;
    }
    echo PHP_EOL;
    
    // 测试3: 查找一个实际存在的商品来测试
    echo "3. 测试现有商品的验证状态..." . PHP_EOL;
    
    $existingProduct = App\Product\Models\Product::first();
    if ($existingProduct) {
        echo "   商品: " . $existingProduct->name . " (ID: " . $existingProduct->id . ")" . PHP_EOL;
        
        $validation = $existingProduct->canBePublishedForSale();
        echo "   当前状态: " . ($existingProduct->status == 1 ? '已上架' : '已下架') . PHP_EOL;
        echo "   验证结果: " . ($validation['can_publish'] ? '可以上架' : '不能上架') . PHP_EOL;
        echo "   信息: " . $validation['message'] . PHP_EOL;
        
        if (!empty($validation['missing_requirements'])) {
            echo "   缺少的条件: " . implode(', ', $validation['missing_requirements']) . PHP_EOL;
        }
        
        // 检查销售单位
        $saleUnit = $existingProduct->getSaleDefaultUnit();
        if ($saleUnit) {
            echo "   销售单位: " . $saleUnit->name . " (ID: " . $saleUnit->id . ")" . PHP_EOL;
        } else {
            echo "   销售单位: 未设置" . PHP_EOL;
        }
    } else {
        echo "   没有找到现有商品" . PHP_EOL;
    }
    echo PHP_EOL;
    
    // 测试4: 测试ProductService的状态更新
    echo "4. 测试ProductService状态更新验证..." . PHP_EOL;
    
    if ($existingProduct) {
        $productService = app(App\Product\Services\ProductService::class);
        
        try {
            // 尝试将商品设置为上架状态
            $result = $productService->updateProductStatus($existingProduct->id, 1);
            echo "   结果: 状态更新成功，商品已上架" . PHP_EOL;
        } catch (Exception $e) {
            echo "   结果: 状态更新失败 - " . $e->getMessage() . PHP_EOL;
        }
    }
    
    echo PHP_EOL . "=== 测试完成 ===" . PHP_EOL;
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . PHP_EOL;
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . PHP_EOL;
} 