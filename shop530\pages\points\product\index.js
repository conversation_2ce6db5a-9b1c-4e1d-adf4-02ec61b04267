// pages/points/product/index.js - 积分商品详情页
const PointsAPI = require('../../../utils/pointsApi');
const app = getApp();

Page({
  data: {
    productId: 0,
    product: null,
    userBalance: 0,
    userLevel: '',
    
    // 页面状态
    loading: true,
    
    // 商品规格
    selectedSpec: {},
    quantity: 1,
    
    // 兑换资格
    eligible: false,
    eligibilityReason: '',
    
    // 相关商品
    relatedProducts: [],
    
    // 商品详情图片
    detailImages: [],
    
    // 评价
    reviews: [],
    reviewStats: {}
  },

  onLoad(options) {
    const productId = parseInt(options.id);
    if (!productId) {
      wx.showToast({
        title: '商品不存在',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({ productId });
    this.initPage();
  },

  onShow() {
    // 刷新用户积分信息
    this.loadUserInfo();
  },

  onShareAppMessage() {
    return {
      title: this.data.product ? this.data.product.name : '积分商城好物推荐',
      path: `/pages/points/product/index?id=${this.data.productId}`,
      imageUrl: this.data.product ? this.data.product.image : ''
    };
  },

  // ==================== 页面初始化 ====================

  async initPage() {
    wx.showLoading({ title: '加载中...' });

    try {
      await Promise.all([
        this.loadProduct(),
        this.loadUserInfo()
      ]);

      // 加载产品后再加载相关数据
      if (this.data.product) {
        await Promise.all([
          this.checkEligibility(),
          this.loadRelatedProducts()
        ]);
      }
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
      wx.hideLoading();
    }
  },

  // ==================== 数据加载 ====================

  async loadProduct() {
    try {
      const result = await PointsAPI.getPointsProduct(this.data.productId);
      const product = result.data;

      this.setData({
        product,
        detailImages: product.detail_images || [],
        selectedSpec: product.specs && product.specs.length > 0 ? product.specs[0] : {}
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: product.name || '商品详情'
      });
    } catch (error) {
      console.error('加载商品详情失败:', error);
      wx.showToast({
        title: '商品不存在',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  async loadUserInfo() {
    try {
      const result = await PointsAPI.getUserBalance();
      this.setData({
        userBalance: result.data.balance || 0,
        userLevel: result.data.level || 'bronze'
      });
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  async checkEligibility() {
    try {
      const result = await PointsAPI.checkExchangeEligibility(
        this.data.productId, 
        this.data.quantity
      );
      
      this.setData({
        eligible: result.data.eligible,
        eligibilityReason: result.data.reason || ''
      });
    } catch (error) {
      console.error('检查兑换资格失败:', error);
      this.setData({
        eligible: false,
        eligibilityReason: '检查失败'
      });
    }
  },

  async loadRelatedProducts() {
    try {
      const result = await PointsAPI.getPointsProducts({
        category: this.data.product.category,
        per_page: 6,
        exclude: this.data.productId
      });
      
      this.setData({
        relatedProducts: result.data.data || []
      });
    } catch (error) {
      console.error('加载相关商品失败:', error);
    }
  },

  // ==================== 用户操作 ====================

  onQuantityChange(e) {
    const quantity = parseInt(e.detail.value) || 1;
    this.setData({ quantity });
    
    // 重新检查兑换资格
    this.checkEligibility();
  },

  onSpecChange(e) {
    const specIndex = e.currentTarget.dataset.index;
    const spec = this.data.product.specs[specIndex];
    
    this.setData({ selectedSpec: spec });
    
    // 重新检查兑换资格
    this.checkEligibility();
  },

  async onExchange() {
    if (!this.data.eligible) {
      wx.showToast({
        title: this.data.eligibilityReason || '无法兑换',
        icon: 'none'
      });
      return;
    }

    const { product, selectedSpec, quantity } = this.data;

    // 构建订单数据
    const orderData = {
      items: [{
        product_id: product.id,
        spec_id: selectedSpec.id || null,
        quantity: quantity,
        points_price: selectedSpec.points_price || product.points_price,
        cash_price: selectedSpec.cash_price || product.cash_price || 0
      }]
    };

    // 如果需要配送地址，先跳转选择地址
    if (product.category === 'physical') {
      this.selectShippingAddress(orderData);
    } else {
      this.createOrder(orderData);
    }
  },

  selectShippingAddress(orderData) {
    wx.chooseAddress({
      success: (res) => {
        orderData.shipping_info = {
          receiver_name: res.userName,
          receiver_phone: res.telNumber,
          receiver_address: `${res.provinceName}${res.cityName}${res.countyName}${res.detailInfo}`,
          postal_code: res.postalCode
        };
        
        this.createOrder(orderData);
      },
      fail: (error) => {
        if (error.errMsg !== 'chooseAddress:cancel') {
          wx.showToast({
            title: '请选择收货地址',
            icon: 'none'
          });
        }
      }
    });
  },

  async createOrder(orderData) {
    wx.showLoading({ title: '创建订单中...' });

    try {
      const result = await PointsAPI.createPointsOrder(orderData);
      const order = result.data;

      wx.hideLoading();

      // 如果需要支付现金部分，跳转支付
      if (order.cash_amount > 0) {
        this.processPayment(order);
      } else {
        // 纯积分兑换，直接完成
        wx.showToast({
          title: '兑换成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          wx.navigateTo({
            url: `/pages/points/orders/detail/index?id=${order.id}`
          });
        }, 1500);
      }
    } catch (error) {
      wx.hideLoading();
      console.error('创建订单失败:', error);
      wx.showToast({
        title: error.message || '兑换失败',
        icon: 'error'
      });
    }
  },

  processPayment(order) {
    // 这里集成微信支付
    wx.showModal({
      title: '支付确认',
      content: `需要支付现金：¥${order.cash_amount}`,
      success: (res) => {
        if (res.confirm) {
          // 调用支付接口
          this.requestPayment(order);
        }
      }
    });
  },

  async requestPayment(order) {
    try {
      // 这里应该调用后端获取支付参数
      // const paymentParams = await PaymentAPI.getPaymentParams(order.id);
      
      wx.showToast({
        title: '支付功能开发中',
        icon: 'none'
      });
    } catch (error) {
      console.error('支付失败:', error);
      wx.showToast({
        title: '支付失败',
        icon: 'error'
      });
    }
  },

  // ==================== 页面跳转 ====================

  goToRelatedProduct(e) {
    const productId = e.currentTarget.dataset.id;
    wx.redirectTo({
      url: `/pages/points/product/index?id=${productId}`
    });
  },

  goToOrders() {
    wx.navigateTo({
      url: '/pages/points/orders/index'
    });
  },

  previewImage(e) {
    const current = e.currentTarget.dataset.url;
    const urls = this.data.detailImages;
    
    wx.previewImage({
      current,
      urls
    });
  },

  // ==================== 工具方法 ====================

  formatPoints(points) {
    return PointsAPI.formatPoints(points);
  },

  formatProductCategory(category) {
    return PointsAPI.formatProductCategory(category);
  },

  calculateTotalPoints() {
    const { product, selectedSpec, quantity } = this.data;
    const unitPoints = selectedSpec.points_price || product.points_price || 0;
    return unitPoints * quantity;
  },

  calculateTotalCash() {
    const { product, selectedSpec, quantity } = this.data;
    const unitCash = selectedSpec.cash_price || product.cash_price || 0;
    return unitCash * quantity;
  },

  isPointsSufficient() {
    return this.data.userBalance >= this.calculateTotalPoints();
  },

  getExchangeButtonText() {
    if (!this.data.eligible) {
      return this.data.eligibilityReason || '无法兑换';
    }
    
    if (!this.isPointsSufficient()) {
      return '积分不足';
    }
    
    const totalCash = this.calculateTotalCash();
    if (totalCash > 0) {
      return `立即兑换（¥${totalCash.toFixed(2)}）`;
    }
    
    return '立即兑换';
  },

  getExchangeButtonClass() {
    if (!this.data.eligible || !this.isPointsSufficient()) {
      return 'exchange-btn disabled';
    }
    return 'exchange-btn';
  }
}); 