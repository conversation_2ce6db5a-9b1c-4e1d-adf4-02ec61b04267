{"pages": ["pages/index/index", "pages/login/index", "pages/register/index", "pages/profile-complete/index", "pages/category/category", "pages/cart/index", "pages/points/index", "pages/points/product/index", "pages/points/orders/index", "pages/points/orders/detail/index", "pages/points/transactions/index", "pages/points/rules/index", "pages/points/ranking/index", "pages/profile/index", "pages/product-detail/product-detail", "pages/order-confirm/index", "pages/address/index", "pages/address-add/index", "pages/user-info/index", "pages/order-list/index", "pages/order-detail/index", "pages/after-sales/index", "pages/after-sales-detail/index", "pages/points-detail/index", "pages/points-exchange/index", "pages/points-record/index", "pages/check-in/index", "pages/order-success/index", "pages/test-price/test-price"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#4CAF50", "navigationBarTitleText": "天心食品", "navigationBarTextStyle": "white", "backgroundColor": "#ffffff", "enablePullDownRefresh": false}, "tabBar": {"color": "#999999", "selectedColor": "#4CAF50", "backgroundColor": "#ffffff", "borderStyle": "white", "position": "bottom", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "images/tabbar/home.png", "selectedIconPath": "images/tabbar/home-active.png"}, {"pagePath": "pages/category/category", "text": "分类", "iconPath": "images/tabbar/category.png", "selectedIconPath": "images/tabbar/category-active.png"}, {"pagePath": "pages/cart/index", "text": "购物车", "iconPath": "images/tabbar/cart.png", "selectedIconPath": "images/tabbar/cart-active.png"}, {"pagePath": "pages/points/index", "text": "积分商城", "iconPath": "images/tabbar/points.png", "selectedIconPath": "images/tabbar/points-active.png"}, {"pagePath": "pages/profile/index", "text": "我的", "iconPath": "images/tabbar/profile.png", "selectedIconPath": "images/tabbar/profile-active.png"}]}, "permission": {"scope.userLocation": {"desc": "您的位置信息将用于自动填写地址信息，提升注册体验"}}, "requiredPrivateInfos": ["getLocation", "chooseLocation"], "networkTimeout": {"request": 10000, "downloadFile": 10000}, "usingComponents": {"price-display": "/components/price-display/index"}, "debug": false, "sitemapLocation": "sitemap.json"}