<?php

namespace App\Product\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Product\Models\Product;
use App\Product\Models\Category;
use App\Product\Models\ProductTag;
use App\Product\Services\ProductService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PublicController extends Controller
{
    /**
     * 产品服务
     */
    protected $productService;
    
    /**
     * 构造函数
     */
    public function __construct(ProductService $productService = null)
    {
        $this->productService = $productService ?: app(ProductService::class);
    }

    /**
     * 获取公开商品列表 - 无需认证
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function products(Request $request)
    {
        Log::info('公开API - 获取商品列表', [
            'request_params' => $request->all(),
        ]);
        
        $query = Product::where('status', 1); // 只获取上架商品
        
        // 添加查询条件
        if ($request->has('category_id') && $request->category_id) {
            $categoryId = $request->category_id;
            
            // 检查是否需要包含子分类商品
            if ($request->has('include_children') && $request->include_children) {
                // 获取指定分类及其所有子分类的ID
                $category = Category::find($categoryId);
                if ($category) {
                    $descendantIds = $category->getAllDescendants(true)->pluck('id')->toArray();
                    $allCategoryIds = array_merge([$categoryId], $descendantIds);
                    
                    Log::info('包含子分类查询', [
                        'parent_category_id' => $categoryId,
                        'all_category_ids' => $allCategoryIds,
                        'descendants_count' => count($descendantIds)
                    ]);
                    
                    $query->whereIn('category_id', $allCategoryIds);
                } else {
                    // 分类不存在，只查询指定分类
                    $query->where('category_id', $categoryId);
                }
            } else {
                // 只查询指定分类
                $query->where('category_id', $categoryId);
            }
        }
        
        if ($request->has('keyword') && $request->keyword) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('subtitle', 'like', "%{$keyword}%");
            });
        }
        
        // 标签筛选 - 支持标签ID和标签别名
        if ($request->has('tag_ids') && $request->tag_ids) {
            $tagIds = is_array($request->tag_ids) ? $request->tag_ids : explode(',', $request->tag_ids);
            $query->whereHas('tags', function($q) use ($tagIds) {
                $q->whereIn('product_tags.id', $tagIds);
            });
        }
        
        // 按标签别名筛选
        if ($request->has('tag_slug') && $request->tag_slug) {
            $query->whereHas('tags', function($q) use ($request) {
                $q->where('product_tags.slug', $request->tag_slug);
            });
        }
        
        // 促销商品筛选 - 使用现有字段
        if ($request->has('is_promotion') && $request->is_promotion) {
            $query->where('is_promotion', 1)
                  ->where('promotion_price', '>', 0);
        }
        
        // 有促销价的商品筛选 - 基于现有字段
        if ($request->has('has_sale_price') && $request->has_sale_price) {
            Log::info('应用促销价筛选条件', [
                'has_sale_price' => $request->has_sale_price
            ]);
            $query->whereNotNull('sale_price')
                  ->where('sale_price', '>', 0)
                  ->whereRaw('sale_price < price'); // 促销价低于原价
        }
        
        // 时间范围筛选 - 新品支持
        if ($request->has('created_after') && $request->created_after) {
            $query->where('created_at', '>=', $request->created_after);
        }
        
        // 根据产品ID筛选相关推荐
        if ($request->has('product_id') && $request->product_id) {
            // 先获取当前商品的分类
            $product = Product::find($request->product_id);
            if ($product) {
                $query->where('category_id', $product->category_id)
                      ->where('id', '!=', $product->id)
                      ->orderBy('is_recommend', 'desc');
            }
        }
        
        // 排序处理 - 性能优化版本
        $sort = $request->input('sort', 'created_at');
        $order = $request->input('order', 'desc');
        
        switch ($sort) {
            case 'sales_count':
                // 热门商品：使用复合索引 (status, sales_count)
                $query->orderBy('sales_count', $order)
                      ->orderBy('id', 'desc'); // 二级排序保证稳定性
                break;
            case 'price':
                $query->orderBy('price', $order)
                      ->orderBy('id', 'desc');
                break;
            case 'sale_price':
                // 促销价排序：优先显示有促销价的商品
                $query->orderByRaw('CASE WHEN sale_price IS NOT NULL AND sale_price > 0 THEN 0 ELSE 1 END')
                      ->orderBy('sale_price', $order)
                      ->orderBy('id', 'desc');
                break;
            case 'promotion_price':
                // 特价商品：使用复合索引 (status, is_promotion, promotion_price)
                $query->orderBy('promotion_price', $order)
                      ->orderBy('id', 'desc');
                break;
            case 'created_at':
            default:
                // 新品：使用复合索引 (status, created_at)
                $query->orderBy('created_at', $order)
                      ->orderBy('id', 'desc'); // 二级排序保证稳定性
                break;
        }
        
        // 分页
        $limit = $request->input('limit', 10);
        $per_page = $request->input('per_page', $limit); // 兼容per_page参数
        $pageSize = $request->input('pageSize', $per_page); // 兼容pageSize参数
        $products = $query->with(['category', 'tags', 'images'])->paginate($pageSize);
        
        // 为每个商品添加库存状态信息和单位信息
        $products->getCollection()->transform(function ($product) {
            // 添加单位信息 - 只显示销售单位
            $saleUnit = $product->getSaleDefaultUnit();
            
            if ($saleUnit) {
                $product->unit = $saleUnit->name;
                $product->unit_symbol = $saleUnit->symbol;
                $product->unit_type = $saleUnit->type;
                $product->sale_unit = [
                    'id' => $saleUnit->id,
                    'name' => $saleUnit->name,
                    'symbol' => $saleUnit->symbol,
                    'type' => $saleUnit->type
                ];
            } else {
                // 没有销售单位时不显示单位
                $product->unit = '';
                $product->unit_symbol = '';
                $product->unit_type = '';
                $product->sale_unit = null;
            }
            
            // 获取库存状态
            $stockStatus = $product->getStockStatus();
            
            // 检查是否可以购买（检查1件商品）
            $canPurchase = true;
            $purchaseMessage = '';
            
            if ($product->track_inventory) {
                $stockCheck = $product->checkStockWithPolicy(1);
                $canPurchase = $stockCheck['allowed'];
                $purchaseMessage = $stockCheck['message'];
            }
            
            // 判断是否真正缺货：只有在严格库存策略下且库存不足时才算缺货
            $isOutOfStock = false;
            if ($product->track_inventory && $product->inventory_policy === 'strict') {
                $isOutOfStock = !$canPurchase;
            }
            
            // 添加库存相关字段到商品数据
            $product->stock_status = $stockStatus['status']; // normal, low, out, negative, unlimited, untracked
            $product->current_stock = $stockStatus['current_stock'];
            $product->can_purchase = $canPurchase;
            $product->purchase_message = $purchaseMessage;
            $product->out_of_stock = $isOutOfStock;
            
            return $product;
        });
        
        Log::info('公开API - 商品列表结果', [
            'count' => $products->count(),
            'total' => $products->total()
        ]);
        
        return response()->json(ApiResponse::success($products));
    }
    
    /**
     * 获取商品详情 - 无需认证
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function productDetail($id)
    {
        Log::info('公开API - 获取商品详情', ['id' => $id]);
        
        $product = Product::with(['category', 'tags', 'images'])
                        ->where('status', 1)
                        ->find($id);
        
        if (!$product) {
            return response()->json(ApiResponse::error('商品不存在或已下架', 404), 404);
        }
        
        // 增加浏览次数
        $product->views_count = $product->views_count + 1;
        $product->save();
        
        // 获取用户信息（如果已登录）
        $user = null;
        try {
            // 修复：使用正确的sanctum守卫
            $user = auth('sanctum')->user();
            if ($user) {
                Log::info('商品详情API - 检测到登录用户', [
                    'user_id' => $user->id,
                    'membership_level' => $user->membershipLevel?->name
                ]);
            }
        } catch (\Exception $e) {
            // 公开接口，用户可能未登录，忽略认证错误
            Log::debug('商品详情API - 用户未登录或认证失败', [
                'error' => $e->getMessage()
            ]);
        }
        
        // 获取区域ID（从请求参数）
        $regionId = request()->input('region_id');
        if ($regionId) {
            $regionId = (int) $regionId;
            Log::info('商品详情API - 接收到区域ID', ['region_id' => $regionId]);
        }
        
        // 计算价格信息（使用后端的价格计算服务）
        $priceInfo = null;
        $priceDisplay = null;
        
        try {
            $priceInfo = $product->calculatePrice($user, $regionId);
            $priceDisplay = $product->getPriceDisplay($user, $regionId);
            
            Log::info('商品详情API - 价格计算完成', [
                'product_id' => $id,
                'user_id' => $user ? $user->id : null,
                'region_id' => $regionId,
                'base_price' => $priceInfo['base_price'],
                'final_price' => $priceInfo['final_price'],
                'price_type' => $priceInfo['price_type']
            ]);
        } catch (\Exception $e) {
            Log::warning('商品详情API - 价格计算失败，使用基础价格', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            // 如果价格计算失败，使用基础价格
            $priceInfo = [
                'base_price' => $product->price,
                'final_price' => $product->price,
                'total_discount' => 0,
                'price_type' => 'base',
                'discount_info' => [],
                'user_type' => $user ? 'member' : 'guest'
            ];
            
            $priceDisplay = [
                'product_id' => $product->id,
                'base_price' => $product->price,
                'current_price' => $product->price,
                'original_price' => $product->price,
                'has_discount' => false,
                'discount_amount' => 0,
                'price_type' => 'base',
                'price_labels' => []
            ];
        }
        
        // 添加库存状态信息
        $stockStatus = $product->getStockStatus();
        
        // 检查是否可以购买（检查1件商品）
        $canPurchase = true;
        $purchaseMessage = '';
        
        if ($product->track_inventory) {
            $stockCheck = $product->checkStockWithPolicy(1);
            $canPurchase = $stockCheck['allowed'];
            $purchaseMessage = $stockCheck['message'];
        }
        
        // 判断是否真正缺货：只有在严格库存策略下且库存不足时才算缺货
        $isOutOfStock = false;
        if ($product->track_inventory && $product->inventory_policy === 'strict') {
            $isOutOfStock = !$canPurchase;
        }
        
        // 构建返回数据
        $productData = $product->toArray();
        
        // 添加单位信息 - 只显示销售单位
        $saleUnit = $product->getSaleDefaultUnit();
        
        if ($saleUnit) {
            $productData['unit'] = $saleUnit->name;
            $productData['unit_symbol'] = $saleUnit->symbol;
            $productData['unit_type'] = $saleUnit->type;
            $productData['sale_unit'] = [
                'id' => $saleUnit->id,
                'name' => $saleUnit->name,
                'symbol' => $saleUnit->symbol,
                'type' => $saleUnit->type
            ];
        } else {
            // 没有销售单位时不显示单位
            $productData['unit'] = '';
            $productData['unit_symbol'] = '';
            $productData['unit_type'] = '';
            $productData['sale_unit'] = null;
        }
        
        // 添加计算好的价格信息
        if ($priceInfo && $priceDisplay) {
            $productData['price_info'] = $priceInfo;
            $productData['price_display'] = $priceDisplay;
            
            // 为了兼容前端，也添加一些直接的价格字段
            $productData['final_price'] = $priceDisplay['current_price'];
            $productData['original_price'] = $priceDisplay['original_price'];
            $productData['has_discount'] = $priceDisplay['has_discount'];
            $productData['discount_amount'] = $priceDisplay['discount_amount'];
            $productData['price_labels'] = $priceDisplay['price_labels'];
        }
        
        // 添加库存相关字段
        $productData['stock_status'] = $stockStatus['status'];
        $productData['current_stock'] = $stockStatus['current_stock'];
        $productData['can_purchase'] = $canPurchase;
        $productData['purchase_message'] = $purchaseMessage;
        $productData['out_of_stock'] = $isOutOfStock;
        
        // 添加上下文信息
        $productData['context'] = [
            'user_id' => $user ? $user->id : null,
            'region_id' => $regionId,
            'calculated_at' => now()->toISOString()
        ];
        
        return response()->json(ApiResponse::success($productData));
    }
    
    /**
     * 搜索商品 - 无需认证
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchProducts(Request $request)
    {
        $keyword = $request->input('keyword', '');
        Log::info('公开API - 搜索商品', ['keyword' => $keyword]);
        
        if (empty($keyword)) {
            return response()->json(ApiResponse::success([]));
        }
        
        $query = Product::where('status', 1);
        
        $query->where(function($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
              ->orWhere('subtitle', 'like', "%{$keyword}%")
              ->orWhere('description', 'like', "%{$keyword}%");
        });
        
        $limit = $request->input('limit', 20);
        $products = $query->with(['category', 'tags', 'images'])->paginate($limit);
        
        // 为每个商品添加单位信息 - 只显示销售单位
        $products->getCollection()->transform(function ($product) {
            $saleUnit = $product->getSaleDefaultUnit();
            
            if ($saleUnit) {
                $product->unit = $saleUnit->name;
                $product->unit_symbol = $saleUnit->symbol;
                $product->unit_type = $saleUnit->type;
                $product->sale_unit = [
                    'id' => $saleUnit->id,
                    'name' => $saleUnit->name,
                    'symbol' => $saleUnit->symbol,
                    'type' => $saleUnit->type
                ];
            } else {
                // 没有销售单位时不显示单位
                $product->unit = '';
                $product->unit_symbol = '';
                $product->unit_type = '';
                $product->sale_unit = null;
            }
            
            return $product;
        });
        
        return response()->json(ApiResponse::success($products));
    }
    
    /**
     * 获取公开标签列表 - 无需认证
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function tags(Request $request)
    {
        Log::info('公开API - 获取标签列表');
        
        $query = ProductTag::where('is_active', 1)
                          ->where('show_in_filter', 1);
        
        // 排序
        $query->orderBy('sort_order', 'asc')
              ->orderBy('id', 'desc');
        
        $tags = $query->get();
        
        return response()->json(ApiResponse::success($tags));
    }
    
    /**
     * 获取热门标签 - 无需认证
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function popularTags(Request $request)
    {
        Log::info('公开API - 获取热门标签');
        
        $limit = $request->input('limit', 10);
        
        $tags = ProductTag::where('is_active', 1)
                         ->where('show_in_filter', 1)
                         ->withCount('products')
                         ->having('products_count', '>', 0)
                         ->orderBy('products_count', 'desc')
                         ->orderBy('sort_order', 'asc')
                         ->limit($limit)
                         ->get();
        
        return response()->json(ApiResponse::success($tags));
    }

    /**
     * 获取商品标签页配置 - 无需认证
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function productTabs(Request $request)
    {
        Log::info('公开API - 获取商品标签页配置');
        
        try {
            // 从数据库获取启用的标签
            $tags = ProductTag::where('is_active', 1)
                             ->where('show_in_filter', 1)
                             ->orderBy('sort_order', 'asc')
                             ->get();
            
            // 转换为前端需要的格式
            $tabs = $tags->map(function($tag) {
                return [
                    'type' => $tag->slug,
                    'name' => $tag->name,
                    'icon' => $tag->icon,
                    'sort' => $tag->sort_order
                ];
            })->toArray();
            
            Log::info('公开API - 商品标签页配置结果', [
                'count' => count($tabs),
                'tags' => $tabs
            ]);
            
            return response()->json(ApiResponse::success($tabs));
            
        } catch (\Exception $e) {
            Log::error('获取商品标签页配置失败', [
                'error' => $e->getMessage()
            ]);
            
            // 如果数据库查询失败，返回默认配置
            $defaultTabs = [
                [
                    'type' => 'hot',
                    'name' => '热销',
                    'icon' => 'fire',
                    'sort' => 1
                ],
                [
                    'type' => 'new',
                    'name' => '新品',
                    'icon' => 'new',
                    'sort' => 2
                ],
                [
                    'type' => 'recommend',
                    'name' => '推荐',
                    'icon' => 'like',
                    'sort' => 3
                ],
                [
                    'type' => 'promotion',
                    'name' => '特价',
                    'icon' => 'discount',
                    'sort' => 4
                ]
            ];
            
            return response()->json(ApiResponse::success($defaultTabs));
        }
    }
} 