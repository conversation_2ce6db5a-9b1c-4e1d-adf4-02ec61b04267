<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Order\Services\PaymentLinkService;

class CleanupExpiredPaymentLinks extends Command
{
    protected $signature = 'payment-links:cleanup-expired';
    protected $description = '清理过期的付款链接';

    protected $paymentLinkService;

    public function __construct(PaymentLinkService $paymentLinkService)
    {
        parent::__construct();
        $this->paymentLinkService = $paymentLinkService;
    }

    public function handle()
    {
        $this->info('开始清理过期付款链接...');

        try {
            $count = $this->paymentLinkService->cleanupExpiredLinks();
            
            $this->info("成功清理了 {$count} 个过期付款链接");
            
            return 0;
        } catch (\Exception $e) {
            $this->error('清理失败: ' . $e->getMessage());
            return 1;
        }
    }
} 