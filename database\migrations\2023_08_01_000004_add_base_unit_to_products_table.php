<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 增加base_unit_id外键，可为空，因为需要考虑兼容性和数据填充
            $table->foreignId('base_unit_id')->nullable()->after('category_id')
                ->constrained('units')->nullOnDelete()->comment('商品基本单位ID');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['base_unit_id']);
            $table->dropColumn('base_unit_id');
        });
    }
}; 