<template>
	<view class="churn-warning-container">
		<!-- 页面头部 -->
		<view class="header-section">
			<view class="header-title">
				<text class="title-text">客户流失预警</text>
				<text class="subtitle-text">生鲜配送 · 7天未下单预警</text>
			</view>
			<view class="warning-stats">
				<text class="warning-count">{{ churnData.total_warnings || 0 }}</text>
				<text class="warning-label">位客户</text>
			</view>
		</view>

		<!-- 风险分布统计 -->
		<view class="risk-distribution" v-if="churnData.risk_distribution">
			<view class="distribution-item high">
				<text class="dist-count">{{ churnData.risk_distribution.high || 0 }}</text>
				<text class="dist-label">高风险</text>
			</view>
			<view class="distribution-item medium">
				<text class="dist-count">{{ churnData.risk_distribution.medium || 0 }}</text>
				<text class="dist-label">中风险</text>
			</view>
			<view class="distribution-item low">
				<text class="dist-count">{{ churnData.risk_distribution.low || 0 }}</text>
				<text class="dist-label">低风险</text>
			</view>
		</view>

		<!-- 风险等级筛选 -->
		<view class="filter-section">
			<view class="filter-title">风险等级</view>
			<view class="risk-filters">
				<view class="filter-item" 
					  :class="{ active: selectedRisk === 'all' }"
					  @tap="filterByRisk('all')">
					<text class="filter-text">全部</text>
					<text class="filter-count">({{ getTotalCount() }})</text>
				</view>
				<view class="filter-item high" 
					  :class="{ active: selectedRisk === 'high' }"
					  @tap="filterByRisk('high')">
					<text class="filter-text">高风险</text>
					<text class="filter-count">({{ getCountByRisk('high') }})</text>
				</view>
				<view class="filter-item medium" 
					  :class="{ active: selectedRisk === 'medium' }"
					  @tap="filterByRisk('medium')">
					<text class="filter-text">中风险</text>
					<text class="filter-count">({{ getCountByRisk('medium') }})</text>
				</view>
				<view class="filter-item low" 
					  :class="{ active: selectedRisk === 'low' }"
					  @tap="filterByRisk('low')">
					<text class="filter-text">低风险</text>
					<text class="filter-count">({{ getCountByRisk('low') }})</text>
				</view>
			</view>
		</view>

		<!-- 统计概览 -->
		<view class="stats-section">
			<view class="stats-grid">
				<view class="stat-item">
					<text class="stat-number">{{ churnData.total_warnings || 0 }}</text>
					<text class="stat-label">预警客户</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ (churnData.risk_distribution && churnData.risk_distribution.high) || 0 }}</text>
					<text class="stat-label">高风险</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ (churnData.follow_up_stats && churnData.follow_up_stats.none) || 0 }}</text>
					<text class="stat-label">未跟进</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ (churnData.follow_up_stats && churnData.follow_up_stats.overdue) || 0 }}</text>
					<text class="stat-label">逾期跟进</text>
				</view>
			</view>
		</view>

		<!-- 跟进状态筛选 -->
		<view class="filter-section">
			<view class="filter-tabs">
				<view 
					class="filter-tab" 
					v-for="(filter, index) in followUpFilters" 
					:key="index"
					:class="{ active: selectedFollowUpFilter === filter.value }"
					@tap="selectFollowUpFilter(filter.value)"
				>
					<text class="tab-text">{{ filter.label }}</text>
					<text class="tab-count" v-if="filter.count > 0">{{ filter.count }}</text>
				</view>
			</view>
		</view>

		<!-- 客户列表 -->
		<view class="customer-list">
			<view class="customer-card" v-for="customer in filteredCustomers" :key="customer.id">
				<view class="customer-header">
					<view class="customer-info">
						<text class="customer-name">{{ customer.name }}</text>
						<text class="customer-phone">{{ customer.phone }}</text>
					</view>
					<view class="risk-badge" :class="customer.riskLevel">
						<text class="risk-text">{{ getRiskLevelText(customer.riskLevel) }}</text>
						<text class="risk-score">{{ customer.riskScore }}分</text>
					</view>
				</view>
				
				<view class="customer-stats">
					<view class="stat-row">
						<view class="stat-item">
							<text class="stat-label">总消费</text>
							<text class="stat-value">¥{{ customer.totalSpend }}</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">订单数</text>
							<text class="stat-value">{{ customer.totalOrders }}单</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">客单价</text>
							<text class="stat-value">¥{{ customer.avgOrderValue }}</text>
						</view>
					</view>
					<view class="stat-row">
						<view class="stat-item">
							<text class="stat-label">未活跃</text>
							<text class="stat-value">{{ customer.daysSinceLastOrder }}天</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">跟进次数</text>
							<text class="stat-value">{{ customer.followUpCount || 0 }}次</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">上次跟进</text>
							<text class="stat-value">{{ getFollowUpDaysText(customer) }}</text>
						</view>
					</view>
				</view>
				
				<!-- 跟进状态标签 -->
				<view class="follow-up-status" v-if="customer.followUpStatus">
					<view class="status-badge" :class="customer.followUpStatus">
						<text class="status-text">{{ getFollowUpStatusText(customer.followUpStatus) }}</text>
					</view>
					<view class="priority-badge" :class="customer.followUpPriority" v-if="customer.followUpPriority !== 'normal'">
						<text class="priority-text">{{ getFollowUpPriorityText(customer.followUpPriority) }}</text>
					</view>
				</view>
				
				<view class="reason-tags">
					<text class="reason-tag" v-for="reason in customer.churnReason" :key="reason">{{ reason }}</text>
				</view>
				
				<view class="suggestion-list">
					<text class="suggestion-item" v-for="suggestion in customer.suggestions" :key="suggestion">• {{ suggestion }}</text>
				</view>
				
				<view class="action-buttons">
					<button class="action-btn primary" @tap="callCustomer(customer)">
						<text class="btn-text">📞 立即联系</text>
					</button>
					<button class="action-btn secondary" @tap="addFollowUp(customer)">
						<text class="btn-text">📝 添加跟进</text>
					</button>
					<button class="action-btn tertiary" @tap="viewFollowUpHistory(customer)">
						<text class="btn-text">📋 跟进历史</text>
					</button>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="filteredCustomers.length === 0 && !loading">
			<view class="empty-icon">🎉</view>
			<text class="empty-title">暂无流失风险客户</text>
			<text class="empty-desc">当前筛选条件下没有需要关注的客户</text>
		</view>

		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
import analyticsApi from '../../api/analytics.js'
import clientApi from '../../api/client.js'

export default {
	data() {
		return {
			churnData: {},
			customers: [],
			selectedRisk: 'all',
			loading: false,
			selectedFollowUpFilter: 'all'
		}
	},
	
	computed: {
		// 筛选后的客户列表
		filteredCustomers() {
			let filtered = this.customers
			
			// 按风险等级筛选
			if (this.selectedRisk !== 'all') {
				filtered = filtered.filter(customer => customer.riskLevel === this.selectedRisk)
			}
			
			// 按跟进状态筛选
			if (this.selectedFollowUpFilter !== 'all') {
				filtered = filtered.filter(customer => {
					switch (this.selectedFollowUpFilter) {
						case 'none':
							return customer.followUpCount === 0
						case 'overdue':
							return customer.followUpStatus === 'overdue'
						case 'urgent':
							return customer.followUpPriority === 'urgent'
						case 'high_value':
							return customer.totalSpend > 10000
						default:
							return true
					}
				})
			}
			
			return filtered
		},
		
		// 跟进筛选器
		followUpFilters() {
			const stats = this.churnData.follow_up_stats || {}
			return [
				{ label: '全部', value: 'all', count: this.customers.length },
				{ label: '未跟进', value: 'none', count: stats.none || 0 },
				{ label: '逾期跟进', value: 'overdue', count: stats.overdue || 0 },
				{ label: '紧急处理', value: 'urgent', count: this.customers.filter(c => c.followUpPriority === 'urgent').length },
				{ label: '高价值客户', value: 'high_value', count: this.customers.filter(c => c.totalSpend > 10000).length }
			]
		}
	},
	
	onLoad() {
		this.loadChurnWarningData()
	},
	
	onShow() {
		this.loadChurnWarningData()
	},
	
	onPullDownRefresh() {
		this.refreshData()
	},
	
	methods: {
		// 加载流失预警数据
		async loadChurnWarningData() {
			this.loading = true
			try {
				const response = await analyticsApi.getChurnWarning()
				console.log('流失预警API响应:', response)
				
				// request.js已经处理了响应，直接使用response即可
				// response格式: {code: 0, message: "获取成功", data: {...}}
				if (response && response.code === 0) {
					this.churnData = response.data || {}
					this.customers = this.churnData.customers || []
					console.log('成功解析客户数据:', this.customers.length, '个客户')
					console.log('风险分布:', this.churnData.risk_distribution)
				} else {
					const errorMsg = (response && response.message) || '获取数据失败'
					console.error('API返回错误:', errorMsg)
					console.error('完整响应数据:', response)
					uni.showToast({
						title: errorMsg,
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载流失预警数据失败:', error)
				uni.showToast({
					title: '网络请求失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 刷新数据
		async refreshData() {
			await this.loadChurnWarningData()
			uni.stopPullDownRefresh()
		},
		
		// 按风险等级筛选
		filterByRisk(risk) {
			this.selectedRisk = risk
		},
		
		// 获取总数量
		getTotalCount() {
			return this.customers.length
		},
		
		// 获取指定风险等级的数量
		getCountByRisk(risk) {
			return this.customers.filter(customer => customer.riskLevel === risk).length
		},
		
		// 获取头像文本
		getAvatarText(customer) {
			const name = customer.name || customer.merchant_name || 'U'
			return name.charAt(0).toUpperCase()
		},
		
		// 获取风险等级文本
		getRiskText(level) {
			const riskMap = {
				'high': '高风险',
				'medium': '中风险',
				'low': '低风险'
			}
			return riskMap[level] || level
		},
		
		// 格式化最后订单时间
		formatLastOrder(dateStr) {
			if (!dateStr) return '无订单记录'
			
			const date = new Date(dateStr)
			const now = new Date()
			const diffTime = now - date
			const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
			
			if (diffDays === 0) return '今天'
			if (diffDays === 1) return '昨天'
			if (diffDays < 30) return `${diffDays}天前`
			if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`
			return `${Math.floor(diffDays / 365)}年前`
		},
		
		// 格式化数字
		formatNumber(num) {
			if (!num) return '0'
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + 'w'
			}
			if (num >= 1000) {
				return (num / 1000).toFixed(1) + 'k'
			}
			return num.toString()
		},
		
		// 跳转到客户详情
		goToCustomerDetail(customerId) {
			uni.navigateTo({
				url: `/pages/clients/client-detail?id=${customerId}`
			})
		},
		
		// 联系客户
		contactCustomer(customer) {
			uni.showActionSheet({
				itemList: ['拨打电话', '发送短信', '微信联系'],
				success: (res) => {
					switch (res.tapIndex) {
						case 0:
							this.callCustomer(customer)
							break
						case 1:
							this.sendSMS(customer)
							break
						case 2:
							this.contactWeChat(customer)
							break
					}
				}
			})
		},
		
		// 拨打电话
		callCustomer(customer) {
			if (customer.phone) {
				uni.makePhoneCall({
					phoneNumber: customer.phone,
					success: () => {
						this.recordContactAction(customer.id, 'phone_call')
					}
				})
			} else {
				uni.showToast({
					title: '客户电话号码不存在',
					icon: 'none'
				})
			}
		},
		
		// 发送短信
		sendSMS(customer) {
			uni.showToast({
				title: '短信功能开发中',
				icon: 'none'
			})
		},
		
		// 微信联系
		contactWeChat(customer) {
			uni.showToast({
				title: '微信联系功能开发中',
				icon: 'none'
			})
		},
		
		// 创建挽回计划
		createRetentionPlan(customer) {
			uni.showToast({
				title: '挽回计划功能开发中',
				icon: 'none'
			})
		},
		
		// 标记为已处理
		async markAsHandled(customer) {
			try {
				uni.showToast({
					title: '已标记为已处理',
					icon: 'success'
				})
				
				// 从列表中移除
				const index = this.customers.findIndex(c => c.id === customer.id)
				if (index > -1) {
					this.customers.splice(index, 1)
				}
			} catch (error) {
				console.error('标记失败:', error)
				uni.showToast({
					title: '操作失败',
					icon: 'none'
				})
			}
		},
		
		// 记录联系行为
		async recordContactAction(customerId, actionType) {
			try {
				console.log('记录联系行为:', customerId, actionType)
			} catch (error) {
				console.error('记录联系行为失败:', error)
			}
		},
		
		// 选择跟进状态
		selectFollowUpFilter(filter) {
			this.selectedFollowUpFilter = filter
		},
		
		// 获取风险等级文本
		getRiskLevelText(level) {
			const riskMap = {
				'high': '高风险',
				'medium': '中风险',
				'low': '低风险'
			}
			return riskMap[level] || level
		},
		
		// 获取跟进天数文本
		getFollowUpDaysText(customer) {
			if (!customer.lastFollowUpDate) return '无跟进记录'
			
			const lastFollowUpDate = new Date(customer.lastFollowUpDate)
			const now = new Date()
			const diffTime = now - lastFollowUpDate
			const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
			
			if (diffDays === 0) return '今天'
			if (diffDays === 1) return '昨天'
			if (diffDays < 30) return `${diffDays}天前`
			if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`
			return `${Math.floor(diffDays / 365)}年前`
		},
		
		// 获取跟进状态文本
		getFollowUpStatusText(status) {
			const statusMap = {
				'none': '未跟进',
				'overdue': '逾期跟进',
				'active': '活跃客户',
				'inactive': '不活跃客户'
			}
			return statusMap[status] || status
		},
		
		// 获取跟进优先级文本
		getFollowUpPriorityText(priority) {
			const priorityMap = {
				'high': '高优先级',
				'medium': '中优先级',
				'low': '低优先级'
			}
			return priorityMap[priority] || priority
		},
		
		// 添加跟进
		addFollowUp(customer) {
			uni.showToast({
				title: '添加跟进功能开发中',
				icon: 'none'
			})
		},
		
		// 查看跟进历史
		viewFollowUpHistory(customer) {
			uni.showToast({
				title: '查看跟进历史功能开发中',
				icon: 'none'
			})
		}
	}
}
</script>

<style scoped>
.churn-warning-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 页面头部 */
.header-section {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
	padding: 40rpx 32rpx 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	color: #ffffff;
}

.header-title {
	flex: 1;
}

.title-text {
	font-size: 40rpx;
	font-weight: 700;
	color: #ffffff;
	margin-bottom: 8rpx;
	display: block;
}

.subtitle-text {
	font-size: 28rpx;
	color: #ffffff;
	opacity: 0.8;
	display: block;
}

.warning-stats {
	text-align: center;
}

.warning-count {
	font-size: 48rpx;
	font-weight: 700;
	display: block;
}

.warning-label {
	font-size: 24rpx;
	opacity: 0.8;
}

/* 风险分布统计 */
.risk-distribution {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
	display: flex;
	gap: 16rpx;
}

.distribution-item {
	flex: 1;
	text-align: center;
}

.dist-count {
	font-size: 48rpx;
	font-weight: 700;
	display: block;
	margin-bottom: 8rpx;
}

.dist-label {
	font-size: 24rpx;
	opacity: 0.8;
}

/* 筛选区域 */
.filter-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.filter-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 24rpx;
}

.risk-filters {
	display: flex;
	gap: 16rpx;
}

.filter-item {
	flex: 1;
	padding: 16rpx 12rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	text-align: center;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
}

.filter-item.active {
	background: #007AFF;
	color: #ffffff;
	border-color: #007AFF;
}

.filter-item.high.active {
	background: #ff4757;
	border-color: #ff4757;
}

.filter-item.medium.active {
	background: #ffa502;
	border-color: #ffa502;
}

.filter-item.low.active {
	background: #2ed573;
	border-color: #2ed573;
}

.filter-text {
	font-size: 28rpx;
	font-weight: 600;
}

.filter-count {
	font-size: 24rpx;
	opacity: 0.8;
	margin-left: 8rpx;
}

/* 统计概览 */
.stats-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.stats-grid {
	display: flex;
	gap: 24rpx;
}

.stat-item {
	flex: 1;
	text-align: center;
}

.stat-number {
	font-size: 48rpx;
	font-weight: 700;
	display: block;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	opacity: 0.8;
}

/* 跟进状态筛选 */
.filter-tabs {
	display: flex;
	gap: 16rpx;
}

.filter-tab {
	flex: 1;
	padding: 16rpx 12rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	text-align: center;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
}

.filter-tab.active {
	background: #007AFF;
	color: #ffffff;
	border-color: #007AFF;
}

.tab-text {
	font-size: 28rpx;
	font-weight: 600;
}

.tab-count {
	font-size: 24rpx;
	opacity: 0.8;
	margin-left: 8rpx;
}

/* 客户列表 */
.customer-list {
	margin: 20rpx;
}

.customer-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 16rpx;
	transition: all 0.3s ease;
}

.customer-card:active {
	transform: scale(0.98);
}

.customer-header {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
}

.customer-info {
	flex: 1;
}

.customer-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.customer-phone {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.risk-badge {
	padding: 8rpx 16rpx;
	border-radius: 50rpx;
	font-size: 24rpx;
	font-weight: 600;
	text-align: center;
}

.risk-badge.high {
	background: #fff2f0;
	color: #ff4757;
}

.risk-badge.medium {
	background: #fff7e6;
	color: #ffa502;
}

.risk-badge.low {
	background: #f6ffed;
	color: #2ed573;
}

.risk-text {
	display: block;
	margin-bottom: 4rpx;
}

.risk-score {
	font-size: 20rpx;
	opacity: 0.8;
}

/* 风险分布统计颜色 */
.distribution-item.high .dist-count {
	color: #ff4757;
}

.distribution-item.medium .dist-count {
	color: #ffa502;
}

.distribution-item.low .dist-count {
	color: #2ed573;
}

/* 客户统计 */
.customer-stats {
	margin-bottom: 24rpx;
}

.stat-row {
	display: flex;
	gap: 24rpx;
	margin-bottom: 16rpx;
}

.stat-item {
	flex: 1;
	text-align: center;
}

.stat-label {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 8rpx;
}

.stat-value {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

/* 跟进状态标签 */
.follow-up-status {
	margin-bottom: 24rpx;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 50rpx;
	font-size: 24rpx;
	font-weight: 600;
	text-align: center;
}

.status-badge.none {
	background: #fff2f0;
	color: #ff4757;
}

.status-badge.overdue {
	background: #fff7e6;
	color: #ffa502;
}

.status-badge.active {
	background: #f6ffed;
	color: #2ed573;
}

.status-badge.inactive {
	background: #fff2f0;
	color: #ff4757;
}

.status-text {
	display: block;
	margin-bottom: 4rpx;
}

.priority-badge {
	padding: 4rpx 8rpx;
	border-radius: 50rpx;
	font-size: 20rpx;
	font-weight: 600;
	text-align: center;
}

.priority-badge.high {
	background: #ff4757;
	color: #ffffff;
}

.priority-badge.medium {
	background: #ffa502;
	color: #ffffff;
}

.priority-badge.low {
	background: #2ed573;
	color: #ffffff;
}

.priority-text {
	display: block;
	margin-top: 4rpx;
}

/* 原因标签 */
.reason-tags {
	margin-bottom: 24rpx;
}

.reason-tag {
	padding: 6rpx 12rpx;
	background: #fff2f0;
	color: #ff4757;
	font-size: 24rpx;
	border-radius: 16rpx;
	margin-right: 8rpx;
}

/* 建议操作 */
.suggestion-list {
	margin-bottom: 24rpx;
}

.suggestion-item {
	padding: 6rpx 12rpx;
	background: #fff2f0;
	color: #ff4757;
	font-size: 24rpx;
	border-radius: 16rpx;
	margin-right: 8rpx;
}

/* 操作按钮 */
.action-buttons {
	display: flex;
	gap: 16rpx;
}

.action-btn {
	flex: 1;
	padding: 16rpx 12rpx;
	border-radius: 8rpx;
	text-align: center;
	transition: all 0.3s ease;
}

.action-btn.primary {
	background: #007AFF;
	color: #ffffff;
}

.action-btn.secondary {
	background: #f8f9fa;
	color: #333333;
	border: 1rpx solid #e9ecef;
}

.action-btn.tertiary {
	background: #f8f9fa;
	color: #666666;
	border: 1rpx solid #e9ecef;
}

.action-btn:active {
	transform: scale(0.98);
}

.btn-text {
	font-size: 28rpx;
	font-weight: 500;
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 32rpx;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
}

.empty-desc {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.4;
}

/* 加载状态 */
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}
</style> 