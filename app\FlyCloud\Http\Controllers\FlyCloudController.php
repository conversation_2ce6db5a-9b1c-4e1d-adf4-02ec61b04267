<?php

namespace App\FlyCloud\Http\Controllers;

use App\Http\Controllers\Controller;
use App\FlyCloud\Services\FlyCloudService;
use App\FlyCloud\Models\WarehousePrinterBinding;
use App\Order\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class FlyCloudController extends Controller
{
    protected FlyCloudService $flyCloudService;

    public function __construct(FlyCloudService $flyCloudService)
    {
        $this->flyCloudService = $flyCloudService;
    }

    /**
     * 飞蛾云打印文本
     */
    public function printText(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'content' => 'required|string',
                'printer_sn' => 'nullable|string',
                'copies' => 'nullable|integer|min:1|max:10'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $content = $request->input('content');
            $options = [
                'printer_sn' => $request->input('printer_sn'),
                'copies' => $request->input('copies', 1)
            ];

            $result = $this->flyCloudService->printText($content, $options);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => '飞蛾云打印任务已发送成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '飞蛾云打印失败，请检查打印机状态'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud print text API error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '飞蛾云打印服务异常: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 飞蛾云打印HTML
     */
    public function printHtml(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'content' => 'required|string',
                'printer_sn' => 'nullable|string',
                'copies' => 'nullable|integer|min:1|max:10'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $content = $request->input('content');
            $options = [
                'printer_sn' => $request->input('printer_sn'),
                'copies' => $request->input('copies', 1)
            ];

            $result = $this->flyCloudService->printHtml($content, $options);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => '飞蛾云打印任务已发送成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '飞蛾云打印失败，请检查打印机状态'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud print HTML API error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => '飞蛾云打印服务异常: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 飞蛾云打印订单小票
     */
    public function printOrderReceipt(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|exists:orders,id',
                'printer_sn' => 'nullable|string',
                'copies' => 'nullable|integer|min:1|max:10'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $orderId = $request->input('order_id');
            $order = Order::with(['items.product'])->findOrFail($orderId);

            $options = [
                'printer_sn' => $request->input('printer_sn'),
                'copies' => $request->input('copies', 1)
            ];

            $result = $this->flyCloudService->printOrderReceipt($order, $options);

            if ($result) {
                Log::info('FlyCloud order receipt printed successfully', [
                    'order_id' => $orderId,
                    'order_no' => $order->order_no
                ]);

                return response()->json([
                    'success' => true,
                    'message' => '订单小票已发送到飞蛾云打印机'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '订单小票打印失败'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud print order receipt API error', [
                'error' => $e->getMessage(),
                'order_id' => $request->input('order_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => '订单小票打印失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 按仓库分单打印订单
     */
    public function printOrderByWarehouses(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|exists:orders,id',
                'print_type' => 'nullable|string|in:order,picking,delivery',
                'copies' => 'nullable|integer|min:1|max:10'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $orderId = $request->input('order_id');
            $order = Order::with(['items.product'])->findOrFail($orderId);

            $options = [
                'print_type' => $request->input('print_type', 'order'),
                'copies' => $request->input('copies', 1)
            ];

            $results = $this->flyCloudService->printOrderByWarehouses($order, $options);

            // 统计结果
            $successCount = 0;
            $failCount = 0;
            $totalWarehouses = count($results);

            foreach ($results as $result) {
                if (isset($result['success']) && $result['success']) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }

            return response()->json([
                'success' => $failCount === 0,
                'message' => "分单打印完成，共 {$totalWarehouses} 个仓库，成功 {$successCount} 个，失败 {$failCount} 个",
                'data' => [
                    'order_id' => $orderId,
                    'order_no' => $order->order_no,
                    'total_warehouses' => $totalWarehouses,
                    'success_count' => $successCount,
                    'fail_count' => $failCount,
                    'results' => $results
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud print order by warehouses API error', [
                'error' => $e->getMessage(),
                'order_id' => $request->input('order_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => '分单打印失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 打印指定仓库的订单分单
     */
    public function printWarehouseOrder(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|exists:orders,id',
                'warehouse_id' => 'required|integer|exists:warehouses,id',
                'print_type' => 'nullable|string|in:order,picking,delivery',
                'copies' => 'nullable|integer|min:1|max:10'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $orderId = $request->input('order_id');
            $warehouseId = $request->input('warehouse_id');
            $order = Order::with(['items.product'])->findOrFail($orderId);

            $options = [
                'print_type' => $request->input('print_type', 'order'),
                'copies' => $request->input('copies', 1)
            ];

            $result = $this->flyCloudService->printWarehouseOrder($order, $warehouseId, $options);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => "仓库 {$warehouseId} 的订单分单已发送到飞蛾云打印机"
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => "仓库 {$warehouseId} 的订单分单打印失败"
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud print warehouse order API error', [
                'error' => $e->getMessage(),
                'order_id' => $request->input('order_id'),
                'warehouse_id' => $request->input('warehouse_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => '仓库分单打印失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取飞蛾云打印机列表
     */
    public function getPrinters(): JsonResponse
    {
        try {
            $printers = $this->flyCloudService->getPrinters();

            return response()->json([
                'success' => true,
                'data' => $printers
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud get printers API error', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取打印机列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取飞蛾云打印机状态
     */
    public function getPrinterStatus(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'printer_sn' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $printerSn = $request->input('printer_sn');
            $status = $this->flyCloudService->getPrinterStatus($printerSn);

            return response()->json([
                'success' => true,
                'data' => $status
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud get printer status API error', [
                'error' => $e->getMessage(),
                'printer_sn' => $request->input('printer_sn')
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取打印机状态失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 添加飞蛾云打印机
     */
    public function addPrinter(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'sn' => 'required|string',
                'key' => 'required|string',
                'name' => 'nullable|string',
                'location' => 'nullable|string',
                'description' => 'nullable|string',
                'is_default' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $sn = $request->input('sn');
            $key = $request->input('key');
            $name = $request->input('name', '');

            $options = [
                'location' => $request->input('location'),
                'description' => $request->input('description'),
                'is_default' => $request->input('is_default', false),
                'created_by' => auth()->id()
            ];

            $result = $this->flyCloudService->addPrinter($sn, $key, $name, $options);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => '飞蛾云打印机添加成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '飞蛾云打印机添加失败'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud add printer API error', [
                'error' => $e->getMessage(),
                'sn' => $request->input('sn')
            ]);

            return response()->json([
                'success' => false,
                'message' => '添加打印机失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除飞蛾云打印机
     */
    public function deletePrinter(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'sn' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $sn = $request->input('sn');
            $result = $this->flyCloudService->deletePrinter($sn);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => '飞蛾云打印机删除成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '飞蛾云打印机删除失败'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud delete printer API error', [
                'error' => $e->getMessage(),
                'sn' => $request->input('sn')
            ]);

            return response()->json([
                'success' => false,
                'message' => '删除打印机失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 清空飞蛾云打印队列
     */
    public function clearPrintQueue(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'sn' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $sn = $request->input('sn');
            $result = $this->flyCloudService->clearPrintQueue($sn);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => '打印队列清空成功'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '打印队列清空失败'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('FlyCloud clear print queue API error', [
                'error' => $e->getMessage(),
                'sn' => $request->input('sn')
            ]);

            return response()->json([
                'success' => false,
                'message' => '清空打印队列失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取订单涉及的仓库列表
     */
    public function getOrderWarehouses(Request $request, $orderId): JsonResponse
    {
        try {
            $order = Order::with(['items.product'])->findOrFail($orderId);
            $warehouseIds = $this->flyCloudService->getOrderWarehouses($order);

            return response()->json([
                'success' => true,
                'data' => [
                    'order_id' => $orderId,
                    'order_no' => $order->order_no,
                    'warehouse_ids' => $warehouseIds,
                    'warehouse_count' => count($warehouseIds)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud get order warehouses API error', [
                'error' => $e->getMessage(),
                'order_id' => $orderId
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取订单仓库信息失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 绑定仓库和打印机
     */
    public function bindWarehousePrinter(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'warehouse_id' => 'required|integer|exists:warehouses,id',
                'flycloud_printer_id' => 'required|integer|exists:flycloud_printers,id',
                'print_type' => 'nullable|string|in:order,picking,delivery',
                'is_default' => 'nullable|boolean',
                'priority' => 'nullable|integer|min:0',
                'settings' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $warehouseId = $request->input('warehouse_id');
            $flyCloudPrinterId = $request->input('flycloud_printer_id');
            $printType = $request->input('print_type', 'order');

            $options = [
                'is_default' => $request->input('is_default', false),
                'priority' => $request->input('priority', 0),
                'settings' => $request->input('settings', []),
                'created_by' => auth()->id()
            ];

            $binding = WarehousePrinterBinding::bindWarehousePrinter(
                $warehouseId,
                $flyCloudPrinterId,
                $printType,
                $options
            );

            return response()->json([
                'success' => true,
                'message' => '仓库打印机绑定成功',
                'data' => [
                    'binding_id' => $binding->id,
                    'warehouse_id' => $warehouseId,
                    'printer_id' => $flyCloudPrinterId,
                    'print_type' => $printType,
                    'is_default' => $binding->is_default
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud bind warehouse printer API error', [
                'error' => $e->getMessage(),
                'warehouse_id' => $request->input('warehouse_id'),
                'printer_id' => $request->input('flycloud_printer_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => '仓库打印机绑定失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量绑定仓库打印机
     */
    public function batchBindWarehousePrinters(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'bindings' => 'required|array|min:1',
                'bindings.*.warehouse_id' => 'required|integer|exists:warehouses,id',
                'bindings.*.flycloud_printer_id' => 'required|integer|exists:flycloud_printers,id',
                'bindings.*.print_type' => 'nullable|string|in:order,picking,delivery',
                'bindings.*.options' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 400);
            }

            $bindings = $request->input('bindings');

            // 为每个绑定添加创建者信息
            foreach ($bindings as &$binding) {
                if (!isset($binding['options'])) {
                    $binding['options'] = [];
                }
                $binding['options']['created_by'] = auth()->id();
            }

            $results = $this->flyCloudService->bindWarehousePrinters($bindings);

            $successCount = count(array_filter($results, function($result) {
                return $result['success'];
            }));
            $totalCount = count($results);

            return response()->json([
                'success' => $successCount === $totalCount,
                'message' => "批量绑定完成，共 {$totalCount} 个，成功 {$successCount} 个",
                'data' => [
                    'total_count' => $totalCount,
                    'success_count' => $successCount,
                    'fail_count' => $totalCount - $successCount,
                    'results' => $results
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud batch bind warehouse printers API error', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => '批量绑定失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取仓库打印机绑定信息
     */
    public function getWarehousePrinterBindings(Request $request, $warehouseId): JsonResponse
    {
        try {
            $bindings = $this->flyCloudService->getWarehousePrinterBindings($warehouseId);

            return response()->json([
                'success' => true,
                'data' => [
                    'warehouse_id' => $warehouseId,
                    'bindings' => $bindings
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('FlyCloud get warehouse printer bindings API error', [
                'error' => $e->getMessage(),
                'warehouse_id' => $warehouseId
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取仓库打印机绑定信息失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 