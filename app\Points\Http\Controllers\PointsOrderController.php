<?php

namespace App\Points\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Points\Services\PointsOrderService;
use App\Points\Services\PointsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class PointsOrderController extends Controller
{
    protected PointsOrderService $orderService;
    protected PointsService $pointsService;

    public function __construct(PointsOrderService $orderService, PointsService $pointsService)
    {
        $this->orderService = $orderService;
        $this->pointsService = $pointsService;
    }

    /**
     * 创建积分订单
     */
    public function store(Request $request): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        $request->validate([
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|integer|exists:points_products,id',
            'items.*.quantity' => 'required|integer|min:1|max:999',
            'shipping_info' => 'sometimes|array',
            'shipping_info.address' => 'sometimes|array',
            'shipping_info.contact_name' => 'sometimes|string|max:50',
            'shipping_info.contact_phone' => 'sometimes|string|max:20',
            'shipping_info.delivery_date' => 'sometimes|date|after_or_equal:today',
            'shipping_info.delivery_time' => 'sometimes|string|max:50',
            'shipping_info.notes' => 'sometimes|string|max:500',
        ]);

        $result = $this->orderService->createOrder(
            $user->id,
            $request->items,
            $request->shipping_info ?? []
        );

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'data' => $result['order'],
                'message' => $result['message']
            ], 201);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 400);
        }
    }

    /**
     * 获取用户积分订单列表
     */
    public function index(Request $request): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        $request->validate([
            'status' => 'sometimes|string|in:pending,paid,shipped,delivered,cancelled,completed',
            'delivery_method' => 'sometimes|string|in:express,pickup,virtual',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100',
        ]);

        $filters = $request->only(['status', 'delivery_method', 'page', 'per_page']);
        $result = $this->orderService->getUserOrders($user->id, $filters);

        return response()->json([
            'success' => true,
            'data' => $result['data'],
            'pagination' => [
                'current_page' => $result['current_page'],
                'last_page' => $result['last_page'],
                'per_page' => $result['per_page'],
                'total' => $result['total'],
            ]
        ]);
    }

    /**
     * 获取积分订单详情
     */
    public function show(Request $request, int $id): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        $order = $this->orderService->getOrderDetail($id, $user->id);

        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => '订单不存在'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $order
        ]);
    }

    /**
     * 支付积分订单
     */
    public function pay(Request $request, int $id): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        $request->validate([
            'payment_no' => 'required|string|max:100',
        ]);

        $order = $this->orderService->getOrderDetail($id, $user->id);
        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => '订单不存在'
            ], 404);
        }

        if (!$order->canBePaid()) {
            return response()->json([
                'success' => false,
                'message' => '订单不能支付'
            ], 400);
        }

        $success = $this->orderService->payOrder($id, $request->payment_no);

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => '支付成功'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => '支付失败'
            ], 500);
        }
    }

    /**
     * 取消积分订单
     */
    public function cancel(Request $request, int $id): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        $request->validate([
            'reason' => 'sometimes|string|max:200',
        ]);

        $order = $this->orderService->getOrderDetail($id, $user->id);
        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => '订单不存在'
            ], 404);
        }

        if (!$order->canBeCancelled()) {
            return response()->json([
                'success' => false,
                'message' => '订单不能取消'
            ], 400);
        }

        $success = $this->orderService->cancelOrder($id, $request->reason);

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => '订单已取消'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => '取消失败'
            ], 500);
        }
    }

    /**
     * 确认收货
     */
    public function confirmDelivery(Request $request, int $id): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        $order = $this->orderService->getOrderDetail($id, $user->id);
        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => '订单不存在'
            ], 404);
        }

        if ($order->status !== 'shipped') {
            return response()->json([
                'success' => false,
                'message' => '订单状态不正确'
            ], 400);
        }

        $success = $this->orderService->confirmDelivery($id);

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => '确认收货成功'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => '确认收货失败'
            ], 500);
        }
    }

    /**
     * 获取订单状态统计
     */
    public function statusStats(Request $request): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        $stats = [
            'pending' => 0,
            'paid' => 0,
            'shipped' => 0,
            'delivered' => 0,
            'completed' => 0,
            'cancelled' => 0,
        ];

        $statusCounts = \App\Points\Models\PointsOrder::byUser($user->id)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        foreach ($statusCounts as $status => $count) {
            if (isset($stats[$status])) {
                $stats[$status] = $count;
            }
        }

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * 预览订单（计算总价但不创建订单）
     */
    public function preview(Request $request): JsonResponse
    {
        $user = auth()->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ], 401);
        }

        $request->validate([
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|integer|exists:points_products,id',
            'items.*.quantity' => 'required|integer|min:1|max:999',
        ]);

        $totalPoints = 0;
        $totalCash = 0;
        $items = [];
        $errors = [];

        foreach ($request->items as $item) {
            $product = \App\Points\Models\PointsProduct::find($item['product_id']);
            
            if (!$product) {
                $errors[] = "商品不存在：{$item['product_id']}";
                continue;
            }

            if (!$product->canUserExchange($user)) {
                $errors[] = "商品不可兑换：{$product->name}";
                continue;
            }

            $quantity = $item['quantity'];
            $itemPoints = $product->points_price * $quantity;
            $itemCash = $product->cash_price * $quantity;

            $totalPoints += $itemPoints;
            $totalCash += $itemCash;

            $items[] = [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'product_image' => $product->image,
                'points_price' => $product->points_price,
                'cash_price' => $product->cash_price,
                'quantity' => $quantity,
                'total_points' => $itemPoints,
                'total_cash' => $itemCash,
                'exchange_type' => $product->exchange_type,
                'category' => $product->category,
            ];
        }

        if (!empty($errors)) {
            return response()->json([
                'success' => false,
                'message' => '商品验证失败',
                'errors' => $errors
            ], 400);
        }

        // 检查用户积分是否充足
        $pointsSufficient = $user->member_points >= $totalPoints;

        return response()->json([
            'success' => true,
            'data' => [
                'items' => $items,
                'total_points' => $totalPoints,
                'cash_amount' => $totalCash,
                'user_points_balance' => $user->member_points,
                'points_sufficient' => $pointsSufficient,
                'is_pure_points' => $totalCash == 0,
                'is_mixed_payment' => $totalCash > 0,
            ]
        ]);
    }
} 