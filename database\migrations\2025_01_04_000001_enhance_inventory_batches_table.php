<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inventory_batches', function (Blueprint $table) {
            // 批次状态管理
            $table->enum('status', ['normal', 'near_expiry', 'expired', 'damaged', 'recalled', 'quarantine', 'disposed'])
                  ->default('normal')
                  ->after('unit_id')
                  ->comment('批次状态');
            
            // 质量管理
            $table->enum('quality_status', ['pending', 'passed', 'failed', 'partial'])
                  ->default('pending')
                  ->after('status')
                  ->comment('质量检验状态');
            
            $table->json('quality_data')->nullable()->after('quality_status')->comment('质量检验数据');
            $table->timestamp('quality_checked_at')->nullable()->after('quality_data')->comment('质量检验时间');
            $table->foreignId('quality_checked_by')->nullable()->constrained('users')->after('quality_checked_at')->comment('质量检验人');
            
            // 供应商和批次追溯信息
            $table->string('supplier_batch_no', 100)->nullable()->after('batch_code')->comment('供应商批次号');
            $table->string('manufacturer', 100)->nullable()->after('supplier_batch_no')->comment('生产厂家');
            $table->string('origin_country', 50)->nullable()->after('manufacturer')->comment('原产国');
            
            // 存储条件
            $table->json('storage_conditions')->nullable()->after('notes')->comment('存储条件要求');
            $table->decimal('storage_temperature_min', 5, 2)->nullable()->after('storage_conditions')->comment('最低存储温度');
            $table->decimal('storage_temperature_max', 5, 2)->nullable()->after('storage_temperature_min')->comment('最高存储温度');
            $table->decimal('storage_humidity_min', 5, 2)->nullable()->after('storage_temperature_max')->comment('最低存储湿度');
            $table->decimal('storage_humidity_max', 5, 2)->nullable()->after('storage_humidity_min')->comment('最高存储湿度');
            
            // 批次操作记录
            $table->timestamp('last_moved_at')->nullable()->after('storage_humidity_max')->comment('最后移动时间');
            $table->foreignId('last_moved_by')->nullable()->constrained('users')->after('last_moved_at')->comment('最后移动人');
            $table->text('move_reason')->nullable()->after('last_moved_by')->comment('移动原因');
            
            // 预警设置
            $table->integer('expiry_warning_days')->default(7)->after('move_reason')->comment('过期预警天数');
            $table->boolean('auto_dispose_expired')->default(false)->after('expiry_warning_days')->comment('是否自动处理过期商品');
            
            // 成本核算
            $table->decimal('total_cost', 12, 2)->nullable()->after('purchase_price')->comment('总成本（含运费等）');
            $table->decimal('unit_cost', 10, 4)->nullable()->after('total_cost')->comment('单位成本');
            $table->json('cost_breakdown')->nullable()->after('unit_cost')->comment('成本明细');
            
            // 批次锁定（用于预留等）
            $table->decimal('locked_quantity', 10, 2)->default(0)->after('quantity')->comment('锁定数量');
            $table->decimal('available_quantity', 10, 2)->default(0)->after('locked_quantity')->comment('可用数量');
            
            // 索引优化
            $table->index('status');
            $table->index('quality_status');
            $table->index('supplier_batch_no');
            $table->index(['expiry_date', 'status']);
            $table->index(['production_date', 'expiry_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inventory_batches', function (Blueprint $table) {
            $table->dropIndex(['status']);
            $table->dropIndex(['quality_status']);
            $table->dropIndex(['supplier_batch_no']);
            $table->dropIndex(['expiry_date', 'status']);
            $table->dropIndex(['production_date', 'expiry_date']);
            
            $table->dropForeign(['quality_checked_by']);
            $table->dropForeign(['last_moved_by']);
            
            $table->dropColumn([
                'status',
                'quality_status',
                'quality_data',
                'quality_checked_at',
                'quality_checked_by',
                'supplier_batch_no',
                'manufacturer',
                'origin_country',
                'storage_conditions',
                'storage_temperature_min',
                'storage_temperature_max',
                'storage_humidity_min',
                'storage_humidity_max',
                'last_moved_at',
                'last_moved_by',
                'move_reason',
                'expiry_warning_days',
                'auto_dispose_expired',
                'total_cost',
                'unit_cost',
                'cost_breakdown',
                'locked_quantity',
                'available_quantity',
            ]);
        });
    }
}; 