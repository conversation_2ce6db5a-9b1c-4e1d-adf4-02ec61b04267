<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // 记录订单使用的库存策略
            $table->json('inventory_policies_used')
                  ->nullable()
                  ->comment('订单中各商品使用的库存策略记录');
            
            // 是否包含负库存商品
            $table->boolean('has_negative_stock_items')
                  ->default(false)
                  ->comment('订单是否包含负库存商品');
            
            // 负库存是否已批准
            $table->boolean('negative_stock_approved')
                  ->default(false)
                  ->comment('负库存是否已批准');
            
            // 批准人ID
            $table->unsignedBigInteger('approved_by_id')
                  ->nullable()
                  ->comment('负库存批准人ID');
            
            // 批准时间
            $table->timestamp('approved_at')
                  ->nullable()
                  ->comment('负库存批准时间');
            
            // 库存警告信息
            $table->json('stock_warnings')
                  ->nullable()
                  ->comment('库存警告信息');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn([
                'inventory_policies_used',
                'has_negative_stock_items',
                'negative_stock_approved',
                'approved_by_id',
                'approved_at',
                'stock_warnings'
            ]);
        });
    }
}; 