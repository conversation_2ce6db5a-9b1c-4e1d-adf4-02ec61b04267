<template>
  <div class="login-container">
    <!-- 左侧背景区域 -->
    <div class="login-left">
      <div class="animated-bg">
        <!-- 动态背景元素 -->
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
        <div class="floating-circle circle-4"></div>
      </div>
      
      <div class="left-content">
        <h1 class="system-title">天心食品配送系统</h1>
        <div class="illustration-placeholder">
          <!-- 3D插画占位区域 -->
          <div class="logistics-scene">
            <div class="scene-element truck"></div>
            <div class="scene-element warehouse"></div>
            <div class="scene-element delivery"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录表单区域 -->
    <div class="login-right">
      <div class="login-form-container">
        <h2 class="welcome-title">欢迎登录</h2>
        
        <el-form :model="loginForm" class="login-form" size="large" ref="loginFormRef" :rules="loginRules">
          <el-form-item>
            <el-input
              v-model="loginForm.username"
              placeholder="请输入账号"
            />
          </el-form-item>
          
          <el-form-item>
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
          
          <el-form-item prop="captcha">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.captcha"
                placeholder="验证码"
                class="captcha-input"
                @keyup="handleKeyup"
              >
                <template #prefix>
                  <el-icon><Picture /></el-icon>
                </template>
              </el-input>
              <div class="captcha-image" @click="refreshCaptcha">
                <img :src="captchaUrl" alt="验证码" />
                <span class="refresh-text">点击刷新</span>
              </div>
            </div>
          </el-form-item>
          
          <el-form-item>
            <el-button 
              type="primary" 
              class="login-btn"
              @click="handleLogin"
              :loading="loading"
            >
              立即登录
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="footer-info">
          <p>© 2024 天心食品配送系统 版权所有</p>
          <p>技术支持：天心食品科技有限公司</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { User, Lock, Picture } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { getCaptcha } from '@/api/auth'
import type { FormInstance, FormRules } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 登录表单数据
const loginForm = reactive({
  username: 'admin',
  password: '123456',
  captcha: ''
})

// 加载状态
const loading = ref(false)

// 验证码图片
const captchaUrl = ref('')

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码为4位', trigger: 'blur' }
  ]
}

// 刷新验证码
const refreshCaptcha = () => {
  captchaUrl.value = getCaptcha()
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    // 表单验证
    await loginFormRef.value.validate()
    
    loading.value = true
    
    // 调用登录API
    await userStore.loginUser({
      username: loginForm.username,
      password: loginForm.password,
      captcha: loginForm.captcha
    })
    
    // 登录成功，跳转到首页
    router.push('/dashboard')
    
  } catch (error) {
    console.error('登录失败:', error)
    // 刷新验证码
    refreshCaptcha()
    loginForm.captcha = ''
  } finally {
    loading.value = false
  }
}

// 键盘事件处理
const handleKeyup = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    handleLogin()
  }
}

// 初始化验证码
onMounted(() => {
  refreshCaptcha()
})
</script>

<style scoped>
.login-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  overflow: hidden;
}

/* 左侧背景区域 */
.login-left {
  flex: 1;
  width: 50%;
  min-width: 500px;
  position: relative;
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 50%, #16a34a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.animated-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.circle-4 {
  width: 80px;
  height: 80px;
  top: 30%;
  right: 30%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

.left-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}

.system-title {
  font-size: 48px;
  font-weight: 600;
  margin-bottom: 60px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: 2px;
}

.illustration-placeholder {
  width: 500px;
  height: 300px;
  margin: 0 auto;
  position: relative;
}

.logistics-scene {
  width: 100%;
  height: 100%;
  position: relative;
}

.scene-element {
  position: absolute;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  animation: sceneFloat 4s ease-in-out infinite;
}

.truck {
  width: 120px;
  height: 60px;
  top: 50%;
  left: 20%;
  animation-delay: 0s;
}

.warehouse {
  width: 100px;
  height: 80px;
  top: 20%;
  right: 30%;
  animation-delay: 1.5s;
}

.delivery {
  width: 80px;
  height: 80px;
  bottom: 30%;
  right: 20%;
  animation-delay: 3s;
}

@keyframes sceneFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 右侧登录表单区域 */
.login-right {
  width: 50%;
  min-width: 400px;
  max-width: 500px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: -2px 0 20px rgba(0, 0, 0, 0.1);
}

.login-form-container {
  width: 360px;
  padding: 40px 0;
}

.welcome-title {
  font-size: 28px;
  font-weight: 500;
  color: #333;
  text-align: center;
  margin-bottom: 40px;
}

.login-form {
  margin-bottom: 30px;
}

.login-form .el-form-item {
  margin-bottom: 24px;
}

.captcha-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 100px;
  height: 40px;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.captcha-image:hover {
  border-color: #28a745;
  background: #f0f9ff;
}

.captcha-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.refresh-text {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  text-align: center;
  padding: 2px;
  opacity: 0;
  transition: opacity 0.3s;
}

.captcha-image:hover .refresh-text {
  opacity: 1;
}

.login-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border: none;
  border-radius: 6px;
  transition: all 0.3s;
}

.login-btn:hover {
  background: linear-gradient(135deg, #16a34a, #15803d);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.4);
}

.footer-info {
  text-align: center;
  font-size: 12px;
  color: #999;
  line-height: 1.6;
}

.footer-info p {
  margin: 4px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }
  
  .login-left {
    width: 100%;
    height: 200px;
    min-width: auto;
  }
  
  .login-right {
    width: 100%;
    min-width: auto;
    max-width: none;
    flex: 1;
  }
  
  .system-title {
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  .illustration-placeholder {
    display: none;
  }
}
</style> 