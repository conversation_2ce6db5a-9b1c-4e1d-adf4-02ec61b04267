<?php

namespace App\Warehouse\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Warehouse\Models\Warehouse;
use App\Product\Models\Product;
use App\Inventory\Models\Inventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class InventoryController extends Controller
{
    /**
     * 获取仓库中所有商品库存
     *
     * @param int $warehouseId 仓库ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function index($warehouseId)
    {
        $warehouse = Warehouse::findOrFail($warehouseId);
        $inventories = Inventory::where('warehouse_id', $warehouseId)
            ->with('product')
            ->paginate(10);
            
        return response()->json(ApiResponse::success($inventories));
    }
    
    /**
     * 获取仓库中特定商品的库存
     *
     * @param int $warehouseId 仓库ID
     * @param int $productId 商品ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($warehouseId, $productId)
    {
        $warehouse = Warehouse::findOrFail($warehouseId);
        $product = Product::findOrFail($productId);
        
        $inventory = Inventory::where('warehouse_id', $warehouseId)
            ->where('product_id', $productId)
            ->first();
            
        if (!$inventory) {
            return response()->json(ApiResponse::error('此仓库中没有该商品库存', 404), 404);
        }
        
        // 获取基本单位库存
        $stockInBaseUnit = $inventory->getStockInBaseUnit();
        
        // 获取各单位的库存量
        $unitsStock = [
            'kg' => $product->getStockInUnit('kg'),
            'g' => $product->getStockInUnit('g'),
            'lb' => $product->getStockInUnit('lb'),
            'pcs' => $product->getStockInUnit('pcs'),
        ];
        
        $data = [
            'inventory' => $inventory,
            'stock_in_base_unit' => $stockInBaseUnit,
            'units_stock' => $unitsStock,
            'product' => $product,
        ];
        
        return response()->json(ApiResponse::success($data));
    }
    
    /**
     * 更新仓库中商品的库存
     *
     * @param Request $request
     * @param int $warehouseId 仓库ID
     * @param int $productId 商品ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStock(Request $request, $warehouseId, $productId)
    {
        // 验证输入
        $validator = Validator::make($request->all(), [
            'stock' => 'required|numeric|min:0',
            'unit_id' => 'required|exists:units,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $warehouse = Warehouse::findOrFail($warehouseId);
        $product = Product::findOrFail($productId);
        $stock = $request->input('stock');
        $unitId = $request->input('unit_id');
        
        // 查找或创建库存记录
        $inventory = Inventory::firstOrCreate(
            [
                'warehouse_id' => $warehouseId,
                'product_id' => $productId,
            ],
            [
                'unit_id' => $product->base_unit_id,
                'stock' => 0,
            ]
        );
        
        // 设置新的库存量
        $success = $inventory->setStockInUnit($stock, $unitId);
        
        if (!$success) {
            return response()->json(ApiResponse::error('更新库存失败，请检查单位转换关系', 422), 422);
        }
        
        // 刷新模型以获取最新数据
        $inventory->refresh();
        
        // 构建响应数据，包括不同单位下的库存
        $responseData = [
            'inventory' => $inventory,
            'stock_in_base_unit' => $inventory->stock,
            'display_stock' => $inventory->stock_in_display_unit,
            'base_unit' => $product->baseUnit,
            'display_unit' => $product->getSaleDefaultUnit()
        ];
        
        return response()->json(ApiResponse::success($responseData, '商品库存更新成功'));
    }
    
    /**
     * 从仓库中移除商品
     *
     * @param int $warehouseId 仓库ID
     * @param int $productId 商品ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeProduct($warehouseId, $productId)
    {
        $inventory = Inventory::where('warehouse_id', $warehouseId)
            ->where('product_id', $productId)
            ->first();
            
        if (!$inventory) {
            return response()->json(ApiResponse::error('此仓库中没有该商品库存', 404), 404);
        }
        
        $product = Product::findOrFail($productId);
        $warehouse = Warehouse::findOrFail($warehouseId);
        
        // 删除库存记录
        $inventory->delete();
        
        // 注意：商品总库存现在动态计算，无需手动更新
        $productTotalStock = Inventory::where('product_id', $productId)->sum('stock');
        \Illuminate\Support\Facades\Log::info('商品库存记录删除', [
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'remaining_total_stock' => $productTotalStock
        ]);
        
        // 更新仓库总库存
        $warehouseTotalStock = Inventory::where('warehouse_id', $warehouseId)->sum('stock');
        $warehouse->total_stock = $warehouseTotalStock;
        $warehouse->save();
        
        return response()->json(ApiResponse::success(null, '商品已从仓库中移除'));
    }
    
    /**
     * 将商品添加到仓库
     *
     * @param Request $request
     * @param int $warehouseId 仓库ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function addProduct(Request $request, $warehouseId)
    {
        // 验证输入
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'stock' => 'required|numeric|min:0',
            'unit_id' => 'required|exists:units,id',
            'min_stock_level' => 'nullable|numeric|min:0',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $warehouse = Warehouse::findOrFail($warehouseId);
        $product = Product::findOrFail($request->input('product_id'));
        $stock = $request->input('stock');
        $unitId = $request->input('unit_id');
        
        // 检查是否已存在库存记录
        $existingInventory = Inventory::where('warehouse_id', $warehouseId)
            ->where('product_id', $request->input('product_id'))
            ->first();
            
        if ($existingInventory) {
            return response()->json(ApiResponse::error('该商品已存在于仓库中，请使用更新库存功能', 422), 422);
        }
        
        // 创建新的库存记录，总是使用产品的基本单位
        $inventory = Inventory::create([
            'warehouse_id' => $warehouseId,
            'product_id' => $request->input('product_id'),
            'unit_id' => $product->base_unit_id,
            'stock' => 0, // 先设置为0，然后通过setStockInUnit设置实际库存
            'min_stock_level' => $request->input('min_stock_level', 0),
        ]);
        
        // 设置库存，自动转换为基本单位
        $success = $inventory->setStockInUnit($stock, $unitId);
        
        if (!$success) {
            // 如果设置失败，删除刚创建的记录
            $inventory->delete();
            return response()->json(ApiResponse::error('添加库存失败，请检查单位转换关系', 422), 422);
        }
        
        // 刷新库存数据
        $inventory->refresh();
        
        // 构建响应数据
        $responseData = [
            'inventory' => $inventory,
            'stock_in_base_unit' => $inventory->stock,
            'display_stock' => $inventory->stock_in_display_unit,
            'base_unit' => $product->baseUnit,
            'display_unit' => $product->getSaleDefaultUnit()
        ];
        
        return response()->json(ApiResponse::success($responseData, '商品已添加到仓库'), 201);
    }
    
    /**
     * 分配商品到仓库
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function assignProductWarehouse(Request $request)
    {
        // 验证输入
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'warehouse_id' => 'nullable|exists:warehouses,id',
            'initial_stock' => 'nullable|numeric|min:0', // 允许设置初始库存
            'unit_id' => 'nullable|exists:units,id', // 允许指定单位
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        $productId = $request->input('product_id');
        $warehouseId = $request->input('warehouse_id');
        $initialStock = $request->input('initial_stock', 0);
        $unitId = $request->input('unit_id');
        
        // 使用InventoryService处理库存初始化
        $inventoryService = new \App\Warehouse\Services\InventoryService();
        $result = $inventoryService->initializeOrUpdateStock($productId, $warehouseId, $initialStock, $unitId);
        
        if (!$result['success']) {
            return response()->json(ApiResponse::error($result['message'], 422), 422);
        }
        
        return response()->json(ApiResponse::success($result['inventory'], $result['message']));
    }
} 