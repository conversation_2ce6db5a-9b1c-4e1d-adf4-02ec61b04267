<?php

namespace App\Inventory\Models;

use App\Models\User;
use App\Warehouse\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BatchOperation extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'batch_id',
        'operation_type',
        'quantity_before',
        'quantity_after',
        'quantity_change',
        'status_before',
        'status_after',
        'operation_data',
        'reason',
        'notes',
        'reference_type',
        'reference_id',
        'reference_no',
        'warehouse_id',
        'location',
        'operated_by',
        'operated_at',
    ];

    /**
     * 应该被转换的属性
     */
    protected $casts = [
        'quantity_before' => 'decimal:2',
        'quantity_after' => 'decimal:2',
        'quantity_change' => 'decimal:2',
        'operation_data' => 'array',
        'operated_at' => 'datetime',
    ];

    /**
     * 获取关联的批次
     */
    public function batch()
    {
        return $this->belongsTo(InventoryBatch::class, 'batch_id');
    }

    /**
     * 获取操作人
     */
    public function operator()
    {
        return $this->belongsTo(User::class, 'operated_by');
    }

    /**
     * 获取仓库
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * 获取操作类型文本
     * 
     * @return string
     */
    public function getOperationTypeTextAttribute()
    {
        return match($this->operation_type) {
            'created' => '创建',
            'received' => '收货',
            'shipped' => '出货',
            'moved' => '移动',
            'adjusted' => '调整',
            'quality_check' => '质量检验',
            'status_change' => '状态变更',
            'locked' => '锁定',
            'unlocked' => '解锁',
            'damaged' => '损坏',
            'expired' => '过期',
            'disposed' => '处置',
            'recalled' => '召回',
            default => '未知操作'
        };
    }

    /**
     * 作用域：按操作类型过滤
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('operation_type', $type);
    }

    /**
     * 作用域：按时间范围过滤
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('operated_at', [$startDate, $endDate]);
    }

    /**
     * 作用域：按操作人过滤
     */
    public function scopeByOperator($query, $operatorId)
    {
        return $query->where('operated_by', $operatorId);
    }
} 