<?php

namespace App\Employee\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Employee\Models\Employee;
use App\Employee\Api\ApiResponse;
use App\Employee\Services\EmployeeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * @var EmployeeService
     */
    protected $employeeService;
    
    /**
     * 构造函数
     * 
     * @param EmployeeService $employeeService 员工服务
     */
    public function __construct(EmployeeService $employeeService)
    {
        $this->employeeService = $employeeService;
    }
    
    /**
     * 员工登录
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        // 只记录必要的登录尝试信息，不记录敏感数据
        Log::info('员工登录尝试', [
            'ip' => $request->ip(),
            'timestamp' => now()->toISOString()
        ]);

        try {
            // 内层try-catch，捕获验证异常
            try {
                $request->validate([
                    'username' => 'required|string',
                    'password' => 'required|string',
                ]);
            } catch (ValidationException $ve) {
                Log::warning('员工登录验证失败', [
                    'ip' => $request->ip(),
                    'timestamp' => now()->toISOString()
                ]);
                
                return response()->json(ApiResponse::error('登录验证失败: ' . collect($ve->errors())->first()[0], 422, $ve->errors()), 422);
            }
    
            // 查找员工记录
            try {
                $employee = Employee::where('username', $request->username)->first();
            } catch (\Exception $e) {
                Log::error('数据库查询错误', [
                    'error_type' => 'database_error',
                    'timestamp' => now()->toISOString()
                ]);
                
                return response()->json(ApiResponse::error('数据库查询失败，请联系管理员', 500), 500);
            }
    
            // 检查员工是否存在和密码是否匹配
            if (!$employee) {
                Log::warning('员工登录失败：用户不存在', [
                    'ip' => $request->ip(),
                    'timestamp' => now()->toISOString()
                ]);
                
                return response()->json(ApiResponse::error('用户名或密码错误', 401), 401);
            }
            
            if (!Hash::check($request->password, $employee->password)) {
                Log::warning('员工登录失败：密码错误', [
                    'employee_id' => $employee->id,
                    'ip' => $request->ip(),
                    'timestamp' => now()->toISOString()
                ]);
                
                return response()->json(ApiResponse::error('用户名或密码错误', 401), 401);
            }
            
            Log::info('员工登录成功', [
                'employee_id' => $employee->id,
                'role' => $employee->role,
                'timestamp' => now()->toISOString()
            ]);
    
            // 删除旧token
            try {
                $employee->tokens()->delete();
            } catch (\Exception $e) {
                Log::error('清理旧token失败', [
                    'employee_id' => $employee->id,
                    'timestamp' => now()->toISOString()
                ]);
                // 继续处理，不中断登录流程
            }
            
            // 创建新token
            try {
                $token = $employee->createToken('employee-token', ['*'])->plainTextToken;
                
                Log::info('Token生成成功', [
                    'employee_id' => $employee->id,
                    'timestamp' => now()->toISOString()
                ]);
            } catch (\Exception $e) {
                Log::error('Token生成失败', [
                    'employee_id' => $employee->id,
                    'timestamp' => now()->toISOString()
                ]);
                
                return response()->json(ApiResponse::error('生成访问令牌失败，请稍后再试', 500), 500);
            }
            
            // 设置permissions
            $permissions = $this->getPermissionsByRole($employee->role);
    
            // 构建响应
            $response = ApiResponse::success([
                'token' => $token,
                'accessToken' => $token,
                'employee' => [
                    'id' => $employee->id,
                    'name' => $employee->name,
                    'username' => $employee->username,
                    'role' => $employee->role,
                    'position' => $employee->position,
                    'permissions' => $permissions
                ]
            ], '登录成功');
            
            return response()->json($response);
            
        } catch (\Exception $e) {
            // 记录错误但不泄露敏感信息
            Log::error('员工登录系统错误', [
                'error_type' => 'system_error',
                'timestamp' => now()->toISOString(),
                'ip' => $request->ip()
            ]);
            
            // 在生产环境中不返回详细错误信息
            $debug = config('app.env') === 'local' ? $e->getMessage() : null;
            return response()->json(ApiResponse::error('服务器内部错误，请稍后重试', 500, $debug), 500);
        }
    }

    /**
     * 获取当前登录员工信息
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function me(Request $request)
    {
        try {
            // 记录请求信息用于调试
            Log::debug('API请求：me', [
                'token' => $request->bearerToken(),
                'url' => $request->fullUrl(),
                'method' => $request->method()
            ]);

            // 尝试多种方式获取员工
            $employee = null;
            
            // 方法1：从sanctum获取
            $employee = $request->user('sanctum');
            if ($employee) {
                Log::debug('通过sanctum守卫获取到员工', [
                    'employee_id' => $employee->id,
                    'role' => $employee->role
                ]);
            } 
            // 方法2：直接通过token查找
            else if ($request->bearerToken()) {
                $token = $request->bearerToken();
                Log::debug('通过bearerToken()获取token', [
                    'token' => substr($token, 0, 10) . '...'
                ]);
                
                // 使用Sanctum提供的findToken方法
                $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
                
                if ($tokenModel) {
                    Log::debug('找到token模型', [
                        'tokenable_type' => $tokenModel->tokenable_type,
                        'tokenable_id' => $tokenModel->tokenable_id
                    ]);
                    
                    $employee = $tokenModel->tokenable;
                    Log::debug('通过token模型获取到员工', [
                        'employee_id' => $employee->id,
                        'role' => $employee->role
                    ]);
                }
            }
            
            // 如果没有找到员工，返回未授权错误
            if (!$employee) {
                Log::warning('API请求me无法找到当前员工', [
                    'token' => substr($request->bearerToken() ?? '', 0, 10) . '...',
                    'ip' => $request->ip()
                ]);
                
                return response()->json(ApiResponse::error('未授权访问，请先登录', 401), 401);
            }
            
            // 设置permissions
            $permissions = $this->getPermissionsByRole($employee->role);
            
            // 构建响应数据
            $employeeData = [
                'id' => $employee->id,
                'name' => $employee->name,
                'username' => $employee->username,
                'role' => $employee->role,
                'position' => $employee->position,
                'permissions' => $permissions,
                'created_at' => $employee->created_at,
                'updated_at' => $employee->updated_at
            ];
            
            if ($employee->phone) {
                $employeeData['phone'] = $employee->phone;
            }
            
            return response()->json(ApiResponse::success($employeeData, '获取员工信息成功'));
            
        } catch (\Exception $e) {
            Log::error('获取员工信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取员工信息失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 员工登出
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        try {
            $employee = $request->user('sanctum');
            
            if (!$employee) {
                return response()->json(ApiResponse::error('未授权访问，请先登录', 401), 401);
            }
            
            // 撤销当前token
            if ($request->bearerToken()) {
                $employee->currentAccessToken()->delete();
            } else {
                // 如果没有传递token，撤销所有token
                $employee->tokens()->delete();
            }
            
            Log::info('员工登出成功', [
                'employee_id' => $employee->id,
                'username' => $employee->username,
                'role' => $employee->role
            ]);
            
            return response()->json(ApiResponse::success(null, '登出成功'));
            
        } catch (\Exception $e) {
            Log::error('员工登出失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('登出失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 根据角色获取权限列表
     *
     * @param  string  $role
     * @return array
     */
    private function getPermissionsByRole($role)
    {
        $allPermissions = [
            'dashboard:view',
            'employees:view',
            'employees:create',
            'employees:edit',
            'employees:delete',
            'orders:view',
            'orders:create',
            'orders:edit',
            'orders:delete',
            'products:view',
            'products:create',
            'products:edit',
            'products:delete',
            'customers:view',
            'customers:create',
            'customers:edit',
            'customers:delete',
            'inventory:view',
            'inventory:create',
            'inventory:edit',
            'inventory:delete',
            'settings:view',
            'settings:edit',
            'crm:view',
            'crm:edit',
            'delivery:view',
            'delivery:manage',
            'warehouse:view',
            'warehouse:manage',
        ];
        
        $adminPermissions = $allPermissions;
        
        $managerPermissions = array_diff($allPermissions, [
            'employees:delete',
            'settings:edit',
        ]);
        
        $staffPermissions = [
            'dashboard:view',
            'orders:view',
            'orders:create',
            'orders:edit',
            'products:view',
            'customers:view',
            'customers:create',
            'customers:edit',
            'inventory:view',
        ];
        
        $crmAgentPermissions = [
            'dashboard:view',
            'customers:view',
            'customers:create',
            'customers:edit',
            'crm:view',
            'crm:edit',
        ];
        
        $deliveryPermissions = [
            'dashboard:view',
            'orders:view',
            'delivery:view',
            'delivery:manage',
        ];
        
        $warehouseManagerPermissions = [
            'dashboard:view',
            'products:view',
            'inventory:view',
            'inventory:create',
            'inventory:edit',
            'warehouse:view',
            'warehouse:manage',
        ];
        
        switch ($role) {
            case Employee::ROLE_ADMIN:
                return $adminPermissions;
            case Employee::ROLE_MANAGER:
                return $managerPermissions;
            case Employee::ROLE_STAFF:
                return $staffPermissions;
            case Employee::ROLE_CRM_AGENT:
                return $crmAgentPermissions;
            case Employee::ROLE_DELIVERY:
                return $deliveryPermissions;
            case Employee::ROLE_WAREHOUSE_MANAGER:
                return $warehouseManagerPermissions;
            default:
                return [];
        }
    }
} 