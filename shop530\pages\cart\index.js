/**
 * 购物车页面 - 重构版
 * 
 * 功能：
 * - 购物车商品展示、选择、数量修改、删除
 * - 全选/取消全选
 * - 结算功能
 * - 推荐商品展示
 * - 缓存机制
 * - 数据恢复机制
 */

// 引入依赖
const { isLoggedIn } = require('../../utils/login-state-manager');
const apiModule = require('../../utils/api');
const api = apiModule.api;
// 移除Toast依赖，使用wx原生API
// const Toast = require('@vant/weapp/toast/toast');
const Dialog = require('@vant/weapp/dialog/dialog');

// 引入统一购物车管理器
const {
  addToCart,
  getCartList,
  updateCartQuantity,
  removeFromCart,
  addListener,
  removeListener,
  CartEvents,
  cartManager
} = require('../../utils/cart-unified');

// 页面配置
Page({
  // ==================== 数据 ====================
  data: {
    // 核心数据
    cartList: [],
    cartItems: [],  // 保留兼容性
    recommendList: [],

    // 状态管理
    loading: true,
    showSkeleton: true,
    isEditMode: false,
    isLoggedIn: false,
    isRefreshing: false,
    isEmpty: false,

    // 计算属性
    allSelected: false,
    selectedCount: 0,
    totalPrice: '0.00',
    totalCount: 0,

    // 配送信息
    deliveryText: '明日达',
    deliveryDate: '',

    // 更新状态追踪
    pendingUpdates: {},

    // 动画相关
    cartAnimation: null,
    defaultImage: '/images/product-default.png', // 默认图片
    debugMode: false // 调试模式
  },

  // ==================== 生命周期 ====================
  onLoad() {
    console.log('购物车页面加载');

    // 初始化所有定时器对象
    this._timers = {};
    this._debugTimer = null;
    this.tabBarUpdateTimer = null;

    // 初始化购物车监听器
    this.initCartListener();

    // 初始化页面
    this.initPage();
  },
  
  onShow() {
    console.log('购物车页面显示');
    
    // 检查登录状态是否变化
    const currentLoginState = isLoggedIn();
    const previousLoginState = this.data.isLoggedIn;
    
    console.log('登录状态检查:', { current: currentLoginState, previous: previousLoginState });
      
    if (currentLoginState !== previousLoginState) {
      console.log('登录状态变化，刷新购物车');
      this.setData({ isLoggedIn: currentLoginState });
      
      if (currentLoginState) {
        // 用户已登录，加载购物车数据
        this.loadCartData().then(() => {
          console.log('登录后加载购物车成功');
          // 加载推荐商品
          this.loadRecommendData();
        }).catch(err => {
          console.error('登录后加载购物车失败:', err);
          // 确保设置正确的状态
          this.setData({
            cartList: [],  // 修复：使用cartList
            cartItems: [],
            totalPrice: '0.00',  // 修复：使用字符串格式
            totalCount: 0,
            isEmpty: true,
            allSelected: false,  // 修复：添加allSelected字段
            loading: false  // 修复：使用loading而不是isLoading
          });
          wx.showToast({
            title: '加载失败，请下拉刷新',
            icon: 'none'
          });
        });
      } else {
        // 用户未登录，显示登录提示
        this.setData({
          cartList: [],
          cartItems: [],
          allSelected: false,
          selectedCount: 0,
          totalPrice: '0.00',
          loading: false,
          isLoading: false, // 确保加载指示器不显示
          isEmpty: false,   // 不显示空购物车提示
          isLoggedIn: false, // 确保登录状态正确
          showSkeleton: false
        });
      }
    } else if (currentLoginState) {
      // 用户已登录且状态未变，检查购物车是否需要刷新
      const now = Date.now();
      const lastRefreshTime = this.lastRefreshTime || 0;
      
      // 如果距离上次刷新超过30秒，自动刷新购物车
      if (now - lastRefreshTime > 30000) {
        console.log('购物车数据可能已过期，自动刷新');
        this.refreshCart();
        this.lastRefreshTime = now;
      }
    }
    
    // 更新购物车徽标
    cartManager.onPageShow();

    // 强制更新购物车徽标
    setTimeout(() => {
      cartManager._updateBadge();
    }, 500);
  },
  
  onHide() {
    // 清理所有定时器
    this.clearTimers();
  },
  
  onUnload() {
    // 清理所有定时器
    this.clearTimers();
    
    // 移除购物车监听器
    this.cleanupCartListener();
  },
  
  onPullDownRefresh() {
    this.refreshCart().finally(() => {
      wx.stopPullDownRefresh();
    });
  },
  
  // ==================== 初始化 ====================
  async initPage() {
    try {
      // 检查登录状态
      const loginState = isLoggedIn();
      this.setData({ isLoggedIn: loginState });
      
      if (!loginState) {
        // 未登录状态，显示登录提示
        this.setData({ 
          cartList: [],
          cartItems: [],
          isEmpty: false, // 不显示空购物车提示
          isLoggedIn: false, // 确保登录状态正确
          showSkeleton: false,
          loading: false,
          isLoading: false // 确保加载指示器不显示
        });
        
        // 即使未登录也加载推荐商品
        this.loadRecommendData();
        return;
      }
      
      // 先从缓存恢复数据，提升用户体验
      this.restoreFromCache();
      
      // 然后从服务器获取最新数据
      await this.loadCartData();
      
      // 加载推荐商品
      this.loadRecommendData();
      
      // 计算配送时间
      this.calculateDeliveryTime();
      
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '加载失败，请下拉刷新',
        icon: 'none'
      });
    } finally {
      // 无论如何都退出骨架屏状态
      this.setData({ 
        showSkeleton: false, 
        loading: false,
        isLoading: false // 确保加载指示器不显示
      });
    }
  },
  
  // 从缓存恢复数据
  restoreFromCache() {
    try {
      // 使用统一购物车管理器的缓存功能
      const cache = wx.getStorageSync('cartCache');
      if (cache) {
        const parsedCache = JSON.parse(cache);
        if (parsedCache && parsedCache.items && parsedCache.items.length > 0) {
          console.log('从缓存恢复购物车数据:', parsedCache.items.length);

          // 设置初始数据
          this.setData({
            cartList: JSON.parse(JSON.stringify(parsedCache.items))
          });

          // 计算总价
          this.calculateTotal();
        }
      }
    } catch (e) {
      console.error('从缓存恢复数据失败:', e);
    }
  },
  
  // 注意：缓存管理已由统一购物车管理器处理，无需重复实现
  
  // 确保定时器对象初始化
  ensureTimersInitialized() {
    try {
      if (!this._timers || typeof this._timers !== 'object') {
        this._timers = {};
        console.log('🔧 重新初始化 _timers 对象');
      }
      if (this._debugTimer === undefined) {
        this._debugTimer = null;
      }
      console.log('✅ 定时器对象检查完成', {
        timersType: typeof this._timers,
        timersKeys: Object.keys(this._timers || {}),
        debugTimerType: typeof this._debugTimer
      });
    } catch (error) {
      console.error('❌ 定时器初始化失败:', error);
      // 强制重新初始化
      this._timers = {};
      this._debugTimer = null;
    }
  },

  // 清理所有定时器
  clearTimers() {
    if (this._timers) {
      Object.values(this._timers).forEach(timer => {
        if (timer) clearTimeout(timer);
      });
      this._timers = {};
    }
    if (this._debugTimer) {
      clearTimeout(this._debugTimer);
      this._debugTimer = null;
    }
  },
  
  // ==================== 数据加载 ====================
  async loadCartData() {
    console.log('🛒 开始加载购物车数据');
    this.setData({ isLoading: true });
    
    // 检查登录状态，未登录时直接返回空数据
    if (!isLoggedIn()) {
      console.log('🛒 用户未登录，显示空购物车');
      this.setData({
        cartItems: [],
        totalPrice: 0,
        totalCount: 0,
        isEmpty: true,
        isLoading: false
      });
      return;
    }
    
    try {
      // 使用统一购物车管理器获取数据
      const result = await cartManager.getCartList();
      console.log('🛒 购物车数据获取结果:', result);
      
      if (result.success && result.data) {
        // 处理统一管理器返回的数据结构
        let items = [];
        if (Array.isArray(result.data)) {
          items = result.data;
        } else if (result.data.items && Array.isArray(result.data.items)) {
          items = result.data.items;
        }

        const cartItems = items.map(item => ({
          ...item,
          // 确保图片字段存在
          image: item.image || item.cover_url || this.data.defaultImage,
          // 确保选择状态字段存在
          selected: item.selected !== false  // 默认选中
        }));

        const totalPrice = result.data.total_price || 0;
        const totalCount = result.data.total_quantity || cartItems.reduce((sum, item) => sum + (parseInt(item.quantity) || 0), 0);
        const isEmpty = cartItems.length === 0;
        
        // 计算是否全选
        const isAllSelected = cartItems.length > 0 && 
          cartItems.every(item => item.selected !== false);
        
      this.setData({
          cartList: cartItems,  // 修复：使用cartList而不是cartItems
          cartItems,
          totalPrice,
          totalCount,
          isEmpty,
          allSelected: isAllSelected,  // 修复：使用allSelected而不是isAllSelected
          loading: false,  // 修复：使用loading而不是isLoading
          showSkeleton: false  // 修复：关闭骨架屏
        });
        
        console.log('🛒 购物车数据已更新:', {
          itemCount: cartItems.length,
          totalPrice,
          totalCount,
          isEmpty,
          isAllSelected,
          cartItems: cartItems.slice(0, 2)  // 只显示前2个商品的详细信息
        });

        // 调用计算总价方法
        this.calculateTotal();

        // 更新购物车徽标
        cartManager._updateBadge();

        // 同时更新应用级别的徽标
        const app = getApp();
        if (app && app.updateCartBadge) {
          setTimeout(() => app.updateCartBadge(), 100);
        }
      } else {
        this.setData({
          cartList: [],  // 修复：使用cartList
          cartItems: [],
          totalPrice: '0.00',  // 修复：使用字符串格式
          totalCount: 0,
          isEmpty: true,
          allSelected: false,  // 修复：添加allSelected字段
          loading: false,  // 修复：使用loading而不是isLoading
          showSkeleton: false  // 修复：关闭骨架屏
        });
        console.log('🛒 购物车为空或获取失败');
      }
    } catch (error) {
      console.error('🛒 加载购物车数据失败:', error);
        this.setData({
        cartList: [],  // 修复：使用cartList
        cartItems: [],
        totalPrice: '0.00',  // 修复：使用字符串格式
        totalCount: 0,
        isEmpty: true,
        allSelected: false,  // 修复：添加allSelected字段
        loading: false,  // 修复：使用loading而不是isLoading
        showSkeleton: false  // 修复：关闭骨架屏
      });
      
        wx.showToast({
        title: '加载购物车失败',
        icon: 'none'
      });
    }
  },
  
  async loadRecommendData() {
    try {
      // 获取热门商品
      const result = await api.getProductsByTag('hot', { per_page: 6 });
      
      if (result && result.data && Array.isArray(result.data)) {
        // 获取基础URL，用于处理相对路径图片
        const baseUrl = this._getApiBaseUrl();
        
        // 处理商品数据
        const recommendList = result.data.map(product => {
          // 处理图片URL
          let imageUrl = product.image_url || product.image || '';
          
          // 如果图片URL是相对路径，添加基础URL
          if (imageUrl && !this._isAbsoluteUrl(imageUrl)) {
            imageUrl = this._normalizeImageUrl(imageUrl, baseUrl);
          }
          
          return {
            id: product.id,
            name: product.name || '商品名称',
            price: parseFloat(product.price) || 0,
            originalPrice: product.sale_price ? parseFloat(product.sale_price) : null,
            image: imageUrl,
            unit: product.unit || '',
            supplier: product.supplier_name || product.supplier || '天心商城',
            // 保留原始数据
            ...product
          };
        });
        
        this.setData({ recommendList });
      }
    } catch (error) {
      console.error('加载推荐商品失败:', error);
      // 失败时使用空数组
      this.setData({ recommendList: [] });
    }
  },
  
  // 刷新购物车数据
  async refreshCart() {
    // 使用wx.showLoading替代Toast.loading
    wx.showLoading({
      title: '刷新中...',
      mask: true
    });
    
    this.setData({ isRefreshing: true });
    
    try {
      // 检查登录状态
      const loginState = isLoggedIn();
      
      if (!loginState) {
        this.setData({
          isLoggedIn: false,
          cartList: [],
          cartItems: [],
          allSelected: false,
          selectedCount: 0,
          totalPrice: '0.00',
          isRefreshing: false,
          isLoading: false,
          isEmpty: false // 不显示空购物车提示，而是显示登录提示
        });
        
        wx.hideLoading();
        wx.showToast({
          title: '已刷新',
          icon: 'success'
        });
        return { success: true, message: '已退出登录状态' };
      }
      
      // 已登录状态，加载数据
      try {
        await this.loadCartData();
      } catch (loadError) {
        console.error('购物车数据加载失败:', loadError);
        // 确保设置正确的状态
      this.setData({ 
          cartItems: [],
          totalPrice: 0,
          totalCount: 0,
          isEmpty: true,
          isLoading: false
        });
      }
      
      // 加载推荐商品
      this.loadRecommendData();
      
      this.setData({ isRefreshing: false });
      
      wx.hideLoading();
      wx.showToast({
        title: '已刷新',
        icon: 'success'
      });
      return { success: true, message: '购物车已刷新' };
    } catch (error) {
      console.error('刷新购物车失败:', error);
      
      this.setData({ isRefreshing: false });
      
      wx.hideLoading();
      wx.showToast({
        title: '刷新失败',
        icon: 'error'
      });
      
      return { success: false, message: error.message || '刷新购物车失败' };
    }
  },
  
  // ==================== 计算属性 ====================
  calculateTotal() {
    try {
      // 确保cartList是有效数组
      if (!this.data.cartList || !Array.isArray(this.data.cartList)) {
        this.setData({
          allSelected: false,
          selectedCount: 0,
          totalPrice: '0.00'
        });
      return;
    }
    
      // 过滤无效的商品项
      const validItems = this.data.cartList.filter(item => 
        item && typeof item === 'object' && item.id && item.quantity > 0);
      
      // 计算选中的商品
      const selectedItems = validItems.filter(item => item.selected);
      
      // 计算总数量
      const selectedCount = selectedItems.reduce((sum, item) => {
        const quantity = parseInt(item.quantity) || 0;
        return sum + quantity;
      }, 0);
      
      // 计算总价格
      const totalPrice = selectedItems.reduce((sum, item) => {
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 0;
        return sum + (price * quantity);
      }, 0);
      
      // 检查是否全选
      const allSelected = validItems.length > 0 && 
        validItems.length === selectedItems.length;
      
      // 更新状态
      this.setData({
        allSelected,
        selectedCount,
        totalPrice: totalPrice.toFixed(2)
      });
    } catch (error) {
      console.error('计算总价出错:', error);
      
      // 出错时设置默认值
      this.setData({
        allSelected: false,
        selectedCount: 0,
        totalPrice: '0.00'
      });
    }
  },
  
  // 计算配送时间
  calculateDeliveryTime() {
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0=星期天, 1=星期一, ..., 6=星期六
    
    let deliveryText = '';
    let deliveryDate = '';
    
    if (dayOfWeek === 0) {
      // 星期天显示后日达
      deliveryText = '后日达';
      // 计算后天日期
      const afterTomorrow = new Date(now);
      afterTomorrow.setDate(now.getDate() + 2);
      deliveryDate = this.formatDate(afterTomorrow);
    } else {
      // 星期一到星期六显示明日达
      deliveryText = '明日达';
      // 计算明天日期
      const tomorrow = new Date(now);
      tomorrow.setDate(now.getDate() + 1);
      deliveryDate = this.formatDate(tomorrow);
    }
        
        this.setData({
      deliveryText,
      deliveryDate
    });
  },
  
  // 格式化日期
  formatDate(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekDay = weekDays[date.getDay()];
    
    return `${month}月${day}日 ${weekDay}`;
  },
  
  // ==================== 事件处理 ====================
  
  // 切换编辑模式
  toggleEditMode() {
        this.setData({
      isEditMode: !this.data.isEditMode
    });
  },
  
  // 手动刷新
  handleManualRefresh() {
    // 确保定时器对象初始化
    this.ensureTimersInitialized();

    // 长按3秒触发调试
    if (this._debugTimer) {
      clearTimeout(this._debugTimer);
    }

    this._debugTimer = setTimeout(() => {
      this.debugCart();
    }, 3000);
    
    // 普通点击刷新购物车
    this.refreshCart();
  },
  
  // 商品选择
  async onItemSelect(e) {
    const { index } = e.currentTarget.dataset;
    const item = this.data.cartList[index];
    
    if (!item) return;
    
    const newSelected = !item.selected;
    
    try {
      // 乐观更新UI
      this.setData({
        [`cartList[${index}].selected`]: newSelected
      });
      
      // 计算总价
      this.calculateTotal();
      
      // 调用API - 注意：统一管理器中没有updateSelection方法，这里只做本地更新
      // await cartManager.updateSelection(item.id, newSelected);
      console.log('商品选择状态已更新（仅本地）:', item.id, newSelected);
    } catch (error) {
      console.error('更新选择状态失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
      
      // 恢复原状态
      this.setData({
        [`cartList[${index}].selected`]: !newSelected
      });
      
      this.calculateTotal();
    }
  },
  
  // 全选/取消全选
  async onSelectAll() {
    const newAllSelected = !this.data.allSelected;
    
    try {
      // 乐观更新UI
      const updatedList = this.data.cartList.map(item => ({
        ...item,
        selected: newAllSelected
      }));
      
      this.setData({
        cartList: updatedList,
        allSelected: newAllSelected
      });
      
      // 计算总价
      this.calculateTotal();
      
      // 逐个更新商品选中状态（仅本地更新）
      console.log('全选状态已更新（仅本地）:', newAllSelected);
    } catch (error) {
      console.error('更新全选状态失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
      
      // 恢复原状态
      this.loadCartData();
    }
  },
  
  // 数量减少
  onQuantityDecrease(e) {
    // 检查登录状态
    if (!isLoggedIn()) {
      console.log('❌ 用户未登录，无法减少数量');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    console.log('🔽 点击减少按钮', e.currentTarget.dataset);
    const { index } = e.currentTarget.dataset;
    const item = this.data.cartList[index];

    console.log('🔽 商品信息', { index, item });

    if (!item) {
      console.error('❌ 商品不存在', { index, cartListLength: this.data.cartList.length });
      return;
    }

    const currentQty = parseInt(item.quantity) || 1;
    if (currentQty <= 1) {
      console.log('🔽 数量已达最小值，不能再减少');
      return;
    }

    const newQty = currentQty - 1;
    console.log('🔽 数量变化', { currentQty, newQty });

    // 尝试使用主要方法，如果失败则使用简化方法
    try {
      this.updateItemQuantity(index, item.id, newQty);
    } catch (error) {
      console.warn('主要更新方法失败，使用简化方法:', error);
      this.simpleUpdateQuantity(index, item.id, newQty);
    }
  },
  
  // 数量增加
  onQuantityIncrease(e) {
    // 检查登录状态
    if (!isLoggedIn()) {
      console.log('❌ 用户未登录，无法增加数量');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    console.log('🔼 点击增加按钮', e.currentTarget.dataset);
    const { index } = e.currentTarget.dataset;
    const item = this.data.cartList[index];

    console.log('🔼 商品信息', { index, item });

    if (!item) {
      console.error('❌ 商品不存在', { index, cartListLength: this.data.cartList.length });
      return;
    }

    const currentQty = parseInt(item.quantity) || 1;
    const newQty = currentQty + 1;
    console.log('🔼 数量变化', { currentQty, newQty });

    // 尝试使用主要方法，如果失败则使用简化方法
    try {
      this.updateItemQuantity(index, item.id, newQty);
    } catch (error) {
      console.warn('主要更新方法失败，使用简化方法:', error);
      this.simpleUpdateQuantity(index, item.id, newQty);
    }
  },
  
  // 数量变化
  onQuantityChange(e) {
    // 检查登录状态
    if (!isLoggedIn()) {
      console.log('❌ 用户未登录，无法修改数量');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const { index } = e.currentTarget.dataset;
    const item = this.data.cartList[index];

    if (!item) return;
    
    let newQty = parseInt(e.detail.value);
    
    // 确保数量有效
    if (isNaN(newQty) || newQty < 1) {
      newQty = 1;
    }

    // 尝试使用主要方法，如果失败则使用简化方法
    try {
      this.updateItemQuantity(index, item.id, newQty);
    } catch (error) {
      console.warn('主要更新方法失败，使用简化方法:', error);
      this.simpleUpdateQuantity(index, item.id, newQty);
    }
  },

  // 数量输入处理
  onQuantityInput(e) {
    // 检查登录状态
    if (!isLoggedIn()) {
      console.log('❌ 用户未登录，无法修改数量');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const { index } = e.currentTarget.dataset;
    const inputValue = e.detail.value;
    const item = this.data.cartList[index];

    console.log('📝 数量输入', { index, inputValue, item });

    if (!item) {
      console.error('❌ 商品不存在', { index });
      return;
    }

    // 验证输入值
    let newQty = parseInt(inputValue) || 1;
    if (newQty < 1) newQty = 1;
    if (newQty > 999) newQty = 999;

    console.log('📝 验证后的数量', { originalInput: inputValue, newQty });

    // 如果数量没有变化，不需要更新
    if (newQty === item.quantity) {
      console.log('📝 数量无变化，跳过更新');
      return;
    }

    // 尝试使用主要方法，如果失败则使用简化方法
    try {
      this.updateItemQuantity(index, item.id, newQty);
    } catch (error) {
      console.warn('主要更新方法失败，使用简化方法:', error);
      this.simpleUpdateQuantity(index, item.id, newQty);
    }
  },

  // 数量输入框获得焦点
  onQuantityFocus(e) {
    const { index } = e.currentTarget.dataset;
    console.log('🎯 数量输入框获得焦点', { index });
  },

  // 更新商品数量
  async updateItemQuantity(index, itemId, quantity) {
    console.log('🚀 updateItemQuantity 开始执行', {
      index,
      itemId,
      quantity,
      timersExists: !!this._timers,
      thisExists: !!this
    });

    try {
      // 检查登录状态
      if (!isLoggedIn()) {
        console.log('❌ 用户未登录，无法更新购物车数量');
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      // 确保所有定时器对象都已初始化
      this.ensureTimersInitialized();

      console.log('✅ 初始化检查完成', {
        timersExists: !!this._timers,
        timersType: typeof this._timers
      });
    } catch (initError) {
      console.error('❌ 初始化检查失败:', initError);
      wx.showToast({
        title: '系统错误',
        icon: 'error'
      });
      return;
    }

    console.log('📝 开始更新数量', {
      index,
      itemId,
      quantity,
      originalQuantity: this.data.cartList[index]?.quantity,
      timersExists: !!this._timers
    });

    // 获取原始数量，用于恢复
    const originalQuantity = this.data.cartList[index].quantity;

    try {
      // 乐观更新UI
      console.log('🎨 乐观更新UI');
      this.setData({
        [`cartList[${index}].quantity`]: quantity,
        [`cartList[${index}].updating`]: true
      });

      // 计算总价
      this.calculateTotal();

      // 延迟更新，避免频繁API调用
      console.log('⏰ 设置定时器', {
        existingTimer: !!this._timers[`update_${itemId}`],
        timersObject: this._timers
      });

      // 保存当前上下文
      const self = this;
      const timerKey = `update_${itemId}`;

      if (this._timers[timerKey]) {
        clearTimeout(this._timers[timerKey]);
      }

      this._timers[timerKey] = setTimeout(async () => {
        try {
          // 使用保存的上下文，确保 _timers 对象存在
          if (!self._timers) {
            self._timers = {};
            console.log('🔧 setTimeout回调中重新初始化 _timers');
          }

          console.log('🔄 开始API调用更新数量', { itemId, quantity });

          // 调用API更新数量
          await cartManager.updateQuantity(itemId, quantity);

          // 更新成功，清除更新状态
          self.setData({
            [`cartList[${index}].updating`]: false
          });

          // 更新购物车徽标
          cartManager._updateBadge();

          // 同时更新应用级别的徽标
          const app = getApp();
          if (app && app.updateCartBadge) {
            setTimeout(() => app.updateCartBadge(), 100);
          }

          console.log('✅ API调用成功，清理定时器');
        } catch (error) {
          console.error('更新数量失败:', error);
          wx.showToast({
            title: '更新失败',
            icon: 'error'
          });

          // 恢复原数量
          self.setData({
            [`cartList[${index}].quantity`]: originalQuantity,
            [`cartList[${index}].updating`]: false
          });

          // 重新计算总价
          self.calculateTotal();
        } finally {
          // 清理定时器引用（无论成功还是失败）
          if (self._timers && self._timers[timerKey]) {
            delete self._timers[timerKey];
            console.log('🧹 清理定时器引用:', timerKey);
          }
        }
      }, 500);
    } catch (error) {
      console.error('更新数量失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
      
      // 恢复原数量
      this.setData({
        [`cartList[${index}].quantity`]: originalQuantity,
        [`cartList[${index}].updating`]: false
      });
      
      // 重新计算总价
      this.calculateTotal();
    }
  },

  // 简化的数量更新方法（备选方案）
  async simpleUpdateQuantity(index, itemId, quantity) {
    console.log('🔄 使用简化更新方法', { index, itemId, quantity });

    try {
      // 检查登录状态
      if (!isLoggedIn()) {
        wx.showToast({ title: '请先登录', icon: 'none' });
        return;
      }

      // 直接调用API，不使用定时器
      await cartManager.updateQuantity(itemId, quantity);

      // 重新加载购物车数据
      await this.loadCartData();

      wx.showToast({
        title: '更新成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('简化更新失败:', error);
      wx.showToast({
        title: '更新失败',
        icon: 'error'
      });
    }
  },

  // 删除单个商品
  async onDeleteItem(e) {
    const { index } = e.currentTarget.dataset;
    const item = this.data.cartList[index];
    
    if (!item) return;
    
    try {
      // 从列表中移除
      const newCartList = [...this.data.cartList];
      newCartList.splice(index, 1);
      
      // 乐观更新UI
      this.setData({
        cartList: newCartList
      });
      
      // 计算总价
      this.calculateTotal();
      
      // 调用API删除
      await cartManager.removeItem(item.id);
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('删除商品失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
      
      // 恢复原状态
      this.loadCartData();
    }
  },
  
  // 删除选中的商品
  async deleteSelectedItems() {
    const selectedItems = this.data.cartList.filter(item => item.selected);
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请先选择要删除的商品',
        icon: 'none'
      });
      return;
    }
    
    try {
      // 从列表中移除所有选中的商品
      const newCartList = this.data.cartList.filter(item => !item.selected);
      
      // 乐观更新UI
      this.setData({
        cartList: newCartList
      });
      
      // 计算总价
      this.calculateTotal();
      
      // 调用API批量删除
      const promises = selectedItems.map(item => cartManager.removeItem(item.id));
      await Promise.all(promises);
      
      wx.showToast({
        title: `成功删除${selectedItems.length}件商品`,
        icon: 'success'
      });
    } catch (error) {
      console.error('批量删除失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
      
      // 恢复原状态
      this.loadCartData();
    }
  },
  
  // 前往商品详情
  goToProductDetail(e) {
    const { id } = e.currentTarget.dataset;
    if (!id) return;
    
    wx.navigateTo({
      url: `/pages/product-detail/index?id=${id}`
    });
  },

  // 前往结算页面
  goToCheckout() {
    // 获取选中的商品
    const selectedItems = this.data.cartList.filter(item => item.selected);
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      });
      return;
    }
    
    try {
      // 构建结算参数
      const cartIds = selectedItems.map(item => item.id);
      const queryString = encodeURIComponent(JSON.stringify(cartIds));
      
      // 跳转到结算页面
      wx.navigateTo({
        url: `/pages/checkout/checkout?cart_ids=${queryString}`
      });
    } catch (error) {
      console.error('跳转结算页面失败:', error);
      wx.showToast({
        title: '结算失败，请重试',
        icon: 'error'
      });
    }
  },
  
  // 前往首页
  goToHome() {
    wx.switchTab({ url: '/pages/home/<USER>' });
  },
  
  // 前往登录页
  goToLogin() {
    wx.navigateTo({ url: '/pages/login/index' });
  },
  
  // ==================== 推荐商品 ====================
  
  // 点击推荐商品
  onProductTap(e) {
    const { product } = e.currentTarget.dataset;
    if (!product || !product.id) return;
    
    wx.navigateTo({
      url: `/pages/product-detail/index?id=${product.id}`
    });
  },
  
  // 添加推荐商品到购物车
  async onAddToCart(e) {
    const { product, quantity = 1 } = e.detail;
    
    if (!product || !product.id) {
      console.error('商品信息无效:', product);
      return;
    }
    
    wx.showLoading({
      title: '添加中...',
      mask: true
    });
    
    try {
      // 调用添加到购物车API
      const result = await cartManager.addToCart({ product_id: product.id, quantity });
      
      wx.hideLoading();
      if (result) {
        // 不显示加购提示，让用户直接看到数量变化
        console.log('✅ 购物车页推荐商品添加成功');
      } else {
        wx.showToast({
          title: '添加失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('添加到购物车失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '添加失败',
        icon: 'error'
      });
    }
  },
  
  // 图片加载失败
  onImageError(e) {
    console.log('🖼️ 图片加载失败:', e.detail);
    const index = e.currentTarget.dataset.index;
    const defaultImage = this.data.defaultImage;
    
    // 更新指定索引的商品图片为默认图片
    const key = `cartItems[${index}].image`;
        this.setData({
      [key]: defaultImage
    });
    
    console.log(`🖼️ 商品 ${index} 图片已替换为默认图片`);
  },
  
  // 推荐商品图片加载失败
  onRecommendImageError(e) {
    const { index } = e.currentTarget.dataset;
    console.log('推荐商品图片加载失败:', index);
    
    // 设置默认图片
              this.setData({
      [`recommendList[${index}].image`]: '/images/default-product.png'
    });
  },
  
  // ==================== 购物车监听器 ====================
  
  /**
   * 初始化购物车监听器
   */
  initCartListener() {
    // 创建监听器回调函数
    this.cartChangeListener = (event) => {
      console.log('购物车变化事件:', event);
      
      // 根据事件类型处理不同的变化
      switch (event.action) {
        case CartEvents.ADD:
          // 商品添加事件，刷新购物车
          this.handleCartAdd(event.data);
          break;
          
        case CartEvents.REMOVE:
          // 商品移除事件，可能是其他页面删除了商品
          this.handleCartRemove(event.data);
          break;
          
        case CartEvents.UPDATE:
          // 商品数量更新事件
          this.handleCartUpdate(event.data);
          break;
          
        case CartEvents.SYNC:
          // 购物车数据同步事件
          this.handleCartSync(event.data);
          break;
          
        case CartEvents.COUNT:
          // 购物车数量变化事件
          // 不需要处理，因为购物车页面自己会计算总数
          break;
      }
    };
    
    // 添加监听器，设置只关心特定事件类型
    addListener('cartPage', this.cartChangeListener, {
      events: [CartEvents.ADD, CartEvents.REMOVE, CartEvents.UPDATE, CartEvents.SYNC],
      immediate: false // 不需要立即触发
    });
  },
  
  /**
   * 清理购物车监听器
   */
  cleanupCartListener() {
    if (this.cartChangeListener) {
      removeListener('cartPage');
      this.cartChangeListener = null;
    }
  },

  /**
   * 处理商品添加事件
   */
  handleCartAdd(data) {
    // 如果页面已经加载了数据，刷新购物车
    if (!this.data.loading) {
      this.refreshCart();
    }
  },
  
  /**
   * 处理商品移除事件
   */
  handleCartRemove(data) {
    // 如果是从其他页面删除的，需要更新当前页面
    if (data.productId && !this.data.loading) {
      // 查找要删除的商品
      const index = this.data.cartList.findIndex(item => 
        item.product_id === data.productId || item.id === data.productId);
      
      if (index !== -1) {
        // 从购物车列表中移除
        const newCartList = [...this.data.cartList];
        newCartList.splice(index, 1);
        
        this.setData({ cartList: newCartList }, () => {
          // 重新计算总价
          this.calculateTotal();
        });
      }
    }
  },
  
  /**
   * 处理商品数量更新事件
   */
  handleCartUpdate(data) {
    // 如果是从其他页面更新的，需要更新当前页面
    if (data.productId && data.quantity && !this.data.loading) {
      // 查找要更新的商品
      const index = this.data.cartList.findIndex(item => 
        item.product_id === data.productId || item.id === data.productId);
      
      if (index !== -1) {
        // 更新商品数量
        const newCartList = [...this.data.cartList];
        newCartList[index].quantity = data.quantity;
        
        this.setData({ cartList: newCartList }, () => {
          // 重新计算总价
          this.calculateTotal();
        });
      }
    }
  },
  
  /**
   * 处理购物车数据同步事件
   */
  handleCartSync(data) {
    // 如果是从API获取的新数据，而且页面不在加载中，刷新购物车
    if (data.source === 'api' && !this.data.loading && !this.data.isRefreshing) {
        this.refreshCart();
    }
  },
  
  // ==================== 调试功能 ====================
  
  // 修改debugCart方法，添加图片URL检查
  debugCart() {
    console.log('===== 购物车调试信息 =====');

    // 0. 检查页面数据状态
    console.log('页面数据状态:', {
      cartList长度: this.data.cartList ? this.data.cartList.length : 'undefined',
      loading: this.data.loading,
      isEmpty: this.data.isEmpty,
      isLoggedIn: this.data.isLoggedIn,
      allSelected: this.data.allSelected,
      selectedCount: this.data.selectedCount,
      totalPrice: this.data.totalPrice
    });

    // 1. 检查登录状态
    const loginState = isLoggedIn();
    console.log('当前登录状态:', loginState);

    // 2. 检查token
    const token = wx.getStorageSync('token');
    console.log('Token状态:', token ? '已存在' : '不存在');

    // 3. 检查购物车数据
    console.log('当前购物车数据:', this.data.cartList);
    
    // 4. 检查缓存
    try {
      const cartCache = wx.getStorageSync('cartCache');
      const parsedCache = cartCache ? JSON.parse(cartCache) : null;
      console.log('购物车缓存:', parsedCache);
              } catch (e) {
      console.error('读取购物车缓存失败:', e);
    }
    
    // 5. 检查全局缓存
    try {
      const globalCache = wx.getStorageSync('cartItemsCache');
      const parsedGlobalCache = globalCache ? JSON.parse(globalCache) : null;
      console.log('全局购物车缓存:', parsedGlobalCache);
        } catch (e) {
      console.error('读取全局购物车缓存失败:', e);
    }
    
    // 6. 检查图片URL
    console.log('===== 图片URL检查 =====');
    if (this.data.cartList && this.data.cartList.length > 0) {
      this.data.cartList.forEach((item, index) => {
        console.log(`商品 ${index + 1}: ${item.name}`);
        console.log(`- 图片URL: ${item.image}`);
        console.log(`- 是否绝对路径: ${this._isAbsoluteUrl(item.image)}`);
        
        // 尝试修复图片URL
        if (item.image && !item.image.startsWith('/images/')) {
          // 获取基础URL
          const baseUrl = this._getApiBaseUrl();
          
          // 尝试不同的处理方式
          const variants = [
            item.image,
            this._normalizeImageUrl(item.image, baseUrl),
            item.image_url,
            item.product_image,
            baseUrl + (item.image.startsWith('/') ? item.image : '/' + item.image)
          ].filter(Boolean);
          
          console.log('- 可能的图片URL变体:');
          variants.forEach((url, i) => {
            console.log(`  ${i + 1}. ${url}`);
          });
        }
      });
      
      // 显示图片调试对话框
      this._showImageDebugDialog();
      } else {
      console.log('购物车为空，无法检查图片URL');
    }
    
    // 7. 尝试重新获取购物车数据
    if (loginState) {
      console.log('尝试重新获取购物车数据...');
      
      // 显示加载提示
      wx.showLoading({
        title: '诊断中...',
        mask: true
      });
      
      // 调用API直接获取购物车数据
      api.getCartList().then(result => {
        console.log('API直接获取购物车结果:', result);
        
        wx.hideLoading();
        wx.showModal({
          title: '购物车诊断结果',
          content: `API返回${result.data ? result.data.length || 0 : 0}个商品，本地显示${this.data.cartList.length}个商品`,
          showCancel: false,
          success: () => {
            // 刷新购物车
        this.refreshCart();
          }
        });
      }).catch(error => {
        console.error('API直接获取购物车失败:', error);
        
        wx.hideLoading();
        wx.showModal({
          title: '购物车诊断失败',
          content: `错误信息: ${error.message || '未知错误'}`,
          showCancel: false
        });
      });
    } else {
      wx.showModal({
        title: '购物车诊断结果',
        content: '您当前未登录，请先登录后再查看购物车',
        showCancel: false
      });
    }
  },
  
  // 添加图片调试对话框
  _showImageDebugDialog() {
    // 获取第一个图片有问题的商品
    const problemItem = this.data.cartList.find(item => 
      item.image && !item.image.startsWith('/images/') && !this._isImageLoadingSuccessfully(item.image)
    );
    
    if (problemItem) {
      wx.showModal({
        title: '图片URL诊断',
        content: `发现可能有问题的图片URL:\n${problemItem.image}\n\n是否尝试修复所有商品图片?`,
        confirmText: '修复',
        success: (res) => {
          if (res.confirm) {
            this._fixAllProductImages();
          }
        }
      });
    }
  },
  
  // 检查图片是否能成功加载
  _isImageLoadingSuccessfully(imageUrl) {
    // 本地图片路径总是可以加载的
    if (imageUrl.startsWith('/')) return true;
    
    // 检查是否是有效的URL格式
    return this._isAbsoluteUrl(imageUrl);
  },
  
  // 修复所有商品图片
  _fixAllProductImages() {
    wx.showLoading({
      title: '修复图片中...',
      mask: true
    });
    
    try {
      const baseUrl = this._getApiBaseUrl();
      const updatedList = this.data.cartList.map(item => {
        // 处理图片URL
        let imageUrl = item.image;
        
        // 如果不是默认图片且不是绝对路径，尝试修复
        if (imageUrl && !imageUrl.startsWith('/images/') && !this._isAbsoluteUrl(imageUrl)) {
          imageUrl = this._normalizeImageUrl(imageUrl, baseUrl);
        }
        
        return {
          ...item,
          image: imageUrl
        };
      });
      
      this.setData({ cartList: updatedList }, () => {
        wx.hideLoading();
              wx.showToast({
          title: '图片已修复',
                icon: 'success'
              });
      });
    } catch (error) {
      console.error('修复图片失败:', error);
        wx.hideLoading();
        wx.showToast({
          title: '修复失败',
        icon: 'error'
      });
    }
  },
  
  // 添加URL处理辅助方法
  _getApiBaseUrl() {
    try {
      // 尝试从request-config.js获取当前环境配置
      const { getCurrentConfig } = require('../../utils/request-config');
      const config = getCurrentConfig();
      return config.baseUrl.replace('/api', '') || 'https://n8n.cdtxsp.com';
    } catch (error) {
      console.error('获取API基础URL失败:', error);
      // 默认返回生产环境URL
      return 'https://n8n.cdtxsp.com';
    }
  },
  
  _isAbsoluteUrl(url) {
    if (!url) return false;
    return url.startsWith('http://') || url.startsWith('https://') || url.startsWith('//');
  },
  
  _normalizeImageUrl(url, baseUrl) {
    if (!url) return '/images/default-product.png';
    
    // 如果已经是绝对URL，直接返回
    if (this._isAbsoluteUrl(url)) return url;
    
    // 如果是以/开头的路径，直接拼接基础URL
    if (url.startsWith('/')) {
      return `${baseUrl}${url}`;
    }
    
    // 否则添加/前缀再拼接
    return `${baseUrl}/${url}`;
  },
  
  // 添加尝试修复图片URL的方法
  _tryFixImageUrl(index) {
    try {
      const item = this.data.cartList[index];
      if (!item) return;
      
      // 获取基础URL
      const baseUrl = this._getApiBaseUrl();
      
      // 尝试不同的图片字段
      const possibleImageFields = ['image_url', 'product_image', 'thumbnail', 'cover'];
      
      for (const field of possibleImageFields) {
        if (item[field] && item[field] !== item.image) {
          let newImageUrl = item[field];
          
          // 处理相对路径
          if (!this._isAbsoluteUrl(newImageUrl)) {
            newImageUrl = this._normalizeImageUrl(newImageUrl, baseUrl);
          }
          
          console.log('尝试使用替代图片:', newImageUrl);
          
          // 更新图片URL
      this.setData({
            [`cartList[${index}].image`]: newImageUrl
          });
          
          break;
        }
      }
    } catch (error) {
      console.error('修复图片URL失败:', error);
    }
  },
}); 