<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 优化区域价格表索引
        Schema::table('region_prices', function (Blueprint $table) {
            // 检查索引是否已存在
            if (!$this->indexExists('region_prices', 'idx_region_prices_lookup')) {
                $table->index(['product_id', 'region_id', 'status', 'start_date', 'end_date'], 'idx_region_prices_lookup');
            }
            
            if (!$this->indexExists('region_prices', 'idx_region_prices_status')) {
                $table->index(['status'], 'idx_region_prices_status');
            }
            
            if (!$this->indexExists('region_prices', 'idx_region_prices_time_range')) {
                $table->index(['start_date', 'end_date'], 'idx_region_prices_time_range');
            }
        });

        // 优化商品会员折扣表索引
        if (Schema::hasTable('product_member_discounts')) {
            Schema::table('product_member_discounts', function (Blueprint $table) {
                if (!$this->indexExists('product_member_discounts', 'idx_product_member_discounts_lookup')) {
                    $table->index(['product_id', 'membership_level_id', 'status', 'start_time', 'end_time'], 'idx_product_member_discounts_lookup');
                }
                
                if (!$this->indexExists('product_member_discounts', 'idx_product_member_discounts_level')) {
                    $table->index(['membership_level_id', 'status'], 'idx_product_member_discounts_level');
                }
            });
        }

        // 优化分类区域价格表索引
        if (Schema::hasTable('category_region_prices')) {
            Schema::table('category_region_prices', function (Blueprint $table) {
                if (!$this->indexExists('category_region_prices', 'idx_category_region_prices_lookup')) {
                    $table->index(['category_id', 'region_id', 'status', 'start_time', 'end_time'], 'idx_category_region_prices_lookup');
                }
                
                if (!$this->indexExists('category_region_prices', 'idx_category_region_prices_region')) {
                    $table->index(['region_id', 'status'], 'idx_category_region_prices_region');
                }
            });
        }

        // 优化分类会员折扣表索引
        if (Schema::hasTable('category_member_discounts')) {
            Schema::table('category_member_discounts', function (Blueprint $table) {
                if (!$this->indexExists('category_member_discounts', 'idx_category_member_discounts_lookup')) {
                    $table->index(['category_id', 'membership_level_id', 'status', 'start_time', 'end_time'], 'idx_category_member_discounts_lookup');
                }
                
                if (!$this->indexExists('category_member_discounts', 'idx_category_member_discounts_level')) {
                    $table->index(['membership_level_id', 'status'], 'idx_category_member_discounts_level');
                }
            });
        }

        // 优化商品表索引
        Schema::table('products', function (Blueprint $table) {
            if (!$this->indexExists('products', 'idx_products_status_category')) {
                $table->index(['status', 'category_id'], 'idx_products_status_category');
            }
            
            if (!$this->indexExists('products', 'idx_products_status_created')) {
                $table->index(['status', 'created_at'], 'idx_products_status_created');
            }
            
            if (!$this->indexExists('products', 'idx_products_status_sales')) {
                $table->index(['status', 'sales_count'], 'idx_products_status_sales');
            }
        });

        // 优化购物车表索引
        if (Schema::hasTable('cart_items')) {
            Schema::table('cart_items', function (Blueprint $table) {
                if (!$this->indexExists('cart_items', 'idx_cart_items_cart_product')) {
                    $table->index(['cart_id', 'product_id', 'sku_id'], 'idx_cart_items_cart_product');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 删除区域价格表索引
        Schema::table('region_prices', function (Blueprint $table) {
            if ($this->indexExists('region_prices', 'idx_region_prices_lookup')) {
                $table->dropIndex('idx_region_prices_lookup');
            }
            if ($this->indexExists('region_prices', 'idx_region_prices_status')) {
                $table->dropIndex('idx_region_prices_status');
            }
            if ($this->indexExists('region_prices', 'idx_region_prices_time_range')) {
                $table->dropIndex('idx_region_prices_time_range');
            }
        });

        // 删除商品会员折扣表索引
        if (Schema::hasTable('product_member_discounts')) {
            Schema::table('product_member_discounts', function (Blueprint $table) {
                if ($this->indexExists('product_member_discounts', 'idx_product_member_discounts_lookup')) {
                    $table->dropIndex('idx_product_member_discounts_lookup');
                }
                if ($this->indexExists('product_member_discounts', 'idx_product_member_discounts_level')) {
                    $table->dropIndex('idx_product_member_discounts_level');
                }
            });
        }

        // 删除分类区域价格表索引
        if (Schema::hasTable('category_region_prices')) {
            Schema::table('category_region_prices', function (Blueprint $table) {
                if ($this->indexExists('category_region_prices', 'idx_category_region_prices_lookup')) {
                    $table->dropIndex('idx_category_region_prices_lookup');
                }
                if ($this->indexExists('category_region_prices', 'idx_category_region_prices_region')) {
                    $table->dropIndex('idx_category_region_prices_region');
                }
            });
        }

        // 删除分类会员折扣表索引
        if (Schema::hasTable('category_member_discounts')) {
            Schema::table('category_member_discounts', function (Blueprint $table) {
                if ($this->indexExists('category_member_discounts', 'idx_category_member_discounts_lookup')) {
                    $table->dropIndex('idx_category_member_discounts_lookup');
                }
                if ($this->indexExists('category_member_discounts', 'idx_category_member_discounts_level')) {
                    $table->dropIndex('idx_category_member_discounts_level');
                }
            });
        }

        // 删除商品表索引
        Schema::table('products', function (Blueprint $table) {
            if ($this->indexExists('products', 'idx_products_status_category')) {
                $table->dropIndex('idx_products_status_category');
            }
            if ($this->indexExists('products', 'idx_products_status_created')) {
                $table->dropIndex('idx_products_status_created');
            }
            if ($this->indexExists('products', 'idx_products_status_sales')) {
                $table->dropIndex('idx_products_status_sales');
            }
        });

        // 删除购物车表索引
        if (Schema::hasTable('cart_items')) {
            Schema::table('cart_items', function (Blueprint $table) {
                if ($this->indexExists('cart_items', 'idx_cart_items_cart_product')) {
                    $table->dropIndex('idx_cart_items_cart_product');
                }
            });
        }
    }
    
    /**
     * 检查索引是否存在
     */
    private function indexExists(string $table, string $indexName): bool
    {
        try {
            $indexes = DB::select("SHOW INDEX FROM `{$table}` WHERE Key_name = ?", [$indexName]);
            return !empty($indexes);
        } catch (\Exception $e) {
            // 如果表不存在或其他错误，返回 false
            return false;
        }
    }
}; 