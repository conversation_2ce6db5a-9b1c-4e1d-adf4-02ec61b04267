<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类数据测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .category { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .level-1 { background: #f0f8ff; }
        .level-2 { background: #f5f5f5; margin-left: 20px; }
        .level-3 { background: #fff2e8; margin-left: 40px; }
        .category-name { font-weight: bold; }
        .category-id { color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <h1>分类数据结构测试</h1>
    <div id="categories"></div>

    <script>
        // 模拟从API获取的分类数据
        const categoryData = {
            "code": 200,
            "message": "Success",
            "data": [
                {
                    "id": 1,
                    "name": "猪生鲜",
                    "children_data": [
                        {
                            "id": 4,
                            "name": "鲜货",
                            "children_data": [
                                {
                                    "id": 108,
                                    "name": "123",
                                    "children_data": []
                                }
                            ]
                        },
                        {
                            "id": 5,
                            "name": "半熟",
                            "children_data": []
                        },
                        {
                            "id": 21,
                            "name": "冻品",
                            "children_data": []
                        }
                    ]
                },
                {
                    "id": 77,
                    "name": "鸡类",
                    "children_data": [
                        {
                            "id": 106,
                            "name": "鲜鸡",
                            "children_data": []
                        },
                        {
                            "id": 79,
                            "name": "鸡爪类",
                            "children_data": []
                        }
                    ]
                }
            ]
        };

        function renderCategories(categories, level = 1) {
            let html = '';
            categories.forEach(category => {
                const levelClass = `level-${level}`;
                html += `
                    <div class="category ${levelClass}">
                        <div class="category-name">${category.name}</div>
                        <div class="category-id">ID: ${category.id}</div>
                        ${category.children_data && category.children_data.length > 0 ? 
                            renderCategories(category.children_data, level + 1) : ''}
                    </div>
                `;
            });
            return html;
        }

        // 渲染分类数据
        document.getElementById('categories').innerHTML = renderCategories(categoryData.data);

        // 测试三级分类提取逻辑
        function testThirdCategoryExtraction() {
            console.log('🧪 测试三级分类提取逻辑');
            
            const categories = categoryData.data;
            
            // 模拟选择"猪生鲜" > "鲜货"
            const mainCategory = categories.find(c => c.id === 1); // 猪生鲜
            const subCategory = mainCategory.children_data.find(c => c.id === 4); // 鲜货
            
            console.log('主分类:', mainCategory.name);
            console.log('子分类:', subCategory.name);
            
            // 提取三级分类
            let thirdCategories = [];
            if (subCategory.children_data && subCategory.children_data.length > 0) {
                thirdCategories = subCategory.children_data.map(thirdCat => ({
                    ...thirdCat,
                    parentName: subCategory.name
                }));
            }
            
            console.log('三级分类:', thirdCategories);
            
            if (thirdCategories.length > 0) {
                console.log('✅ 成功提取到三级分类:', thirdCategories.map(c => c.name).join(', '));
            } else {
                console.log('ℹ️ 该子分类下没有三级分类');
            }
        }

        // 运行测试
        testThirdCategoryExtraction();
    </script>
</body>
</html>
