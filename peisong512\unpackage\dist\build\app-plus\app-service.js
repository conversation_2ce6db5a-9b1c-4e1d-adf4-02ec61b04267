if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((o=>t.resolve(e()).then((()=>o))),(o=>t.resolve(e()).then((()=>{throw o}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.<PERSON>Int64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...o){uni.__log__?uni.__log__(e,t,...o):console[e].apply(console,[...o,t])}function o(e,t){return"string"==typeof e?t:e}const a=[{font_class:"arrow-down",unicode:""},{font_class:"arrow-left",unicode:""},{font_class:"arrow-right",unicode:""},{font_class:"arrow-up",unicode:""},{font_class:"auth",unicode:""},{font_class:"auth-filled",unicode:""},{font_class:"back",unicode:""},{font_class:"bars",unicode:""},{font_class:"calendar",unicode:""},{font_class:"calendar-filled",unicode:""},{font_class:"camera",unicode:""},{font_class:"camera-filled",unicode:""},{font_class:"cart",unicode:""},{font_class:"cart-filled",unicode:""},{font_class:"chat",unicode:""},{font_class:"chat-filled",unicode:""},{font_class:"chatboxes",unicode:""},{font_class:"chatboxes-filled",unicode:""},{font_class:"chatbubble",unicode:""},{font_class:"chatbubble-filled",unicode:""},{font_class:"checkbox",unicode:""},{font_class:"checkbox-filled",unicode:""},{font_class:"checkmarkempty",unicode:""},{font_class:"circle",unicode:""},{font_class:"circle-filled",unicode:""},{font_class:"clear",unicode:""},{font_class:"close",unicode:""},{font_class:"closeempty",unicode:""},{font_class:"cloud-download",unicode:""},{font_class:"cloud-download-filled",unicode:""},{font_class:"cloud-upload",unicode:""},{font_class:"cloud-upload-filled",unicode:""},{font_class:"color",unicode:""},{font_class:"color-filled",unicode:""},{font_class:"compose",unicode:""},{font_class:"contact",unicode:""},{font_class:"contact-filled",unicode:""},{font_class:"down",unicode:""},{font_class:"bottom",unicode:""},{font_class:"download",unicode:""},{font_class:"download-filled",unicode:""},{font_class:"email",unicode:""},{font_class:"email-filled",unicode:""},{font_class:"eye",unicode:""},{font_class:"eye-filled",unicode:""},{font_class:"eye-slash",unicode:""},{font_class:"eye-slash-filled",unicode:""},{font_class:"fire",unicode:""},{font_class:"fire-filled",unicode:""},{font_class:"flag",unicode:""},{font_class:"flag-filled",unicode:""},{font_class:"folder-add",unicode:""},{font_class:"folder-add-filled",unicode:""},{font_class:"font",unicode:""},{font_class:"forward",unicode:""},{font_class:"gear",unicode:""},{font_class:"gear-filled",unicode:""},{font_class:"gift",unicode:""},{font_class:"gift-filled",unicode:""},{font_class:"hand-down",unicode:""},{font_class:"hand-down-filled",unicode:""},{font_class:"hand-up",unicode:""},{font_class:"hand-up-filled",unicode:""},{font_class:"headphones",unicode:""},{font_class:"heart",unicode:""},{font_class:"heart-filled",unicode:""},{font_class:"help",unicode:""},{font_class:"help-filled",unicode:""},{font_class:"home",unicode:""},{font_class:"home-filled",unicode:""},{font_class:"image",unicode:""},{font_class:"image-filled",unicode:""},{font_class:"images",unicode:""},{font_class:"images-filled",unicode:""},{font_class:"info",unicode:""},{font_class:"info-filled",unicode:""},{font_class:"left",unicode:""},{font_class:"link",unicode:""},{font_class:"list",unicode:""},{font_class:"location",unicode:""},{font_class:"location-filled",unicode:""},{font_class:"locked",unicode:""},{font_class:"locked-filled",unicode:""},{font_class:"loop",unicode:""},{font_class:"mail-open",unicode:""},{font_class:"mail-open-filled",unicode:""},{font_class:"map",unicode:""},{font_class:"map-filled",unicode:""},{font_class:"map-pin",unicode:""},{font_class:"map-pin-ellipse",unicode:""},{font_class:"medal",unicode:""},{font_class:"medal-filled",unicode:""},{font_class:"mic",unicode:""},{font_class:"mic-filled",unicode:""},{font_class:"micoff",unicode:""},{font_class:"micoff-filled",unicode:""},{font_class:"minus",unicode:""},{font_class:"minus-filled",unicode:""},{font_class:"more",unicode:""},{font_class:"more-filled",unicode:""},{font_class:"navigate",unicode:""},{font_class:"navigate-filled",unicode:""},{font_class:"notification",unicode:""},{font_class:"notification-filled",unicode:""},{font_class:"paperclip",unicode:""},{font_class:"paperplane",unicode:""},{font_class:"paperplane-filled",unicode:""},{font_class:"person",unicode:""},{font_class:"person-filled",unicode:""},{font_class:"personadd",unicode:""},{font_class:"personadd-filled",unicode:""},{font_class:"personadd-filled-copy",unicode:""},{font_class:"phone",unicode:""},{font_class:"phone-filled",unicode:""},{font_class:"plus",unicode:""},{font_class:"plus-filled",unicode:""},{font_class:"plusempty",unicode:""},{font_class:"pulldown",unicode:""},{font_class:"pyq",unicode:""},{font_class:"qq",unicode:""},{font_class:"redo",unicode:""},{font_class:"redo-filled",unicode:""},{font_class:"refresh",unicode:""},{font_class:"refresh-filled",unicode:""},{font_class:"refreshempty",unicode:""},{font_class:"reload",unicode:""},{font_class:"right",unicode:""},{font_class:"scan",unicode:""},{font_class:"search",unicode:""},{font_class:"settings",unicode:""},{font_class:"settings-filled",unicode:""},{font_class:"shop",unicode:""},{font_class:"shop-filled",unicode:""},{font_class:"smallcircle",unicode:""},{font_class:"smallcircle-filled",unicode:""},{font_class:"sound",unicode:""},{font_class:"sound-filled",unicode:""},{font_class:"spinner-cycle",unicode:""},{font_class:"staff",unicode:""},{font_class:"staff-filled",unicode:""},{font_class:"star",unicode:""},{font_class:"star-filled",unicode:""},{font_class:"starhalf",unicode:""},{font_class:"trash",unicode:""},{font_class:"trash-filled",unicode:""},{font_class:"tune",unicode:""},{font_class:"tune-filled",unicode:""},{font_class:"undo",unicode:""},{font_class:"undo-filled",unicode:""},{font_class:"up",unicode:""},{font_class:"top",unicode:""},{font_class:"upload",unicode:""},{font_class:"upload-filled",unicode:""},{font_class:"videocam",unicode:""},{font_class:"videocam-filled",unicode:""},{font_class:"vip",unicode:""},{font_class:"vip-filled",unicode:""},{font_class:"wallet",unicode:""},{font_class:"wallet-filled",unicode:""},{font_class:"weibo",unicode:""},{font_class:"weixin",unicode:""}],i=(e,t)=>{const o=e.__vccOpts||e;for(const[a,i]of t)o[a]=i;return o};const n=i({name:"UniIcons",emits:["click"],props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customPrefix:{type:String,default:""},fontFamily:{type:String,default:""}},data:()=>({icons:a}),computed:{unicode(){let e=this.icons.find((e=>e.font_class===this.type));return e?e.unicode:""},iconSize(){return"number"==typeof(e=this.size)||/^[0-9]*$/g.test(e)?e+"px":e;var e},styleObj(){return""!==this.fontFamily?`color: ${this.color}; font-size: ${this.iconSize}; font-family: ${this.fontFamily};`:`color: ${this.color}; font-size: ${this.iconSize};`}},methods:{_onClick(){this.$emit("click")}}},[["render",function(t,o,a,i,n,s){return e.openBlock(),e.createElementBlock("text",{style:e.normalizeStyle(s.styleObj),class:e.normalizeClass(["uni-icons",["uniui-"+a.type,a.customPrefix,a.customPrefix?a.type:""]]),onClick:o[0]||(o[0]=(...e)=>s._onClick&&s._onClick(...e))},[e.renderSlot(t.$slots,"default",{},void 0,!0)],6)}],["__scopeId","data-v-5610c8db"]]);function s(e){let t="";for(let o in e){t+=`${o}:${e[o]};`}return t}const l=i({name:"uni-easyinput",emits:["click","iconClick","update:modelValue","input","focus","blur","confirm","clear","eyes","change","keyboardheightchange"],model:{prop:"modelValue",event:"update:modelValue"},options:{virtualHost:!0},inject:{form:{from:"uniForm",default:null},formItem:{from:"uniFormItem",default:null}},props:{name:String,value:[Number,String],modelValue:[Number,String],type:{type:String,default:"text"},clearable:{type:Boolean,default:!0},autoHeight:{type:Boolean,default:!1},placeholder:{type:String,default:" "},placeholderStyle:String,focus:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},clearSize:{type:[Number,String],default:24},inputBorder:{type:Boolean,default:!0},prefixIcon:{type:String,default:""},suffixIcon:{type:String,default:""},trim:{type:[Boolean,String],default:!1},cursorSpacing:{type:Number,default:0},passwordIcon:{type:Boolean,default:!0},adjustPosition:{type:Boolean,default:!0},primaryColor:{type:String,default:"#2979ff"},styles:{type:Object,default:()=>({color:"#333",backgroundColor:"#fff",disableColor:"#F7F6F6",borderColor:"#e5e5e5"})},errorMessage:{type:[String,Boolean],default:""}},data:()=>({focused:!1,val:"",showMsg:"",border:!1,isFirstBorder:!1,showClearIcon:!1,showPassword:!1,focusShow:!1,localMsg:"",isEnter:!1}),computed:{isVal(){const e=this.val;return!(!e&&0!==e)},msg(){return this.localMsg||this.errorMessage},inputMaxlength(){return Number(this.maxlength)},boxStyle(){return`color:${this.inputBorder&&this.msg?"#e43d33":this.styles.color};`},inputContentClass(){return function(e){let t="";for(let o in e)e[o]&&(t+=`${o} `);return t}({"is-input-border":this.inputBorder,"is-input-error-border":this.inputBorder&&this.msg,"is-textarea":"textarea"===this.type,"is-disabled":this.disabled,"is-focused":this.focusShow})},inputContentStyle(){const e=this.focusShow?this.primaryColor:this.styles.borderColor;return s({"border-color":(this.inputBorder&&this.msg?"#dd524d":e)||"#e5e5e5","background-color":this.disabled?this.styles.disableColor:this.styles.backgroundColor})},inputStyle(){return s({"padding-right":"password"===this.type||this.clearable||this.prefixIcon?"":"10px","padding-left":this.prefixIcon?"":"10px"})}},watch:{value(e){this.val=e},modelValue(e){this.val=e},focus(e){this.$nextTick((()=>{this.focused=this.focus,this.focusShow=this.focus}))}},created(){this.init(),this.form&&this.formItem&&this.$watch("formItem.errMsg",(e=>{this.localMsg=e}))},mounted(){this.$nextTick((()=>{this.focused=this.focus,this.focusShow=this.focus}))},methods:{init(){this.value||0===this.value?this.val=this.value:this.modelValue||0===this.modelValue||""===this.modelValue?this.val=this.modelValue:this.val=null},onClickIcon(e){this.$emit("iconClick",e)},onEyes(){this.showPassword=!this.showPassword,this.$emit("eyes",this.showPassword)},onInput(e){let t=e.detail.value;this.trim&&("boolean"==typeof this.trim&&this.trim&&(t=this.trimStr(t)),"string"==typeof this.trim&&(t=this.trimStr(t,this.trim))),this.errMsg&&(this.errMsg=""),this.val=t,this.$emit("input",t),this.$emit("update:modelValue",t)},onFocus(){this.$nextTick((()=>{this.focused=!0})),this.$emit("focus",null)},_Focus(e){this.focusShow=!0,this.$emit("focus",e)},onBlur(){this.focused=!1,this.$emit("blur",null)},_Blur(e){if(e.detail.value,this.focusShow=!1,this.$emit("blur",e),!1===this.isEnter&&this.$emit("change",this.val),this.form&&this.formItem){const{validateTrigger:e}=this.form;"blur"===e&&this.formItem.onFieldChange()}},onConfirm(e){this.$emit("confirm",this.val),this.isEnter=!0,this.$emit("change",this.val),this.$nextTick((()=>{this.isEnter=!1}))},onClear(e){this.val="",this.$emit("input",""),this.$emit("update:modelValue",""),this.$emit("clear")},onkeyboardheightchange(e){this.$emit("keyboardheightchange",e)},trimStr:(e,t="both")=>"both"===t?e.trim():"left"===t?e.trimLeft():"right"===t?e.trimRight():"start"===t?e.trimStart():"end"===t?e.trimEnd():"all"===t?e.replace(/\s+/g,""):e}},[["render",function(t,a,i,s,l,r){const c=o(e.resolveDynamicComponent("uni-icons"),n);return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["uni-easyinput",{"uni-easyinput-error":r.msg}]),style:e.normalizeStyle(r.boxStyle)},[e.createElementVNode("view",{class:e.normalizeClass(["uni-easyinput__content",r.inputContentClass]),style:e.normalizeStyle(r.inputContentStyle)},[i.prefixIcon?(e.openBlock(),e.createBlock(c,{key:0,class:"content-clear-icon",type:i.prefixIcon,color:"#c0c4cc",onClick:a[0]||(a[0]=e=>r.onClickIcon("prefix")),size:"22"},null,8,["type"])):e.createCommentVNode("",!0),e.renderSlot(t.$slots,"left",{},void 0,!0),"textarea"===i.type?(e.openBlock(),e.createElementBlock("textarea",{key:1,class:e.normalizeClass(["uni-easyinput__content-textarea",{"input-padding":i.inputBorder}]),name:i.name,value:l.val,placeholder:i.placeholder,placeholderStyle:i.placeholderStyle,disabled:i.disabled,"placeholder-class":"uni-easyinput__placeholder-class",maxlength:r.inputMaxlength,focus:l.focused,autoHeight:i.autoHeight,"cursor-spacing":i.cursorSpacing,"adjust-position":i.adjustPosition,onInput:a[1]||(a[1]=(...e)=>r.onInput&&r.onInput(...e)),onBlur:a[2]||(a[2]=(...e)=>r._Blur&&r._Blur(...e)),onFocus:a[3]||(a[3]=(...e)=>r._Focus&&r._Focus(...e)),onConfirm:a[4]||(a[4]=(...e)=>r.onConfirm&&r.onConfirm(...e)),onKeyboardheightchange:a[5]||(a[5]=(...e)=>r.onkeyboardheightchange&&r.onkeyboardheightchange(...e))},null,42,["name","value","placeholder","placeholderStyle","disabled","maxlength","focus","autoHeight","cursor-spacing","adjust-position"])):(e.openBlock(),e.createElementBlock("input",{key:2,type:"password"===i.type?"text":i.type,class:"uni-easyinput__content-input",style:e.normalizeStyle(r.inputStyle),name:i.name,value:l.val,password:!l.showPassword&&"password"===i.type,placeholder:i.placeholder,placeholderStyle:i.placeholderStyle,"placeholder-class":"uni-easyinput__placeholder-class",disabled:i.disabled,maxlength:r.inputMaxlength,focus:l.focused,confirmType:i.confirmType,"cursor-spacing":i.cursorSpacing,"adjust-position":i.adjustPosition,onFocus:a[6]||(a[6]=(...e)=>r._Focus&&r._Focus(...e)),onBlur:a[7]||(a[7]=(...e)=>r._Blur&&r._Blur(...e)),onInput:a[8]||(a[8]=(...e)=>r.onInput&&r.onInput(...e)),onConfirm:a[9]||(a[9]=(...e)=>r.onConfirm&&r.onConfirm(...e)),onKeyboardheightchange:a[10]||(a[10]=(...e)=>r.onkeyboardheightchange&&r.onkeyboardheightchange(...e))},null,44,["type","name","value","password","placeholder","placeholderStyle","disabled","maxlength","focus","confirmType","cursor-spacing","adjust-position"])),"password"===i.type&&i.passwordIcon?(e.openBlock(),e.createElementBlock(e.Fragment,{key:3},[r.isVal?(e.openBlock(),e.createBlock(c,{key:0,class:e.normalizeClass(["content-clear-icon",{"is-textarea-icon":"textarea"===i.type}]),type:l.showPassword?"eye-slash-filled":"eye-filled",size:22,color:l.focusShow?i.primaryColor:"#c0c4cc",onClick:r.onEyes},null,8,["class","type","color","onClick"])):e.createCommentVNode("",!0)],64)):e.createCommentVNode("",!0),i.suffixIcon?(e.openBlock(),e.createElementBlock(e.Fragment,{key:4},[i.suffixIcon?(e.openBlock(),e.createBlock(c,{key:0,class:"content-clear-icon",type:i.suffixIcon,color:"#c0c4cc",onClick:a[11]||(a[11]=e=>r.onClickIcon("suffix")),size:"22"},null,8,["type"])):e.createCommentVNode("",!0)],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:5},[i.clearable&&r.isVal&&!i.disabled&&"textarea"!==i.type?(e.openBlock(),e.createBlock(c,{key:0,class:e.normalizeClass(["content-clear-icon",{"is-textarea-icon":"textarea"===i.type}]),type:"clear",size:i.clearSize,color:r.msg?"#dd524d":l.focusShow?i.primaryColor:"#c0c4cc",onClick:r.onClear},null,8,["class","size","color","onClick"])):e.createCommentVNode("",!0)],64)),e.renderSlot(t.$slots,"right",{},void 0,!0)],6)],6)}],["__scopeId","data-v-63e7b685"]]);const r=i({name:"uniFormsItem",options:{virtualHost:!0},provide(){return{uniFormItem:this}},inject:{form:{from:"uniForm",default:null}},props:{rules:{type:Array,default:()=>null},name:{type:[String,Array],default:""},required:{type:Boolean,default:!1},label:{type:String,default:""},labelWidth:{type:[String,Number],default:""},labelAlign:{type:String,default:""},errorMessage:{type:[String,Boolean],default:""},leftIcon:String,iconColor:{type:String,default:"#606266"}},data:()=>({errMsg:"",userRules:null,localLabelAlign:"left",localLabelWidth:"70px",localLabelPos:"left",border:!1,isFirstBorder:!1}),computed:{msg(){return this.errorMessage||this.errMsg}},watch:{"form.formRules"(e){this.init()},"form.labelWidth"(e){this.localLabelWidth=this._labelWidthUnit(e)},"form.labelPosition"(e){this.localLabelPos=this._labelPosition()},"form.labelAlign"(e){}},created(){this.init(!0),this.name&&this.form&&this.$watch((()=>this.form._getDataValue(this.name,this.form.localData)),((e,t)=>{if(!this.form._isEqual(e,t)){const t=this.itemSetValue(e);this.onFieldChange(t,!1)}}),{immediate:!1})},unmounted(){this.__isUnmounted=!0,this.unInit()},methods:{setRules(e=null){this.userRules=e,this.init(!1)},setValue(){},async onFieldChange(e,t=!0){const{formData:o,localData:a,errShowType:i,validateCheck:n,validateTrigger:s,_isRequiredField:l,_realName:r}=this.form,c=r(this.name);e||(e=this.form.formData[c]);const d=this.itemRules.rules&&this.itemRules.rules.length;if(!this.validator||!d||0===d)return;const u=l(this.itemRules.rules||[]);let m=null;return"bind"===s||t?(m=await this.validator.validateUpdate({[c]:e},o),u||void 0!==e&&""!==e||(m=null),m&&m.errorMessage?("undertext"===i&&(this.errMsg=m?m.errorMessage:""),"toast"===i&&uni.showToast({title:m.errorMessage||"校验错误",icon:"none"}),"modal"===i&&uni.showModal({title:"提示",content:m.errorMessage||"校验错误"})):this.errMsg="",n(m||null)):this.errMsg="",m||null},init(e=!1){const{validator:t,formRules:o,childrens:a,formData:i,localData:n,_realName:s,labelWidth:l,_getDataValue:r,_setDataValue:c}=this.form||{};if(this.localLabelAlign=this._justifyContent(),this.localLabelWidth=this._labelWidthUnit(l),this.localLabelPos=this._labelPosition(),this.form&&e&&a.push(this),!t||!o)return;this.form.isFirstBorder||(this.form.isFirstBorder=!0,this.isFirstBorder=!0),this.group&&(this.group.isFirstBorder||(this.group.isFirstBorder=!0,this.isFirstBorder=!0)),this.border=this.form.border;const d=s(this.name),u=this.userRules||this.rules;"object"==typeof o&&u&&(o[d]={rules:u},t.updateSchema(o));const m=o[d]||{};this.itemRules=m,this.validator=t,this.itemSetValue(r(this.name,n))},unInit(){if(this.form){const{childrens:e,formData:t,_realName:o}=this.form;e.forEach(((e,a)=>{e===this&&(this.form.childrens.splice(a,1),delete t[o(e.name)])}))}},itemSetValue(e){const t=this.form._realName(this.name),o=this.itemRules.rules||[],a=this.form._getValue(t,e,o);return this.form._setDataValue(t,this.form.formData,a),a},clearValidate(){this.errMsg=""},_isRequired(){return this.required},_justifyContent(){if(this.form){const{labelAlign:e}=this.form;let t=this.labelAlign?this.labelAlign:e;if("left"===t)return"flex-start";if("center"===t)return"center";if("right"===t)return"flex-end"}return"flex-start"},_labelWidthUnit(e){return this.num2px(this.labelWidth?this.labelWidth:e||(this.label?70:"auto"))},_labelPosition(){return this.form&&this.form.labelPosition||"left"},isTrigger:(e,t,o)=>"submit"!==e&&e?"bind":void 0===e?"bind"!==t?t?"submit":""===o?"bind":"submit":"bind":"submit",num2px:e=>"number"==typeof e?`${e}px`:e}},[["render",function(t,o,a,i,n,s){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["uni-forms-item",["is-direction-"+n.localLabelPos,n.border?"uni-forms-item--border":"",n.border&&n.isFirstBorder?"is-first-border":""]])},[e.renderSlot(t.$slots,"label",{},(()=>[e.createElementVNode("view",{class:e.normalizeClass(["uni-forms-item__label",{"no-label":!a.label&&!a.required}]),style:e.normalizeStyle({width:n.localLabelWidth,justifyContent:n.localLabelAlign})},[a.required?(e.openBlock(),e.createElementBlock("text",{key:0,class:"is-required"},"*")):e.createCommentVNode("",!0),e.createElementVNode("text",null,e.toDisplayString(a.label),1)],6)]),!0),e.createElementVNode("view",{class:"uni-forms-item__content"},[e.renderSlot(t.$slots,"default",{},void 0,!0),e.createElementVNode("view",{class:e.normalizeClass(["uni-forms-item__error",{"msg--active":s.msg}])},[e.createElementVNode("text",null,e.toDisplayString(s.msg),1)],2)])],2)}],["__scopeId","data-v-7b97b15c"]]);var c={email:/^\S+?@\S+?\.\S+?$/,idcard:/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,url:new RegExp("^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$","i")};const d={int:"integer",bool:"boolean",double:"number",long:"number",password:"string"};function u(e,t=""){["label"].forEach((t=>{void 0===e[t]&&(e[t]="")}));let o=t;for(let a in e){let t=new RegExp("{"+a+"}");o=o.replace(t,e[a])}return o}const m={integer:e=>m.number(e)&&parseInt(e,10)===e,string:e=>"string"==typeof e,number:e=>!isNaN(e)&&"number"==typeof e,boolean:function(e){return"boolean"==typeof e},float:function(e){return m.number(e)&&!m.integer(e)},array:e=>Array.isArray(e),object:e=>"object"==typeof e&&!m.array(e),date:e=>e instanceof Date,timestamp(e){return!(!this.integer(e)||Math.abs(e).toString().length>16)},file:e=>"string"==typeof e.url,email:e=>"string"==typeof e&&!!e.match(c.email)&&e.length<255,url:e=>"string"==typeof e&&!!e.match(c.url),pattern(e,t){try{return new RegExp(e).test(t)}catch(o){return!1}},method:e=>"function"==typeof e,idcard:e=>"string"==typeof e&&!!e.match(c.idcard),"url-https"(e){return this.url(e)&&e.startsWith("https://")},"url-scheme":e=>e.startsWith("://"),"url-web":e=>!1};const p={required:(e,t,o)=>e.required&&function(e,t){return null==e||"string"==typeof e&&!e||!(!Array.isArray(e)||e.length)||"object"===t&&!Object.keys(e).length}(t,e.format||typeof t)?u(e,e.errorMessage||o.required):null,range(e,t,o){const{range:a,errorMessage:i}=e;let n=new Array(a.length);for(let l=0;l<a.length;l++){const e=a[l];m.object(e)&&void 0!==e.value?n[l]=e.value:n[l]=e}let s=!1;return Array.isArray(t)?s=new Set(t.concat(n)).size===n.length:n.indexOf(t)>-1&&(s=!0),s?null:u(e,i||o.enum)},rangeNumber(e,t,o){if(!m.number(t))return u(e,e.errorMessage||o.pattern.mismatch);let{minimum:a,maximum:i,exclusiveMinimum:n,exclusiveMaximum:s}=e,l=n?t<=a:t<a,r=s?t>=i:t>i;return void 0!==a&&l?u(e,e.errorMessage||o.number[n?"exclusiveMinimum":"minimum"]):void 0!==i&&r?u(e,e.errorMessage||o.number[s?"exclusiveMaximum":"maximum"]):void 0!==a&&void 0!==i&&(l||r)?u(e,e.errorMessage||o.number.range):null},rangeLength(e,t,o){if(!m.string(t)&&!m.array(t))return u(e,e.errorMessage||o.pattern.mismatch);let a=e.minLength,i=e.maxLength,n=t.length;return void 0!==a&&n<a?u(e,e.errorMessage||o.length.minLength):void 0!==i&&n>i?u(e,e.errorMessage||o.length.maxLength):void 0!==a&&void 0!==i&&(n<a||n>i)?u(e,e.errorMessage||o.length.range):null},pattern:(e,t,o)=>m.pattern(e.pattern,t)?null:u(e,e.errorMessage||o.pattern.mismatch),format(e,t,o){var a=Object.keys(m),i=d[e.format]?d[e.format]:e.format||e.arrayType;return a.indexOf(i)>-1&&!m[i](t)?u(e,e.errorMessage||o.typeError):null},arrayTypeFormat(e,t,o){if(!Array.isArray(t))return u(e,e.errorMessage||o.typeError);for(let a=0;a<t.length;a++){const i=t[a];let n=this.format(e,i,o);if(null!==n)return n}return null}};class g extends class{constructor(e){this._message=e}async validateRule(e,t,o,a,i){var n=null;let s=t.rules;if(s.findIndex((e=>e.required))<0){if(null==o)return n;if("string"==typeof o&&!o.length)return n}var l=this._message;if(void 0===s)return l.default;for(var r=0;r<s.length;r++){let c=s[r],d=this._getValidateType(c);if(Object.assign(c,{label:t.label||`["${e}"]`}),p[d]&&null!=(n=p[d](c,o,l)))break;if(c.validateExpr){let e=Date.now();if(!1===c.validateExpr(o,i,e)){n=this._getMessage(c,c.errorMessage||this._message.default);break}}if(c.validateFunction&&null!==(n=await this.validateFunction(c,o,a,i,d)))break}return null!==n&&(n=l.TAG+n),n}async validateFunction(e,t,o,a,i){let n=null;try{let s=null;const l=await e.validateFunction(e,t,a||o,(e=>{s=e}));(s||"string"==typeof l&&l||!1===l)&&(n=this._getMessage(e,s||l,i))}catch(s){n=this._getMessage(e,s.message,i)}return n}_getMessage(e,t,o){return u(e,t||e.errorMessage||this._message[o]||t.default)}_getValidateType(e){var t="";return e.required?t="required":e.format?t="format":e.arrayType?t="arrayTypeFormat":e.range?t="range":void 0!==e.maximum||void 0!==e.minimum?t="rangeNumber":void 0!==e.maxLength||void 0!==e.minLength?t="rangeLength":e.pattern?t="pattern":e.validateFunction&&(t="validateFunction"),t}}{constructor(e,t){super(g.message),this._schema=e,this._options=t||null}updateSchema(e){this._schema=e}async validate(e,t){let o=this._checkFieldInSchema(e);return o||(o=await this.invokeValidate(e,!1,t)),o.length?o[0]:null}async validateAll(e,t){let o=this._checkFieldInSchema(e);return o||(o=await this.invokeValidate(e,!0,t)),o}async validateUpdate(e,t){let o=this._checkFieldInSchema(e);return o||(o=await this.invokeValidateUpdate(e,!1,t)),o.length?o[0]:null}async invokeValidate(e,t,o){let a=[],i=this._schema;for(let n in i){let s=i[n],l=await this.validateRule(n,s,e[n],e,o);if(null!=l&&(a.push({key:n,errorMessage:l}),!t))break}return a}async invokeValidateUpdate(e,t,o){let a=[];for(let i in e){let n=await this.validateRule(i,this._schema[i],e[i],e,o);if(null!=n&&(a.push({key:i,errorMessage:n}),!t))break}return a}_checkFieldInSchema(e){var t=Object.keys(e),o=Object.keys(this._schema);if(new Set(t.concat(o)).size===o.length)return"";var a=t.filter((e=>o.indexOf(e)<0));return[{key:"invalid",errorMessage:u({field:JSON.stringify(a)},g.message.TAG+g.message.defaultInvalid)}]}}g.message=new function(){return{TAG:"",default:"验证错误",defaultInvalid:"提交的字段{field}在数据库中并不存在",validateFunction:"验证无效",required:"{label}必填",enum:"{label}超出范围",timestamp:"{label}格式无效",whitespace:"{label}不能为空",typeError:"{label}类型无效",date:{format:"{label}日期{value}格式无效",parse:"{label}日期无法解析,{value}无效",invalid:"{label}日期{value}无效"},length:{minLength:"{label}长度不能少于{minLength}",maxLength:"{label}长度不能超过{maxLength}",range:"{label}必须介于{minLength}和{maxLength}之间"},number:{minimum:"{label}不能小于{minimum}",maximum:"{label}不能大于{maximum}",exclusiveMinimum:"{label}不能小于等于{minimum}",exclusiveMaximum:"{label}不能大于等于{maximum}",range:"{label}必须介于{minimum}and{maximum}之间"},pattern:{mismatch:"{label}格式不匹配"}}};const h=(e,t,o)=>{const a=o.find((e=>{return e.format&&("int"===(t=e.format)||"double"===t||"number"===t||"timestamp"===t);var t})),i=o.find((e=>e.format&&"boolean"===e.format||"bool"===e.format));return a&&(t=t||0===t?S(Number(t))?Number(t):t:null),i&&(t=!!x(t)&&t),t},f=(e,t)=>_(t,e),y=(e,t={})=>{const o=k(e);if("object"==typeof o&&Array.isArray(o)&&o.length>1){return o.reduce(((e,t)=>e+`#${t}`),"_formdata_")}return o[0]||e},v=e=>{let t=e.replace("_formdata_#","");return t=t.split("#").map((e=>S(e)?Number(e):e)),t},b=(e,t,o)=>("object"!=typeof e||k(t).reduce(((e,t,a,i)=>a===i.length-1?(e[t]=o,null):(t in e||(e[t]=/^[0-9]{1,}$/.test(i[a+1])?[]:{}),e[t])),e),e);function k(e){return Array.isArray(e)?e:e.replace(/\[/g,".").replace(/\]/g,"").split(".")}const _=(e,t,o="undefined")=>{let a=k(t).reduce(((e,t)=>(e||{})[t]),e);return a&&void 0===a?o:a},S=e=>!isNaN(Number(e)),x=e=>"boolean"==typeof e;const w=i({name:"uniForms",emits:["validate","submit"],options:{virtualHost:!0},props:{value:{type:Object,default:()=>null},modelValue:{type:Object,default:()=>null},model:{type:Object,default:()=>null},rules:{type:Object,default:()=>({})},errShowType:{type:String,default:"undertext"},validateTrigger:{type:String,default:"submit"},labelPosition:{type:String,default:"left"},labelWidth:{type:[String,Number],default:""},labelAlign:{type:String,default:"left"},border:{type:Boolean,default:!1}},provide(){return{uniForm:this}},data:()=>({formData:{},formRules:{}}),computed:{localData(){const e=this.model||this.modelValue||this.value;return e?(t=e,JSON.parse(JSON.stringify(t))):{};var t}},watch:{rules:{handler:function(e,t){this.setRules(e)},deep:!0,immediate:!0}},created(){getApp().$vm.$.appContext.config.globalProperties.binddata||(getApp().$vm.$.appContext.config.globalProperties.binddata=function(e,o,a){if(a)this.$refs[a].setValue(e,o);else{let a;for(let e in this.$refs){const t=this.$refs[e];if(t&&t.$options&&"uniForms"===t.$options.name){a=t;break}}if(!a)return t("error","at uni_modules/uni-forms/components/uni-forms/uni-forms.vue:182","当前 uni-froms 组件缺少 ref 属性");a.setValue(e,o)}}),this.childrens=[],this.inputChildrens=[],this.setRules(this.rules)},methods:{setRules(e){this.formRules=Object.assign({},this.formRules,e),this.validator=new g(e)},setValue(e,t){let o=this.childrens.find((t=>t.name===e));return o?(this.formData[e]=h(0,t,this.formRules[e]&&this.formRules[e].rules||[]),o.onFieldChange(this.formData[e])):null},validate(e,t){return this.checkAll(this.formData,e,t)},validateField(e=[],t){e=[].concat(e);let o={};return this.childrens.forEach((t=>{const a=y(t.name);-1!==e.indexOf(a)&&(o=Object.assign({},o,{[a]:this.formData[a]}))})),this.checkAll(o,[],t)},clearValidate(e=[]){e=[].concat(e),this.childrens.forEach((t=>{if(0===e.length)t.errMsg="";else{const o=y(t.name);-1!==e.indexOf(o)&&(t.errMsg="")}}))},submit(e,o,a){for(let t in this.dataValue){this.childrens.find((e=>e.name===t))&&void 0===this.formData[t]&&(this.formData[t]=this._getValue(t,this.dataValue[t]))}return a||t("warn","at uni_modules/uni-forms/components/uni-forms/uni-forms.vue:289","submit 方法即将废弃，请使用validate方法代替！"),this.checkAll(this.formData,e,o,"submit")},async checkAll(e,t,o,a){if(!this.validator)return;let i,n=[];for(let c in e){const e=this.childrens.find((e=>y(e.name)===c));e&&n.push(e)}o||"function"!=typeof t||(o=t),!o&&"function"!=typeof o&&Promise&&(i=new Promise(((e,t)=>{o=function(o,a){o?t(o):e(a)}})));let s=[],l=JSON.parse(JSON.stringify(e));for(let c in n){const e=n[c];let t=y(e.name);const o=await e.onFieldChange(l[t]);if(o&&(s.push(o),"toast"===this.errShowType||"modal"===this.errShowType))break}Array.isArray(s)&&0===s.length&&(s=null),Array.isArray(t)&&t.forEach((e=>{let t=y(e),o=f(e,this.localData);void 0!==o&&(l[t]=o)})),"submit"===a?this.$emit("submit",{detail:{value:l,errors:s}}):this.$emit("validate",s);let r={};return r=((e={},t)=>{let o=JSON.parse(JSON.stringify(e)),a={};for(let i in o){let e=v(i);b(a,e,o[i])}return a})(l,this.name),o&&"function"==typeof o&&o(s,r),i&&o?i:null},validateCheck(e){this.$emit("validate",e)},_getValue:h,_isRequiredField:e=>{let t=!1;for(let o=0;o<e.length;o++){if(e[o].required){t=!0;break}}return t},_setDataValue:(e,t,o)=>(t[e]=o,o||""),_getDataValue:f,_realName:y,_isRealName:e=>/^_formdata_#*/.test(e),_isEqual:(e,t)=>{if(e===t)return 0!==e||1/e==1/t;if(null==e||null==t)return e===t;var o=toString.call(e);if(o!==toString.call(t))return!1;switch(o){case"[object RegExp]":case"[object String]":return""+e==""+t;case"[object Number]":return+e!=+e?+t!=+t:0==+e?1/+e==1/t:+e==+t;case"[object Date]":case"[object Boolean]":return+e==+t}if("[object Object]"==o){var a=Object.getOwnPropertyNames(e),i=Object.getOwnPropertyNames(t);if(a.length!=i.length)return!1;for(var n=0;n<a.length;n++){var s=a[n];if(e[s]!==t[s])return!1}return!0}return"[object Array]"==o?e.toString()==t.toString():void 0}}},[["render",function(t,o,a,i,n,s){return e.openBlock(),e.createElementBlock("view",{class:"uni-forms"},[e.createElementVNode("form",null,[e.renderSlot(t.$slots,"default",{},void 0,!0)])])}],["__scopeId","data-v-bdc00db0"]]);const N=i({data:()=>({formData:{phone:"",password:""},rules:{phone:{rules:[{required:!0,errorMessage:"请输入手机号"},{pattern:/^1[3-9]\d{9}$/,errorMessage:"请输入正确的手机号"}]},password:{rules:[{required:!0,errorMessage:"请输入密码"}]}},rememberLogin:!0,autoLogin:!1}),onLoad(){t("log","at pages/login/login.vue:60","登录页面加载");const e=getCurrentPages(),o=e[e.length-1];if(o&&"pages/login/login"===o.route){const e=this.getOpenerEventChannel();e&&e.on&&e.on("loginError",(e=>{t("log","at pages/login/login.vue:70","接收到首页传来的错误信息:",e),e&&e.error&&uni.showToast({title:e.error,icon:"none",duration:3e3})}))}this.loadSavedLoginInfo()},onShow(){setTimeout((()=>{this.checkAutoLogin()}),100)},methods:{toggleRemember(){this.rememberLogin=!this.rememberLogin,t("log","at pages/login/login.vue:94","记住账号:",this.rememberLogin),this.rememberLogin||(this.autoLogin=!1)},toggleAutoLogin(){this.autoLogin=!this.autoLogin,t("log","at pages/login/login.vue:105","自动登录:",this.autoLogin),this.autoLogin&&(this.rememberLogin=!0)},loadSavedLoginInfo(){try{const e=uni.getStorageSync("delivererLoginConfig");if(e){const o=JSON.parse(e);t("log","at pages/login/login.vue:119","读取到已保存的登录配置:",o),this.rememberLogin=!1!==o.rememberLogin,this.autoLogin=!0===o.autoLogin,this.rememberLogin&&o.phone&&(this.formData.phone=o.phone,this.autoLogin&&o.password&&(this.formData.password=o.password)),this.$nextTick((()=>{this.checkAutoLogin()}))}else t("log","at pages/login/login.vue:140","未找到已保存的登录配置，使用默认值")}catch(e){t("error","at pages/login/login.vue:143","读取保存的登录信息出错:",e)}},checkAutoLogin(){const e=uni.getStorageSync("token"),o=uni.getStorageSync("isLoggedIn");if(e&&"true"===o)return t("log","at pages/login/login.vue:154","用户已登录，直接跳转到首页"),void uni.reLaunch({url:"/pages/index/index"});this.autoLogin&&this.formData.phone&&this.formData.password?(t("log","at pages/login/login.vue:163","检测到自动登录设置，尝试自动登录"),setTimeout((()=>{this.doLogin()}),500)):this.autoLogin&&this.formData.phone&&!this.formData.password&&(t("log","at pages/login/login.vue:170","自动登录已开启但密码已清除，需要重新输入密码"),uni.showToast({title:"请输入密码以完成自动登录",icon:"none",duration:2e3}))},saveLoginInfo(){const e={rememberLogin:this.rememberLogin,autoLogin:this.autoLogin,phone:this.rememberLogin?this.formData.phone:"",password:this.autoLogin?this.formData.password:""};t("log","at pages/login/login.vue:188","保存登录配置:",e),uni.setStorageSync("delivererLoginConfig",JSON.stringify(e))},async doLogin(){var e;try{if(!(await this.$refs.loginForm.validate()))return;uni.showLoading({title:"登录中...",mask:!0});const a={phone:this.formData.phone.trim(),password:this.formData.password},i=getApp().globalData.BASE_API+"/api/deliverers/login";t("log","at pages/login/login.vue:213","发送登录请求到:",i);try{const o=await uni.request({url:i,method:"POST",data:a,header:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});if(uni.hideLoading(),t("log","at pages/login/login.vue:231","登录响应:",o),o.data&&200===o.data.code&&o.data.data){const e=o.data.data;t("log","at pages/login/login.vue:238","登录成功，保存数据:",e),e.token&&(uni.setStorageSync("token",e.token),getApp().globalData.token=e.token,getApp().globalData.isLoggedIn=!0,getApp().globalData.requestHeader={Authorization:"Bearer "+e.token,"Content-Type":"application/json",Accept:"application/json"}),e.user&&uni.setStorageSync("userInfo",JSON.stringify(e.user)),e.deliverer&&(uni.setStorageSync("delivererInfo",JSON.stringify(e.deliverer)),getApp().globalData.delivererId=e.deliverer.id),e.employee&&uni.setStorageSync("employeeInfo",JSON.stringify(e.employee)),uni.setStorageSync("isLoggedIn","true"),this.saveLoginInfo(),uni.showToast({title:"登录成功",icon:"success",duration:1500}),setTimeout((()=>{uni.reLaunch({url:"/pages/index/index"})}),1500)}else{const a=(null==(e=o.data)?void 0:e.message)||"登录失败，请检查账号密码";t("error","at pages/login/login.vue:291","登录失败:",a),uni.showToast({title:a,icon:"none",duration:3e3})}}catch(o){uni.hideLoading(),t("error","at pages/login/login.vue:303","登录请求异常:",o);let e="网络连接失败，请检查网络设置";o.errMsg&&(o.errMsg.includes("timeout")?e="请求超时，请稍后重试":o.errMsg.includes("fail")&&(e="网络连接失败，请检查网络设置")),uni.showToast({title:e,icon:"none",duration:3e3})}}catch(a){uni.hideLoading(),t("error","at pages/login/login.vue:325","登录过程异常:",a),uni.showToast({title:"登录过程出现异常，请稍后重试",icon:"none",duration:3e3})}}}},[["render",function(t,a,i,n,s,c){const d=o(e.resolveDynamicComponent("uni-easyinput"),l),u=o(e.resolveDynamicComponent("uni-forms-item"),r),m=o(e.resolveDynamicComponent("uni-forms"),w);return e.openBlock(),e.createElementBlock("view",{class:"login-container"},[e.createElementVNode("view",{class:"logo"},[e.createElementVNode("image",{src:"/static/logo.png",mode:"aspectFit"})]),e.createElementVNode("view",{class:"login-form"},[e.createVNode(m,{ref:"loginForm",modelValue:s.formData,rules:s.rules},{default:e.withCtx((()=>[e.createVNode(u,{name:"phone",required:""},{default:e.withCtx((()=>[e.createVNode(d,{modelValue:s.formData.phone,"onUpdate:modelValue":a[0]||(a[0]=e=>s.formData.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])])),_:1}),e.createVNode(u,{name:"password",required:""},{default:e.withCtx((()=>[e.createVNode(d,{modelValue:s.formData.password,"onUpdate:modelValue":a[1]||(a[1]=e=>s.formData.password=e),type:"password",placeholder:"请输入密码"},null,8,["modelValue"])])),_:1}),e.createElementVNode("view",{class:"form-options"},[e.createElementVNode("label",{class:"remember-box",onClick:a[2]||(a[2]=(...e)=>c.toggleRemember&&c.toggleRemember(...e))},[e.createElementVNode("checkbox",{checked:s.rememberLogin,color:"#007AFF",style:{transform:"scale(0.8)"}},null,8,["checked"]),e.createElementVNode("text",null,"记住账号")]),e.createElementVNode("label",{class:"auto-login",onClick:a[3]||(a[3]=(...e)=>c.toggleAutoLogin&&c.toggleAutoLogin(...e))},[e.createElementVNode("checkbox",{checked:s.autoLogin,color:"#007AFF",style:{transform:"scale(0.8)"}},null,8,["checked"]),e.createElementVNode("text",null,"自动登录")])])])),_:1},8,["modelValue","rules"]),e.createElementVNode("button",{class:"login-btn",type:"primary",onClick:a[4]||(a[4]=(...e)=>c.doLogin&&c.doLogin(...e))},"登录")])])}]]);const C=i({name:"UniSegmentedControl",emits:["clickItem"],props:{current:{type:Number,default:0},values:{type:Array,default:()=>[]},activeColor:{type:String,default:"#2979FF"},inActiveColor:{type:String,default:"transparent"},styleType:{type:String,default:"button"}},data:()=>({currentIndex:0}),watch:{current(e){e!==this.currentIndex&&(this.currentIndex=e)}},computed:{},created(){this.currentIndex=this.current},methods:{_onClick(e){this.currentIndex!==e&&(this.currentIndex=e,this.$emit("clickItem",{currentIndex:e}))}}},[["render",function(t,o,a,i,n,s){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass([["text"===a.styleType?"segmented-control--text":"segmented-control--button"],"segmented-control"]),style:e.normalizeStyle({borderColor:"text"===a.styleType?"":a.activeColor})},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.values,((t,o)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass([["text"===a.styleType?"":"segmented-control__item--button",0===o&&"button"===a.styleType?"segmented-control__item--button--first":"",o===a.values.length-1&&"button"===a.styleType?"segmented-control__item--button--last":""],"segmented-control__item"]),key:o,style:e.normalizeStyle({backgroundColor:o===n.currentIndex&&"button"===a.styleType?a.activeColor:"button"===a.styleType?a.inActiveColor:"transparent",borderColor:o===n.currentIndex&&"text"===a.styleType||"button"===a.styleType?a.activeColor:a.inActiveColor}),onClick:e=>s._onClick(o)},[e.createElementVNode("view",null,[e.createElementVNode("text",{style:e.normalizeStyle({color:o===n.currentIndex?"text"===a.styleType?a.activeColor:"#fff":"text"===a.styleType?"#000":a.activeColor}),class:e.normalizeClass(["segmented-control__text","text"===a.styleType&&o===n.currentIndex?"segmented-control__item--text":""])},e.toDisplayString(t),7)])],14,["onClick"])))),128))],6)}],["__scopeId","data-v-409bd450"]]);const V=i({name:"UniCard",emits:["click"],props:{title:{type:String,default:""},subTitle:{type:String,default:""},padding:{type:String,default:"10px"},margin:{type:String,default:"15px"},spacing:{type:String,default:"0 10px"},extra:{type:String,default:""},cover:{type:String,default:""},thumbnail:{type:String,default:""},isFull:{type:Boolean,default:!1},isShadow:{type:Boolean,default:!0},shadow:{type:String,default:"0px 0px 3px 1px rgba(0, 0, 0, 0.08)"},border:{type:Boolean,default:!0}},methods:{onClick(e){this.$emit("click",e)}}},[["render",function(t,o,a,i,n,s){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["uni-card",{"uni-card--full":a.isFull,"uni-card--shadow":a.isShadow,"uni-card--border":a.border}]),style:e.normalizeStyle({margin:a.isFull?0:a.margin,padding:a.spacing,"box-shadow":a.isShadow?a.shadow:""})},[e.renderSlot(t.$slots,"cover",{},(()=>[a.cover?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uni-card__cover"},[e.createElementVNode("image",{class:"uni-card__cover-image",mode:"widthFix",onClick:o[0]||(o[0]=e=>s.onClick("cover")),src:a.cover},null,8,["src"])])):e.createCommentVNode("",!0)]),!0),e.renderSlot(t.$slots,"title",{},(()=>[a.title||a.extra?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uni-card__header"},[e.createElementVNode("view",{class:"uni-card__header-box",onClick:o[1]||(o[1]=e=>s.onClick("title"))},[a.thumbnail?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uni-card__header-avatar"},[e.createElementVNode("image",{class:"uni-card__header-avatar-image",src:a.thumbnail,mode:"aspectFit"},null,8,["src"])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"uni-card__header-content"},[e.createElementVNode("text",{class:"uni-card__header-content-title uni-ellipsis"},e.toDisplayString(a.title),1),a.title&&a.subTitle?(e.openBlock(),e.createElementBlock("text",{key:0,class:"uni-card__header-content-subtitle uni-ellipsis"},e.toDisplayString(a.subTitle),1)):e.createCommentVNode("",!0)])]),e.createElementVNode("view",{class:"uni-card__header-extra",onClick:o[2]||(o[2]=e=>s.onClick("extra"))},[e.createElementVNode("text",{class:"uni-card__header-extra-text"},e.toDisplayString(a.extra),1)])])):e.createCommentVNode("",!0)]),!0),e.createElementVNode("view",{class:"uni-card__content",style:e.normalizeStyle({padding:a.padding}),onClick:o[3]||(o[3]=e=>s.onClick("content"))},[e.renderSlot(t.$slots,"default",{},void 0,!0)],4),e.createElementVNode("view",{class:"uni-card__actions",onClick:o[4]||(o[4]=e=>s.onClick("actions"))},[e.renderSlot(t.$slots,"actions",{},void 0,!0)])],6)}],["__scopeId","data-v-a4d554f5"]]);const E=i({data:()=>({deliveryList:[],userInfo:null,delivererInfo:null,timer:null,location:{latitude:39.9087,longitude:116.3974},currentTaskTab:1,taskTabs:["待配送","配送中","已完成"]}),onLoad(){t("log","at pages/index/index.vue:128","首页加载"),setTimeout((()=>{this.initPage()}),100)},onShow(){this.delivererInfo&&this.loadDeliveryTasks(!1)},onUnload(){this.timer&&clearInterval(this.timer)},methods:{initPage(){try{const e=uni.getStorageSync("userInfo"),o=uni.getStorageSync("delivererInfo"),a=uni.getStorageSync("token");if(!a||!e&&!o)return t("error","at pages/index/index.vue:157","登录信息缺失，跳转到登录页"),void this.redirectToLogin("登录信息已过期，请重新登录");o&&(this.delivererInfo=JSON.parse(o)),e&&(this.userInfo=JSON.parse(e)),a&&!getApp().globalData.token&&(getApp().globalData.token=a,getApp().globalData.isLoggedIn=!0,getApp().globalData.requestHeader={Authorization:"Bearer "+a,"Content-Type":"application/json",Accept:"application/json"}),this.loadDeliveryTasks(),this.timer&&clearInterval(this.timer),this.timer=setInterval((()=>{this.updateLocation(),this.loadDeliveryTasks(!1)}),6e4),this.getLocation()}catch(e){t("error","at pages/index/index.vue:196","首页加载异常",e),uni.showToast({title:"页面加载异常，请稍后重试",icon:"none",duration:3e3})}},getLocation(){uni.getLocation({type:"gcj02",success:e=>{this.location.latitude=e.latitude,this.location.longitude=e.longitude,this.updateLocation()},fail:e=>{t("error","at pages/index/index.vue:215","获取位置失败",e),uni.showToast({title:"获取位置失败，请开启定位权限",icon:"none"})}})},updateLocation(){const e=uni.getStorageSync("token"),o=uni.getStorageSync("employeeInfo");if(!e||!o)return;let a;try{a="string"==typeof o?JSON.parse(o):o}catch(i){return void t("error","at pages/index/index.vue:233","解析员工信息失败",i)}a&&a.id?uni.request({url:getApp().globalData.BASE_API+"/api/deliverers/update-location",method:"POST",header:{Authorization:"Bearer "+e,"Content-Type":"application/json",Accept:"application/json"},data:{employee_id:a.id,latitude:this.location.latitude,longitude:this.location.longitude},success:e=>{e.data&&200===e.data.code&&t("log","at pages/index/index.vue:257","位置更新成功")},fail:e=>{t("error","at pages/index/index.vue:261","更新位置失败",e)}}):t("error","at pages/index/index.vue:238","员工信息不完整")},loadDeliveryTasks(e=!0){e&&uni.showLoading({title:"加载中..."});const o=getApp().globalData.token||uni.getStorageSync("token");if(!o)return e&&uni.hideLoading(),void this.redirectToLogin("登录状态已失效，请重新登录");let a="";switch(this.currentTaskTab){case 0:a="pending";break;case 1:a="in_progress";break;case 2:a="completed"}uni.request({url:getApp().globalData.BASE_API+"/api/deliveries",method:"GET",header:{Authorization:"Bearer "+o,Accept:"application/json"},data:{status:a},success:o=>{if(200===o.statusCode&&o.data)if(200===o.data.code||o.data.success){t("log","at pages/index/index.vue:308","获取任务成功",o.data);let e=null;(o.data.data||o.data.success)&&(e=o.data.data),this.deliveryList=e&&e.data?e.data:e||[],this.deliveryList.forEach((e=>{e.hasOwnProperty("showItemsDetail")||(e.showItemsDetail=!1)})),t("log","at pages/index/index.vue:329","处理后的任务列表:",this.deliveryList),this.deliveryList.length>0&&(t("log","at pages/index/index.vue:333","第一个任务的数据结构:",this.deliveryList[0]),t("log","at pages/index/index.vue:334","第一个任务的order数据:",this.deliveryList[0].order),this.deliveryList[0].order&&(t("log","at pages/index/index.vue:336","订单号:",this.deliveryList[0].order.order_no),t("log","at pages/index/index.vue:337","收货人:",this.deliveryList[0].order.contact_name),t("log","at pages/index/index.vue:338","电话:",this.deliveryList[0].order.contact_phone),t("log","at pages/index/index.vue:339","地址:",this.deliveryList[0].order.shipping_address),t("log","at pages/index/index.vue:340","用户信息:",this.deliveryList[0].order.user),this.deliveryList[0].order.user&&(t("log","at pages/index/index.vue:342","用户名:",this.deliveryList[0].order.user.name),t("log","at pages/index/index.vue:343","商家名称:",this.deliveryList[0].order.user.merchant_name))))}else 401===o.statusCode?(t("error","at pages/index/index.vue:350","获取任务失败 - 授权失效",o),this.redirectToLogin("登录状态已过期，请重新登录")):(t("error","at pages/index/index.vue:355","获取任务失败",o),this.deliveryList=[],e&&uni.showToast({title:o.data&&o.data.message||"获取任务失败",icon:"none",duration:2e3}));else t("error","at pages/index/index.vue:368","响应状态码或数据无效",o),this.deliveryList=[],e&&uni.showToast({title:"获取数据失败",icon:"none",duration:2e3})},fail:o=>{t("error","at pages/index/index.vue:381","请求获取任务失败",o),e&&uni.showToast({title:"网络错误，请稍后重试",icon:"none",duration:2e3})},complete:()=>{e&&uni.hideLoading()}})},formatTime(e){if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`},startDelivery(e){uni.showLoading({title:"处理中..."});const o=uni.getStorageSync("token");uni.request({url:`${getApp().globalData.BASE_API}/api/deliveries/${e}/status`,method:"PUT",header:{Authorization:"Bearer "+o,Accept:"application/json"},data:{status:"in_progress"},success:e=>{var t;e.data&&e.data.success||e.data&&200===e.data.code?(uni.showToast({title:"已开始配送",icon:"success"}),this.loadDeliveryTasks()):uni.showToast({title:(null==(t=e.data)?void 0:t.message)||"操作失败",icon:"none"})},fail:e=>{t("error","at pages/index/index.vue:436","开始配送失败",e),uni.showToast({title:"网络错误，请稍后重试",icon:"none"})},complete:()=>{uni.hideLoading()}})},completeDelivery(e){uni.showLoading({title:"操作中..."});const o=uni.getStorageSync("token");uni.request({url:`${getApp().globalData.BASE_API}/api/deliveries/${e}/status`,method:"PUT",header:{Authorization:"Bearer "+o,Accept:"application/json"},data:{status:"completed"},success:e=>{var t;e.data&&e.data.success||e.data&&200===e.data.code?(uni.showToast({title:"配送已完成",icon:"success"}),this.loadDeliveryTasks()):uni.showToast({title:(null==(t=e.data)?void 0:t.message)||"操作失败",icon:"none"})},fail:e=>{t("error","at pages/index/index.vue:480","完成配送失败",e),uni.showToast({title:"网络错误，请稍后重试",icon:"none"})},complete:()=>{uni.hideLoading()}})},callCustomer(e){if(!e)return void uni.showToast({title:"电话号码无效",icon:"none"});/^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$|^400-?\d{3}-?\d{4}$/.test(e.replace(/\s+/g,""))?this.makeCall(e):uni.showModal({title:"电话号码格式提示",content:`电话号码：${e}\n格式可能不正确，是否继续拨打？`,success:t=>{t.confirm&&this.makeCall(e)}})},makeCall(e){t("log","at pages/index/index.vue:519","准备拨打电话:",e),uni.makePhoneCall({phoneNumber:e,success:()=>{t("log","at pages/index/index.vue:524","电话拨打成功")},fail:o=>{t("error","at pages/index/index.vue:527","拨打电话失败:",o),uni.showModal({title:"拨打电话失败",content:`无法拨打电话：${e}\n请检查设备是否支持通话功能或手动拨打`,showCancel:!0,cancelText:"取消",confirmText:"复制号码",success:t=>{t.confirm&&uni.setClipboardData({data:e,success:()=>{uni.showToast({title:"电话号码已复制",icon:"success"})}})}})}})},navigate(e){e?uni.showActionSheet({itemList:["高德地图","百度地图","腾讯地图","苹果地图(iOS)","谷歌地图"],success:t=>{const o=t.tapIndex;this.openNavigation(e,o)},fail:()=>{this.openNavigation(e,0)}}):uni.showToast({title:"地址无效",icon:"none"})},openNavigation(e,o=0){const a=this.location.latitude,i=this.location.longitude,n=encodeURIComponent(e);let s="",l="";switch(o){case 0:s=`amapuri://route/plan/?sid=BGVIS1&slat=${a}&slon=${i}&sname=当前位置&did=BGVIS2&dlat=&dlon=&dname=${n}&dev=0&t=0`,l="高德地图";break;case 1:s=`baidumap://map/direction?origin=${a},${i}&destination=${n}&mode=driving&src=配送助手`,l="百度地图";break;case 2:s=`qqmap://map/routeplan?type=drive&from=当前位置&fromcoord=${a},${i}&to=${n}&tocoord=&referer=配送助手`,l="腾讯地图";break;case 3:s=`http://maps.apple.com/?saddr=${a},${i}&daddr=${n}&dirflg=d`,l="苹果地图";break;case 4:s=`comgooglemaps://?saddr=${a},${i}&daddr=${n}&directionsmode=driving`,l="谷歌地图"}t("log","at pages/index/index.vue:612",`尝试打开${l}:`,s),plus.runtime.openURL(s,(a=>{t("error","at pages/index/index.vue:616",`打开${l}失败:`,a),this.openWebNavigation(e,o,l)}))},openWebNavigation(e,o,a){const i=encodeURIComponent(e),n=this.location.latitude,s=this.location.longitude;let l="";switch(o){case 0:l=`https://uri.amap.com/navigation?from=${s},${n},当前位置&to=,${i}&mode=car&policy=1&src=配送助手&coordinate=gaode&callnative=1`;break;case 1:l=`https://api.map.baidu.com/direction?origin=${n},${s}&destination=${i}&mode=driving&region=全国&output=html&src=配送助手`;break;case 2:l=`https://apis.map.qq.com/uri/v1/routeplan?type=drive&from=当前位置&fromcoord=${n},${s}&to=${i}&referer=配送助手`;break;case 3:l=`https://maps.apple.com/?saddr=${n},${s}&daddr=${i}&dirflg=d`;break;case 4:l=`https://www.google.com/maps/dir/${n},${s}/${i}`}t("log","at pages/index/index.vue:651",`${a}应用未安装，尝试打开网页版:`,l),plus.runtime.openURL(l,(o=>{t("error","at pages/index/index.vue:655",`打开${a}网页版失败:`,o),this.fallbackNavigation(e,a)}))},fallbackNavigation(e,o){t("log","at pages/index/index.vue:662","所有导航方案失败，使用内置地图"),uni.openLocation({latitude:this.location.latitude,longitude:this.location.longitude,name:e,address:e,scale:15,success:()=>{t("log","at pages/index/index.vue:672","打开内置地图成功"),uni.showToast({title:"已打开内置地图",icon:"success"})},fail:a=>{t("error","at pages/index/index.vue:679","打开内置地图失败:",a),uni.showModal({title:"导航提示",content:`无法自动打开导航，请手动使用${o||"地图应用"}导航到：\n${e}`,showCancel:!1,confirmText:"知道了"})}})},redirectToLogin(e){try{this.timer&&(clearInterval(this.timer),this.timer=null),getApp().globalData.isLoggedIn=!1,getApp().globalData.token=null,uni.removeStorageSync("isLoggedIn"),uni.reLaunch({url:"/pages/login/login",success:t=>{t.eventChannel&&t.eventChannel.emit("loginError",{error:e||"登录已过期，请重新登录"})}})}catch(o){t("error","at pages/index/index.vue:715","跳转到登录页出错",o),uni.reLaunch({url:"/pages/login/login"})}},toggleItemsDetail(e){this.deliveryList[e].showItemsDetail=!this.deliveryList[e].showItemsDetail},getPaymentMethodText(e){switch(e){case"wechat":return"微信支付";case"alipay":return"支付宝支付";case"cash":return"现金支付";case"card":return"银行卡支付";default:return"未知支付方式"}},getTotalQuantity:e=>e&&Array.isArray(e)?e.reduce(((e,t)=>e+parseInt(t.quantity||0)),0):0,onTaskTabChange(e){this.currentTaskTab=e.currentIndex,this.loadDeliveryTasks()},getStatusText(e){switch(e){case"pending":return"待配送";case"in_progress":return"配送中";case"completed":return"已完成";default:return"未知状态"}}}},[["render",function(t,a,i,s,l,r){const c=o(e.resolveDynamicComponent("uni-segmented-control"),C),d=o(e.resolveDynamicComponent("uni-icons"),n),u=o(e.resolveDynamicComponent("uni-card"),V);return e.openBlock(),e.createElementBlock("view",{class:"delivery-container"},[e.createElementVNode("view",{class:"fixed-header"},[e.createVNode(c,{current:l.currentTaskTab,values:l.taskTabs,onClickItem:r.onTaskTabChange},null,8,["current","values","onClickItem"])]),e.createElementVNode("scroll-view",{class:"scroll-content","scroll-y":"true"},[0===l.deliveryList.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-tip"},[e.createVNode(d,{type:"info",size:"50",color:"#ccc"}),e.createElementVNode("text",null,"暂无"+e.toDisplayString(l.taskTabs[l.currentTaskTab])+"任务",1)])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"delivery-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.deliveryList,((t,o)=>{var a;return e.openBlock(),e.createElementBlock("view",{key:o,class:"task-card"},[e.createVNode(u,{title:"订单号: "+((null==(a=t.order)?void 0:a.order_no)||"undefined"),extra:r.formatTime(t.created_at)},{default:e.withCtx((()=>{var a,i,n,s,l,c,u,m,p,g;return[e.createElementVNode("view",{class:"delivery-info"},[e.createElementVNode("view",{class:"info-row"},[e.createElementVNode("text",{class:"label"},"收货人:"),e.createElementVNode("text",{class:"value"},e.toDisplayString((null==(a=t.order)?void 0:a.contact_name)||""),1),(null==(n=null==(i=t.order)?void 0:i.user)?void 0:n.merchant_name)?(e.openBlock(),e.createElementBlock("text",{key:0,class:"merchant-name"},"("+e.toDisplayString(t.order.user.merchant_name)+")",1)):(null==(l=null==(s=t.order)?void 0:s.user)?void 0:l.name)?(e.openBlock(),e.createElementBlock("text",{key:1,class:"merchant-name"},"("+e.toDisplayString(t.order.user.name)+")",1)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"info-row"},[e.createElementVNode("text",{class:"label"},"电话:"),e.createElementVNode("text",{class:"value"},e.toDisplayString((null==(c=t.order)?void 0:c.contact_phone)||""),1)]),e.createElementVNode("view",{class:"info-row"},[e.createElementVNode("text",{class:"label"},"地址:"),e.createElementVNode("text",{class:"value address"},e.toDisplayString((null==(u=t.order)?void 0:u.shipping_address)||""),1)]),e.createElementVNode("view",{class:"info-row"},[e.createElementVNode("text",{class:"label"},"状态:"),e.createElementVNode("text",{class:e.normalizeClass(["value status",t.status])},e.toDisplayString(r.getStatusText(t.status)),3)]),(null==(m=t.order)?void 0:m.items)&&t.order.items.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"order-items-section"},[e.createElementVNode("view",{class:"section-title",onClick:e=>r.toggleItemsDetail(o)},[e.createElementVNode("text",null,"商品清单 ("+e.toDisplayString(t.order.items.length)+"种商品)",1),e.createElementVNode("view",{class:"detail-btn"},[e.createElementVNode("text",{class:"detail-text"},e.toDisplayString(t.showItemsDetail?"收起":"详情"),1),e.createVNode(d,{type:t.showItemsDetail?"up":"down",size:"16",color:"#007AFF"},null,8,["type"])])],8,["onClick"]),e.createElementVNode("view",{class:"items-summary"},[e.createElementVNode("view",{class:"summary-item"},[e.createElementVNode("text",{class:"summary-label"},"商品总数:"),e.createElementVNode("text",{class:"summary-value"},e.toDisplayString(r.getTotalQuantity(t.order.items))+" 件",1)]),e.createElementVNode("view",{class:"summary-item"},[e.createElementVNode("text",{class:"summary-label"},"订单金额:"),e.createElementVNode("text",{class:"summary-value amount"},"¥"+e.toDisplayString(parseFloat((null==(p=t.order)?void 0:p.total)||0).toFixed(2)),1)]),e.createElementVNode("view",{class:"summary-item"},[e.createElementVNode("text",{class:"summary-label"},"支付方式:"),e.createElementVNode("text",{class:"summary-value"},e.toDisplayString(r.getPaymentMethodText(null==(g=t.order)?void 0:g.payment_method)),1)])]),t.showItemsDetail?(e.openBlock(),e.createElementBlock("view",{key:0,class:"items-detail"},[e.createElementVNode("view",{class:"items-header"},[e.createElementVNode("text",{class:"header-name"},"商品名称"),e.createElementVNode("text",{class:"header-qty"},"数量"),e.createElementVNode("text",{class:"header-price"},"单价"),e.createElementVNode("text",{class:"header-total"},"小计")]),e.createElementVNode("view",{class:"items-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.order.items,((t,o)=>(e.openBlock(),e.createElementBlock("view",{class:"item-row",key:t.id},[e.createElementVNode("view",{class:"item-name"},[e.createElementVNode("text",{class:"product-name"},e.toDisplayString(t.product_name),1),t.product_sku?(e.openBlock(),e.createElementBlock("text",{key:0,class:"product-sku"},e.toDisplayString(t.product_sku),1)):e.createCommentVNode("",!0)]),e.createElementVNode("text",{class:"item-qty"},e.toDisplayString(t.quantity)+e.toDisplayString(t.unit||"件"),1),e.createElementVNode("text",{class:"item-price"},"¥"+e.toDisplayString(parseFloat(t.price).toFixed(2)),1),e.createElementVNode("text",{class:"item-total"},"¥"+e.toDisplayString(parseFloat(t.total).toFixed(2)),1)])))),128))])])):e.createCommentVNode("",!0)])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-items"},[e.createElementVNode("text",null,"暂无商品信息")]))]),e.createElementVNode("view",{class:"card-actions"},["pending"===t.status?(e.openBlock(),e.createElementBlock("button",{key:0,class:"action-btn accept",onClick:e.withModifiers((e=>r.startDelivery(t.id)),["stop"])},"开始配送",8,["onClick"])):e.createCommentVNode("",!0),"in_progress"===t.status?(e.openBlock(),e.createElementBlock("button",{key:1,class:"action-btn complete",onClick:e.withModifiers((e=>r.completeDelivery(t.id)),["stop"])},"完成",8,["onClick"])):e.createCommentVNode("",!0),e.createElementVNode("button",{class:"action-btn call",onClick:e.withModifiers((e=>{var o;return r.callCustomer(null==(o=t.order)?void 0:o.contact_phone)}),["stop"])},"电话",8,["onClick"]),e.createElementVNode("button",{class:"action-btn navigate",onClick:e.withModifiers((e=>{var o;return r.navigate(null==(o=t.order)?void 0:o.shipping_address)}),["stop"])},"导航",8,["onClick"])])]})),_:2},1032,["title","extra"])])})),128))]))])])}]]);const B=i({name:"UniBadge",emits:["click"],props:{type:{type:String,default:"error"},inverted:{type:Boolean,default:!1},isDot:{type:Boolean,default:!1},maxNum:{type:Number,default:99},absolute:{type:String,default:""},offset:{type:Array,default:()=>[0,0]},text:{type:[String,Number],default:""},size:{type:String,default:"small"},customStyle:{type:Object,default:()=>({})}},data:()=>({}),computed:{width(){return 8*String(this.text).length+12},classNames(){const{inverted:e,type:t,size:o,absolute:a}=this;return[e?"uni-badge--"+t+"-inverted":"","uni-badge--"+t,"uni-badge--"+o,a?"uni-badge--absolute":""].join(" ")},positionStyle(){if(!this.absolute)return{};let e=this.width/2,t=10;this.isDot&&(e=5,t=5);const o=`${-e+this.offset[0]}px`,a=`${-t+this.offset[1]}px`,i={rightTop:{right:o,top:a},rightBottom:{right:o,bottom:a},leftBottom:{left:o,bottom:a},leftTop:{left:o,top:a}},n=i[this.absolute];return n||i.rightTop},dotStyle(){return this.isDot?{width:"10px",minWidth:"0",height:"10px",padding:"0",borderRadius:"10px"}:{}},displayValue(){const{isDot:e,text:t,maxNum:o}=this;return e?"":Number(t)>o?`${o}+`:t}},methods:{onClick(){this.$emit("click")}}},[["render",function(t,o,a,i,n,s){return e.openBlock(),e.createElementBlock("view",{class:"uni-badge--x"},[e.renderSlot(t.$slots,"default",{},void 0,!0),a.text?(e.openBlock(),e.createElementBlock("text",{key:0,class:e.normalizeClass([s.classNames,"uni-badge"]),style:e.normalizeStyle([s.positionStyle,a.customStyle,s.dotStyle]),onClick:o[0]||(o[0]=e=>s.onClick())},e.toDisplayString(s.displayValue),7)):e.createCommentVNode("",!0)])}],["__scopeId","data-v-bca7b097"]]);const T=i({name:"UniListItem",emits:["click","switchChange"],props:{direction:{type:String,default:"row"},title:{type:String,default:""},note:{type:String,default:""},ellipsis:{type:[Number,String],default:0},disabled:{type:[Boolean,String],default:!1},clickable:{type:Boolean,default:!1},showArrow:{type:[Boolean,String],default:!1},link:{type:[Boolean,String],default:!1},to:{type:String,default:""},showBadge:{type:[Boolean,String],default:!1},showSwitch:{type:[Boolean,String],default:!1},switchChecked:{type:[Boolean,String],default:!1},badgeText:{type:String,default:""},badgeType:{type:String,default:"success"},badgeStyle:{type:Object,default:()=>({})},rightText:{type:String,default:""},thumb:{type:String,default:""},thumbSize:{type:String,default:"base"},showExtraIcon:{type:[Boolean,String],default:!1},extraIcon:{type:Object,default:()=>({type:"",color:"#000000",size:20,customPrefix:""})},border:{type:Boolean,default:!0},customStyle:{type:Object,default:()=>({padding:"",backgroundColor:"#FFFFFF"})},keepScrollPosition:{type:Boolean,default:!1}},watch:{"customStyle.padding":{handler(e){"number"==typeof e&&(e+="");let t=e.split(" ");if(1===t.length){const e=t[0];this.padding={top:e,right:e,bottom:e,left:e}}else if(2===t.length){const[e,o]=t;this.padding={top:e,right:o,bottom:e,left:o}}else if(4===t.length){const[e,o,a,i]=t;this.padding={top:e,right:o,bottom:a,left:i}}},immediate:!0}},data:()=>({isFirstChild:!1,padding:{top:"",right:"",bottom:"",left:""}}),mounted(){this.list=this.getForm(),this.list&&(this.list.firstChildAppend||(this.list.firstChildAppend=!0,this.isFirstChild=!0))},methods:{getForm(e="uniList"){let t=this.$parent,o=t.$options.name;for(;o!==e;){if(t=t.$parent,!t)return!1;o=t.$options.name}return t},onClick(){""===this.to?(this.clickable||this.link)&&this.$emit("click",{data:{}}):this.openPage()},onSwitchChange(e){this.$emit("switchChange",e.detail)},openPage(){-1!==["navigateTo","redirectTo","reLaunch","switchTab"].indexOf(this.link)?this.pageApi(this.link):this.pageApi("navigateTo")},pageApi(e){let t={url:this.to,success:e=>{this.$emit("click",{data:e})},fail:e=>{this.$emit("click",{data:e})}};switch(e){case"navigateTo":default:uni.navigateTo(t);break;case"redirectTo":uni.redirectTo(t);break;case"reLaunch":uni.reLaunch(t);break;case"switchTab":uni.switchTab(t)}}}},[["render",function(t,a,i,s,l,r){const c=o(e.resolveDynamicComponent("uni-icons"),n),d=o(e.resolveDynamicComponent("uni-badge"),B);return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass([{"uni-list-item--disabled":i.disabled},"uni-list-item"]),style:e.normalizeStyle({"background-color":i.customStyle.backgroundColor}),"hover-class":!i.clickable&&!i.link||i.disabled||i.showSwitch?"":"uni-list-item--hover",onClick:a[1]||(a[1]=(...e)=>r.onClick&&r.onClick(...e))},[l.isFirstChild?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["border--left",{"uni-list--border":i.border}])},null,2)),e.createElementVNode("view",{class:e.normalizeClass(["uni-list-item__container",{"container--right":i.showArrow||i.link,"flex--direction":"column"===i.direction}]),style:e.normalizeStyle({paddingTop:l.padding.top,paddingLeft:l.padding.left,paddingRight:l.padding.right,paddingBottom:l.padding.bottom})},[e.renderSlot(t.$slots,"header",{},(()=>[e.createElementVNode("view",{class:"uni-list-item__header"},[i.thumb?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uni-list-item__icon"},[e.createElementVNode("image",{src:i.thumb,class:e.normalizeClass(["uni-list-item__icon-img",["uni-list--"+i.thumbSize]])},null,10,["src"])])):i.showExtraIcon?(e.openBlock(),e.createElementBlock("view",{key:1,class:"uni-list-item__icon"},[e.createVNode(c,{customPrefix:i.extraIcon.customPrefix,color:i.extraIcon.color,size:i.extraIcon.size,type:i.extraIcon.type},null,8,["customPrefix","color","size","type"])])):e.createCommentVNode("",!0)])]),!0),e.renderSlot(t.$slots,"body",{},(()=>[e.createElementVNode("view",{class:e.normalizeClass(["uni-list-item__content",{"uni-list-item__content--center":i.thumb||i.showExtraIcon||i.showBadge||i.showSwitch}])},[i.title?(e.openBlock(),e.createElementBlock("text",{key:0,class:e.normalizeClass(["uni-list-item__content-title",[0!==i.ellipsis&&i.ellipsis<=2?"uni-ellipsis-"+i.ellipsis:""]])},e.toDisplayString(i.title),3)):e.createCommentVNode("",!0),i.note?(e.openBlock(),e.createElementBlock("text",{key:1,class:"uni-list-item__content-note"},e.toDisplayString(i.note),1)):e.createCommentVNode("",!0)],2)]),!0),e.renderSlot(t.$slots,"footer",{},(()=>[i.rightText||i.showBadge||i.showSwitch?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["uni-list-item__extra",{"flex--justify":"column"===i.direction}])},[i.rightText?(e.openBlock(),e.createElementBlock("text",{key:0,class:"uni-list-item__extra-text"},e.toDisplayString(i.rightText),1)):e.createCommentVNode("",!0),i.showBadge?(e.openBlock(),e.createBlock(d,{key:1,type:i.badgeType,text:i.badgeText,"custom-style":i.badgeStyle},null,8,["type","text","custom-style"])):e.createCommentVNode("",!0),i.showSwitch?(e.openBlock(),e.createElementBlock("switch",{key:2,disabled:i.disabled,checked:i.switchChecked,onChange:a[0]||(a[0]=(...e)=>r.onSwitchChange&&r.onSwitchChange(...e))},null,40,["disabled","checked"])):e.createCommentVNode("",!0)],2)):e.createCommentVNode("",!0)]),!0)],6),i.showArrow||i.link?(e.openBlock(),e.createBlock(c,{key:1,size:16,class:"uni-icon-wrapper",color:"#bbb",type:"arrowright"})):e.createCommentVNode("",!0)],14,["hover-class"])}],["__scopeId","data-v-380d0f47"]]);const D=i({name:"uniList","mp-weixin":{options:{multipleSlots:!1}},props:{stackFromEnd:{type:Boolean,default:!1},enableBackToTop:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},border:{type:Boolean,default:!0},renderReverse:{type:Boolean,default:!1}},created(){this.firstChildAppend=!1},methods:{loadMore(e){this.$emit("scrolltolower")},scroll(e){this.$emit("scroll",e)}}},[["render",function(t,o,a,i,n,s){return e.openBlock(),e.createElementBlock("view",{class:"uni-list uni-border-top-bottom"},[a.border?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uni-list--border-top"})):e.createCommentVNode("",!0),e.renderSlot(t.$slots,"default",{},void 0,!0),a.border?(e.openBlock(),e.createElementBlock("view",{key:1,class:"uni-list--border-bottom"})):e.createCommentVNode("",!0)])}],["__scopeId","data-v-c1d7c358"]]);const L=i({name:"UniSection",emits:["click"],props:{type:{type:String,default:""},title:{type:String,required:!0,default:""},titleFontSize:{type:String,default:"14px"},titleColor:{type:String,default:"#333"},subTitle:{type:String,default:""},subTitleFontSize:{type:String,default:"12px"},subTitleColor:{type:String,default:"#999"},padding:{type:[Boolean,String],default:!1}},computed:{_padding(){return"string"==typeof this.padding?this.padding:this.padding?"10px":""}},watch:{title(e){uni.report&&""!==e&&uni.report("title",e)}},methods:{onClick(){this.$emit("click")}}},[["render",function(t,o,a,i,n,s){return e.openBlock(),e.createElementBlock("view",{class:"uni-section"},[e.createElementVNode("view",{class:"uni-section-header",onClick:o[0]||(o[0]=(...e)=>s.onClick&&s.onClick(...e))},[a.type?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["uni-section-header__decoration",a.type])},null,2)):e.renderSlot(t.$slots,"decoration",{key:1},void 0,!0),e.createElementVNode("view",{class:"uni-section-header__content"},[e.createElementVNode("text",{style:e.normalizeStyle({"font-size":a.titleFontSize,color:a.titleColor}),class:e.normalizeClass(["uni-section__content-title",{distraction:!a.subTitle}])},e.toDisplayString(a.title),7),a.subTitle?(e.openBlock(),e.createElementBlock("text",{key:0,style:e.normalizeStyle({"font-size":a.subTitleFontSize,color:a.subTitleColor}),class:"uni-section-header__content-sub"},e.toDisplayString(a.subTitle),5)):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"uni-section-header__slot-right"},[e.renderSlot(t.$slots,"right",{},void 0,!0)])]),e.createElementVNode("view",{class:"uni-section-content",style:e.normalizeStyle({padding:s._padding})},[e.renderSlot(t.$slots,"default",{},void 0,!0)],4)])}],["__scopeId","data-v-0a8818d5"]]);const I=i({data:()=>({userInfo:null,delivererInfo:null,delivererStatus:"offline"}),computed:{userName(){var e,t;return(null==(e=this.userInfo)?void 0:e.name)?this.userInfo.name:(null==(t=this.delivererInfo)?void 0:t.name)?this.delivererInfo.name:"配送员"},userPhone(){var e,t;return(null==(e=this.userInfo)?void 0:e.phone)?this.userInfo.phone:(null==(t=this.delivererInfo)?void 0:t.phone)?this.delivererInfo.phone:"未设置联系方式"},avatarText(){return this.userName?this.userName.charAt(0):"配送员"}},onLoad(){this.loadUserInfo()},onShow(){this.loadUserInfo()},methods:{loadUserInfo(){try{const e=uni.getStorageSync("userInfo"),t=uni.getStorageSync("delivererInfo"),o=uni.getStorageSync("employeeInfo");if(e&&(this.userInfo=JSON.parse(e)),t&&(this.delivererInfo=JSON.parse(t),this.delivererStatus=this.delivererInfo.status||"available"),!this.delivererInfo&&o){const e=JSON.parse(o);this.delivererInfo={id:e.id,name:e.name,phone:e.phone,delivery_area:"未设置",transportation:"未设置",status:"available"},this.delivererStatus="available"}}catch(e){t("error","at pages/profile/profile.vue:111","读取用户信息失败:",e)}},getStatusText(e){switch(e){case"available":return"空闲中";case"busy":return"配送中";case"offline":return"休息中";default:return"未知状态"}},showAbout(){uni.showModal({title:"关于系统",content:"配送员APP v1.0.0\n为配送员提供便捷的配送管理工具",showCancel:!1})},logout(){uni.showModal({title:"退出登录",content:"确定要退出登录吗？",success:e=>{if(e.confirm){const e=uni.getStorageSync("delivererLoginConfig");let a=!1;if(e)try{a=!0===JSON.parse(e).autoLogin}catch(o){t("error","at pages/profile/profile.vue:151","解析登录配置失败:",o)}a?uni.showModal({title:"自动登录设置",content:"检测到您开启了自动登录，是否同时清除自动登录设置？",confirmText:"清除",cancelText:"保留",success:e=>{this.performLogout(e.confirm)}}):this.performLogout(!1)}}})},performLogout(e){if(uni.removeStorageSync("token"),uni.removeStorageSync("userInfo"),uni.removeStorageSync("delivererInfo"),uni.removeStorageSync("employeeInfo"),uni.removeStorageSync("isLoggedIn"),e)uni.removeStorageSync("delivererLoginConfig"),t("log","at pages/profile/profile.vue:186","已清除自动登录设置");else{const e=uni.getStorageSync("delivererLoginConfig");if(e)try{const o=JSON.parse(e);o.password="",uni.setStorageSync("delivererLoginConfig",JSON.stringify(o)),t("log","at pages/profile/profile.vue:196","已保留登录设置但清除密码")}catch(o){t("error","at pages/profile/profile.vue:198","更新登录配置失败:",o)}}getApp().globalData.isLoggedIn=!1,getApp().globalData.token="",getApp().globalData.delivererId=null,uni.showToast({title:"已退出登录",icon:"success",duration:1500}),setTimeout((()=>{uni.reLaunch({url:"/pages/login/login"})}),1500)}}},[["render",function(t,a,i,n,s,l){const r=o(e.resolveDynamicComponent("uni-list-item"),T),c=o(e.resolveDynamicComponent("uni-list"),D),d=o(e.resolveDynamicComponent("uni-section"),L);return e.openBlock(),e.createElementBlock("view",{class:"profile-container"},[e.createElementVNode("view",{class:"user-card"},[e.createElementVNode("view",{class:"avatar"},[e.createElementVNode("text",{class:"avatar-text"},e.toDisplayString(l.avatarText),1)]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("text",{class:"name"},e.toDisplayString(l.userName),1),e.createElementVNode("text",{class:"phone"},e.toDisplayString(l.userPhone),1)]),e.createElementVNode("view",{class:e.normalizeClass(["status-indicator",s.delivererStatus])},[e.createElementVNode("text",null,e.toDisplayString(l.getStatusText(s.delivererStatus)),1)],2)]),e.createVNode(d,{title:"基本信息",type:"line"},{default:e.withCtx((()=>[e.createVNode(c,null,{default:e.withCtx((()=>{var t,o,a;return[e.createVNode(r,{title:"工号",rightText:(null==(t=s.delivererInfo)?void 0:t.id)||"--"},null,8,["rightText"]),e.createVNode(r,{title:"配送区域",rightText:(null==(o=s.delivererInfo)?void 0:o.delivery_area)||"--"},null,8,["rightText"]),e.createVNode(r,{title:"运输工具",rightText:(null==(a=s.delivererInfo)?void 0:a.transportation)||"--"},null,8,["rightText"]),e.createVNode(r,{title:"工作状态",rightText:l.getStatusText(s.delivererStatus)},null,8,["rightText"])]})),_:1})])),_:1}),e.createVNode(d,{title:"系统功能",type:"line"},{default:e.withCtx((()=>[e.createVNode(c,null,{default:e.withCtx((()=>[e.createVNode(r,{title:"关于系统",showArrow:"",onClick:l.showAbout},null,8,["onClick"])])),_:1})])),_:1}),e.createElementVNode("button",{class:"logout-btn",onClick:a[0]||(a[0]=(...e)=>l.logout&&l.logout(...e))},"退出登录")])}]]);const A=i({data:()=>({currentPeriod:0,periodTabs:["今日","本周","本月"],stats:{totalOrders:0,completedOrders:0,pendingOrders:0,inProgressOrders:0,completionRate:0,avgTime:0,rating:0,ordersTrend:0,completedTrend:0,timeTrend:0,ratingTrend:0},timeDistribution:[],maxTimeCount:0,areaStats:[],maxAreaCount:0,rankingList:[]}),onLoad(){this.loadStatistics()},onShow(){this.loadStatistics()},methods:{onPeriodChange(e){this.currentPeriod=e.currentIndex,this.loadStatistics()},loadStatistics(){uni.showLoading({title:"加载中..."});const e=uni.getStorageSync("token");if(!e)return uni.hideLoading(),uni.showToast({title:"请先登录",icon:"none"}),void setTimeout((()=>{uni.redirectTo({url:"/pages/login/login"})}),1500);const o=["today","week","month"][this.currentPeriod];uni.request({url:getApp().globalData.BASE_API+"/api/deliverers/statistics",method:"GET",header:{Authorization:"Bearer "+e,Accept:"application/json"},data:{period:o},success:e=>{if(t("log","at pages/statistics/statistics.vue:252","统计API响应:",e),200===e.statusCode&&e.data&&200===e.data.code){const t=e.data.data||{};this.updateStats(t)}else 401===e.statusCode?(uni.showToast({title:"登录已过期，请重新登录",icon:"none"}),setTimeout((()=>{uni.redirectTo({url:"/pages/login/login"})}),1500)):(t("error","at pages/statistics/statistics.vue:269","获取统计数据失败",e),uni.showToast({title:"获取数据失败，显示模拟数据",icon:"none"}),this.loadMockData())},fail:e=>{t("error","at pages/statistics/statistics.vue:278","请求统计数据失败",e),uni.showToast({title:"网络请求失败，显示模拟数据",icon:"none"}),this.loadMockData()},complete:()=>{uni.hideLoading()}})},updateStats(e){this.stats={totalOrders:e.total_orders||0,completedOrders:e.completed_orders||0,pendingOrders:e.pending_orders||0,inProgressOrders:e.in_progress_orders||0,completionRate:e.completion_rate||0,avgTime:e.avg_delivery_time||0,rating:e.average_rating||0,ordersTrend:e.orders_trend||0,completedTrend:e.completed_trend||0,timeTrend:e.time_trend||0,ratingTrend:e.rating_trend||0},this.timeDistribution=e.time_distribution||this.generateMockTimeData(),this.maxTimeCount=this.timeDistribution.length>0?Math.max(...this.timeDistribution.map((e=>e.count))):1,this.areaStats=e.area_stats||this.generateMockAreaData(),this.maxAreaCount=this.areaStats.length>0?Math.max(...this.areaStats.map((e=>e.count))):1,this.rankingList=e.ranking||this.generateMockRankingData(),t("log","at pages/statistics/statistics.vue:319","统计数据更新完成:",{stats:this.stats,timeDistribution:this.timeDistribution,areaStats:this.areaStats,ranking:this.rankingList})},loadMockData(){this.updateStats({total_orders:45,completed_orders:38,pending_orders:3,in_progress_orders:4,completion_rate:84.4,avg_delivery_time:25,average_rating:4.8,orders_trend:12.5,completed_trend:8.3,time_trend:-5.2,rating_trend:2.1})},generateMockTimeData:()=>[8,9,10,11,12,13,14,15,16,17,18,19,20].map((e=>({hour:e,count:Math.floor(8*Math.random())+1}))),generateMockAreaData:()=>[{name:"朝阳区",count:15},{name:"海淀区",count:12},{name:"西城区",count:8},{name:"东城区",count:6},{name:"丰台区",count:4}],generateMockRankingData:()=>[{name:"张三",completedOrders:52,completionRate:92.8,isCurrentUser:!1},{name:"李四",completedOrders:48,completionRate:89.6,isCurrentUser:!0},{name:"王五",completedOrders:45,completionRate:87.2,isCurrentUser:!1},{name:"赵六",completedOrders:42,completionRate:85.1,isCurrentUser:!1},{name:"钱七",completedOrders:38,completionRate:82.3,isCurrentUser:!1}],getTrendClass:e=>e>0?"trend-up":e<0?"trend-down":"trend-stable",formatTrend:e=>0===e?"0%":(e>0?"+":"")+e.toFixed(1)+"%",getCircleStyle(){const e=this.stats.completionRate,t=2*Math.PI*45;return{"stroke-dasharray":t,"stroke-dashoffset":t-e/100*t}},getPeriodText(){return["今日","本周","本月"][this.currentPeriod]}}},[["render",function(t,a,i,s,l,r){const c=o(e.resolveDynamicComponent("uni-segmented-control"),C),d=o(e.resolveDynamicComponent("uni-icons"),n),u=o(e.resolveDynamicComponent("uni-card"),V);return e.openBlock(),e.createElementBlock("view",{class:"statistics-container"},[e.createElementVNode("view",{class:"time-selector"},[e.createVNode(c,{current:l.currentPeriod,values:l.periodTabs,onClickItem:r.onPeriodChange},null,8,["current","values","onClickItem"])]),e.createElementVNode("view",{class:"stats-cards"},[e.createElementVNode("view",{class:"stats-card primary"},[e.createElementVNode("view",{class:"card-icon"},[e.createVNode(d,{type:"list",size:"24",color:"#fff"})]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-number"},e.toDisplayString(l.stats.totalOrders),1),e.createElementVNode("text",{class:"card-label"},"总订单数"),e.createElementVNode("text",{class:e.normalizeClass(["card-trend",r.getTrendClass(l.stats.ordersTrend)])},e.toDisplayString(r.formatTrend(l.stats.ordersTrend)),3)])]),e.createElementVNode("view",{class:"stats-card success"},[e.createElementVNode("view",{class:"card-icon"},[e.createVNode(d,{type:"checkmarkempty",size:"24",color:"#fff"})]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-number"},e.toDisplayString(l.stats.completedOrders),1),e.createElementVNode("text",{class:"card-label"},"已完成"),e.createElementVNode("text",{class:e.normalizeClass(["card-trend",r.getTrendClass(l.stats.completedTrend)])},e.toDisplayString(r.formatTrend(l.stats.completedTrend)),3)])]),e.createElementVNode("view",{class:"stats-card warning"},[e.createElementVNode("view",{class:"card-icon"},[e.createVNode(d,{type:"clock",size:"24",color:"#fff"})]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-number"},e.toDisplayString(l.stats.avgTime)+"min",1),e.createElementVNode("text",{class:"card-label"},"平均用时"),e.createElementVNode("text",{class:e.normalizeClass(["card-trend",r.getTrendClass(-l.stats.timeTrend)])},e.toDisplayString(r.formatTrend(-l.stats.timeTrend)),3)])]),e.createElementVNode("view",{class:"stats-card info"},[e.createElementVNode("view",{class:"card-icon"},[e.createVNode(d,{type:"star",size:"24",color:"#fff"})]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-number"},e.toDisplayString(l.stats.rating),1),e.createElementVNode("text",{class:"card-label"},"评分"),e.createElementVNode("text",{class:e.normalizeClass(["card-trend",r.getTrendClass(l.stats.ratingTrend)])},e.toDisplayString(r.formatTrend(l.stats.ratingTrend)),3)])])]),e.createElementVNode("view",{class:"completion-section"},[e.createVNode(u,{title:"完成率统计","is-shadow":!1},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"completion-chart"},[e.createElementVNode("view",{class:"chart-circle"},[e.createElementVNode("view",{class:"circle-progress",style:e.normalizeStyle(r.getCircleStyle())},[e.createElementVNode("view",{class:"circle-inner"},[e.createElementVNode("text",{class:"completion-rate"},e.toDisplayString(l.stats.completionRate)+"%",1),e.createElementVNode("text",{class:"completion-label"},"完成率")])],4)]),e.createElementVNode("view",{class:"completion-details"},[e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("view",{class:"detail-dot pending"}),e.createElementVNode("text",{class:"detail-label"},"待配送"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(l.stats.pendingOrders),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("view",{class:"detail-dot progress"}),e.createElementVNode("text",{class:"detail-label"},"配送中"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(l.stats.inProgressOrders),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("view",{class:"detail-dot completed"}),e.createElementVNode("text",{class:"detail-label"},"已完成"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(l.stats.completedOrders),1)])])])])),_:1})]),e.createElementVNode("view",{class:"time-analysis"},[e.createVNode(u,{title:"配送时段分析","is-shadow":!1},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"time-chart"},[e.createElementVNode("view",{class:"time-bars"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.timeDistribution,((t,o)=>(e.openBlock(),e.createElementBlock("view",{key:o,class:"time-bar-item"},[e.createElementVNode("view",{class:"time-bar"},[e.createElementVNode("view",{class:"time-bar-fill",style:e.normalizeStyle({height:t.count/l.maxTimeCount*100+"%"})},null,4)]),e.createElementVNode("text",{class:"time-label"},e.toDisplayString(t.hour)+":00",1),e.createElementVNode("text",{class:"time-count"},e.toDisplayString(t.count),1)])))),128))])])])),_:1})]),e.createElementVNode("view",{class:"area-stats"},[e.createVNode(u,{title:"配送区域统计","is-shadow":!1},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"area-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.areaStats,((t,o)=>(e.openBlock(),e.createElementBlock("view",{key:o,class:"area-item"},[e.createElementVNode("view",{class:"area-info"},[e.createElementVNode("text",{class:"area-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"area-count"},e.toDisplayString(t.count)+"单",1)]),e.createElementVNode("view",{class:"area-progress"},[e.createElementVNode("view",{class:"area-progress-fill",style:e.normalizeStyle({width:t.count/l.maxAreaCount*100+"%"})},null,4)]),e.createElementVNode("text",{class:"area-percentage"},e.toDisplayString((t.count/l.stats.totalOrders*100).toFixed(1))+"%",1)])))),128))])])),_:1})]),e.createElementVNode("view",{class:"ranking-section"},[e.createVNode(u,{title:"团队排名","is-shadow":!1},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"ranking-list"},[e.createElementVNode("view",{class:"ranking-header"},[e.createElementVNode("text",{class:"rank-col"},"排名"),e.createElementVNode("text",{class:"name-col"},"配送员"),e.createElementVNode("text",{class:"orders-col"},"完成量"),e.createElementVNode("text",{class:"rate-col"},"完成率")]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.rankingList,((t,o)=>(e.openBlock(),e.createElementBlock("view",{key:o,class:e.normalizeClass(["ranking-item",{"current-user":t.isCurrentUser}])},[e.createElementVNode("view",{class:"rank-col"},[o<3?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["rank-medal","medal-"+(o+1)])},e.toDisplayString(o+1),3)):(e.openBlock(),e.createElementBlock("text",{key:1,class:"rank-number"},e.toDisplayString(o+1),1))]),e.createElementVNode("view",{class:"name-col"},[e.createElementVNode("text",{class:"deliverer-name"},e.toDisplayString(t.name),1),t.isCurrentUser?(e.openBlock(),e.createElementBlock("text",{key:0,class:"current-tag"},"我")):e.createCommentVNode("",!0)]),e.createElementVNode("text",{class:"orders-col"},e.toDisplayString(t.completedOrders),1),e.createElementVNode("text",{class:"rate-col"},e.toDisplayString(t.completionRate)+"%",1)],2)))),128))])])),_:1})])])}]]);__definePage("pages/login/login",N),__definePage("pages/index/index",E),__definePage("pages/profile/profile",I),__definePage("pages/statistics/statistics",A);const $=new class{constructor(){this.baseURL="",this.defaultHeaders={"Content-Type":"application/json",Accept:"application/json"}}init(){this.baseURL=getApp().globalData.BASE_API}getHeaders(e={}){const t=uni.getStorageSync("token"),o={...this.defaultHeaders,...e};return t&&(o.Authorization="Bearer "+t),o}request(e){return new Promise(((t,o)=>{const{url:a,method:i="GET",data:n={},headers:s={},showLoading:l=!1,loadingText:r="请求中..."}=e;l&&uni.showLoading({title:r}),uni.request({url:this.baseURL+a,method:i,data:n,header:this.getHeaders(s),success:e=>{var a;l&&uni.hideLoading(),200===e.statusCode?e.data&&200===e.data.code||e.data&&e.data.success?t(e.data):o(new Error((null==(a=e.data)?void 0:a.message)||"请求失败")):401===e.statusCode?(this.handleUnauthorized(),o(new Error("登录已过期，请重新登录"))):o(new Error(`请求失败: ${e.statusCode}`))},fail:e=>{l&&uni.hideLoading(),o(e)}})}))}handleUnauthorized(){uni.removeStorageSync("token"),uni.removeStorageSync("isLoggedIn"),uni.removeStorageSync("userInfo"),uni.removeStorageSync("delivererInfo"),uni.removeStorageSync("employeeInfo"),getApp().globalData.token="",getApp().globalData.isLoggedIn=!1,getApp().globalData.delivererId=null,uni.reLaunch({url:"/pages/login/login"})}get(e,t={},o={}){const a=Object.keys(t).length>0?"?"+Object.keys(t).map((e=>`${e}=${encodeURIComponent(t[e])}`)).join("&"):"";return this.request({url:e+a,method:"GET",...o})}post(e,t={},o={}){return this.request({url:e,method:"POST",data:t,...o})}put(e,t={},o={}){return this.request({url:e,method:"PUT",data:t,...o})}delete(e,t={}){return this.request({url:e,method:"DELETE",...t})}upload(e,t,o="file",a={}){return new Promise(((i,n)=>{uni.showLoading({title:"上传中..."}),uni.uploadFile({url:this.baseURL+e,filePath:t,name:o,formData:a,header:this.getHeaders(),success:e=>{uni.hideLoading();try{const t=JSON.parse(e.data);200===t.code?i(t):n(new Error(t.message||"上传失败"))}catch(t){n(new Error("响应数据解析失败"))}},fail:e=>{uni.hideLoading(),n(e)}})}))}},F={globalData:{BASE_API:"http://n8n.cdtxsp.com",token:"",isLoggedIn:!1,delivererId:null,requestHeader:{"Content-Type":"application/json",Accept:"application/json"}},onLaunch:function(){t("log","at App.vue:16","App Launch"),$.init(),this.checkLoginStatus()},onShow:function(){t("log","at App.vue:27","App Show")},onHide:function(){t("log","at App.vue:30","App Hide")},methods:{checkLoginStatus(){const e=uni.getStorageSync("token"),o=uni.getStorageSync("isLoggedIn");if(!e)return this.globalData.isLoggedIn=!1,this.globalData.token="",void uni.setStorageSync("isLoggedIn","false");this.globalData.token=e,this.globalData.isLoggedIn="true"===o,"true"!==o&&e&&uni.setStorageSync("isLoggedIn","true"),this.globalData.requestHeader={Authorization:"Bearer "+e,"Content-Type":"application/json",Accept:"application/json"};const a=uni.getStorageSync("delivererInfo");if(a)try{const e=JSON.parse(a);this.globalData.delivererId=e.id}catch(i){t("error","at App.vue:68","解析配送员信息失败",i)}}}};const{app:z,Vuex:M,Pinia:P}={app:e.createVueApp(F)};uni.Vuex=M,uni.Pinia=P,z.provide("__globalStyles",__uniConfig.styles),z._component.mpType="app",z._component.render=()=>{},z.mount("#app")}(Vue);
