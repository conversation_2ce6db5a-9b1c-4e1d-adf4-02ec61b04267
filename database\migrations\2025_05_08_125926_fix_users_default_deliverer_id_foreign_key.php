<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * 修复users表中default_deliverer_id外键关系
     * 将外键引用从users表自身改为deliverers表
     */
    public function up(): void
    {
        // 使用原始SQL语句直接删除外键约束，避免点号导致的语法问题
        try {
            // 使用不同的方式尝试删除外键
            // 方法1：尝试使用不带点号的约束名
            DB::statement('ALTER TABLE `users` DROP FOREIGN KEY `FK_users_tianxin_db_users`');
        } catch (\Exception $e) {
            // 如果方法1失败，尝试方法2（如果已经成功删除，这里会忽略）
            try {
                // 获取与default_deliverer_id相关的所有外键
                $constraints = DB::select("
                    SELECT CONSTRAINT_NAME 
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                    WHERE TABLE_NAME = 'users' 
                    AND COLUMN_NAME = 'default_deliverer_id'
                    AND CONSTRAINT_NAME != 'PRIMARY'
                ");
                
                // 尝试删除所有找到的外键
                foreach ($constraints as $constraint) {
                    DB::statement("ALTER TABLE `users` DROP FOREIGN KEY `{$constraint->CONSTRAINT_NAME}`");
                }
            } catch (\Exception $e2) {
                // 忽略错误，继续执行
            }
        }
        
        Schema::table('users', function (Blueprint $table) {
            // 添加新的外键约束
            $table->foreign('default_deliverer_id', 'users_default_deliverer_id_foreign_fix')
                  ->references('id')
                  ->on('deliverers')
                  ->onDelete('set null');
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // 删除我们添加的外键约束
            $table->dropForeign('users_default_deliverer_id_foreign_fix');
        });
        
        // 不尝试恢复原始外键约束，因为它存在问题
    }
};
