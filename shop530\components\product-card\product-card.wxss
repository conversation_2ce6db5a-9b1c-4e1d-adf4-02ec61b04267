.product-card {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.06);
  transition: all 0.3s ease;
  position: relative;
  /* 为瀑布流优化：确保卡片宽度一致 */
  width: 100%;
  /* 防止内容撑开卡片 */
  box-sizing: border-box;
}

/* 瀑布流专用样式 */
.product-card.waterfall {
  /* 确保瀑布流中的卡片宽度完全一致 */
  max-width: none;
  flex-shrink: 0;
}

.product-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

/* ========== 商品图片区域 ========== */
.product-image-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 16rpx 16rpx 0 0;
  /* 限制图片容器的宽高比，保持瀑布流效果的同时避免过度差异 */
  min-height: 200rpx;
  max-height: 400rpx;
}

.product-image {
  width: 100%;
  /* 设置合适的高度范围，保持图片比例 */
  min-height: 200rpx;
  max-height: 400rpx;
  /* 使用 object-fit 确保图片填充且不变形 */
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
}

.product-image-wrapper:active .product-image {
  transform: scale(1.05);
}

/* 商品标签容器 */
.product-tags-container {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  z-index: 2;
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

/* 标签样式覆盖 - 与分类页保持一致 */
.product-tag {
  background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%) !important;
  color: #fff !important;
  font-size: 18rpx !important;
  padding: 4rpx 8rpx !important;
  border-radius: 12rpx !important;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3) !important;
  backdrop-filter: blur(8rpx);
  border: none !important;
}

/* 促销类标签 - 橙红色 */
.product-tag-promotion {
  background: linear-gradient(135deg, #ff6b35 0%, #ff4444 100%) !important;
  box-shadow: 0 2rpx 8rpx rgba(255, 68, 68, 0.3) !important;
}

/* 新品类标签 - 绿色 */
.product-tag-new {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%) !important;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3) !important;
}

/* 折扣/优惠类标签 - 红色 */
.product-tag-discount {
  background: linear-gradient(135deg, #ff4444 0%, #e53935 100%) !important;
  box-shadow: 0 2rpx 8rpx rgba(229, 57, 53, 0.3) !important;
}

/* 热销类标签 - 紫色 */
.product-tag-hot {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%) !important;
  box-shadow: 0 2rpx 8rpx rgba(123, 31, 162, 0.3) !important;
}

/* 推荐类标签 - 蓝色 */
.product-tag-recommend {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%) !important;
  box-shadow: 0 2rpx 8rpx rgba(25, 118, 210, 0.3) !important;
}

/* 默认类标签 - 保持原有橙色 */
.product-tag-default {
  background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%) !important;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3) !important;
}

/* ========== 商品信息区域 ========== */
.product-info {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

/* 商品信息区域的标签容器 */
.product-tags-container-info {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

/* 商品标题 */
.product-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 商品副标题 */
.product-subtitle {
  font-size: 24rpx;
  color: #999;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ========== 价格和操作区域 ========== */
.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: auto;
}

/* 价格区域 */
.price-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  min-width: 0; /* 防止flex子项溢出 */
}

/* 会员价格标识 */
.price-section .member-price-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4rpx;
  font-size: 20rpx;
  color: #faad14;
  font-weight: 500;
}

.price-section .member-price-indicator::before {
  content: '👑';
  font-size: 16rpx;
}

/* 价格加载状态 */
.price-section .price-loading {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #999;
  font-size: 24rpx;
}

.price-section .price-loading::before {
  content: '';
  width: 16rpx;
  height: 16rpx;
  border: 2rpx solid #e8e8e8;
  border-top: 2rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 价格错误状态 */
.price-section .price-error {
  color: #ff4757;
  font-size: 22rpx;
  opacity: 0.8;
}

/* 价格显示组件样式 */
.price-section .price-display.product-price {
  align-self: flex-start;
}

.price-section .price-display.product-price .price-symbol {
  font-size: 24rpx;
  color: #ff4444;
  font-weight: 600;
}

.price-section .price-display.product-price .price-main {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: 700;
}

.price-section .price-display.product-price .price-unit {
  font-size: 24rpx;
  color: #ff4444;
  font-weight: 500;
}

.price-section .price-display.product-price .login-prompt {
  font-size: 28rpx;
  color: #999;
  font-weight: 500;
}

/* 供应商信息 */
.supplier-info {
  font-size: 20rpx;
  color: #999;
  margin-top: 4rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ========== 加购按钮 ========== */
.add-cart-button {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.add-cart-button:active {
  transform: scale(0.9);
  box-shadow: 0 2rpx 12rpx rgba(76, 175, 80, 0.4);
}

/* ========== 购物车控制区域 ========== */
.cart-control-section {
  flex-shrink: 0;
  margin-left: 20rpx;
}

/* 数量控制器 */
.quantity-controller {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 30rpx;
  overflow: hidden;
  border: 1rpx solid #e8e8e8;
}

.quantity-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.quantity-btn:active {
  transform: scale(0.9);
}

.decrease-btn {
  background: #f8f8f8;
}

.decrease-btn:active {
  background: #e8e8e8;
}

.increase-btn {
  background: #4CAF50;
}

.increase-btn:active {
  background: #45a049;
}

/* 数量输入框 */
.quantity-input {
  width: 60rpx;
  height: 48rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  background: #fff;
  border: none;
  border-left: 1rpx solid #e8e8e8;
  border-right: 1rpx solid #e8e8e8;
  outline: none;
  padding: 0;
  margin: 0;
  transition: all 0.2s ease;
}

.quantity-input:focus {
  background: #f0f8ff;
  border-left-color: var(--primary-color);
  border-right-color: var(--primary-color);
}

/* 最小起购数量标记 */
.min-quantity-badge {
  font-size: 20rpx;
  color: #ff6b35;
  background: rgba(255, 107, 53, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-top: 8rpx;
  display: inline-block;
  border: 1rpx solid rgba(255, 107, 53, 0.2);
}

.quantity-display:active {
  background: #f0f0f0;
}

/* ========== 响应式适配 ========== */

/* 小卡片样式 */
.product-card.small .product-info {
  padding: 20rpx;
  gap: 10rpx;
}

.product-card.small .product-title {
  font-size: 26rpx;
  -webkit-line-clamp: 1;
}

.product-card.small .price-display.product-price .price-main {
  font-size: 28rpx;
}

.product-card.small .add-cart-button {
  width: 52rpx;
  height: 52rpx;
}

.product-card.small .quantity-btn {
  width: 44rpx;
  height: 44rpx;
}

.product-card.small .quantity-display {
  min-width: 56rpx;
  height: 44rpx;
  font-size: 26rpx;
}

/* 大卡片样式 */
.product-card.large .product-info {
  padding: 32rpx;
  gap: 16rpx;
}

.product-card.large .product-title {
  font-size: 32rpx;
}

.product-card.large .price-display.product-price .price-main {
  font-size: 36rpx;
}

.product-card.large .add-cart-button {
  width: 68rpx;
  height: 68rpx;
}

.product-card.large .quantity-btn {
  width: 52rpx;
  height: 52rpx;
}

.product-card.large .quantity-display {
  min-width: 64rpx;
  height: 52rpx;
  font-size: 30rpx;
}

/* ========== 特殊状态 ========== */

/* 缺货状态 */
.product-card.out-of-stock {
  opacity: 0.7;
}

.product-card.out-of-stock .product-image {
  filter: grayscale(50%);
}

/* 缺货遮罩 */
.out-of-stock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
}

.out-of-stock-text {
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  font-size: 24rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(4rpx);
}

/* 缺货按钮 */
.out-of-stock-button {
  width: 60rpx;
  height: 60rpx;
  background: #ccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #999;
  font-weight: 500;
}

/* 促销状态 */
.product-card.promotion::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-left: 60rpx solid transparent;
  border-top: 60rpx solid #ff4757;
  z-index: 1;
}

.product-card.promotion::after {
  content: '促';
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  color: #fff;
  font-size: 20rpx;
  font-weight: 700;
  z-index: 2;
  transform: rotate(45deg);
}

/* ========== 深色模式适配 ========== */
@media (prefers-color-scheme: dark) {
  .product-card {
    background: #2c2c2c;
    border: 1rpx solid #444;
  }
  
  .product-card .product-title {
    color: #fff;
  }
  
  .product-card .product-subtitle {
    color: #aaa;
  }
  
  .product-card .supplier-info {
    color: #ccc;
  }
}

/* ========== 紧凑布局 ========== */
.product-card.compact .product-footer {
  flex-direction: column;
  align-items: flex-start;
  gap: 16rpx;
}

.product-card.compact .cart-control-section {
  margin-left: 0;
  align-self: flex-end;
}

/* ========== 响应式设计 ========== */
@media (max-width: 350px) {
  .product-card .product-info {
    padding: 20rpx;
  }
  
  .product-card .product-title {
    font-size: 26rpx;
  }
  
  .product-card .price-display.product-price .price-main {
    font-size: 28rpx;
  }
} 