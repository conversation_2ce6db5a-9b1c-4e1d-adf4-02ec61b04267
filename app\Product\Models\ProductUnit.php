<?php

namespace App\Product\Models;

use App\Unit\Models\Unit;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Product\Models\ProductUnit
 *
 * @property int $id
 * @property int $product_id 产品ID
 * @property int $unit_id 单位ID
 * @property float $conversion_factor 转换系数
 * @property array|null $roles 单位角色
 * @property array|null $role_priority 角色优先级
 * @property bool $is_default 是否为默认单位
 * @property bool $is_active 是否启用
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class ProductUnit extends Model
{
    use HasFactory;

    /**
     * 表名
     */
    protected $table = 'product_units';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'product_id',
        'unit_id',
        'conversion_factor',
        'roles',
        'role_priority',
        'is_default',
        'is_active'
    ];

    /**
     * 类型转换
     */
    protected $casts = [
        'conversion_factor' => 'float',
        'roles' => 'array',
        'role_priority' => 'array',
        'is_default' => 'boolean',
        'is_active' => 'boolean'
    ];

    /**
     * 获取关联的产品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 获取关联的单位
     */
    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * 格式化转换因子（自动去除多余的0）
     * 
     * @param float $value
     * @return string
     */
    public function getFormattedConversionFactorAttribute()
    {
        $factor = (string)$this->conversion_factor;
        
        // 如果包含小数点
        if (strpos($factor, '.') !== false) {
            // 移除尾部多余的0
            $factor = rtrim($factor, '0');
            // 如果结尾是小数点，也移除
            $factor = rtrim($factor, '.');
        }
        
        return $factor;
    }
    
    /**
     * 获取转换关系的可读描述
     * 
     * @return string
     */
    public function getConversionDescriptionAttribute()
    {
        $product = $this->product;
        $unit = $this->unit;
        $baseUnit = $product->baseUnit;
        
        if (!$baseUnit || !$unit) {
            return "";
        }
        
        $factor = $this->formatted_conversion_factor;
        
        return "1{$unit->symbol} = {$factor}{$baseUnit->symbol}";
    }
    
    /**
     * 判断单位是否具有指定角色
     *
     * @param string $role 角色名称
     * @return bool
     */
    public function hasRole(string $role): bool
    {
        if (!$this->roles) {
            return false;
        }
        
        return in_array($role, $this->roles);
    }

    /**
     * 获取角色优先级
     *
     * @param string $role 角色名称
     * @return int 优先级值，值越小优先级越高
     */
    public function getRolePriority(string $role): int
    {
        if (!$this->role_priority) {
            return 999; // 默认最低优先级
        }
        
        return $this->role_priority[$role] ?? 999;
    }
    
    /**
     * 为兼容性提供旧的转换率属性
     * 
     * @deprecated 请使用conversion_factor
     * @return float
     */
    public function getConversionRateAttribute()
    {
        return $this->conversion_factor;
    }
    
    /**
     * 为兼容性设置旧的转换率属性
     * 
     * @deprecated 请使用conversion_factor
     * @param float $value
     * @return void
     */
    public function setConversionRateAttribute($value)
    {
        $this->attributes['conversion_factor'] = $value;
    }
    
    /**
     * 为兼容性提供旧的格式化转换率属性
     * 
     * @deprecated 请使用formatted_conversion_factor
     * @return string
     */
    public function getFormattedConversionRateAttribute()
    {
        return $this->getFormattedConversionFactorAttribute();
    }
    
    /**
     * 为兼容性提供旧的is_sale_unit属性
     * 
     * @deprecated 请使用hasRole('sales')
     * @return bool
     */
    public function getIsSaleUnitAttribute()
    {
        return $this->hasRole('sales');
    }
    
    /**
     * 为兼容性提供旧的is_purchase_unit属性
     * 
     * @deprecated 请使用hasRole('purchase')
     * @return bool
     */
    public function getIsPurchaseUnitAttribute()
    {
        return $this->hasRole('purchase');
    }
    
    /**
     * 为兼容性提供旧的is_inventory_unit属性
     * 
     * @deprecated 请使用hasRole('inventory')
     * @return bool
     */
    public function getIsInventoryUnitAttribute()
    {
        return $this->hasRole('inventory');
    }
} 