<?php

namespace App\Search\Repositories;

use App\Product\Models\Product;
use App\Product\Models\Category;
use App\Product\Models\ProductTag;
use App\Search\Models\SearchLog;
use App\Search\Models\HotKeyword;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class SearchRepository
{
    /**
     * 搜索商品
     *
     * @param string $keyword 搜索关键词
     * @param array $options 搜索选项
     * @return array
     */
    public function searchProducts($keyword, array $options = [])
    {
        // 默认选项
        $options = array_merge([
            'page' => 1,
            'pageSize' => 10,
            'sort' => 'default', // default, sales, price
            'order' => 'desc',   // asc, desc
            'categoryId' => null,
            'minPrice' => null,
            'maxPrice' => null,
            'tags' => [],
            'includeOutOfStock' => true, // 默认包含无库存商品
        ], $options);

        // 缓存键
        $cacheKey = 'search_products_' . md5($keyword . json_encode($options));
        
        // 清除旧缓存，确保结果实时更新
        if (isset($options['clearCache']) && $options['clearCache']) {
            Cache::forget($cacheKey);
        }
        
        // 使用缓存减少数据库查询，缓存10分钟
        return Cache::remember($cacheKey, 600, function () use ($keyword, $options) {
            // 使用 Scout 搜索引擎
            $query = Product::search($keyword);
            
            // 添加过滤条件
            // 状态过滤 - 只搜索上架商品
            $query->where('status', 1);
            $query->where('allow_sale', true);
            
            // 分类过滤
            if ($options['categoryId']) {
                $query->where('category_id', $options['categoryId']);
            }
            
            // 价格过滤
            if ($options['minPrice'] !== null) {
                $query->where('price', '>=', $options['minPrice']);
            }
            if ($options['maxPrice'] !== null) {
                $query->where('price', '<=', $options['maxPrice']);
            }
            
            // 标签过滤
            if (!empty($options['tags'])) {
                $query->whereIn('tag_ids', $options['tags']);
            }
            
            // 库存过滤
            if ($options['includeOutOfStock'] === false) {
                $query->where('total_stock', '>', 0)
                      ->orWhere('track_inventory', false);
            }
            
            // 排序
            switch ($options['sort']) {
                case 'sales':
                    $query->orderBy('sales_count', $options['order']);
                    break;
                case 'price':
                    $query->orderBy('price', $options['order']);
                    break;
                default:
                    // 默认排序 - 使用搜索引擎的相关性排序
                    break;
            }
            
            // 分页
            $searchResults = $query->paginate(
                $options['pageSize'],
                'page',
                $options['page']
            );
            
            // 格式化结果
            $formattedProducts = collect($searchResults->items())->map(function ($product) {
                // 获取完整的产品信息
                $fullProduct = Product::with(['tags', 'category', 'baseUnit'])->find($product->id);
                if (!$fullProduct) return null;
                
                // 计算商品总库存
                $totalStock = $fullProduct->track_inventory 
                    ? $fullProduct->getTotalStock() 
                    : 999; // 不跟踪库存的商品默认有足够库存
                
                return [
                    'id' => $fullProduct->id,
                    'title' => $fullProduct->name,
                    'image' => $fullProduct->image_url ?? $fullProduct->cover_url ?? '',
                    'price' => $fullProduct->price,
                    'originalPrice' => $fullProduct->original_price ?? $fullProduct->price,
                    'sales' => $fullProduct->sales_count,
                    'tags' => $fullProduct->tags->pluck('name')->toArray(),
                    'category' => $fullProduct->category ? $fullProduct->category->name : null,
                    'status' => $fullProduct->status == 1 ? 'active' : 'inactive',
                    'stock' => $totalStock,
                    'out_of_stock' => $totalStock <= 0 && $fullProduct->track_inventory,
                    'unit' => $fullProduct->baseUnit ? $fullProduct->baseUnit->name : '件',
                    'code' => $fullProduct->code,
                ];
            })->filter()->values();

            return [
                'list' => $formattedProducts,
                'total' => $searchResults->total(),
                'page' => $options['page'],
                'pageSize' => $options['pageSize'],
                'hasMore' => $searchResults->hasMorePages()
            ];
        });
    }

    /**
     * 获取热门搜索关键词
     *
     * @param int $limit 限制数量
     * @return array
     */
    public function getHotKeywords($limit = 10)
    {
        // 使用缓存减少数据库查询，缓存1小时
        return Cache::remember('search_hot_keywords', 3600, function () use ($limit) {
            // 优先获取手动设置的热门关键词
            $manualKeywords = HotKeyword::where('is_active', true)
                ->where('is_manual', true)
                ->orderBy('weight', 'desc')
                ->limit($limit)
                ->get(['id', 'keyword', 'highlight']);
                
            $result = $manualKeywords->map(function ($item) {
                return [
                    'keyword' => $item->keyword,
                    'highlight' => $item->highlight
                ];
            })->toArray();
            
            // 如果手动设置的关键词不足，则从自动统计的关键词中补充
            if (count($result) < $limit) {
                $remainingLimit = $limit - count($result);
                
                // 从搜索日志中获取热门关键词
                $autoKeywords = SearchLog::select('keyword', DB::raw('COUNT(*) as count'))
                    ->where('created_at', '>=', now()->subDays(30))
                    ->groupBy('keyword')
                    ->orderBy('count', 'desc')
                    ->limit($remainingLimit)
                    ->get();
                    
                foreach ($autoKeywords as $item) {
                    $result[] = [
                        'keyword' => $item->keyword,
                        'highlight' => false
                    ];
                }
            }
            
            return $result;
        });
    }

    /**
     * 获取搜索建议
     *
     * @param string $keyword 搜索关键词
     * @param int $limit 限制数量
     * @return array
     */
    public function getSuggestions($keyword, $limit = 10)
    {
        if (empty($keyword)) {
            return [];
        }

        // 缓存键
        $cacheKey = 'search_suggestions_' . md5($keyword);

        // 使用缓存减少数据库查询，缓存5分钟
        return Cache::remember($cacheKey, 300, function () use ($keyword, $limit) {
            // 使用 Scout 搜索引擎获取产品建议
            $productSuggestions = Product::search($keyword)
                ->where('status', 1)
                ->take($limit)
                ->get()
                ->pluck('name')
                ->toArray();

            // 从分类名称中获取包含关键词的建议
            $categorySuggestions = Category::where('name', 'like', "%{$keyword}%")
                ->where('status', 'active')
                ->limit($limit / 2)
                ->pluck('name')
                ->toArray();

            // 从标签中获取包含关键词的建议
            $tagSuggestions = ProductTag::where('name', 'like', "%{$keyword}%")
                ->limit($limit / 2)
                ->pluck('name')
                ->toArray();

            // 合并建议并去重
            $suggestions = array_unique(array_merge($productSuggestions, $categorySuggestions, $tagSuggestions));
            
            // 限制数量
            $suggestions = array_slice($suggestions, 0, $limit);
            
            // 格式化结果
            return array_map(function ($text) use ($keyword) {
                return [
                    'text' => $text,
                    'highlight' => $keyword
                ];
            }, $suggestions);
        });
    }

    /**
     * 记录搜索日志
     *
     * @param string $keyword 搜索关键词
     * @param int $resultCount 结果数量
     * @param string $platform 平台
     * @return bool
     */
    public function logSearch($keyword, $resultCount = 0, $platform = 'web')
    {
        try {
            SearchLog::create([
                'keyword' => $keyword,
                'user_id' => auth()->id(),
                'session_id' => session()->getId(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'result_count' => $resultCount,
                'platform' => $platform,
                'source' => 'search'
            ]);
            
            return true;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('记录搜索日志失败', [
                'error' => $e->getMessage(),
                'keyword' => $keyword
            ]);
            
            return false;
        }
    }

    /**
     * 获取搜索统计数据
     *
     * @param array $options 选项
     * @return array
     */
    public function getSearchStats(array $options = [])
    {
        $options = array_merge([
            'days' => 30,
            'limit' => 10
        ], $options);
        
        // 热门搜索词统计
        $hotKeywords = SearchLog::select('keyword', DB::raw('COUNT(*) as count'))
            ->where('created_at', '>=', now()->subDays($options['days']))
            ->groupBy('keyword')
            ->orderBy('count', 'desc')
            ->limit($options['limit'])
            ->get();
            
        // 每日搜索量统计
        $dailyStats = SearchLog::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->where('created_at', '>=', now()->subDays($options['days']))
            ->groupBy('date')
            ->orderBy('date', 'asc')
            ->get();
            
        // 平台搜索量统计
        $platformStats = SearchLog::select('platform', DB::raw('COUNT(*) as count'))
            ->where('created_at', '>=', now()->subDays($options['days']))
            ->groupBy('platform')
            ->orderBy('count', 'desc')
            ->get();
            
        // 零结果搜索词统计
        $zeroResultKeywords = SearchLog::select('keyword', DB::raw('COUNT(*) as count'))
            ->where('created_at', '>=', now()->subDays($options['days']))
            ->where('result_count', 0)
            ->groupBy('keyword')
            ->orderBy('count', 'desc')
            ->limit($options['limit'])
            ->get();
            
        return [
            'hot_keywords' => $hotKeywords,
            'daily_stats' => $dailyStats,
            'platform_stats' => $platformStats,
            'zero_result_keywords' => $zeroResultKeywords,
            'total_searches' => SearchLog::where('created_at', '>=', now()->subDays($options['days']))->count(),
            'unique_keywords' => SearchLog::where('created_at', '>=', now()->subDays($options['days']))->distinct('keyword')->count('keyword')
        ];
    }
} 