<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('openid')->nullable()->comment('微信小程序openid');
            $table->string('unionid')->nullable()->comment('微信开放平台unionid');
            $table->string('phone')->nullable()->comment('手机号码');
            $table->string('avatar')->nullable()->comment('头像URL');
            $table->string('nickname')->nullable()->comment('微信昵称');
            $table->tinyInteger('gender')->default(0)->comment('性别：0未知，1男，2女');
            $table->string('province')->nullable()->comment('省份');
            $table->string('city')->nullable()->comment('城市');
            $table->string('country')->nullable()->comment('国家');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'openid',
                'unionid',
                'phone',
                'avatar',
                'nickname',
                'gender',
                'province',
                'city',
                'country',
            ]);
        });
    }
}; 