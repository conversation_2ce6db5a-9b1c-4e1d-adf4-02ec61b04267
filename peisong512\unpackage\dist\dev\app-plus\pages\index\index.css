/* 水平间距 */
/* 水平间距 */
.segmented-control[data-v-86aa1171] {
  display: flex;
  box-sizing: border-box;
  flex-direction: row;
  height: 36px;
  overflow: hidden;
}
.segmented-control__item[data-v-86aa1171] {
  display: inline-flex;
  box-sizing: border-box;
  position: relative;
  flex: 1;
  justify-content: center;
  align-items: center;
}
.segmented-control__item--button[data-v-86aa1171] {
  border-style: solid;
  border-top-width: 1px;
  border-bottom-width: 1px;
  border-right-width: 1px;
  border-left-width: 0;
}
.segmented-control__item--button--first[data-v-86aa1171] {
  border-left-width: 1px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}
.segmented-control__item--button--last[data-v-86aa1171] {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.segmented-control__item--text[data-v-86aa1171] {
  border-bottom-style: solid;
  border-bottom-width: 2px;
  padding: 6px 0;
}
.segmented-control__text[data-v-86aa1171] {
  font-size: 14px;
  line-height: 20px;
  text-align: center;
}
/* 水平间距 */
/* 水平间距 */
.uniui-cart-filled[data-v-d31e1c47]:before {
  content: "\e6d0";
}
.uniui-gift-filled[data-v-d31e1c47]:before {
  content: "\e6c4";
}
.uniui-color[data-v-d31e1c47]:before {
  content: "\e6cf";
}
.uniui-wallet[data-v-d31e1c47]:before {
  content: "\e6b1";
}
.uniui-settings-filled[data-v-d31e1c47]:before {
  content: "\e6ce";
}
.uniui-auth-filled[data-v-d31e1c47]:before {
  content: "\e6cc";
}
.uniui-shop-filled[data-v-d31e1c47]:before {
  content: "\e6cd";
}
.uniui-staff-filled[data-v-d31e1c47]:before {
  content: "\e6cb";
}
.uniui-vip-filled[data-v-d31e1c47]:before {
  content: "\e6c6";
}
.uniui-plus-filled[data-v-d31e1c47]:before {
  content: "\e6c7";
}
.uniui-folder-add-filled[data-v-d31e1c47]:before {
  content: "\e6c8";
}
.uniui-color-filled[data-v-d31e1c47]:before {
  content: "\e6c9";
}
.uniui-tune-filled[data-v-d31e1c47]:before {
  content: "\e6ca";
}
.uniui-calendar-filled[data-v-d31e1c47]:before {
  content: "\e6c0";
}
.uniui-notification-filled[data-v-d31e1c47]:before {
  content: "\e6c1";
}
.uniui-wallet-filled[data-v-d31e1c47]:before {
  content: "\e6c2";
}
.uniui-medal-filled[data-v-d31e1c47]:before {
  content: "\e6c3";
}
.uniui-fire-filled[data-v-d31e1c47]:before {
  content: "\e6c5";
}
.uniui-refreshempty[data-v-d31e1c47]:before {
  content: "\e6bf";
}
.uniui-location-filled[data-v-d31e1c47]:before {
  content: "\e6af";
}
.uniui-person-filled[data-v-d31e1c47]:before {
  content: "\e69d";
}
.uniui-personadd-filled[data-v-d31e1c47]:before {
  content: "\e698";
}
.uniui-arrowthinleft[data-v-d31e1c47]:before {
  content: "\e6d2";
}
.uniui-arrowthinup[data-v-d31e1c47]:before {
  content: "\e6d3";
}
.uniui-arrowthindown[data-v-d31e1c47]:before {
  content: "\e6d4";
}
.uniui-back[data-v-d31e1c47]:before {
  content: "\e6b9";
}
.uniui-forward[data-v-d31e1c47]:before {
  content: "\e6ba";
}
.uniui-arrow-right[data-v-d31e1c47]:before {
  content: "\e6bb";
}
.uniui-arrow-left[data-v-d31e1c47]:before {
  content: "\e6bc";
}
.uniui-arrow-up[data-v-d31e1c47]:before {
  content: "\e6bd";
}
.uniui-arrow-down[data-v-d31e1c47]:before {
  content: "\e6be";
}
.uniui-arrowthinright[data-v-d31e1c47]:before {
  content: "\e6d1";
}
.uniui-down[data-v-d31e1c47]:before {
  content: "\e6b8";
}
.uniui-bottom[data-v-d31e1c47]:before {
  content: "\e6b8";
}
.uniui-arrowright[data-v-d31e1c47]:before {
  content: "\e6d5";
}
.uniui-right[data-v-d31e1c47]:before {
  content: "\e6b5";
}
.uniui-up[data-v-d31e1c47]:before {
  content: "\e6b6";
}
.uniui-top[data-v-d31e1c47]:before {
  content: "\e6b6";
}
.uniui-left[data-v-d31e1c47]:before {
  content: "\e6b7";
}
.uniui-arrowup[data-v-d31e1c47]:before {
  content: "\e6d6";
}
.uniui-eye[data-v-d31e1c47]:before {
  content: "\e651";
}
.uniui-eye-filled[data-v-d31e1c47]:before {
  content: "\e66a";
}
.uniui-eye-slash[data-v-d31e1c47]:before {
  content: "\e6b3";
}
.uniui-eye-slash-filled[data-v-d31e1c47]:before {
  content: "\e6b4";
}
.uniui-info-filled[data-v-d31e1c47]:before {
  content: "\e649";
}
.uniui-reload[data-v-d31e1c47]:before {
  content: "\e6b2";
}
.uniui-micoff-filled[data-v-d31e1c47]:before {
  content: "\e6b0";
}
.uniui-map-pin-ellipse[data-v-d31e1c47]:before {
  content: "\e6ac";
}
.uniui-map-pin[data-v-d31e1c47]:before {
  content: "\e6ad";
}
.uniui-location[data-v-d31e1c47]:before {
  content: "\e6ae";
}
.uniui-starhalf[data-v-d31e1c47]:before {
  content: "\e683";
}
.uniui-star[data-v-d31e1c47]:before {
  content: "\e688";
}
.uniui-star-filled[data-v-d31e1c47]:before {
  content: "\e68f";
}
.uniui-calendar[data-v-d31e1c47]:before {
  content: "\e6a0";
}
.uniui-fire[data-v-d31e1c47]:before {
  content: "\e6a1";
}
.uniui-medal[data-v-d31e1c47]:before {
  content: "\e6a2";
}
.uniui-font[data-v-d31e1c47]:before {
  content: "\e6a3";
}
.uniui-gift[data-v-d31e1c47]:before {
  content: "\e6a4";
}
.uniui-link[data-v-d31e1c47]:before {
  content: "\e6a5";
}
.uniui-notification[data-v-d31e1c47]:before {
  content: "\e6a6";
}
.uniui-staff[data-v-d31e1c47]:before {
  content: "\e6a7";
}
.uniui-vip[data-v-d31e1c47]:before {
  content: "\e6a8";
}
.uniui-folder-add[data-v-d31e1c47]:before {
  content: "\e6a9";
}
.uniui-tune[data-v-d31e1c47]:before {
  content: "\e6aa";
}
.uniui-auth[data-v-d31e1c47]:before {
  content: "\e6ab";
}
.uniui-person[data-v-d31e1c47]:before {
  content: "\e699";
}
.uniui-email-filled[data-v-d31e1c47]:before {
  content: "\e69a";
}
.uniui-phone-filled[data-v-d31e1c47]:before {
  content: "\e69b";
}
.uniui-phone[data-v-d31e1c47]:before {
  content: "\e69c";
}
.uniui-email[data-v-d31e1c47]:before {
  content: "\e69e";
}
.uniui-personadd[data-v-d31e1c47]:before {
  content: "\e69f";
}
.uniui-chatboxes-filled[data-v-d31e1c47]:before {
  content: "\e692";
}
.uniui-contact[data-v-d31e1c47]:before {
  content: "\e693";
}
.uniui-chatbubble-filled[data-v-d31e1c47]:before {
  content: "\e694";
}
.uniui-contact-filled[data-v-d31e1c47]:before {
  content: "\e695";
}
.uniui-chatboxes[data-v-d31e1c47]:before {
  content: "\e696";
}
.uniui-chatbubble[data-v-d31e1c47]:before {
  content: "\e697";
}
.uniui-upload-filled[data-v-d31e1c47]:before {
  content: "\e68e";
}
.uniui-upload[data-v-d31e1c47]:before {
  content: "\e690";
}
.uniui-weixin[data-v-d31e1c47]:before {
  content: "\e691";
}
.uniui-compose[data-v-d31e1c47]:before {
  content: "\e67f";
}
.uniui-qq[data-v-d31e1c47]:before {
  content: "\e680";
}
.uniui-download-filled[data-v-d31e1c47]:before {
  content: "\e681";
}
.uniui-pyq[data-v-d31e1c47]:before {
  content: "\e682";
}
.uniui-sound[data-v-d31e1c47]:before {
  content: "\e684";
}
.uniui-trash-filled[data-v-d31e1c47]:before {
  content: "\e685";
}
.uniui-sound-filled[data-v-d31e1c47]:before {
  content: "\e686";
}
.uniui-trash[data-v-d31e1c47]:before {
  content: "\e687";
}
.uniui-videocam-filled[data-v-d31e1c47]:before {
  content: "\e689";
}
.uniui-spinner-cycle[data-v-d31e1c47]:before {
  content: "\e68a";
}
.uniui-weibo[data-v-d31e1c47]:before {
  content: "\e68b";
}
.uniui-videocam[data-v-d31e1c47]:before {
  content: "\e68c";
}
.uniui-download[data-v-d31e1c47]:before {
  content: "\e68d";
}
.uniui-help[data-v-d31e1c47]:before {
  content: "\e679";
}
.uniui-navigate-filled[data-v-d31e1c47]:before {
  content: "\e67a";
}
.uniui-plusempty[data-v-d31e1c47]:before {
  content: "\e67b";
}
.uniui-smallcircle[data-v-d31e1c47]:before {
  content: "\e67c";
}
.uniui-minus-filled[data-v-d31e1c47]:before {
  content: "\e67d";
}
.uniui-micoff[data-v-d31e1c47]:before {
  content: "\e67e";
}
.uniui-closeempty[data-v-d31e1c47]:before {
  content: "\e66c";
}
.uniui-clear[data-v-d31e1c47]:before {
  content: "\e66d";
}
.uniui-navigate[data-v-d31e1c47]:before {
  content: "\e66e";
}
.uniui-minus[data-v-d31e1c47]:before {
  content: "\e66f";
}
.uniui-image[data-v-d31e1c47]:before {
  content: "\e670";
}
.uniui-mic[data-v-d31e1c47]:before {
  content: "\e671";
}
.uniui-paperplane[data-v-d31e1c47]:before {
  content: "\e672";
}
.uniui-close[data-v-d31e1c47]:before {
  content: "\e673";
}
.uniui-help-filled[data-v-d31e1c47]:before {
  content: "\e674";
}
.uniui-paperplane-filled[data-v-d31e1c47]:before {
  content: "\e675";
}
.uniui-plus[data-v-d31e1c47]:before {
  content: "\e676";
}
.uniui-mic-filled[data-v-d31e1c47]:before {
  content: "\e677";
}
.uniui-image-filled[data-v-d31e1c47]:before {
  content: "\e678";
}
.uniui-locked-filled[data-v-d31e1c47]:before {
  content: "\e668";
}
.uniui-info[data-v-d31e1c47]:before {
  content: "\e669";
}
.uniui-locked[data-v-d31e1c47]:before {
  content: "\e66b";
}
.uniui-camera-filled[data-v-d31e1c47]:before {
  content: "\e658";
}
.uniui-chat-filled[data-v-d31e1c47]:before {
  content: "\e659";
}
.uniui-camera[data-v-d31e1c47]:before {
  content: "\e65a";
}
.uniui-circle[data-v-d31e1c47]:before {
  content: "\e65b";
}
.uniui-checkmarkempty[data-v-d31e1c47]:before {
  content: "\e65c";
}
.uniui-chat[data-v-d31e1c47]:before {
  content: "\e65d";
}
.uniui-circle-filled[data-v-d31e1c47]:before {
  content: "\e65e";
}
.uniui-flag[data-v-d31e1c47]:before {
  content: "\e65f";
}
.uniui-flag-filled[data-v-d31e1c47]:before {
  content: "\e660";
}
.uniui-gear-filled[data-v-d31e1c47]:before {
  content: "\e661";
}
.uniui-home[data-v-d31e1c47]:before {
  content: "\e662";
}
.uniui-home-filled[data-v-d31e1c47]:before {
  content: "\e663";
}
.uniui-gear[data-v-d31e1c47]:before {
  content: "\e664";
}
.uniui-smallcircle-filled[data-v-d31e1c47]:before {
  content: "\e665";
}
.uniui-map-filled[data-v-d31e1c47]:before {
  content: "\e666";
}
.uniui-map[data-v-d31e1c47]:before {
  content: "\e667";
}
.uniui-refresh-filled[data-v-d31e1c47]:before {
  content: "\e656";
}
.uniui-refresh[data-v-d31e1c47]:before {
  content: "\e657";
}
.uniui-cloud-upload[data-v-d31e1c47]:before {
  content: "\e645";
}
.uniui-cloud-download-filled[data-v-d31e1c47]:before {
  content: "\e646";
}
.uniui-cloud-download[data-v-d31e1c47]:before {
  content: "\e647";
}
.uniui-cloud-upload-filled[data-v-d31e1c47]:before {
  content: "\e648";
}
.uniui-redo[data-v-d31e1c47]:before {
  content: "\e64a";
}
.uniui-images-filled[data-v-d31e1c47]:before {
  content: "\e64b";
}
.uniui-undo-filled[data-v-d31e1c47]:before {
  content: "\e64c";
}
.uniui-more[data-v-d31e1c47]:before {
  content: "\e64d";
}
.uniui-more-filled[data-v-d31e1c47]:before {
  content: "\e64e";
}
.uniui-undo[data-v-d31e1c47]:before {
  content: "\e64f";
}
.uniui-images[data-v-d31e1c47]:before {
  content: "\e650";
}
.uniui-paperclip[data-v-d31e1c47]:before {
  content: "\e652";
}
.uniui-settings[data-v-d31e1c47]:before {
  content: "\e653";
}
.uniui-search[data-v-d31e1c47]:before {
  content: "\e654";
}
.uniui-redo-filled[data-v-d31e1c47]:before {
  content: "\e655";
}
.uniui-list[data-v-d31e1c47]:before {
  content: "\e644";
}
.uniui-mail-open-filled[data-v-d31e1c47]:before {
  content: "\e63a";
}
.uniui-hand-down-filled[data-v-d31e1c47]:before {
  content: "\e63c";
}
.uniui-hand-down[data-v-d31e1c47]:before {
  content: "\e63d";
}
.uniui-hand-up-filled[data-v-d31e1c47]:before {
  content: "\e63e";
}
.uniui-hand-up[data-v-d31e1c47]:before {
  content: "\e63f";
}
.uniui-heart-filled[data-v-d31e1c47]:before {
  content: "\e641";
}
.uniui-mail-open[data-v-d31e1c47]:before {
  content: "\e643";
}
.uniui-heart[data-v-d31e1c47]:before {
  content: "\e639";
}
.uniui-loop[data-v-d31e1c47]:before {
  content: "\e633";
}
.uniui-pulldown[data-v-d31e1c47]:before {
  content: "\e632";
}
.uniui-scan[data-v-d31e1c47]:before {
  content: "\e62a";
}
.uniui-bars[data-v-d31e1c47]:before {
  content: "\e627";
}
.uniui-checkbox[data-v-d31e1c47]:before {
  content: "\e62b";
}
.uniui-checkbox-filled[data-v-d31e1c47]:before {
  content: "\e62c";
}
.uniui-shop[data-v-d31e1c47]:before {
  content: "\e62f";
}
.uniui-headphones[data-v-d31e1c47]:before {
  content: "\e630";
}
.uniui-cart[data-v-d31e1c47]:before {
  content: "\e631";
}
@font-face {
  font-family: uniicons;
  src: url("../../assets/uniicons.32e978a5.ttf");
}
.uni-icons[data-v-d31e1c47] {
  font-family: uniicons;
  text-decoration: none;
  text-align: center;
}
/* 水平间距 */
/* 水平间距 */
.uni-card[data-v-ae4bee67] {
  margin: 10px;
  padding: 0 8px;
  border-radius: 4px;
  overflow: hidden;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;
  background-color: #fff;
  flex: 1;
}
.uni-card .uni-card__cover[data-v-ae4bee67] {
  position: relative;
  margin-top: 10px;
  flex-direction: row;
  overflow: hidden;
  border-radius: 4px;
}
.uni-card .uni-card__cover .uni-card__cover-image[data-v-ae4bee67] {
  flex: 1;
}
.uni-card .uni-card__header[data-v-ae4bee67] {
  display: flex;
  border-bottom: 1px #DCDCDC solid;
  flex-direction: row;
  align-items: center;
  padding: 10px;
  overflow: hidden;
}
.uni-card .uni-card__header .uni-card__header-box[data-v-ae4bee67] {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
  overflow: hidden;
}
.uni-card .uni-card__header .uni-card__header-avatar[data-v-ae4bee67] {
  width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: 5px;
  margin-right: 10px;
}
.uni-card .uni-card__header .uni-card__header-avatar .uni-card__header-avatar-image[data-v-ae4bee67] {
  flex: 1;
  width: 40px;
  height: 40px;
}
.uni-card .uni-card__header .uni-card__header-content[data-v-ae4bee67] {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  overflow: hidden;
}
.uni-card .uni-card__header .uni-card__header-content .uni-card__header-content-title[data-v-ae4bee67] {
  font-size: 15px;
  color: #3a3a3a;
}
.uni-card .uni-card__header .uni-card__header-content .uni-card__header-content-subtitle[data-v-ae4bee67] {
  font-size: 12px;
  margin-top: 5px;
  color: #909399;
}
.uni-card .uni-card__header .uni-card__header-extra[data-v-ae4bee67] {
  line-height: 12px;
}
.uni-card .uni-card__header .uni-card__header-extra .uni-card__header-extra-text[data-v-ae4bee67] {
  font-size: 12px;
  color: #909399;
}
.uni-card .uni-card__content[data-v-ae4bee67] {
  padding: 10px;
  font-size: 14px;
  color: #6a6a6a;
  line-height: 22px;
}
.uni-card .uni-card__actions[data-v-ae4bee67] {
  font-size: 12px;
}
.uni-card--border[data-v-ae4bee67] {
  border: 1px solid #DCDCDC;
}
.uni-card--shadow[data-v-ae4bee67] {
  position: relative;
  box-shadow: 0 1px 8px 1px rgba(165, 165, 165, 0.2);
}
.uni-card--full[data-v-ae4bee67] {
  margin: 0;
  border-left-width: 0;
  border-left-width: 0;
  border-radius: 0;
}
.uni-card--full[data-v-ae4bee67]:after {
  border-radius: 0;
}
.uni-ellipsis[data-v-ae4bee67] {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/* 水平间距 */
/* 水平间距 */
.delivery-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  border-bottom: 0.03125rem solid #e0e0e0;
  padding: 0.46875rem 0;
  /* 添加安全区域适配 */
  padding-top: calc(0.46875rem + env(safe-area-inset-top));
}
.scroll-content {
  flex: 1;
  /* 动态计算顶部间距，包含安全区域 */
  margin-top: calc(2.5rem + env(safe-area-inset-top));
  height: calc(100vh - 2.5rem - env(safe-area-inset-top));
}
.empty-tip {
  margin-top: 3.125rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}
.empty-tip uni-text {
  margin-top: 0.625rem;
  font-size: 0.9375rem;
}
.delivery-list {
  padding: 0.3125rem;
}
.task-card {
  margin-bottom: 0.3125rem;
}
.delivery-info .info-row {
  display: flex;
  margin-bottom: 0.3125rem;
  font-size: 0.8125rem;
}
.delivery-info .info-row .label {
  width: 3.125rem;
  color: #666;
}
.delivery-info .info-row .value {
  flex: 1;
  color: #333;
}
.delivery-info .info-row .value.address {
  word-break: break-all;
}
.delivery-info .info-row .value.status.pending {
  color: #ff9800;
}
.delivery-info .info-row .value.status.in_progress {
  color: #2196f3;
}
.delivery-info .info-row .value.status.completed {
  color: #4caf50;
}
.delivery-info .info-row .merchant-name {
  color: #666;
  font-size: 0.6875rem;
  margin-left: 0.25rem;
  font-style: italic;
}
.card-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.46875rem;
}
.card-actions .action-btn {
  margin-left: 0.46875rem;
  font-size: 0.6875rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.1875rem;
  border: none;
}
.card-actions .action-btn.accept {
  background-color: #4caf50;
  color: white;
}
.card-actions .action-btn.complete {
  background-color: #ff9800;
  color: white;
}
.card-actions .action-btn.call {
  background-color: #2196f3;
  color: white;
}
.card-actions .action-btn.navigate {
  background-color: #9c27b0;
  color: white;
}
.order-items-section {
  margin-top: 0.46875rem;
  border-top: 0.03125rem solid #e0e0e0;
  padding-top: 0.3125rem;
}
.order-items-section .section-title {
  margin-bottom: 0.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}
.order-items-section .section-title uni-text {
  font-size: 0.75rem;
  color: #333;
  font-weight: bold;
}
.order-items-section .section-title .detail-btn {
  display: flex;
  align-items: center;
  padding: 0.125rem 0.25rem;
  background-color: #f0f8ff;
  border-radius: 0.375rem;
  border: 0.03125rem solid #007AFF;
}
.order-items-section .section-title .detail-btn .detail-text {
  font-size: 0.625rem;
  color: #007AFF;
  margin-right: 0.125rem;
}
.order-items-section .items-summary {
  margin-bottom: 0.25rem;
}
.order-items-section .items-summary .summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.125rem;
}
.order-items-section .items-summary .summary-item .summary-label {
  font-size: 0.6875rem;
  color: #666;
}
.order-items-section .items-summary .summary-item .summary-value {
  font-size: 0.6875rem;
  color: #333;
  font-weight: bold;
}
.order-items-section .items-summary .summary-item .summary-value.amount {
  color: #ff5722;
  font-size: 0.75rem;
}
.order-items-section .items-detail {
  margin-top: 0.25rem;
  border-top: 0.03125rem solid #e0e0e0;
  padding-top: 0.25rem;
}
.order-items-section .items-detail .items-header {
  display: flex;
  background-color: #f8f9fa;
  padding: 0.1875rem 0.25rem;
  border-radius: 0.125rem;
  margin-bottom: 0.1875rem;
}
.order-items-section .items-detail .items-header .header-name {
  flex: 2;
  font-size: 0.6875rem;
  color: #666;
  font-weight: bold;
}
.order-items-section .items-detail .items-header .header-qty {
  flex: 0.8;
  text-align: center;
  font-size: 0.6875rem;
  color: #666;
  font-weight: bold;
}
.order-items-section .items-detail .items-header .header-price {
  flex: 1;
  text-align: center;
  font-size: 0.6875rem;
  color: #666;
  font-weight: bold;
}
.order-items-section .items-detail .items-header .header-total {
  flex: 1;
  text-align: right;
  font-size: 0.6875rem;
  color: #666;
  font-weight: bold;
}
.order-items-section .items-detail .items-list .item-row {
  display: flex;
  align-items: center;
  padding: 0.1875rem 0.25rem;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.order-items-section .items-detail .items-list .item-row:last-child {
  border-bottom: none;
}
.order-items-section .items-detail .items-list .item-row .item-name {
  flex: 2;
  display: flex;
  flex-direction: column;
}
.order-items-section .items-detail .items-list .item-row .item-name .product-name {
  font-size: 0.6875rem;
  color: #333;
  line-height: 1.2;
}
.order-items-section .items-detail .items-list .item-row .item-name .product-sku {
  font-size: 0.5625rem;
  color: #999;
  margin-top: 0.0625rem;
}
.order-items-section .items-detail .items-list .item-row .item-qty {
  flex: 0.8;
  text-align: center;
  font-size: 0.6875rem;
  color: #333;
}
.order-items-section .items-detail .items-list .item-row .item-price {
  flex: 1;
  text-align: center;
  font-size: 0.6875rem;
  color: #666;
}
.order-items-section .items-detail .items-list .item-row .item-total {
  flex: 1;
  text-align: right;
  font-size: 0.6875rem;
  color: #ff5722;
  font-weight: bold;
}
.no-items {
  margin-top: 0.3125rem;
  padding: 0.46875rem;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 0.125rem;
}
.no-items uni-text {
  font-size: 0.6875rem;
  color: #999;
}