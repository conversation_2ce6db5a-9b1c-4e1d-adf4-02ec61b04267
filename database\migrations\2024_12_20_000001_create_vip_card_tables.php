<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // VIP卡详情表
        Schema::create('zjhj_bd_vip_card_detail', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('VIP卡名称');
            $table->text('description')->nullable()->comment('VIP卡描述');
            $table->decimal('price', 10, 2)->default(0)->comment('VIP卡价格');
            $table->integer('validity_days')->default(0)->comment('有效期天数');
            $table->json('benefits')->nullable()->comment('会员权益');
            $table->boolean('status')->default(true)->comment('状态：启用/禁用');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('is_delete')->default(false)->comment('是否删除');
            $table->timestamps();
            
            $table->index(['status', 'is_delete']);
        });

        // VIP卡关联卡券表
        Schema::create('zjhj_bd_vip_card_cards', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('detail_id')->comment('VIP卡ID');
            $table->unsignedBigInteger('card_id')->comment('卡券ID');
            $table->integer('send_num')->default(0)->comment('赠送数量');
            $table->boolean('is_delete')->default(false)->comment('是否删除');
            $table->timestamps();
            
            $table->index(['detail_id', 'is_delete']);
            $table->index(['card_id', 'is_delete']);
        });

        // VIP卡关联优惠券表
        Schema::create('zjhj_bd_vip_card_coupons', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('detail_id')->comment('VIP卡ID');
            $table->unsignedBigInteger('coupon_id')->comment('优惠券ID');
            $table->integer('send_num')->default(0)->comment('赠送数量');
            $table->boolean('is_delete')->default(false)->comment('是否删除');
            $table->timestamps();
            
            $table->index(['detail_id', 'is_delete']);
            $table->index(['coupon_id', 'is_delete']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('zjhj_bd_vip_card_coupons');
        Schema::dropIfExists('zjhj_bd_vip_card_cards');
        Schema::dropIfExists('zjhj_bd_vip_card_detail');
    }
}; 