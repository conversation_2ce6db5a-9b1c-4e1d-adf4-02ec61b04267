<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('unit_conversions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('from_unit_id')->constrained('units')->onDelete('cascade')->comment('源单位ID');
            $table->foreignId('to_unit_id')->constrained('units')->onDelete('cascade')->comment('目标单位ID');
            $table->decimal('conversion_factor', 15, 6)->comment('转换系数');
            $table->timestamps();
            
            // 添加唯一约束
            $table->unique(['from_unit_id', 'to_unit_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unit_conversions');
    }
}; 