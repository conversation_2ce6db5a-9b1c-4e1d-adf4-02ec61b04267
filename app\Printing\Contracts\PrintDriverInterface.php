<?php

namespace App\Printing\Contracts;

interface PrintDriverInterface
{
    /**
     * 初始化打印驱动
     *
     * @param array $config 配置参数
     * @return void
     */
    public function initialize(array $config = []): void;

    /**
     * 打印文本内容
     *
     * @param string $content 文本内容
     * @param array $options 打印选项
     * @return bool
     */
    public function printText(string $content, array $options = []): bool;

    /**
     * 打印HTML内容
     *
     * @param string $html HTML内容
     * @param array $options 打印选项
     * @return bool
     */
    public function printHtml(string $html, array $options = []): bool;

    /**
     * 获取可用打印机列表
     *
     * @return array
     */
    public function getPrinters(): array;

    /**
     * 检查打印机状态
     *
     * @param string $printerName 打印机名称
     * @return array
     */
    public function getPrinterStatus(string $printerName): array;

    /**
     * 设置默认打印机
     *
     * @param string $printerName 打印机名称
     * @return bool
     */
    public function setDefaultPrinter(string $printerName): bool;

    /**
     * 预览打印内容
     *
     * @param string $content 内容
     * @param array $options 选项
     * @return string
     */
    public function preview(string $content, array $options = []): string;

    /**
     * 生成C-Lodop打印脚本
     *
     * @param string $content 打印内容
     * @param array $options 打印选项
     * @return string
     */
    public function generatePrintScript(string $content, array $options = []): string;

    /**
     * 生成小票打印脚本
     *
     * @param string $content 打印内容
     * @param array $options 打印选项
     * @return string
     */
    public function generateReceiptScript(string $content, array $options = []): string;
} 