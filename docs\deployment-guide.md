# Laravel 10 部署指南

## 宝塔面板部署步骤

### 1. 环境配置
1. 登录宝塔面板
2. 软件商店安装：
   - Nginx 1.22+
   - PHP 8.1+
   - MySQL 5.7+
   - Redis（可选）
   - Supervisor（用于队列）

### 2. 创建网站
1. 网站 -> 添加站点
2. 填写域名、选择PHP版本8.1
3. 创建MySQL数据库

### 3. 上传代码
```bash
# 方式1：使用Git
cd /www/wwwroot/your-domain
git clone https://github.com/your-repo/laravel-project.git .

# 方式2：使用宝塔文件管理器上传压缩包
```

### 4. 配置项目
```bash
# 进入项目目录
cd /www/wwwroot/your-domain

# 安装依赖
composer install --optimize-autoloader --no-dev

# 复制环境配置
cp .env.example .env

# 生成应用密钥
php artisan key:generate

# 配置.env文件
nano .env
```

### 5. 配置.env文件
```env
APP_NAME="天心食品"
APP_ENV=production
APP_KEY=base64:your-generated-key
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

### 6. 设置权限
```bash
# 设置目录权限
chown -R www:www /www/wwwroot/your-domain
chmod -R 755 /www/wwwroot/your-domain
chmod -R 777 /www/wwwroot/your-domain/storage
chmod -R 777 /www/wwwroot/your-domain/bootstrap/cache
```

### 7. 配置Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /www/wwwroot/your-domain/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/tmp/php-cgi-81.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

### 8. 执行迁移
```bash
# 运行数据库迁移
php artisan migrate --force

# 运行数据填充（如果有）
php artisan db:seed --force

# 创建存储链接
php artisan storage:link
```

### 9. 配置队列（如果使用）
```bash
# 安装Supervisor
# 在宝塔面板 -> 软件商店 -> Supervisor

# 添加守护进程
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /www/wwwroot/your-domain/artisan queue:work --sleep=3 --tries=3
autostart=true
autorestart=true
user=www
numprocs=4
redirect_stderr=true
stdout_logfile=/www/wwwroot/your-domain/storage/logs/worker.log
```

### 10. 配置定时任务
```bash
# 宝塔面板 -> 计划任务 -> 添加任务
# 任务类型：Shell脚本
# 任务名称：Laravel Schedule
# 执行周期：每分钟
# 脚本内容：
cd /www/wwwroot/your-domain && php artisan schedule:run >> /dev/null 2>&1
```

## 使用Docker部署

### 1. 创建Dockerfile
```dockerfile
FROM php:8.1-fpm

# 安装依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    nginx \
    supervisor

# 安装PHP扩展
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# 安装Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# 设置工作目录
WORKDIR /var/www

# 复制项目文件
COPY . .

# 安装项目依赖
RUN composer install --optimize-autoloader --no-dev

# 设置权限
RUN chown -R www-data:www-data /var/www \
    && chmod -R 755 /var/www/storage

# 暴露端口
EXPOSE 80

CMD ["php-fpm"]
```

### 2. 创建docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: laravel-app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - laravel

  nginx:
    image: nginx:alpine
    container_name: laravel-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx/conf.d/:/etc/nginx/conf.d/
    networks:
      - laravel

  mysql:
    image: mysql:8.0
    container_name: laravel-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USERNAME}
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - laravel

  redis:
    image: redis:alpine
    container_name: laravel-redis
    restart: unless-stopped
    networks:
      - laravel

networks:
  laravel:
    driver: bridge

volumes:
  mysql-data:
    driver: local
```

### 3. 启动Docker
```bash
# 构建并启动容器
docker-compose up -d

# 执行迁移
docker-compose exec app php artisan migrate

# 查看日志
docker-compose logs -f
```

## 云服务器一键部署

### 1. 使用Laravel Forge
- 支持自动部署
- SSL证书管理
- 队列和定时任务配置
- 适合AWS、DigitalOcean等

### 2. 使用Vercel/Netlify（Serverless）
```json
// vercel.json
{
  "version": 2,
  "functions": {
    "api/index.php": { "runtime": "vercel-php@0.6.0" }
  },
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/api/index.php"
    }
  ]
}
```

## 部署后优化

### 1. 启用OPcache
```ini
; php.ini
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=60
```

### 2. 配置Redis缓存
```php
// config/cache.php
'default' => env('CACHE_DRIVER', 'redis'),
```

### 3. 启用HTTPS
```bash
# 使用Let's Encrypt
certbot --nginx -d your-domain.com
```

### 4. 监控和日志
```bash
# 安装Laravel Telescope（开发环境）
composer require laravel/telescope --dev

# 配置日志通道
LOG_CHANNEL=daily
```

## 常见问题解决

### 1. 500错误
```bash
# 检查日志
tail -f storage/logs/laravel.log

# 重新生成缓存
php artisan config:clear
php artisan cache:clear
php artisan view:clear
```

### 2. 权限问题
```bash
# 修复权限
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 775 storage bootstrap/cache
```

### 3. 数据库连接失败
- 检查.env配置
- 确认MySQL服务运行
- 检查防火墙设置 