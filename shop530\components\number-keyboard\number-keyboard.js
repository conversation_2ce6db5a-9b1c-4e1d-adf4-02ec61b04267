/**
 * 数字键盘组件
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示键盘
    show: {
      type: Boolean,
      value: false
    },
    // 商品信息
    product: {
      type: Object,
      value: {}
    },
    // 默认数量
    defaultQuantity: {
      type: Number,
      value: 0
    },
    // 最大数量限制
    maxQuantity: {
      type: Number,
      value: 999
    },
    // 最小数量限制
    minQuantity: {
      type: Number,
      value: 1
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentQuantity: 0
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
      // 阻止点击事件冒泡到遮罩层
    },

    /**
     * 阻止默认滚动行为
     */
    preventDefault(e) {
      e.preventDefault();
    },

    /**
     * 遮罩层点击事件
     */
    onMaskTap() {
      this.hideKeyboard();
    },

    /**
     * 关闭按钮点击事件
     */
    onClose() {
      this.hideKeyboard();
    },

    /**
     * 数字按键点击事件
     */
    onNumberTap(e) {
      const number = e.currentTarget.dataset.number;
      if (number === undefined || number === null) return;
      
      this.inputNumber(number);
    },

    /**
     * 清除按钮点击事件
     */
    onClear() {
      this.setData({
        currentQuantity: 0
      });
    },

    /**
     * 删除按钮点击事件
     */
    onDelete() {
      const currentQuantity = this.data.currentQuantity;
      if (currentQuantity > 0) {
        const newQuantity = Math.floor(currentQuantity / 10);
        this.setData({
          currentQuantity: newQuantity
        });
      }
    },

    /**
     * 确认按钮点击事件
     */
    onConfirm() {
      const quantity = this.data.currentQuantity;
      const product = this.properties.product;
      
      // 验证商品信息
      if (!product || !product.id) {
        console.error('❌ 数字键盘确认时商品信息无效:', product);
        wx.showToast({
          title: '商品信息不完整',
          icon: 'none'
        });
        return;
      }
      
      // 验证数量
      if (quantity < this.properties.minQuantity) {
        wx.showToast({
          title: `最少购买${this.properties.minQuantity}件`,
          icon: 'none'
        });
        return;
      }
      
      if (quantity > this.properties.maxQuantity) {
        wx.showToast({
          title: `最多购买${this.properties.maxQuantity}件`,
          icon: 'none'
        });
        return;
      }
      
      // 确保传递完整的商品对象
      console.log('🔢 数字键盘确认:', product.name, '数量:', quantity);
      
      // 触发确认事件
      this.triggerEvent('confirm', {
        product: product,
        quantity: quantity
      });
      
      // 隐藏键盘
      this.hideKeyboard();
    },

    /**
     * 输入数字
     */
    inputNumber(number) {
      const currentQuantity = this.data.currentQuantity;
      let newQuantity = currentQuantity * 10 + parseInt(number);
      
      // 限制最大数量
      if (newQuantity > this.properties.maxQuantity) {
        newQuantity = this.properties.maxQuantity;
        wx.showToast({
          title: `最多购买${this.properties.maxQuantity}件`,
          icon: 'none'
        });
      }
      
      this.setData({
        currentQuantity: newQuantity
      });
    },

    /**
     * 显示键盘
     */
    showKeyboard() {
      // 重置数量为默认值
      this.setData({
        currentQuantity: this.properties.defaultQuantity || 0
      });
      
      // 触发显示事件
      this.triggerEvent('show');
    },

    /**
     * 隐藏键盘
     */
    hideKeyboard() {
      // 触发隐藏事件
      this.triggerEvent('hide');
    },

    /**
     * 设置数量（外部调用）
     */
    setQuantity(quantity) {
      const newQuantity = Math.max(0, Math.min(quantity, this.properties.maxQuantity));
      this.setData({
        currentQuantity: newQuantity
      });
    },

    /**
     * 获取当前数量（外部调用）
     */
    getQuantity() {
      return this.data.currentQuantity;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    /**
     * 组件实例刚刚被创建好时
     */
    created() {
      console.log('🎹 数字键盘组件创建');
    },

    /**
     * 组件实例进入页面节点树时
     */
    attached() {
      console.log('🎹 数字键盘组件加载');
      
      // 初始化数量
      this.setData({
        currentQuantity: this.properties.defaultQuantity || 0
      });
    },

    /**
     * 组件实例被从页面节点树移除时
     */
    detached() {
      console.log('🎹 数字键盘组件卸载');
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    /**
     * 组件所在的页面被展示时
     */
    show() {
      // 页面显示时的逻辑
    },

    /**
     * 组件所在的页面被隐藏时
     */
    hide() {
      // 页面隐藏时自动关闭键盘
      if (this.properties.show) {
        this.hideKeyboard();
      }
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'show': function(show) {
      if (show) {
        // 显示时重置数量
        this.setData({
          currentQuantity: this.properties.defaultQuantity || 0
        });
      }
    },

    'defaultQuantity': function(defaultQuantity) {
      // 默认数量变化时更新当前数量
      this.setData({
        currentQuantity: defaultQuantity || 0
      });
    }
  }
}); 