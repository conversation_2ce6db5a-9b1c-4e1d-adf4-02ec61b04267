<?php

namespace App\Employee\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Employee\Models\Employee;
use App\Employee\Api\ApiResponse;

/**
 * 员工角色检查中间件
 * 
 * 用于检查当前登录员工是否拥有访问特定路由的权限
 * 基于员工表中的role字段进行权限判断
 */
class CheckEmployeeRole
{
    /**
     * 处理传入的请求，检查当前员工是否拥有指定的角色权限
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|array  $roles 要检查的角色，可以是单个角色字符串或多个角色的数组
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        // 从sanctum获取当前登录的员工
        $employee = $request->user('sanctum');
        
        if (!$employee || !($employee instanceof Employee)) {
            if ($request->expectsJson()) {
                return response()->json(ApiResponse::error('未授权访问，请先以员工身份登录', 401), 401);
            }
            
            // 重定向到员工登录页面
            return redirect()->route('employee.login');
        }
        
        // 管理员和经理拥有所有权限，可以绕过角色检查
        if (in_array($employee->role, [Employee::ROLE_ADMIN, Employee::ROLE_MANAGER])) {
            return $next($request);
        }
        
        // 检查员工角色是否在指定的角色列表中
        if (!in_array($employee->role, $roles)) {
            if ($request->expectsJson()) {
                return response()->json(ApiResponse::error('无权访问，需要' . implode('或', $roles) . '角色权限', 403), 403);
            }
            
            // 根据角色获取适当的重定向页面
            $redirectTo = $this->getRedirectPageByRole($employee->role);
            return redirect()->route($redirectTo)->with('error', '无权访问此页面');
        }
        
        return $next($request);
    }
    
    /**
     * 根据员工角色获取适当的重定向页面
     * 
     * @param string $role 员工角色
     * @return string 重定向路由名称
     */
    private function getRedirectPageByRole(string $role): string
    {
        switch ($role) {
            case Employee::ROLE_ADMIN:
            case Employee::ROLE_MANAGER:
            case Employee::ROLE_STAFF:
                return 'admin.dashboard';
            case Employee::ROLE_CRM_AGENT:
                return 'crm.dashboard';
            case Employee::ROLE_DELIVERY:
                return 'delivery.dashboard';
            case Employee::ROLE_WAREHOUSE_MANAGER:
                return 'warehouse.dashboard';
            default:
                return 'employee.login';
        }
    }
} 