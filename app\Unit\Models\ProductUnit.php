<?php

namespace App\Unit\Models;

use App\Product\Models\Product;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductUnit extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id', 'unit_id', 'is_default', 
        'conversion_factor', 'roles', 'role_priority', 'is_active'
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'is_active' => 'boolean',
        'conversion_factor' => 'decimal:10',
        'roles' => 'json',
        'role_priority' => 'json'
    ];

    /**
     * 关联产品
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 关联单位
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * 判断单位是否具有指定角色
     *
     * @param string $role 角色名称
     * @return bool
     */
    public function hasRole(string $role): bool
    {
        if (!$this->roles) {
            return false;
        }
        
        $roles = is_array($this->roles) ? $this->roles : json_decode($this->roles, true);
        return in_array($role, $roles);
    }

    /**
     * 获取角色优先级
     *
     * @param string $role 角色名称
     * @return int 优先级值，值越小优先级越高
     */
    public function getRolePriority(string $role): int
    {
        if (!$this->role_priority) {
            return 999; // 默认最低优先级
        }
        
        $priorities = is_array($this->role_priority) ? $this->role_priority : json_decode($this->role_priority, true);
        return $priorities[$role] ?? 999;
    }

    /**
     * 获取单位转换值
     *
     * @param float $value 值
     * @param Unit|null $toUnit 目标单位
     * @return float 转换后的值
     */
    public function convert($value, ?Unit $toUnit = null)
    {
        if (!$toUnit) {
            return $value;
        }

        return $this->unit->convertValue($value, $toUnit);
    }

    /**
     * 为兼容性提供旧的转换率属性
     * 
     * @deprecated 请使用conversion_factor
     * @return float
     */
    public function getConversionRateAttribute()
    {
        return $this->conversion_factor;
    }
    
    /**
     * 为兼容性设置旧的转换率属性
     * 
     * @deprecated 请使用conversion_factor
     * @param float $value
     */
    public function setConversionRateAttribute($value)
    {
        $this->attributes['conversion_factor'] = $value;
    }
} 