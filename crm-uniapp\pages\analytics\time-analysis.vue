<template>
	<view class="time-analysis-container">
		<!-- 页面头部 -->
		<view class="header-section">
			<view class="header-title">
				<text class="title-text">时间分析</text>
				<text class="subtitle-text">用户活跃时段分析</text>
			</view>
		</view>

		<!-- 今日活跃概览 -->
		<view class="today-overview">
			<view class="section-title">
				<text>📅 今日活跃概览</text>
			</view>
			<view class="overview-stats">
				<view class="stat-item">
					<text class="stat-number">{{ timeData.today_peak_hour || '--' }}</text>
					<text class="stat-label">高峰时段</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ timeData.today_active_users || 0 }}</text>
					<text class="stat-label">活跃用户</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ timeData.avg_session_duration || 0 }}m</text>
					<text class="stat-label">平均时长</text>
				</view>
			</view>
		</view>

		<!-- 24小时活跃分布 -->
		<view class="hourly-activity">
			<view class="section-title">
				<text>🕐 24小时活跃分布</text>
			</view>
			<view class="hour-chart">
				<view class="hour-item" v-for="hour in timeData.hourly_stats" :key="hour.hour">
					<view class="hour-bar">
						<view class="bar-fill" :style="{ height: getHourBarHeight(hour.users) }"></view>
					</view>
					<text class="hour-label">{{ hour.hour }}:00</text>
					<text class="hour-users">{{ hour.users }}</text>
				</view>
			</view>
		</view>

		<!-- 一周活跃趋势 -->
		<view class="weekly-trend">
			<view class="section-title">
				<text>📊 一周活跃趋势</text>
			</view>
			<view class="week-chart">
				<view class="day-item" v-for="day in timeData.weekly_stats" :key="day.day">
					<view class="day-bar">
						<view class="bar-fill" :style="{ height: getDayBarHeight(day.users) }"></view>
					</view>
					<text class="day-label">{{ getDayName(day.day) }}</text>
					<text class="day-users">{{ day.users }}</text>
				</view>
			</view>
		</view>

		<!-- 用户行为时段分析 -->
		<view class="behavior-periods">
			<view class="section-title">
				<text>⏰ 用户行为时段分析</text>
			</view>
			<view class="period-list">
				<view class="period-item" v-for="period in timeData.behavior_periods" :key="period.name">
					<view class="period-info">
						<text class="period-name">{{ period.name }}</text>
						<text class="period-time">{{ period.time_range }}</text>
					</view>
					<view class="period-stats">
						<text class="period-users">{{ period.users }}人</text>
						<text class="period-behavior">{{ period.main_behavior }}</text>
					</view>
					<view class="period-percentage">
						<text class="percentage-text">{{ period.percentage }}%</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 活跃度热力图 -->
		<view class="heatmap-section">
			<view class="section-title">
				<text>🔥 活跃度热力图</text>
			</view>
			<view class="heatmap-grid">
				<view class="heatmap-row" v-for="(row, rowIndex) in timeData.heatmap" :key="rowIndex">
					<text class="row-label">{{ getRowLabel(rowIndex) }}</text>
					<view class="heatmap-cells">
						<view class="heatmap-cell" 
							  v-for="(cell, cellIndex) in row" 
							  :key="cellIndex"
							  :class="getHeatmapCellClass(cell.intensity)">
						</view>
					</view>
				</view>
			</view>
			<view class="heatmap-legend">
				<text class="legend-label">活跃度：</text>
				<view class="legend-items">
					<view class="legend-item low"></view>
					<view class="legend-item medium"></view>
					<view class="legend-item high"></view>
					<view class="legend-item very-high"></view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
import analyticsApi from '../../api/analytics.js'

export default {
	data() {
		return {
			timeData: {
				today_peak_hour: null,
				today_active_users: 0,
				avg_session_duration: 0,
				hourly_stats: [],
				weekly_stats: [],
				behavior_periods: [],
				heatmap: []
			},
			loading: false
		}
	},
	
	onLoad() {
		this.loadTimeAnalysis()
	},
	
	onPullDownRefresh() {
		this.refreshData()
	},
	
	methods: {
		// 加载时间分析数据
		async loadTimeAnalysis() {
			this.loading = true
			try {
				const response = await analyticsApi.getTimeAnalysis()
				this.timeData = response.data || {}
			} catch (error) {
				console.error('加载时间分析数据失败:', error)
				uni.showToast({
					title: '加载数据失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 刷新数据
		async refreshData() {
			await this.loadTimeAnalysis()
			uni.stopPullDownRefresh()
		},
		
		// 获取小时柱状图高度
		getHourBarHeight(users) {
			if (!this.timeData.hourly_stats || this.timeData.hourly_stats.length === 0) return '0%'
			const maxUsers = Math.max(...this.timeData.hourly_stats.map(h => h.users))
			const percentage = maxUsers > 0 ? (users / maxUsers) * 100 : 0
			return `${Math.max(percentage, 5)}%`
		},
		
		// 获取天柱状图高度
		getDayBarHeight(users) {
			if (!this.timeData.weekly_stats || this.timeData.weekly_stats.length === 0) return '0%'
			const maxUsers = Math.max(...this.timeData.weekly_stats.map(d => d.users))
			const percentage = maxUsers > 0 ? (users / maxUsers) * 100 : 0
			return `${Math.max(percentage, 5)}%`
		},
		
		// 获取星期名称
		getDayName(day) {
			const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
			return dayNames[day] || day
		},
		
		// 获取热力图行标签
		getRowLabel(rowIndex) {
			const labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
			return labels[rowIndex] || ''
		},
		
		// 获取热力图单元格样式类
		getHeatmapCellClass(intensity) {
			if (intensity >= 0.8) return 'very-high'
			if (intensity >= 0.6) return 'high'
			if (intensity >= 0.3) return 'medium'
			return 'low'
		}
	}
}
</script>

<style scoped>
.time-analysis-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 页面头部 */
.header-section {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx 32rpx 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	color: #ffffff;
}

.header-title {
	flex: 1;
}

.title-text {
	font-size: 40rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
}

.subtitle-text {
	font-size: 28rpx;
	opacity: 0.8;
}

/* 今日概览 */
.today-overview {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 24rpx;
}

.overview-stats {
	display: flex;
	gap: 24rpx;
}

.stat-item {
	flex: 1;
	text-align: center;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.stat-number {
	display: block;
	font-size: 32rpx;
	font-weight: 700;
	color: #007AFF;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666666;
}

/* 24小时活跃分布 */
.hourly-activity {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.hour-chart {
	display: flex;
	gap: 8rpx;
	overflow-x: auto;
	padding-bottom: 16rpx;
}

.hour-item {
	flex-shrink: 0;
	width: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.hour-bar {
	height: 120rpx;
	width: 24rpx;
	background: #f0f0f0;
	border-radius: 12rpx;
	display: flex;
	align-items: flex-end;
	margin-bottom: 8rpx;
}

.bar-fill {
	width: 100%;
	background: linear-gradient(180deg, #007AFF 0%, #5856D6 100%);
	border-radius: 12rpx;
	transition: height 0.3s ease;
}

.hour-label {
	font-size: 20rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.hour-users {
	font-size: 20rpx;
	color: #333333;
	font-weight: 600;
}

/* 一周活跃趋势 */
.weekly-trend {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.week-chart {
	display: flex;
	gap: 16rpx;
}

.day-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.day-bar {
	height: 100rpx;
	width: 32rpx;
	background: #f0f0f0;
	border-radius: 16rpx;
	display: flex;
	align-items: flex-end;
	margin-bottom: 12rpx;
}

.day-label {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 8rpx;
}

.day-users {
	font-size: 24rpx;
	color: #333333;
	font-weight: 600;
}

/* 用户行为时段分析 */
.behavior-periods {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.period-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.period-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.period-info {
	flex: 1;
}

.period-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.period-time {
	font-size: 24rpx;
	color: #666666;
}

.period-stats {
	text-align: center;
	margin: 0 24rpx;
}

.period-users {
	font-size: 28rpx;
	font-weight: 600;
	color: #007AFF;
	margin-bottom: 4rpx;
}

.period-behavior {
	font-size: 24rpx;
	color: #666666;
}

.period-percentage {
	width: 80rpx;
	text-align: center;
}

.percentage-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #28a745;
}

/* 活跃度热力图 */
.heatmap-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.heatmap-grid {
	margin-bottom: 24rpx;
}

.heatmap-row {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.row-label {
	width: 80rpx;
	font-size: 24rpx;
	color: #666666;
}

.heatmap-cells {
	display: flex;
	gap: 4rpx;
}

.heatmap-cell {
	width: 24rpx;
	height: 24rpx;
	border-radius: 4rpx;
}

.heatmap-cell.low {
	background: #f0f0f0;
}

.heatmap-cell.medium {
	background: #a8dadc;
}

.heatmap-cell.high {
	background: #457b9d;
}

.heatmap-cell.very-high {
	background: #1d3557;
}

.heatmap-legend {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
}

.legend-label {
	font-size: 24rpx;
	color: #666666;
}

.legend-items {
	display: flex;
	gap: 8rpx;
}

.legend-item {
	width: 20rpx;
	height: 20rpx;
	border-radius: 4rpx;
}

.legend-item.low {
	background: #f0f0f0;
}

.legend-item.medium {
	background: #a8dadc;
}

.legend-item.high {
	background: #457b9d;
}

.legend-item.very-high {
	background: #1d3557;
}

/* 加载状态 */
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}
</style> 