<?php

namespace App\shop\Providers;

use Illuminate\Support\ServiceProvider;
use App\shop\Services\ConfigService;
use App\shop\Services\Sms\AliyunSmsService;

class ShopServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     *
     * @return void
     */
    public function register()
    {
        // 注册ConfigService
        $this->app->singleton(ConfigService::class, function ($app) {
            return new ConfigService();
        });
        
        // 注册AliyunSmsService
        $this->app->singleton(AliyunSmsService::class, function ($app) {
            return new AliyunSmsService($app->make(ConfigService::class));
        });
    }

    /**
     * 引导服务
     *
     * @return void
     */
    public function boot()
    {
        // 加载路由
        $this->loadRoutesFrom(__DIR__.'/../routes/api.php');
        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');
    }
} 