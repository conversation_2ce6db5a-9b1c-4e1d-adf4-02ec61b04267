<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * 修复外键约束问题
     */
    public function up(): void
    {
        echo "=== 修复外键约束问题 ===\n";
        
        // 1. 修复无效的base_unit_id
        $this->fixInvalidBaseUnitIds();
        
        // 2. 重新创建外键约束
        $this->recreateForeignKeyConstraint();
        
        echo "=== 修复完成 ===\n";
    }
    
    /**
     * 修复无效的base_unit_id
     */
    private function fixInvalidBaseUnitIds(): void
    {
        echo "修复无效的base_unit_id...\n";
        
        // 获取默认单位ID
        $defaultUnit = DB::table('units')->where('name', 'LIKE', '%件%')->first();
        $defaultUnitId = $defaultUnit ? $defaultUnit->id : 13;
        
        echo "使用默认单位ID: {$defaultUnitId}\n";
        
        // 查找并修复无效的base_unit_id
        $invalidProducts = DB::select("
            SELECT p.id, p.base_unit_id 
            FROM products p 
            LEFT JOIN units u ON p.base_unit_id = u.id 
            WHERE p.base_unit_id IS NOT NULL AND u.id IS NULL
        ");
        
        if (count($invalidProducts) > 0) {
            echo "发现 " . count($invalidProducts) . " 个商品有无效的base_unit_id\n";
            
            foreach ($invalidProducts as $product) {
                echo "  - 商品ID {$product->id}: 无效单位ID {$product->base_unit_id} -> {$defaultUnitId}\n";
            }
            
            // 批量更新
            $updated = DB::update("
                UPDATE products p 
                LEFT JOIN units u ON p.base_unit_id = u.id 
                SET p.base_unit_id = ? 
                WHERE p.base_unit_id IS NOT NULL AND u.id IS NULL
            ", [$defaultUnitId]);
            
            echo "✅ 修复了 {$updated} 个商品的base_unit_id\n";
        } else {
            echo "✅ 没有发现无效的base_unit_id\n";
        }
    }
    
    /**
     * 重新创建外键约束
     */
    private function recreateForeignKeyConstraint(): void
    {
        echo "重新创建外键约束...\n";
        
        try {
            // 尝试创建外键约束
            Schema::table('products', function (Blueprint $table) {
                $table->foreign('base_unit_id')->references('id')->on('units')->onDelete('restrict');
            });
            echo "✅ 外键约束创建成功\n";
        } catch (Exception $e) {
            echo "❌ 外键约束创建失败: " . $e->getMessage() . "\n";
            echo "尝试检查剩余的数据问题...\n";
            
            // 检查是否还有问题
            $stillInvalid = DB::select("
                SELECT COUNT(*) as count 
                FROM products p 
                LEFT JOIN units u ON p.base_unit_id = u.id 
                WHERE p.base_unit_id IS NOT NULL AND u.id IS NULL
            ")[0]->count;
            
            echo "仍有 {$stillInvalid} 个无效记录\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['base_unit_id']);
        });
    }
}; 