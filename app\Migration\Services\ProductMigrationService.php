<?php

namespace App\Migration\Services;

use App\Product\Models\Product;
use App\Product\Models\Category;
use App\Inventory\Models\Inventory;
use App\Unit\Models\Unit;
use App\Warehouse\Models\Warehouse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Collection;
use Carbon\Carbon;

/**
 * 商品数据迁移服务
 * 支持从老系统迁移商品、分类、库存等数据到新系统
 */
class ProductMigrationService
{
    private $config;
    private $oldDb;
    private $stats;
    private $dryRun;
    private $migrationLog;

    public function __construct()
    {
        $this->config = config('migration');
        
        // 检查配置是否存在
        if (!$this->config) {
            throw new \Exception('迁移配置不存在，请确保config/migration.php文件已正确配置');
        }
        
        $this->setupOldDatabaseConnection();
        $this->initializeStats();
        $this->dryRun = $this->config['migration_settings']['dry_run'];
        $this->migrationLog = [];
    }

    /**
     * 设置老系统数据库连接
     */
    private function setupOldDatabaseConnection()
    {
        $oldConfig = $this->config['old_system'] ?? null;
        
        // 检查老系统配置是否存在
        if (!$oldConfig) {
            throw new \Exception('老系统数据库配置不存在，请检查migration配置文件');
        }
        
        config(['database.connections.old_system_db' => array_merge([
            'driver' => 'mysql',
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                \PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ], $oldConfig)]);

        $this->oldDb = DB::connection('old_system_db');
    }

    /**
     * 初始化统计数据
     */
    private function initializeStats()
    {
        $this->stats = [
            'start_time' => now(),
            'products' => ['total' => 0, 'success' => 0, 'failed' => 0, 'skipped' => 0],
            'categories' => ['total' => 0, 'success' => 0, 'failed' => 0],
            'inventory' => ['total' => 0, 'success' => 0, 'failed' => 0],
            'images' => ['total' => 0, 'success' => 0, 'failed' => 0],
            'errors' => [],
            'warnings' => [],
        ];
    }

    /**
     * 执行完整的数据迁移
     */
    public function migrateAll($options = [])
    {
        try {
            $this->log('info', '🚀 开始数据迁移', ['dry_run' => $this->dryRun]);

            // 步骤1: 数据预检查
            $this->preflightCheck();

            // 步骤2: 备份现有数据（如果需要）
            if ($this->config['migration_settings']['backup_before_migrate'] && !$this->dryRun) {
                $this->backupCurrentData();
            }

            // 步骤3: 迁移分类
            if ($options['migrate_categories'] ?? true) {
                $this->migrateCategories();
            }

            // 步骤4: 迁移商品基础信息
            if ($options['migrate_products'] ?? true) {
                $this->migrateProducts();
            }

            // 步骤5: 迁移库存数据
            if ($options['migrate_inventory'] ?? true) {
                $this->migrateInventory();
            }

            // 步骤6: 迁移商品图片
            if ($options['migrate_images'] ?? true) {
                $this->migrateImages();
            }

            // 步骤7: 数据完整性验证
            if ($this->config['migration_settings']['validate_data']) {
                $this->validateMigratedData();
            }

            $this->log('info', '✅ 数据迁移完成', $this->stats);
            
            return $this->generateMigrationReport();

        } catch (\Exception $e) {
            $this->log('error', '❌ 数据迁移失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 迁移前预检查
     */
    private function preflightCheck()
    {
        $this->log('info', '🔍 执行迁移前预检查');

        // 检查老系统数据库连接
        try {
            $oldProductsCount = $this->oldDb->table('products')->count();
            $this->log('info', "老系统商品总数: {$oldProductsCount}");
        } catch (\Exception $e) {
            throw new \Exception("无法连接到老系统数据库: " . $e->getMessage());
        }

        // 检查新系统必需的表和字段
        $requiredTables = ['products', 'categories', 'inventory', 'warehouses', 'units'];
        foreach ($requiredTables as $table) {
            if (!DB::getSchemaBuilder()->hasTable($table)) {
                throw new \Exception("新系统缺少必需的表: {$table}");
            }
        }

        // 检查默认数据
        $defaultWarehouse = Warehouse::first();
        if (!$defaultWarehouse) {
            throw new \Exception("新系统中没有仓库记录，请先创建至少一个仓库");
        }

        $defaultUnit = Unit::find($this->config['product_mapping']['unit_mapping']['default_unit_id']);
        if (!$defaultUnit) {
            throw new \Exception("默认单位不存在，请检查配置");
        }

        $this->log('info', '✅ 预检查通过');
    }

    /**
     * 迁移商品分类
     */
    private function migrateCategories()
    {
        $this->log('info', '📂 开始迁移商品分类');

        $categoryMapping = $this->config['product_mapping']['category_mapping'];
        if (!($categoryMapping['table'] ?? null)) {
            $this->log('warning', '跳过分类迁移：未配置分类表');
            return;
        }

        try {
            if (!$this->oldDb->getSchemaBuilder()->hasTable($categoryMapping['table'])) {
                $this->log('warning', "跳过分类迁移：老系统中不存在表 {$categoryMapping['table']}");
                return;
            }

            $oldCategories = $this->oldDb->table($categoryMapping['table'])->get();
            $this->stats['categories']['total'] = $oldCategories->count();

            $categoryIdMapping = []; // 老ID => 新ID映射

            foreach ($oldCategories as $oldCategory) {
                try {
                    $categoryData = [
                        'name' => $this->cleanString($oldCategory->{$categoryMapping['name_field']}),
                        'parent_id' => null, // 稍后处理父子关系
                        'sort' => $oldCategory->{$categoryMapping['sort_field'] ?? 'id'} ?? 0,
                        'status' => 1,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    if ($this->validateData('category', $categoryData)) {
                        if (!$this->dryRun) {
                            $newCategory = Category::create($categoryData);
                            $categoryIdMapping[$oldCategory->{$categoryMapping['id_field']}] = $newCategory->id;
                        }
                        $this->stats['categories']['success']++;
                    } else {
                        $this->stats['categories']['failed']++;
                    }

                } catch (\Exception $e) {
                    $this->log('error', "分类迁移失败", [
                        'old_id' => $oldCategory->{$categoryMapping['id_field']},
                        'error' => $e->getMessage()
                    ]);
                    $this->stats['categories']['failed']++;
                }
            }

            // 处理父子关系
            if (!$this->dryRun && ($categoryMapping['parent_field'] ?? null)) {
                $this->updateCategoryParentRelations($oldCategories, $categoryMapping, $categoryIdMapping);
            }

            $this->log('info', '✅ 分类迁移完成', $this->stats['categories']);

        } catch (\Exception $e) {
            $this->log('error', '❌ 分类迁移失败', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * 迁移商品基础信息
     */
    private function migrateProducts()
    {
        $this->log('info', '📦 开始迁移商品基础信息');

        try {
            $batchSize = $this->config['migration_settings']['batch_size'];
            $oldProducts = $this->oldDb->table('products');
            $total = $oldProducts->count();
            $this->stats['products']['total'] = $total;

            $this->log('info', "准备迁移 {$total} 个商品，批次大小: {$batchSize}");

            $oldProducts->orderBy('id')->chunk($batchSize, function ($products) {
                $this->processBatchProducts($products);
            });

            $this->log('info', '✅ 商品基础信息迁移完成', $this->stats['products']);

        } catch (\Exception $e) {
            $this->log('error', '❌ 商品迁移失败', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * 处理一批商品数据
     */
    private function processBatchProducts(Collection $products)
    {
        $fieldMapping = $this->config['product_mapping']['basic_fields'];
        $defaultValues = $this->config['product_mapping']['default_values'];

        foreach ($products as $oldProduct) {
            try {
                $productData = $defaultValues; // 从默认值开始

                // 映射基础字段
                foreach ($fieldMapping as $newField => $oldFieldConfig) {
                    if (is_array($oldFieldConfig)) {
                        // 复杂映射（如状态值转换）
                        $oldValue = $oldProduct->{$oldFieldConfig['field']};
                        $productData[$newField] = $oldFieldConfig['mapping'][$oldValue] ?? $oldValue;
                    } else {
                        // 简单字段映射
                        if (property_exists($oldProduct, $oldFieldConfig)) {
                            $productData[$newField] = $oldProduct->{$oldFieldConfig};
                        }
                    }
                }

                // 数据清理
                $productData = $this->cleanProductData($productData);

                // 处理商品代码唯一性
                $productData['code'] = $this->ensureUniqueProductCode($productData['code'] ?? null, $oldProduct->id);

                // 设置基本单位
                $productData['base_unit_id'] = $this->mapUnit($oldProduct);

                // 设置分类
                $productData['category_id'] = $this->mapCategory($oldProduct);

                // 设置时间戳
                $productData['created_at'] = $this->parseDateTime($oldProduct->created_at ?? now());
                $productData['updated_at'] = $this->parseDateTime($oldProduct->updated_at ?? now());

                // 验证数据
                if ($this->validateData('product', $productData)) {
                    if (!$this->dryRun) {
                        $newProduct = Product::create($productData);
                        $this->log('debug', "商品创建成功", [
                            'old_id' => $oldProduct->id,
                            'new_id' => $newProduct->id,
                            'code' => $newProduct->code
                        ]);
                    }
                    $this->stats['products']['success']++;
                } else {
                    $this->stats['products']['failed']++;
                }

            } catch (\Exception $e) {
                $this->log('error', "商品迁移失败", [
                    'old_id' => $oldProduct->id,
                    'error' => $e->getMessage()
                ]);
                $this->stats['products']['failed']++;

                if (!$this->config['migration_settings']['continue_on_error']) {
                    throw $e;
                }
            }
        }
    }

    /**
     * 迁移库存数据
     */
    private function migrateInventory()
    {
        $this->log('info', '📊 开始迁移库存数据');

        $inventoryMapping = $this->config['product_mapping']['inventory_mapping'];
        
        try {
            // 方式1: 如果老系统有独立的库存表
            if (($inventoryMapping['table'] ?? null) && $this->oldDb->getSchemaBuilder()->hasTable($inventoryMapping['table'])) {
                $this->migrateFromInventoryTable($inventoryMapping);
            } 
            // 方式2: 如果库存数据在商品表中
            else {
                $this->migrateFromProductTable($inventoryMapping);
            }

            $this->log('info', '✅ 库存数据迁移完成', $this->stats['inventory']);

        } catch (\Exception $e) {
            $this->log('error', '❌ 库存迁移失败', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * 从独立库存表迁移
     */
    private function migrateFromInventoryTable($mapping)
    {
        $oldInventories = $this->oldDb->table($mapping['table'])->get();
        $this->stats['inventory']['total'] = $oldInventories->count();

        foreach ($oldInventories as $oldInventory) {
            try {
                // 根据商品代码精确匹配（需要在配置中指定匹配策略）
                $oldProductId = $oldInventory->{$mapping['product_field']};
                
                // 方式1: 如果商品代码直接对应老系统ID
                $newProduct = Product::where('code', "MIGRATED_{$oldProductId}")->first();
                
                // 方式2: 如果有其他匹配字段，可以配置
                if (!$newProduct && isset($mapping['match_field'])) {
                    $matchValue = $oldInventory->{$mapping['match_field']};
                    $newProduct = Product::where($mapping['new_match_field'], $matchValue)->first();
                }

                if (!$newProduct) {
                    $this->log('warning', "找不到对应商品，跳过库存", ['old_product_id' => $oldProductId]);
                    $this->stats['inventory']['failed']++;
                    continue;
                }

                $inventoryData = [
                    'product_id' => $newProduct->id,
                    'warehouse_id' => $mapping['warehouse_default'],
                    'stock' => $this->normalizeStock($oldInventory->{$mapping['stock_field']}),
                    'unit_id' => $newProduct->base_unit_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                if ($this->validateData('inventory', $inventoryData)) {
                    if (!$this->dryRun) {
                        Inventory::create($inventoryData);
                    }
                    $this->stats['inventory']['success']++;
                } else {
                    $this->stats['inventory']['failed']++;
                }

            } catch (\Exception $e) {
                $this->log('error', "库存迁移失败", [
                    'old_inventory_id' => $oldInventory->id ?? 'unknown',
                    'error' => $e->getMessage()
                ]);
                $this->stats['inventory']['failed']++;
            }
        }
    }

    /**
     * 从商品表迁移库存
     */
    private function migrateFromProductTable($mapping)
    {
        $newProducts = Product::all();
        $this->stats['inventory']['total'] = $newProducts->count();

        foreach ($newProducts as $product) {
            try {
                // 为每个商品创建默认库存记录
                $inventoryData = [
                    'product_id' => $product->id,
                    'warehouse_id' => $mapping['warehouse_default'],
                    'stock' => 0, // 默认库存为0，后续可以手动调整
                    'unit_id' => $product->base_unit_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                if (!$this->dryRun) {
                    Inventory::create($inventoryData);
                }
                $this->stats['inventory']['success']++;

            } catch (\Exception $e) {
                $this->log('error', "创建默认库存失败", [
                    'product_id' => $product->id,
                    'error' => $e->getMessage()
                ]);
                $this->stats['inventory']['failed']++;
            }
        }
    }

    /**
     * 迁移商品图片
     */
    private function migrateImages()
    {
        $this->log('info', '🖼️ 开始迁移商品图片');

        $imageMapping = $this->config['product_mapping']['image_mapping'];
        if (!($imageMapping['table'] ?? null)) {
            $this->log('warning', '跳过图片迁移：未配置图片表');
            return;
        }

        try {
            if (!$this->oldDb->getSchemaBuilder()->hasTable($imageMapping['table'])) {
                $this->log('warning', "跳过图片迁移：老系统中不存在表 {$imageMapping['table']}");
                return;
            }

            $this->log('info', '图片迁移功能待完善 - 需要根据具体的图片存储方式实现');
            
        } catch (\Exception $e) {
            $this->log('error', '❌ 图片迁移失败', ['error' => $e->getMessage()]);
        }
    }

    // ========================== 辅助方法 ==========================

    private function cleanString($value)
    {
        if (!$this->config['data_cleaning']['trim_strings']) {
            return $value;
        }
        return trim($value ?? '');
    }

    private function cleanProductData($data)
    {
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                if ($this->config['data_cleaning']['trim_strings']) {
                    $data[$key] = trim($value);
                }
                if ($this->config['data_cleaning']['empty_to_null'] && $value === '') {
                    $data[$key] = null;
                }
            }
        }
        return $data;
    }

    private function ensureUniqueProductCode($code, $oldId)
    {
        if (empty($code) && $this->config['data_cleaning']['generate_missing_codes']) {
            $code = 'MIGRATED_' . $oldId;
        }

        if (empty($code)) {
            return 'MIGRATED_' . $oldId;
        }

        $originalCode = $code;
        $counter = 1;
        
        while (Product::where('code', $code)->exists()) {
            $code = $originalCode . '_' . $counter;
            $counter++;
        }

        return $code;
    }

    private function mapUnit($oldProduct)
    {
        // 根据配置映射单位，如果找不到则使用默认单位
        return $this->config['product_mapping']['unit_mapping']['default_unit_id'];
    }

    private function mapCategory($oldProduct)
    {
        // 这里需要根据你的分类映射逻辑来实现
        // 可以通过老系统的category_id查找对应的新分类ID
        return null; // 或者默认分类ID
    }

    private function parseDateTime($dateTime)
    {
        try {
            return Carbon::parse($dateTime);
        } catch (\Exception $e) {
            return now();
        }
    }

    private function normalizeStock($stock)
    {
        return max(0, (float)($stock ?? 0));
    }

    private function validateData($type, $data)
    {
        if (!$this->config['migration_settings']['validate_data']) {
            return true;
        }

        $rules = $this->config['validation_rules'][$type] ?? [];
        if (empty($rules)) {
            return true;
        }

        $validator = Validator::make($data, $rules);
        
        if ($validator->fails()) {
            $this->log('warning', "数据验证失败", [
                'type' => $type,
                'errors' => $validator->errors()->toArray(),
                'data' => $data
            ]);
            return false;
        }

        return true;
    }

    private function log($level, $message, $context = [])
    {
        Log::$level($message, $context);
        $this->migrationLog[] = [
            'time' => now()->toDateTimeString(),
            'level' => $level,
            'message' => $message,
            'context' => $context
        ];
        
        // 控制台输出
        $timestamp = now()->format('H:i:s');
        echo "[{$timestamp}] [{$level}] {$message}\n";
        
        if (!empty($context) && $level === 'error') {
            echo "Context: " . json_encode($context, JSON_UNESCAPED_UNICODE) . "\n";
        }
    }

    private function generateMigrationReport()
    {
        $this->stats['end_time'] = now();
        $this->stats['execution_time'] = $this->stats['end_time']->diffInSeconds($this->stats['start_time']);

        $report = [
            'summary' => $this->stats,
            'dry_run' => $this->dryRun,
            'log_entries' => count($this->migrationLog),
            'recommendations' => $this->generateRecommendations(),
        ];

        // 保存报告到文件
        $reportFile = storage_path('migration_reports/migration_' . now()->format('Y-m-d_H-i-s') . '.json');
        if (!is_dir(dirname($reportFile))) {
            mkdir(dirname($reportFile), 0755, true);
        }
        file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        $this->log('info', "迁移报告已保存: {$reportFile}");

        return $report;
    }

    private function generateRecommendations()
    {
        $recommendations = [];

        if ($this->stats['products']['failed'] > 0) {
            $recommendations[] = "有 {$this->stats['products']['failed']} 个商品迁移失败，请检查日志文件";
        }

        if ($this->stats['inventory']['total'] === 0) {
            $recommendations[] = "没有迁移库存数据，建议检查库存配置并手动调整库存";
        }

        if ($this->dryRun) {
            $recommendations[] = "这是试运行模式，没有实际修改数据。确认无误后请设置 dry_run = false";
        }

        return $recommendations;
    }

    // 更多辅助方法
    private function backupCurrentData()
    {
        $this->log('info', '💾 开始备份现有数据');
        
        $backupFile = storage_path('migration_backups/backup_' . now()->format('Y-m-d_H-i-s') . '.sql');
        if (!is_dir(dirname($backupFile))) {
            mkdir(dirname($backupFile), 0755, true);
        }
        
        // 这里可以实现数据库备份逻辑
        $this->log('info', "备份文件: {$backupFile}");
    }

    private function validateMigratedData()
    {
        $this->log('info', '✅ 验证迁移数据完整性');
        
        // 检查商品是否都有基本单位
        $productsWithoutUnit = Product::whereNull('base_unit_id')->count();
        if ($productsWithoutUnit > 0) {
            $this->log('warning', "发现 {$productsWithoutUnit} 个商品没有基本单位");
        }

        // 检查商品是否都有库存记录
        $productsWithoutInventory = Product::doesntHave('inventory')->count();
        if ($productsWithoutInventory > 0) {
            $this->log('warning', "发现 {$productsWithoutInventory} 个商品没有库存记录");
        }
    }

    private function updateCategoryParentRelations($oldCategories, $mapping, $idMapping)
    {
        foreach ($oldCategories as $oldCategory) {
            $parentId = $oldCategory->{$mapping['parent_field']};
            if ($parentId && isset($idMapping[$parentId])) {
                $newCategoryId = $idMapping[$oldCategory->{$mapping['id_field']}];
                Category::where('id', $newCategoryId)
                    ->update(['parent_id' => $idMapping[$parentId]]);
            }
        }
    }
} 