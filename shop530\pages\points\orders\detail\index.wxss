/* pages/points/orders/detail/index.wxss - 积分订单详情页样式 */

/* 主容器 */
.order-detail-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* ===== 订单状态 ===== */
.order-status-section {
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  padding: 60rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 30rpx;
  color: #fff;
}

.status-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-image {
  width: 60rpx;
  height: 60rpx;
}

.status-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.status-text {
  font-size: 36rpx;
  font-weight: 600;
}

.status-desc {
  font-size: 24rpx;
  opacity: 0.9;
}

/* ===== 通用卡片样式 ===== */
.order-info-section,
.products-section,
.shipping-section,
.cost-section,
.remarks-section {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

/* ===== 订单信息 ===== */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F8F8F8;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20rpx;
}

.order-no {
  font-family: monospace;
}

.copy-btn {
  background: #4CAF50;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

/* ===== 商品信息 ===== */
.product-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F8F8F8;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-spec {
  font-size: 22rpx;
  color: #999;
}

.product-price {
  display: flex;
  align-items: baseline;
  gap: 12rpx;
}

.points-price {
  font-size: 24rpx;
  color: #FF6B35;
  font-weight: 600;
}

.cash-price {
  font-size: 22rpx;
  color: #666;
}

.product-quantity {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #666;
}

/* ===== 配送信息 ===== */
.address-info {
  margin-bottom: 30rpx;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.receiver-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.receiver-phone {
  font-size: 26rpx;
  color: #666;
}

.address-detail {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.logistics-info {
  border-top: 1rpx solid #F0F0F0;
  padding-top: 30rpx;
}

.logistics-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.logistics-label {
  font-size: 26rpx;
  color: #666;
}

.logistics-value {
  font-size: 26rpx;
  color: #333;
}

/* ===== 费用明细 ===== */
.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F8F8F8;
}

.cost-item:last-child {
  border-bottom: none;
}

.cost-label {
  font-size: 26rpx;
  color: #666;
}

.cost-value {
  font-size: 26rpx;
  color: #333;
}

.cost-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-top: 2rpx solid #F0F0F0;
  margin-top: 20rpx;
}

.total-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.total-value {
  display: flex;
  align-items: baseline;
  gap: 12rpx;
}

.total-points {
  font-size: 32rpx;
  color: #FF6B35;
  font-weight: 600;
}

.total-cash {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* ===== 订单备注 ===== */
.remarks-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* ===== 加载状态 ===== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E0E0E0;
  border-top: 4rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/* ===== 底部操作栏 ===== */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #E0E0E0;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.action-btn {
  flex: 1;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
  padding: 24rpx 0;
  border: 1rpx solid #E0E0E0;
  background: #fff;
  color: #666;
  margin: 0;
}

.action-btn::after {
  border: none;
}

.action-btn.primary {
  background: #4CAF50;
  color: #fff;
  border-color: #4CAF50;
}

.action-btn.danger {
  background: #fff;
  color: #F44336;
  border-color: #F44336;
}

.action-btn.secondary {
  background: #fff;
  color: #2196F3;
  border-color: #2196F3;
} 