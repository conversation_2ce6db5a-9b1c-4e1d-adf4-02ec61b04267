<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->decimal('payment_discount', 10, 2)->default(0)->after('discount')->comment('支付方式优惠金额');
            $table->json('payment_discount_info')->nullable()->after('payment_discount')->comment('支付优惠详情（JSON格式）');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['payment_discount', 'payment_discount_info']);
        });
    }
};
