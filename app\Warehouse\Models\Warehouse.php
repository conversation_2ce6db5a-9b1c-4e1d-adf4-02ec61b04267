<?php

namespace App\Warehouse\Models;

use App\Product\Models\Product;
use App\Inventory\Models\Inventory;
use App\Inventory\Models\InventoryAlert;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Warehouse extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'location',
        'manager',
        'contact',
        'capacity',
        'used_capacity',
        'status',
        'description',
        'total_stock',
        // 库存策略字段
        'inventory_policy',
        'min_stock_threshold',
        'max_negative_stock',
        'auto_reorder',
        'reorder_point',
        'reorder_quantity',
        'track_inventory',
        'allow_oversell',
        'inventory_alerts',
        'priority',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'capacity' => 'decimal:2',
        'used_capacity' => 'decimal:2',
        'total_stock' => 'integer',
        'min_stock_threshold' => 'decimal:2',
        'max_negative_stock' => 'decimal:2',
        'reorder_point' => 'decimal:2',
        'reorder_quantity' => 'decimal:2',
        'auto_reorder' => 'boolean',
        'track_inventory' => 'boolean',
        'allow_oversell' => 'boolean',
        'inventory_alerts' => 'array',
        'priority' => 'integer',
    ];

    /**
     * 获取仓库所有库存记录
     */
    public function inventories()
    {
        return $this->hasMany(Inventory::class);
    }

    /**
     * 获取仓库中的所有产品
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'inventory')
            ->withPivot('stock', 'unit', 'min_stock_level')
            ->withTimestamps();
    }

    /**
     * 获取仓库的预警信息
     */
    public function alerts()
    {
        return $this->hasMany(InventoryAlert::class);
    }

    /**
     * 获取容量利用率
     */
    public function getUtilizationRateAttribute()
    {
        if ($this->capacity <= 0) {
            return 0;
        }
        return min(round(($this->used_capacity / $this->capacity) * 100), 100);
    }

    /**
     * 获取商品种类数量
     */
    public function getProductCountAttribute()
    {
        return $this->inventories()->count();
    }

    /**
     * 获取库存总价值
     */
    public function getTotalValueAttribute()
    {
        return $this->inventories()
            ->join('products', 'inventory.product_id', '=', 'products.id')
            ->sum(DB::raw('inventory.stock * products.price'));
    }

    /**
     * 获取活跃预警数量
     */
    public function getActiveAlertsCountAttribute()
    {
        return $this->alerts()->where('status', 'active')->count();
    }

    /**
     * 自动生成仓库编码
     */
    public static function generateCode()
    {
        $lastWarehouse = static::orderBy('id', 'desc')->first();
        $nextId = $lastWarehouse ? $lastWarehouse->id + 1 : 1;
        return 'WH' . str_pad($nextId, 3, '0', STR_PAD_LEFT);
    }

    /**
     * 更新已使用容量
     */
    public function updateUsedCapacity()
    {
        $this->used_capacity = $this->total_stock;
        $this->save();
    }

    /**
     * 检查仓库状态
     */
    public function checkStatus()
    {
        $utilizationRate = $this->utilization_rate;
        $alertsCount = $this->active_alerts_count;

        if ($alertsCount > 0 || $utilizationRate > 90) {
            $this->status = 'warning';
        } elseif ($utilizationRate < 10) {
            $this->status = 'inactive';
        } else {
            $this->status = 'active';
        }

        $this->save();
    }
} 