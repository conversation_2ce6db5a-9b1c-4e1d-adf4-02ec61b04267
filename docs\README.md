# 进销存系统API文档

## 介绍

这是一个基于Laravel开发的完整进销存系统API文档。系统提供了全面的单位管理、供应商管理、采购管理、库存管理和仓库管理功能，支持多单位管理和灵活的库存事务处理。

## 文档目录

API文档分为两个部分：

1. [第一部分](inventory-management-api-part1.md) - 包含以下模块：
   - 单位管理
   - 单位转换
   - 供应商管理
   - 采购订单管理

2. [第二部分](inventory-management-api-part2.md) - 包含以下模块：
   - 库存事务管理
     - 库存事务
     - 库存调整
     - 库存查询
     - 库存调拨
   - 仓库管理

## 系统特性

- **多单位管理系统**：支持自定义单位和单位转换，商品可以有不同的采购、销售和库存单位
- **完整的采购流程**：从采购订单创建、审批到收货的全流程管理
- **灵活的库存事务处理**：支持多种类型的库存事务，包括采购入库、销售出库、库存调整、库存调拨等
- **多仓库支持**：支持多仓库管理，可以在不同仓库之间进行库存调拨
- **详细的库存记录**：记录每笔库存变动的详细信息，便于追踪和审计

## 技术框架

- Laravel 10
- RESTful API设计
- JSON响应格式
- Bearer Token认证 