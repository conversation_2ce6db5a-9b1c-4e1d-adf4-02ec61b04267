<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 优化products表
        Schema::table('products', function (Blueprint $table) {
            // 1. 移除冗余的cover_url字段
            // 注: 这个操作可能导致一些依赖此字段的功能暂时不可用
            // 确保在运行此迁移前，所有相关代码都已更新为使用product_images表
            $table->dropColumn('cover_url');
            
            // 2. 添加索引以优化查询性能
            $table->index('status', 'idx_products_status');
            $table->index(['status', 'category_id'], 'idx_products_status_category');
            
            // 3. 添加其他有用的字段
            $table->decimal('sale_price', 10, 2)->nullable()->after('price')->comment('促销价格');
            $table->decimal('cost_price', 10, 2)->nullable()->after('price')->comment('成本价格');
            $table->timestamp('promoted_at')->nullable()->after('status')->comment('促销开始日期');
            $table->timestamp('promoted_end_at')->nullable()->after('promoted_at')->comment('促销结束日期');
            $table->bigInteger('sales_count')->default(0)->after('stock')->comment('销量');
            $table->bigInteger('views_count')->default(0)->after('sales_count')->comment('浏览次数');
            $table->tinyInteger('is_featured')->default(0)->after('status')->comment('是否推荐：0否，1是');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 1. 恢复cover_url字段
            $table->string('cover_url')->nullable()->after('description')->comment('封面图片URL');
            
            // 2. 移除添加的索引
            $table->dropIndex('idx_products_status');
            $table->dropIndex('idx_products_status_category');
            
            // 3. 移除新增字段
            $table->dropColumn([
                'sale_price',
                'cost_price',
                'promoted_at',
                'promoted_end_at',
                'sales_count',
                'views_count',
                'is_featured'
            ]);
        });
    }
}; 