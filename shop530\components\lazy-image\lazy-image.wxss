/* 图片懒加载组件样式 */

.lazy-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}

/* ========== 占位图样式 ========== */
.lazy-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 8rpx;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.lazy-image-placeholder.hidden {
  opacity: 0;
  pointer-events: none;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  padding: 20rpx;
  text-align: center;
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  text-align: center;
  line-height: 1.4;
}

/* 占位图片样式 */
.placeholder-image {
  width: 100%;
  height: 100%;
  opacity: 0.6;
  filter: grayscale(100%);
  transition: opacity 0.3s ease;
}

/* ========== 实际图片样式 ========== */
.lazy-image {
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease;
}

.lazy-image.hide {
  opacity: 0;
}

.lazy-image.show {
  opacity: 1;
}

.lazy-image.rounded {
  border-radius: 50%;
}

/* ========== 响应式预设尺寸 ========== */
.lazy-image-container.size-small {
  width: 80rpx;
  height: 80rpx;
}

.lazy-image-container.size-medium {
  width: 160rpx;
  height: 160rpx;
}

.lazy-image-container.size-large {
  width: 240rpx;
  height: 240rpx;
}

.lazy-image-container.size-banner {
  width: 100%;
  height: 300rpx;
}

.lazy-image-container.size-square {
  width: 100%;
  aspect-ratio: 1;
}

.lazy-image-container.size-product {
  width: 100%;
  aspect-ratio: 1.2;
}

/* ========== 圆角预设 ========== */
.lazy-image-container.radius-small {
  border-radius: 4rpx;
}

.lazy-image-container.radius-medium {
  border-radius: 8rpx;
}

.lazy-image-container.radius-large {
  border-radius: 16rpx;
}

.lazy-image-container.radius-round {
  border-radius: 50%;
}

/* ========== 阴影效果 ========== */
.lazy-image-container.shadow {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.lazy-image-container.shadow-light {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.lazy-image-container.shadow-heavy {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

/* ========== 边框样式 ========== */
.lazy-image-container.border {
  border: 2rpx solid #e0e0e0;
}

.lazy-image-container.border-primary {
  border: 2rpx solid #4CAF50;
}

/* ========== 特殊效果 ========== */
.lazy-image-container.grayscale .lazy-image {
  filter: grayscale(100%);
}

.lazy-image-container.blur .lazy-image {
  filter: blur(4rpx);
}

.lazy-image-container.brightness .lazy-image {
  filter: brightness(1.1);
}

/* ========== 悬停效果 ========== */
.lazy-image-container.hover-scale {
  transition: transform 0.3s ease;
}

.lazy-image-container.hover-scale:active {
  transform: scale(0.95);
}

.lazy-image-container.hover-opacity {
  transition: opacity 0.3s ease;
}

.lazy-image-container.hover-opacity:active {
  opacity: 0.8;
}

/* ========== 深色模式适配 ========== */
@media (prefers-color-scheme: dark) {
  .lazy-image-container {
    background: #2a2a2a;
  }
  
  .lazy-image-placeholder {
    background: #2a2a2a;
  }
  
  .placeholder-text {
    color: #666;
  }
}

.fade-in {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(245, 245, 245, 0.5);
  z-index: 1;
}

.loading-spinner {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid rgba(0, 0, 0, 0.1);
  border-top: 3rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  z-index: 3;
}

.error-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
}

.error-text {
  font-size: 24rpx;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
} 