<?php

namespace App\Console\Commands;

use App\Order\Models\PaymentLink;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupTimeoutPayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment:cleanup-timeout {--dry-run : 只显示将要处理的记录，不实际执行}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清理超时的支付处理状态';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始清理超时的支付处理状态...');

        // 查找超时的支付处理记录
        $timeoutPayments = PaymentLink::where('status', 'processing')
            ->where('updated_at', '<', now()->subMinutes(10))
            ->get();

        if ($timeoutPayments->isEmpty()) {
            $this->info('没有找到超时的支付处理记录');
            return 0;
        }

        $this->info("找到 {$timeoutPayments->count()} 个超时的支付处理记录");

        if ($this->option('dry-run')) {
            $this->warn('--dry-run 模式，只显示将要处理的记录：');
            
            $headers = ['ID', '订单号', '金额', '支付类型', '更新时间'];
            $rows = [];
            
            foreach ($timeoutPayments as $payment) {
                $rows[] = [
                    $payment->id,
                    $payment->order->order_no ?? 'N/A',
                    '¥' . number_format($payment->amount, 2),
                    $payment->payment_type_name,
                    $payment->updated_at->format('Y-m-d H:i:s'),
                ];
            }
            
            $this->table($headers, $rows);
            return 0;
        }

        $successCount = 0;
        $errorCount = 0;

        foreach ($timeoutPayments as $payment) {
            try {
                $payment->failPayment('支付处理超时，系统自动恢复');
                $successCount++;
                
                $this->line("✓ 处理成功: {$payment->id} (订单: {$payment->order->order_no})");
                
                Log::info('清理超时支付处理', [
                    'payment_link_id' => $payment->id,
                    'order_no' => $payment->order->order_no,
                    'timeout_minutes' => now()->diffInMinutes($payment->updated_at),
                ]);
                
            } catch (\Exception $e) {
                $errorCount++;
                $this->error("✗ 处理失败: {$payment->id} - {$e->getMessage()}");
                
                Log::error('清理超时支付处理失败', [
                    'payment_link_id' => $payment->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        $this->info("清理完成！成功: {$successCount}, 失败: {$errorCount}");

        return 0;
    }
} 