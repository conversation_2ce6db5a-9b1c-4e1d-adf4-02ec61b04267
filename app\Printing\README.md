# 打印模块 (Printing Module)

这是一个通用的打印模块，支持多种打印驱动，包括浏览器打印和CLodop打印控件，同时提供完整的打印状态管理和历史记录功能。

## 主要功能

### 1. 基础打印功能
- 浏览器打印和CLodop打印控件支持
- 订单小票打印和配送单打印
- 预览和脚本生成

### 2. 打印状态管理 ⭐ 核心功能
- 打印记录的持久化存储
- 打印状态跟踪（待打印、打印中、已完成、失败）
- 打印完成和失败回调
- 打印历史记录查询

### 3. 打印机管理
- 打印机检测和状态监控
- 默认打印机设置

## 支持的打印驱动

### 1. 浏览器打印 (Browser)
- 使用浏览器原生打印功能
- 适用于简单的文档打印

### 2. CLodop打印控件 (CLodop)
- 支持精确的打印控制
- 适用于需要精确布局的打印场景
- 支持自动检测打印机
- 支持打印预览和设置

## 打印状态管理

### 打印状态说明
- `pending` - 待打印：打印任务已创建，等待执行
- `printing` - 打印中：前端正在执行打印
- `completed` - 已完成：打印成功完成
- `failed` - 失败：打印过程中出现错误

### 工作流程
1. 调用打印API，系统创建打印记录
2. 前端接收打印脚本并执行打印
3. 前端根据打印结果调用完成或失败回调
4. 系统更新打印记录状态

## 环境配置

在 `.env` 文件中添加以下配置：

```env
# 打印配置
PRINTING_DRIVER=clodop
PRINTING_DEBUG=false

# CLodop打印配置
CLODOP_SERVICE_URL=http://localhost:8000/CLodopfuncs.js
CLODOP_LICENSE=
CLODOP_DEFAULT_PRINTER=
```

## 数据库迁移

运行以下命令创建打印记录表：

```bash
# 发布迁移文件
php artisan vendor:publish --tag=printing-migrations

# 运行迁移
php artisan migrate
```

## API接口

### 基础打印功能

#### 打印订单
```http
POST /api/print/order/{orderId}
Content-Type: application/json

{
    "driver": "clodop",
    "type": "normal",
    "copies": 1
}

Response:
{
    "success": true,
    "print_record_id": 123,
    "html": "...",
    "script": "...",
    "preview_url": "..."
}
```

#### 打印配送单
```http
POST /api/print/order/{orderId}/delivery
Content-Type: application/json

{
    "driver": "clodop",
    "copies": 1
}
```

#### 批量打印订单
```http
POST /api/print/orders/batch
Content-Type: application/json

{
    "order_ids": [1, 2, 3],
    "driver": "clodop",
    "type": "normal"
}
```

### 打印状态管理

#### 打印完成回调
```http
POST /api/print/completed
Content-Type: application/json

{
    "print_record_id": 123
}
```

#### 打印失败回调
```http
POST /api/print/failed
Content-Type: application/json

{
    "print_record_id": 123,
    "error_message": "打印机离线"
}
```

#### 批量状态回调
```http
POST /api/print/batch-callback
Content-Type: application/json

{
    "callbacks": [
        {
            "print_record_id": 123,
            "status": "completed"
        },
        {
            "print_record_id": 124,
            "status": "failed",
            "error_message": "纸张不足"
        }
    ]
}
```

#### 查询订单打印状态
```http
GET /api/print/order/{orderId}/status?print_type=receipt

Response:
{
    "success": true,
    "data": {
        "order_id": 123,
        "order_no": "20240101123456",
        "print_stats": {
            "total_prints": 2,
            "last_printed_at": "2024-01-01 12:34:56",
            "last_printed_by": "张三",
            "is_printed": true
        },
        "print_history": [...]
    }
}
```

#### 重新打印订单
```http
POST /api/print/order/{orderId}/reprint
Content-Type: application/json

{
    "type": "receipt",
    "allow_reprint": true
}
```

#### 获取待打印列表
```http
GET /api/print/pending

Response:
{
    "success": true,
    "data": [
        {
            "id": 123,
            "printable_type": "App\\Order\\Models\\Order",
            "printable_id": 456,
            "print_type": "receipt",
            "driver": "clodop",
            "created_at": "2024-01-01 12:34:56",
            "printable_info": {
                "type": "order",
                "order_no": "20240101123456"
            }
        }
    ]
}
```

#### 获取打印记录详情
```http
GET /api/print/record/{recordId}
```

### 打印机管理

#### 获取打印机列表
```http
GET /api/print/printers?driver=clodop
```

#### 获取打印机状态
```http
GET /api/print/printer/status?printer_sn=打印机编号&driver=clodop
```

#### 设置默认打印机
```http
POST /api/print/printer/default
Content-Type: application/json

{
    "printer_name": "打印机名称"
}
```

### 预览和脚本生成

#### 预览打印内容
```http
POST /api/print/preview
Content-Type: application/json

{
    "content": "打印内容",
    "driver": "clodop"
}
```

#### 生成打印脚本
```http
POST /api/print/script
Content-Type: application/json

{
    "content": "打印内容",
    "driver": "clodop",
    "type": "receipt"
}
```

## 使用示例

### PHP代码示例
```php
use App\Printing\Services\PrintingService;
use App\Order\Models\Order;

// 获取打印服务
$printingService = app(PrintingService::class);

// 设置CLodop驱动
$printingService->setDriver('clodop');

// 打印订单（会自动创建打印记录）
$order = Order::find(123);
$result = $printingService->printOrder($order, [
    'type' => 'receipt',
    'copies' => 1
]);

if ($result['success']) {
    $printRecordId = $result['print_record_id'];
    echo "打印任务已创建，记录ID: $printRecordId";
    
    // 前端需要根据打印结果调用回调
    // 成功：POST /api/print/completed {"print_record_id": $printRecordId}
    // 失败：POST /api/print/failed {"print_record_id": $printRecordId, "error_message": "错误信息"}
}

// 检查订单打印状态
$isPrinted = $order->isPrinted('receipt');
$printStats = $order->getPrintStats('receipt');
```

### JavaScript前端调用示例
```javascript
// 调用打印API
async function printOrder(orderId) {
    try {
        const response = await fetch(`/api/print/order/${orderId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                driver: 'clodop',
                type: 'receipt',
                copies: 1
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            const printRecordId = data.print_record_id;
            
            // 执行打印脚本
            try {
                // 这里执行具体的打印操作
                eval(data.script);
                
                // 打印成功，调用完成回调
                await markPrintCompleted(printRecordId);
                
            } catch (error) {
                // 打印失败，调用失败回调
                await markPrintFailed(printRecordId, error.message);
            }
        }
    } catch (error) {
        console.error('打印失败:', error);
    }
}

// 标记打印完成
async function markPrintCompleted(printRecordId) {
    await fetch('/api/print/completed', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            print_record_id: printRecordId
        })
    });
}

// 标记打印失败
async function markPrintFailed(printRecordId, errorMessage) {
    await fetch('/api/print/failed', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            print_record_id: printRecordId,
            error_message: errorMessage
        })
    });
}
```

## 文件结构

```
app/Printing/
├── Contracts/
│   └── PrintDriverInterface.php     # 打印驱动接口
├── Models/
│   └── PrintRecord.php              # 打印记录模型
├── Services/
│   ├── PrintingService.php          # 打印服务主类
│   └── Drivers/
│       ├── BrowserDriver.php        # 浏览器打印驱动
│       └── CLodopDriver.php         # CLodop打印驱动
├── Http/
│   └── Controllers/
│       └── PrintController.php      # 打印API控制器
├── Config/
│   └── printing.php                 # 打印配置文件
├── Providers/
│   └── PrintingServiceProvider.php  # 服务提供者
├── routes/
│   └── api.php                      # 路由配置
└── README.md                        # 说明文档

database/migrations/
└── 2024_01_03_000001_create_print_records_table.php
```

## 注意事项

1. **打印状态回调**：前端必须在打印完成或失败后调用相应的回调接口
2. **CLodop服务状态**：确保CLodop服务正常运行
3. **打印机连接**：确保打印机已正确连接并可用
4. **浏览器兼容性**：CLodop需要特定的浏览器插件支持
5. **权限设置**：某些浏览器可能需要用户授权打印权限
6. **重复打印控制**：可通过 `allow_reprint` 参数控制是否允许重复打印

## 故障排除

### 常见问题

1. **CLodop服务无法连接**
   - 检查CLodop服务是否启动
   - 确认服务端口配置正确
   - 检查防火墙设置

2. **打印机检测失败**
   - 确认打印机已正确安装
   - 检查打印机状态是否正常
   - 尝试重启CLodop服务

3. **打印状态未更新**
   - 检查前端是否正确调用回调接口
   - 确认打印记录ID是否正确
   - 查看API调用日志

4. **打印内容显示异常**
   - 检查HTML模板格式
   - 确认CSS样式设置
   - 调整打印页面设置

## 扩展开发

如需添加新的打印驱动，请：

1. 实现 `PrintDriverInterface` 接口
2. 在 `PrintingServiceProvider` 中注册新驱动
3. 在配置文件中添加驱动配置
4. 更新控制器验证规则

## 参考资料

- [CLodop官方文档](http://www.lodop.net/download.html)
- [浏览器打印API](https://developer.mozilla.org/en-US/docs/Web/API/Window/print) 