<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Points\Models\PointsRule;

// 引导Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\\Contracts\\Console\\Kernel')->bootstrap();

echo "🚀 开始创建积分规则...\n\n";

try {
    // 直接使用模型创建积分规则
    $defaultRules = [
        [
            'name' => '每日签到',
            'rule_type' => 'signin',
            'points_amount' => 10,
            'max_times_per_day' => 1,
            'max_times_total' => null,
            'status' => true,
            'description' => '每日签到获得10积分',
            'conditions' => null,
        ],
        [
            'name' => '邀请好友注册',
            'rule_type' => 'invite',
            'points_amount' => 100,
            'max_times_per_day' => 5,
            'max_times_total' => 50,
            'status' => true,
            'description' => '成功邀请好友注册获得100积分',
            'conditions' => json_encode(['require_friend_first_order' => true]),
        ],
        [
            'name' => '商品评价',
            'rule_type' => 'review',
            'points_amount' => 5,
            'max_times_per_day' => 10,
            'max_times_total' => null,
            'status' => true,
            'description' => '发表商品评价获得5积分',
            'conditions' => json_encode(['min_content_length' => 10]),
        ],
        [
            'name' => '分享商品',
            'rule_type' => 'share',
            'points_amount' => 2,
            'max_times_per_day' => 20,
            'max_times_total' => null,
            'status' => true,
            'description' => '分享商品到社交媒体获得2积分',
            'conditions' => null,
        ],
        [
            'name' => '生日奖励',
            'rule_type' => 'birthday',
            'points_amount' => 50,
            'max_times_per_day' => 1,
            'max_times_total' => null,
            'status' => true,
            'description' => '生日当天获得50积分奖励',
            'conditions' => null,
        ],
        [
            'name' => '会员升级奖励',
            'rule_type' => 'level_upgrade',
            'points_amount' => 200,
            'max_times_per_day' => null,
            'max_times_total' => null,
            'status' => true,
            'description' => '会员等级升级获得200积分奖励',
            'conditions' => json_encode(['min_level' => 2]),
        ],
    ];

    $created = 0;
    $updated = 0;

    foreach ($defaultRules as $ruleData) {
        $existing = PointsRule::where('rule_type', $ruleData['rule_type'])->first();
        
        if ($existing) {
            // 更新现有规则
            $existing->update($ruleData);
            echo "✅ 更新积分规则: {$ruleData['name']} ({$ruleData['points_amount']}积分)\n";
            $updated++;
        } else {
            // 创建新规则
            PointsRule::create($ruleData);
            echo "🆕 创建积分规则: {$ruleData['name']} ({$ruleData['points_amount']}积分)\n";
            $created++;
        }
    }

    echo "\n🎉 积分规则创建完成！\n";
    echo "📊 统计信息:\n";
    echo "   - 新创建: {$created} 个规则\n";
    echo "   - 更新: {$updated} 个规则\n";
    echo "   - 总计: " . ($created + $updated) . " 个规则\n\n";

    // 验证创建结果
    echo "🔍 验证积分规则:\n";
    $allRules = PointsRule::orderBy('rule_type')->get();
    foreach ($allRules as $rule) {
        $status = $rule->status ? '启用' : '禁用';
        echo "   - {$rule->name}: {$rule->points_amount}积分 [{$status}]\n";
    }

    echo "\n✨ 现在可以正常使用签到功能了！\n";

} catch (\Exception $e) {
    echo "❌ 创建积分规则失败: " . $e->getMessage() . "\n";
    echo "📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    exit(1);
} 