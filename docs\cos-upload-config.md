# 腾讯云COS上传配置指南

本文档介绍如何在Laravel项目中配置腾讯云对象存储（COS）以实现图片上传功能。

## 前提条件

1. 已经创建腾讯云账号并开通COS服务
2. 已经创建存储桶（Bucket）
3. 已经获取密钥（SecretId和SecretKey）

## 安装依赖包

```bash
composer require overtrue/laravel-filesystem-cos
```

## 配置说明

### 1. 在 `.env` 文件中添加以下配置

```
# 腾讯云COS配置
COS_APP_ID=你的应用ID
COS_SECRET_ID=你的SecretId
COS_SECRET_KEY=你的SecretKey
COS_REGION=ap-beijing  # 存储桶所在地域，例如：ap-beijing, ap-guangzhou
COS_BUCKET=your-bucket-name  # 存储桶名称
COS_CDN=  # 如果配置了CDN，填写CDN域名（例如 https://cdn.example.com）
COS_SCHEME=https  # 访问协议，http或https
COS_READ_FROM_CDN=false  # 是否优先从CDN读取
COS_PREFIX=  # 存储路径前缀，例如：shop1
```

### 2. 目录结构说明

系统的上传文件分类存储在不同目录，确保内容有良好的组织结构：

- `banners/{日期}` - 轮播图
- `products/{日期}` - 商品图片
- `categories/{日期}` - 分类图片
- `categories/icons/{日期}` - 分类图标
- `avatars/{日期}` - 用户头像
- `articles/{日期}` - 文章图片
- `attachments/{日期}/{类型}` - 附件文件

其中`{日期}`使用格式为`Ymd`，例如：`20231201`

### 3. 切换存储方式

默认情况下，上传服务使用COS存储。如果需要临时切换到本地存储，可以修改以下代码：

```php
// 使用COS存储
$uploadService = UploadService::cos();

// 或使用本地存储
$uploadService = UploadService::local();
```

## 最佳实践

1. **安全性配置**
   - 设置适当的存储桶访问权限，建议使用私有读写
   - 为存储桶配置CDN，通过CDN分发内容
   - 定期轮换密钥

2. **性能优化**
   - 开启CDN加速
   - 合理设置缓存策略
   - 开启图片处理功能进行自动压缩

3. **目录结构**
   - 按照日期和类型组织文件
   - 避免单个目录下文件过多
   - 使用UUID作为文件名避免冲突

## 故障排除

如果上传失败，请检查以下几点：

1. 确认密钥是否正确配置
2. 检查存储桶名称和地域是否一致
3. 存储桶是否有足够的权限
4. 查看Laravel日志了解详细错误信息 