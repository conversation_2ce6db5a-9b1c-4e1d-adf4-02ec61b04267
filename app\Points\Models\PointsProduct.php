<?php

namespace App\Points\Models;

use App\Models\User;
use App\Product\Models\Product;
use App\Crm\Models\MembershipLevel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PointsProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'name',
        'description',
        'image',
        'points_price',
        'cash_price',
        'stock_quantity',
        'exchange_type',
        'category',
        'status',
        'sort_order',
        'membership_level_id',
        'daily_limit',
        'total_limit',
        'start_time',
        'end_time',
        'exchanged_count',
    ];

    protected $casts = [
        'points_price' => 'integer',
        'cash_price' => 'decimal:2',
        'stock_quantity' => 'integer',
        'sort_order' => 'integer',
        'daily_limit' => 'integer',
        'total_limit' => 'integer',
        'exchanged_count' => 'integer',
        'status' => 'boolean',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    /**
     * 兑换类型常量
     */
    const EXCHANGE_TYPE_PURE_POINTS = 'pure_points';    // 纯积分兑换
    const EXCHANGE_TYPE_MIXED_PAYMENT = 'mixed_payment'; // 积分+现金

    /**
     * 商品分类常量
     */
    const CATEGORY_PHYSICAL = 'physical';   // 实物商品
    const CATEGORY_VIRTUAL = 'virtual';     // 虚拟商品
    const CATEGORY_COUPON = 'coupon';       // 优惠券

    /**
     * 关联普通商品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 关联会员等级限制
     */
    public function membershipLevel()
    {
        return $this->belongsTo(MembershipLevel::class);
    }

    /**
     * 关联积分订单项
     */
    public function orderItems()
    {
        return $this->hasMany(PointsOrderItem::class);
    }

    /**
     * 检查商品是否可兑换
     */
    public function isAvailable(): bool
    {
        // 检查状态
        if (!$this->status) {
            return false;
        }

        // 检查时间限制
        $now = now();
        if ($this->start_time && $now->lt($this->start_time)) {
            return false;
        }
        if ($this->end_time && $now->gt($this->end_time)) {
            return false;
        }

        // 检查库存
        if ($this->category === self::CATEGORY_PHYSICAL && $this->stock_quantity <= 0) {
            return false;
        }

        // 检查总限兑数量
        if ($this->total_limit && $this->exchanged_count >= $this->total_limit) {
            return false;
        }

        return true;
    }

    /**
     * 检查用户是否可以兑换此商品
     */
    public function canUserExchange(User $user): bool
    {
        if (!$this->isAvailable()) {
            return false;
        }

        // 检查会员等级限制
        if ($this->membership_level_id && $user->membership_level_id !== $this->membership_level_id) {
            return false;
        }

        // 检查用户积分是否足够
        if ($user->member_points < $this->points_price) {
            return false;
        }

        // 检查每日限兑
        if ($this->daily_limit) {
            $todayExchanged = PointsOrderItem::whereHas('order', function ($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->whereDate('created_at', today())
                      ->where('status', '!=', 'cancelled');
            })->where('points_product_id', $this->id)->sum('quantity');

            if ($todayExchanged >= $this->daily_limit) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取兑换类型文本
     */
    public function getExchangeTypeTextAttribute(): string
    {
        return match($this->exchange_type) {
            self::EXCHANGE_TYPE_PURE_POINTS => '纯积分兑换',
            self::EXCHANGE_TYPE_MIXED_PAYMENT => '积分+现金',
            default => '未知类型'
        };
    }

    /**
     * 获取商品分类文本
     */
    public function getCategoryTextAttribute(): string
    {
        return match($this->category) {
            self::CATEGORY_PHYSICAL => '实物商品',
            self::CATEGORY_VIRTUAL => '虚拟商品',
            self::CATEGORY_COUPON => '优惠券',
            default => '未知分类'
        };
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return $this->status ? '上架' : '下架';
    }

    /**
     * 扣减库存
     */
    public function decreaseStock(int $quantity = 1): bool
    {
        if ($this->category !== self::CATEGORY_PHYSICAL) {
            return true; // 虚拟商品不需要扣减库存
        }

        if ($this->stock_quantity < $quantity) {
            return false;
        }

        return $this->decrement('stock_quantity', $quantity) > 0;
    }

    /**
     * 增加兑换次数
     */
    public function increaseExchangedCount(int $quantity = 1): void
    {
        $this->increment('exchanged_count', $quantity);
    }

    /**
     * 作用域：可用商品
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', true)
                    ->where(function ($q) {
                        $q->whereNull('start_time')->orWhere('start_time', '<=', now());
                    })
                    ->where(function ($q) {
                        $q->whereNull('end_time')->orWhere('end_time', '>=', now());
                    });
    }

    /**
     * 作用域：按分类筛选
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * 作用域：按兑换类型筛选
     */
    public function scopeByExchangeType($query, string $exchangeType)
    {
        return $query->where('exchange_type', $exchangeType);
    }
} 