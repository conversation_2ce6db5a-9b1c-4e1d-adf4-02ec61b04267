# 微信支付服务商模式使用说明

本文档介绍了系统中实现的微信支付服务商模式的使用方法，包括后台配置、前端接入等。

## 一、概述

微信支付服务商模式是指第三方服务商为子商户提供支付接入服务的模式。服务商使用自己的微信支付商户号，为子商户提供支付接入和资金清算服务，同时可以收取一定的服务费。

本系统实现了完整的微信支付服务商模式流程，包括：
1. 服务商和子商户信息管理
2. 小程序支付创建
3. 支付状态查询
4. 申请退款
5. 支付和退款回调处理

## 二、配置流程

### 1. 服务商配置

服务商需在微信支付商户平台完成以下步骤：
- 开通服务商功能
- 下载API证书（apiclient_cert.pem 和 apiclient_key.pem）
- 设置支付和退款回调地址

在系统后台配置服务商信息：
- 登录后台 -> 微信支付服务商
- 点击「新增」，填写服务商信息：
  - 名称：服务商名称
  - 商户号：微信支付商户号
  - AppID：服务商公众号/小程序AppID
  - API密钥(v2)：微信支付API密钥
  - API密钥(v3)：微信支付API v3密钥（如启用v3接口）
  - 证书路径：上传apiclient_cert.pem
  - 证书密钥路径：上传apiclient_key.pem
  - 支付回调地址：如 https://example.com/api/wechat-payment/notify/pay
  - 退款回调地址：如 https://example.com/api/wechat-payment/notify/refund
  - 沙箱环境：是否启用沙箱环境
  - 状态：是否启用该服务商

### 2. 子商户配置

在系统后台配置子商户信息：
- 登录后台 -> 微信支付子商户
- 点击「新增」，填写子商户信息：
  - 服务商：选择已配置的服务商
  - 关联商户ID：如需关联系统中的商户，填入商户ID
  - 子商户名称：子商户名称
  - 子商户号：微信支付子商户号
  - 子商户AppID：如子商户有自己的小程序，填写其AppID
  - 服务费率：设置抽成比例，如0.006表示0.6%
  - 联系人、联系电话、联系邮箱：子商户联系信息
  - 营业执照信息：可填写营业执照号、法人等信息
  - 备注信息：其他备注
  - 状态：是否启用该子商户

## 三、接口使用说明

### 1. 创建小程序支付

**请求地址**：`/api/wechat-payment/mini-app-pay`  
**请求方式**：POST  
**请求参数**：
```json
{
  "order_id": "订单ID",
  "openid": "用户的微信openid",
  "sub_merchant_id": "子商户ID"
}
```

**返回示例**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "appId": "wx123456789abcdef",
    "timeStamp": "1623456789",
    "nonceStr": "abc123def456ghi789",
    "package": "prepay_id=wx123456789abcdef",
    "signType": "MD5",
    "paySign": "ABCDEF1234567890ABCDEF1234567890"
  }
}
```

### 2. 查询订单支付状态

**请求地址**：`/api/wechat-payment/order-status`  
**请求方式**：GET  
**请求参数**：
```
order_id=订单ID&sub_merchant_id=子商户ID
```

**返回示例**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "paid": true,
    "payment_method": "微信支付",
    "paid_at": "2023-06-01 12:34:56"
  }
}
```

### 3. 申请退款

退款需在管理后台操作，系统会自动计算应退还的服务费。

### 4. 回调通知

系统已实现自动处理微信支付和退款的回调通知，无需额外开发。回调处理流程：
1. 接收微信通知数据
2. 验证签名
3. 更新支付或退款记录状态
4. 触发相关业务逻辑（如更新订单状态）
5. 返回处理结果给微信

## 四、前端接入示例

### 微信小程序调用支付示例

```javascript
// 1. 发起创建支付请求
wx.request({
  url: 'https://example.com/api/wechat-payment/mini-app-pay',
  method: 'POST',
  data: {
    order_id: orderId,
    openid: wx.getStorageSync('openid'),
    sub_merchant_id: subMerchantId
  },
  success(res) {
    if (res.data.code === 0) {
      const payParams = res.data.data;
      
      // 2. 调用微信支付
      wx.requestPayment({
        ...payParams,
        success() {
          // 支付成功逻辑
          console.log('支付成功');
        },
        fail(err) {
          // 支付失败逻辑
          console.error('支付失败', err);
        },
        complete() {
          // 查询支付结果
          queryOrderStatus(orderId, subMerchantId);
        }
      });
    } else {
      wx.showToast({
        title: res.data.message || '创建支付失败',
        icon: 'none'
      });
    }
  },
  fail(err) {
    console.error('请求失败', err);
  }
});

// 查询订单支付状态
function queryOrderStatus(orderId, subMerchantId) {
  wx.request({
    url: `https://example.com/api/wechat-payment/order-status?order_id=${orderId}&sub_merchant_id=${subMerchantId}`,
    method: 'GET',
    success(res) {
      if (res.data.code === 0 && res.data.data.paid) {
        // 订单已支付，更新UI或跳转页面
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });
      } else {
        // 订单未支付，可以提示用户或重试
        wx.showToast({
          title: '正在确认支付结果...',
          icon: 'loading'
        });
        // 延迟重试
        setTimeout(() => {
          queryOrderStatus(orderId, subMerchantId);
        }, 1000);
      }
    }
  });
}
```

## 五、注意事项

1. 服务商收款资金需定期结算给子商户，请设置合理的结算周期。
2. 请确保API证书的安全存储和定期更新。
3. 服务费率设置应与子商户达成一致，并签订相应协议。
4. 应定期检查回调通知日志，确保支付和退款回调正常处理。
5. 系统会根据服务费率自动计算每笔交易的服务费，并在退款时自动计算应退还的服务费。

## 六、常见问题

1. **支付失败怎么处理？**  
   检查服务商和子商户配置是否正确，查看系统日志中的具体错误信息。

2. **回调通知没有收到怎么办？**  
   确认回调地址是否正确配置，检查服务器防火墙是否拦截了微信的回调请求。

3. **如何查看交易和服务费明细？**  
   在系统后台可以查看每笔交易的详细信息，包括交易金额、服务费等。

4. **支付成功但订单状态未更新？**  
   可能是回调处理出现问题，建议检查回调日志，必要时手动查询订单状态并更新。 