<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('category_region_prices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('category_id')->comment('分类ID');
            $table->unsignedBigInteger('region_id')->comment('区域ID');
            $table->enum('discount_type', ['fixed_amount', 'percentage'])->default('fixed_amount')->comment('优惠类型');
            $table->decimal('discount_value', 10, 2)->comment('优惠值');
            $table->decimal('max_discount', 10, 2)->nullable()->comment('最大优惠金额');
            $table->boolean('status')->default(true)->comment('启用状态');
            $table->datetime('start_time')->nullable()->comment('开始时间');
            $table->datetime('end_time')->nullable()->comment('结束时间');
            $table->text('description')->nullable()->comment('说明');
            
            $table->timestamps();
            
            // 索引
            $table->unique(['category_id', 'region_id']);
            $table->index(['category_id', 'status']);
            $table->index(['region_id', 'status']);
            $table->index(['start_time', 'end_time']);
            
            // 外键约束
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');
            $table->foreign('region_id')->references('id')->on('regions')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('category_region_prices');
    }
}; 