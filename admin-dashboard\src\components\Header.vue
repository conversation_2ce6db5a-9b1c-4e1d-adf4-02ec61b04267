<template>
  <div class="header">
    <!-- 左侧区域 -->
    <div class="header-left">
      <div class="system-info">
        <span class="system-name">天心食品配送系统</span>
        <span class="system-date">2025-07-16 最新时间</span>
      </div>
    </div>
    
    <!-- 右侧区域 -->
    <div class="header-right">
      <!-- 系统设置 -->
      <el-button type="text" class="header-btn">
        <el-icon><Setting /></el-icon>
        系统设置
      </el-button>
      
      <!-- 数据库 -->
      <el-button type="text" class="header-btn">
        <el-icon><Coin /></el-icon>
        数据库
      </el-button>
      
      <!-- 帮助中心 -->
      <el-button type="text" class="header-btn">
        <el-icon><QuestionFilled /></el-icon>
        帮助中心
      </el-button>
      
      <!-- 版本信息 -->
      <span class="version">4.1.3 更新</span>
      
      <!-- Token监控 -->
      <TokenMonitor />
      
      <!-- 右侧用户区域 -->
      <div class="user-area">
        <el-dropdown @command="handleUserCommand">
          <div class="user-info">
            <img :src="userStore.userInfo?.avatar || '/api/placeholder/32/32'" alt="头像" class="user-avatar" />
            <span class="user-name">{{ userStore.userInfo?.name || '用户' }}</span>
            <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人资料
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                系统设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  Setting, Coin, QuestionFilled, 
  User, ArrowDown, SwitchButton 
} from '@element-plus/icons-vue'
import { useUserStore } from '../stores/user'
import TokenMonitor from './TokenMonitor.vue'

const router = useRouter()
const userStore = useUserStore()

const handleUserCommand = async (command: string) => {
  if (command === 'logout') {
    // 使用store的退出登录方法
    await userStore.logoutUser()
    router.push('/login')
  } else if (command === 'profile') {
    console.log('个人资料')
  } else if (command === 'settings') {
    console.log('系统设置')
  }
}
</script>

<style scoped>
.header {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.system-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.system-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.system-date {
  font-size: 12px;
  color: #999;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-btn {
  color: #606266;
  font-size: 14px;
  padding: 8px 12px;
}

.header-btn:hover {
  color: #28a745;
  background-color: #f5f5f5;
}

.version {
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.user-area {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #606266;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
  color: #28a745;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.dropdown-icon {
  margin-left: 4px;
}

.user-name {
  font-weight: 600;
}
</style> 