<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade')->comment('订单ID');
            $table->foreignId('product_id')->constrained()->comment('商品ID');
            $table->string('product_name')->comment('商品名称');
            $table->string('product_sku')->nullable()->comment('商品SKU');
            $table->integer('quantity')->unsigned()->comment('数量');
            $table->decimal('price', 10, 2)->comment('单价');
            $table->decimal('total', 10, 2)->comment('小计金额');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
