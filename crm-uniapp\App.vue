<script>
import config from './utils/config.js'

export default {
	onLaunch: function() {
		console.log('App Launch')
		this.checkGlobalAuth()
	},
	
	onShow: function() {
		console.log('App Show')
		// 应用从后台切换到前台时也检查认证状态
		this.checkGlobalAuth()
	},
	
	onHide: function() {
		console.log('App Hide')
	},
	
	methods: {
		// 全局认证检查
		checkGlobalAuth() {
			// 获取当前页面路径
			const pages = getCurrentPages()
			const currentPage = pages[pages.length - 1]
			const currentRoute = currentPage ? currentPage.route : ''
			
			// 登录页和其他不需要认证的页面跳过检查
			const noAuthPages = [
				'pages/login/login',
				'pages/register/register',
				'pages/forgot-password/forgot-password'
			]
			
			if (noAuthPages.includes(currentRoute)) {
				return
			}
			
			// 检查token是否存在
			const token = uni.getStorageSync(config.storageKeys.token)
			if (!token) {
				console.log('未找到token，跳转到登录页')
				uni.reLaunch({
					url: '/pages/login/login'
				})
				return
			}
			
			// 检查token是否过期（如果token包含过期时间）
			try {
				const employeeInfo = uni.getStorageSync(config.storageKeys.employeeInfo)
				if (employeeInfo && employeeInfo.token_expires_at) {
					const expiresAt = new Date(employeeInfo.token_expires_at).getTime()
					const now = new Date().getTime()
					
					if (now >= expiresAt) {
						console.log('token已过期，跳转到登录页')
						this.clearAuthData()
						uni.reLaunch({
							url: '/pages/login/login'
						})
						return
					}
				}
			} catch (error) {
				console.error('检查token过期时间失败:', error)
			}
		},
		
		// 清除认证数据
		clearAuthData() {
			uni.removeStorageSync(config.storageKeys.token)
			uni.removeStorageSync(config.storageKeys.userInfo)
			uni.removeStorageSync(config.storageKeys.employeeInfo)
		}
	}
}
</script>

<style>
	/*每个页面公共css */
</style>
