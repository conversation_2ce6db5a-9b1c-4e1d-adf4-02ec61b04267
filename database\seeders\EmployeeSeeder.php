<?php

namespace Database\Seeders;

use App\Employee\Models\Employee;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EmployeeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 临时禁用外键约束
        DB::statement('SET FOREIGN_KEY_CHECKS=0');
        
        // 清空表
        Employee::truncate();
        
        // 创建一个管理员账号
        Employee::create([
            'name' => '系统管理员',
            'username' => 'admin',
            'password' => '123456',  // 明文密码，会被模型自动加密
            'phone' => '13800000000',
            'position' => '系统管理员',
            'role' => 'admin',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
        
        // 创建一个经理账号
        Employee::create([
            'name' => '部门经理',
            'username' => 'manager',
            'password' => '123456',  // 明文密码，会被模型自动加密
            'phone' => '13900000000',
            'position' => '部门经理',
            'role' => 'manager',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
        
        // 创建普通员工账号
        Employee::create([
            'name' => '普通员工',
            'username' => 'staff',
            'password' => '123456',  // 明文密码，会被模型自动加密
            'phone' => '13700000000',
            'position' => '普通员工',
            'role' => 'staff',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
        
        // 创建更多员工
        $positions = ['销售', '客服', '技术支持', '仓库管理', '配送员', '会计', '人事'];
        $roles = ['staff', 'staff', 'staff', 'warehouse', 'delivery', 'staff', 'staff'];
        
        for ($i = 1; $i <= 20; $i++) {
            $posIndex = $i % count($positions);
            Employee::create([
                'name' => $positions[$posIndex] . $i,
                'username' => strtolower($positions[$posIndex]) . $i,
                'password' => '123456',  // 明文密码，会被模型自动加密
                'phone' => '138' . str_pad($i, 8, '0', STR_PAD_LEFT),
                'position' => $positions[$posIndex],
                'role' => $roles[$posIndex],
                'created_at' => Carbon::now()->subDays(rand(1, 365)),
                'updated_at' => Carbon::now(),
            ]);
        }
        
        $this->command->info('员工数据添加完成！共添加 ' . (3 + 20) . ' 条记录');
        
        // 重新启用外键约束
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }
} 