# 采购单位修复总结

## 问题描述

用户发现商品选择弹窗中显示的单位不正确：
- 代码尝试获取 `purchase_unit_name`（采购单位）
- 但如果商品没有设置专门的采购单位，该字段为空
- 应该回退到 `base_unit_name`（基本单位）

## 问题分析

### 后端数据结构
在 `InventoryProductController.php` 的 `getUnitInfo` 方法中：
```php
private function getUnitInfo($product, $availableUnits)
{
    $baseUnitName = $product->baseUnit?->name ?? '个';
    $saleUnitName = $baseUnitName;
    $purchaseUnitName = $baseUnitName;
    $inventoryUnitName = $baseUnitName;

    // 从可用单位中查找不同角色的单位
    foreach ($availableUnits as $unit) {
        if (isset($unit['roles']) && is_array($unit['roles'])) {
            if (in_array('purchase', $unit['roles'])) {
                $purchaseUnitName = $unit['name'];
            }
        }
    }
    
    return [
        'base_unit_name' => $baseUnitName,
        'purchase_unit_name' => $purchaseUnitName,
        // ...
    ];
}
```

**关键逻辑**：如果没有设置采购单位角色，`purchaseUnitName` 会保持为 `baseUnitName`。

### 前端问题
1. **商品选择弹窗**：使用了错误的回退逻辑
2. **采购订单编辑**：商品选择后的单位处理不正确
3. **类型定义**：缺少必要的字段类型

## 修复内容

### 1. 商品选择弹窗单位显示

**修复前**：
```vue
{{ row.purchase_unit_name || row.unit_name }}
```

**修复后**：
```vue
{{ row.purchase_unit_name || row.base_unit_name || '件' }}
```

### 2. 采购订单商品选择逻辑

**修复前**：
```javascript
formData.items.push({
  // ...
  unit_name: product.purchase_unit_name,
  unit_price: product.purchase_unit_price || 0,
  // ...
})
```

**修复后**：
```javascript
// 确定采购单位和价格：优先使用采购单位，否则使用基本单位
const unitName = product.purchase_unit_name || product.base_unit_name || '件'
const unitPrice = product.purchase_unit_price || product.cost_price || 0

formData.items.push({
  // ...
  unit_name: unitName,
  unit_price: unitPrice,
  // ...
})
```

### 3. TypeScript 类型定义

**修复前**：
```typescript
const availableProducts = ref<Array<{
  id: number
  name: string
  code?: string
  purchase_unit_name?: string
  purchase_unit_price?: number
}>>([])
```

**修复后**：
```typescript
const availableProducts = ref<Array<{
  id: number
  name: string
  code?: string
  purchase_unit_name?: string
  purchase_unit_price?: number
  base_unit_name?: string
  cost_price?: number
}>>([])
```

## 修复效果

### ✅ 单位显示正确
- 有采购单位：显示采购单位名称
- 无采购单位：显示基本单位名称
- 兜底处理：显示"件"

### ✅ 价格回退合理
- 有采购单位价格：使用采购单位价格
- 无采购单位价格：使用基本单位成本价
- 确保不会出现空价格

### ✅ 类型安全
- 补充了缺失的字段类型定义
- 消除了 TypeScript 编译错误

## 业务逻辑说明

### 单位优先级
1. **采购单位** - 如果商品设置了专门的采购单位
2. **基本单位** - 商品的基础计量单位
3. **默认单位** - 兜底显示"件"

### 价格优先级
1. **采购单位价格** - 如果设置了采购单位的专门价格
2. **基本单位成本价** - 使用商品的成本价作为采购参考价
3. **零价格** - 兜底处理

## 后端数据流
```
商品单位设置 → getAllUnits() → getUnitInfo() → API响应
                     ↓
前端接收 → 商品选择 → 单位回退逻辑 → 采购订单明细
```

## 总结

这次修复解决了采购模块中单位显示和处理的核心问题：
- **正确的回退逻辑**：采购单位 → 基本单位 → 默认单位
- **价格处理优化**：确保总有合理的参考价格
- **类型安全保证**：完善的 TypeScript 类型定义

修复后，无论商品是否设置了专门的采购单位，系统都能正确显示和处理单位信息，提升了用户体验和数据准确性。 