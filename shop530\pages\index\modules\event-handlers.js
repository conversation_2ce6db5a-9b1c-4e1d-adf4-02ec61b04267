// pages/index/modules/event-handlers.js - 事件处理模块

const { isLoggedIn } = require('../../../utils/login-state-manager');
const cartManager = require('../../../utils/cart-unified');
const { debounce } = require('./utils');

/**
 * 事件处理器
 */
class EventHandlers {
  constructor(pageContext) {
    this.page = pageContext;
    this.debouncedSearch = debounce(this._performSearch.bind(this), 300);
  }

  /**
   * 添加到购物车
   */
  async onAddToCart(e) {
    const { product, quantity = 1 } = e.detail;
    
    if (!product) {
      wx.showToast({
        title: '商品信息异常',
        icon: 'none'
      });
      return;
    }

    // 检查登录状态
    const userLoggedIn = isLoggedIn();
    if (!userLoggedIn) {
      wx.navigateTo({
        url: '/pages/login/index'
      });
      return;
    }

    // 检查商品缺货状态
    if (product.out_of_stock) {
      wx.showToast({
        title: product.purchase_message || '商品已售罄',
        icon: 'none'
      });
      return;
    }

    console.log('🛒 添加到购物车:', product.name, '数量:', quantity);

    try {
      // 🔥 使用统一购物车管理器
      const { addToCart } = require('../../../utils/cart-unified');
      const success = await addToCart(product, quantity);
      
      if (success) {
        console.log('✅ 添加到购物车成功:', product.name);
        
        // Toast提示由统一管理器的监听器处理
        // 购物车徽标由统一管理器自动更新
      } else {
        console.error('❌ 添加到购物车失败');
        wx.showToast({
          title: '添加失败，请重试',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('❌ 添加到购物车异常:', error);
      
      // 根据错误类型显示不同提示
      let errorMessage = '添加失败';
      if (error.message) {
        if (error.message.includes('登录')) {
          errorMessage = '登录已过期，请重新登录';
        } else if (error.message.includes('网络')) {
          errorMessage = '网络异常，请重试';
        } else {
          errorMessage = error.message;
        }
      }
      
      wx.showToast({
        title: errorMessage,
        icon: 'error'
      });
    }
  }

  /**
   * 从购物车移除
   */
  async onRemoveFromCart(e) {
    const { product, quantity = 1 } = e.detail;
    
    if (!product) {
      console.warn('⚠️ 商品信息无效:', product);
      return;
    }

    console.log('🗑️ 从购物车移除:', product.name, '数量:', quantity);

    try {
      // 🔥 使用统一购物车管理器
      const { removeFromCart } = require('../../../utils/cart-unified');
      const success = await removeFromCart(product.id);
      if (success) {
        console.log('✅ 移除购物车成功');
        // Toast提示和徽标更新由统一管理器处理
      }
    } catch (error) {
      console.error('❌ 移除购物车失败:', error);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  }

  /**
   * 商品点击
   */
  onProductTap(e) {
    const { product } = e.detail;
    
    if (!product || !product.id) {
      console.warn('⚠️ 商品信息无效');
      return;
    }

    console.log('👆 商品点击:', product.name);
    
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${product.id}`
    }).catch(error => {
      console.error('跳转商品详情失败:', error);
      wx.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    });
  }

  /**
   * 轮播图点击
   */
  onBannerTap(e) {
    const { banner } = e.detail;
    
    if (!banner || !banner.link) {
      console.log('🖼️ 轮播图无链接');
      return;
    }

    console.log('👆 轮播图点击:', banner.title, banner.link);
    
    // 根据链接类型处理跳转
    if (banner.link.startsWith('/pages/')) {
      wx.navigateTo({
        url: banner.link
      }).catch(error => {
        console.error('轮播图跳转失败:', error);
      });
    } else if (banner.link.startsWith('http')) {
      // 外部链接，可以使用web-view或其他方式处理
      console.log('外部链接:', banner.link);
    }
  }

  /**
   * 分类点击
   */
  onCategoryTap(e) {
    const { category } = e.detail;
    
    if (!category || !category.id) {
      console.warn('⚠️ 分类信息无效');
      return;
    }

    console.log('👆 分类点击:', category.name);
    
    wx.navigateTo({
      url: `/pages/category/category?id=${category.id}&name=${encodeURIComponent(category.name)}`
    }).catch(error => {
      console.error('跳转分类页面失败:', error);
      wx.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    });
  }

  /**
   * 搜索输入变化
   */
  onSearchChange(e) {
    if (this.page.data._isDestroyed) return;
    
    const value = e.detail.value || e.detail || '';
    this.page.setData({ searchValue: value });
    
    // 使用防抖处理搜索建议
    if (value && value.trim()) {
      this.debouncedSearch(value.trim());
    }
  }

  /**
   * 搜索提交
   */
  onSearch(e) {
    if (this.page.data._isDestroyed) return;
    
    const keyword = e.detail.value || e.detail || this.page.data.searchValue;
    
    if (!keyword || !keyword.trim()) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    this._performSearch(keyword.trim());
  }

  /**
   * 搜索焦点
   */
  onSearchFocus(e) {
    console.log('🔍 搜索框获得焦点');
    // 可以在这里添加搜索建议等功能
  }

  /**
   * 标签切换
   */
  async onTabChange(e) {
    const index = this._parseTabIndex(e);
    
    if (!this._isValidTabIndex(index)) {
      console.warn('⚠️ 无效的标签索引:', index);
      return;
    }

    const currentIndex = this.page.data.currentTabIndex;
    if (index === currentIndex) {
      console.log('📋 标签未变化，跳过处理');
      return;
    }

    console.log(`📋 标签切换: ${currentIndex} -> ${index}`);

    try {
      this.page.setData({ 
        currentTabIndex: index,
        isTabLoading: true 
      });

      const tab = this.page.data.tabsList[index];
      if (tab) {
        await this._loadTabContent(index, tab);
      }
    } catch (error) {
      console.error('❌ 标签切换失败:', error);
      this._handleTabChangeError();
    } finally {
      this.page.setData({ isTabLoading: false });
    }
  }

  /**
   * 执行搜索
   */
  _performSearch(keyword) {
    console.log('🔍 执行搜索:', keyword);
    
    wx.navigateTo({
      url: `/pages/search/search?keyword=${encodeURIComponent(keyword)}`
    }).catch((error) => {
      console.error('跳转搜索页面失败:', error);
      wx.showToast({
        title: '搜索功能暂时不可用',
        icon: 'none',
        duration: 2000
      });
    });
  }

  /**
   * 解析标签索引
   */
  _parseTabIndex(event) {
    if (event && typeof event.detail === 'object' && event.detail.index !== undefined) {
      return parseInt(event.detail.index);
    }
    
    if (event && typeof event.detail === 'number') {
      return event.detail;
    }
    
    if (typeof event === 'number') {
      return event;
    }
    
    return 0;
  }

  /**
   * 验证标签索引
   */
  _isValidTabIndex(index) {
    return typeof index === 'number' && 
           index >= 0 && 
           index < (this.page.data.tabsList?.length || 0);
  }

  /**
   * 加载标签内容
   */
  async _loadTabContent(index, tab) {
    const { dataLoader } = require('./data-loader');
    
    try {
      const products = await dataLoader.loadTabProducts(tab.type);
      
      this.page.setData({
        [`productSections[0].products`]: products,
        [`productSections[0].title`]: tab.name,
        [`productSections[0].type`]: tab.type
      });
      
      console.log(`✅ 标签内容加载完成: ${tab.name}, ${products.length}个商品`);
    } catch (error) {
      console.error(`❌ 加载标签内容失败: ${tab.name}`, error);
      throw error;
    }
  }

  /**
   * 处理标签切换错误
   */
  _handleTabChangeError() {
    wx.showToast({
      title: '加载失败，请重试',
      icon: 'none',
      duration: 2000
    });
    
    // 重置到第一个标签
    this.page.setData({ 
      currentTabIndex: 0,
      isTabLoading: false 
    });
  }
}

module.exports = {
  EventHandlers
}; 