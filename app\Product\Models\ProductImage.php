<?php

namespace App\Product\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductImage extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'product_id',
        'url',
        'path',
        'sort',
        'is_main',
        'driver',
        'original_name',
        'size',
        'mime_type',
        'status'
    ];

    /**
     * 类型转换
     */
    protected $casts = [
        'is_main' => 'boolean',
        'status' => 'boolean',
        'sort' => 'integer',
        'size' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 图片所属商品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 设置为主图片
     * 
     * @return bool
     */
    public function setAsMain()
    {
        // 先将所有同商品的图片设为非主图
        self::where('product_id', $this->product_id)
            ->where('id', '!=', $this->id)
            ->update(['is_main' => false]);
        
        // 将当前图片设为主图
        $this->is_main = true;
        $this->status = true; // 确保图片为启用状态
        $result = $this->save();
        
        \Illuminate\Support\Facades\Log::info('设置商品主图', [
            'product_id' => $this->product_id,
            'image_id' => $this->id,
            'url' => $this->url
        ]);
        
        // 注意：之前这里有更新商品cover_url字段的代码
        // 现在已经不再需要，因为我们将使用Product模型的getCoverUrlAttribute方法
        // 通过product_images表直接获取主图URL
        
        return $result;
    }

    /**
     * 根据商品ID和图片URL创建或更新图片
     * 
     * @param int $productId 商品ID
     * @param array $imageData 图片数据
     * @return ProductImage
     */
    public static function createOrUpdateFromUpload($productId, $imageData)
    {
        return self::updateOrCreate(
            [
                'product_id' => $productId,
                'url' => $imageData['url']
            ],
            [
                'path' => $imageData['path'],
                'driver' => $imageData['driver'] ?? null,
                'original_name' => $imageData['original_name'] ?? null,
                'size' => $imageData['size'] ?? null,
                'mime_type' => $imageData['mime_type'] ?? null,
                'is_main' => $imageData['is_main'] ?? false,
                'sort' => $imageData['sort'] ?? 0,
                'status' => $imageData['status'] ?? true
            ]
        );
    }
} 