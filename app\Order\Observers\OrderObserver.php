<?php

namespace App\Order\Observers;

use App\Order\Models\Order;
use App\Order\Events\OrderCompleted;
use App\FlyCloud\Services\FlyCloudService;
use Illuminate\Support\Facades\Log;

class OrderObserver
{
    protected FlyCloudService $flyCloudService;

    public function __construct(FlyCloudService $flyCloudService)
    {
        $this->flyCloudService = $flyCloudService;
    }

    /**
     * 订单创建后触发
     */
    public function created(Order $order): void
    {
        // 订单创建后自动触发分单打印
        $this->triggerAutoPrint($order, '订单创建');
    }

    /**
     * 订单状态更新后触发
     */
    public function updated(Order $order): void
    {
        // 检查是否是状态变更为已付款
        if ($order->wasChanged('status') && $order->status === 'paid') {
            // 订单已付款，触发分单打印
            $this->triggerAutoPrint($order, '订单付款');
        }
        
        // 检查是否是状态变更为已送达（订单完成）
        if ($order->wasChanged('status') && $order->status === 'delivered') {
            // 触发订单完成事件，给予积分奖励
            event(new OrderCompleted($order));
            
            Log::info('订单送达完成，触发积分奖励事件', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'user_id' => $order->user_id,
                'status' => $order->status
            ]);
        }
    }

    /**
     * 触发自动打印
     */
    protected function triggerAutoPrint(Order $order, string $trigger): void
    {
        try {
            // 检查系统是否启用自动打印
            if (!config('flycloud.auto_print_enabled', true)) {
                Log::info('自动打印已禁用', ['order_id' => $order->id]);
                return;
            }

            // 检查订单是否有商品
            if ($order->items->isEmpty()) {
                Log::warning('订单无商品，跳过自动打印', ['order_id' => $order->id]);
                return;
            }

            // 获取订单涉及的仓库
            $warehouseIds = $this->flyCloudService->getOrderWarehouses($order);
            
            if (empty($warehouseIds)) {
                Log::warning('订单无仓库分配，跳过自动打印', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no
                ]);
                return;
            }

            Log::info('开始自动分单打印', [
                'trigger' => $trigger,
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'warehouses' => $warehouseIds
            ]);

            // 异步执行打印任务（避免阻塞订单创建）
            dispatch(function () use ($order, $trigger, $warehouseIds) {
                $this->executePrintTask($order, $trigger, $warehouseIds);
            })->delay(now()->addSeconds(5)); // 延迟5秒执行，确保订单数据完整

        } catch (\Exception $e) {
            Log::error('自动打印触发失败', [
                'trigger' => $trigger,
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 执行打印任务
     */
    protected function executePrintTask(Order $order, string $trigger, array $warehouseIds): void
    {
        try {
            // 重新加载订单数据（确保数据完整性）
            $refreshedOrder = Order::with(['items.product'])->find($order->id);
            
            if (!$refreshedOrder) {
                Log::error('订单不存在，无法执行自动打印', ['order_id' => $order->id]);
                return;
            }

            // 执行分单打印
            $results = $this->flyCloudService->printOrderByWarehouses($refreshedOrder, [
                'print_type' => 'order',
                'copies' => 1,
                'auto_print' => true,
                'trigger' => $trigger
            ]);

            // 统计打印结果
            $successCount = 0;
            $failCount = 0;
            $messages = [];

            foreach ($results as $warehouseId => $result) {
                if (isset($result['success']) && $result['success']) {
                    $successCount++;
                    $messages[] = "仓库{$warehouseId}: 打印成功";
                } else {
                    $failCount++;
                    $message = $result['message'] ?? '打印失败';
                    $messages[] = "仓库{$warehouseId}: {$message}";
                }
            }

            // 记录打印结果
            Log::info('自动分单打印完成', [
                'trigger' => $trigger,
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'total_warehouses' => count($warehouseIds),
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'messages' => $messages
            ]);

            // 如果有打印失败，发送通知（可选）
            if ($failCount > 0) {
                $this->notifyPrintFailure($order, $messages);
            }

        } catch (\Exception $e) {
            Log::error('自动打印任务执行失败', [
                'trigger' => $trigger,
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 通知打印失败（可扩展为邮件、短信等）
     */
    protected function notifyPrintFailure(Order $order, array $messages): void
    {
        // 这里可以扩展为发送邮件、短信、钉钉消息等
        Log::warning('订单分单打印存在失败项，请检查', [
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'messages' => $messages
        ]);
    }
} 