<?php

namespace App\Inventory\Http\Controllers;

use App\Inventory\Models\Inventory;
use App\Inventory\Models\InventoryBatch;
use App\Purchase\Models\PurchaseItem;
use App\Product\Models\Product;
use App\Models\Warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;

class InventoryBatchController extends Controller
{
    /**
     * 获取库存批次列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = InventoryBatch::with(['inventory.product', 'inventory.warehouse', 'purchaseItem']);
        
        // 按仓库过滤
        if ($request->has('warehouse_id') && $request->warehouse_id) {
            $query->whereHas('inventory', function($q) use ($request) {
                $q->where('warehouse_id', $request->warehouse_id);
            });
        }
        
        // 按产品过滤
        if ($request->has('product_id') && $request->product_id) {
            $query->whereHas('inventory', function($q) use ($request) {
                $q->where('product_id', $request->product_id);
            });
        }
        
        // 按批次编码过滤
        if ($request->has('batch_code') && $request->batch_code) {
            $query->where('batch_code', 'like', '%' . $request->batch_code . '%');
        }
        
        // 按过期状态过滤
        if ($request->has('expiry_status')) {
            if ($request->expiry_status === 'expired') {
                $query->whereNotNull('expiry_date')
                      ->where('expiry_date', '<', now())
                      ->where('quantity', '>', 0);
            } elseif ($request->expiry_status === 'near_expiry') {
                $days = $request->get('expiry_days', 7);
                $query->whereNotNull('expiry_date')
                      ->where('expiry_date', '>=', now())
                      ->where('expiry_date', '<=', now()->addDays($days))
                      ->where('quantity', '>', 0);
            } elseif ($request->expiry_status === 'valid') {
                $query->where(function($q) {
                    $q->whereNull('expiry_date')
                      ->orWhere('expiry_date', '>=', now());
                })->where('quantity', '>', 0);
            }
        }
        
        // 按库存量过滤
        if ($request->has('has_stock')) {
            if ($request->has_stock) {
                $query->where('quantity', '>', 0);
            } else {
                $query->where('quantity', '<=', 0);
            }
        }
        
        // 排序
        $sortField = $request->get('sort_field', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortField, $sortOrder);
        
        $perPage = $request->get('per_page', 15);
        $batches = $query->paginate($perPage);
        
        return response()->json([
            'code' => 0,
            'message' => '获取批次列表成功',
            'data' => $batches
        ]);
    }
    
    /**
     * 获取单个批次详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $batch = InventoryBatch::with(['inventory.product', 'inventory.warehouse', 'purchaseItem.purchaseOrder.supplier'])
            ->findOrFail($id);
            
        return response()->json([
            'code' => 0,
            'message' => '获取批次详情成功',
            'data' => $batch
        ]);
    }
    
    /**
     * 创建新的批次
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'inventory_id' => 'required|exists:inventory,id',
            'batch_code' => 'nullable|string|max:50',
            'purchase_price' => 'required|numeric|min:0',
            'quantity' => 'required|numeric|min:0',
            'unit' => 'required|string|max:20',
            'production_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after_or_equal:production_date',
            'notes' => 'nullable|string',
            'purchase_item_id' => 'nullable|exists:purchase_items,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }
        
        DB::beginTransaction();
        try {
            // 生成批次编码（如果没有提供）
            if (!$request->batch_code) {
                $batchCode = InventoryBatch::generateBatchCode();
            } else {
                $batchCode = $request->batch_code;
            }
            
            // 创建批次
            $batch = InventoryBatch::create([
                'inventory_id' => $request->inventory_id,
                'purchase_item_id' => $request->purchase_item_id,
                'batch_code' => $batchCode,
                'purchase_price' => $request->purchase_price,
                'quantity' => $request->quantity,
                'initial_quantity' => $request->quantity,
                'unit' => $request->unit,
                'production_date' => $request->production_date,
                'expiry_date' => $request->expiry_date,
                'notes' => $request->notes,
                'created_by' => Auth::id(),
            ]);
            
            // 更新库存总量
            $inventory = Inventory::findOrFail($request->inventory_id);
            $currentStock = $inventory->stock ?? 0;
            $inventory->stock = $currentStock + $request->quantity;
            $inventory->save();
            
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '创建批次成功',
                'data' => $batch
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'code' => 1,
                'message' => '创建批次失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新批次信息
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'batch_code' => 'nullable|string|max:50',
            'purchase_price' => 'nullable|numeric|min:0',
            'production_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after_or_equal:production_date',
            'notes' => 'nullable|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $batch = InventoryBatch::findOrFail($id);
        
        // 不允许修改库存量，只能通过出库/入库流程修改
        $batch->update($request->only([
            'batch_code',
            'purchase_price',
            'production_date',
            'expiry_date',
            'notes',
        ]));
        
        return response()->json([
            'code' => 0,
            'message' => '更新批次信息成功',
            'data' => $batch
        ]);
    }
    
    /**
     * 从采购明细创建批次（入库）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createFromPurchase(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'purchase_item_id' => 'required|exists:purchase_items,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'quantity' => 'required|numeric|min:0.01',
            'batch_code' => 'nullable|string|max:50',
            'production_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after_or_equal:production_date',
            'notes' => 'nullable|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }
        
        DB::beginTransaction();
        try {
            $purchaseItem = PurchaseItem::with(['purchaseOrder', 'product', 'unit'])->findOrFail($request->purchase_item_id);
            
            // 检查是否超过待收货数量
            if ($request->quantity > $purchaseItem->pending_quantity) {
                return response()->json([
                    'code' => 1,
                    'message' => '入库数量不能超过待收货数量'
                ], 422);
            }
            
            // 查找或创建库存记录
            $inventory = Inventory::firstOrCreate(
                [
                    'warehouse_id' => $request->warehouse_id,
                    'product_id' => $purchaseItem->product_id,
                ],
                [
                    'stock' => 0,
                    'unit' => $purchaseItem->unit->code ?? 'pcs',
                ]
            );
            
            // 创建批次
            $batch = $purchaseItem->addBatch(
                $inventory->id,
                $request->quantity,
                $purchaseItem->unit->code ?? 'pcs',
                $request->batch_code,
                $request->production_date,
                $request->expiry_date,
                $request->notes,
                Auth::id()
            );
            
            // 更新库存总量
            $inventory->stock += $request->quantity;
            $inventory->save();
            
            // 更新采购明细的已收货数量
            $purchaseItem->received_quantity += $request->quantity;
            $purchaseItem->save();
            
            // 检查采购订单是否已完全收货
            $purchaseOrder = $purchaseItem->purchaseOrder;
            $allItemsReceived = $purchaseOrder->purchaseItems()->get()->every(function($item) {
                return $item->is_fully_received;
            });
            
            if ($allItemsReceived) {
                $purchaseOrder->status = 'completed';
                $purchaseOrder->save();
            }
            
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '创建批次并入库成功',
                'data' => $batch
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'code' => 1,
                'message' => '创建批次失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取即将过期的批次列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function nearExpiry(Request $request)
    {
        $days = $request->get('days', 7);
        
        $batches = InventoryBatch::with(['inventory.product', 'inventory.warehouse'])
            ->whereNotNull('expiry_date')
            ->where('expiry_date', '>=', now())
            ->where('expiry_date', '<=', now()->addDays($days))
            ->where('quantity', '>', 0)
            ->orderBy('expiry_date', 'asc')
            ->paginate($request->get('per_page', 15));
            
        return response()->json([
            'code' => 0,
            'message' => '获取即将过期批次成功',
            'data' => $batches
        ]);
    }
    
    /**
     * 获取已过期的批次列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function expired(Request $request)
    {
        $batches = InventoryBatch::with(['inventory.product', 'inventory.warehouse'])
            ->whereNotNull('expiry_date')
            ->where('expiry_date', '<', now())
            ->where('quantity', '>', 0)
            ->orderBy('expiry_date', 'desc')
            ->paginate($request->get('per_page', 15));
            
        return response()->json([
            'code' => 0,
            'message' => '获取已过期批次成功',
            'data' => $batches
        ]);
    }
    
    /**
     * 批次出库
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reduceStock(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'quantity' => 'required|numeric|min:0.01',
            'reason' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }
        
        DB::beginTransaction();
        try {
            $batch = InventoryBatch::findOrFail($id);
            
            // 检查库存是否足够
            if ($batch->quantity < $request->quantity) {
                return response()->json([
                    'code' => 1,
                    'message' => '批次库存不足'
                ], 422);
            }
            
            // 减少批次库存
            $batch->quantity -= $request->quantity;
            $batch->save();
            
            // 更新总库存
            $inventory = $batch->inventory;
            $inventory->stock -= $request->quantity;
            $inventory->save();
            
            // 记录出库原因
            // 这里可以添加库存流水记录
            
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '批次出库成功',
                'data' => $batch
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'code' => 1,
                'message' => '批次出库失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取产品所有批次的加权平均成本
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAverageCost(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'warehouse_id' => 'nullable|exists:warehouses,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $query = Inventory::where('product_id', $request->product_id);
        
        if ($request->has('warehouse_id') && $request->warehouse_id) {
            $query->where('warehouse_id', $request->warehouse_id);
        }
        
        $inventories = $query->get();
        
        $totalCost = 0;
        $totalQuantity = 0;
        
        foreach ($inventories as $inventory) {
            $batches = $inventory->batches()->where('quantity', '>', 0)->get();
            
            foreach ($batches as $batch) {
                $totalCost += $batch->purchase_price * $batch->quantity;
                $totalQuantity += $batch->quantity;
            }
        }
        
        $averageCost = $totalQuantity > 0 ? $totalCost / $totalQuantity : null;
        
        return response()->json([
            'code' => 0,
            'message' => '获取平均成本成功',
            'data' => [
                'product_id' => $request->product_id,
                'warehouse_id' => $request->warehouse_id ?? 'all',
                'average_cost' => $averageCost,
                'total_quantity' => $totalQuantity,
                'total_cost' => $totalCost,
            ]
        ]);
    }
}
