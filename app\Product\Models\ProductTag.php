<?php

namespace App\Product\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class ProductTag extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'color',
        'background_color',
        'icon',
        'description',
        'sort_order',
        'is_active',
        'show_in_filter',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'show_in_filter' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * 自动生成slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($tag) {
            if (empty($tag->slug)) {
                $tag->slug = Str::slug($tag->name);
                
                // 确保slug唯一
                $originalSlug = $tag->slug;
                $counter = 1;
                while (static::where('slug', $tag->slug)->exists()) {
                    $tag->slug = $originalSlug . '-' . $counter;
                    $counter++;
                }
            }
        });
    }

    /**
     * 关联的商品
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'product_tag_relations', 'tag_id', 'product_id')
                    ->withTimestamps();
    }

    /**
     * 获取使用该标签的商品数量
     */
    public function getProductCountAttribute()
    {
        return $this->products()->count();
    }

    /**
     * 作用域：只获取激活的标签
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：只获取在筛选中显示的标签
     */
    public function scopeShowInFilter($query)
    {
        return $query->where('show_in_filter', true);
    }

    /**
     * 作用域：按排序排列
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('name', 'asc');
    }

    /**
     * 获取标签的完整样式信息
     */
    public function getStyleAttribute()
    {
        return [
            'color' => $this->color,
            'backgroundColor' => $this->background_color,
        ];
    }

    /**
     * 获取用于前端显示的标签信息
     */
    public function toDisplayArray()
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'color' => $this->color,
            'background' => $this->background_color,
            'icon' => $this->icon,
            'description' => $this->description,
        ];
    }
} 