Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#4CAF50",
    backgroundColor: "#ffffff",
    list: [
      {
        pagePath: "/pages/index/index",
        text: "首页",
        iconPath: "/images/tabbar/home.png",
        selectedIconPath: "/images/tabbar/home-active.png"
      },
      {
        pagePath: "/pages/category/category",
        text: "分类",
        iconPath: "/images/tabbar/category.png",
        selectedIconPath: "/images/tabbar/category-active.png"
      },
      {
        pagePath: "/pages/points/index",
        text: "积分商城",
        iconPath: "/images/tabbar/points.png",
        selectedIconPath: "/images/tabbar/points-active.png"
      },
      {
        pagePath: "/pages/cart/index",
        text: "购物车",
        iconPath: "/images/tabbar/cart.png",
        selectedIconPath: "/images/tabbar/cart-active.png"
      },
      {
        pagePath: "/pages/profile/index",
        text: "我的",
        iconPath: "/images/tabbar/profile.png",
        selectedIconPath: "/images/tabbar/profile-active.png"
      }
    ]
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      wx.switchTab({
        url
      });
      this.setData({
        selected: data.index
      });
    }
  }
}); 