<?php

namespace App\Product\Services;

use App\Product\Models\Category;
use App\Product\Models\Product;
use Illuminate\Support\Facades\Log;

class CategoryService
{
    /**
     * 获取分类列表
     *
     * @param array $filters 过滤条件
     * @param int $perPage 每页数量
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getCategories($filters = [], $perPage = 10)
    {
        $query = Category::query();
        
        // 关键词搜索
        if (!empty($filters['keyword'])) {
            $query->where('name', 'like', '%' . $filters['keyword'] . '%');
        }
        
        // 状态筛选
        if (isset($filters['status']) && $filters['status'] !== '') {
            $query->where('status', $filters['status']);
        }
        
        // 父级ID筛选
        if (isset($filters['parent_id']) && $filters['parent_id'] !== '') {
            $query->where('parent_id', $filters['parent_id']);
        }
        
        // 排序
        $query->orderBy('sort', 'asc')->orderBy('id', 'asc');
        
        return $query->paginate($perPage);
    }

    /**
     * 获取所有分类列表（不分页）
     *
     * @param array $filters 过滤条件
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllCategories($filters = [])
    {
        $query = Category::query();
        
        // 关键词搜索
        if (!empty($filters['keyword'])) {
            $query->where('name', 'like', '%' . $filters['keyword'] . '%');
        }
        
        // 状态筛选
        if (isset($filters['status']) && $filters['status'] !== '') {
            $query->where('status', $filters['status']);
        }
        
        // 父级ID筛选
        if (isset($filters['parent_id']) && $filters['parent_id'] !== '') {
            $query->where('parent_id', $filters['parent_id']);
        }
        
        // 排序
        $query->orderBy('sort', 'asc')->orderBy('id', 'asc');
        
        return $query->get();
    }

    /**
     * 创建分类
     *
     * @param array $data 分类数据
     * @return Category 创建的分类
     */
    public function createCategory(array $data)
    {
        try {
            $category = Category::create([
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'parent_id' => $data['parent_id'] ?? 0,
                'sort' => $data['sort'] ?? 0,
                'status' => isset($data['status']) ? (int)$data['status'] : 1,
                'image_url' => $data['image_url'] ?? '',
                'icon' => $data['icon'] ?? '',
                'custom_icon_url' => $data['custom_icon_url'] ?? '',
            ]);

            return $category;
        } catch (\Exception $e) {
            Log::error('分类创建失败', ['error' => $e->getMessage(), 'data' => $data]);
            throw $e;
        }
    }

    /**
     * 更新分类
     *
     * @param int $id 分类ID
     * @param array $data 更新数据
     * @return Category 更新后的分类
     */
    public function updateCategory($id, array $data)
    {
        $category = Category::findOrFail($id);
        
        // 防止设置自己为自己的父类
        if (isset($data['parent_id']) && $data['parent_id'] == $id) {
            throw new \InvalidArgumentException('不能将分类设为自己的父类');
        }
        
        // 防止设置自己的后代为自己的父类（形成循环）
        if (isset($data['parent_id']) && $data['parent_id'] > 0) {
            $potentialParent = Category::findOrFail($data['parent_id']);
            if ($potentialParent->isDescendantOf($id)) {
                throw new \InvalidArgumentException('不能将自己的后代设为自己的父类');
            }
        }

        try {
            $category->fill($data);
            $category->save();

            return $category;
        } catch (\Exception $e) {
            Log::error('分类更新失败', ['error' => $e->getMessage(), 'data' => $data]);
            throw $e;
        }
    }

    /**
     * 删除分类
     *
     * @param int $id 分类ID
     * @return bool 是否成功
     */
    public function deleteCategory($id)
    {
        $category = Category::findOrFail($id);
        
        // 使用新的hasChildren方法检查子分类
        if ($category->hasChildren()) {
            throw new \InvalidArgumentException('该分类下有子分类，不能删除');
        }
        
        // 检查是否有关联的商品
        $hasProducts = Product::where('category_id', $id)->exists();
        if ($hasProducts) {
            throw new \InvalidArgumentException('该分类下有商品，不能删除');
        }

        return $category->delete();
    }

    /**
     * 构建分类树
     *
     * @param int $parentId 父级ID
     * @param int $status 状态 (null=不筛选)
     * @return array 分类树
     */
    public function getCategoryTree($parentId = 0, $status = 1)
    {
        $conditions = [];
        if ($status !== null) {
            $conditions['status'] = $status;
        }
        
        // 如果指定了parent_id，使用原来的方法
        if ($parentId > 0) {
            return Category::getTree($parentId, $conditions);
        } else {
            // 使用优化后的方法（减少数据库查询）
            return Category::getOptimizedTree($conditions);
        }
    }

    /**
     * 获取分类的所有祖先
     *
     * @param int $id 分类ID
     * @return \Illuminate\Support\Collection 祖先集合
     */
    public function getCategoryAncestors($id)
    {
        $category = Category::findOrFail($id);
        return $category->getAncestors();
    }
    
    /**
     * 获取分类的所有后代
     *
     * @param int $id 分类ID
     * @param bool $activeOnly 是否只获取激活的分类
     * @return \Illuminate\Support\Collection 后代集合
     */
    public function getCategoryDescendants($id, $activeOnly = false)
    {
        $category = Category::findOrFail($id);
        return $category->getAllDescendants($activeOnly);
    }
    
    /**
     * 获取分类的完整路径
     *
     * @param int $id 分类ID
     * @param string $separator 分隔符
     * @return string 完整路径
     */
    public function getCategoryPath($id, $separator = ' > ')
    {
        $category = Category::findOrFail($id);
        return $category->getFullPath($separator);
    }
    
    /**
     * 获取分类的面包屑
     *
     * @param int $id 分类ID
     * @return \Illuminate\Support\Collection 面包屑集合
     */
    public function getCategoryBreadcrumb($id)
    {
        $category = Category::findOrFail($id);
        return $category->getBreadcrumb();
    }
} 