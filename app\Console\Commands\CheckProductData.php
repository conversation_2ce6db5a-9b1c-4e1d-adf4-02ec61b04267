<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Product\Models\Product;
use App\Inventory\Models\Inventory;

class CheckProductData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'product:check-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检查商品和库存数据状态';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始检查商品数据...');
        
        // 检查商品表
        $totalProducts = Product::count();
        $this->info("商品总数: {$totalProducts}");
        
        if ($totalProducts > 0) {
            $this->info("\n前5个商品:");
            $products = Product::select('id', 'name', 'code', 'status', 'created_at')
                ->limit(5)
                ->get();
            
            $this->table(
                ['ID', '名称', '编码', '状态', '创建时间'],
                $products->map(function($product) {
                    return [
                        $product->id,
                        $product->name,
                        $product->code ?: '无',
                        $product->status ? '启用' : '禁用',
                        $product->created_at->format('Y-m-d H:i:s')
                    ];
                })->toArray()
            );
            
            // 状态统计
            $enabledCount = Product::where('status', 1)->count();
            $disabledCount = Product::where('status', 0)->count();
            $this->info("\n商品状态统计:");
            $this->info("启用: {$enabledCount}");
            $this->info("禁用: {$disabledCount}");
        }
        
        // 检查库存表
        $totalInventories = Inventory::count();
        $this->info("\n库存记录总数: {$totalInventories}");
        
        if ($totalInventories > 0) {
            $this->info("\n前5个库存记录:");
            $inventories = Inventory::with(['product:id,name', 'warehouse:id,location'])
                ->select('id', 'product_id', 'warehouse_id', 'stock', 'created_at')
                ->limit(5)
                ->get();
            
            $this->table(
                ['ID', '商品', '仓库', '库存', '创建时间'],
                $inventories->map(function($inventory) {
                    return [
                        $inventory->id,
                        $inventory->product->name ?? '未知商品',
                        $inventory->warehouse->location ?? '未知仓库',
                        $inventory->stock,
                        $inventory->created_at->format('Y-m-d H:i:s')
                    ];
                })->toArray()
            );
        }
        
        // 检查关联关系
        $productsWithInventory = Product::whereHas('inventories')->count();
        $productsWithoutInventory = Product::whereDoesntHave('inventories')->count();
        
        $this->info("\n关联关系统计:");
        $this->info("有库存记录的商品: {$productsWithInventory}");
        $this->info("无库存记录的商品: {$productsWithoutInventory}");
        
        // 如果没有商品数据，给出建议
        if ($totalProducts === 0) {
            $this->warn("\n⚠️  数据库中没有商品数据！");
            $this->info("建议:");
            $this->info("1. 运行数据库种子: php artisan db:seed");
            $this->info("2. 手动添加商品数据");
            $this->info("3. 导入商品数据");
        }
        
        $this->info("\n✅ 数据检查完成");
        
        return 0;
    }
} 