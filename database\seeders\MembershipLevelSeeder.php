<?php

namespace Database\Seeders;

use App\Crm\Models\MembershipLevel;
use Illuminate\Database\Seeder;

class MembershipLevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建默认会员等级
        $levels = [
            [
                'name' => '普通会员',
                'description' => '普通会员基础权益',
                'upgrade_points' => 0,
                'upgrade_amount' => 0.00,
                'quick_upgrade_amount' => 0.00,
                'discount_rate' => 0.00, // 无减免
                'is_default' => true,
                'icon' => 'member-level-1.png',
                'sort_order' => 1,
                'status' => true,
                'privileges' => json_encode([
                    'free_shipping' => false,
                    'birthday_gift' => false,
                    'priority_service' => false,
                ])
            ],
            [
                'name' => '银牌会员',
                'description' => '消费达到一定额度的会员',
                'upgrade_points' => 1000,
                'upgrade_amount' => 2000.00, // 累计消费2000元可升级
                'quick_upgrade_amount' => 1000.00, // 单笔订单1000元可直接升级
                'discount_rate' => 5.00, // 减免5元
                'is_default' => false,
                'icon' => 'member-level-2.png',
                'sort_order' => 2,
                'status' => true,
                'privileges' => json_encode([
                    'free_shipping' => false,
                    'birthday_gift' => true,
                    'priority_service' => false,
                ])
            ],
            [
                'name' => '金牌会员',
                'description' => '高消费会员',
                'upgrade_points' => 5000,
                'upgrade_amount' => 10000.00, // 累计消费10000元可升级
                'quick_upgrade_amount' => 3000.00, // 单笔订单3000元可直接升级
                'discount_rate' => 15.00, // 减免15元
                'is_default' => false,
                'icon' => 'member-level-3.png',
                'sort_order' => 3,
                'status' => true,
                'privileges' => json_encode([
                    'free_shipping' => true,
                    'birthday_gift' => true,
                    'priority_service' => false,
                ])
            ],
            [
                'name' => '钻石会员',
                'description' => '尊贵VIP会员',
                'upgrade_points' => 10000,
                'upgrade_amount' => 30000.00, // 累计消费30000元可升级
                'quick_upgrade_amount' => 8000.00, // 单笔订单8000元可直接升级
                'discount_rate' => 30.00, // 减免30元
                'is_default' => false,
                'icon' => 'member-level-4.png',
                'sort_order' => 4,
                'status' => true,
                'privileges' => json_encode([
                    'free_shipping' => true,
                    'birthday_gift' => true,
                    'priority_service' => true,
                ])
            ],
        ];
        
        foreach ($levels as $level) {
            MembershipLevel::create($level);
        }
    }
} 