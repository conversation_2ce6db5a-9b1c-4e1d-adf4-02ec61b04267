/**
 * 测试 API 加载问题
 * 检查分类页面加载卡住的原因
 */

console.log('🧪 开始测试 API 加载问题\n');

// 模拟小程序环境
global.wx = {
  showToast: (options) => console.log('Toast:', options.title),
  showLoading: (options) => console.log('Loading:', options.title),
  hideLoading: () => console.log('Hide Loading'),
  getStorageSync: (key) => {
    const storage = {
      'token': 'test-token',
      'isLoggedIn': true,
      'userInfo': JSON.stringify({ id: 1, username: 'testuser' })
    };
    return storage[key] || '';
  },
  setStorageSync: () => {},
  request: (options) => {
    console.log('🌐 模拟请求:', options.url);
    
    // 模拟不同的响应
    setTimeout(() => {
      if (options.url.includes('/public/categories/tree')) {
        // 模拟分类树响应
        options.success({
          statusCode: 200,
          data: {
            success: true,
            data: [
              { id: 1, name: '测试分类1', image_url: '' },
              { id: 2, name: '测试分类2', image_url: '' }
            ]
          }
        });
      } else if (options.url.includes('/public/categories/') && options.url.includes('/children')) {
        // 模拟子分类响应
        options.success({
          statusCode: 200,
          data: {
            success: true,
            data: []
          }
        });
      } else if (options.url.includes('/api/inventory/products')) {
        // 模拟商品响应
        options.success({
          statusCode: 200,
          data: {
            success: true,
            data: {
              data: [
                {
                  id: 1,
                  name: '测试商品1',
                  price: '10.00',
                  image: '/images/test1.jpg'
                }
              ]
            }
          }
        });
      } else {
        // 其他请求
        options.success({
          statusCode: 200,
          data: { success: true, data: [] }
        });
      }
    }, 100); // 模拟网络延迟
  }
};

global.Component = (config) => config;
global.Page = (config) => config;

// 测试分类页面初始化
async function testCategoryPageInit() {
  console.log('1️⃣ 测试分类页面初始化');
  
  try {
    // 模拟页面数据
    const mockPage = {
      data: {
        loading: true,
        targetCategoryId: null,
        currentCategory: null,
        categories: [],
        products: []
      },
      setData: function(data) {
        console.log('   📝 setData:', Object.keys(data));
        Object.assign(this.data, data);
      }
    };
    
    // 导入 API
    const { api } = require('./utils/api');
    
    console.log('   🔄 开始加载分类...');
    
    // 测试分类树加载
    const startTime = Date.now();
    const categories = await api.getCategoryTree(0);
    const loadTime = Date.now() - startTime;
    
    console.log(`   ✅ 分类加载完成，耗时: ${loadTime}ms`);
    console.log(`   📊 分类数量: ${categories.length}`);
    
    if (categories.length > 0) {
      console.log(`   📋 第一个分类: ${categories[0].name}`);
    }
    
    // 测试子分类加载
    if (categories.length > 0) {
      console.log('   🔄 开始加载子分类...');
      const childStartTime = Date.now();
      const children = await api.getCategoryChildren(categories[0].id);
      const childLoadTime = Date.now() - childStartTime;
      
      console.log(`   ✅ 子分类加载完成，耗时: ${childLoadTime}ms`);
      console.log(`   📊 子分类数量: ${children.length}`);
    }
    
    // 测试商品加载
    console.log('   🔄 开始加载商品...');
    const productStartTime = Date.now();
    const products = await api.getProducts({ page: 1, limit: 20, category_id: 1 });
    const productLoadTime = Date.now() - productStartTime;
    
    console.log(`   ✅ 商品加载完成，耗时: ${productLoadTime}ms`);
    console.log(`   📊 商品数据结构:`, typeof products);
    
    if (products && products.data && products.data.data) {
      console.log(`   📊 商品数量: ${products.data.data.length}`);
    }
    
  } catch (error) {
    console.log('   ❌ 分类页面初始化失败:', error.message);
    console.log('   🔍 错误详情:', error);
  }
}

// 测试超时情况
async function testTimeout() {
  console.log('\n2️⃣ 测试超时情况');
  
  // 模拟超时的请求
  const timeoutPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
      reject(new Error('请求超时'));
    }, 5000);
  });
  
  const quickPromise = new Promise((resolve) => {
    setTimeout(() => {
      resolve('快速响应');
    }, 100);
  });
  
  try {
    const result = await Promise.race([quickPromise, timeoutPromise]);
    console.log('   ✅ 快速响应测试通过:', result);
  } catch (error) {
    console.log('   ❌ 超时测试失败:', error.message);
  }
}

// 运行测试
async function runTests() {
  await testCategoryPageInit();
  await testTimeout();
  
  console.log('\n✅ API 加载测试完成！');
  console.log('\n📋 可能的问题原因:');
  console.log('   1. API 端点不存在或返回错误');
  console.log('   2. 网络请求超时');
  console.log('   3. Promise.all 中某个请求挂起');
  console.log('   4. 缓存机制导致的问题');
  console.log('   5. 登录状态监听器导致的阻塞');
}

runTests().catch(console.error);
