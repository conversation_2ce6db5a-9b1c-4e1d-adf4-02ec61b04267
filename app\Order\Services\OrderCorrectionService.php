<?php

namespace App\Order\Services;

use App\Order\Models\Order;
use App\Order\Models\OrderCorrection;
use App\Billing\Services\PaymentLinkService;
use App\Billing\Models\BillingPaymentLink;
use App\Payment\Services\PaymentOfferService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class OrderCorrectionService
{
    private $paymentOfferService;

    public function __construct(PaymentOfferService $paymentOfferService = null)
    {
        $this->paymentOfferService = $paymentOfferService;
    }

    /**
     * 创建订单更正
     */
    public function createCorrection(Order $order, array $correctionData, array $items, int $operatedBy): OrderCorrection
    {
        return DB::transaction(function () use ($order, $correctionData, $items, $operatedBy) {
            // 🔥 添加订单状态检查：只有已送达的订单才能进行更正
            if ($order->status !== 'delivered') {
                throw new \Exception('只有已送达的订单才能进行更正');
            }
            
            // 检查是否已有待处理的更正
            $hasPendingCorrection = $order->corrections()
                ->where('status', 'pending')
                ->exists();
                
            if ($hasPendingCorrection) {
                throw new \Exception('该订单已有待处理的更正记录，请先处理完成');
            }
            
            // 🔥 新增：检查是否已有确认的更正记录
            $hasConfirmedCorrection = $order->corrections()
                ->where('status', 'confirmed')
                ->exists();
                
            if ($hasConfirmedCorrection) {
                throw new \Exception('该订单已有确认的更正记录，如需再次更正，请先反审之前的更正记录');
            }

            // 🔥 重要限制：微信支付订单如果已经退款，不能再次更正
            if ($order->payment_method === 'wechat') {
                $this->checkWechatRefundRestriction($order);
            }
            
            // 计算更正后的总金额
            $correctedTotal = 0;
            foreach ($items as $item) {
                $correctedTotal += $item['corrected_total'];
            }
            
            // 🔥 修复：支付优惠处理逻辑
            // 获取原始订单的支付优惠信息
            $originalPaymentDiscount = $order->payment_discount ?? 0;
            $originalSubtotal = $order->subtotal ?? $order->total;
            
            // 计算更正后的商品小计
            $correctedSubtotal = $correctedTotal;
            
            // 🔥 修正：微信支付优惠的正确计算逻辑
            $newPaymentDiscount = $originalPaymentDiscount; // 默认保持原优惠
            $paymentDiscountChange = 0;
            
            if ($order->payment_method === 'wechat' && $originalPaymentDiscount > 0) {
                // 计算商品金额变化
                $subtotalChange = $correctedSubtotal - $originalSubtotal;
                
                if ($subtotalChange < 0) {
                    // 🔥 减少场景：按比例反推支付优惠
                    $discountRate = $originalPaymentDiscount / $originalSubtotal; // 原始优惠比例
                    $reducedAmount = abs($subtotalChange); // 减少的商品金额
                    $reducedDiscount = $reducedAmount * $discountRate; // 按比例减少的优惠
                    
                    $newPaymentDiscount = max(0, $originalPaymentDiscount - $reducedDiscount);
                    $paymentDiscountChange = $newPaymentDiscount - $originalPaymentDiscount;
                    
                    Log::info('💰 微信支付减少场景：按比例反推支付优惠', [
                        'order_id' => $order->id,
                        'original_subtotal' => $originalSubtotal,
                        'corrected_subtotal' => $correctedSubtotal,
                        'subtotal_change' => $subtotalChange,
                        'reduced_amount' => $reducedAmount,
                        'original_payment_discount' => $originalPaymentDiscount,
                        'discount_rate' => round($discountRate * 100, 2) . '%',
                        'reduced_discount' => $reducedDiscount,
                        'new_payment_discount' => $newPaymentDiscount,
                        'payment_discount_change' => $paymentDiscountChange
                    ]);
                } elseif ($subtotalChange > 0) {
                    // 🔥 增加场景：增加部分不享受支付优惠，保持原有优惠不变
                    $newPaymentDiscount = $originalPaymentDiscount; // 保持原有优惠
                    $paymentDiscountChange = 0; // 无变化
                    
                    Log::info('💰 微信支付增加场景：增加部分不享受支付优惠', [
                        'order_id' => $order->id,
                        'original_subtotal' => $originalSubtotal,
                        'corrected_subtotal' => $correctedSubtotal,
                        'subtotal_change' => $subtotalChange,
                        'increased_amount' => $subtotalChange,
                        'original_payment_discount' => $originalPaymentDiscount,
                        'new_payment_discount' => $newPaymentDiscount,
                        'payment_discount_change' => $paymentDiscountChange,
                        'logic' => '增加部分按原价计算，原有优惠保持不变'
                    ]);
                } else {
                    // 无变化场景
                    Log::info('💰 微信支付无变化场景：保持原支付优惠', [
                        'order_id' => $order->id,
                        'original_payment_discount' => $originalPaymentDiscount,
                        'new_payment_discount' => $newPaymentDiscount
                    ]);
                }
            } else {
                // 其他支付方式保持原支付优惠不变
                Log::info('📊 非微信支付订单更正保持原支付优惠', [
                    'order_id' => $order->id,
                    'payment_method' => $order->payment_method,
                    'original_payment_discount' => $originalPaymentDiscount,
                    'corrected_subtotal' => $correctedSubtotal,
                    'keep_original_discount' => true
                ]);
            }
            
            // 计算最终的更正后金额
            $finalCorrectedTotal = max(0, $correctedSubtotal - $newPaymentDiscount);
            
            // 计算差异金额（考虑支付优惠变化）
            $differenceAmount = $finalCorrectedTotal - $order->total;
            
            Log::info('📊 订单更正金额计算详情', [
                'order_id' => $order->id,
                'payment_method' => $order->payment_method,
                'original_total' => $order->total,
                'corrected_subtotal' => $correctedSubtotal,
                'original_payment_discount' => $originalPaymentDiscount,
                'new_payment_discount' => $newPaymentDiscount,
                'payment_discount_change' => $paymentDiscountChange,
                'final_corrected_total' => $finalCorrectedTotal,
                'difference_amount' => $differenceAmount,
                'recalculated_payment_discount' => $order->payment_method === 'wechat'
            ]);
            
            // 确定更正类型（如果前端传递了类型，优先使用前端的）
            $correctionType = $correctionData['correction_type'] ?? $this->determineCorrectionType($differenceAmount);
            
            Log::info('确定更正类型', [
                'order_id' => $order->id,
                'difference_amount' => $differenceAmount,
                'frontend_correction_type' => $correctionData['correction_type'] ?? null,
                'calculated_correction_type' => $this->determineCorrectionType($differenceAmount),
                'final_correction_type' => $correctionType
            ]);
            
            // 检查是否为无修改确认
            $isNoChangeConfirmation = $correctionType === 'no_change';
            
            // 🔥 修复：无修改确认时以原订单金额为准，忽略系统重新计算的差异
            if ($isNoChangeConfirmation && abs($differenceAmount) > 0.01) {
                Log::info('无修改确认：以用户实际支付金额为准，忽略系统重新计算的差异', [
                    'order_id' => $order->id,
                    'user_paid_amount' => $order->total,
                    'system_recalculated_amount' => $finalCorrectedTotal,
                    'difference_amount' => $differenceAmount,
                    'payment_method' => $order->payment_method,
                    'payment_discount_change' => $paymentDiscountChange,
                    'action' => '保持原订单金额，直接核销实际支付金额'
                ]);
                
                // 无修改确认：保持原订单金额不变
                $finalCorrectedTotal = $order->total;
                $differenceAmount = 0; // 强制差额为0
                
                Log::info('无修改确认处理完成', [
                    'order_id' => $order->id,
                    'final_amount' => $finalCorrectedTotal,
                    'difference_amount' => $differenceAmount,
                    'reason' => '按用户实际支付金额核销，忽略系统计算差异'
                ]);
            }
            
            // 🔥 重要变更：所有更正创建时直接确认，不需要二次确认
            $correction = OrderCorrection::create([
                'order_id' => $order->id,
                'correction_no' => $this->generateCorrectionNo(),
                'correction_type' => $correctionType,
                'original_total' => $order->total,
                'corrected_total' => $finalCorrectedTotal,
                'difference_amount' => $differenceAmount,
                'correction_reason' => $correctionData['correction_reason'] ?? null,
                'status' => 'confirmed', // 🔥 所有更正直接设为已确认
                'corrected_by' => $operatedBy,
                'confirmed_by' => $operatedBy, // 🔥 创建时直接设置确认人
                'confirmed_at' => now(), // 🔥 创建时直接设置确认时间
                // 🔥 新增：保存支付优惠相关信息
                'original_payment_discount' => $originalPaymentDiscount,
                'corrected_payment_discount' => $newPaymentDiscount,
                'payment_discount_change' => $paymentDiscountChange,
                // 🔥 新增：货到付款相关字段
                'payment_method' => $correctionData['payment_method'] ?? null,
                'is_credit' => $correctionData['is_credit'] ?? false,
                'actual_received_amount' => $correctionData['actual_received_amount'] ?? null,
                'discount_amount' => $correctionData['discount_amount'] ?? 0,
                'extra_amount' => $correctionData['extra_amount'] ?? 0,
                'discount_reason' => $correctionData['discount_reason'] ?? null,
                // 🔥 新增：微信补款相关字段
                'payment_method_for_supplement' => $correctionData['payment_method_for_supplement'] ?? null,
                'pricing_details' => json_encode([
                    'original_subtotal' => $originalSubtotal,
                    'corrected_subtotal' => $correctedSubtotal,
                    'original_payment_discount' => $originalPaymentDiscount,
                    'new_payment_discount' => $newPaymentDiscount,
                    'payment_discount_change' => $paymentDiscountChange,
                    'final_corrected_total' => $finalCorrectedTotal,
                    'payment_offer_recalculated' => $originalPaymentDiscount > 0,
                    'payment_method' => $order->payment_method,
                    'calculated_at' => now()->toISOString(),
                    // 🔥 新增：货到付款相关信息
                    'cod_payment_details' => [
                        'payment_method' => $correctionData['payment_method'] ?? null,
                        'is_credit' => $correctionData['is_credit'] ?? false,
                        'actual_received_amount' => $correctionData['actual_received_amount'] ?? null,
                        'discount_amount' => $correctionData['discount_amount'] ?? 0,
                        'extra_amount' => $correctionData['extra_amount'] ?? 0,
                        'discount_reason' => $correctionData['discount_reason'] ?? null,
                    ],
                    // 🔥 新增：微信补款相关信息
                    'wechat_supplement_details' => [
                        'payment_method_for_supplement' => $correctionData['payment_method_for_supplement'] ?? null,
                    ]
                ])
            ]);

            // 创建更正项目
            foreach ($items as $item) {
                $correction->items()->create([
                    'order_item_id' => $item['order_item_id'],
                    'original_quantity' => $item['original_quantity'],
                    'original_price' => $item['original_price'],
                    'original_total' => $item['original_total'],
                    'corrected_quantity' => $item['corrected_quantity'],
                    'corrected_weight' => $item['corrected_weight'] ?? null,
                    'corrected_price' => $item['corrected_price'],
                    'corrected_total' => $item['corrected_total'],
                    'difference_amount' => $item['difference_amount'] ?? ($item['corrected_total'] - $item['original_total']),
                    'correction_reason' => $item['reason'] ?? null,
                ]);
            }

            // 🔥 重要变更：所有更正创建时都直接确认并处理
            
            // 更新订单项数据
            $this->updateOrderItems($correction);

            // 更新订单总金额和更正状态
            $order->update([
                'total' => $finalCorrectedTotal,
                'correction_status' => 'confirmed',
                'is_corrected' => true,
            ]);

            // 🔥 修复：使用try-catch包装账单服务调用，避免账单问题影响更正核心功能
            try {
                if ($isNoChangeConfirmation) {
                    // 无修改确认只记录状态，不处理任何支付差异
                    $this->notifyBillingForRecord($correction, $operatedBy);
                } else {
                    // 有变化的更正需要处理支付差异
                    $this->handleCorrectionPaymentDifferenceWithPaymentMethod($correction, $operatedBy);
                    $this->notifyBillingForRecord($correction, $operatedBy);
                }
            } catch (\Exception $e) {
                Log::error('订单更正创建时支付差异处理失败，但更正已确认', [
                    'correction_id' => $correction->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                // 不抛出异常，让更正创建继续
            }

            $this->logOperation('create_and_confirm_correction', $correction, $operatedBy);

            return $correction;
        });
    }

    /**
     * 确认订单更正
     */
    public function confirmCorrection(OrderCorrection $correction, int $operatedBy): void
    {
        DB::transaction(function () use ($correction, $operatedBy) {
            // 更新更正状态
            $correction->update([
                'status' => 'confirmed',
                'confirmed_at' => now(),
                'confirmed_by' => $operatedBy,
            ]);

            // 更新订单项数据
            $this->updateOrderItems($correction);

            // 更新订单总金额和更正状态
            $correction->order->update([
                'total' => $correction->corrected_total,
                'correction_status' => 'confirmed',
                'is_corrected' => true,
            ]);

            // 🔥 修复：使用try-catch包装账单服务调用，避免账单问题影响更正核心功能
            try {
                // 根据支付方式和前端传递的数据处理支付差异
                $this->handleCorrectionPaymentDifferenceWithPaymentMethod($correction, $operatedBy);
            } catch (\Exception $e) {
                Log::error('订单更正确认时支付差异处理失败，但更正已确认', [
                    'correction_id' => $correction->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                // 不抛出异常，让更正确认继续
            }

            try {
                // 通知账单模块记录状态
                $this->notifyBillingForRecord($correction, $operatedBy);
            } catch (\Exception $e) {
                Log::error('订单更正确认时账单状态记录失败，但更正已确认', [
                    'correction_id' => $correction->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                // 不抛出异常，让更正确认继续
            }

            $this->logOperation('confirm_correction', $correction, $operatedBy);
        });
    }

    /**
     * 🔥 新增：根据支付方式处理订单更正的支付差异
     */
    private function handleCorrectionPaymentDifferenceWithPaymentMethod(OrderCorrection $correction, int $operatedBy): void
    {
        $differenceAmount = $correction->difference_amount;
        $orderPaymentMethod = $correction->order->payment_method;
        
        // 🔥 修复：无修改确认不应该有任何支付差异处理
        if ($correction->correction_type === 'no_change') {
            Log::info('无修改确认，无需处理任何支付差异', [
                'correction_id' => $correction->id,
                'correction_type' => $correction->correction_type,
                'difference_amount' => $differenceAmount
            ]);
            
            // 双重安全检查：无修改确认不应该有差异
            if (abs($differenceAmount) > 0.01) {
                Log::error('严重错误：无修改确认但存在金额差异！这不应该发生', [
                    'correction_id' => $correction->id,
                    'difference_amount' => $differenceAmount,
                    'original_total' => $correction->original_total,
                    'corrected_total' => $correction->corrected_total,
                    'suggestion' => '应该在创建更正时就阻止或自动调整确认类型'
                ]);
            }
            
            return; // 无修改确认直接返回，不处理任何支付差异
        }

        // 无金额差异时无需处理
        if (abs($differenceAmount) < 0.01) {
            Log::info('无金额差异，无需处理支付', [
                'correction_id' => $correction->id,
                'difference_amount' => $differenceAmount
            ]);
            return;
        }

        $billingService = app(\App\Billing\Services\BillingService::class);

        Log::info('开始处理订单更正支付差异', [
            'correction_id' => $correction->id,
            'order_payment_method' => $orderPaymentMethod,
            'difference_amount' => $differenceAmount,
            'has_payment_method' => !empty($correction->payment_method),
            'has_payment_method_for_supplement' => !empty($correction->payment_method_for_supplement),
            'operator_id' => $operatedBy
        ]);

        // 根据订单支付方式和差异金额处理
        if ($orderPaymentMethod === 'wechat') {
            if ($differenceAmount < 0) {
                // 微信支付退款：直接退款到微信
                $billingService->processOrderCorrectionPaymentDifference($correction, $differenceAmount, $operatedBy);
            } else {
                // 微信支付补款：检查是否选择了补款支付方式
                if (!empty($correction->payment_method_for_supplement)) {
                    $billingService->processWechatOrderCorrectionSupplement($correction, [
                        'payment_method_for_supplement' => $correction->payment_method_for_supplement
                    ], $operatedBy);
                } else {
                    // 默认微信补款
                    $billingService->processOrderCorrectionPaymentDifference($correction, $differenceAmount, $operatedBy);
                }
            }
        } elseif ($orderPaymentMethod === 'cod') {
            // 货到付款：使用新的处理方法
            $paymentData = [
                'payment_method' => $correction->payment_method,
                'is_credit' => $correction->is_credit,
                'actual_received_amount' => $correction->actual_received_amount,
                'discount_amount' => $correction->discount_amount,
                'extra_amount' => $correction->extra_amount,
                'discount_reason' => $correction->discount_reason,
            ];
            
            $billingService->processCodOrderCorrectionPayment($correction, $paymentData, $operatedBy);
        } else {
            // 其他支付方式：使用原有逻辑
            $billingService->processOrderCorrectionPaymentDifference($correction, $differenceAmount, $operatedBy);
        }
    }

    /**
     * 🔥 原有：处理订单更正的支付差异（退款/补款）
     * Order模块负责触发，Payment模块负责执行
     */
    private function handleCorrectionPaymentDifference(OrderCorrection $correction, int $operatedBy): void
    {
        $differenceAmount = $correction->difference_amount;
        
        // 🔥 修复：无修改确认不应该有任何支付差异处理
        if ($correction->correction_type === 'no_change') {
            Log::info('无修改确认，无需处理任何支付差异', [
                'correction_id' => $correction->id,
                'correction_type' => $correction->correction_type,
                'difference_amount' => $differenceAmount
            ]);
            
            // 双重安全检查：无修改确认不应该有差异
            if (abs($differenceAmount) > 0.01) {
                Log::error('严重错误：无修改确认但存在金额差异！这不应该发生', [
                    'correction_id' => $correction->id,
                    'difference_amount' => $differenceAmount,
                    'original_total' => $correction->original_total,
                    'corrected_total' => $correction->corrected_total,
                    'suggestion' => '应该在创建更正时就阻止或自动调整确认类型'
                ]);
            }
            
            return; // 无修改确认直接返回，不处理任何支付差异
        }

        // 有金额差异才处理
        if (abs($differenceAmount) >= 0.01) {
            $paymentMethod = $correction->order->payment_method;
            
            Log::info('Order模块开始处理支付差异', [
                'correction_id' => $correction->id,
                'order_id' => $correction->order_id,
                'difference_amount' => $differenceAmount,
                'payment_method' => $paymentMethod,
                'operator_id' => $operatedBy
            ]);

            if ($differenceAmount < 0) {
                // 金额减少 - 触发退款
                $this->triggerRefund($correction, abs($differenceAmount), $operatedBy);
            } else {
                // 金额增加 - 触发补款
                $this->triggerSupplement($correction, $differenceAmount, $operatedBy);
            }
        }
    }

    /**
     * 🔥 修改：通知账单系统处理退款（不再直接处理）
     */
    private function triggerRefund(OrderCorrection $correction, float $refundAmount, int $operatedBy): void
    {
        try {
            Log::info('Order模块通知账单系统处理支付差异（退款）', [
                'correction_id' => $correction->id,
                'order_id' => $correction->order_id,
                'refund_amount' => $refundAmount,
                'payment_method' => $correction->order->payment_method,
                'operator_id' => $operatedBy
            ]);

            // 通知账单系统处理支付差异（负数表示退款）
            $billingService = app(\App\Billing\Services\BillingService::class);
            $billingService->processOrderCorrectionPaymentDifference($correction, -$refundAmount, $operatedBy);

            Log::info('账单系统支付差异处理完成', [
                'correction_id' => $correction->id,
                'difference_amount' => -$refundAmount
            ]);

        } catch (\Exception $e) {
            Log::error('账单系统支付差异处理失败', [
                'correction_id' => $correction->id,
                'difference_amount' => -$refundAmount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception("支付差异处理失败：{$e->getMessage()}");
        }
    }

    /**
     * 🔥 修改：通知账单系统处理补款（不再直接处理）
     */
    private function triggerSupplement(OrderCorrection $correction, float $supplementAmount, int $operatedBy): void
    {
        try {
            Log::info('Order模块通知账单系统处理支付差异（补款）', [
                'correction_id' => $correction->id,
                'order_id' => $correction->order_id,
                'supplement_amount' => $supplementAmount,
                'payment_method' => $correction->order->payment_method,
                'operator_id' => $operatedBy
            ]);

            // 通知账单系统处理支付差异（正数表示补款）
            $billingService = app(\App\Billing\Services\BillingService::class);
            $billingService->processOrderCorrectionPaymentDifference($correction, $supplementAmount, $operatedBy);

            Log::info('账单系统支付差异处理完成', [
                'correction_id' => $correction->id,
                'difference_amount' => $supplementAmount
            ]);

        } catch (\Exception $e) {
            Log::error('账单系统支付差异处理失败', [
                'correction_id' => $correction->id,
                'difference_amount' => $supplementAmount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception("支付差异处理失败：{$e->getMessage()}");
        }
    }

    /**
     * 🔥 重构：只通知账单模块记录状态，不处理退款
     */
    private function notifyBillingForRecord(OrderCorrection $correction, int $operatedBy): void
    {
        try {
            Log::info('通知账单模块记录订单更正状态', [
                'correction_id' => $correction->id,
                'order_id' => $correction->order_id,
                'correction_type' => $correction->correction_type,
                'operator_id' => $operatedBy
            ]);

            $billingService = app(\App\Billing\Services\BillingService::class);
            
            // 🔥 修复：传递完整的支付方式信息给账单系统
            $paymentMethodData = [
                'order_payment_method' => $correction->order->payment_method,
                'correction_payment_method' => $correction->payment_method,
                'payment_method_for_supplement' => $correction->payment_method_for_supplement,
                'is_credit' => $correction->is_credit,
                'actual_received_amount' => $correction->actual_received_amount,
                'discount_amount' => $correction->discount_amount,
                'extra_amount' => $correction->extra_amount,
                'discount_reason' => $correction->discount_reason,
            ];
            
            // 只调用记录方法，不处理退款
            $billingService->recordOrderCorrectionResult($correction, [
                'operator_id' => $operatedBy,
                'notes' => "订单更正确认完成，更正单号：{$correction->correction_no}",
                'correction_type' => $correction->correction_type,
                'difference_amount' => $correction->difference_amount,
                'payment_method_data' => $paymentMethodData, // 🔥 新增：支付方式数据
                'metadata' => [
                    'correction_id' => $correction->id,
                    'correction_no' => $correction->correction_no,
                    'correction_confirmed_at' => now()->toISOString(),
                    'handled_by_order_module' => true, // 标识已由Order模块处理
                    'payment_context' => $paymentMethodData // 🔥 新增：支付上下文
                ]
            ]);

            Log::info('账单模块状态记录成功', [
                'correction_id' => $correction->id,
                'operator_id' => $operatedBy
            ]);

        } catch (\Exception $e) {
            Log::error('账单模块状态记录失败', [
                'correction_id' => $correction->id,
                'operator_id' => $operatedBy,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 记录失败不应该影响订单更正流程
            Log::warning('账单模块状态记录失败，但订单更正流程继续', [
                'correction_id' => $correction->id
            ]);
        }
    }

    /**
     * 🔥 新增：检查微信支付退款限制
     */
    private function checkWechatRefundRestriction(Order $order): void
    {
        // 检查是否有已确认的减少类型更正（退款）
        $hasRefundCorrection = $order->corrections()
            ->where('status', 'confirmed')
            ->where('correction_type', 'decrease')
            ->exists();

        if ($hasRefundCorrection) {
            throw new \Exception('微信支付订单已经退款，无法再次更正。如需重新更正，请联系财务人员处理。');
        }

        // 检查账单系统中是否有退款记录
        try {
            $bill = \App\Billing\Models\Bill::where('order_id', $order->id)->first();
            if ($bill) {
                $hasRefundPayment = $bill->paymentRecords()
                    ->where('payment_type', 'refund')
                    ->where('status', 'success')
                    ->exists();

                if ($hasRefundPayment) {
                    throw new \Exception('该订单在账单系统中已有退款记录，无法再次更正。');
                }
            }
        } catch (\Exception $e) {
            // 如果账单系统检查失败，记录日志但不阻止更正
            Log::warning('检查微信退款限制时账单系统查询失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }

        // 检查微信支付系统中的退款记录
        try {
            $hasWechatRefund = \App\WechatPayment\Models\WechatServiceRefund::where('order_id', $order->id)
                ->where('refund_status', 'SUCCESS')
                ->exists();

            if ($hasWechatRefund) {
                throw new \Exception('该订单在微信支付系统中已有退款记录，无法再次更正。');
            }
        } catch (\Exception $e) {
            // 如果微信支付系统检查失败，记录日志但不阻止更正
            Log::warning('检查微信退款限制时微信支付系统查询失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 生成更正单号
     */
    private function generateCorrectionNo(): string
    {
        return 'COR' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 确定更正类型
     */
    private function determineCorrectionType(float $differenceAmount): string
    {
        if (abs($differenceAmount) < 0.01) {
            return 'no_change';
        } elseif ($differenceAmount > 0) {
            return 'increase';
        } else {
            return 'decrease';
        }
    }

    /**
     * 更新订单项数据
     */
    private function updateOrderItems(OrderCorrection $correction): void
    {
        foreach ($correction->items as $correctionItem) {
            $orderItem = $correctionItem->orderItem;
            $product = $orderItem->product;
            
            // 计算数量变化
            $quantityDifference = $correctionItem->corrected_quantity - $correctionItem->original_quantity;
            
            // 更新订单项数据
            $orderItem->update([
                'quantity' => $correctionItem->corrected_quantity,
                'weight' => $correctionItem->corrected_weight,
                'price' => $correctionItem->corrected_price,
                'total' => $correctionItem->corrected_total,
            ]);
            
            // 🔥 新增：调整库存
            if ($quantityDifference != 0 && $product && $product->track_inventory) {
                try {
                    if ($quantityDifference > 0) {
                        // 数量增加，需要减少库存
                        $result = $product->reduceStockWithPolicy($quantityDifference, $orderItem->unit_id);
                        
                        Log::info('订单更正：减少库存', [
                            'correction_id' => $correction->id,
                            'product_id' => $product->id,
                            'product_name' => $product->name,
                            'quantity_increased' => $quantityDifference,
                            'stock_reduced' => $quantityDifference,
                            'result' => $result,
                            'stock_before' => $result['stock_before'] ?? 'N/A',
                            'stock_after' => $result['stock_after'] ?? 'N/A'
                        ]);
                        
                        if (!$result['success']) {
                            Log::warning('订单更正库存减少失败', [
                                'correction_id' => $correction->id,
                                'product_id' => $product->id,
                                'error' => $result['message']
                            ]);
                        }
                    } else {
                        // 数量减少，需要增加库存
                        $stockToAdd = abs($quantityDifference);
                        $product->addStock($stockToAdd);
                        
                        $stockBefore = $product->getTotalStock() - $stockToAdd;
                        $stockAfter = $product->getTotalStock();
                        Log::info('订单更正：增加库存', [
                            'correction_id' => $correction->id,
                            'product_id' => $product->id,
                            'product_name' => $product->name,
                            'quantity_decreased' => abs($quantityDifference),
                            'stock_added' => $stockToAdd,
                            'stock_before' => $stockBefore,
                            'stock_after' => $stockAfter
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::error('订单更正库存调整失败', [
                        'correction_id' => $correction->id,
                        'product_id' => $product->id,
                        'quantity_difference' => $quantityDifference,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    
                    // 不抛出异常，避免影响更正流程，但记录错误
                }
            }
        }
    }

    /**
     * 获取账单处理错误的友好提示信息
     */
    private function getAccountingErrorMessage(\Exception $e): string
    {
        $originalMessage = $e->getMessage();
        
        // 根据错误类型返回更友好的错误信息
        if (str_contains($originalMessage, "Column 'bill_id' cannot be null")) {
            return "账单系统配置错误，请联系系统管理员修复后重试";
        }
        
        if (str_contains($originalMessage, "只有确认后的订单才能创建正式账单")) {
            return "订单状态异常，无法创建账单，请刷新页面后重试";
        }
        
        if (str_contains($originalMessage, "未找到该订单的微信支付记录")) {
            return "未找到原始支付记录，无法处理退款，请联系财务人员";
        }
        
        if (str_contains($originalMessage, "Connection timed out") || str_contains($originalMessage, "timeout")) {
            return "网络连接超时，请稍后重试";
        }
        
        if (str_contains($originalMessage, "Integrity constraint violation")) {
            return "数据约束冲突，可能存在重复操作，请刷新页面确认状态";
        }
        
        // 默认错误信息
        return "账单处理失败：" . $originalMessage . "。请联系管理员或稍后重试";
    }

    /**
     * 记录操作日志
     */
    private function logOperation(string $action, OrderCorrection $correction, int $operatedBy, array $data = []): void
    {
        Log::info('Order correction operation', [
            'action' => $action,
            'correction_id' => $correction->id,
            'correction_no' => $correction->correction_no,
            'order_id' => $correction->order_id,
            'order_no' => $correction->order->order_no ?? null,
            'operated_by' => $operatedBy,
            'timestamp' => now()->toDateTimeString(),
            'data' => $data
        ]);
    }

    /**
     * 取消订单更正
     */
    public function cancelCorrection(OrderCorrection $correction): void
    {
        DB::transaction(function () use ($correction) {
            // 如果更正已确认，需要先恢复订单数据
            if ($correction->status === 'confirmed') {
                Log::info('撤回已确认的更正，需要恢复订单数据', [
                    'correction_id' => $correction->id,
                    'order_id' => $correction->order_id,
                    'original_total' => $correction->original_total,
                    'corrected_total' => $correction->corrected_total
                ]);
                
                // 恢复订单项数据到更正前的状态
                $this->restoreOrderItems($correction);

                // 恢复订单总金额和更正状态
                $correction->order->update([
                    'total' => $correction->original_total,
                    'is_corrected' => false,
                ]);

                // 取消相关的支付记录
                $this->cancelRelatedPaymentRecords($correction);

                // 如果有相关账单，需要处理账单状态
                $this->handleBillAfterReverse($correction);
            }

            // 更新更正记录状态为已取消
            $correction->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
            ]);

            // 取消相关的付款链接
            $this->cancelRelatedPaymentLinks($correction, 'Order correction cancelled');

            $this->logOperation('cancel_correction', $correction, auth()->id() ?? 0);
        });
    }

    /**
     * 反审订单更正
     */
    public function reverseCorrection(OrderCorrection $correction, int $operatedBy): void
    {
        DB::transaction(function () use ($correction, $operatedBy) {
            // 恢复订单项数据到更正前的状态
            $this->restoreOrderItems($correction);

            // 恢复订单总金额，不设置correction_status（让计算字段自动处理）
            $correction->order->update([
                'total' => $correction->original_total,
                'is_corrected' => false,
            ]);

            // 更新更正状态为待确认
            $correction->update([
                'status' => 'pending',
                'confirmed_at' => null,
                'confirmed_by' => null,
                'reversed_at' => now(),
                'reversed_by' => $operatedBy,
            ]);

            // 取消相关的支付记录
            $this->cancelRelatedPaymentRecords($correction);

            // 取消相关的付款链接
            $this->cancelRelatedPaymentLinks($correction, '订单更正反审，付款链接已失效');

            // 如果有相关账单，需要处理账单状态
            $this->handleBillAfterReverse($correction);

            $this->logOperation('reverse_correction', $correction, $operatedBy);
        });
    }

    /**
     * 恢复订单项数据到更正前的状态
     */
    private function restoreOrderItems(OrderCorrection $correction): void
    {
        foreach ($correction->items as $correctionItem) {
            $orderItem = $correctionItem->orderItem;
            $product = $orderItem->product;
            
            // 计算需要恢复的数量变化（与更正时相反）
            $quantityDifference = $correctionItem->corrected_quantity - $correctionItem->original_quantity;
            
            // 恢复订单项数据
            $orderItem->update([
                'quantity' => $correctionItem->original_quantity,
                'weight' => $correctionItem->original_weight,
                'price' => $correctionItem->original_price,
                'total' => $correctionItem->original_total,
            ]);
            
            // 🔥 新增：恢复库存（与更正时的操作相反）
            if ($quantityDifference != 0 && $product && $product->track_inventory) {
                try {
                    if ($quantityDifference > 0) {
                        // 之前是增加数量减少库存，现在要增加库存
                        $stockToAdd = $quantityDifference;
                        $product->addStock($stockToAdd);
                        
                        $stockBefore = $product->getTotalStock() - $stockToAdd;
                        $stockAfter = $product->getTotalStock();
                        Log::info('订单更正反审：恢复库存（增加）', [
                            'correction_id' => $correction->id,
                            'product_id' => $product->id,
                            'product_name' => $product->name,
                            'original_quantity_increase' => $quantityDifference,
                            'stock_restored' => $stockToAdd,
                            'stock_before' => $stockBefore,
                            'stock_after' => $stockAfter
                        ]);
                    } else {
                        // 之前是减少数量增加库存，现在要减少库存
                        $stockToReduce = abs($quantityDifference);
                        $result = $product->reduceStockWithPolicy($stockToReduce, $orderItem->unit_id);
                        
                        Log::info('订单更正反审：恢复库存（减少）', [
                            'correction_id' => $correction->id,
                            'product_id' => $product->id,
                            'product_name' => $product->name,
                            'original_quantity_decrease' => abs($quantityDifference),
                            'stock_reduced' => $stockToReduce,
                            'result' => $result,
                            'stock_before' => $result['stock_before'] ?? 'N/A',
                            'stock_after' => $result['stock_after'] ?? 'N/A'
                        ]);
                        
                        if (!$result['success']) {
                            Log::warning('订单更正反审库存减少失败', [
                                'correction_id' => $correction->id,
                                'product_id' => $product->id,
                                'error' => $result['message']
                            ]);
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('订单更正反审库存恢复失败', [
                        'correction_id' => $correction->id,
                        'product_id' => $product->id,
                        'quantity_difference' => $quantityDifference,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    
                    // 不抛出异常，避免影响反审流程，但记录错误
                }
            }
        }
    }

    /**
     * 取消相关的支付记录
     */
    private function cancelRelatedPaymentRecords(OrderCorrection $correction): void
    {
        try {
            Log::info('通知账单模块取消相关支付记录', [
                'correction_id' => $correction->id,
                'order_id' => $correction->order_id,
                'reason' => '订单更正反审'
            ]);
            
            // 🔥 修复：调用账单系统处理反审
            $billingService = app(\App\Billing\Services\BillingService::class);
            $billingService->handleCorrectionReversal($correction, auth()->id() ?? 0);
            
            Log::info('支付记录取消处理完成', [
                'correction_id' => $correction->id
            ]);
            
        } catch (\Exception $e) {
            Log::error('通知账单模块取消支付记录失败', [
                'correction_id' => $correction->id,
                'error' => $e->getMessage()
            ]);
            // 不抛出异常，避免影响反审流程
        }
    }

    /**
     * 处理反审后的账单状态
     */
    private function handleBillAfterReverse(OrderCorrection $correction): void
    {
        // 🔥 修复：账单处理已在 cancelRelatedPaymentRecords 中统一处理
        // 这里不需要重复处理，避免双重处理
        Log::info('账单状态处理已在支付记录处理中完成', [
            'order_id' => $correction->order_id,
            'correction_id' => $correction->id
        ]);
    }

    /**
     * 取消相关的付款链接（已迁移到新的账单系统）
     */
    private function cancelRelatedPaymentLinks(OrderCorrection $correction, string $reason): void
    {
        // 🔥 使用新的账单系统付款链接
        $billingPaymentLinks = BillingPaymentLink::where('correction_id', $correction->id)
            ->where('status', BillingPaymentLink::STATUS_ACTIVE)
            ->get();

        $paymentLinkService = app(PaymentLinkService::class);
        
        foreach ($billingPaymentLinks as $paymentLink) {
            $paymentLinkService->cancelPaymentLink($paymentLink, $reason);
            
            // 记录取消原因
            Log::info('账单系统付款链接已取消', [
                'payment_link_id' => $paymentLink->id,
                'link_no' => $paymentLink->link_no,
                'correction_id' => $correction->id,
                'reason' => $reason,
            ]);
        }

        // 🔥 旧系统付款链接已完全迁移到账单系统，无需兼容性处理
        Log::info('付款链接已完全迁移到账单系统', [
            'correction_id' => $correction->id,
            'billing_payment_links_cancelled' => $billingPaymentLinks->count()
        ]);
    }

    /**
     * 🔥 修复：处理退款（统一调用账单系统）
     */
    public function processRefund(OrderCorrection $correction, int $operatedBy): void
    {
        if (!$correction->needsRefund()) {
            throw new \Exception('该订单更正不需要退款');
        }

        $refundAmount = abs($correction->difference_amount);
        
        Log::info('管理页面手动处理订单更正退款', [
            'correction_id' => $correction->id,
            'order_id' => $correction->order_id,
            'refund_amount' => $refundAmount,
            'operated_by' => $operatedBy
        ]);
        
        try {
            // 调用账单系统处理支付差异（负数表示退款）
            $billingService = app(\App\Billing\Services\BillingService::class);
            $billingService->processOrderCorrectionPaymentDifference($correction, -$refundAmount, $operatedBy);
            
            Log::info('管理页面退款处理成功', [
                'correction_id' => $correction->id,
                'refund_amount' => $refundAmount
            ]);
            
        } catch (\Exception $e) {
            Log::error('管理页面退款处理失败', [
                'correction_id' => $correction->id,
                'refund_amount' => $refundAmount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception("退款处理失败：{$e->getMessage()}");
        }
    }

    /**
     * 🔥 修复：处理补款（统一调用账单系统）
     */
    public function processSupplement(OrderCorrection $correction, int $operatedBy): void
    {
        if (!$correction->needsSupplement()) {
            throw new \Exception('该订单更正不需要补款');
        }

        $supplementAmount = $correction->difference_amount;
        
        Log::info('管理页面手动处理订单更正补款', [
            'correction_id' => $correction->id,
            'order_id' => $correction->order_id,
            'supplement_amount' => $supplementAmount,
            'operated_by' => $operatedBy
        ]);
        
        try {
            // 调用账单系统处理支付差异（正数表示补款）
            $billingService = app(\App\Billing\Services\BillingService::class);
            $billingService->processOrderCorrectionPaymentDifference($correction, $supplementAmount, $operatedBy);
            
            Log::info('管理页面补款处理成功', [
                'correction_id' => $correction->id,
                'supplement_amount' => $supplementAmount
            ]);
            
        } catch (\Exception $e) {
            Log::error('管理页面补款处理失败', [
                'correction_id' => $correction->id,
                'supplement_amount' => $supplementAmount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception("补款处理失败：{$e->getMessage()}");
        }
    }

    /**
     * 选择货到付款结算方式
     */
    public function chooseCodSettlementMethod(OrderCorrection $correction, string $paymentMethod, int $operatedBy, array $options = []): void
    {
        Log::info('选择货到付款结算方式', [
            'correction_id' => $correction->id,
            'payment_method' => $paymentMethod,
            'operated_by' => $operatedBy,
            'options' => $options
        ]);
        
        // 根据选择的支付方式处理
        // 这里可以根据具体业务需求实现
    }

    /**
     * 更新订单更正
     */
    public function updateCorrection(OrderCorrection $correction, array $correctionData, array $items): OrderCorrection
    {
        return DB::transaction(function () use ($correction, $correctionData, $items) {
            // 更新更正基本信息
            if (!empty($correctionData)) {
                $correction->update($correctionData);
            }

            // 更新更正项目
            if (!empty($items)) {
                // 删除旧的更正项目
                $correction->items()->delete();
                
                // 重新计算更正后的总金额
                $correctedTotal = 0;
                foreach ($items as $item) {
                    $correctedTotal += $item['corrected_total'];
                    
                    // 创建新的更正项目
                    $correction->items()->create([
                        'order_item_id' => $item['order_item_id'],
                        'original_quantity' => $item['original_quantity'],
                        'original_price' => $item['original_price'],
                        'original_total' => $item['original_total'],
                        'corrected_quantity' => $item['corrected_quantity'],
                        'corrected_weight' => $item['corrected_weight'] ?? null,
                        'corrected_price' => $item['corrected_price'],
                        'corrected_total' => $item['corrected_total'],
                        'difference_amount' => $item['difference_amount'] ?? ($item['corrected_total'] - $item['original_total']),
                        'correction_reason' => $item['reason'] ?? null,
                    ]);
                }

                // 更新更正记录的总金额和差异
                $differenceAmount = $correctedTotal - $correction->original_total;
                $correction->update([
                    'corrected_total' => $correctedTotal,
                    'difference_amount' => $differenceAmount,
                    'correction_type' => $this->determineCorrectionType($differenceAmount),
                ]);
            }

            $this->logOperation('update_correction', $correction, auth()->id() ?? 0);

            return $correction;
        });
    }

    /**
     * 🔥 新增：处理微信退款回调
     */
    public function handleWechatRefundCallback(string $outRefundNo, array $refundInfo): void
    {
        try {
            Log::info('处理微信退款回调', [
                'out_refund_no' => $outRefundNo,
                'refund_status' => $refundInfo['refund_status'] ?? null,
                'refund_id' => $refundInfo['refund_id'] ?? null
            ]);

            // 根据退款单号查找相关的支付记录
            $paymentRecord = \App\Order\Models\PaymentRecord::where('out_trade_no', $outRefundNo)
                ->where('payment_type', 'refund')
                ->first();

            if (!$paymentRecord) {
                Log::warning('未找到对应的支付记录', ['out_refund_no' => $outRefundNo]);
                return;
            }

            // 更新支付记录状态
            $status = $refundInfo['refund_status'] ?? 'UNKNOWN';
            switch ($status) {
                case 'SUCCESS':
                    $paymentRecord->update([
                        'status' => 'success',
                        'transaction_id' => $refundInfo['refund_id'] ?? null,
                        'completed_at' => now(),
                        'extra_data' => array_merge($paymentRecord->extra_data ?? [], [
                            'refund_success_time' => $refundInfo['success_time'] ?? now()->toISOString(),
                            'wechat_refund_id' => $refundInfo['refund_id'] ?? null,
                            'refund_callback_data' => $refundInfo
                        ])
                    ]);
                    
                    Log::info('微信退款成功', [
                        'payment_record_id' => $paymentRecord->id,
                        'correction_id' => $paymentRecord->correction_id,
                        'refund_amount' => abs($paymentRecord->amount)
                    ]);
                    break;
                    
                case 'REFUNDCLOSE':
                case 'CHANGE':
                    $paymentRecord->update([
                        'status' => 'failed',
                        'notes' => "微信退款失败：{$status}",
                        'extra_data' => array_merge($paymentRecord->extra_data ?? [], [
                            'refund_failed_time' => now()->toISOString(),
                            'failure_reason' => $status,
                            'refund_callback_data' => $refundInfo
                        ])
                    ]);
                    
                    Log::error('微信退款失败', [
                        'payment_record_id' => $paymentRecord->id,
                        'correction_id' => $paymentRecord->correction_id,
                        'reason' => $status
                    ]);
                    break;
                    
                default:
                    Log::info('微信退款状态更新', [
                        'payment_record_id' => $paymentRecord->id,
                        'status' => $status
                    ]);
            }

        } catch (\Exception $e) {
            Log::error('处理微信退款回调失败', [
                'out_refund_no' => $outRefundNo,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
} 