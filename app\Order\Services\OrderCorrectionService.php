<?php

namespace App\Order\Services;

use App\Order\Models\Order;
use App\Order\Models\OrderCorrection;
use App\Order\Models\PaymentRecord;
use App\Order\Services\PaymentLinkService;
use App\Payment\Services\PaymentOfferService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class OrderCorrectionService
{
    private $paymentOfferService;

    public function __construct(PaymentOfferService $paymentOfferService = null)
    {
        $this->paymentOfferService = $paymentOfferService;
    }

    /**
     * 创建订单更正
     */
    public function createCorrection(Order $order, array $correctionData, array $items, int $operatedBy): OrderCorrection
    {
        return DB::transaction(function () use ($order, $correctionData, $items, $operatedBy) {
            // 计算更正后的总金额
            $correctedTotal = 0;
            foreach ($items as $item) {
                $correctedTotal += $item['corrected_total'];
            }
            
            // 计算差异金额
            $differenceAmount = $correctedTotal - $order->total;
            
            // 确定更正类型
            $correctionType = $this->determineCorrectionType($differenceAmount);

            // 创建更正记录
            $correction = OrderCorrection::create([
                'order_id' => $order->id,
                'correction_no' => $this->generateCorrectionNo(),
                'correction_type' => $correctionType,
                'original_total' => $order->total,
                'corrected_total' => $correctedTotal,
                'difference_amount' => $differenceAmount,
                'reason' => $correctionData['correction_reason'] ?? null,
                'status' => 'pending',
                'created_by' => $operatedBy,
            ]);

            // 创建更正项目
            foreach ($items as $item) {
                $correction->items()->create([
                    'order_item_id' => $item['order_item_id'],
                    'original_quantity' => $item['original_quantity'],
                    'original_price' => $item['original_price'],
                    'original_total' => $item['original_total'],
                    'corrected_quantity' => $item['corrected_quantity'],
                    'corrected_weight' => $item['corrected_weight'] ?? null,
                    'corrected_price' => $item['corrected_price'],
                    'corrected_total' => $item['corrected_total'],
                    'reason' => $item['reason'] ?? null,
                ]);
            }

            $this->logOperation('create_correction', $correction, $operatedBy);

            return $correction;
        });
    }

    /**
     * 确认订单更正
     */
    public function confirmCorrection(OrderCorrection $correction, int $operatedBy): void
    {
        DB::transaction(function () use ($correction, $operatedBy) {
            // 更新更正状态
            $correction->update([
                'status' => 'confirmed',
                'confirmed_at' => now(),
                'confirmed_by' => $operatedBy,
            ]);

            // 更新订单项数据
            $this->updateOrderItems($correction);

            // 更新订单总金额
            $correction->order->update([
                'total' => $correction->corrected_total,
            ]);

            // 处理金额差异
            $this->handleAmountDifference($correction, $operatedBy);

            $this->logOperation('confirm_correction', $correction, $operatedBy);
        });
    }

    /**
     * 处理金额差异
     */
    private function handleAmountDifference(OrderCorrection $correction, int $operatedBy): void
    {
        $differenceAmount = $correction->difference_amount;
        $correctionType = $this->determineCorrectionType($differenceAmount);

        if ($correctionType === 'no_change') {
            $this->completeCorrection($correction, $operatedBy);
            return;
        }

        // 根据原订单支付方式处理
        $paymentMethod = $correction->order->payment_method;

        if ($paymentMethod === 'wechat') {
            $this->processWechatAmountDifference($correction, $operatedBy, $correctionType);
        } elseif ($paymentMethod === 'cod') {
            $this->processCodAmountDifference($correction, $operatedBy, $correctionType);
        }
    }

    /**
     * 处理微信支付的金额差异
     */
    private function processWechatAmountDifference(OrderCorrection $correction, int $operatedBy, string $type): void
    {
        $amount = abs($correction->difference_amount);

        if ($type === 'decrease') {
            // 需要退款
            $this->handleWechatRefund($correction, $amount, $operatedBy);
        } else {
            // 需要补款
            $this->processWechatSupplement($correction, $amount, $operatedBy);
        }
    }

    /**
     * 处理货到付款的金额差异
     */
    private function processCodAmountDifference(OrderCorrection $correction, int $operatedBy, string $type): void
    {
        // 货到付款需要人工处理结算
        $this->createPendingSettlementRecord($correction, $operatedBy);

        $correction->order->update([
            'settlement_status' => 'pending_settlement',
        ]);

        $this->logOperation('cod_pending_settlement', $correction, $operatedBy);
    }

    /**
     * 创建待结算记录
     */
    private function createPendingSettlementRecord(OrderCorrection $correction, int $operatedBy): void
    {
        PaymentRecord::create([
            'order_id' => $correction->order_id,
            'correction_id' => $correction->id,
            'payment_type' => $correction->correction_type === 'decrease' ? 'refund' : 'supplement',
            'business_type' => 'cod_settlement',
            'amount' => $correction->difference_amount,
            'payment_method' => 'cod',
            'status' => 'pending_settlement',
            'operated_by' => $operatedBy,
            'notes' => 'COD settlement pending for order correction',
        ]);
    }

    /**
     * 生成更正单号
     */
    private function generateCorrectionNo(): string
    {
        return 'COR' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 选择货到付款结算方式
     */
    public function chooseCodSettlementMethod(OrderCorrection $correction, string $paymentMethod, int $operatedBy, array $options = []): void
    {
        if ($paymentMethod === 'cash') {
            $this->processCodCashSettlement($correction, $operatedBy, $options);
        } elseif ($paymentMethod === 'online') {
            $this->processCodOnlineSettlement($correction, $operatedBy, $options);
        } else {
            throw new \InvalidArgumentException('Unsupported payment method: ' . $paymentMethod);
        }
    }

    /**
     * 处理货到付款现金结算
     */
    private function processCodCashSettlement(OrderCorrection $correction, int $operatedBy, array $options): void
    {
        $amount = abs($correction->difference_amount);
        
        // 更新支付记录状态
        $paymentRecord = PaymentRecord::where('correction_id', $correction->id)
            ->where('status', 'pending_settlement')
            ->first();
            
        if ($paymentRecord) {
            $paymentRecord->update([
                'payment_method' => 'cash',
                'business_type' => $correction->correction_type === 'decrease' ? 'cod_difference_refund' : 'cod_difference_supplement',
                'status' => 'success',
                'settlement_at' => now(),
                'notes' => 'COD Cash Settlement: ¥' . $amount . '. Notes: ' . ($options['notes'] ?? ''),
                'operated_by' => $operatedBy,
            ]);
        }

        // 取消相关的付款链接（如果存在）
        $this->cancelRelatedPaymentLinks($correction, 'Cash settlement completed, payment link invalidated');

        // 更新订单状态
        $correction->order->update([
            'settlement_status' => 'completed',
            'settlement_method' => 'cash',
            'settlement_completed_at' => now(),
            'settlement_operator_id' => $operatedBy,
        ]);

        $this->logOperation('cod_cash_settlement', $correction, $operatedBy, $options);
    }

    /**
     * 处理货到付款线上结算
     */
    private function processCodOnlineSettlement(OrderCorrection $correction, int $operatedBy, array $options): void
    {
        // 货到付款线上支付：用户需要支付更正后的订单总金额
        $totalAmount = $correction->corrected_total;
        
        // 生成付款链接（不管增加、减少还是无变化，都是支付最终总金额）
        $paymentLinkService = app(PaymentLinkService::class);
        $paymentLink = $paymentLinkService->generateCodPaymentLink($correction->order);

        // 更新支付记录状态
        $paymentRecord = PaymentRecord::where('correction_id', $correction->id)
            ->where('status', 'pending_settlement')
            ->first();
            
        if ($paymentRecord) {
            $paymentRecord->update([
                'payment_method' => 'wechat',
                'business_type' => 'cod_total_payment',
                'amount' => $totalAmount,
                'status' => 'pending',
                'notes' => 'COD Online Payment: ¥' . $totalAmount . ' (corrected total amount), payment link generated. Notes: ' . ($options['notes'] ?? ''),
                'operated_by' => $operatedBy,
            ]);
        }

        // 更新订单状态
        $correction->order->update([
            'settlement_status' => 'pending_settlement',
            'settlement_method' => 'wechat',
            'payment_link_id' => $paymentLink->id,
            'payment_link_expires_at' => $paymentLink->expires_at,
        ]);

        $this->logOperation('cod_online_payment', $correction, $operatedBy, array_merge($options, [
            'total_amount' => $totalAmount,
            'payment_link_id' => $paymentLink->id,
        ]));
    }

    /**
     * 完成更正（无需额外处理）
     */
    private function completeCorrection(OrderCorrection $correction, int $operatedBy): void
    {
        $correction->order->update([
            'settlement_status' => 'completed',
            'settlement_completed_at' => now(),
            'settlement_operator_id' => $operatedBy,
        ]);

        $this->logOperation('complete_correction', $correction, $operatedBy);
    }

    /**
     * 处理微信退款
     */
    private function handleWechatRefund(OrderCorrection $correction, float $refundAmount, int $operatedBy): void
    {
        // 创建退款记录
        $paymentRecord = PaymentRecord::create([
            'order_id' => $correction->order_id,
            'correction_id' => $correction->id,
            'payment_type' => 'refund',
            'business_type' => 'correction_refund',
            'amount' => -$refundAmount, // 负数表示退款
            'payment_method' => 'wechat',
            'status' => 'pending',
            'operated_by' => $operatedBy,
            'notes' => 'Order Correction WeChat Refund: ¥' . $refundAmount,
        ]);

        // 调用微信退款API
        $wechatService = app(\App\WechatPayment\Services\WechatServiceProviderPayment::class);
        
        try {
            $refundResult = $wechatService->refund([
                'out_refund_no' => 'REF' . $correction->correction_no,
                'total_fee' => $correction->order->total * 100, // 原订单金额（分）
                'refund_fee' => $refundAmount * 100, // 退款金额（分）
                'refund_desc' => 'Order correction refund',
            ]);

            // 创建微信退款记录
            $wechatRefund = \App\WechatPayment\Models\WechatServiceRefund::create([
                'out_refund_no' => 'REF' . $correction->correction_no,
                'refund_id' => $refundResult['refund_id'] ?? null,
                'total_fee' => $correction->order->total * 100,
                'refund_fee' => $refundAmount * 100,
                'refund_status' => 'PROCESSING',
                'refund_desc' => 'Order correction refund',
                'notify_data' => $refundResult,
            ]);

            // 关联微信退款记录
            $paymentRecord->update([
                'wechat_refund_id' => $wechatRefund->id,
                'status' => 'processing',
                'out_trade_no' => 'REF' . $correction->correction_no,
            ]);

            $this->logOperation('wechat_refund_initiated', $correction, $operatedBy, [
                'refund_amount' => $refundAmount,
                'out_refund_no' => 'REF' . $correction->correction_no,
            ]);

        } catch (\Exception $e) {
            // 退款失败
            $paymentRecord->update([
                'status' => 'failed',
                'notes' => $paymentRecord->notes . ' [Refund failed: ' . $e->getMessage() . ']',
            ]);

            Log::error('WeChat refund failed', [
                'correction_id' => $correction->id,
                'refund_amount' => $refundAmount,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * 处理微信补款
     */
    private function processWechatSupplement(OrderCorrection $correction, float $supplementAmount, int $operatedBy): void
    {
        // 创建补款记录
        $paymentRecord = PaymentRecord::create([
            'order_id' => $correction->order_id,
            'correction_id' => $correction->id,
            'payment_type' => 'supplement',
            'business_type' => 'correction_supplement',
            'amount' => $supplementAmount,
            'payment_method' => 'wechat',
            'status' => 'pending',
            'operated_by' => $operatedBy,
            'notes' => 'Order Correction WeChat Supplement: ¥' . $supplementAmount,
        ]);

        // 生成微信付款链接
        $paymentLinkService = app(PaymentLinkService::class);
        $paymentLink = $paymentLinkService->generateSupplementLink(
            $correction->order_id,
            $supplementAmount,
            $correction->id,
            $operatedBy
        );

        // 更新支付记录
        $paymentRecord->update([
            'out_trade_no' => $paymentLink->link_no,
        ]);

        // 更新订单状态
        $correction->order->update([
            'payment_link_id' => $paymentLink->id,
            'payment_link_expires_at' => $paymentLink->expires_at,
        ]);

        $this->logOperation('wechat_supplement_initiated', $correction, $operatedBy, [
            'supplement_amount' => $supplementAmount,
            'payment_link_id' => $paymentLink->id,
        ]);
    }

    /**
     * 更新订单项数据
     */
    private function updateOrderItems(OrderCorrection $correction): void
    {
        foreach ($correction->items as $correctionItem) {
            $orderItem = $correctionItem->orderItem;
            $orderItem->update([
                'quantity' => $correctionItem->corrected_quantity,
                'weight' => $correctionItem->corrected_weight,
                'price' => $correctionItem->corrected_price,
                'total' => $correctionItem->corrected_total,
            ]);
        }
    }

    /**
     * 确定更正类型
     */
    private function determineCorrectionType(float $differenceAmount): string
    {
        if (abs($differenceAmount) < 0.01) {
            return 'no_change';
        } elseif ($differenceAmount > 0) {
            return 'increase';
        } else {
            return 'decrease';
        }
    }

    /**
     * 取消相关的付款链接
     */
    private function cancelRelatedPaymentLinks(OrderCorrection $correction, string $reason): void
    {
        $paymentLinks = \App\Order\Models\PaymentLink::where('correction_id', $correction->id)
            ->where('status', 'active')
            ->get();

        foreach ($paymentLinks as $paymentLink) {
            $paymentLinkService = app(PaymentLinkService::class);
            $paymentLinkService->cancelPaymentLink($paymentLink);
            
            // 记录取消原因
            Log::info('Payment link cancelled', [
                'payment_link_id' => $paymentLink->id,
                'correction_id' => $correction->id,
                'reason' => $reason,
            ]);
        }
    }

    /**
     * 记录操作日志
     */
    private function logOperation(string $action, OrderCorrection $correction, int $operatedBy, array $data = []): void
    {
        Log::info('Order correction operation', [
            'action' => $action,
            'correction_id' => $correction->id,
            'correction_no' => $correction->correction_no,
            'order_id' => $correction->order_id,
            'order_no' => $correction->order->order_no ?? null,
            'operated_by' => $operatedBy,
            'timestamp' => now()->toDateTimeString(),
            'data' => $data
        ]);
    }

    /**
     * 取消订单更正
     */
    public function cancelCorrection(OrderCorrection $correction): void
    {
        DB::transaction(function () use ($correction) {
            $correction->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
            ]);

            // 取消相关的付款链接
            $this->cancelRelatedPaymentLinks($correction, 'Order correction cancelled');

            $this->logOperation('cancel_correction', $correction, auth()->id() ?? 0);
        });
    }

    /**
     * 处理退款
     */
    public function processRefund(OrderCorrection $correction, int $operatedBy): void
    {
        $refundAmount = abs($correction->difference_amount);
        
        if ($correction->order->payment_method === 'wechat') {
            $this->handleWechatRefund($correction, $refundAmount, $operatedBy);
        } elseif ($correction->order->payment_method === 'cod') {
            // 货到付款的退款处理
            $this->createPendingSettlementRecord($correction, $operatedBy);
        }
    }

    /**
     * 处理补款
     */
    public function processSupplement(OrderCorrection $correction, int $operatedBy): void
    {
        $supplementAmount = $correction->difference_amount;
        
        if ($correction->order->payment_method === 'wechat') {
            $this->processWechatSupplement($correction, $supplementAmount, $operatedBy);
        } elseif ($correction->order->payment_method === 'cod') {
            // 货到付款的补款处理
            $this->createPendingSettlementRecord($correction, $operatedBy);
        }
    }

    /**
     * 更新订单更正
     */
    public function updateCorrection(OrderCorrection $correction, array $correctionData, array $items): OrderCorrection
    {
        return DB::transaction(function () use ($correction, $correctionData, $items) {
            // 更新更正记录
            if (!empty($correctionData)) {
                $correction->update($correctionData);
            }

            // 更新更正项目
            if (!empty($items)) {
                // 删除现有项目
                $correction->items()->delete();
                
                // 创建新的项目
                foreach ($items as $item) {
                    $correction->items()->create($item);
                }
            }

            $this->logOperation('update_correction', $correction, auth()->id() ?? 0);

            return $correction;
        });
    }

    /**
     * 查询微信退款状态
     */
    public function queryWechatRefundStatus(OrderCorrection $correction): array
    {
        $refundRecords = PaymentRecord::where('correction_id', $correction->id)
            ->where('payment_type', 'refund')
            ->where('payment_method', 'wechat')
            ->with('wechatRefund')
            ->get();

        $results = [];
        foreach ($refundRecords as $record) {
            if ($record->wechatRefund) {
                $wechatService = app(\App\WechatPayment\Services\WechatServiceProviderPayment::class);
                
                try {
                    $status = $wechatService->queryRefund($record->out_trade_no);
                    
                    $results[] = [
                        'payment_record_id' => $record->id,
                        'out_refund_no' => $record->out_trade_no,
                        'refund_amount' => abs($record->amount),
                        'status' => $record->status,
                        'wechat_status' => $status,
                        'created_at' => $record->created_at,
                    ];
                } catch (\Exception $e) {
                    $results[] = [
                        'payment_record_id' => $record->id,
                        'out_refund_no' => $record->out_trade_no,
                        'refund_amount' => abs($record->amount),
                        'status' => $record->status,
                        'error' => $e->getMessage(),
                        'created_at' => $record->created_at,
                    ];
                }
            }
        }

        return $results;
    }

    /**
     * 手动触发微信退款
     */
    public function processWechatRefund(OrderCorrection $correction, float $refundAmount, int $operatedBy): void
    {
        $this->handleWechatRefund($correction, $refundAmount, $operatedBy);
    }

    /**
     * 处理微信补款回调
     */
    public function handleWechatSupplementCallback(string $outTradeNo, array $notifyData): void
    {
        try {
            // 查找对应的支付记录
            $paymentRecord = PaymentRecord::where('out_trade_no', $outTradeNo)
                ->where('payment_type', 'supplement')
                ->where('payment_method', 'wechat')
                ->first();

            if (!$paymentRecord) {
                Log::warning('未找到对应的补款记录', ['out_trade_no' => $outTradeNo]);
                return;
            }

            // 检查支付状态
            if ($notifyData['trade_state'] === 'SUCCESS') {
                // 更新支付记录状态
                $paymentRecord->update([
                    'status' => 'success',
                    'paid_at' => now(),
                    'transaction_id' => $notifyData['transaction_id'] ?? null,
                    'notes' => ($paymentRecord->notes ?? '') . ' [微信支付成功]'
                ]);

                // 更新订单更正状态
                if ($paymentRecord->correction_id) {
                    $correction = OrderCorrection::find($paymentRecord->correction_id);
                    if ($correction) {
                        // 取消相关的付款链接
                        $this->cancelRelatedPaymentLinks($correction, 'Payment completed successfully');

                        // 完成更正
                        $this->completeCorrection($correction, $paymentRecord->operated_by);

                        Log::info('订单更正补款完成', [
                            'correction_id' => $correction->id,
                            'payment_record_id' => $paymentRecord->id,
                            'amount' => $paymentRecord->amount,
                            'transaction_id' => $notifyData['transaction_id']
                        ]);
                    }
                }
            } else {
                // 支付失败
                $paymentRecord->update([
                    'status' => 'failed',
                    'notes' => ($paymentRecord->notes ?? '') . ' [微信支付失败: ' . ($notifyData['trade_state'] ?? 'UNKNOWN') . ']'
                ]);

                Log::warning('订单更正补款失败', [
                    'payment_record_id' => $paymentRecord->id,
                    'out_trade_no' => $outTradeNo,
                    'trade_state' => $notifyData['trade_state'] ?? 'UNKNOWN'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('处理微信补款回调失败', [
                'out_trade_no' => $outTradeNo,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
} 