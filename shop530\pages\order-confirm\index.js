// pages/order-confirm/index.js - 提交订单页面
const api = require('../../utils/api');

Page({
  data: {
    // 收货地址信息
    address: {
      id: null,
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detail: '',
      isDefault: false
    },
    
    // 配送信息
    delivery: {
      type: 'platform',
      typeName: '明日达',
      time: '',
      date: '',
      fee: 0
    },
    
    // 支付方式信息
    payment: {
      method: 'online', // online在线支付 / cod货到付款
      methods: [] // 从后端API获取
    },
    
    // 商品列表
    products: [],
    
    // 价格汇总
    summary: {
      productAmount: 0,    // 商品金额
      discountAmount: 0,   // 优惠金额
      deliveryFee: 0,      // 配送费
      paymentDiscount: 0,  // 支付优惠
      totalAmount: 0       // 总金额
    },
    
    // 优惠券信息
    coupon: {
      available: [],
      selected: null,
      hasAvailable: false
    },
    
    // 页面状态
    submitting: false,
    loading: true,
    
    // 系统状态栏高度
    statusBarHeight: 44
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    console.log('📦 订单确认页面加载，参数:', options);
    
    // 初始化统一加载管理器
    const { loadingMixin } = require('../../utils/loading-manager');
    Object.assign(this, loadingMixin);
    
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 44;
    
    this.setData({
      statusBarHeight: statusBarHeight
    });
    
    // 计算配送时间
    this.calculateDeliveryTime();
    
    // 初始化页面数据
    this.initPageData(options);
  },

  /**
   * 初始化页面数据（修复版）
   */
  async initPageData(options) {
    try {
      console.log('🚀 开始初始化订单页面数据...');
      
      // 显示加载状态
      this.setData({ loading: true });
      
      // 并行获取数据
      const [products, address, coupons, paymentMethods, userInfo] = await Promise.all([
        this.getProducts(options),
        this.getDefaultAddress(),
        this.getAvailableCoupons(),
        this.getPaymentMethods(),
        this.getUserInfo()
      ]);
      
      console.log('📦 获取到的数据:', { 
        products: products.length, 
        address: address?.name || '无地址',
        coupons: coupons.length,
        paymentMethods: paymentMethods.length,
        userRegion: userInfo?.region_id || '无区域'
      });
      
      // 验证商品数据
      if (!products || products.length === 0) {
        throw new Error('没有找到商品信息');
      }
      
      // 计算价格
      const summary = this.calculateSummary(products);
      
      // 设置页面数据
      this.setData({
        products,
        address,
        'coupon.available': coupons,
        'coupon.hasAvailable': coupons.length > 0,
        'payment.methods': paymentMethods,
        userInfo, // 保存用户信息
        summary,
        loading: false
      });
      
      console.log('✅ 订单页面数据初始化完成');
      
      // 🔍 验证商品ID映射（开发环境）
      if (typeof __wxConfig !== 'undefined' && __wxConfig.envVersion !== 'release') {
        setTimeout(() => {
          this.validateProductMapping();
        }, 1000);
      }
      
    } catch (error) {
      console.error('❌ 初始化订单页面失败:', error);
      
      this.setData({ loading: false });
      
      wx.showModal({
        title: '提示',
        content: error.message || '页面加载失败，请重试',
        showCancel: true,
        cancelText: '返回',
        confirmText: '重试',
        success: (res) => {
          if (res.confirm) {
            this.initPageData(options);
          } else {
            wx.navigateBack();
          }
        }
      });
    }
  },

  /**
   * 获取商品信息（修复版）
   */
  async getProducts(options) {
    try {
      console.log('📦 获取商品信息，参数:', options);
      
      // 如果是从购物车来的
      if (options.from === 'cart') {
        const cartProducts = wx.getStorageSync('selectedCartProducts') || [];
        console.log('🛒 从购物车获取商品:', cartProducts.length);
        
        if (cartProducts.length === 0) {
          throw new Error('购物车中没有选中的商品');
        }
        
        // 🔍 详细调试购物车商品数据
        console.log('🔍 购物车原始数据详细调试:');
        cartProducts.forEach((product, index) => {
          console.log(`商品 ${index + 1}:`, {
            id: product.id,
            product_id: product.product_id,
            name: product.name,
            price: product.price,
            quantity: product.quantity,
            原始数据: product
          });
        });
        
        // 确保商品数据格式正确
        const formattedProducts = cartProducts.map((product, index) => {
          // 🚨 关键修复：确保使用正确的商品ID
          const productId = product.product_id || product.id;
          
          console.log(`🔧 格式化商品 ${index + 1}:`, {
            原始ID: product.id,
            商品ID: product.product_id,
            最终使用ID: productId,
            商品名称: product.name
          });
          
          return {
            id: productId, // 使用正确的商品ID
            cart_item_id: product.id, // 保存购物车项ID，用于删除
            name: product.name || '商品名称',
            image: product.image || product.cover_url || '',
            price: parseFloat(product.price) || 0,
            originalPrice: parseFloat(product.original_price || product.price) || 0,
            unit: product.unit || '',
            quantity: parseInt(product.quantity) || 1,
            totalPrice: parseFloat(product.price) * parseInt(product.quantity) || 0,
            // 保留原始数据用于调试
            _debug: {
              原始数据: product,
              购物车项ID: product.id,
              商品ID: productId
            },
            ...product
          };
        });
        
        // 调试：记录商品名称信息
        console.log('🔍 购物车商品最终格式化结果:');
        formattedProducts.forEach((p, index) => {
          console.log(`最终商品 ${index + 1}:`, {
            id: p.id,
            name: p.name,
            price: p.price,
            quantity: p.quantity,
            调试信息: p._debug
          });
        });
        
        return formattedProducts;
      }
      
      // 如果是直接购买
      if (options.productId && options.quantity) {
        console.log('🛍️ 直接购买商品:', options.productId, '数量:', options.quantity);
        
        const result = await api.getProductDetail(options.productId);
        
        if (result && result.data) {
          const product = result.data;
          const quantity = parseInt(options.quantity);
          
          const formattedProduct = {
            id: product.id,
            name: product.name || '商品名称',
            image: product.image || '',
            price: parseFloat(product.price) || 0,
            originalPrice: parseFloat(product.originalPrice || product.price) || 0,
            unit: product.unit || '',
            quantity: quantity,
            totalPrice: parseFloat(product.price) * quantity || 0,
            ...product
          };
          
          // 调试：记录商品名称信息
          console.log('🔍 直接购买商品名称调试:', {
            id: formattedProduct.id,
            name: formattedProduct.name,
            api_name: product.name,
            source: '商品详情API'
          });
          
          return [formattedProduct];
        } else {
          throw new Error('获取商品详情失败');
        }
      }
      
      // 如果没有参数，检查是否有临时存储的商品信息
      const tempProducts = wx.getStorageSync('tempOrderProducts');
      if (tempProducts && tempProducts.length > 0) {
        console.log('📦 使用临时存储的商品信息');
        wx.removeStorageSync('tempOrderProducts'); // 使用后清除
        return tempProducts;
      }
      
      throw new Error('没有找到商品信息，请重新选择商品');
      
    } catch (error) {
      console.error('❌ 获取商品信息失败:', error);
      throw error;
    }
  },

  /**
   * 获取用户信息
   */
  async getUserInfo() {
    try {
      console.log('👤 获取用户信息...');
      const result = await api.getUserInfo();
      
      console.log('👤 用户信息API返回:', result);
      
      // 处理不同的返回格式
      let userInfo = null;
      
      if (result && result.data) {
        userInfo = result.data;
      } else if (result && result.id) {
        userInfo = result;
      }
      
      if (userInfo) {
        console.log('✅ 获取用户信息成功:', {
          id: userInfo.id,
          name: userInfo.name,
          region_id: userInfo.region_id,
          membership_level_id: userInfo.membership_level_id
        });
        return userInfo;
      }
      
      console.log('⚠️ 没有获取到用户信息');
      return null;
      
    } catch (error) {
      console.error('❌ 获取用户信息失败:', error);
      return null;
    }
  },

  /**
   * 获取默认收货地址（修复版）
   */
  async getDefaultAddress() {
    try {
      console.log('📡 获取默认收货地址...');
      const result = await api.getAddressList();
      
      console.log('📡 地址API返回结果:', result);
      
      // 处理不同的返回格式
      let addresses = [];
      
      if (result && result.data && Array.isArray(result.data)) {
        addresses = result.data;
      } else if (result && Array.isArray(result)) {
        addresses = result;
      } else if (result && result.list && Array.isArray(result.list)) {
        addresses = result.list;
      }
      
      console.log('📡 解析到的地址列表:', addresses.length, '个地址');
      
      if (addresses.length > 0) {
        // 查找默认地址，支持不同的字段名
        const defaultAddress = addresses.find(addr => 
          addr.is_default === 1 || 
          addr.is_default === true || 
          addr.isDefault === true ||
          addr.default === true
        );
        
        const selectedAddress = defaultAddress || addresses[0];
        
        // 处理字段映射，确保前端兼容性
        const formattedAddress = {
          id: selectedAddress.id,
          name: selectedAddress.contact_name || selectedAddress.name || selectedAddress.receiver_name || '',
          phone: selectedAddress.contact_phone || selectedAddress.phone || selectedAddress.receiver_phone || '',
          province: selectedAddress.province || selectedAddress.province_name || '',
          city: selectedAddress.city || selectedAddress.city_name || '',
          district: selectedAddress.district || selectedAddress.district_name || selectedAddress.area || '',
          detail: selectedAddress.address || selectedAddress.detail || selectedAddress.detailed_address || '',
          isDefault: Boolean(selectedAddress.is_default || selectedAddress.isDefault || selectedAddress.default),
          ...selectedAddress
        };
        
        console.log('✅ 找到地址:', formattedAddress.name, formattedAddress.phone);
        return formattedAddress;
      }
      
      console.log('⚠️ 没有找到收货地址');
      // 如果没有地址，返回空地址
      return {
        id: null,
        name: '',
        phone: '',
        province: '',
        city: '',
        district: '',
        detail: '',
        isDefault: false
      };
      
    } catch (error) {
      console.error('❌ 获取收货地址失败:', error);
      
      // 返回空地址，让用户手动添加
      return {
        id: null,
        name: '',
        phone: '',
        province: '',
        city: '',
        district: '',
        detail: '',
        isDefault: false
      };
    }
  },

  /**
   * 获取可用优惠券（修复版）
   */
  async getAvailableCoupons() {
    try {
      console.log('📡 获取可用优惠券...');
      
      // 尝试调用优惠券API
      if (api.getAvailableCoupons) {
        const result = await api.getAvailableCoupons();
        
        if (result && result.data && Array.isArray(result.data)) {
          console.log('✅ 获取到优惠券:', result.data.length, '张');
          return result.data;
        } else if (result && Array.isArray(result)) {
          console.log('✅ 获取到优惠券:', result.length, '张');
          return result;
        }
      }
      
      console.log('⚠️ 优惠券API暂未实现或无可用优惠券');
      return [];
      
    } catch (error) {
      console.error('❌ 获取优惠券失败:', error);
      return [];
    }
  },

  /**
   * 获取支付方式配置（从后端API获取）
   */
  async getPaymentMethods() {
    try {
      console.log('📡 获取支付方式配置...');
      
      const paymentMethods = await api.getPaymentMethods();
      
      if (paymentMethods && paymentMethods.length > 0) {
        console.log('✅ 获取支付方式配置成功:', paymentMethods.length, '种支付方式');
        return paymentMethods;
      }
      
      console.log('⚠️ 后端未返回支付方式配置，使用默认配置');
      return this.getDefaultPaymentMethods();
      
    } catch (error) {
      console.error('❌ 获取支付方式配置失败:', error);
      console.log('⚠️ 使用默认支付方式配置');
      return this.getDefaultPaymentMethods();
    }
  },

  /**
   * 获取默认支付方式配置（兜底方案）
   */
  getDefaultPaymentMethods() {
    return [
      {
        value: 'wechat', // 修复：使用数据库支持的支付方式值
        name: '在线支付',
        icon: 'gold-coin-o',
        discount: 5,
        discount_type: 'fixed',
        description: '立减5元，更优惠',
        enabled: true,
        sort_order: 1
      },
      {
        value: 'cod',
        name: '货到付款',
        icon: 'cash-o',
        discount: 0,
        discount_type: 'fixed',
        description: '送货上门再付款',
        enabled: true,
        sort_order: 2
      }
    ];
  },

  /**
   * 计算价格汇总
   */
  calculateSummary(products) {
    const productAmount = products.reduce((sum, product) => {
      return sum + (product.price * product.quantity);
    }, 0);
    
    const deliveryFee = this.data.delivery.fee;
    const discountAmount = this.data.coupon.selected ? this.data.coupon.selected.amount : 0;
    
    // 计算支付方式优惠 - 基于各种优惠后的最终价格
    const currentPaymentMethod = this.data.payment.methods.find(method => method.value === this.data.payment.method);
    let paymentDiscount = 0;
    
    if (currentPaymentMethod && currentPaymentMethod.offer_info) {
      // 使用后端优惠信息计算
      const offer = currentPaymentMethod.offer_info;
      // 基于各种优惠后的价格计算支付优惠（不包含配送费和优惠券）
      const baseAmountForPayment = productAmount; // 商品优惠后的价格
      
      if (baseAmountForPayment >= offer.min_amount) {
        switch (offer.offer_type) {
          case 'fixed_amount':
            paymentDiscount = Math.min(offer.offer_value, baseAmountForPayment);
            break;
          case 'percentage':
            paymentDiscount = baseAmountForPayment * (offer.offer_value / 100);
            if (offer.max_offer) {
              paymentDiscount = Math.min(paymentDiscount, offer.max_offer);
            }
            break;
        }
      }
    } else if (currentPaymentMethod && currentPaymentMethod.discount) {
      // 兜底：使用前端配置的固定优惠
      paymentDiscount = currentPaymentMethod.discount;
    }
    
    const totalAmount = productAmount + deliveryFee - discountAmount - paymentDiscount;
    
    return {
      productAmount: Number(productAmount.toFixed(2)),
      discountAmount: Number(discountAmount.toFixed(2)),
      deliveryFee: Number(deliveryFee.toFixed(2)),
      paymentDiscount: Number(paymentDiscount.toFixed(2)),
      totalAmount: Number(Math.max(0, totalAmount).toFixed(2)) // 确保总金额不为负数
    };
  },

  /**
   * 选择收货地址
   */
  onSelectAddress() {
    wx.navigateTo({
      url: `/pages/address/index?from=order-confirm&selectedId=${this.data.address.id || ''}`
    });
  },

  /**
   * 计算配送时间
   */
  calculateDeliveryTime() {
    try {
      const now = new Date();
      const dayOfWeek = now.getDay(); // 0=星期天, 1=星期一, ..., 6=星期六
      
      let deliveryTypeName = '';
      let deliveryDate = '';
      let timeSlots = [];
      
      if (dayOfWeek === 0) {
        // 星期天显示后日达
        deliveryTypeName = '后日达';
        // 计算后天日期
        const afterTomorrow = new Date(now);
        afterTomorrow.setDate(now.getDate() + 2);
        deliveryDate = this.formatDate(afterTomorrow);
        
        // 后日达时间段
        timeSlots = [
          '09:00-10:00',
          '10:00-11:00',
          '11:00-12:00',
          '14:00-15:00',
          '15:00-16:00',
          '16:00-17:00'
        ];
      } else {
        // 星期一到星期六显示明日达
        deliveryTypeName = '明日达';
        // 计算明天日期
        const tomorrow = new Date(now);
        tomorrow.setDate(now.getDate() + 1);
        deliveryDate = this.formatDate(tomorrow);
        
        // 明日达时间段
        timeSlots = [
          '09:00-10:00',
          '10:00-11:00',
          '11:00-12:00',
          '14:00-15:00',
          '15:00-16:00',
          '16:00-17:00'
        ];
      }
      
      // 设置默认时间段（第一个）
      const defaultTime = timeSlots[0] || '09:00-10:00';
      
      console.log('📅 当前星期:', dayOfWeek === 0 ? '星期天' : `星期${['', '一', '二', '三', '四', '五', '六'][dayOfWeek]}`);
      console.log('🚚 配送时间:', deliveryTypeName, deliveryDate, defaultTime);
      
      this.setData({
        'delivery.typeName': deliveryTypeName,
        'delivery.date': deliveryDate,
        'delivery.time': defaultTime,
        'delivery.timeSlots': timeSlots
      });
    } catch (error) {
      console.error('❌ 计算配送时间失败:', error);
      // 设置默认值
      this.setData({
        'delivery.typeName': '明日达',
        'delivery.date': '明天',
        'delivery.time': '09:00-10:00',
        'delivery.timeSlots': ['09:00-10:00', '10:00-11:00', '14:00-15:00']
      });
    }
  },

  /**
   * 格式化日期显示
   */
  formatDate(date) {
    try {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const weekDay = weekDays[date.getDay()];
      
      return `${month}月${day}日 ${weekDay}`;
    } catch (error) {
      console.error('❌ 格式化日期失败:', error);
      return '明天';
    }
  },

  /**
   * 选择配送时间
   */
  onSelectDeliveryTime() {
    const timeSlots = this.data.delivery.timeSlots || [];
    const deliveryTypeName = this.data.delivery.typeName;
    const deliveryDate = this.data.delivery.date;
    
    if (timeSlots.length === 0) {
      wx.showToast({
        title: '暂无可选时间段',
        icon: 'none'
      });
      return;
    }
    
    // 构建选项列表，格式：明日达 12月25日 周三 09:00-10:00
    const itemList = timeSlots.map(slot => `${deliveryTypeName} ${deliveryDate} ${slot}`);
    
    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        const selectedTime = timeSlots[res.tapIndex];
        
        this.setData({
          'delivery.time': selectedTime
        });
        
        wx.showToast({
          title: `已选择 ${deliveryTypeName} ${selectedTime}`,
          icon: 'success'
        });
      }
    });
  },

  /**
   * 选择优惠券
   */
  onSelectCoupon() {
    if (!this.data.coupon.hasAvailable) {
      wx.showToast({
        title: '暂无可用优惠券',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: '/pages/coupon/index?from=order-confirm'
    }).catch(() => {
      wx.showToast({
        title: '优惠券页面暂未开发',
        icon: 'none'
      });
    });
  },

  /**
   * 选择支付方式
   */
  onSelectPayment(e) {
    const { method } = e.currentTarget.dataset;
    
    if (!method || method === this.data.payment.method) {
      return;
    }
    
    this.setData({
      'payment.method': method
    });
    
    // 重新计算价格
    const summary = this.calculateSummary(this.data.products);
    this.setData({ summary });
    
    // 显示支付方式改变提示
    const paymentMethod = this.data.payment.methods.find(item => item.value === method);
    if (paymentMethod) {
      if (method === 'wechat') { // 修复：使用正确的支付方式值判断
        wx.showToast({
          title: `已选择${paymentMethod.name}，立减${paymentMethod.discount}元`,
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: `已选择${paymentMethod.name}`,
          icon: 'success'
        });
      }
    }
  },

  /**
   * 提交订单（修复版）
   */
  async onSubmitOrder() {
    if (this.data.submitting) {
      console.log('⏸️ 正在提交订单，请勿重复点击');
      return;
    }
    
    // 验证必填信息
    if (!this.validateOrderInfo()) {
      return;
    }
    
    try {
      console.log('🚀 开始提交订单...');
      this.setData({ submitting: true });
      
      // 显示提交中状态
      const submitLoadingKey = this.showActionLoading('提交订单中...');
      
      // 🔍 详细调试商品数据
      console.log('🔍 订单提交前商品数据详细调试:');
      this.data.products.forEach((product, index) => {
        console.log(`提交商品 ${index + 1}:`, {
          显示名称: product.name,
          商品ID: product.id,
          购物车项ID: product.cart_item_id,
          实际商品ID: product.product_id,
          数量: product.quantity,
          价格: product.price,
          调试信息: product._debug || '无调试信息',
          完整数据: product
        });
      });
      
      // 构建订单数据（修正为后端期望的格式）
      const orderData = {
        // 地址信息 - 修正字段名匹配后端验证规则
        user_address_id: parseInt(this.data.address.id),
        
        // 商品信息 - 🚨 关键修复：确保使用正确的商品ID
        items: this.data.products.map((product, index) => {
          // 优先使用 product_id，如果没有则使用 id（但要确保这是商品ID而不是购物车项ID）
          let productId = product.product_id || product.id;
          
          // 如果有购物车项ID，说明这可能是购物车项，需要使用 product_id
          if (product.cart_item_id && product.product_id) {
            productId = product.product_id;
          }
          
          const quantity = parseInt(product.quantity);
          
          console.log(`🔧 构建订单项 ${index + 1}:`, {
            原始商品名称: product.name,
            购物车项ID: product.cart_item_id || product.id,
            最终使用商品ID: productId,
            提交数量: quantity,
            数据来源: product.product_id ? '使用product_id' : '使用id字段'
          });
          
          return {
            product_id: parseInt(productId),
            quantity: quantity
            // 移除price字段，让后端重新计算价格
          };
        }),
        
        // 支付信息
        payment_method: String(this.data.payment.method || 'cod'),
        
        // 备注信息
        notes: String(this.data.notes || '').trim(),
        
        // 区域信息（优先使用页面设置的区域，否则使用用户默认区域）
        region_id: this.data.region?.id ? parseInt(this.data.region.id) : 
                   (this.data.userInfo?.region_id ? parseInt(this.data.userInfo.region_id) : null)
      };
      
      console.log('📦 订单数据:', orderData);
      
      // 🔍 调试区域信息传递
      console.log('🌍 区域信息调试:', {
        页面区域: this.data.region?.id || '无',
        用户区域: this.data.userInfo?.region_id || '无',
        最终传递: orderData.region_id || '无'
      });
      
      // 数据完整性检查
      this.logOrderDataValidation(orderData);
      
      // 详细记录每个字段的值和类型（用于后端调试）
      console.log('🔍 订单数据详细分析:', {
        address: {
          user_address_id: typeof orderData.user_address_id + ' = ' + orderData.user_address_id
        },
        items: {
          count: orderData.items.length,
          details: orderData.items.map((item, idx) => ({
            [`商品${idx + 1}`]: {
              product_id: typeof item.product_id + ' = ' + item.product_id,
              quantity: typeof item.quantity + ' = ' + item.quantity
            }
          }))
        },
        payment: {
          method: typeof orderData.payment_method + ' = ' + orderData.payment_method
        },
        other: {
          notes: typeof orderData.notes + ' = ' + orderData.notes,
          region_id: typeof orderData.region_id + ' = ' + orderData.region_id
        }
      });
      
      // 提交订单
      const result = await api.createOrder(orderData);
      
      this.hideActionLoading(submitLoadingKey);
      
      console.log('📦 订单提交结果:', result);
      
      // 处理不同的返回格式
      let orderId = null;
      let success = false;
      
      if (result && result.data) {
        success = true;
        orderId = result.data.order_id || result.data.id || result.data.orderId;
      } else if (result && result.id) {
        success = true;
        orderId = result.id;
      } else if (result && result.order_id) {
        success = true;
        orderId = result.order_id;
      }
      
      if (success && orderId) {
        console.log('✅ 订单提交成功，订单ID:', orderId);
        
        // 清除购物车选中商品
        await this.clearSelectedCartProducts();
        
        wx.showToast({
          title: '订单提交成功',
          icon: 'success'
        });
        
        // 根据支付方式决定后续流程
        if (this.data.payment.method === 'wechat') { // 修复：使用正确的支付方式值判断
          // 在线支付：直接调用支付流程
          setTimeout(() => {
            this.handleOnlinePayment(orderId);
          }, 1500);
        } else {
          // 货到付款：跳转到订单成功页面
          setTimeout(() => {
            this.navigateToSuccess(orderId, 'cod');
          }, 1500);
        }
      } else {
        throw new Error(result?.message || result?.msg || '订单提交失败');
      }
      
    } catch (error) {
      this.hideActionLoading(submitLoadingKey);
      console.error('❌ 提交订单失败:', error);
      
      let errorMsg = '提交失败，请重试';
      let showRetry = true;
      
      if (error.message) {
        const message = error.message.toLowerCase();
        
        if (message.includes('验证失败') || message.includes('数据验证失败')) {
          errorMsg = '订单信息验证失败，请检查填写的信息';
          showRetry = false; // 验证失败不建议重试，需要用户修改数据
        } else if (message.includes('地址')) {
          errorMsg = '收货地址信息有误，请检查';
          showRetry = false;
        } else if (message.includes('商品')) {
          errorMsg = '商品信息有误，请重新选择';
          showRetry = false;
        } else if (message.includes('库存')) {
          errorMsg = '商品库存不足，请调整数量';
          showRetry = false;
        } else if (message.includes('价格')) {
          errorMsg = '商品价格异常，请刷新后重试';
          showRetry = false;
        } else if (message.includes('支付')) {
          errorMsg = '支付方式异常，请重新选择';
          showRetry = false;
        } else if (message.includes('网络') || message.includes('超时')) {
          errorMsg = '网络异常，请检查网络后重试';
          showRetry = true;
        } else {
          errorMsg = error.message;
        }
      }
      
      // 记录详细错误信息用于调试
      console.error('📋 订单提交错误详情:', {
        error: error.message,
        stack: error.stack,
        orderData: this.data,
        timestamp: new Date().toISOString()
      });
      
      wx.showModal({
        title: '订单提交失败',
        content: errorMsg,
        showCancel: showRetry,
        cancelText: '返回修改',
        confirmText: showRetry ? '重试' : '确定',
        success: (res) => {
          if (res.confirm && showRetry) {
            // 重试提交
            setTimeout(() => {
              this.onSubmitOrder();
            }, 500);
          }
        }
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 跳转到成功页面
   */
  navigateToSuccess(orderId, paymentMethod, status = 'success') {
    const url = `/pages/order-success/index?orderId=${orderId}&paymentMethod=${paymentMethod}&status=${status}`;
    
    wx.redirectTo({ url }).catch(() => {
      // 如果订单成功页面不存在，跳转到订单列表
      wx.redirectTo({
        url: '/pages/order-list/index'
      }).catch(() => {
        // 如果订单列表页面也不存在，返回首页
        wx.switchTab({
          url: '/pages/index/index'
        });
      });
    });
  },

  /**
   * 验证订单信息
   */
  validateOrderInfo() {
    // 验证收货地址
    if (!this.data.address.id) {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none'
      });
      return false;
    }
    
    // 验证地址完整性
    if (!this.data.address.name || !this.data.address.phone || !this.data.address.detail) {
      wx.showToast({
        title: '收货地址信息不完整',
        icon: 'none'
      });
      return false;
    }
    
    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(this.data.address.phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      });
      return false;
    }
    
    // 验证商品信息
    if (!this.data.products || this.data.products.length === 0) {
      wx.showToast({
        title: '购物车为空',
        icon: 'none'
      });
      return false;
    }
    
    // 验证商品数据完整性
    for (let i = 0; i < this.data.products.length; i++) {
      const product = this.data.products[i];
      if (!product.id || !product.quantity || !product.price) {
        wx.showToast({
          title: `商品${i + 1}信息不完整`,
          icon: 'none'
        });
        return false;
      }
      
      if (product.quantity <= 0) {
        wx.showToast({
          title: `商品${product.name || (i + 1)}数量必须大于0`,
          icon: 'none'
        });
        return false;
      }
      
      if (product.price <= 0) {
        wx.showToast({
          title: `商品${product.name || (i + 1)}价格异常`,
          icon: 'none'
        });
        return false;
      }
    }
    
    // 验证支付方式
    if (!this.data.payment.method) {
      wx.showToast({
        title: '请选择支付方式',
        icon: 'none'
      });
      return false;
    }
    
    // 验证总金额
    if (!this.data.summary.totalAmount || this.data.summary.totalAmount <= 0) {
      wx.showToast({
        title: '订单金额异常',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  /**
   * 清除购物车选中商品
   */
  async clearSelectedCartProducts() {
    try {
      // 只有从购物车来的订单才清除购物车
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      
      if (options.from === 'cart') {
        console.log('🗑️ 开始清除购物车中已下单的商品...');
        
        // 获取已下单的商品信息
        const orderedProducts = this.data.products || [];
        
        // 批量删除购物车中的商品
        const deletePromises = orderedProducts.map(async (product) => {
          try {
            // 使用购物车项ID删除（不是商品ID）
            const cartItemId = product._debug?.购物车项ID || product.cart_item_id || product.id;
            
            if (cartItemId) {
              console.log(`🗑️ 删除购物车项: ${cartItemId} (${product.name})`);
              await api.removeFromCart(cartItemId);
              return { success: true, id: cartItemId, name: product.name };
            } else {
              console.warn(`⚠️ 商品 ${product.name} 没有购物车项ID，跳过删除`);
              return { success: false, id: null, name: product.name, reason: '缺少购物车项ID' };
            }
          } catch (error) {
            console.error(`❌ 删除购物车项失败: ${product.name}`, error);
            return { success: false, id: product.id, name: product.name, error: error.message };
          }
        });
        
        // 等待所有删除操作完成
        const results = await Promise.all(deletePromises);
        
        // 统计删除结果
        const successCount = results.filter(r => r.success).length;
        const failCount = results.filter(r => !r.success).length;
        
        console.log(`✅ 购物车清理完成: 成功删除 ${successCount} 个商品，失败 ${failCount} 个`);
        
        if (failCount > 0) {
          console.warn('❌ 删除失败的商品:', results.filter(r => !r.success));
        }
        
        // 清除本地缓存
        wx.removeStorageSync('selectedCartProducts');
        console.log('🗑️ 已清除购物车选中商品缓存');
        
        // 如果有删除失败的情况，给用户提示
        if (failCount > 0 && successCount > 0) {
          wx.showToast({
            title: `已清理 ${successCount} 个商品`,
            icon: 'success',
            duration: 2000
          });
        }
      }
    } catch (error) {
      console.error('❌ 清除购物车失败:', error);
      // 即使API调用失败，也要清除本地缓存
      try {
        wx.removeStorageSync('selectedCartProducts');
        console.log('🗑️ 已清除购物车选中商品缓存（降级处理）');
      } catch (cacheError) {
        console.error('❌ 清除本地缓存也失败:', cacheError);
      }
    }
  },

  /**
   * 记录订单数据验证信息
   */
  logOrderDataValidation(orderData) {
    console.log('🔍 订单数据验证检查:');
    
    // 检查地址信息
    console.log('📍 地址信息:', {
      user_address_id: orderData.user_address_id,
      address_valid: !!(orderData.user_address_id)
    });
    
    // 检查商品信息
    console.log('🛍️ 商品信息:', {
      items_count: orderData.items.length,
      items_valid: orderData.items.every(item => item.product_id && item.quantity > 0),
      items_detail: orderData.items.map(item => ({
        product_id: item.product_id,
        quantity: item.quantity,
        valid: !!(item.product_id && item.quantity > 0)
      }))
    });
    
    // 检查其他信息
    console.log('ℹ️ 其他信息:', {
      payment_method: orderData.payment_method,
      notes: orderData.notes,
      region_id: orderData.region_id
    });
    
    // 整体验证状态
    const isValid = !!(
      orderData.user_address_id &&
      orderData.items.length > 0 &&
      orderData.items.every(item => item.product_id && item.quantity > 0) &&
      orderData.payment_method
    );
    
    console.log('✅ 整体验证状态:', isValid);
  },

  /**
   * 处理在线支付（修复版）
   */
  async handleOnlinePayment(orderId) {
    console.log('💳 开始处理在线支付，订单ID:', orderId);
    
    try {
      // 显示支付加载提示
      const paymentLoadingKey = this.showActionLoading('准备支付...');
      
      // 调用支付API获取支付参数
      const payResult = await api.payOrder(orderId);
      
      console.log('💳 支付API返回:', payResult);
      
      this.hideActionLoading(paymentLoadingKey);
      
      // 处理不同的支付API返回格式
      if (payResult && (payResult.success || payResult.data || payResult.payment_params)) {
        const paymentParams = payResult.data || payResult.payment_params || payResult;
        
        // 调用微信支付
        await this.callWxPay(paymentParams, orderId);
        
      } else {
        throw new Error(payResult?.message || payResult?.msg || '获取支付参数失败');
      }
      
    } catch (error) {
      this.hideActionLoading(paymentLoadingKey);
      console.error('❌ 支付处理失败:', error);
      
      this.handlePaymentError(error, orderId);
    }
  },

  /**
   * 调用微信支付
   */
  async callWxPay(paymentParams, orderId) {
    return new Promise((resolve, reject) => {
      wx.requestPayment({
        timeStamp: paymentParams.timeStamp || paymentParams.timestamp || String(Date.now()),
        nonceStr: paymentParams.nonceStr || paymentParams.nonce_str || '',
        package: paymentParams.package || paymentParams.prepay_id ? `prepay_id=${paymentParams.prepay_id}` : '',
        signType: paymentParams.signType || paymentParams.sign_type || 'MD5',
        paySign: paymentParams.paySign || paymentParams.pay_sign || paymentParams.sign || '',
        
        success: (res) => {
          console.log('✅ 支付成功:', res);
          
          wx.showToast({
            title: '支付成功',
            icon: 'success'
          });
          
          // 跳转到支付成功页面
          setTimeout(() => {
            this.navigateToSuccess(orderId, 'wechat', 'success'); // 修复：使用正确的支付方式值
          }, 1500);
          
          resolve(res);
        },
        
        fail: (err) => {
          console.error('❌ 支付失败:', err);
          
          if (err.errMsg && err.errMsg.includes('cancel')) {
            // 用户取消支付
            wx.showToast({
              title: '支付已取消',
              icon: 'none'
            });
            
            // 跳转到订单列表，用户可以稍后支付
            setTimeout(() => {
              wx.redirectTo({
                url: '/pages/order-list/index'
              }).catch(() => {
                wx.switchTab({
                  url: '/pages/index/index'
                });
              });
            }, 1500);
          } else {
            // 其他支付错误
            this.handlePaymentError(err, orderId);
          }
          
          reject(err);
        }
      });
    });
  },

  /**
   * 处理支付错误
   */
  handlePaymentError(error, orderId) {
    let errorMsg = '支付失败，请重试';
    
    if (error.message || error.errMsg) {
      const msg = error.message || error.errMsg;
      if (msg.includes('取消') || msg.includes('cancel')) {
        errorMsg = '支付已取消';
      } else if (msg.includes('余额')) {
        errorMsg = '账户余额不足';
      } else if (msg.includes('网络')) {
        errorMsg = '网络异常，请检查网络连接';
      } else if (msg.includes('参数')) {
        errorMsg = '支付参数错误，请联系客服';
      } else if (msg.includes('openid')) {
        errorMsg = '用户信息异常，请重新登录';
      } else {
        errorMsg = msg;
      }
    }
    
    wx.showModal({
      title: '支付失败',
      content: errorMsg,
      showCancel: true,
      cancelText: '稍后支付',
      confirmText: '重新支付',
      success: (res) => {
        if (res.confirm) {
          // 重新支付
          setTimeout(() => {
            this.handleOnlinePayment(orderId);
          }, 500);
        } else {
          // 跳转到订单列表，用户可以稍后支付
          wx.redirectTo({
            url: '/pages/order-list/index'
          }).catch(() => {
            wx.switchTab({
              url: '/pages/index/index'
            });
          });
        }
      }
    });
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 验证商品ID映射是否正确
   */
  async validateProductMapping() {
    try {
      console.log('🔍 开始验证商品ID映射...');
      
      // 获取当前页面的商品列表
      const products = this.data.products || [];
      
      if (products.length === 0) {
        console.log('⚠️ 没有商品需要验证');
        return;
      }
      
      // 逐个验证商品信息
      for (let i = 0; i < products.length; i++) {
        const product = products[i];
        const productId = product.product_id || product.id;
        
        console.log(`🔍 验证商品 ${i + 1}:`, {
          前端显示名称: product.name,
          商品ID: productId,
          数量: product.quantity
        });
        
        try {
          // 调用后端API获取真实的商品信息
          const realProduct = await api.getProductDetail(productId);
          
          if (realProduct && realProduct.data) {
            const realName = realProduct.data.name;
            const frontendName = product.name;
            
            console.log(`📊 商品 ${i + 1} 对比结果:`, {
              商品ID: productId,
              前端显示名称: frontendName,
              后端真实名称: realName,
              名称匹配: frontendName === realName ? '✅' : '❌'
            });
            
            // 如果名称不匹配，发出警告
            if (frontendName !== realName) {
              console.warn(`🚨 商品ID映射错误！`, {
                商品ID: productId,
                前端显示: frontendName,
                后端实际: realName
              });
              
              // 显示警告给用户
              wx.showModal({
                title: '商品信息异常',
                content: `检测到商品信息不一致：\n显示：${frontendName}\n实际：${realName}\n\n建议刷新页面重新选择商品`,
                showCancel: true,
                cancelText: '继续',
                confirmText: '刷新',
                success: (res) => {
                  if (res.confirm) {
                    wx.navigateBack();
                  }
                }
              });
            }
          }
        } catch (error) {
          console.error(`❌ 验证商品 ${productId} 失败:`, error);
        }
      }
      
      console.log('✅ 商品ID映射验证完成');
      
    } catch (error) {
      console.error('❌ 商品ID映射验证失败:', error);
    }
  },
}); 