<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('banners', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('轮播图标题');
            $table->string('image_url')->comment('轮播图图片URL');
            $table->string('link_url')->nullable()->comment('轮播图跳转链接');
            $table->integer('sort_order')->default(0)->comment('排序顺序');
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->timestamp('start_time')->nullable()->comment('开始展示时间');
            $table->timestamp('end_time')->nullable()->comment('结束展示时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('banners');
    }
}; 