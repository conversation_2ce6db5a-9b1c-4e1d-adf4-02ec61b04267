<template>
	<view class="analytics-container">
		<!-- 页面头部 -->
		<view class="header-section">
			<view class="header-title">
				<text class="title-text">行为分析</text>
				<text class="subtitle-text">数据驱动决策</text>
			</view>
			<view class="date-filter" @tap="showDatePicker">
				<text class="date-text">{{ formatDateRange() }}</text>
				<text class="filter-icon">📅</text>
					</view>
				</view>

		<!-- 核心指标卡片 -->
		<view class="metrics-section">
			<view class="metrics-grid">
				<view class="metric-card" @tap="goToDetail('overview')">
					<view class="metric-icon">👥</view>
					<view class="metric-content">
						<text class="metric-number">{{ behaviorOverview.active_users || 0 }}</text>
						<text class="metric-label">活跃用户</text>
						<text class="metric-trend" :class="getTrendClass('up')">↑ 12%</text>
					</view>
				</view>
				
				<view class="metric-card" @tap="goToDetail('sessions')">
					<view class="metric-icon">📱</view>
					<view class="metric-content">
						<text class="metric-number">{{ formatNumber(behaviorOverview.total_sessions) }}</text>
						<text class="metric-label">总会话</text>
						<text class="metric-trend" :class="getTrendClass('up')">↑ 8%</text>
					</view>
				</view>
				
				<view class="metric-card" @tap="goToDetail('duration')">
					<view class="metric-icon">⏱️</view>
					<view class="metric-content">
						<text class="metric-number">{{ formatDuration(behaviorOverview.avg_session_duration) }}</text>
						<text class="metric-label">平均时长</text>
						<text class="metric-trend" :class="getTrendClass('down')">↓ 3%</text>
					</view>
				</view>
				
				<view class="metric-card" @tap="goToDetail('conversion')">
					<view class="metric-icon">💰</view>
					<view class="metric-content">
						<text class="metric-number">{{ behaviorOverview.repurchase_rate || 0 }}%</text>
						<text class="metric-label">复购率</text>
						<text class="metric-trend" :class="getTrendClass('up')">↑ 15%</text>
			</view>
		</view>
			</view>
				</view>

		<!-- 分析模块 -->
		<view class="analysis-modules">
			<view class="module-title">
				<text>分析模块</text>
				</view>
			
			<view class="modules-grid">
				<view class="module-item" @tap="goToModule('browse')">
					<view class="module-icon">🔍</view>
					<view class="module-content">
						<text class="module-name">浏览分析</text>
						<text class="module-desc">页面访问路径分析</text>
				</view>
					<view class="module-arrow">></view>
				</view>
				
				<view class="module-item" @tap="goToModule('purchase')">
					<view class="module-icon">🛒</view>
					<view class="module-content">
						<text class="module-name">购买分析</text>
						<text class="module-desc">购买行为深度分析</text>
			</view>
					<view class="module-arrow">></view>
		</view>

				<view class="module-item" @tap="goToModule('customer-segment')">
					<view class="module-icon">👥</view>
					<view class="module-content">
						<text class="module-name">客户细分</text>
						<text class="module-desc">RFM模型客户分析</text>
			</view>
					<view class="module-arrow">></view>
					</view>
				
				<view class="module-item" @tap="goToModule('churn-warning')">
					<view class="module-icon">⚠️</view>
					<view class="module-content">
						<text class="module-name">流失预警</text>
						<text class="module-desc">客户流失风险预测</text>
						<view class="warning-badge" v-if="churnCount > 0">{{ churnCount }}</view>
					</view>
					<view class="module-arrow">></view>
				</view>
				
				<view class="module-item" @tap="goToModule('product-preference')">
					<view class="module-icon">📊</view>
					<view class="module-content">
						<text class="module-name">商品偏好</text>
						<text class="module-desc">热门商品趋势分析</text>
			</view>
					<view class="module-arrow">></view>
		</view>

				<view class="module-item" @tap="goToModule('time-analysis')">
					<view class="module-icon">⏰</view>
					<view class="module-content">
						<text class="module-name">时间分析</text>
						<text class="module-desc">用户活跃时段分析</text>
			</view>
					<view class="module-arrow">></view>
					</view>
				</view>
						</view>

		<!-- 快速洞察 -->
		<view class="insights-section" v-if="quickInsights.length > 0">
			<view class="section-title">
				<text>💡 快速洞察</text>
					</view>
			
			<view class="insights-list">
				<view class="insight-card" v-for="(insight, index) in quickInsights" :key="index" @tap="handleInsightAction(insight)">
					<view class="insight-header">
						<view class="insight-icon">{{ insight.icon }}</view>
						<view class="insight-priority" :class="insight.priority">{{ insight.priorityText }}</view>
				</view>
					<text class="insight-title">{{ insight.title }}</text>
					<text class="insight-description">{{ insight.description }}</text>
					<view class="insight-action">
						<text class="action-btn">{{ insight.actionText }}</text>
			</view>
		</view>
			</view>
					</view>

		<!-- 日期选择器 -->
		<view class="date-picker-modal" v-if="showDateModal" @tap="closeDatePicker">
			<view class="date-picker-container" @tap.stop>
				<view class="picker-header">
					<text class="picker-title">选择时间范围</text>
					<text class="picker-close" @tap="closeDatePicker">✕</text>
					</view>
				<view class="date-options">
					<view class="date-option" 
						  v-for="option in dateOptions" 
						  :key="option.value"
						  :class="{ active: selectedDateRange === option.value }"
						  @tap="selectDateRange(option.value)">
						<text class="option-text">{{ option.label }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import analyticsApi from '../../api/analytics.js'

export default {
	data() {
		return {
			behaviorOverview: {},
			churnCount: 0,
			quickInsights: [],
			selectedDateRange: 'last_30_days',
			dateOptions: [
				{ value: 'today', label: '今天' },
				{ value: 'last_7_days', label: '最近7天' },
				{ value: 'last_30_days', label: '最近30天' },
				{ value: 'last_90_days', label: '最近90天' }
			],
			loading: false,
			showDateModal: false
		}
	},
	
	onLoad() {
		this.loadAnalyticsData()
	},
	
	onShow() {
		this.loadAnalyticsData()
	},
	
	onPullDownRefresh() {
		this.refreshData()
	},
	
	methods: {
		// 加载分析数据
		async loadAnalyticsData() {
			this.loading = true
			try {
				await Promise.all([
					this.loadBehaviorOverview(),
					this.loadChurnCount(),
					this.loadQuickInsights()
				])
			} catch (error) {
				console.error('加载分析数据失败:', error)
			} finally {
				this.loading = false
			}
		},
		
		// 加载行为概览
		async loadBehaviorOverview() {
			try {
				const params = this.getDateRangeParams()
				const response = await analyticsApi.getBehaviorOverview(params)
				this.behaviorOverview = response.data || {}
			} catch (error) {
				console.error('加载行为概览失败:', error)
			}
		},
		
		// 加载流失预警数量
		async loadChurnCount() {
			try {
				const response = await analyticsApi.getChurnWarning({ limit: 1 })
				this.churnCount = response.data.total_warnings || 0
			} catch (error) {
				console.error('加载流失预警数量失败:', error)
			}
		},
		
		// 加载快速洞察
		async loadQuickInsights() {
			try {
				this.quickInsights = this.generateQuickInsights()
			} catch (error) {
				console.error('加载快速洞察失败:', error)
			}
		},
		
		// 生成快速洞察
		generateQuickInsights() {
			const insights = []
			
			// 流失预警洞察
			if (this.churnCount > 0) {
				insights.push({
					icon: '⚠️',
					title: '客户流失风险',
					description: `发现 ${this.churnCount} 位客户存在流失风险，建议立即采取挽回措施`,
					priority: 'high',
					priorityText: '高优先级',
					actionText: '立即处理',
					action: 'churn_warning'
				})
			}
			
			// 复购率洞察
			if (this.behaviorOverview.repurchase_rate < 50) {
				insights.push({
					icon: '📈',
					title: '复购率待提升',
					description: `当前复购率 ${this.behaviorOverview.repurchase_rate}%，可通过精准营销提升客户粘性`,
					priority: 'medium',
					priorityText: '中优先级',
					actionText: '查看策略',
					action: 'purchase_analysis'
				})
			}
			
			// 会话质量洞察
			if (this.behaviorOverview.avg_session_duration > 300) {
				insights.push({
					icon: '🎯',
					title: '用户参与度高',
					description: `平均会话时长 ${Math.round(this.behaviorOverview.avg_session_duration/60)} 分钟，用户粘性良好`,
					priority: 'low',
					priorityText: '关注',
					actionText: '深入分析',
					action: 'browse_analysis'
				})
			}
			
			return insights.slice(0, 3)
		},
		
		// 刷新数据
		async refreshData() {
			await this.loadAnalyticsData()
			uni.stopPullDownRefresh()
		},
		
		// 获取日期范围参数
		getDateRangeParams() {
			const now = new Date()
			let startDate, endDate = now
			
			switch (this.selectedDateRange) {
				case 'today':
					startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
					break
				case 'last_7_days':
					startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
					break
				case 'last_30_days':
					startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
					break
				case 'last_90_days':
					startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
					break
				default:
					startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
			}
			
			return {
				start_date: startDate.toISOString().split('T')[0],
				end_date: endDate.toISOString().split('T')[0]
			}
		},
		
		// 格式化日期范围
		formatDateRange() {
			const option = this.dateOptions.find(opt => opt.value === this.selectedDateRange)
			return option ? option.label : '最近30天'
		},
		
		// 格式化数字
		formatNumber(num) {
			if (!num) return '0'
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + 'w'
			}
			if (num >= 1000) {
				return (num / 1000).toFixed(1) + 'k'
			}
			return num.toString()
		},
		
		// 格式化时长
		formatDuration(seconds) {
			if (!seconds) return '0s'
			if (seconds >= 3600) {
				return Math.round(seconds / 3600) + 'h'
			}
			if (seconds >= 60) {
				return Math.round(seconds / 60) + 'm'
			}
			return Math.round(seconds) + 's'
		},
		
		// 获取趋势类
		getTrendClass(trend) {
			return {
				positive: trend === 'up',
				negative: trend === 'down'
			}
		},
		
		// 显示日期选择器
		showDatePicker() {
			this.showDateModal = true
		},
		
		// 关闭日期选择器
		closeDatePicker() {
			this.showDateModal = false
		},
		
		// 选择日期范围
		selectDateRange(range) {
			this.selectedDateRange = range
			this.closeDatePicker()
			this.loadAnalyticsData()
		},
		
		// 跳转到详情
		goToDetail(type) {
			// 实现跳转逻辑
			console.log('跳转到详情:', type)
		},
		
		// 跳转到模块
		goToModule(module) {
			const moduleRoutes = {
				'browse': '/pages/analytics/browse-analysis',
				'purchase': '/pages/analytics/purchase-analysis',
				'customer-segment': '/pages/analytics/customer-segment',
				'churn-warning': '/pages/analytics/churn-warning',
				'product-preference': '/pages/analytics/product-analysis',
				'time-analysis': '/pages/analytics/time-analysis'
			}
			
			const route = moduleRoutes[module]
			if (route) {
			uni.navigateTo({
					url: route
				})
			}
		},
		
		// 处理洞察操作
		handleInsightAction(insight) {
			switch (insight.action) {
				case 'churn_warning':
					this.goToModule('churn-warning')
					break
				case 'purchase_analysis':
					this.goToModule('purchase')
					break
				case 'browse_analysis':
					this.goToModule('browse')
					break
				default:
					console.log('未知的洞察操作:', insight.action)
			}
		}
	}
}
</script>

<style scoped>
.analytics-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 页面头部 */
.header-section {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx 32rpx 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	color: #ffffff;
}

.header-title {
	flex: 1;
}

.title-text {
	font-size: 40rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
}

.subtitle-text {
	font-size: 28rpx;
	opacity: 0.8;
}

.date-filter {
	display: flex;
	align-items: center;
	background: rgba(255, 255, 255, 0.2);
	padding: 16rpx 24rpx;
	border-radius: 50rpx;
}

.date-text {
	font-size: 28rpx;
	margin-right: 8rpx;
}

.filter-icon {
	font-size: 24rpx;
}

/* 核心指标 */
.metrics-section {
	margin: 20rpx;
}

.metrics-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
}

.metric-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	display: flex;
	align-items: center;
	transition: all 0.3s ease;
}

.metric-card:active {
	transform: scale(0.98);
}

.metric-icon {
	font-size: 48rpx;
	margin-right: 24rpx;
}

.metric-content {
	flex: 1;
}

.metric-number {
	font-size: 36rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.metric-label {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 8rpx;
}

.metric-trend {
	font-size: 24rpx;
}

.metric-trend.positive {
	color: #28a745;
}

.metric-trend.negative {
	color: #dc3545;
}

/* 分析模块 */
.analysis-modules {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.module-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 24rpx;
}

.modules-grid {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.module-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	transition: all 0.3s ease;
	position: relative;
}

.module-item:active {
	transform: scale(0.98);
	background: #e9ecef;
}

.module-icon {
	font-size: 40rpx;
	margin-right: 24rpx;
}

.module-content {
	flex: 1;
}

.module-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.module-desc {
	font-size: 24rpx;
	color: #666666;
}

.module-arrow {
	font-size: 32rpx;
	color: #cccccc;
}

.warning-badge {
	position: absolute;
	top: 16rpx;
	right: 60rpx;
	background: #ff4757;
	color: #ffffff;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 50rpx;
	min-width: 32rpx;
	text-align: center;
}

/* 快速洞察 */
.insights-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 24rpx;
}

.insights-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.insight-card {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	transition: all 0.3s ease;
}

.insight-card:active {
	transform: scale(0.98);
	background: #e9ecef;
}

.insight-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.insight-icon {
	font-size: 32rpx;
}

.insight-priority {
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 50rpx;
	color: #ffffff;
}

.insight-priority.high {
	background: #ff4757;
}

.insight-priority.medium {
	background: #ffa502;
}

.insight-priority.low {
	background: #2ed573;
}

.insight-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 12rpx;
}

.insight-description {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.4;
	margin-bottom: 16rpx;
}

.insight-action {
	text-align: right;
}

.action-btn {
	font-size: 28rpx;
	color: #007AFF;
	font-weight: 600;
}

/* 日期选择器 */
.date-picker-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	justify-content: center;
	z-index: 9999;
}

.date-picker-container {
	background: #ffffff;
	border-radius: 16rpx 16rpx 0 0;
	padding: 32rpx;
	width: 100%;
	max-width: 750rpx;
	animation: slideUp 0.3s ease;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
}
	to {
		transform: translateY(0);
	}
}

.picker-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 32rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.picker-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.picker-close {
	font-size: 32rpx;
	color: #666666;
	padding: 8rpx;
	border-radius: 50%;
	background: #f8f9fa;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.date-options {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.date-option {
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	text-align: center;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
}

.date-option:active {
	transform: scale(0.98);
}

.date-option.active {
	background: #007AFF;
	color: #ffffff;
	border-color: #007AFF;
}

.option-text {
	font-size: 32rpx;
	font-weight: 500;
}
</style> 