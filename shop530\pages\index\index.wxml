<!-- 固定标题栏背景层 - 根据滚动状态动态显示 -->
<view class="fixed-header-bg {{showHeaderBg ? 'show' : 'hide'}}" style="height: {{statusBarHeight + 90}}px;" wx:if="{{!loading}}"></view>

<!-- 固定标题栏 -->
<view class="simple-title-bar" style="top: {{statusBarHeight}}px;" wx:if="{{!loading}}">
  <text class="simple-title-text">天心食品</text>
  <!-- 配送时间显示块 - 在标题后面同一行 -->
  <text class="delivery-time-text">{{deliveryText}}</text>
</view>

<!-- 搜索框容器 -->
<view class="search-container" style="top: {{statusBarHeight + 50}}px;">
  <search-header 
    show="{{true}}"
    value="{{searchKeyword}}"
    placeholder="搜索商品"
    showSearchButton="{{true}}"
    buttonPosition="right"
    searchIcon="search"
    bind:search="onSearch"
    bind:change="onSearchChange"
    bind:click-input="onSearchTap"
    bind:search-button-click="onSearchTap"
    bind:clear="onSearchClear"
  />
</view>

<view class="home-container">

  <!-- 骨架屏 -->
  <skeleton 
    loading="{{loading}}"
    show-header="{{true}}"
    show-search="{{true}}"
    show-banner="{{true}}"
    show-service="{{true}}"
    show-category="{{true}}"
    show-products="{{true}}"
    category-count="{{10}}"
    section-count="{{4}}"
    product-count="{{2}}"
  />

  <!-- 主要内容 -->
  <view class="main-content" wx:if="{{!loading}}">
    
    <!-- 轮播图区域 - 三层切割轮播 -->
    <view class="banner-section-layered" wx:if="{{bannerList && bannerList.length > 0}}">
      
      <!-- 上层 - 静态装饰层 -->
      <view class="banner-layer banner-layer-top">
        <view class="banner-item-top">
          <lazy-image
            src="{{bannerList[0].image || ''}}"
            mode="aspectFill"
            width="100%"
            height="100%"
            custom-class="banner-image-top"
          />
        </view>
      </view>
      
      <!-- 中层 - 主轮播区域 -->
      <view class="banner-layer banner-layer-middle">
        <swiper 
          class="banner-swiper-middle" 
          indicator-dots="{{true}}"
          autoplay="{{bannerAutoplay}}"
          interval="{{bannerInterval}}"
          duration="{{800}}"
          circular="{{true}}"
          indicator-color="rgba(255,255,255,0.4)"
          indicator-active-color="rgba(255,255,255,0.9)"
          bindchange="onBannerChange"
          easing-function="easeInOutCubic"
          current="{{currentBannerIndex}}"
        >
          <block wx:for="{{bannerList}}" wx:key="id">
            <swiper-item>
              <view class="banner-item-middle" bindtap="onBannerTap" data-item="{{item}}">
                <lazy-image
                  src="{{item.image || ''}}"
                  mode="aspectFill"
                  width="100%"
                  height="100%"
                  custom-class="banner-image-middle"
                />
              </view>
            </swiper-item>
          </block>
        </swiper>
      </view>
      
      <!-- 下层 - 静态装饰层 -->
      <view class="banner-layer banner-layer-bottom">
        <view class="banner-item-bottom">
          <lazy-image
            src="{{bannerList[0].image || ''}}"
            mode="aspectFill"
            width="100%"
            height="100%"
            custom-class="banner-image-bottom"
          />
        </view>
      </view>
    </view>

    <!-- 备用轮播图显示 - 当没有数据时 -->
    <view class="banner-section-layered" wx:else>
      <view class="banner-placeholder"></view>
    </view>

    <!-- 分类网格 -->
    <view class="category-section" wx:if="{{categoryList && categoryList.length > 0}}">
      <view class="category-grid-container">
        <view 
          class="category-grid-item" 
          wx:for="{{categoryList}}" 
          wx:for-index="index"
          wx:key="id"
          bindtap="onCategoryTap"
          data-category="{{item}}"
        >
          <view class="category-icon-container">
            <lazy-image
              src="{{item.icon}}"
              width="100rpx"
              height="100rpx"
              mode="aspectFill"
              custom-class="category-icon radius-round border shadow-light"
            />
          </view>
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>
    </view>

    <!-- 服务特色 -->
    <view class="service-container" wx:if="{{serviceFeatures && serviceFeatures.length > 0}}">
      <view 
        class="service-item" 
        wx:for="{{serviceFeatures}}" 
        wx:key="id"
      >
        <van-icon name="{{item.icon}}" size="36rpx" color="#999" />
        <text class="service-text">{{item.text}}</text>
      </view>
    </view>

    <!-- 活动通知栏 -->
    <view class="notice-container">
      <view class="notice-content" bindtap="onNoticeTap">
        <van-icon name="volume-o" size="32rpx" color="#ff6b35" class="notice-icon" />
        <view class="notice-text-wrapper">
          <text class="notice-text">天心食品欢迎您~</text>
        </view>
        <van-icon name="arrow" size="24rpx" color="#999" />
      </view>
    </view>

    <!-- 商品Tab页 -->
    <view class="product-tabs-container" wx:if="{{sectionTabs && sectionTabs.length > 0}}">
      <!-- Tab标签页 - 使用Vant Sticky + Tabs -->
      <van-sticky 
        offset-top="{{offsetTop}}" 
        z-index="999"
        bind:scroll="onStickyScroll"
      >
        <van-tabs 
          active="{{activeTab}}" 
          bind:change="onTabChange"
          color="#4CAF50"
          background="#fff"
          line-width="60rpx"
          line-height="6rpx"
          title-active-color="#4CAF50"
          title-inactive-color="#666"
          animated="{{true}}"
          swipeable="{{false}}"
        >
          <van-tab 
            wx:for="{{sectionTabs}}"
            wx:key="type"
            title="{{item.name}}"
          />
        </van-tabs>
      </van-sticky>
      
      <!-- Tab内容区域 -->
      <view class="tab-content">
        <!-- 标签切换骨架屏 -->
        <view class="tab-switching-skeleton" wx:if="{{tabSwitching}}">
          <view class="skeleton-section">
            <view class="skeleton-header">
              <view class="skeleton-title"></view>
              <view class="skeleton-more"></view>
            </view>
            <view class="skeleton-products">
              <view class="skeleton-product-row">
                <view class="skeleton-product-card"></view>
                <view class="skeleton-product-card"></view>
              </view>
              <view class="skeleton-product-row">
                <view class="skeleton-product-card"></view>
                <view class="skeleton-product-card"></view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 实际内容 - 只在非切换状态时显示 -->
        <view class="tab-actual-content" wx:if="{{!tabSwitching}}">
          <!-- 全部商品和特定分类商品统一显示逻辑 -->
          <view class="product-section" wx:if="{{currentSectionProducts && currentSectionProducts.length > 0}}">
            <view class="product-waterfall">
              <view class="waterfall-column waterfall-left">
                <block wx:for="{{currentSectionProducts}}" wx:key="id" wx:for-item="product" wx:for-index="productIndex">
                  <product-card 
                    wx:if="{{productIndex % 2 === 0 && product && product.id}}"
                    product="{{product}}"
                    custom-class="waterfall"
                    card-size="small"
                    image-height="{{product.imageHeight || '240rpx'}}"
                    show-add-cart="{{true}}"
                    show-supplier="{{true}}"
                    show-tags="{{true}}"
                    enable-number-keyboard="{{true}}"
                    bind:productTap="onProductTap"
                    bind:addToCart="onAddToCart"
                    bind:removeFromCart="onRemoveFromCart"
                    bind:imageLoad="onProductImageLoad"
                    bind:imageError="onProductImageError"
                    bind:cartAnimation="onCartAnimation"
                  />
                </block>
              </view>
              
              <view class="waterfall-column waterfall-right">
                <block wx:for="{{currentSectionProducts}}" wx:key="id" wx:for-item="product" wx:for-index="productIndex">
                  <product-card 
                    wx:if="{{productIndex % 2 === 1 && product && product.id}}"
                    product="{{product}}"
                    custom-class="waterfall"
                    card-size="small"
                    image-height="{{product.imageHeight || '240rpx'}}"
                    show-add-cart="{{true}}"
                    show-supplier="{{true}}"
                    show-tags="{{true}}"
                    enable-number-keyboard="{{true}}"
                    bind:productTap="onProductTap"
                    bind:addToCart="onAddToCart"
                    bind:removeFromCart="onRemoveFromCart"
                    bind:imageLoad="onProductImageLoad"
                    bind:imageError="onProductImageError"
                    bind:cartAnimation="onCartAnimation"
                  />
                </block>
              </view>
            </view>
          </view>
          
          <!-- 空状态 -->
          <view class="empty-state" wx:elif="{{!currentSectionProducts || currentSectionProducts.length === 0}}">
            <van-icon name="shopping-cart-o" size="120rpx" color="#ddd" />
            <text class="empty-text">暂无商品</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部占位 -->
    <view class="bottom-placeholder"></view>
    
  </view>
  
</view>

<!-- 回到顶部按钮 -->
<view class="back-to-top" wx:if="{{showBackToTop}}" bindtap="scrollToTop">
  <van-icon name="back-top" size="40rpx" color="#fff" />
</view>

<!-- 悬浮客服按钮 -->
<customer-service 
  show-floating="{{true}}"
  session-from="首页"
  page-info="{{pageInfo}}"
  bind:serviceOpen="onServiceOpen"
/>

 