<template>
	<view class="delivery-container">
		<!-- 固定的任务切换标签 -->
		<view class="fixed-header">
			<uni-segmented-control :current="currentTaskTab" :values="taskTabs" @clickItem="onTaskTabChange"></uni-segmented-control>
		</view>
		
		<!-- 可滚动的内容区域 -->
		<scroll-view class="scroll-content" scroll-y="true">
			<!-- 无数据提示 -->
			<view v-if="deliveryList.length === 0" class="empty-tip">
				<uni-icons type="info" size="50" color="#ccc"></uni-icons>
				<text>暂无{{ taskTabs[currentTaskTab] }}任务</text>
			</view>
			
			<!-- 任务列表 -->
			<view v-else class="delivery-list">
				<view v-for="(item, index) in deliveryList" :key="index" class="task-card">
					<uni-card :title="'订单号: ' + (item.order?.order_no || 'undefined')" :extra="formatTime(item.created_at)">
						<view class="delivery-info">
							<view class="info-row">
								<text class="label">收货人:</text>
								<text class="value">{{ item.order?.contact_name || '' }}</text>
								<text v-if="item.order?.user?.merchant_name" class="merchant-name">({{ item.order.user.merchant_name }})</text>
								<text v-else-if="item.order?.user?.name" class="merchant-name">({{ item.order.user.name }})</text>
							</view>
							<view class="info-row">
								<text class="label">电话:</text>
								<text class="value">{{ item.order?.contact_phone || '' }}</text>
							</view>
							<view class="info-row">
								<text class="label">地址:</text>
								<text class="value address">{{ item.order?.shipping_address || '' }}</text>
							</view>
							<view class="info-row">
								<text class="label">状态:</text>
								<text class="value status" :class="item.status">{{ getStatusText(item.status) }}</text>
							</view>
							
							<!-- 商品信息表单 -->
							<view class="order-items-section" v-if="item.order?.items && item.order.items.length > 0">
								<view class="section-title" @click="toggleItemsDetail(index)">
									<text>商品清单 ({{ item.order.items.length }}种商品)</text>
									<view class="detail-btn">
										<text class="detail-text">{{ item.showItemsDetail ? '收起' : '详情' }}</text>
										<uni-icons :type="item.showItemsDetail ? 'up' : 'down'" size="16" color="#007AFF"></uni-icons>
									</view>
								</view>
								
								<!-- 商品概览（始终显示） -->
								<view class="items-summary">
									<view class="summary-item">
										<text class="summary-label">商品总数:</text>
										<text class="summary-value">{{ getTotalQuantity(item.order.items) }} 件</text>
									</view>
									<view class="summary-item">
										<text class="summary-label">订单金额:</text>
										<text class="summary-value amount">¥{{ parseFloat(item.order?.total || 0).toFixed(2) }}</text>
									</view>
									<view class="summary-item">
										<text class="summary-label">支付方式:</text>
										<text class="summary-value">{{ getPaymentMethodText(item.order?.payment_method) }}</text>
									</view>
								</view>
								
								<!-- 商品详细列表（可折叠） -->
								<view class="items-detail" v-if="item.showItemsDetail">
									<!-- 商品表头 -->
									<view class="items-header">
										<text class="header-name">商品名称</text>
										<text class="header-qty">数量</text>
										<text class="header-price">单价</text>
										<text class="header-total">小计</text>
									</view>
									
									<!-- 商品列表 -->
									<view class="items-list">
										<view class="item-row" v-for="(product, productIndex) in item.order.items" :key="product.id">
											<view class="item-name">
												<text class="product-name">{{ product.product_name }}</text>
												<text v-if="product.product_sku" class="product-sku">{{ product.product_sku }}</text>
											</view>
											<text class="item-qty">{{ product.quantity }}{{ product.unit || '件' }}</text>
											<text class="item-price">¥{{ parseFloat(product.price).toFixed(2) }}</text>
											<text class="item-total">¥{{ parseFloat(product.total).toFixed(2) }}</text>
										</view>
									</view>
								</view>
							</view>
							
							<!-- 无商品信息提示 -->
							<view class="no-items" v-else>
								<text>暂无商品信息</text>
							</view>
						</view>
						<view class="card-actions">
							<button v-if="item.status === 'pending'" class="action-btn accept" @click.stop="startDelivery(item.id)">开始配送</button>
							<button v-if="item.status === 'in_progress'" class="action-btn complete" @click.stop="completeDelivery(item.id)">完成</button>
							<button class="action-btn call" @click.stop="callCustomer(item.order?.contact_phone)">电话</button>
							<button class="action-btn navigate" @click.stop="navigate(item.order?.shipping_address)">导航</button>
						</view>
					</uni-card>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			deliveryList: [],
			userInfo: null,
			delivererInfo: null,
			timer: null,
			
			// 位置信息
			location: {
				latitude: 39.9087,
				longitude: 116.3974
			},
			currentTaskTab: 1,
			taskTabs: ['待配送', '配送中', '已完成']
		}
	},
	onLoad() {
		console.log('首页加载');
		
		// 延迟执行，确保实例已完全准备好
		setTimeout(() => {
			this.initPage();
		}, 100);
	},
	onShow() {
		// 每次页面显示时重新加载数据
		if (this.delivererInfo) {
			this.loadDeliveryTasks(false); // 静默加载，不显示loading
		}
	},
	onUnload() {
		// 页面卸载时清除定时器
		if (this.timer) {
			clearInterval(this.timer);
		}
	},
	methods: {
		initPage() {
			try {
				// 从本地获取用户和配送员信息
				const userInfoStr = uni.getStorageSync('userInfo');
				const delivererInfoStr = uni.getStorageSync('delivererInfo');
				const token = uni.getStorageSync('token');
				
				// 检查登录状态
				if (!token || (!userInfoStr && !delivererInfoStr)) {
					console.error('登录信息缺失，跳转到登录页');
					this.redirectToLogin('登录信息已过期，请重新登录');
					return;
				}
				
				if (delivererInfoStr) {
					this.delivererInfo = JSON.parse(delivererInfoStr);
				}
				
				if (userInfoStr) {
					this.userInfo = JSON.parse(userInfoStr);
				}
				
				// 将token设置到全局
				if (token && !getApp().globalData.token) {
					getApp().globalData.token = token;
					getApp().globalData.isLoggedIn = true;
					getApp().globalData.requestHeader = {
						'Authorization': 'Bearer ' + token,
						'Content-Type': 'application/json',
						'Accept': 'application/json'
					};
				}
				
				// 加载配送任务
				this.loadDeliveryTasks();
				
				// 设置定时器，每分钟更新位置和任务列表
				if (this.timer) {
					clearInterval(this.timer);
				}
				this.timer = setInterval(() => {
					this.updateLocation();
					this.loadDeliveryTasks(false); // 静默更新，不显示加载中
				}, 60000);
				
				// 初始获取位置
				this.getLocation();
			} catch (error) {
				console.error('首页加载异常', error);
				// 出现异常时，不立即跳转，先显示错误
				uni.showToast({
					title: '页面加载异常，请稍后重试',
					icon: 'none',
					duration: 3000
				});
			}
		},
		getLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					this.location.latitude = res.latitude;
					this.location.longitude = res.longitude;
					// 获取位置后立即更新到服务器
					this.updateLocation();
				},
				fail: (err) => {
					console.error('获取位置失败', err);
					uni.showToast({
						title: '获取位置失败，请开启定位权限',
						icon: 'none'
					});
				}
			});
		},
		updateLocation() {
			const token = uni.getStorageSync('token');
			const employeeInfo = uni.getStorageSync('employeeInfo');
			
			if (!token || !employeeInfo) return;
			
			let employee;
			try {
				employee = typeof employeeInfo === 'string' ? JSON.parse(employeeInfo) : employeeInfo;
			} catch (e) {
				console.error('解析员工信息失败', e);
				return;
			}
			
			if (!employee || !employee.id) {
				console.error('员工信息不完整');
				return;
			}
			
			uni.request({
				url: getApp().globalData.BASE_API + '/api/deliverers/update-location',
				method: 'POST',
				header: {
					'Authorization': 'Bearer ' + token,
					'Content-Type': 'application/json',
					'Accept': 'application/json'
				},
				data: {
					employee_id: employee.id,
					latitude: this.location.latitude,
					longitude: this.location.longitude
				},
				success: (res) => {
					if (res.data && res.data.code === 200) {
						console.log('位置更新成功');
					}
				},
				fail: (err) => {
					console.error('更新位置失败', err);
				}
			});
		},
		loadDeliveryTasks(showLoading = true) {
			if (showLoading) {
				uni.showLoading({
					title: '加载中...'
				});
			}
			
			const token = getApp().globalData.token || uni.getStorageSync('token');
			if (!token) {
				if (showLoading) uni.hideLoading();
				this.redirectToLogin('登录状态已失效，请重新登录');
				return;
			}
			
			// 根据当前选中的标签页确定要获取的状态
			let status = '';
			switch (this.currentTaskTab) {
				case 0:
					status = 'pending';
					break;
				case 1:
					status = 'in_progress';
					break;
				case 2:
					status = 'completed';
					break;
			}
			
			uni.request({
				url: getApp().globalData.BASE_API + '/api/deliveries',
				method: 'GET',
				header: {
					'Authorization': 'Bearer ' + token,
					'Accept': 'application/json'
				},
				data: {
					status: status
				},
				success: (res) => {
					// 检查响应状态和内容
					if (res.statusCode === 200 && res.data) {
						// 处理两种可能的响应格式
						if (res.data.code === 200 || res.data.success) {
							console.log('获取任务成功', res.data);
							// 根据返回的数据结构获取任务列表
							let taskData = null;
							if (res.data.data) {
								// 处理 {code:200, message:"Success", data:{data:[]...}} 结构
								taskData = res.data.data;
							} else if (res.data.success) {
								// 处理 {success:true, data:[]} 结构
								taskData = res.data.data;
							}
							
							// 处理分页数据结构
							this.deliveryList = (taskData && taskData.data) ? taskData.data : (taskData || []);
							
							// 为每个配送任务初始化折叠状态
							this.deliveryList.forEach(item => {
								if (!item.hasOwnProperty('showItemsDetail')) {
									item.showItemsDetail = false; // 默认折叠
								}
							});
							
							console.log('处理后的任务列表:', this.deliveryList);
							
							// 添加详细的数据结构调试
							if (this.deliveryList.length > 0) {
								console.log('第一个任务的数据结构:', this.deliveryList[0]);
								console.log('第一个任务的order数据:', this.deliveryList[0].order);
								if (this.deliveryList[0].order) {
									console.log('订单号:', this.deliveryList[0].order.order_no);
									console.log('收货人:', this.deliveryList[0].order.contact_name);
									console.log('电话:', this.deliveryList[0].order.contact_phone);
									console.log('地址:', this.deliveryList[0].order.shipping_address);
									console.log('用户信息:', this.deliveryList[0].order.user);
									if (this.deliveryList[0].order.user) {
										console.log('用户名:', this.deliveryList[0].order.user.name);
										console.log('商家名称:', this.deliveryList[0].order.user.merchant_name);
									}
								}
							}
						} 
						else if (res.statusCode === 401) {
							// 401未授权，需要重新登录
							console.error('获取任务失败 - 授权失效', res);
							this.redirectToLogin('登录状态已过期，请重新登录');
						}
						else {
							// 其他错误
							console.error('获取任务失败', res);
							this.deliveryList = [];
							
							// 只有在主动加载时显示错误提示
							if (showLoading) {
								uni.showToast({
									title: (res.data && res.data.message) || '获取任务失败',
									icon: 'none',
									duration: 2000
								});
							}
						}
					} else {
						console.error('响应状态码或数据无效', res);
						this.deliveryList = [];
						
						if (showLoading) {
							uni.showToast({
								title: '获取数据失败',
								icon: 'none',
								duration: 2000
							});
						}
					}
				},
				fail: (err) => {
					console.error('请求获取任务失败', err);
					
					// 只有在主动加载时显示错误提示
					if (showLoading) {
						uni.showToast({
							title: '网络错误，请稍后重试',
							icon: 'none',
							duration: 2000
						});
					}
				},
				complete: () => {
					if (showLoading) uni.hideLoading();
				}
			});
		},
		formatTime(time) {
			if (!time) return '';
			const date = new Date(time);
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
		},
		// 开始配送任务
		startDelivery(id) {
			uni.showLoading({
				title: '处理中...'
			});
			
			const token = uni.getStorageSync('token');
			
			uni.request({
				url: `${getApp().globalData.BASE_API}/api/deliveries/${id}/status`,
				method: 'PUT',
				header: {
					'Authorization': 'Bearer ' + token,
					'Accept': 'application/json'
				},
				data: {
					status: 'in_progress'
				},
				success: (res) => {
					if ((res.data && res.data.success) || (res.data && res.data.code === 200)) {
						uni.showToast({
							title: '已开始配送',
							icon: 'success'
						});
						// 重新加载任务列表
						this.loadDeliveryTasks();
					} else {
						uni.showToast({
							title: res.data?.message || '操作失败',
							icon: 'none'
						});
					}
				},
				fail: (err) => {
					console.error('开始配送失败', err);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				},
				complete: () => {
					uni.hideLoading();
				}
			});
		},
		completeDelivery(id) {
			uni.showLoading({
				title: '操作中...'
			});
			
			const token = uni.getStorageSync('token');
			
			uni.request({
				url: `${getApp().globalData.BASE_API}/api/deliveries/${id}/status`,
				method: 'PUT',
				header: {
					'Authorization': 'Bearer ' + token,
					'Accept': 'application/json'
				},
				data: {
					status: 'completed'
				},
				success: (res) => {
					if ((res.data && res.data.success) || (res.data && res.data.code === 200)) {
						uni.showToast({
							title: '配送已完成',
							icon: 'success'
						});
						// 重新加载任务列表
						this.loadDeliveryTasks();
					} else {
						uni.showToast({
							title: res.data?.message || '操作失败',
							icon: 'none'
						});
					}
				},
				fail: (err) => {
					console.error('完成配送失败', err);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				},
				complete: () => {
					uni.hideLoading();
				}
			});
		},
		callCustomer(phone) {
			if (!phone) {
				uni.showToast({
					title: '电话号码无效',
					icon: 'none'
				});
				return;
			}
			
			// 验证电话号码格式
			const phoneRegex = /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$|^400-?\d{3}-?\d{4}$/;
			if (!phoneRegex.test(phone.replace(/\s+/g, ''))) {
				uni.showModal({
					title: '电话号码格式提示',
					content: `电话号码：${phone}\n格式可能不正确，是否继续拨打？`,
					success: (res) => {
						if (res.confirm) {
							this.makeCall(phone);
						}
					}
				});
				return;
			}
			
			this.makeCall(phone);
		},
		
		makeCall(phone) {
			console.log('准备拨打电话:', phone);
			
			uni.makePhoneCall({
				phoneNumber: phone,
				success: () => {
					console.log('电话拨打成功');
				},
				fail: (err) => {
					console.error('拨打电话失败:', err);
					uni.showModal({
						title: '拨打电话失败',
						content: `无法拨打电话：${phone}\n请检查设备是否支持通话功能或手动拨打`,
						showCancel: true,
						cancelText: '取消',
						confirmText: '复制号码',
						success: (res) => {
							if (res.confirm) {
								// 复制电话号码到剪贴板
								uni.setClipboardData({
									data: phone,
									success: () => {
										uni.showToast({
											title: '电话号码已复制',
											icon: 'success'
										});
									}
								});
							}
						}
					});
				}
			});
		},
		navigate(address) {
			if (!address) {
				uni.showToast({
					title: '地址无效',
					icon: 'none'
				});
				return;
			}
			
			// 显示导航选择弹窗
			uni.showActionSheet({
				itemList: ['高德地图', '百度地图', '腾讯地图', '苹果地图(iOS)', '谷歌地图'],
				success: (res) => {
					const index = res.tapIndex;
					this.openNavigation(address, index);
				},
				fail: () => {
					// 用户取消选择，直接使用默认导航
					this.openNavigation(address, 0);
				}
			});
		},
		openNavigation(address, navType = 0) {
			// 获取当前位置作为起点
			const startLat = this.location.latitude;
			const startLng = this.location.longitude;
			
			// 编码地址用于URL
			const encodedAddress = encodeURIComponent(address);
			
			let navUrl = '';
			let appName = '';
			
			switch (navType) {
				case 0: // 高德地图
					navUrl = `amapuri://route/plan/?sid=BGVIS1&slat=${startLat}&slon=${startLng}&sname=当前位置&did=BGVIS2&dlat=&dlon=&dname=${encodedAddress}&dev=0&t=0`;
					appName = '高德地图';
					break;
					
				case 1: // 百度地图
					navUrl = `baidumap://map/direction?origin=${startLat},${startLng}&destination=${encodedAddress}&mode=driving&src=配送助手`;
					appName = '百度地图';
					break;
					
				case 2: // 腾讯地图
					navUrl = `qqmap://map/routeplan?type=drive&from=当前位置&fromcoord=${startLat},${startLng}&to=${encodedAddress}&tocoord=&referer=配送助手`;
					appName = '腾讯地图';
					break;
					
				case 3: // 苹果地图 (iOS)
					navUrl = `http://maps.apple.com/?saddr=${startLat},${startLng}&daddr=${encodedAddress}&dirflg=d`;
					appName = '苹果地图';
					break;
					
				case 4: // 谷歌地图
					navUrl = `comgooglemaps://?saddr=${startLat},${startLng}&daddr=${encodedAddress}&directionsmode=driving`;
					appName = '谷歌地图';
					break;
			}
			
			console.log(`尝试打开${appName}:`, navUrl);
			
			// APP环境下直接调用本机导航应用
			plus.runtime.openURL(navUrl, (error) => {
				console.error(`打开${appName}失败:`, error);
				
				// 如果打开失败，尝试使用网页版作为备用方案
				this.openWebNavigation(address, navType, appName);
			});
		},
		openWebNavigation(address, navType, appName) {
			const encodedAddress = encodeURIComponent(address);
			const startLat = this.location.latitude;
			const startLng = this.location.longitude;
			
			let webUrl = '';
			
			switch (navType) {
				case 0: // 高德地图网页版
					webUrl = `https://uri.amap.com/navigation?from=${startLng},${startLat},当前位置&to=,${encodedAddress}&mode=car&policy=1&src=配送助手&coordinate=gaode&callnative=1`;
					break;
					
				case 1: // 百度地图网页版
					webUrl = `https://api.map.baidu.com/direction?origin=${startLat},${startLng}&destination=${encodedAddress}&mode=driving&region=全国&output=html&src=配送助手`;
					break;
					
				case 2: // 腾讯地图网页版
					webUrl = `https://apis.map.qq.com/uri/v1/routeplan?type=drive&from=当前位置&fromcoord=${startLat},${startLng}&to=${encodedAddress}&referer=配送助手`;
					break;
					
				case 3: // 苹果地图网页版
					webUrl = `https://maps.apple.com/?saddr=${startLat},${startLng}&daddr=${encodedAddress}&dirflg=d`;
					break;
					
				case 4: // 谷歌地图网页版
					webUrl = `https://www.google.com/maps/dir/${startLat},${startLng}/${encodedAddress}`;
					break;
			}
			
			console.log(`${appName}应用未安装，尝试打开网页版:`, webUrl);
			
			// 在APP中打开网页版导航
			plus.runtime.openURL(webUrl, (error) => {
				console.error(`打开${appName}网页版失败:`, error);
				
				// 最后的备用方案：使用内置地图
				this.fallbackNavigation(address, appName);
			});
		},
		fallbackNavigation(address, appName) {
			console.log(`所有导航方案失败，使用内置地图`);
			
			// 备用方案：使用uni-app内置地图
			uni.openLocation({
				latitude: this.location.latitude,
				longitude: this.location.longitude,
				name: address,
				address: address,
				scale: 15,
				success: () => {
					console.log('打开内置地图成功');
					uni.showToast({
						title: '已打开内置地图',
						icon: 'success'
					});
				},
				fail: (err) => {
					console.error('打开内置地图失败:', err);
					uni.showModal({
						title: '导航提示',
						content: `无法自动打开导航，请手动使用${appName || '地图应用'}导航到：\n${address}`,
						showCancel: false,
						confirmText: '知道了'
					});
				}
			});
		},
		redirectToLogin(errorMsg) {
			try {
				// 清理定时器
				if (this.timer) {
					clearInterval(this.timer);
					this.timer = null;
				}
				
				// 清除登录状态
				getApp().globalData.isLoggedIn = false;
				getApp().globalData.token = null;
				uni.removeStorageSync('isLoggedIn');
				
				// 跳转到登录页，并传递错误信息
				uni.reLaunch({
					url: '/pages/login/login',
					success: (res) => {
						// 传递错误信息给登录页
						if (res.eventChannel) {
							res.eventChannel.emit('loginError', {
								error: errorMsg || '登录已过期，请重新登录'
							});
						}
					}
				});
			} catch (e) {
				console.error('跳转到登录页出错', e);
				// 如果传递参数有问题，直接跳转
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}
		},
		toggleItemsDetail(index) {
			this.deliveryList[index].showItemsDetail = !this.deliveryList[index].showItemsDetail;
		},
		getPaymentMethodText(method) {
			switch (method) {
				case 'wechat':
					return '微信支付';
				case 'alipay':
					return '支付宝支付';
				case 'cash':
					return '现金支付';
				case 'card':
					return '银行卡支付';
				default:
					return '未知支付方式';
			}
		},
		getTotalQuantity(items) {
			if (!items || !Array.isArray(items)) return 0;
			return items.reduce((total, item) => total + parseInt(item.quantity || 0), 0);
		},
		onTaskTabChange(e) {
			this.currentTaskTab = e.currentIndex;
			this.loadDeliveryTasks();
		},
		getStatusText(status) {
			switch (status) {
				case 'pending':
					return '待配送';
				case 'in_progress':
					return '配送中';
				case 'completed':
					return '已完成';
				default:
					return '未知状态';
			}
		}
	}
}
</script>

<style lang="scss">
.delivery-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background-color: #fff;
	border-bottom: 1rpx solid #e0e0e0;
	padding: 15rpx 0;
	/* 添加安全区域适配 */
	padding-top: calc(15rpx + env(safe-area-inset-top));
}

.scroll-content {
	flex: 1;
	/* 动态计算顶部间距，包含安全区域 */
	margin-top: calc(80rpx + env(safe-area-inset-top));
	height: calc(100vh - 80rpx - env(safe-area-inset-top));
}

.empty-tip {
	margin-top: 100rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	color: #999;
	
	text {
		margin-top: 20rpx;
		font-size: 30rpx;
	}
}

.delivery-list {
	padding: 10rpx;
}

.task-card {
	margin-bottom: 10rpx;
}

.delivery-info {
	.info-row {
		display: flex;
		margin-bottom: 10rpx;
		font-size: 26rpx;
		
		.label {
			width: 100rpx;
			color: #666;
		}
		
		.value {
			flex: 1;
			color: #333;
			
			&.address {
				word-break: break-all;
			}
			
			&.status {
				&.pending {
					color: #ff9800;
				}
				&.in_progress {
					color: #2196f3;
				}
				&.completed {
					color: #4caf50;
				}
			}
		}
		
		.merchant-name {
			color: #666;
			font-size: 22rpx;
			margin-left: 8rpx;
			font-style: italic;
		}
	}
}

.card-actions {
	display: flex;
	justify-content: flex-end;
	margin-top: 15rpx;
	
	.action-btn {
		margin-left: 15rpx;
		font-size: 22rpx;
		padding: 8rpx 16rpx;
		border-radius: 6rpx;
		border: none;
		
		&.accept {
			background-color: #4caf50;
			color: white;
		}
		
		&.complete {
			background-color: #ff9800;
			color: white;
		}
		
		&.call {
			background-color: #2196f3;
			color: white;
		}
		
		&.navigate {
			background-color: #9c27b0;
			color: white;
		}
	}
}

// 商品信息表单样式
.order-items-section {
	margin-top: 15rpx;
	border-top: 1rpx solid #e0e0e0;
	padding-top: 10rpx;
	
	.section-title {
		margin-bottom: 8rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		cursor: pointer;
		
		text {
			font-size: 24rpx;
			color: #333;
			font-weight: bold;
		}
		
		.detail-btn {
			display: flex;
			align-items: center;
			padding: 4rpx 8rpx;
			background-color: #f0f8ff;
			border-radius: 12rpx;
			border: 1rpx solid #007AFF;
			
			.detail-text {
				font-size: 20rpx;
				color: #007AFF;
				margin-right: 4rpx;
			}
		}
	}
	
	.items-summary {
		margin-bottom: 8rpx;
		
		.summary-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 4rpx;
			
			.summary-label {
				font-size: 22rpx;
				color: #666;
			}
			
			.summary-value {
				font-size: 22rpx;
				color: #333;
				font-weight: bold;
				
				&.amount {
					color: #ff5722;
					font-size: 24rpx;
				}
			}
		}
	}
	
	.items-detail {
		margin-top: 8rpx;
		border-top: 1rpx solid #e0e0e0;
		padding-top: 8rpx;
		
		.items-header {
			display: flex;
			background-color: #f8f9fa;
			padding: 6rpx 8rpx;
			border-radius: 4rpx;
			margin-bottom: 6rpx;
			
			.header-name {
				flex: 2;
				font-size: 22rpx;
				color: #666;
				font-weight: bold;
			}
			
			.header-qty {
				flex: 0.8;
				text-align: center;
				font-size: 22rpx;
				color: #666;
				font-weight: bold;
			}
			
			.header-price {
				flex: 1;
				text-align: center;
				font-size: 22rpx;
				color: #666;
				font-weight: bold;
			}
			
			.header-total {
				flex: 1;
				text-align: right;
				font-size: 22rpx;
				color: #666;
				font-weight: bold;
			}
		}
		
		.items-list {
			.item-row {
				display: flex;
				align-items: center;
				padding: 6rpx 8rpx;
				border-bottom: 1rpx solid #f0f0f0;
				
				&:last-child {
					border-bottom: none;
				}
				
				.item-name {
					flex: 2;
					display: flex;
					flex-direction: column;
					
					.product-name {
						font-size: 22rpx;
						color: #333;
						line-height: 1.2;
					}
					
					.product-sku {
						font-size: 18rpx;
						color: #999;
						margin-top: 2rpx;
					}
				}
				
				.item-qty {
					flex: 0.8;
					text-align: center;
					font-size: 22rpx;
					color: #333;
				}
				
				.item-price {
					flex: 1;
					text-align: center;
					font-size: 22rpx;
					color: #666;
				}
				
				.item-total {
					flex: 1;
					text-align: right;
					font-size: 22rpx;
					color: #ff5722;
					font-weight: bold;
				}
			}
		}
	}
}

.no-items {
	margin-top: 10rpx;
	padding: 15rpx;
	text-align: center;
	background-color: #f8f9fa;
	border-radius: 4rpx;
	
	text {
		font-size: 22rpx;
		color: #999;
	}
}
</style>
