<template>
	<view class="profile-container">
		<!-- 用户信息卡片 -->
		<view class="user-card">
			<view class="avatar">
				<text class="avatar-text">{{ avatarText }}</text>
			</view>
			<view class="user-info">
				<text class="name">{{ userName }}</text>
				<text class="phone">{{ userPhone }}</text>
			</view>
			<view class="status-indicator" :class="delivererStatus">
				<text>{{ getStatusText(delivererStatus) }}</text>
			</view>
		</view>
		
		<!-- 基本信息 -->
		<uni-section title="基本信息" type="line">
			<uni-list>
				<uni-list-item title="工号" :rightText="delivererInfo?.id || '--'" />
				<uni-list-item title="配送区域" :rightText="delivererInfo?.delivery_area || '--'" />
				<uni-list-item title="运输工具" :rightText="delivererInfo?.transportation || '--'" />
				<uni-list-item title="工作状态" :rightText="getStatusText(delivererStatus)" />
			</uni-list>
		</uni-section>
		
		<!-- 系统功能 -->
		<uni-section title="系统功能" type="line">
			<uni-list>
				<uni-list-item title="关于系统" showArrow @click="showAbout" />
			</uni-list>
		</uni-section>
		
		<!-- 退出登录按钮 -->
		<button class="logout-btn" @click="logout">退出登录</button>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo: null,
			delivererInfo: null,
			delivererStatus: 'offline'
		}
	},
	computed: {
		userName() {
			if (this.userInfo?.name) {
				return this.userInfo.name;
			}
			if (this.delivererInfo?.name) {
				return this.delivererInfo.name;
			}
			return '配送员';
		},
		userPhone() {
			if (this.userInfo?.phone) {
				return this.userInfo.phone;
			}
			if (this.delivererInfo?.phone) {
				return this.delivererInfo.phone;
			}
			return '未设置联系方式';
		},
		avatarText() {
			if (this.userName) {
				return this.userName.charAt(0);
			}
			return '配送员';
		}
	},
	onLoad() {
		this.loadUserInfo();
	},
	onShow() {
		this.loadUserInfo();
	},
	methods: {
		loadUserInfo() {
			try {
				const userInfoStr = uni.getStorageSync('userInfo');
				const delivererInfoStr = uni.getStorageSync('delivererInfo');
				const employeeInfoStr = uni.getStorageSync('employeeInfo');
				
				if (userInfoStr) {
					this.userInfo = JSON.parse(userInfoStr);
				}
				
				if (delivererInfoStr) {
					this.delivererInfo = JSON.parse(delivererInfoStr);
					this.delivererStatus = this.delivererInfo.status || 'available';
				}
				
				// 如果没有配送员信息但有员工信息，使用员工信息
				if (!this.delivererInfo && employeeInfoStr) {
					const employeeInfo = JSON.parse(employeeInfoStr);
					this.delivererInfo = {
						id: employeeInfo.id,
						name: employeeInfo.name,
						phone: employeeInfo.phone,
						delivery_area: '未设置',
						transportation: '未设置',
						status: 'available'
					};
					this.delivererStatus = 'available';
				}
				
			} catch (error) {
				console.error('读取用户信息失败:', error);
			}
		},
		
		getStatusText(status) {
			switch (status) {
				case 'available':
					return '空闲中';
				case 'busy':
					return '配送中';
				case 'offline':
					return '休息中';
				default:
					return '未知状态';
			}
		},
		
		showAbout() {
			uni.showModal({
				title: '关于系统',
				content: '配送员APP v1.0.0\n为配送员提供便捷的配送管理工具',
				showCancel: false
			});
		},
		
		logout() {
			uni.showModal({
				title: '退出登录',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						// 检查是否有自动登录设置
						const loginConfig = uni.getStorageSync('delivererLoginConfig');
						let hasAutoLogin = false;
						
						if (loginConfig) {
							try {
								const config = JSON.parse(loginConfig);
								hasAutoLogin = config.autoLogin === true;
							} catch (e) {
								console.error('解析登录配置失败:', e);
							}
						}
						
						if (hasAutoLogin) {
							// 如果有自动登录设置，询问是否清除
							uni.showModal({
								title: '自动登录设置',
								content: '检测到您开启了自动登录，是否同时清除自动登录设置？',
								confirmText: '清除',
								cancelText: '保留',
								success: (modalRes) => {
									this.performLogout(modalRes.confirm);
								}
							});
						} else {
							// 没有自动登录设置，直接退出
							this.performLogout(false);
						}
					}
				}
			});
		},
		
		performLogout(clearAutoLogin) {
			// 清除所有登录相关信息
			uni.removeStorageSync('token');
			uni.removeStorageSync('userInfo');
			uni.removeStorageSync('delivererInfo');
			uni.removeStorageSync('employeeInfo');
			uni.removeStorageSync('isLoggedIn');
			
			// 根据用户选择决定是否清除登录配置
			if (clearAutoLogin) {
				uni.removeStorageSync('delivererLoginConfig');
				console.log('已清除自动登录设置');
			} else {
				// 保留登录配置但清除密码
				const loginConfig = uni.getStorageSync('delivererLoginConfig');
				if (loginConfig) {
					try {
						const config = JSON.parse(loginConfig);
						// 保留记住账号和自动登录设置，但清除密码
						config.password = '';
						uni.setStorageSync('delivererLoginConfig', JSON.stringify(config));
						console.log('已保留登录设置但清除密码');
					} catch (e) {
						console.error('更新登录配置失败:', e);
					}
				}
			}
			
			// 清除全局状态
			getApp().globalData.isLoggedIn = false;
			getApp().globalData.token = '';
			getApp().globalData.delivererId = null;
			
			// 显示退出成功提示
			uni.showToast({
				title: '已退出登录',
				icon: 'success',
				duration: 1500
			});
			
			// 跳转到登录页
			setTimeout(() => {
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}, 1500);
		}
	}
}
</script>

<style lang="scss">
.profile-container {
	padding-bottom: 40rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.user-card {
	display: flex;
	align-items: center;
	padding: 40rpx 30rpx;
	background-color: #007AFF;
	position: relative;
	
	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		overflow: hidden;
		margin-right: 30rpx;
		border: 4rpx solid rgba(255, 255, 255, 0.3);
		background-color: rgba(255, 255, 255, 0.2);
		display: flex;
		align-items: center;
		justify-content: center;
		
		.avatar-text {
			font-size: 48rpx;
			font-weight: bold;
			color: white;
		}
	}
	
	.user-info {
		color: #ffffff;
		flex: 1;
		
		.name {
			font-size: 36rpx;
			font-weight: bold;
			margin-bottom: 10rpx;
			display: block;
		}
		
		.phone {
			font-size: 28rpx;
			opacity: 0.8;
			display: block;
		}
	}
	
	.status-indicator {
		padding: 8rpx 20rpx;
		border-radius: 30rpx;
		font-size: 24rpx;
		
		&.available {
			background-color: #4caf50;
			color: white;
		}
		
		&.busy {
			background-color: #ff9800;
			color: white;
		}
		
		&.offline {
			background-color: rgba(255, 255, 255, 0.3);
			color: white;
		}
	}
}

.logout-btn {
	margin: 60rpx 30rpx 30rpx;
	background-color: #f44336;
	color: white;
	border: none;
	border-radius: 8rpx;
	font-size: 32rpx;
	padding: 20rpx 0;
}
</style> 