<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Upload\Services\UploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class UploadExampleController extends Controller
{
    /**
     * 通用图片上传示例
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImage(Request $request)
    {
        try {
            // 验证请求
            $request->validate([
                'image' => 'required|file|image|max:5120', // 5MB限制
                'type' => 'required|string|in:banner,product,category,avatar', // 支持的类型
            ]);
            
            // 创建上传服务（会自动使用配置中的存储驱动）
            $uploadService = UploadService::createFromConfig();
            
            // 获取上传文件和类型
            $uploadedFile = $request->file('image');
            $type = $request->input('type');
            
            // 上传图片（通用方法）
            $result = $uploadService->uploadImage($uploadedFile, $type);
            
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => '图片上传失败'
                ], 500);
            }
            
            // 返回上传结果
            return response()->json([
                'success' => true,
                'data' => [
                    'url' => $result['url'],
                    'path' => $result['path'],
                    'driver' => $result['driver'],
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('上传图片异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '上传图片过程中发生错误: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 轮播图上传示例
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadBanner(Request $request)
    {
        try {
            // 验证请求
            $request->validate([
                'image' => 'required|file|image|max:5120',
            ]);
            
            // 创建上传服务实例
            $uploadService = UploadService::createFromConfig();
            
            // 使用专用方法上传轮播图
            $result = $uploadService->uploadBannerImage($request->file('image'));
            
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => '轮播图上传失败'
                ], 500);
            }
            
            // 保存轮播图信息到数据库（示例）
            // $banner = new Banner();
            // $banner->image_url = $result['url'];
            // $banner->save();
            
            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('上传轮播图异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '上传轮播图过程中发生错误: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 商品图片上传（指定存储驱动）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadProductWithCos(Request $request)
    {
        try {
            // 验证请求
            $request->validate([
                'image' => 'required|file|image|max:5120',
                'product_id' => 'required|integer',
            ]);
            
            $productId = $request->input('product_id');
            
            // 直接使用COS存储
            $uploadService = UploadService::cos();
            
            // 配置自定义选项
            $options = [
                'name_prefix' => 'product_' . $productId . '_', // 添加前缀
                'subtype' => 'main', // 子类型（用于替换{type}占位符）
            ];
            
            // 上传商品图片
            $result = $uploadService->uploadProductImage($request->file('image'), $options);
            
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => '商品图片上传失败'
                ], 500);
            }
            
            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('上传商品图片异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '上传商品图片过程中发生错误: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 商品多图片上传
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadProductImages(Request $request)
    {
        try {
            // 记录请求信息
            \Illuminate\Support\Facades\Log::info('商品多图片上传请求', [
                'has_product_id' => $request->has('product_id'),
                'product_id' => $request->input('product_id'),
                'images_count' => $request->hasFile('images') ? count($request->file('images')) : 0,
                'all_data' => $request->all()
            ]);
            
            // 验证请求
            $request->validate([
                'images.*' => 'required|file|image|max:5120', // 每张图片5MB限制
                'images' => 'required|array|max:5', // 最多5张图片
                'product_id' => 'nullable|integer', // 可选关联的产品ID
            ]);
            
            // 创建上传服务
            $uploadService = UploadService::createFromConfig();
            
            $results = [];
            $failureCount = 0;
            
            // 处理每张图片
            foreach ($request->file('images') as $index => $imageFile) {
                // 配置自定义选项
                $options = [
                    'name_prefix' => 'product_' . ($request->input('product_id') ?? 'new') . '_' . ($index + 1) . '_',
                    'subtype' => 'gallery', // 子类型
                ];
                
                // 上传商品图片
                $result = $uploadService->uploadProductImage($imageFile, $options);
                
                if ($result) {
                    $uploadResult = [
                        'url' => $result['url'],
                        'path' => $result['path'],
                        'driver' => $result['driver'],
                        'original_name' => $imageFile->getClientOriginalName(),
                        'size' => $imageFile->getSize(),
                        'mime_type' => $imageFile->getMimeType(),
                        'is_success' => true,
                        'sort' => $index,
                        'is_main' => ($index === 0), // 第一张图片默认为主图
                    ];
                    
                    $results[] = $uploadResult;
                    
                    // 如果提供了product_id，则直接保存到数据库
                    if ($request->input('product_id')) {
                        try {
                            $productId = $request->input('product_id');
                            \Illuminate\Support\Facades\Log::info('尝试保存图片到数据库', [
                                'product_id' => $productId,
                                'image_url' => $uploadResult['url']
                            ]);
                            
                            $productImage = \App\Product\Models\ProductImage::createOrUpdateFromUpload(
                                $productId,
                                $uploadResult
                            );
                            
                            \Illuminate\Support\Facades\Log::info('图片保存成功', [
                                'product_id' => $productId,
                                'image_id' => $productImage->id
                            ]);
                        } catch (\Exception $e) {
                            \Illuminate\Support\Facades\Log::error('保存商品图片到数据库失败', [
                                'error' => $e->getMessage(),
                                'trace' => $e->getTraceAsString(),
                                'product_id' => $productId,
                                'image' => $uploadResult
                            ]);
                        }
                    } else {
                        \Illuminate\Support\Facades\Log::warning('上传图片时没有提供product_id，跳过保存到数据库');
                    }
                } else {
                    $failureCount++;
                    $results[] = [
                        'original_name' => $imageFile->getClientOriginalName(),
                        'error' => '上传失败',
                        'is_success' => false,
                    ];
                }
            }
            
            if ($failureCount === count($request->file('images'))) {
                return response()->json([
                    'success' => false,
                    'message' => '所有图片上传失败',
                    'data' => $results
                ], 500);
            }
            
            return response()->json([
                'success' => true,
                'message' => $failureCount > 0 ? '部分图片上传成功' : '所有图片上传成功',
                'data' => [
                    'images' => $results,
                    'product_id' => $request->input('product_id'),
                ]
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('上传商品图片异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '上传商品图片过程中发生错误: ' . $e->getMessage()
            ], 500);
        }
    }
} 