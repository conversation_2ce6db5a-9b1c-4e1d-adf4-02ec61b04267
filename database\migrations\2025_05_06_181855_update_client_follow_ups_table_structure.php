<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('client_follow_ups', function (Blueprint $table) {
            // 先删除外键约束
            $table->dropForeign(['agent_id']);
            
            // 重命名列
            $table->renameColumn('agent_id', 'employee_id');
        });
        
        // 重新添加外键约束
        Schema::table('client_follow_ups', function (Blueprint $table) {
            $table->foreign('employee_id')
                  ->references('id')
                  ->on('employees');
        });
        
        // 更新注释
        DB::statement('ALTER TABLE `client_follow_ups` CHANGE `employee_id` `employee_id` BIGINT UNSIGNED NOT NULL COMMENT "CRM专员ID(员工)"');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('client_follow_ups', function (Blueprint $table) {
            // 先删除外键约束
            $table->dropForeign(['employee_id']);
            
            // 重命名列
            $table->renameColumn('employee_id', 'agent_id');
        });
        
        // 重新添加外键约束
        Schema::table('client_follow_ups', function (Blueprint $table) {
            $table->foreign('agent_id')
                  ->references('id')
                  ->on('users');
        });
        
        // 更新注释
        DB::statement('ALTER TABLE `client_follow_ups` CHANGE `agent_id` `agent_id` BIGINT UNSIGNED NOT NULL COMMENT "CRM专员ID"');
    }
};
