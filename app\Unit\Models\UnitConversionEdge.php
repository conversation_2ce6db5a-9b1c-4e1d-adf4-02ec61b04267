<?php

namespace App\Unit\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UnitConversionEdge extends Model
{
    use HasFactory;

    protected $fillable = [
        'graph_id', 'from_unit_id', 'to_unit_id',
        'conversion_factor', 'is_bidirectional'
    ];

    protected $casts = [
        'conversion_factor' => 'decimal:10',
        'is_bidirectional' => 'boolean',
    ];

    /**
     * 所属图
     */
    public function graph(): BelongsTo
    {
        return $this->belongsTo(UnitConversionGraph::class, 'graph_id');
    }

    /**
     * 源单位
     */
    public function fromUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'from_unit_id');
    }

    /**
     * 目标单位
     */
    public function toUnit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'to_unit_id');
    }

    /**
     * 创建双向边
     *
     * @param int $graphId 转换图ID
     * @param int $fromUnitId 源单位ID
     * @param int $toUnitId 目标单位ID
     * @param float $conversionFactor 转换系数
     * @return array 创建的两条边
     */
    public static function createBidirectional(
        int $graphId,
        int $fromUnitId,
        int $toUnitId,
        float $conversionFactor
    ): array {
        $forward = self::create([
            'graph_id' => $graphId,
            'from_unit_id' => $fromUnitId,
            'to_unit_id' => $toUnitId,
            'conversion_factor' => $conversionFactor,
            'is_bidirectional' => true
        ]);
        
        $reverse = self::create([
            'graph_id' => $graphId,
            'from_unit_id' => $toUnitId,
            'to_unit_id' => $fromUnitId,
            'conversion_factor' => 1 / $conversionFactor,
            'is_bidirectional' => true
        ]);
        
        return [$forward, $reverse];
    }
} 