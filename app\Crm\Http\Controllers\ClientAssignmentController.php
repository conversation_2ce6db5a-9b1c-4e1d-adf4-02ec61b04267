<?php

namespace App\Crm\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Crm\Models\CrmAgent;
use App\Models\User;
use App\Employee\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;


class ClientAssignmentController extends Controller
{
    /**
     * 分配客户给CRM专员
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function assign(Request $request)
    {
        // 验证权限 - 暂时禁用权限检查
        /*
        $currentUser = $request->user();
        if (!$currentUser->employee || !in_array($currentUser->employee->role, ['admin', 'manager'])) {
            return response()->json(ApiResponse::error('没有权限分配客户', 403), 403);
        }
        */
        
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'agent_id' => 'required|exists:employees,id',
            'notes' => 'nullable|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        // 添加日志
        Log::info('分配客户给CRM专员', [
            'user_id' => $request->user_id,
            'agent_id' => $request->agent_id
        ]);
        
        try {
            // 查找客户
            $user = User::findOrFail($request->user_id);
            
            // 查找CRM专员（员工）
            $employee = Employee::findOrFail($request->agent_id);
            if (!$employee->isCrmAgent()) {
                Log::error('指定员工不是CRM专员', [
                    'employee_id' => $employee->id,
                    'role' => $employee->role
                ]);
                return response()->json(ApiResponse::error('指定员工不是CRM专员', 400), 400);
            }
            
            // 检查CRM专员是否可以接受新客户
            $crmAgent = $employee->crmAgentInfo;
            if ($crmAgent && method_exists($crmAgent, 'canAcceptClients') && !$crmAgent->canAcceptClients()) {
                Log::error('CRM专员当前无法接受新客户', [
                    'agent_id' => $employee->id,
                    'clients_count' => $crmAgent->clients_count,
                    'max_clients' => $crmAgent->max_clients
                ]);
                return response()->json(ApiResponse::error('该CRM专员当前无法接受新客户', 400), 400);
            }
            
            // 直接更新用户的crm_agent_id字段
            $user->crm_agent_id = $employee->id;
            $user->save();
            
            // 如果需要记录notes，可以将其存储在其他表中
            // 比如client_follow_ups表或自定义表
            if ($request->notes && !empty($request->notes)) {
                // 这里可以根据实际需求处理notes
                Log::info('客户分配附带备注', [
                    'user_id' => $user->id,
                    'agent_id' => $employee->id,
                    'notes' => $request->notes
                ]);
            }
            
            // 更新CRM专员的客户数量统计（如果需要）
            if ($crmAgent && method_exists($crmAgent, 'updateClientsCount')) {
                $crmAgent->updateClientsCount();
            }
            
            Log::info('成功分配客户给CRM专员', [
                'user_id' => $user->id,
                'agent_id' => $employee->id
            ]);
            
            return response()->json(ApiResponse::success(null, '客户分配成功'));
        } catch (\Exception $e) {
            Log::error('客户分配失败', [
                'user_id' => $request->user_id,
                'agent_id' => $request->agent_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('客户分配失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 取消客户分配
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unassign(Request $request)
    {
        // 验证权限
        $currentUser = $request->user();
        if (!$currentUser->employee || !in_array($currentUser->employee->role, ['admin', 'manager'])) {
            return response()->json(ApiResponse::error('没有权限取消客户分配', 403), 403);
        }
        
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'agent_id' => 'required|exists:employees,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        try {
            // 查找用户
            $user = User::findOrFail($request->user_id);
            
            // 验证用户是否与此CRM专员关联
            if ($user->crm_agent_id != $request->agent_id) {
                return response()->json(ApiResponse::error('用户未与指定CRM专员关联', 400), 400);
            }
            
            // 清除用户的CRM专员关联
            $user->crm_agent_id = null;
            $user->save();
            
            // 更新专员的客户计数
            $employee = Employee::findOrFail($request->agent_id);
            if ($employee && $employee->crmAgentInfo) {
                $employee->crmAgentInfo->updateClientsCount();
            }
            
            return response()->json(ApiResponse::success(null, '客户分配已取消'));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('取消客户分配失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取客户的CRM专员列表
     *
     * @param Request $request
     * @param int $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getClientAgents(Request $request, $userId)
    {
        // 验证权限
        $currentUser = $request->user();
        
        // 放宽权限检查：
        // 1. 用户查看自己的专员列表
        // 2. 用户有管理员或经理角色
        // 3. 用户是CRM专员且在查询自己负责的客户
        $hasPermission = false;
        
        // 1. 用户查看自己的数据
        if ($currentUser->id == $userId) {
            $hasPermission = true;
        }
        // 2. 管理员或经理可以查看任何用户
        else if ($currentUser->employee && in_array($currentUser->employee->role, ['admin', 'manager'])) {
            $hasPermission = true;
        }
        // 3. CRM专员查看自己负责的客户
        else if ($currentUser->employee && $currentUser->employee->role == 'crm_agent') {
            // 检查此用户是否是该CRM专员负责的客户
            $isMyClient = User::where('id', $userId)
                ->where('crm_agent_id', $currentUser->employee->id)
                ->exists();
                
            if ($isMyClient) {
                $hasPermission = true;
            }
        }
        
        if (!$hasPermission) {
            return response()->json(ApiResponse::error('没有权限查看此用户的CRM专员列表', 403), 403);
        }
        
        $user = User::findOrFail($userId);
        
        // 由于用户只能被一个CRM专员负责，从users表获取CRM专员信息
        if ($user->crm_agent_id) {
            $agent = Employee::where('id', $user->crm_agent_id)->first();
            
            // 构造分页结果
            $agentCollection = collect([$agent]);
            $perPage = $request->input('per_page', 10);
            $paginatedAgents = new \Illuminate\Pagination\LengthAwarePaginator(
                $agentCollection, 
                $agentCollection->count(), 
                $perPage, 
                1, 
                ['path' => $request->url()]
            );
            
            return response()->json(ApiResponse::success($paginatedAgents));
        }
        
        // 用户没有CRM专员，返回空分页结果
        $emptyCollection = collect([]);
        $perPage = $request->input('per_page', 10);
        $emptyPaginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $emptyCollection, 
            0, 
            $perPage, 
            1, 
            ['path' => $request->url()]
        );
        
        return response()->json(ApiResponse::success($emptyPaginator));
    }
    
    /**
     * 获取CRM专员的客户列表
     *
     * @param Request $request
     * @param int $agentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAgentClients(Request $request, $agentId)
    {
        // 验证权限 - 临时允许所有已登录用户访问
        // $currentUser = $request->user();
        
        // 临时禁用权限检查，允许所有已登录用户访问
        // 放宽权限检查：
        // 1. 用户ID与被查询的专员ID相同
        // 2. 用户有管理员或经理角色
        // 3. 用户是CRM专员（允许CRM专员之间互相查看）
        /*
        $hasPermission = false;
        
        // 1. 自己查看自己
        if ($currentUser->id == $agentId) {
            $hasPermission = true;
        }
        // 2. 管理员或经理查看任何人
        else if ($currentUser->employee && in_array($currentUser->employee->role, ['admin', 'manager'])) {
            $hasPermission = true;
        }
        // 3. CRM专员也可以查看其他CRM专员的客户列表
        else if ($currentUser->employee && $currentUser->employee->role == 'crm_agent') {
            $hasPermission = true;
        }
        
        if (!$hasPermission) {
            return response()->json(ApiResponse::error('没有权限查看此CRM专员的客户列表', 403), 403);
        }
        */
        
        // 添加日志调试信息
        Log::info('访问CRM专员客户列表', [
            'agent_id' => $agentId,
            'request_params' => $request->all()
        ]);
        
        try {
            // 根据users表结构，crm_agent_id直接关联到employees表的id
            // 因此，我们可以直接使用提供的agentId查询用户
            $perPage = $request->input('per_page', 10);
            $keyword = $request->input('keyword');
            
            Log::info('直接使用crm_agent_id查询客户列表', ['crm_agent_id' => $agentId]);
            
            // 简单直接地查询crm_agent_id字段等于提供的ID的所有用户
            $query = User::with('defaultAddress')
                ->where('crm_agent_id', $agentId);
            
            // 如果有搜索关键词，添加搜索条件
            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%")
                      ->orWhere('merchant_name', 'like', "%{$keyword}%");
                });
            }
            
            $clients = $query->paginate($perPage);
            
            Log::info('查询结果', ['total' => $clients->total()]);
            
            // 返回成功结果
            return response()->json(ApiResponse::success($clients));
                
        } catch (\Exception $e) {
            Log::error('获取CRM专员客户列表失败', [
                'agent_id' => $agentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取数据失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 批量分配客户
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchAssign(Request $request)
    {
        // 验证权限 - 暂时禁用权限检查
        /*
        $currentUser = $request->user();
        if (!$currentUser->employee || !in_array($currentUser->employee->role, ['admin', 'manager'])) {
            return response()->json(ApiResponse::error('没有权限批量分配客户', 403), 403);
        }
        */
        
        $validator = Validator::make($request->all(), [
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'agent_id' => 'required|exists:employees,id',
            'notes' => 'nullable|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        // 添加日志
        Log::info('批量分配客户给CRM专员', [
            'user_ids' => $request->user_ids,
            'agent_id' => $request->agent_id
        ]);
        
        try {
            // 查找CRM专员（员工）
            $employee = Employee::findOrFail($request->agent_id);
            if (!$employee->isCrmAgent()) {
                Log::error('指定员工不是CRM专员', [
                    'employee_id' => $employee->id,
                    'role' => $employee->role
                ]);
                return response()->json(ApiResponse::error('指定员工不是CRM专员', 400), 400);
            }
            
            // 检查CRM专员是否可以接受这么多客户
            $crmAgent = $employee->crmAgentInfo;
            $newCount = count($request->user_ids);
            
            // 如果存在crm_agents表记录和客户数限制
            if ($crmAgent && method_exists($crmAgent, 'canAcceptClients')) {
                $currentCount = $crmAgent->clients_count ?? 0;
                $maxClients = $crmAgent->max_clients ?? PHP_INT_MAX;
                
                if ($currentCount + $newCount > $maxClients) {
                    Log::error('超出专员最大客户数限制', [
                        'agent_id' => $employee->id,
                        'current_count' => $currentCount,
                        'new_count' => $newCount,
                        'max_clients' => $maxClients
                    ]);
                    return response()->json(ApiResponse::error('超出专员最大客户数限制', 400), 400);
                }
            }
            
            // 使用事务确保数据一致性
            DB::beginTransaction();
            
            $updatedCount = 0;
            
            // 遍历所有用户ID
            foreach ($request->user_ids as $userId) {
                $user = User::find($userId);
                if ($user) {
                    // 直接更新用户的crm_agent_id字段
                    $user->crm_agent_id = $employee->id;
                    $user->save();
                    $updatedCount++;
                }
            }
            
            // 如果需要记录notes，可以将其存储在其他表中
            // 比如client_follow_ups表或自定义表
            if ($request->notes && !empty($request->notes) && $updatedCount > 0) {
                // 这里可以根据实际需求处理notes
                Log::info('批量客户分配附带备注', [
                    'agent_id' => $employee->id,
                    'updated_count' => $updatedCount,
                    'notes' => $request->notes
                ]);
            }
            
            // 更新CRM专员的客户数量统计（如果需要）
            if ($crmAgent && method_exists($crmAgent, 'updateClientsCount')) {
                $crmAgent->updateClientsCount();
            }
            
            DB::commit();
            
            Log::info('成功批量分配客户给CRM专员', [
                'agent_id' => $employee->id,
                'updated_count' => $updatedCount
            ]);
            
            return response()->json(ApiResponse::success([
                'updated_count' => $updatedCount
            ], "成功分配 {$updatedCount} 个客户"));
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('批量分配客户失败', [
                'user_ids' => $request->user_ids,
                'agent_id' => $request->agent_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('批量分配客户失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 取消单个客户与CRM专员的关联
     *
     * @param Request $request
     * @param int $agentId CRM专员ID（employees表的ID）
     * @param int $userId 客户用户ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function unassignSingle(Request $request, $agentId, $userId)
    {
        // 暂时禁用权限检查
        /*
        // 验证权限
        $currentUser = $request->user();
        if (!$currentUser->employee || !in_array($currentUser->employee->role, ['admin', 'manager'])) {
            return response()->json(ApiResponse::error('没有权限取消客户分配', 403), 403);
        }
        */
        
        // 添加日志
        Log::info('取消客户与CRM专员的关联', [
            'agent_id' => $agentId,
            'user_id' => $userId
        ]);
        
        try {
            // 查找用户
            $user = User::find($userId);
            if (!$user) {
                Log::error('找不到指定用户', ['user_id' => $userId]);
                return response()->json(ApiResponse::error('找不到指定用户', 404), 404);
            }
            
            // 验证用户是否与此CRM专员关联
            if ($user->crm_agent_id != $agentId) {
                Log::error('用户未与指定CRM专员关联', [
                    'user_id' => $userId, 
                    'current_agent_id' => $user->crm_agent_id,
                    'requested_agent_id' => $agentId
                ]);
                return response()->json(ApiResponse::error('用户未与指定CRM专员关联', 400), 400);
            }
            
            // 清除用户的CRM专员关联
            $user->crm_agent_id = null;
            $user->save();
            
            Log::info('成功取消客户与CRM专员的关联', [
                'user_id' => $userId,
                'agent_id' => $agentId
            ]);
            
            // 更新CRM专员的客户数量统计（如果需要）
            $employee = Employee::find($agentId);
            if ($employee) {
                $crmAgent = $employee->crmAgentInfo;
                if ($crmAgent) {
                    // 假设CrmAgent模型有updateClientsCount方法
                    $crmAgent->updateClientsCount();
                }
            }
            
            return response()->json(ApiResponse::success(null, '已取消客户与CRM专员的关联'));
            
        } catch (\Exception $e) {
            Log::error('取消客户与CRM专员关联失败', [
                'agent_id' => $agentId,
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('取消关联失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 批量取消客户与CRM专员的关联
     *
     * @param Request $request
     * @param int $agentId CRM专员ID（employees表的ID）
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchUnassign(Request $request, $agentId)
    {
        // 暂时禁用权限检查
        /*
        // 验证权限
        $currentUser = $request->user();
        if (!$currentUser->employee || !in_array($currentUser->employee->role, ['admin', 'manager'])) {
            return response()->json(ApiResponse::error('没有权限取消客户分配', 403), 403);
        }
        */
        
        $validator = Validator::make($request->all(), [
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        // 添加日志
        Log::info('批量取消客户与CRM专员的关联', [
            'agent_id' => $agentId,
            'user_ids' => $request->user_ids
        ]);
        
        try {
            $userIds = $request->user_ids;
            $updatedCount = 0;
            
            // 使用事务确保数据一致性
            DB::beginTransaction();
            
            // 逐个处理用户
            foreach ($userIds as $userId) {
                $user = User::find($userId);
                if ($user && $user->crm_agent_id == $agentId) {
                    $user->crm_agent_id = null;
                    $user->save();
                    $updatedCount++;
                }
            }
            
            // 更新CRM专员的客户数量统计（如果需要）
            $employee = Employee::find($agentId);
            if ($employee) {
                $crmAgent = $employee->crmAgentInfo;
                if ($crmAgent) {
                    // 假设CrmAgent模型有updateClientsCount方法
                    $crmAgent->updateClientsCount();
                }
            }
            
            DB::commit();
            
            Log::info('成功批量取消客户与CRM专员的关联', [
                'agent_id' => $agentId,
                'updated_count' => $updatedCount
            ]);
            
            return response()->json(ApiResponse::success([
                'updated_count' => $updatedCount
            ], "成功取消 {$updatedCount} 个客户的关联"));
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('批量取消客户与CRM专员关联失败', [
                'agent_id' => $agentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('批量取消关联失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取未分配CRM专员的用户列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnassignedClients(Request $request)
    {
        // 暂时禁用权限检查
        /*
        $currentUser = $request->user();
        if (!$currentUser->employee || !in_array($currentUser->employee->role, ['admin', 'manager', 'crm_agent'])) {
            return response()->json(ApiResponse::error('没有权限查看未分配用户列表', 403), 403);
        }
        */
        
        try {
            $perPage = $request->input('per_page', 10);
            $keyword = $request->input('keyword');
            
            $query = User::with('defaultAddress')
                ->whereNull('crm_agent_id');
            
            // 如果有搜索关键词，添加搜索条件
            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%")
                      ->orWhere('merchant_name', 'like', "%{$keyword}%");
                });
            }
            
            // 排序，默认按创建时间降序
            $sortField = $request->input('sort_field', 'created_at');
            $sortOrder = $request->input('sort_order', 'desc');
            
            // 安全检查，防止SQL注入
            $allowedSortFields = ['id', 'name', 'phone', 'merchant_name', 'created_at', 'joined_at'];
            if (in_array($sortField, $allowedSortFields)) {
                $query->orderBy($sortField, $sortOrder === 'asc' ? 'asc' : 'desc');
            } else {
                $query->orderBy('created_at', 'desc');
            }
            
            $clients = $query->paginate($perPage);
            
            Log::info('获取未分配CRM专员的用户列表', [
                'count' => $clients->count(),
                'keyword' => $keyword,
                'sort_field' => $sortField,
                'sort_order' => $sortOrder
            ]);
            
            return response()->json(ApiResponse::success($clients));
            
        } catch (\Exception $e) {
            Log::error('获取未分配CRM专员的用户列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取数据失败: ' . $e->getMessage(), 500), 500);
        }
    }
} 