<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('batch_operations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('batch_id')->constrained('inventory_batches')->onDelete('cascade')->comment('批次ID');
            $table->enum('operation_type', [
                'created',      // 创建
                'received',     // 收货
                'shipped',      // 出货
                'moved',        // 移动
                'adjusted',     // 调整
                'quality_check', // 质量检验
                'status_change', // 状态变更
                'locked',       // 锁定
                'unlocked',     // 解锁
                'damaged',      // 损坏
                'expired',      // 过期
                'disposed',     // 处置
                'recalled',     // 召回
            ])->comment('操作类型');
            
            $table->decimal('quantity_before', 10, 2)->nullable()->comment('操作前数量');
            $table->decimal('quantity_after', 10, 2)->nullable()->comment('操作后数量');
            $table->decimal('quantity_change', 10, 2)->nullable()->comment('数量变化');
            
            $table->string('status_before', 50)->nullable()->comment('操作前状态');
            $table->string('status_after', 50)->nullable()->comment('操作后状态');
            
            $table->json('operation_data')->nullable()->comment('操作详细数据');
            $table->text('reason')->nullable()->comment('操作原因');
            $table->text('notes')->nullable()->comment('备注');
            
            // 关联信息
            $table->string('reference_type', 50)->nullable()->comment('关联类型（订单、调拨单等）');
            $table->unsignedBigInteger('reference_id')->nullable()->comment('关联ID');
            $table->string('reference_no', 100)->nullable()->comment('关联单号');
            
            // 位置信息
            $table->foreignId('warehouse_id')->nullable()->constrained()->comment('仓库ID');
            $table->string('location', 100)->nullable()->comment('具体位置');
            
            // 操作人员
            $table->foreignId('operated_by')->constrained('users')->comment('操作人');
            $table->timestamp('operated_at')->useCurrent()->comment('操作时间');
            
            $table->timestamps();
            
            // 索引
            $table->index(['batch_id', 'operation_type']);
            $table->index(['operation_type', 'operated_at']);
            $table->index(['reference_type', 'reference_id']);
            $table->index('operated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('batch_operations');
    }
}; 