<?php

namespace App\Upload\Contracts;

use Illuminate\Http\UploadedFile;

interface StorageDriverInterface
{
    /**
     * 存储文件
     *
     * @param UploadedFile $file 上传的文件
     * @param string $directory 目标目录
     * @param string $filename 文件名
     * @param array $options 附加选项
     * @return array|null 上传结果
     */
    public function store(UploadedFile $file, string $directory, string $filename, array $options = []): ?array;
    
    /**
     * 获取文件URL
     *
     * @param string $path 文件路径
     * @return string
     */
    public function getUrl(string $path): string;
    
    /**
     * 删除文件
     *
     * @param string $path 文件路径
     * @return bool
     */
    public function delete(string $path): bool;
    
    /**
     * 获取存储驱动名称
     *
     * @return string
     */
    public function getDriverName(): string;
} 