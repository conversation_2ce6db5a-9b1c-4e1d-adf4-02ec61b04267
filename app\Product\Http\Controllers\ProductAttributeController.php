<?php

namespace App\Product\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Product\Models\ProductAttribute;
use App\Product\Models\ProductAttributeValue;
use App\Product\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class ProductAttributeController extends Controller
{
    /**
     * 获取属性列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = ProductAttribute::query();
            
            // 搜索
            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('key', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }
            
            // 按类型筛选
            if ($request->filled('type')) {
                $query->where('type', $request->input('type'));
            }
            
            // 按状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }
            
            // 是否可搜索
            if ($request->filled('is_searchable')) {
                $query->where('is_searchable', $request->input('is_searchable'));
            }
            
            // 是否在列表显示
            if ($request->filled('show_in_list')) {
                $query->where('show_in_list', $request->input('show_in_list'));
            }
            
            // 排序
            $sortBy = $request->input('sort_by', 'sort');
            $sortOrder = $request->input('sort_order', 'asc');
            $query->orderBy($sortBy, $sortOrder);
            
            // 分页
            $perPage = $request->input('per_page', 15);
            if ($request->input('no_pagination')) {
                $attributes = $query->get();
                return response()->json(ApiResponse::success($attributes));
            } else {
                $attributes = $query->paginate($perPage);
                return response()->json(ApiResponse::success($attributes));
            }
        } catch (\Exception $e) {
            Log::error('获取属性列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(ApiResponse::error('获取属性列表失败'), 500);
        }
    }
    
    /**
     * 创建属性
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'key' => 'required|string|max:255|unique:product_attributes,key',
                'type' => 'required|in:text,number,select,multi_select,boolean,date,textarea',
                'options' => 'nullable|array',
                'unit' => 'nullable|string|max:50',
                'placeholder' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'is_required' => 'boolean',
                'is_searchable' => 'boolean',
                'show_in_list' => 'boolean',
                'sort' => 'integer|min:0',
                'status' => 'boolean',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $attribute = ProductAttribute::create($request->all());
            
            return response()->json(ApiResponse::success($attribute, '属性创建成功'), 201);
        } catch (\Exception $e) {
            Log::error('创建属性失败', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            return response()->json(ApiResponse::error('创建属性失败'), 500);
        }
    }
    
    /**
     * 获取属性详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $attribute = ProductAttribute::with(['values.product'])->findOrFail($id);
            
            // 统计使用该属性的商品数量
            $productCount = ProductAttributeValue::where('attribute_id', $id)->distinct('product_id')->count();
            $attribute->product_count = $productCount;
            
            return response()->json(ApiResponse::success($attribute));
        } catch (\Exception $e) {
            Log::error('获取属性详情失败', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取属性详情失败'), 500);
        }
    }
    
    /**
     * 更新属性
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $attribute = ProductAttribute::findOrFail($id);
            
            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|string|max:255',
                'key' => 'sometimes|string|max:255|unique:product_attributes,key,' . $id,
                'type' => 'sometimes|in:text,number,select,multi_select,boolean,date,textarea',
                'options' => 'nullable|array',
                'unit' => 'nullable|string|max:50',
                'placeholder' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'is_required' => 'boolean',
                'is_searchable' => 'boolean',
                'show_in_list' => 'boolean',
                'sort' => 'integer|min:0',
                'status' => 'boolean',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $attribute->update($request->all());
            
            return response()->json(ApiResponse::success($attribute, '属性更新成功'));
        } catch (\Exception $e) {
            Log::error('更新属性失败', [
                'id' => $id,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            return response()->json(ApiResponse::error('更新属性失败'), 500);
        }
    }
    
    /**
     * 删除属性
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $attribute = ProductAttribute::findOrFail($id);
            
            // 检查是否有商品使用了该属性
            $usageCount = ProductAttributeValue::where('attribute_id', $id)->count();
            if ($usageCount > 0) {
                return response()->json(ApiResponse::error("该属性已被 {$usageCount} 个商品使用，无法删除"), 422);
            }
            
            $attribute->delete();
            
            return response()->json(ApiResponse::success(null, '属性删除成功'));
        } catch (\Exception $e) {
            Log::error('删除属性失败', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('删除属性失败'), 500);
        }
    }
    
    /**
     * 获取属性类型列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTypes()
    {
        try {
            $types = ProductAttribute::getTypes();
            return response()->json(ApiResponse::success($types));
        } catch (\Exception $e) {
            Log::error('获取属性类型失败', [
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取属性类型失败'), 500);
        }
    }
    
    /**
     * 批量更新属性排序
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateSort(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'attributes' => 'required|array',
                'attributes.*.id' => 'required|exists:product_attributes,id',
                'attributes.*.sort' => 'required|integer|min:0',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            DB::transaction(function () use ($request) {
                foreach ($request->input('attributes') as $item) {
                    ProductAttribute::where('id', $item['id'])->update(['sort' => $item['sort']]);
                }
            });
            
            return response()->json(ApiResponse::success(null, '排序更新成功'));
        } catch (\Exception $e) {
            Log::error('更新属性排序失败', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            return response()->json(ApiResponse::error('更新排序失败'), 500);
        }
    }
    
    /**
     * 获取商品的属性值
     *
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductAttributes($productId)
    {
        try {
            $product = Product::findOrFail($productId);
            
            // 获取所有启用的属性
            $allAttributes = ProductAttribute::active()->ordered()->get();
            
            // 获取该商品已设置的属性值
            $productAttributeValues = ProductAttributeValue::where('product_id', $productId)
                ->with('attribute')
                ->get()
                ->keyBy('attribute_id');
            
            // 组合数据
            $attributes = $allAttributes->map(function ($attribute) use ($productAttributeValues) {
                $value = $productAttributeValues->get($attribute->id);
                
                return [
                    'id' => $attribute->id,
                    'name' => $attribute->name,
                    'key' => $attribute->key,
                    'type' => $attribute->type,
                    'type_name' => $attribute->type_name,
                    'options' => $attribute->options,
                    'unit' => $attribute->unit,
                    'placeholder' => $attribute->placeholder,
                    'description' => $attribute->description,
                    'is_required' => $attribute->is_required,
                    'is_searchable' => $attribute->is_searchable,
                    'show_in_list' => $attribute->show_in_list,
                    'value' => $value ? $value->formatted_value : null,
                    'raw_value' => $value ? $value->value : null,
                    'value_json' => $value ? $value->value_json : null,
                    'has_value' => $value !== null,
                ];
            });
            
            return response()->json(ApiResponse::success($attributes));
        } catch (\Exception $e) {
            Log::error('获取商品属性失败', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取商品属性失败'), 500);
        }
    }
    
    /**
     * 设置商品的属性值
     *
     * @param Request $request
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function setProductAttributes(Request $request, $productId)
    {
        try {
            $product = Product::findOrFail($productId);
            
            $validator = Validator::make($request->all(), [
                'attributes' => 'required|array',
                'attributes.*.attribute_id' => 'required|exists:product_attributes,id',
                'attributes.*.value' => 'nullable',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            DB::transaction(function () use ($request, $productId) {
                // 先删除该商品的所有现有属性值
                if ($request->input('clear_all_attributes', true)) {
                    ProductAttributeValue::where('product_id', $productId)->delete();
                    Log::info('已清除商品所有现有属性值', [
                        'product_id' => $productId
                    ]);
                }
                
                // 收集要设置的属性ID列表，用于确保唯一性
                $attributeIds = collect($request->input('attributes'))->pluck('attribute_id')->toArray();
                
                // 检查是否有重复的属性ID
                if (count($attributeIds) !== count(array_unique($attributeIds))) {
                    throw new \Exception("请求中包含重复的属性ID，每个属性只能设置一次");
                }
                
                foreach ($request->input('attributes') as $item) {
                    $attributeId = $item['attribute_id'];
                    $value = $item['value'] ?? null;
                    
                    // 获取属性定义
                    $attribute = ProductAttribute::find($attributeId);
                    if (!$attribute) {
                        continue;
                    }
                    
                    // 创建属性值记录（即使值为空也创建，表示属性已绑定）
                    $attributeValue = new ProductAttributeValue([
                            'product_id' => $productId,
                            'attribute_id' => $attributeId,
                            'value' => $value ?: null,
                            'value_json' => null,
                    ]);
                    
                    // 如果有值，进行验证并设置
                    if ($value !== null && $value !== '') {
                        // 验证属性值
                        if (!$attribute->validateValue($value)) {
                            throw new \Exception("属性 {$attribute->name} 的值无效");
                        }
                        
                        // 设置值
                        $attributeValue->setValue($value);
                    }
                    
                        $attributeValue->save();
                    
                    Log::info('设置商品属性值', [
                        'product_id' => $productId,
                        'attribute_id' => $attributeId,
                        'attribute_name' => $attribute->name,
                        'value' => $value
                    ]);
                }
            });
            
            return response()->json(ApiResponse::success(null, '商品属性设置成功'));
        } catch (\Exception $e) {
            Log::error('设置商品属性失败', [
                'product_id' => $productId,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            return response()->json(ApiResponse::error('设置商品属性失败: ' . $e->getMessage()), 500);
        }
    }
    
    /**
     * 获取属性的统计信息
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats()
    {
        try {
            $stats = [
                'total_attributes' => ProductAttribute::count(),
                'active_attributes' => ProductAttribute::where('status', true)->count(),
                'searchable_attributes' => ProductAttribute::where('is_searchable', true)->count(),
                'required_attributes' => ProductAttribute::where('is_required', true)->count(),
                'type_distribution' => ProductAttribute::select('type', DB::raw('count(*) as count'))
                    ->groupBy('type')
                    ->pluck('count', 'type'),
                'usage_stats' => ProductAttributeValue::select('attribute_id', DB::raw('count(distinct product_id) as product_count'))
                    ->groupBy('attribute_id')
                    ->with('attribute:id,name')
                    ->get()
                    ->map(function ($item) {
                        return [
                            'attribute_id' => $item->attribute_id,
                            'attribute_name' => $item->attribute->name ?? '未知',
                            'product_count' => $item->product_count,
                        ];
                    }),
            ];
            
            return response()->json(ApiResponse::success($stats));
        } catch (\Exception $e) {
            Log::error('获取属性统计失败', [
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取属性统计失败'), 500);
        }
    }
    
    /**
     * 复制属性
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function copy($id)
    {
        try {
            $originalAttribute = ProductAttribute::findOrFail($id);
            
            $newAttribute = $originalAttribute->replicate();
            $newAttribute->name = $originalAttribute->name . ' - 副本';
            $newAttribute->key = $originalAttribute->key . '_copy_' . time();
            $newAttribute->save();
            
            return response()->json(ApiResponse::success($newAttribute, '属性复制成功'), 201);
        } catch (\Exception $e) {
            Log::error('复制属性失败', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('复制属性失败'), 500);
        }
    }
} 