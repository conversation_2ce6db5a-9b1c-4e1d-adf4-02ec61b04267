<!-- components/search-header/search-header.wxml -->
<view class="search-header" wx:if="{{show}}">
  <!-- 左侧搜索按钮 -->
  <block wx:if="{{buttonPosition === 'left' && showSearchButton && !focused}}">
    <view class="search-btn" bindtap="onSearchButtonTap">
      <van-icon name="{{searchIcon || 'search'}}" color="#ffffff" size="32rpx" />
    </view>
  </block>
  
  <!-- 搜索输入区域 -->
  <view class="search-input-area {{focused ? 'focused' : ''}}">
    <van-icon name="search" color="#999999" size="30rpx" class="search-icon" />
    <input
      class="search-input"
      value="{{value}}"
      placeholder="{{placeholder}}"
      placeholder-class="placeholder"
      bindinput="onSearchChange"
      bindconfirm="onSearch"
      bindfocus="onSearchFocus"
      bindblur="onSearchBlur"
      bindtap="onClickInput"
      confirm-type="search"
      focus="{{autoFocus}}"
      adjust-position="{{true}}"
    />
    <van-icon 
      wx:if="{{value.length > 0}}" 
      name="clear" 
      color="#999999" 
      size="30rpx" 
      class="clear-icon" 
      bindtap="clearSearch"
    />
  </view>
  
  <!-- 右侧搜索按钮 -->
  <block wx:if="{{buttonPosition === 'right' && showSearchButton && !focused}}">
    <view class="search-btn" bindtap="onSearchButtonTap">
      <van-icon name="{{searchIcon || 'search'}}" color="#ffffff" size="32rpx" />
    </view>
  </block>
  
  <!-- 取消按钮 -->
  <view wx:if="{{showAction && focused}}" class="action-btn" bindtap="onSearchCancel">
    <van-icon name="close" size="28rpx" custom-style="margin-right: 4rpx;" />
    {{actionText}}
  </view>
</view> 