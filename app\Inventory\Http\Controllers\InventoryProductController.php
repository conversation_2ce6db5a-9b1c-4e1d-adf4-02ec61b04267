<?php

namespace App\Inventory\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Inventory\Models\Inventory;
use App\Product\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InventoryProductController extends Controller
{
    /**
     * 获取库存商品列表（库存管理视角）
     * 包含商品基础信息、多仓库库存状态、单位转换信息
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        Log::info('🔍 InventoryProductController::index 被调用', [
            'request_params' => $request->all(),
            'user_id' => auth()->id(),
        ]);
        
        try {
            // 基础查询：获取所有商品（包括没有库存记录的）
            $query = Product::with([
                'inventories.warehouse', 
                'inventories.unit',
                'category', 
                'baseUnit', 
                'images'
            ]);
            
            // 首先检查是否有任何商品数据
            $basicProductCount = Product::count();
            Log::info('🔍 基础商品统计', [
                'total_products_count' => $basicProductCount,
                'sample_products' => Product::select('id', 'name', 'status')->limit(3)->get()->toArray()
            ]);
            
            // 是否只显示有库存的商品
            $onlyWithInventory = $request->input('only_with_inventory', false);
            // 处理字符串布尔值
            if (is_string($onlyWithInventory)) {
                $onlyWithInventory = filter_var($onlyWithInventory, FILTER_VALIDATE_BOOLEAN);
            }
            Log::info('📦 only_with_inventory 参数', [
                'raw_value' => $request->input('only_with_inventory'),
                'processed_value' => $onlyWithInventory,
                'type' => gettype($onlyWithInventory)
            ]);
            
            if ($onlyWithInventory) {
                $query->whereHas('inventories');
                Log::info('⚠️ 已应用 only_with_inventory 过滤器');
            } else {
                Log::info('✅ 显示所有商品（包括无库存商品）');
            }
            
            // 添加库存统计子查询
            $query->withCount('inventories as warehouse_count')
                ->addSelect([
                    'total_stock' => Inventory::selectRaw('COALESCE(SUM(stock), 0)')
                        ->whereColumn('product_id', 'products.id'),
                    'low_stock_warehouses' => Inventory::selectRaw('COUNT(*)')
                        ->whereColumn('product_id', 'products.id')
                        ->where('stock', '>', 0)
                        ->where('stock', '<=', 10),
                    'out_stock_warehouses' => Inventory::selectRaw('COUNT(*)')
                        ->whereColumn('product_id', 'products.id')
                        ->where('stock', '<=', 0),
                ]);

            // 关键词搜索
            if ($request->keyword) {
                $query->where(function($q) use ($request) {
                    $q->where('name', 'like', "%{$request->keyword}%")
                      ->orWhere('code', 'like', "%{$request->keyword}%")
                      ->orWhere('description', 'like', "%{$request->keyword}%");
                });
            }

            // 分类筛选
            if ($request->category_id) {
                $query->where('category_id', $request->category_id);
            }

            // 仓库筛选（显示在指定仓库有库存的商品）
            if ($request->warehouse_id) {
                $query->whereHas('inventories', function($q) use ($request) {
                    $q->where('warehouse_id', $request->warehouse_id);
                });
            }

            // 库存状态筛选
            if ($request->stock_status) {
                switch ($request->stock_status) {
                    case 'low':
                        $query->whereHas('inventories', function($q) {
                            $q->where('stock', '>', 0)->where('stock', '<=', 10);
                        });
                        break;
                    case 'out':
                        $query->whereHas('inventories', function($q) {
                            $q->where('stock', '<=', 0);
                        });
                        break;
                    case 'normal':
                        $query->whereHas('inventories', function($q) {
                            $q->where('stock', '>', 10);
                        });
                        break;
                }
            }

            // 供应商筛选
            if ($request->supplier) {
                $query->where('supplier', 'like', "%{$request->supplier}%");
            }

            // 价格范围筛选
            if ($request->min_price) {
                $query->where('sale_price', '>=', $request->min_price);
            }
            if ($request->max_price) {
                $query->where('sale_price', '<=', $request->max_price);
            }

            // 日期范围筛选
            if ($request->start_date) {
                $query->where('created_at', '>=', $request->start_date);
            }
            if ($request->end_date) {
                $query->where('created_at', '<=', $request->end_date);
            }

            // 商品状态筛选
            if ($request->status !== null && $request->status !== '') {
                $query->where('status', $request->status);
            }

            // 排序
            $orderBy = $request->order_by ?? 'id';
            $direction = $request->direction ?? 'desc';
            
            // 支持按库存排序
            if ($orderBy === 'total_stock') {
                $query->orderByRaw('total_stock ' . $direction);
            } else {
                $query->orderBy($orderBy, $direction);
            }

            // 在分页前检查基础商品数量
            $totalProductsInDb = Product::count();
            Log::info('🔍 数据库基础信息', [
                'total_products_in_db' => $totalProductsInDb,
                'total_inventories_in_db' => Inventory::count()
            ]);
            
            // 检查当前查询的SQL和绑定参数
            $queryClone = clone $query;
            $sql = $queryClone->toSql();
            $bindings = $queryClone->getBindings();
            Log::info('🔍 查询SQL信息', [
                'sql' => $sql,
                'bindings' => $bindings
            ]);
            
            // 分页
            $perPage = $request->per_page ?? $request->limit ?? 20;
            $products = $query->paginate($perPage);
            
            Log::info('📦 查询结果统计', [
                'total_products_in_db' => $totalProductsInDb,
                'query_result_total' => $products->total(),
                'current_page_count' => count($products->items()),
                'per_page' => $perPage
            ]);

            // 处理商品数据，添加详细的库存和单位信息
            $transformedItems = $products->items();
            $transformedData = collect($transformedItems)->map(function ($product) use ($request) {
                return $this->formatInventoryProductData($product, $request);
            });
            
            // 重新构建分页响应
            $result = [
                'data' => $transformedData,
                'current_page' => $products->currentPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
                'last_page' => $products->lastPage(),
                'from' => $products->firstItem(),
                'to' => $products->lastItem(),
                'path' => $products->path(),
            ];
            
            Log::info('✅ 准备返回响应', [
                'result_data_count' => $transformedData->count(),
                'result_total' => $result['total']
            ]);

            return response()->json(ApiResponse::success($result, '获取库存商品列表成功'));

        } catch (\Exception $e) {
            Log::error('获取库存商品列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'params' => $request->all()
            ]);
            return response()->json(ApiResponse::error('获取库存商品列表失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 格式化库存商品数据
     * 
     * @param Product $product
     * @param Request $request
     * @return array
     */
    private function formatInventoryProductData($product, $request = null)
    {
        // 获取商品图片
        $image = $this->getProductImage($product);

        // 计算库存状态
        $totalStock = floatval($product->total_stock ?? 0);
        $stockStatus = $this->calculateStockStatus($totalStock);

        // 获取单位信息
        $availableUnits = $product->getAllUnits() ?? [];
        $unitInfo = $this->getUnitInfo($product, $availableUnits);

        // 获取仓库库存详情（包含移动加权平均成本价和时间信息）
        $warehouseStocks = $this->getWarehouseStocks($product, $request);

        // 获取商品表中的成本价（这是销售单位的成本价）
        $saleCostPrice = floatval($product->cost_price ?? 0);
        
        // 转换为基本单位的成本价
        $baseCostPrice = $this->calculateBaseCostPrice($product, $availableUnits);
        
        // 计算利润率（使用基本单位的价格和成本价）
        $baseSalePrice = $this->calculateBaseSalePrice($product, $availableUnits);
        $profitRate = 0;
        if ($baseCostPrice > 0 && $baseSalePrice > 0) {
            $profitRate = (($baseSalePrice - $baseCostPrice) / $baseCostPrice) * 100;
        }

        // 获取最新的入库和出库时间
        $lastInTime = null;
        $lastOutTime = null;
        
        if ($product->inventories) {
            foreach ($product->inventories as $inventory) {
                // 获取最新的入库和出库时间
                $inventoryLastIn = \App\Inventory\Models\InventoryTransaction::where('product_id', $product->id)
                    ->where('warehouse_id', $inventory->warehouse_id)
                    ->whereHas('transactionType', function($query) {
                        $query->where('effect_direction', '>', 0);
                    })
                    ->orderBy('created_at', 'desc')
                    ->value('created_at');
                    
                $inventoryLastOut = \App\Inventory\Models\InventoryTransaction::where('product_id', $product->id)
                    ->where('warehouse_id', $inventory->warehouse_id)
                    ->whereHas('transactionType', function($query) {
                        $query->where('effect_direction', '<', 0);
                    })
                    ->orderBy('created_at', 'desc')
                    ->value('created_at');
                
                if (!$lastInTime || ($inventoryLastIn && $inventoryLastIn > $lastInTime)) {
                    $lastInTime = $inventoryLastIn;
                }
                if (!$lastOutTime || ($inventoryLastOut && $inventoryLastOut > $lastOutTime)) {
                    $lastOutTime = $inventoryLastOut;
                }
            }
        }

        return [
            'id' => $product->id,
            'name' => $product->name,
            'code' => $product->code,
            'description' => $product->description,
            'image' => $image,
            'cover_url' => $image,
            
            // 分类信息
            'category' => $product->category ? [
                'id' => $product->category->id,
                'name' => $product->category->name,
            ] : null,
            'category_name' => $product->category?->name,
            'category_id' => $product->category_id,
            
            // 价格信息 - 统一使用基本单位价格
            'sale_price' => $this->calculateBaseSalePrice($product, $availableUnits), // 基本单位销售价格
            'cost_price' => $baseCostPrice, // 基本单位成本价格
            'base_cost_price' => $baseCostPrice, // 基础单位的成本价
            'price' => $this->calculateBaseSalePrice($product, $availableUnits), // 兼容字段
            'profit_rate' => round($profitRate, 2),
            
            // 添加单位转换信息
            'sale_conversion_factor' => $this->getSaleConversionFactor($product, $availableUnits),
            'base_sale_price' => $this->calculateBaseSalePrice($product, $availableUnits),
            
            // 库存信息
            'stock' => $totalStock,
            'stock_quantity' => $totalStock,
            'total_stock' => $totalStock, // 添加前端需要的字段
            'warehouse_count' => intval($product->warehouse_count ?? 0),
            'low_stock_warehouses' => intval($product->low_stock_warehouses ?? 0),
            'out_stock_warehouses' => intval($product->out_stock_warehouses ?? 0),
            'stock_status' => $stockStatus,
            'warehouse_stocks' => $warehouseStocks,
            
            // 商品状态
            'status' => intval($product->status),
            'inventory_policy' => $product->inventory_policy ?? 'inherit',
            'track_inventory' => boolval($product->track_inventory ?? true),
            'min_stock_threshold' => floatval($product->min_stock_threshold ?? 0),
            
            // 单位信息
            'base_unit_id' => $product->base_unit_id,
            'base_unit_name' => $unitInfo['base_unit_name'],
            'sale_unit_name' => $unitInfo['sale_unit_name'],
            'purchase_unit_name' => $unitInfo['purchase_unit_name'],
            'inventory_unit_name' => $unitInfo['inventory_unit_name'],
            'unit_name' => $unitInfo['base_unit_name'], // 兼容字段
            'baseUnit' => $product->baseUnit ? [
                'id' => $product->baseUnit->id,
                'name' => $product->baseUnit->name,
                'display_name' => $product->baseUnit->display_name ?? $product->baseUnit->name,
                'symbol' => $product->baseUnit->symbol ?? $product->baseUnit->name,
            ] : null,
            'available_units' => $availableUnits,
            
            // 时间信息
            'created_at' => $product->created_at?->toISOString(),
            'updated_at' => $product->updated_at?->toISOString(),
            'last_in_time' => $lastInTime ? $lastInTime->format('Y-m-d H:i:s') : null,
            'last_out_time' => $lastOutTime ? $lastOutTime->format('Y-m-d H:i:s') : null,
            'last_in_date' => $lastInTime ? $lastInTime->format('Y-m-d') : null,
            'last_out_date' => $lastOutTime ? $lastOutTime->format('Y-m-d') : null,
        ];
    }

    /**
     * 获取商品图片
     */
    private function getProductImage($product)
    {
        $image = '';

        // 安全检查：确保images是Collection或数组且不为空
        if ($product->images) {
            // 如果是Collection，使用Collection方法
            if (is_object($product->images) && method_exists($product->images, 'isNotEmpty')) {
                if ($product->images->isNotEmpty()) {
                    $mainImage = $product->images->firstWhere('is_main', true) ?? $product->images->first();
                    $image = $mainImage->url ?? $mainImage->path ?? '';
                }
            }
            // 如果是数组，使用数组方法
            elseif (is_array($product->images) && count($product->images) > 0) {
                $mainImage = collect($product->images)->firstWhere('is_main', true) ?? $product->images[0];
                $image = is_object($mainImage) ? ($mainImage->url ?? $mainImage->path ?? '') : '';
            }
        }

        return $image ?: $product->cover_url ?: '';
    }

    /**
     * 计算库存状态
     */
    private function calculateStockStatus($totalStock)
    {
        if ($totalStock <= 0) {
            return 'out';
        } elseif ($totalStock <= 10) {
            return 'low';
        }
        return 'normal';
    }

    /**
     * 获取单位信息
     */
    private function getUnitInfo($product, $availableUnits)
    {
        $baseUnitName = $product->baseUnit?->name ?? '个';
        $saleUnitName = $baseUnitName;
        $purchaseUnitName = $baseUnitName;
        $inventoryUnitName = $baseUnitName;

        // 从可用单位中查找不同角色的单位
        foreach ($availableUnits as $unit) {
            if (isset($unit['roles']) && is_array($unit['roles'])) {
                if (in_array('sales', $unit['roles'])) {
                    $saleUnitName = $unit['name'];
                }
                if (in_array('purchase', $unit['roles'])) {
                    $purchaseUnitName = $unit['name'];
                }
                if (in_array('inventory', $unit['roles'])) {
                    $inventoryUnitName = $unit['name'];
                }
            }
        }

        return [
            'base_unit_name' => $baseUnitName,
            'sale_unit_name' => $saleUnitName,
            'purchase_unit_name' => $purchaseUnitName,
            'inventory_unit_name' => $inventoryUnitName,
        ];
    }

    /**
     * 获取销售单位转换率
     */
    private function getSaleConversionFactor($product, $availableUnits)
    {
        try {
            // 直接从 availableUnits 中查找具有 sales 角色的单位
            foreach ($availableUnits as $unit) {
                if (isset($unit['roles']) && is_array($unit['roles']) && in_array('sales', $unit['roles'])) {
                    return floatval($unit['conversion_factor'] ?? 1);
                }
            }

            // 如果没有找到销售单位，返回1.0（使用基本单位）
            return 1.0;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::warning('获取销售单位转换率失败', [
                'product_id' => $product->id,
                'error' => $e->getMessage()
            ]);
            
            return 1.0;
        }
    }

    /**
     * 计算基本单位销售价格
     */
    private function calculateBaseSalePrice($product, $availableUnits)
    {
        $salePrice = floatval($product->sale_price ?? $product->price ?? 0);
        
        if ($salePrice <= 0) {
            return 0.0;
        }

        // 获取销售单位转换率
        $saleConversionFactor = $this->getSaleConversionFactor($product, $availableUnits);
        
        // 基本单位销售价格 = 销售单位价格 ÷ 转换率
        // 例如：1斤9元，1斤=0.5千克，基本单位价格=9÷0.5=18元/千克
        return round($salePrice / $saleConversionFactor, 4);
    }

    /**
     * 计算基本单位成本价格
     */
    private function calculateBaseCostPrice($product, $availableUnits)
    {
        $saleCostPrice = floatval($product->cost_price ?? 0);
        
        if ($saleCostPrice <= 0) {
            return 0.0;
        }

        // 获取销售单位转换率
        $saleConversionFactor = $this->getSaleConversionFactor($product, $availableUnits);
        
        // 基本单位成本价格 = 销售单位成本价格 ÷ 转换率
        // 例如：1斤9.97元，1斤=0.5千克，基本单位成本价格=9.97÷0.5=19.94元/千克
        return round($saleCostPrice / $saleConversionFactor, 4);
    }



    /**
     * 获取仓库库存详情（包含移动加权平均成本价和时间信息）
     */
    private function getWarehouseStocks($product, $request = null)
    {
        $warehouseStocks = [];
        
        // 如果请求了详细库存信息
        $includeWarehouseDetail = $request ? $request->input('include_warehouse_detail', false) : false;
        
        if ($includeWarehouseDetail && $product->inventories) {
            // 获取商品的基础成本价
            $baseCostPrice = floatval($product->cost_price ?? 0);
            
            foreach ($product->inventories as $inventory) {
                // 直接使用商品表的成本价（基础单位）
                
                // 获取最后入库和出库时间
                $lastInTime = \App\Inventory\Models\InventoryTransaction::where('product_id', $product->id)
                    ->where('warehouse_id', $inventory->warehouse_id)
                    ->whereHas('transactionType', function($query) {
                        $query->where('effect_direction', '>', 0);
                    })
                    ->orderBy('created_at', 'desc')
                    ->value('created_at');
                    
                $lastOutTime = \App\Inventory\Models\InventoryTransaction::where('product_id', $product->id)
                    ->where('warehouse_id', $inventory->warehouse_id)
                    ->whereHas('transactionType', function($query) {
                        $query->where('effect_direction', '<', 0);
                    })
                    ->orderBy('created_at', 'desc')
                    ->value('created_at');
                
                $warehouseStocks[] = [
                    'warehouse_id' => $inventory->warehouse_id,
                    'warehouse_name' => $inventory->warehouse?->location ?? '未知仓库',
                    'stock' => floatval($inventory->stock),
                    'unit_name' => $inventory->unit?->name ?? '',
                    'unit_id' => $inventory->unit_id,
                    'min_stock_level' => floatval($inventory->min_stock_level ?? 0),
                    'stock_status' => $this->calculateStockStatus($inventory->stock),
                    'updated_at' => $inventory->updated_at?->toISOString(),
                    
                    // 成本价信息（基础单位）
                    'base_cost_price' => $baseCostPrice,
                    'last_in_time' => $lastInTime ? $lastInTime->format('Y-m-d H:i:s') : null,
                    'last_out_time' => $lastOutTime ? $lastOutTime->format('Y-m-d H:i:s') : null,
                    'last_in_date' => $lastInTime ? $lastInTime->format('Y-m-d') : null,
                    'last_out_date' => $lastOutTime ? $lastOutTime->format('Y-m-d') : null,
                ];
            }
        }
        
        return $warehouseStocks;
    }

    /**
     * 获取商品详情
     * 根据查询参数决定返回库存视角还是编辑视角的数据
     * 
     * @param Request $request
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $productId)
    {
        try {
            // 如果是编辑用途，返回适合编辑表单的数据格式
            if ($request->query('for_edit') === 'true') {
                return $this->getForEdit($productId);
            }
            
            // 默认返回库存视角的详细数据
            $product = Product::with(['category', 'baseUnit', 'images'])->findOrFail($productId);
            
            // 获取商品在所有仓库的库存
            $inventories = Inventory::where('product_id', $productId)
                ->with(['warehouse', 'unit'])
                ->get();

            // 格式化库存数据
            $inventoryData = $inventories->map(function ($inventory) use ($product) {
                return [
                    'id' => $inventory->id,
                    'warehouse' => [
                        'id' => $inventory->warehouse->id,
                        'location' => $inventory->warehouse->location,
                        'name' => $inventory->warehouse->name ?? $inventory->warehouse->location,
                    ],
                    'stock' => $inventory->stock,
                    'unit' => $inventory->unit ? [
                        'id' => $inventory->unit->id,
                        'name' => $inventory->unit->name,
                        'symbol' => $inventory->unit->symbol ?? $inventory->unit->name,
                    ] : null,
                    'min_stock_level' => $inventory->min_stock_level,
                    'updated_at' => $inventory->updated_at?->toISOString(),
                ];
            });

            // 统计信息
            $stats = [
                'total_stock' => $inventories->sum('stock'),
                'warehouse_count' => $inventories->count(),
                'low_stock_warehouses' => $inventories->where('stock', '<=', 10)->count(),
                'out_stock_warehouses' => $inventories->where('stock', '<=', 0)->count(),
            ];

            return response()->json(ApiResponse::success([
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'code' => $product->code,
                    'description' => $product->description,
                    'category' => $product->category,
                    'sale_price' => $product->sale_price ?? $product->price,
                    'cost_price' => $product->cost_price,
                    'status' => $product->status,
                    'base_unit' => $product->baseUnit,
                    'available_units' => $product->getAllUnits(),
                    'image' => $product->cover_url,
                ],
                'inventories' => $inventoryData,
                'stats' => $stats,
            ], '获取商品库存详情成功'));

        } catch (\Exception $e) {
            Log::error('获取商品库存详情失败', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取商品库存详情失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 创建商品（库存视角）
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            // 委托给Product控制器处理创建逻辑
            $productController = app(\App\Product\Http\Controllers\ProductController::class);
            return $productController->store($request);
        } catch (\Exception $e) {
            Log::error('创建商品失败', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            return response()->json(ApiResponse::error('创建商品失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 更新商品（库存视角）
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            // 委托给Product控制器处理更新逻辑
            $productController = app(\App\Product\Http\Controllers\ProductController::class);
            return $productController->update($request, $id);
        } catch (\Exception $e) {
            Log::error('更新商品失败', [
                'product_id' => $id,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            return response()->json(ApiResponse::error('更新商品失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 删除商品（库存视角）
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            // 委托给Product控制器处理删除逻辑
            $productController = app(\App\Product\Http\Controllers\ProductController::class);
            return $productController->destroy($id);
        } catch (\Exception $e) {
            Log::error('删除商品失败', [
                'product_id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('删除商品失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 获取商品详情（适合编辑的格式）
     * 返回标准的商品数据结构，而不是库存视角的复杂结构
     * 
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getForEdit($productId)
    {
        try {
            Log::info('开始获取商品编辑数据', ['product_id' => $productId]);
            
            // 验证产品ID
            if (!is_numeric($productId) || $productId <= 0) {
                Log::warning('无效的产品ID', ['product_id' => $productId]);
                return response()->json(ApiResponse::error('无效的商品ID'), 400);
            }
            
            // 先检查商品是否存在
            $product = Product::find($productId);
            if (!$product) {
                Log::warning('商品不存在', ['product_id' => $productId]);
                return response()->json(ApiResponse::error('商品不存在'), 404);
            }
            
            // 基础数据加载
            $product = Product::with(['category', 'baseUnit', 'images'])->find($productId);
            
            Log::info('商品基础数据加载完成', [
                'product_id' => $productId,
                'name' => $product->name,
                'category_loaded' => !is_null($product->category),
                'base_unit_loaded' => !is_null($product->baseUnit),
                'images_count' => $product->images()->count() // 直接使用关系查询而不是属性访问器
            ]);

            // 获取单位信息和转换率 - 使用安全的方式
            $saleUnit = null;
            $purchaseUnit = null;
            $allUnits = [];
            $unitConversions = [];
            
            try {
                // 获取所有单位信息
                $allUnits = $product->getAllUnits();
                Log::info('获取所有单位成功', [
                    'product_id' => $productId,
                    'units_count' => count($allUnits)
                ]);
            } catch (\Exception $e) {
                Log::warning('获取所有单位失败', ['error' => $e->getMessage()]);
                $allUnits = [];
            }
            
            try {
                // 安全地获取销售单位
                $saleUnit = $product->getSaleDefaultUnit();
                Log::info('获取销售单位', [
                    'sale_unit_id' => $saleUnit ? $saleUnit->id : null,
                    'sale_unit_name' => $saleUnit ? $saleUnit->name : null
                ]);
            } catch (\Exception $e) {
                Log::warning('获取销售单位失败', ['error' => $e->getMessage()]);
                $saleUnit = $product->baseUnit; // 降级到基础单位
            }
            
            try {
                // 安全地获取采购单位
                $purchaseUnit = $product->getPurchaseDefaultUnit();
                Log::info('获取采购单位', [
                    'purchase_unit_id' => $purchaseUnit ? $purchaseUnit->id : null,
                    'purchase_unit_name' => $purchaseUnit ? $purchaseUnit->name : null
                ]);
            } catch (\Exception $e) {
                Log::warning('获取采购单位失败', ['error' => $e->getMessage()]);
                $purchaseUnit = $product->baseUnit; // 降级到基础单位
            }
            
            // 计算单位转换率
            try {
                if ($product->baseUnit) {
                    $baseUnitId = $product->baseUnit->id;
                    
                    foreach ($allUnits as $unit) {
                        if (isset($unit['id']) && $unit['id'] != $baseUnitId) {
                            try {
                                $conversionRate = $product->getUnitConversionRate($baseUnitId, $unit['id']);
                                $unitConversions[] = [
                                    'from_unit_id' => $baseUnitId,
                                    'to_unit_id' => $unit['id'],
                                    'conversion_rate' => $conversionRate,
                                    'from_unit_name' => $product->baseUnit->name,
                                    'to_unit_name' => $unit['name'] ?? '',
                                ];
                                
                                // 反向转换率
                                $reverseRate = $product->getUnitConversionRate($unit['id'], $baseUnitId);
                                $unitConversions[] = [
                                    'from_unit_id' => $unit['id'],
                                    'to_unit_id' => $baseUnitId,
                                    'conversion_rate' => $reverseRate,
                                    'from_unit_name' => $unit['name'] ?? '',
                                    'to_unit_name' => $product->baseUnit->name,
                                ];
                            } catch (\Exception $e) {
                                Log::warning('计算转换率失败', [
                                    'unit_id' => $unit['id'],
                                    'error' => $e->getMessage()
                                ]);
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::warning('计算单位转换率失败', ['error' => $e->getMessage()]);
            }
            
            Log::info('单位信息获取完成', [
                'has_base_unit' => !is_null($product->baseUnit),
                'sale_unit_id' => $saleUnit ? $saleUnit->id : null,
                'purchase_unit_id' => $purchaseUnit ? $purchaseUnit->id : null,
                'all_units_count' => count($allUnits),
                'conversions_count' => count($unitConversions)
            ]);
            
            // 处理图片数据 - 直接使用关系查询避免属性访问器的问题
            $images = [];
            try {
                // 直接查询图片关系，避免使用可能返回数组的属性访问器
                $productImages = $product->images()->orderBy('sort', 'asc')->get();
                
                if ($productImages && $productImages->count() > 0) {
                    $images = $productImages->map(function($image) {
                        return [
                            'id' => $image->id ?? null,
                            'url' => $image->url ?? $image->path ?? '',
                            'sort_order' => $image->sort ?? $image->sort_order ?? 0,
                            'is_main' => $image->is_main ?? false
                        ];
                    })->filter(function($image) {
                        return !empty($image['url']);
                    })->values()->toArray();
                }
                
                Log::info('图片数据处理完成', [
                    'product_id' => $productId,
                    'images_count' => count($images),
                    'raw_images_count' => $productImages ? $productImages->count() : 0
                ]);
            } catch (\Exception $e) {
                Log::warning('处理商品图片失败', ['error' => $e->getMessage()]);
                $images = [];
            }
            
            // 计算当前库存（安全方式）
            $totalStock = 0;
            try {
                $totalStock = \App\Inventory\Models\Inventory::where('product_id', $productId)
                    ->sum('stock') ?? 0;
            } catch (\Exception $e) {
                Log::warning('计算库存失败', ['error' => $e->getMessage()]);
            }

            // 构建响应数据 - 确保所有字段都有明确的默认值
            $responseData = [
                // 基本信息
                'id' => $product->id,
                'name' => $product->name ?? '',
                'code' => $product->code ?? '',
                'description' => $product->description ?? '',
                'category_id' => $product->category_id ?? null,
                'status' => (int) ($product->status ?? 1),
                'allow_sale' => (bool) ($product->allow_sale ?? true),
                
                // 价格信息
                'sale_price' => (float) ($product->sale_price ?? $product->price ?? 0),
                'cost_price' => (float) ($product->cost_price ?? 0),
                
                // 单位信息
                'base_unit_id' => $product->base_unit_id ?? null,
                'sale_unit_id' => $saleUnit ? $saleUnit->id : null,
                'purchase_unit_id' => $purchaseUnit ? $purchaseUnit->id : null,
                'min_sale_quantity' => (float) ($product->min_sale_quantity ?? 1),
                
                // 库存策略
                'inventory_policy' => $product->inventory_policy ?? 'inherit',
                'min_stock_threshold' => (float) ($product->min_stock_threshold ?? 0),
                'max_negative_stock' => (float) ($product->max_negative_stock ?? 0),
                'track_inventory' => (bool) ($product->track_inventory ?? true),
                'auto_reorder' => (bool) ($product->auto_reorder ?? false),
                'reorder_point' => (float) ($product->reorder_point ?? 0),
                'reorder_quantity' => (float) ($product->reorder_quantity ?? 0),
                
                // 图片信息
                'cover_url' => $product->cover_url ?? '',
                'image' => $product->cover_url ?? $product->image ?? '',
                'images' => $images,
                
                // 关联数据
                'category' => $product->category ? [
                    'id' => $product->category->id,
                    'name' => $product->category->name ?? '',
                    'display_name' => $product->category->display_name ?? $product->category->name ?? ''
                ] : null,
                
                // 单位信息 - 详细的单位设置
                'base_unit' => $product->baseUnit ? [
                    'id' => $product->baseUnit->id,
                    'name' => $product->baseUnit->name ?? '',
                    'symbol' => $product->baseUnit->symbol ?? '',
                    'type' => $product->baseUnit->type ?? ''
                ] : null,
                
                'sale_unit' => $saleUnit ? [
                    'id' => $saleUnit->id,
                    'name' => $saleUnit->name ?? '',
                    'symbol' => $saleUnit->symbol ?? '',
                    'type' => $saleUnit->type ?? ''
                ] : null,
                
                'purchase_unit' => $purchaseUnit ? [
                    'id' => $purchaseUnit->id,
                    'name' => $purchaseUnit->name ?? '',
                    'symbol' => $purchaseUnit->symbol ?? '',
                    'type' => $purchaseUnit->type ?? ''
                ] : null,
                
                // 所有可用单位
                'all_units' => $allUnits,
                
                // 单位转换率设置
                'unit_conversions' => $unitConversions,
                
                // 当前库存信息
                'stock' => $totalStock,
                'total_stock' => $totalStock,
                
                // 时间戳
                'created_at' => $product->created_at?->toISOString(),
                'updated_at' => $product->updated_at?->toISOString(),
            ];

            Log::info('商品编辑数据构建完成', [
                'product_id' => $productId,
                'has_category' => !is_null($responseData['category']),
                'has_base_unit' => !is_null($responseData['base_unit']),
                'has_sale_unit' => !is_null($responseData['sale_unit']),
                'has_purchase_unit' => !is_null($responseData['purchase_unit']),
                'images_count' => count($responseData['images']),
                'all_units_count' => count($responseData['all_units']),
                'unit_conversions_count' => count($responseData['unit_conversions']),
                'total_stock' => $responseData['total_stock']
            ]);

            return response()->json(ApiResponse::success($responseData, '获取商品详情成功'));

        } catch (\Exception $e) {
            Log::error('获取商品详情失败', [
                'product_id' => $productId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回一个基础的错误响应，但包含有用的调试信息
            return response()->json([
                'success' => false,
                'message' => '获取商品详情失败',
                'error' => $e->getMessage(),
                'debug' => [
                    'product_id' => $productId,
                    'timestamp' => now()->toISOString()
                ]
            ], 500);
        }
    }

    /**
     * 获取库存商品统计
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats()
    {
        try {
            // 所有商品总数（库存管理视角应该显示所有商品）
            $totalProducts = Product::count();
            
            // 在售商品数
            $activeProducts = Product::where('status', 1)->count();
                
            // 低库存商品数（有库存记录且库存在1-10之间）
            $lowStockProducts = Product::whereHas('inventories', function($q) {
                $q->where('stock', '>', 0)->where('stock', '<=', 10);
            })->count();
            
            // 缺货商品数（有库存记录且库存为0，或者没有库存记录）
            $outOfStockProducts = Product::where(function($query) {
                $query->whereHas('inventories', function($q) {
                    $q->where('stock', '<=', 0);
                })->orWhereDoesntHave('inventories');
            })->count();
            
            // 计算总价值和平均价格（使用price字段，如果没有则使用sale_price）
            $totalValue = Product::where('status', 1)
                ->whereRaw('COALESCE(price, sale_price, 0) > 0')
                ->sum(DB::raw('COALESCE(price, sale_price, 0)'));
            $averagePrice = $activeProducts > 0 ? round($totalValue / $activeProducts, 2) : 0;

            return response()->json(ApiResponse::success([
                'totalProducts' => $totalProducts,
                'activeProducts' => $activeProducts,
                'lowStockProducts' => $lowStockProducts,
                'outOfStockProducts' => $outOfStockProducts,
                'totalValue' => $totalValue,
                'averagePrice' => $averagePrice,
            ]));

        } catch (\Exception $e) {
            Log::error('获取库存商品统计失败', [
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取统计数据失败'), 500);
        }
    }

    /**
     * 调试接口 - 返回详细的调试信息
     */
    public function debug(Request $request)
    {
        try {
            $debugInfo = [
                'timestamp' => now()->toISOString(),
                'request_info' => [
                    'method' => $request->method(),
                    'url' => $request->fullUrl(),
                    'params' => $request->all(),
                    'headers' => $request->headers->all(),
                    'user_id' => auth()->id(),
                    'user_type' => auth()->user() ? get_class(auth()->user()) : null,
                ],
                'database_info' => [
                    'products_count' => Product::count(),
                    'inventories_count' => Inventory::count(),
                    'sample_product' => Product::with(['inventories.warehouse', 'category', 'baseUnit'])
                        ->first(),
                ],
                'environment_info' => [
                    'app_env' => config('app.env'),
                    'app_debug' => config('app.debug'),
                    'database_connection' => config('database.default'),
                ],
            ];

            // 测试基础查询
            try {
                $testQuery = Product::with([
                    'inventories.warehouse', 
                    'inventories.unit',
                    'category', 
                    'baseUnit', 
                    'images'
                ])->limit(5)->get();
                
                $debugInfo['test_query'] = [
                    'success' => true,
                    'count' => $testQuery->count(),
                    'sample_data' => $testQuery->map(function($product) {
                        return $this->formatInventoryProductData($product);
                    })
                ];
            } catch (\Exception $e) {
                $debugInfo['test_query'] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ];
            }

            return response()->json(ApiResponse::success($debugInfo, '调试信息获取成功'));

        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('获取调试信息失败: ' . $e->getMessage()), 500);
        }
    }
} 