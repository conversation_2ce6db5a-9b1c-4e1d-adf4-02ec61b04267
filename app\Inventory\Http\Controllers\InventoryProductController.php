<?php

namespace App\Inventory\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Inventory\Models\Inventory;
use App\Product\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InventoryProductController extends Controller
{
    /**
     * 获取库存商品列表（库存管理视角）
     * 包含商品基础信息、多仓库库存状态、单位转换信息
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 基础查询：获取所有商品（包括没有库存记录的）
            $query = Product::with([
                'inventories.warehouse', 
                'inventories.unit',
                'category', 
                'baseUnit', 
                'images'
            ]);
            
            // 是否只显示有库存的商品
            $onlyWithInventory = $request->input('only_with_inventory', false);
            if ($onlyWithInventory) {
                $query->whereHas('inventories');
            }
            
            // 添加库存统计子查询
            $query->withCount('inventories as warehouse_count')
                ->addSelect([
                    'total_stock' => Inventory::selectRaw('COALESCE(SUM(stock), 0)')
                        ->whereColumn('product_id', 'products.id'),
                    'low_stock_warehouses' => Inventory::selectRaw('COUNT(*)')
                        ->whereColumn('product_id', 'products.id')
                        ->where('stock', '>', 0)
                        ->where('stock', '<=', 10),
                    'out_stock_warehouses' => Inventory::selectRaw('COUNT(*)')
                        ->whereColumn('product_id', 'products.id')
                        ->where('stock', '<=', 0),
                ]);

            // 关键词搜索
            if ($request->keyword) {
                $query->where(function($q) use ($request) {
                    $q->where('name', 'like', "%{$request->keyword}%")
                      ->orWhere('code', 'like', "%{$request->keyword}%")
                      ->orWhere('description', 'like', "%{$request->keyword}%");
                });
            }

            // 分类筛选
            if ($request->category_id) {
                $query->where('category_id', $request->category_id);
            }

            // 仓库筛选（显示在指定仓库有库存的商品）
            if ($request->warehouse_id) {
                $query->whereHas('inventories', function($q) use ($request) {
                    $q->where('warehouse_id', $request->warehouse_id);
                });
            }

            // 库存状态筛选
            if ($request->stock_status) {
                switch ($request->stock_status) {
                    case 'low':
                        $query->whereHas('inventories', function($q) {
                            $q->where('stock', '>', 0)->where('stock', '<=', 10);
                        });
                        break;
                    case 'out':
                        $query->whereHas('inventories', function($q) {
                            $q->where('stock', '<=', 0);
                        });
                        break;
                    case 'normal':
                        $query->whereHas('inventories', function($q) {
                            $q->where('stock', '>', 10);
                        });
                        break;
                }
            }

            // 供应商筛选
            if ($request->supplier) {
                $query->where('supplier', 'like', "%{$request->supplier}%");
            }

            // 价格范围筛选
            if ($request->min_price) {
                $query->where('sale_price', '>=', $request->min_price);
            }
            if ($request->max_price) {
                $query->where('sale_price', '<=', $request->max_price);
            }

            // 日期范围筛选
            if ($request->start_date) {
                $query->where('created_at', '>=', $request->start_date);
            }
            if ($request->end_date) {
                $query->where('created_at', '<=', $request->end_date);
            }

            // 商品状态筛选
            if ($request->status !== null && $request->status !== '') {
                $query->where('status', $request->status);
            }

            // 排序
            $orderBy = $request->order_by ?? 'id';
            $direction = $request->direction ?? 'desc';
            
            // 支持按库存排序
            if ($orderBy === 'total_stock') {
                $query->orderByRaw('total_stock ' . $direction);
            } else {
                $query->orderBy($orderBy, $direction);
            }

            // 分页
            $perPage = $request->per_page ?? $request->limit ?? 20;
            $products = $query->paginate($perPage);

            // 处理商品数据，添加详细的库存和单位信息
            $transformedItems = $products->items();
            $transformedData = collect($transformedItems)->map(function ($product) use ($request) {
                return $this->formatInventoryProductData($product, $request);
            });
            
            // 重新构建分页响应
            $result = [
                'data' => $transformedData,
                'current_page' => $products->currentPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
                'last_page' => $products->lastPage(),
                'from' => $products->firstItem(),
                'to' => $products->lastItem(),
                'path' => $products->path(),
            ];

            return response()->json(ApiResponse::success($result, '获取库存商品列表成功'));

        } catch (\Exception $e) {
            Log::error('获取库存商品列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'params' => $request->all()
            ]);
            return response()->json(ApiResponse::error('获取库存商品列表失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 格式化库存商品数据
     * 
     * @param Product $product
     * @param Request $request
     * @return array
     */
    private function formatInventoryProductData($product, $request = null)
    {
        // 获取商品图片
        $image = $this->getProductImage($product);

        // 计算库存状态
        $totalStock = floatval($product->total_stock ?? 0);
        $stockStatus = $this->calculateStockStatus($totalStock);

        // 获取单位信息
        $availableUnits = $product->getAllUnits() ?? [];
        $unitInfo = $this->getUnitInfo($product, $availableUnits);

        // 获取仓库库存详情
        $warehouseStocks = $this->getWarehouseStocks($product, $request);

        // 计算利润率
        $profitRate = 0;
        if ($product->cost_price > 0 && $product->sale_price > 0) {
            $profitRate = (($product->sale_price - $product->cost_price) / $product->cost_price) * 100;
        }

        return [
            'id' => $product->id,
            'name' => $product->name,
            'code' => $product->code,
            'description' => $product->description,
            'image' => $image,
            'cover_url' => $image,
            
            // 分类信息
            'category' => $product->category ? [
                'id' => $product->category->id,
                'name' => $product->category->name,
            ] : null,
            'category_name' => $product->category?->name,
            'category_id' => $product->category_id,
            
            // 价格信息
            'sale_price' => floatval($product->sale_price ?? $product->price ?? 0),
            'cost_price' => floatval($product->cost_price ?? 0),
            'price' => floatval($product->sale_price ?? $product->price ?? 0),
            'profit_rate' => round($profitRate, 2),
            
            // 库存信息
            'stock' => $totalStock,
            'stock_quantity' => $totalStock,
            'warehouse_count' => intval($product->warehouse_count ?? 0),
            'low_stock_warehouses' => intval($product->low_stock_warehouses ?? 0),
            'out_stock_warehouses' => intval($product->out_stock_warehouses ?? 0),
            'stock_status' => $stockStatus,
            'warehouse_stocks' => $warehouseStocks,
            
            // 商品状态
            'status' => intval($product->status),
            'inventory_policy' => $product->inventory_policy ?? 'inherit',
            'track_inventory' => boolval($product->track_inventory ?? true),
            'min_stock_threshold' => floatval($product->min_stock_threshold ?? 0),
            
            // 单位信息
            'base_unit_id' => $product->base_unit_id,
            'base_unit_name' => $unitInfo['base_unit_name'],
            'sale_unit_name' => $unitInfo['sale_unit_name'],
            'purchase_unit_name' => $unitInfo['purchase_unit_name'],
            'inventory_unit_name' => $unitInfo['inventory_unit_name'],
            'unit_name' => $unitInfo['base_unit_name'], // 兼容字段
            'baseUnit' => $product->baseUnit ? [
                'id' => $product->baseUnit->id,
                'name' => $product->baseUnit->name,
                'display_name' => $product->baseUnit->display_name ?? $product->baseUnit->name,
                'symbol' => $product->baseUnit->symbol ?? $product->baseUnit->name,
            ] : null,
            'available_units' => $availableUnits,
            
            // 时间信息
            'created_at' => $product->created_at?->toISOString(),
            'updated_at' => $product->updated_at?->toISOString(),
        ];
    }

    /**
     * 获取商品图片
     */
    private function getProductImage($product)
    {
        $image = '';

        // 安全检查：确保images是Collection或数组且不为空
        if ($product->images) {
            // 如果是Collection，使用Collection方法
            if (is_object($product->images) && method_exists($product->images, 'isNotEmpty')) {
                if ($product->images->isNotEmpty()) {
                    $mainImage = $product->images->firstWhere('is_main', true) ?? $product->images->first();
                    $image = $mainImage->url ?? $mainImage->path ?? '';
                }
            }
            // 如果是数组，使用数组方法
            elseif (is_array($product->images) && count($product->images) > 0) {
                $mainImage = collect($product->images)->firstWhere('is_main', true) ?? $product->images[0];
                $image = is_object($mainImage) ? ($mainImage->url ?? $mainImage->path ?? '') : '';
            }
        }

        return $image ?: $product->cover_url ?: '';
    }

    /**
     * 计算库存状态
     */
    private function calculateStockStatus($totalStock)
    {
        if ($totalStock <= 0) {
            return 'out';
        } elseif ($totalStock <= 10) {
            return 'low';
        }
        return 'normal';
    }

    /**
     * 获取单位信息
     */
    private function getUnitInfo($product, $availableUnits)
    {
        $baseUnitName = $product->baseUnit?->name ?? '个';
        $saleUnitName = $baseUnitName;
        $purchaseUnitName = $baseUnitName;
        $inventoryUnitName = $baseUnitName;

        // 从可用单位中查找不同角色的单位
        foreach ($availableUnits as $unit) {
            if (isset($unit['roles']) && is_array($unit['roles'])) {
                if (in_array('sales', $unit['roles'])) {
                    $saleUnitName = $unit['name'];
                }
                if (in_array('purchase', $unit['roles'])) {
                    $purchaseUnitName = $unit['name'];
                }
                if (in_array('inventory', $unit['roles'])) {
                    $inventoryUnitName = $unit['name'];
                }
            }
        }

        return [
            'base_unit_name' => $baseUnitName,
            'sale_unit_name' => $saleUnitName,
            'purchase_unit_name' => $purchaseUnitName,
            'inventory_unit_name' => $inventoryUnitName,
        ];
    }

    /**
     * 获取仓库库存详情
     */
    private function getWarehouseStocks($product, $request = null)
    {
        $warehouseStocks = [];
        
        // 如果请求了详细库存信息
        $includeWarehouseDetail = $request ? $request->input('include_warehouse_detail', false) : false;
        
        if ($includeWarehouseDetail && $product->inventories) {
            foreach ($product->inventories as $inventory) {
                $warehouseStocks[] = [
                    'warehouse_id' => $inventory->warehouse_id,
                    'warehouse_name' => $inventory->warehouse?->location ?? '未知仓库',
                    'stock' => floatval($inventory->stock),
                    'unit_name' => $inventory->unit?->name ?? '',
                    'unit_id' => $inventory->unit_id,
                    'min_stock_level' => floatval($inventory->min_stock_level ?? 0),
                    'stock_status' => $this->calculateStockStatus($inventory->stock),
                    'updated_at' => $inventory->updated_at?->toISOString(),
                ];
            }
        }
        
        return $warehouseStocks;
    }

    /**
     * 获取商品在所有仓库的库存详情
     * 
     * @param int $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($productId)
    {
        try {
            $product = Product::with(['category', 'baseUnit', 'images'])->findOrFail($productId);
            
            // 获取商品在所有仓库的库存
            $inventories = Inventory::where('product_id', $productId)
                ->with(['warehouse', 'unit'])
                ->get();

            // 格式化库存数据
            $inventoryData = $inventories->map(function ($inventory) use ($product) {
                return [
                    'id' => $inventory->id,
                    'warehouse' => [
                        'id' => $inventory->warehouse->id,
                        'location' => $inventory->warehouse->location,
                        'name' => $inventory->warehouse->name ?? $inventory->warehouse->location,
                    ],
                    'stock' => $inventory->stock,
                    'unit' => $inventory->unit ? [
                        'id' => $inventory->unit->id,
                        'name' => $inventory->unit->name,
                        'symbol' => $inventory->unit->symbol ?? $inventory->unit->name,
                    ] : null,
                    'min_stock_level' => $inventory->min_stock_level,
                    'updated_at' => $inventory->updated_at?->toISOString(),
                ];
            });

            // 统计信息
            $stats = [
                'total_stock' => $inventories->sum('stock'),
                'warehouse_count' => $inventories->count(),
                'low_stock_warehouses' => $inventories->where('stock', '<=', 10)->count(),
                'out_stock_warehouses' => $inventories->where('stock', '<=', 0)->count(),
            ];

            return response()->json(ApiResponse::success([
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'code' => $product->code,
                    'description' => $product->description,
                    'category' => $product->category,
                    'sale_price' => $product->sale_price ?? $product->price,
                    'cost_price' => $product->cost_price,
                    'status' => $product->status,
                    'base_unit' => $product->baseUnit,
                    'available_units' => $product->getAllUnits(),
                    'image' => $product->cover_url,
                ],
                'inventories' => $inventoryData,
                'stats' => $stats,
            ], '获取商品库存详情成功'));

        } catch (\Exception $e) {
            Log::error('获取商品库存详情失败', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取商品库存详情失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 获取库存商品统计
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats()
    {
        try {
            // 有库存的商品总数
            $totalProducts = Product::whereHas('inventories')->count();
            
            // 在售商品数
            $activeProducts = Product::whereHas('inventories')
                ->where('status', 1)
                ->count();
                
            // 低库存商品数
            $lowStockProducts = Product::whereHas('inventories', function($q) {
                $q->where('stock', '>', 0)->where('stock', '<=', 10);
            })->count();
            
            // 缺货商品数
            $outOfStockProducts = Product::whereHas('inventories', function($q) {
                $q->where('stock', '<=', 0);
            })->count();

            return response()->json(ApiResponse::success([
                'totalProducts' => $totalProducts,
                'activeProducts' => $activeProducts,
                'lowStockProducts' => $lowStockProducts,
                'outOfStockProducts' => $outOfStockProducts,
            ]));

        } catch (\Exception $e) {
            Log::error('获取库存商品统计失败', [
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取统计数据失败'), 500);
        }
    }
} 