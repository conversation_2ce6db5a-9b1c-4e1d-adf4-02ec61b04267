<?php

namespace App\Supplier\Services;

use App\Supplier\Models\Supplier;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class SupplierService
{
    /**
     * 获取供应商列表
     *
     * @param array $params 过滤参数
     * @return LengthAwarePaginator
     */
    public function getSuppliers(array $params): LengthAwarePaginator
    {
        $query = Supplier::query();
        
        // 搜索条件
        if (isset($params['search'])) {
            $search = $params['search'];
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('contact_person', 'like', "%{$search}%")
                  ->orWhere('contact_phone', 'like', "%{$search}%");
            });
        }
        
        // 排序
        $orderBy = $params['order_by'] ?? 'created_at';
        $direction = $params['direction'] ?? 'desc';
        $query->orderBy($orderBy, $direction);
        
        return $query->paginate($params['per_page'] ?? 15);
    }
    
    /**
     * 创建供应商
     *
     * @param array $data 供应商数据
     * @return Supplier
     */
    public function createSupplier(array $data): Supplier
    {
        return Supplier::create($data);
    }
    
    /**
     * 获取供应商详情
     *
     * @param int $id 供应商ID
     * @return Supplier
     */
    public function getSupplier(int $id): Supplier
    {
        return Supplier::with('purchaseOrders')->findOrFail($id);
    }
    
    /**
     * 更新供应商
     *
     * @param int $id 供应商ID
     * @param array $data 供应商数据
     * @return Supplier
     */
    public function updateSupplier(int $id, array $data): Supplier
    {
        $supplier = Supplier::findOrFail($id);
        $supplier->update($data);
        return $supplier;
    }
    
    /**
     * 删除供应商
     *
     * @param int $id 供应商ID
     * @return bool|null
     * @throws \Exception 如果有关联订单则抛出异常
     */
    public function deleteSupplier(int $id): ?bool
    {
        $supplier = Supplier::findOrFail($id);
        
        // 检查是否有关联的采购订单
        if ($supplier->purchaseOrders()->count() > 0) {
            throw new \Exception('无法删除，此供应商有关联的采购订单');
        }
        
        return $supplier->delete();
    }
    
    /**
     * 获取供应商的采购历史
     *
     * @param int $id 供应商ID
     * @param array $params 查询参数
     * @return LengthAwarePaginator
     */
    public function getSupplierPurchaseHistory(int $id, array $params): LengthAwarePaginator
    {
        $supplier = Supplier::findOrFail($id);
        
        $query = $supplier->purchaseOrders()
                 ->with(['items', 'warehouse'])
                 ->orderBy($params['order_by'] ?? 'order_date', $params['direction'] ?? 'desc');
        
        // 日期范围筛选
        if (isset($params['start_date'])) {
            $query->where('order_date', '>=', $params['start_date']);
        }
        
        if (isset($params['end_date'])) {
            $query->where('order_date', '<=', $params['end_date']);
        }
        
        // 状态筛选
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }
        
        // 返回分页结果
        return $query->paginate($params['per_page'] ?? 5);
    }
} 