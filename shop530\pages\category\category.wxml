<!-- 分类页面 - 完全重构版 -->
<view class="category-page">
  <!-- 搜索栏 -->
  <view class="search-container">
    <search-header 
      show="{{true}}"
      value="{{searchKeyword}}"
      placeholder="搜索商品"
      showSearchButton="{{true}}"
      buttonPosition="right"
      searchIcon="search"
      bind:search="handleSearch"
      bind:change="handleSearchChange"
      bind:click-input="handleSearchButtonTap"
      bind:search-button-click="handleSearchButtonTap"
      bind:clear="handleSearchClear"
    />
  </view>

  <!-- 主体内容 -->
  <view class="content-container">
    <!-- 左侧分类导航 -->
    <scroll-view class="category-nav" scroll-y enable-flex>
      <!-- 快捷入口 -->
      <view class="quick-entries">
        <view class="quick-entry {{currentCategory.type === 'frequent' ? 'active' : ''}}" 
              bindtap="handleQuickEntryTap" data-type="frequent">
          <text>常购清单</text>
        </view>
        <view class="quick-entry {{currentCategory.type === 'new' ? 'active' : ''}}" 
              bindtap="handleQuickEntryTap" data-type="new">
          <text>新品</text>
        </view>
        <view class="quick-entry {{currentCategory.type === 'sale' ? 'active' : ''}}" 
              bindtap="handleQuickEntryTap" data-type="sale">
          <text>特价</text>
        </view>
      </view>

      <!-- 分类列表 -->
      <view class="category-list">
        <block wx:for="{{categories}}" wx:key="id">
          <!-- 主分类 -->
          <view class="category-item {{activeCategoryId === item.id ? 'active' : ''}}" 
                bindtap="handleCategoryTap" 
                data-id="{{item.id}}" 
                data-type="main">
            <view class="category-main">
              <text class="category-name">{{item.name}}</text>
              <view wx:if="{{item.hasChildren}}" class="expand-icon {{item.isExpanded ? 'expanded' : ''}}">
                <view class="arrow"></view>
              </view>
            </view>
          </view>
          
          <!-- 子分类 -->
          <view class="subcategory-container {{item.isExpanded ? 'expanded' : ''}}" style="height: {{item.isExpanded && item.children_data.length ? (item.children_data.length * 40) + 'px' : '0'}}">
            <block wx:if="{{item.children_data && item.children_data.length > 0}}">
              <view wx:for="{{item.children_data}}" wx:key="id" wx:for-item="subItem"
                    class="subcategory-item {{activeSubCategoryId === subItem.id ? 'active' : ''}}"
                    bindtap="handleCategoryTap"
                    data-id="{{subItem.id}}"
                    data-parent-id="{{item.id}}"
                    data-type="sub">
                <text class="subcategory-name">{{subItem.name}}</text>
              </view>
            </block>
          </view>
        </block>
      </view>
    </scroll-view>

    <!-- 右侧商品区域 -->
    <view class="product-container">
      <!-- 三级分类横向滚动列表 -->
      <view wx:if="{{thirdCategories.length > 0}}" class="third-category-container">
        <scroll-view class="third-category-scroll" scroll-x enable-flex>
          <view class="third-category-list">
            <view wx:for="{{thirdCategories}}" wx:key="id"
                  class="third-category-item {{activeThirdCategoryId === item.id ? 'active' : ''}}"
                  bindtap="handleThirdCategoryTap"
                  data-id="{{item.id}}">
              <text class="third-category-name">{{item.name}}</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 排序栏 -->
      <view class="sort-bar">
        <view class="sort-item" bindtap="handleSortTap">
          <text>{{sortText}}</text>
          <view class="sort-icon"></view>
        </view>
        <view class="sort-item {{priceSort ? 'active' : ''}}" bindtap="handlePriceTap">
          <text>价格</text>
          <view class="price-sort-icon {{priceSort}}"></view>
        </view>
        <view class="sort-item" bindtap="handleFilterTap">
          <text>筛选</text>
          <view class="filter-icon"></view>
        </view>
      </view>

      <!-- 商品列表 -->
      <scroll-view class="product-list" scroll-y bindscrolltolower="loadMore" enable-flex>
        <!-- 加载中 -->
        <view wx:if="{{loading}}" class="loading-container">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 空状态 -->
        <view wx:elif="{{!loading && products.length === 0}}" class="empty-container">
          <van-icon name="info-o" size="80rpx" color="#cccccc" />
          <text class="empty-text">{{emptyMessage || '暂无商品'}}</text>
          <button wx:if="{{showLoginButton}}" class="login-button" bindtap="navigateToLogin">立即登录</button>
        </view>
        
        <!-- 商品列表 -->
        <view wx:else class="product-list-items">
          <block wx:for="{{products}}" wx:key="id">
            <view class="product-item" bindtap="handleProductTap" data-id="{{item.id}}" wx:if="{{item && item.id}}">
              <product-card-horizontal 
                product="{{item}}" 
                bind:addToCart="handleAddToCart"
                bind:productTap="handleProductCardTap"
              />
            </view>
          </block>
        </view>
        
        <!-- 加载更多 -->
        <view wx:if="{{loadingMore}}" class="loading-more">
          <view class="loading-spinner small"></view>
          <view class="loading-more-text">加载更多...</view>
        </view>
        
        <!-- 没有更多 -->
        <view wx:if="{{!loadingMore && !hasMore && products.length > 0}}" class="no-more">
          <text>没有更多了</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 购物车按钮 -->
  <view class="cart-button" bindtap="navigateToCart">
    <van-icon name="cart-o" size="48rpx" color="#ffffff" />
    <view wx:if="{{cartCount > 0}}" class="cart-badge">{{cartCount}}</view>
  </view>

  <!-- 购物车动画 -->
  <block wx:if="{{cartAnimation}}">
    <view class="cart-animation" style="left: {{cartAnimation.x}}px; top: {{cartAnimation.y}}px" animation="{{cartAnimation.animation}}">
      <view class="cart-animation-image">
        <van-icon name="cart-o" size="48rpx" color="#4CAF50" />
      </view>
    </view>
  </block>
</view> 