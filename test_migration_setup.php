<?php

/**
 * 数据迁移设置测试脚本
 * 运行此脚本检查迁移环境是否正确配置
 * 
 * 使用方法: php test_migration_setup.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;

// 初始化Laravel环境
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

class MigrationSetupTester
{
    private $results = [];
    private $errors = [];

    public function runAllTests()
    {
        echo "🔍 数据迁移环境检查\n";
        echo str_repeat('=', 50) . "\n\n";

        $this->testLaravelEnvironment();
        $this->testMigrationConfig();
        $this->testDatabaseConnections();
        $this->testRequiredTables();
        $this->testDefaultData();
        $this->testPermissions();

        $this->displayResults();
    }

    /**
     * 测试Laravel环境
     */
    private function testLaravelEnvironment()
    {
        $this->section('Laravel 环境检查');

        // 检查Laravel版本
        $version = app()->version();
        $this->test('Laravel版本', true, "版本: {$version}");

        // 检查APP_ENV
        $env = config('app.env');
        $this->test('应用环境', true, "环境: {$env}");

        // 检查数据库配置
        $dbConnection = config('database.default');
        $this->test('默认数据库连接', !empty($dbConnection), "连接: {$dbConnection}");

        // 检查必需的扩展
        $requiredExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'json'];
        foreach ($requiredExtensions as $ext) {
            $loaded = extension_loaded($ext);
            $this->test("PHP扩展: {$ext}", $loaded, $loaded ? '已加载' : '未加载');
        }
    }

    /**
     * 测试迁移配置
     */
    private function testMigrationConfig()
    {
        $this->section('迁移配置检查');

        // 检查配置文件是否存在
        $configFile = config_path('migration.php');
        $configExists = file_exists($configFile);
        $this->test('配置文件存在', $configExists, $configExists ? $configFile : '文件不存在');

        if ($configExists) {
            $config = config('migration');
            
            // 检查老系统数据库配置
            $oldSystemConfig = $config['old_system'] ?? [];
            $requiredKeys = ['host', 'database', 'username'];
            
            foreach ($requiredKeys as $key) {
                $hasKey = !empty($oldSystemConfig[$key]);
                $this->test("老系统DB配置: {$key}", $hasKey, 
                    $hasKey ? '已配置' : '未配置或为空');
            }

            // 检查字段映射配置
            $basicFields = $config['product_mapping']['basic_fields'] ?? [];
            $this->test('基础字段映射', !empty($basicFields), 
                '映射字段数: ' . count($basicFields));

            // 检查默认值配置
            $defaultValues = $config['product_mapping']['default_values'] ?? [];
            $this->test('默认值配置', !empty($defaultValues), 
                '默认值数: ' . count($defaultValues));
        }
    }

    /**
     * 测试数据库连接
     */
    private function testDatabaseConnections()
    {
        $this->section('数据库连接检查');

        // 测试新系统数据库连接
        try {
            DB::connection()->getPdo();
            $dbName = DB::connection()->getDatabaseName();
            $this->test('新系统数据库连接', true, "数据库: {$dbName}");
        } catch (Exception $e) {
            $this->test('新系统数据库连接', false, "错误: " . $e->getMessage());
        }

        // 测试老系统数据库连接
        try {
            $config = config('migration.old_system');
            if (empty($config['host']) || empty($config['database'])) {
                $this->test('老系统数据库连接', false, '配置信息不完整');
                return;
            }

            // 配置老系统连接
            Config::set('database.connections.old_system_test', [
                'driver' => 'mysql',
                'host' => $config['host'],
                'port' => $config['port'] ?? 3306,
                'database' => $config['database'],
                'username' => $config['username'],
                'password' => $config['password'],
                'charset' => $config['charset'] ?? 'utf8mb4',
                'collation' => $config['collation'] ?? 'utf8mb4_unicode_ci',
            ]);

            DB::connection('old_system_test')->getPdo();
            $oldDbName = DB::connection('old_system_test')->getDatabaseName();
            $this->test('老系统数据库连接', true, "数据库: {$oldDbName}");

            // 检查老系统商品表
            $oldProductsCount = DB::connection('old_system_test')->table('products')->count();
            $this->test('老系统商品表', true, "商品数量: {$oldProductsCount}");

        } catch (Exception $e) {
            $this->test('老系统数据库连接', false, "错误: " . $e->getMessage());
        }
    }

    /**
     * 测试必需的表
     */
    private function testRequiredTables()
    {
        $this->section('必需表检查');

        $requiredTables = [
            'products' => '商品表',
            'categories' => '分类表',
            'inventory' => '库存表',
            'warehouses' => '仓库表',
            'units' => '单位表',
        ];

        foreach ($requiredTables as $table => $description) {
            try {
                $exists = DB::getSchemaBuilder()->hasTable($table);
                if ($exists) {
                    $count = DB::table($table)->count();
                    $this->test($description, true, "记录数: {$count}");
                } else {
                    $this->test($description, false, '表不存在');
                }
            } catch (Exception $e) {
                $this->test($description, false, "错误: " . $e->getMessage());
            }
        }
    }

    /**
     * 测试默认数据
     */
    private function testDefaultData()
    {
        $this->section('默认数据检查');

        // 检查仓库数据
        try {
            $warehouseCount = DB::table('warehouses')->count();
            $this->test('仓库数据', $warehouseCount > 0, "仓库数量: {$warehouseCount}");
        } catch (Exception $e) {
            $this->test('仓库数据', false, "错误: " . $e->getMessage());
        }

        // 检查单位数据
        try {
            $unitCount = DB::table('units')->count();
            $defaultUnitId = config('migration.product_mapping.unit_mapping.default_unit_id', 13);
            $defaultUnitExists = DB::table('units')->where('id', $defaultUnitId)->exists();
            
            $this->test('单位数据', $unitCount > 0, "单位数量: {$unitCount}");
            $this->test('默认单位', $defaultUnitExists, "默认单位ID: {$defaultUnitId}");
        } catch (Exception $e) {
            $this->test('单位数据', false, "错误: " . $e->getMessage());
        }
    }

    /**
     * 测试文件权限
     */
    private function testPermissions()
    {
        $this->section('文件权限检查');

        $directories = [
            storage_path('migration_reports') => '迁移报告目录',
            storage_path('migration_backups') => '备份目录',
            storage_path('logs') => '日志目录',
        ];

        foreach ($directories as $dir => $description) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
            
            $writable = is_writable($dir);
            $this->test($description, $writable, $writable ? '可写' : '不可写');
        }

        // 检查配置文件是否可读
        $configFile = config_path('migration.php');
        $readable = is_readable($configFile);
        $this->test('配置文件可读性', $readable, $readable ? '可读' : '不可读');
    }

    // ================ 辅助方法 ================

    private function section($title)
    {
        echo "\n📋 {$title}\n";
        echo str_repeat('-', 30) . "\n";
    }

    private function test($name, $passed, $details = '')
    {
        $status = $passed ? '✅' : '❌';
        $this->results[] = ['name' => $name, 'passed' => $passed, 'details' => $details];
        
        echo sprintf("  %s %-25s %s\n", $status, $name, $details);
        
        if (!$passed) {
            $this->errors[] = $name;
        }
    }

    private function displayResults()
    {
        echo "\n\n🎯 检查结果汇总\n";
        echo str_repeat('=', 50) . "\n";

        $total = count($this->results);
        $passed = count(array_filter($this->results, fn($r) => $r['passed']));
        $failed = $total - $passed;

        echo "总检查项: {$total}\n";
        echo "通过项: {$passed}\n";
        echo "失败项: {$failed}\n";
        
        $successRate = $total > 0 ? round(($passed / $total) * 100, 1) : 0;
        echo "成功率: {$successRate}%\n";

        if ($failed > 0) {
            echo "\n⚠️  需要解决的问题:\n";
            foreach ($this->errors as $error) {
                echo "  • {$error}\n";
            }
            
            echo "\n💡 建议解决步骤:\n";
            $this->provideSuggestions();
        } else {
            echo "\n🎉 所有检查都通过了！您可以开始数据迁移。\n";
        }

        echo "\n📝 接下来的步骤:\n";
        echo "  1. 修改 config/migration.php 中的字段映射配置\n";
        echo "  2. 运行试迁移: php artisan migrate:products --dry-run --all\n";
        echo "  3. 检查试迁移报告\n";
        echo "  4. 执行正式迁移: php artisan migrate:products --all\n";
    }

    private function provideSuggestions()
    {
        foreach ($this->errors as $error) {
            switch (true) {
                case str_contains($error, '配置文件'):
                    echo "  • 创建 config/migration.php 配置文件\n";
                    break;
                case str_contains($error, '老系统数据库'):
                    echo "  • 检查 .env 文件中的老系统数据库配置\n";
                    break;
                case str_contains($error, '仓库数据'):
                    echo "  • 创建至少一个仓库记录\n";
                    break;
                case str_contains($error, '默认单位'):
                    echo "  • 检查并修正默认单位ID配置\n";
                    break;
                case str_contains($error, '权限'):
                    echo "  • 设置正确的文件/目录权限\n";
                    break;
            }
        }
    }
}

// 运行测试
try {
    $tester = new MigrationSetupTester();
    $tester->runAllTests();
} catch (Exception $e) {
    echo "❌ 测试运行失败: " . $e->getMessage() . "\n";
    echo "请检查Laravel环境配置。\n";
} 