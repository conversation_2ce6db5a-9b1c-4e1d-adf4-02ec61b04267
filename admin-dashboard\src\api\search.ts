import { get } from '@/utils/request'

/**
 * 搜索商品参数接口
 */
export interface SearchProductsParams {
  keyword: string;
  page?: number;
  pageSize?: number;
  categoryId?: number | string;
  sort?: string;
  order?: 'asc' | 'desc';
  minPrice?: number;
  maxPrice?: number;
  tags?: string[];
  includeOutOfStock?: boolean;
  clearCache?: boolean;
  status?: string | number;
}

/**
 * 搜索商品
 * @param params 搜索参数
 * @returns 搜索结果
 */
export const searchProducts = async (params: SearchProductsParams) => {
  try {
    // 验证必要参数
    if (!params.keyword) {
      throw new Error('关键词不能为空');
    }
    
    // 构建查询参数
    const urlParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        urlParams.append(key, String(value));
      }
    });
    
    // 添加时间戳防止缓存
    urlParams.append('_t', Date.now().toString());
    
    // 调用后端搜索API并直接返回结果
    return await get(`/search/products?${urlParams.toString()}`);
  } catch (error: any) {
    console.error('搜索商品失败:', error);
    throw error;
  }
};

/**
 * 获取热门搜索关键词
 * @param limit 关键词数量，默认10
 * @returns 热门关键词列表
 */
export const getHotKeywords = async (limit: number = 10) => {
  try {
    return await get(`/search/hot-keywords?limit=${limit}`);
  } catch (error: any) {
    console.error('获取热门关键词失败:', error);
    throw error;
  }
};

/**
 * 获取搜索建议
 * @param keyword 关键词
 * @param limit 建议数量，默认10
 * @returns 搜索建议列表
 */
export const getSuggestions = async (keyword: string, limit: number = 10) => {
  try {
    if (!keyword) {
      return { data: [] };
    }
    
    return await get(`/search/suggestions?keyword=${encodeURIComponent(keyword)}&limit=${limit}`);
  } catch (error: any) {
    console.error('获取搜索建议失败:', error);
    throw error;
  }
};

// 导出API
export default {
  searchProducts,
  getHotKeywords,
  getSuggestions
}; 