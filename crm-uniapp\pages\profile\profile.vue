<template>
	<view class="profile-container">
		<!-- 用户信息卡片 -->
		<view class="user-card">
			<view class="user-header">
				<view class="user-avatar">
					<text class="avatar-text">{{ (userInfo.name || 'U').charAt(0) }}</text>
				</view>
				<view class="user-info">
					<text class="user-name">{{ userInfo.name || '员工' }}</text>
					<text class="user-role">{{ getRoleText(userInfo.role) }}</text>
					<text class="user-department" v-if="userInfo.department">{{ userInfo.department }}</text>
					<text class="user-id">工号：{{ userInfo.employee_id || userInfo.id }}</text>
				</view>
				<button class="edit-btn" @tap="editProfile">编辑</button>
			</view>
		</view>
		
		<!-- 工作统计卡片 -->
		<view class="stats-card">
			<view class="card-title">工作统计</view>
			<view class="stats-grid">
				<view class="stat-item">
					<text class="stat-number">{{ workStats.today_orders || 0 }}</text>
					<text class="stat-label">今日订单</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ workStats.month_orders || 0 }}</text>
					<text class="stat-label">本月订单</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ workStats.total_clients || 0 }}</text>
					<text class="stat-label">服务客户</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ workStats.follow_ups || 0 }}</text>
					<text class="stat-label">跟进记录</text>
				</view>
			</view>
		</view>
		
		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-group">
				<view class="group-title">业务管理</view>
				<view class="menu-item" @tap="goToOrders">
					<view class="menu-icon orders">📋</view>
					<text class="menu-text">我的订单</text>
					<text class="menu-badge" v-if="workStats.pending_orders">{{ workStats.pending_orders }}</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @tap="goToClients">
					<view class="menu-icon clients">👥</view>
					<text class="menu-text">我的客户</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @tap="goToFollowUps">
					<view class="menu-icon followups">📝</view>
					<text class="menu-text">跟进记录</text>
					<text class="menu-arrow">></text>
				</view>
			</view>
			
			<view class="menu-group">
				<view class="group-title">系统设置</view>
				<view class="menu-item" @tap="changePassword">
					<view class="menu-icon password">🔒</view>
					<text class="menu-text">修改密码</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @tap="clearCache">
					<view class="menu-icon cache">🗑️</view>
					<text class="menu-text">清除缓存</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @tap="checkUpdate">
					<view class="menu-icon update">⬆️</view>
					<text class="menu-text">检查更新</text>
					<text class="menu-arrow">></text>
				</view>
			</view>
			
			<view class="menu-group">
				<view class="group-title">帮助与反馈</view>
				<view class="menu-item" @tap="showHelp">
					<view class="menu-icon help">❓</view>
					<text class="menu-text">使用帮助</text>
					<text class="menu-arrow">></text>
				</view>
				<view class="menu-item" @tap="showAbout">
					<view class="menu-icon about">ℹ️</view>
					<text class="menu-text">关于我们</text>
					<text class="menu-arrow">></text>
				</view>
			</view>
		</view>
		
		<!-- 退出登录按钮 -->
		<view class="logout-section">
			<button class="logout-btn" @tap="handleLogout">退出登录</button>
		</view>
		
		<!-- 版本信息 -->
		<view class="version-info">
			<text class="version-text">版本号：v1.0.0</text>
		</view>
		
		<!-- 修改密码弹窗 -->
		<view class="password-popup-mask" v-if="showPasswordPopup" @tap="closePasswordPopup">
			<view class="password-popup" @tap.stop>
				<view class="popup-header">
					<text class="popup-title">修改密码</text>
					<button class="popup-close" @tap="closePasswordPopup">×</button>
				</view>
				<view class="popup-content">
					<view class="form-item">
						<text class="form-label">当前密码</text>
						<input class="form-input" type="password" placeholder="请输入当前密码" v-model="passwordForm.oldPassword" />
					</view>
					<view class="form-item">
						<text class="form-label">新密码</text>
						<input class="form-input" type="password" placeholder="请输入新密码" v-model="passwordForm.newPassword" />
					</view>
					<view class="form-item">
						<text class="form-label">确认密码</text>
						<input class="form-input" type="password" placeholder="请再次输入新密码" v-model="passwordForm.confirmPassword" />
					</view>
				</view>
				<view class="popup-actions">
					<button class="action-btn cancel-btn" @tap="closePasswordPopup">取消</button>
					<button class="action-btn submit-btn" @tap="submitPassword">确认</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import authApi from '../../api/auth.js'
import config from '../../utils/config.js'

export default {
	data() {
		return {
			userInfo: {},
			workStats: {},
			loading: false,
			
			// 修改密码表单
			passwordForm: {
				oldPassword: '',
				newPassword: '',
				confirmPassword: ''
			},
			showPasswordPopup: false
		}
	},
	
	onLoad() {
		this.loadUserInfo()
		this.loadWorkStats()
	},
	
	onShow() {
		// 页面显示时刷新数据
		this.loadWorkStats()
	},
	
	onPullDownRefresh() {
		this.refreshData()
	},
	
	methods: {
		// 加载用户信息
		async loadUserInfo() {
			try {
				const employeeInfo = uni.getStorageSync(config.storageKeys.employeeInfo)
				if (employeeInfo) {
					this.userInfo = employeeInfo
				} else {
					// 从服务器获取最新信息
					const response = await authApi.getProfile()
					this.userInfo = response.data
					uni.setStorageSync(config.storageKeys.employeeInfo, response.data)
				}
			} catch (error) {
				console.error('加载用户信息失败:', error)
			}
		},
		
		// 加载工作统计
		async loadWorkStats() {
			try {
				// 这里应该调用统计API，暂时使用模拟数据
				this.workStats = {
					today_orders: 5,
					month_orders: 68,
					total_clients: 156,
					follow_ups: 23,
					pending_orders: 3
				}
			} catch (error) {
				console.error('加载工作统计失败:', error)
			}
		},
		
		// 刷新数据
		async refreshData() {
			await Promise.all([
				this.loadUserInfo(),
				this.loadWorkStats()
			])
			uni.stopPullDownRefresh()
		},
		
		// 编辑个人资料
		editProfile() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			})
		},
		
		// 跳转到订单管理
		goToOrders() {
			uni.switchTab({
				url: '/pages/orders/orders'
			})
		},
		
		// 跳转到客户管理
		goToClients() {
			uni.switchTab({
				url: '/pages/clients/clients'
			})
		},
		
		// 跳转到跟进记录
		goToFollowUps() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			})
		},
		
		// 修改密码
		changePassword() {
			this.showPasswordPopup = true
		},
		
		// 关闭密码弹窗
		closePasswordPopup() {
			this.showPasswordPopup = false
			this.passwordForm = {
				oldPassword: '',
				newPassword: '',
				confirmPassword: ''
			}
		},
		
		// 提交密码修改
		async submitPassword() {
			const { oldPassword, newPassword, confirmPassword } = this.passwordForm
			
			if (!oldPassword || !newPassword || !confirmPassword) {
				uni.showToast({
					title: '请填写完整信息',
					icon: 'none'
				})
				return
			}
			
			if (newPassword !== confirmPassword) {
				uni.showToast({
					title: '两次密码输入不一致',
					icon: 'none'
				})
				return
			}
			
			if (newPassword.length < 6) {
				uni.showToast({
					title: '密码长度不能少于6位',
					icon: 'none'
				})
				return
			}
			
			try {
				// 这里应该调用修改密码API
				// await authApi.changePassword({
				//     old_password: oldPassword,
				//     new_password: newPassword
				// })
				
				uni.showToast({
					title: '密码修改成功',
					icon: 'success'
				})
				
				this.closePasswordPopup()
			} catch (error) {
				console.error('修改密码失败:', error)
			}
		},
		
		// 清除缓存
		clearCache() {
			uni.showModal({
				title: '确认清除',
				content: '确定要清除应用缓存吗？',
				success: (res) => {
					if (res.confirm) {
						try {
							// 清除除了登录信息外的其他缓存
							const token = uni.getStorageSync(config.storageKeys.token)
							const userInfo = uni.getStorageSync(config.storageKeys.employeeInfo)
							
							uni.clearStorageSync()
							
							// 恢复登录信息
							uni.setStorageSync(config.storageKeys.token, token)
							uni.setStorageSync(config.storageKeys.employeeInfo, userInfo)
							
							uni.showToast({
								title: '缓存清除成功',
								icon: 'success'
							})
						} catch (error) {
							console.error('清除缓存失败:', error)
						}
					}
				}
			})
		},
		
		// 检查更新
		checkUpdate() {
			uni.showToast({
				title: '已是最新版本',
				icon: 'success'
			})
		},
		
		// 显示帮助
		showHelp() {
			uni.showModal({
				title: '使用帮助',
				content: '1. 代客下单：为客户创建订单\n2. 客户管理：查看和管理客户信息\n3. 订单管理：处理订单状态\n4. 跟进记录：记录客户沟通情况',
				showCancel: false
			})
		},
		
		// 显示关于
		showAbout() {
			uni.showModal({
				title: '关于我们',
				content: 'CRM管理系统 v1.0.0\n生鲜配送员工管理平台\n\n© 2024 生鲜配送CRM系统',
				showCancel: false
			})
		},
		
		// 处理退出登录
		handleLogout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							await authApi.logout()
						} catch (error) {
							console.error('退出登录失败:', error)
						} finally {
							// 清除本地数据
							uni.clearStorageSync()
							// 跳转到登录页
							uni.reLaunch({
								url: '/pages/login/login'
							})
						}
					}
				}
			})
		},
		
		// 获取角色文本
		getRoleText(role) {
			return config.roles[role] || role
		}
	}
}
</script>

<style scoped>
.profile-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 用户信息卡片 */
.user-card {
	background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 40rpx;
	color: #ffffff;
}

.user-header {
	display: flex;
	align-items: center;
}

.user-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 32rpx;
}

.avatar-text {
	color: #ffffff;
	font-size: 48rpx;
	font-weight: 600;
}

.user-info {
	flex: 1;
}

.user-name {
	display: block;
	font-size: 40rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
}

.user-role {
	display: block;
	font-size: 28rpx;
	opacity: 0.8;
	margin-bottom: 4rpx;
}

.user-department {
	display: block;
	font-size: 24rpx;
	opacity: 0.6;
	margin-bottom: 4rpx;
}

.user-id {
	display: block;
	font-size: 24rpx;
	opacity: 0.6;
}

.edit-btn {
	background: rgba(255, 255, 255, 0.2);
	color: #ffffff;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 12rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
}

/* 统计卡片 */
.stats-card {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 32rpx;
}

.stats-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 32rpx;
}

.stat-item {
	text-align: center;
	padding: 32rpx 16rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.stat-number {
	display: block;
	font-size: 48rpx;
	font-weight: 600;
	color: #007AFF;
	margin-bottom: 8rpx;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666666;
}

/* 菜单部分 */
.menu-section {
	margin: 20rpx;
}

.menu-group {
	background: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.group-title {
	font-size: 28rpx;
	color: #666666;
	padding: 24rpx 32rpx 16rpx;
	background: #f8f9fa;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 32rpx;
	border-bottom: 2rpx solid #f0f0f0;
	transition: all 0.3s ease;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-item:active {
	background: #f8f9fa;
}

.menu-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
	font-size: 40rpx;
}

.menu-icon.orders {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.menu-icon.clients {
	background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);
}

.menu-icon.followups {
	background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);
}

.menu-icon.password {
	background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);
}

.menu-icon.cache {
	background: linear-gradient(135deg, #ffa502 0%, #ff6348 100%);
}

.menu-icon.update {
	background: linear-gradient(135deg, #2ed573 0%, #7bed9f 100%);
}

.menu-icon.help {
	background: linear-gradient(135deg, #70a1ff 0%, #5352ed 100%);
}

.menu-icon.about {
	background: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
}

.menu-text {
	flex: 1;
	font-size: 32rpx;
	color: #333333;
}

.menu-badge {
	background: #ff4757;
	color: #ffffff;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 10rpx;
	margin-right: 16rpx;
}

.menu-arrow {
	font-size: 32rpx;
	color: #cccccc;
}

/* 退出登录 */
.logout-section {
	margin: 20rpx;
}

.logout-btn {
	width: 100%;
	background: #ff4757;
	color: #ffffff;
	border: none;
	border-radius: 16rpx;
	padding: 32rpx;
	font-size: 32rpx;
	font-weight: 600;
}

/* 版本信息 */
.version-info {
	text-align: center;
	padding: 32rpx;
}

.version-text {
	font-size: 24rpx;
	color: #999999;
}

/* 密码弹窗 */
.password-popup-mask {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
}

.password-popup {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	width: 600rpx;
}

.popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 32rpx;
}

.popup-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
}

.popup-close {
	background: none;
	border: none;
	font-size: 48rpx;
	color: #666666;
	padding: 0;
	width: 60rpx;
	height: 60rpx;
}

.form-item {
	margin-bottom: 32rpx;
}

.form-label {
	display: block;
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 16rpx;
}

.form-input {
	width: 100%;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	font-size: 32rpx;
	color: #333333;
	border: 2rpx solid transparent;
}

.form-input:focus {
	border-color: #007AFF;
	background: #ffffff;
}

.popup-actions {
	display: flex;
	gap: 16rpx;
	margin-top: 32rpx;
}

.action-btn {
	flex: 1;
	border: none;
	border-radius: 12rpx;
	padding: 24rpx;
	font-size: 32rpx;
}

.cancel-btn {
	background: #f8f9fa;
	color: #666666;
}

.submit-btn {
	background: #007AFF;
	color: #ffffff;
}
</style> 