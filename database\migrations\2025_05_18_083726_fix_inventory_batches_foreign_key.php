<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inventory_batches', function (Blueprint $table) {
            // 先删除可能存在的错误外键约束
            $foreignKeys = $this->listTableForeignKeys('inventory_batches');
            if (in_array('inventory_batches_inventory_id_foreign', $foreignKeys)) {
                $table->dropForeign('inventory_batches_inventory_id_foreign');
            }
            
            // 添加正确的外键引用inventory表
            $table->foreign('inventory_id')->references('id')->on('inventory')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inventory_batches', function (Blueprint $table) {
            $table->dropForeign(['inventory_id']);
        });
    }
    
    /**
     * 获取表的所有外键约束名称
     *
     * @param string $table
     * @return array
     */
    private function listTableForeignKeys($table)
    {
        $conn = Schema::getConnection()->getDoctrineSchemaManager();
        
        $foreignKeys = [];
        try {
            $tableDetails = $conn->listTableDetails($table);
            foreach ($tableDetails->getForeignKeys() as $foreignKey) {
                $foreignKeys[] = $foreignKey->getName();
            }
        } catch (\Exception $e) {
            // 表可能不存在或其他错误，返回空数组
        }
        
        return $foreignKeys;
    }
};
