<?php

namespace App\Crm\Models;

use App\Models\User;
use App\Employee\Models\Employee;
use App\Crm\Models\ClientFollowUp;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CrmAgent extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'employee_id',
        'service_area',
        'max_clients',
        'performance_rating',
        'status',
        'specialty',
        'monthly_target',
        'clients_count',
    ];

    /**
     * 属性转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'service_area' => 'array',
        'performance_rating' => 'decimal:2',
        'max_clients' => 'integer',
        'monthly_target' => 'integer',
        'clients_count' => 'integer',
    ];

    /**
     * 获取关联的员工
     */
    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * 获取专员管理的客户列表
     */
    public function clients()
    {
        return $this->hasMany(User::class, 'crm_agent_id', 'employee_id');
    }

    /**
     * 获取活跃客户数
     */
    public function activeClients()
    {
        return $this->clients()->where('status', 'active');
    }

    /**
     * 获取专员的跟进记录
     */
    public function followUps()
    {
        return $this->hasMany(ClientFollowUp::class, 'employee_id', 'employee_id');
    }

    /**
     * 检查是否可以分配新客户
     */
    public function canAcceptClients()
    {
        if ($this->status !== 'available') {
            return false;
        }

        return $this->clients_count < $this->max_clients;
    }

    /**
     * 更新客户计数
     */
    public function updateClientsCount()
    {
        $this->clients_count = $this->clients()->count();
        $this->save();
    }
} 