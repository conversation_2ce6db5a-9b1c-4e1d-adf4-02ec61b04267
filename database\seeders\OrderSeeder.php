<?php

namespace Database\Seeders;

use App\Delivery\Models\Delivery;
use App\Employee\Models\Employee;
use App\Order\Models\Order;
use App\Order\Models\OrderItem;
use App\Product\Models\Product;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清除现有订单相关数据
        $this->command->info('开始清除现有订单数据...');
        try {
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
            
            // 删除相关的交付记录
            $deliveryCount = DB::table('deliveries')->count();
            DB::table('deliveries')->delete();
            $this->command->info("已删除 {$deliveryCount} 条配送记录");
            
            // 删除订单项和订单
            $orderItemCount = OrderItem::count();
            OrderItem::query()->delete();
            $this->command->info("已删除 {$orderItemCount} 条订单项记录");
            
            $orderCount = Order::count();
            Order::query()->delete();
            $this->command->info("已删除 {$orderCount} 条订单记录");
            
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        } catch (\Exception $e) {
            $this->command->error('清除数据时出错: ' . $e->getMessage());
            return;
        }
        
        // 获取用户数据（消费者角色）
        $this->command->info('正在获取用户数据...');
        $customers = User::limit(20)->get();
        
        if ($customers->isEmpty()) {
            $this->command->error('没有找到用户数据，无法创建订单');
            return;
        }
        
        $this->command->info('找到 ' . $customers->count() . ' 个用户');
        
        // 获取配送员数据（从员工表中获取）
        $this->command->info('正在获取配送员数据...');
        $deliverers = Employee::where('role', 'delivery')->get();
        
        if ($deliverers->isEmpty()) {
            $this->command->info('没有找到配送员数据，使用随机员工作为配送员');
            $deliverers = Employee::limit(5)->get();
            if ($deliverers->isEmpty()) {
                $this->command->error('没有找到员工数据，无法分配配送员');
                // 继续创建订单，但不分配配送员
            }
        }
        
        $this->command->info('找到 ' . $deliverers->count() . ' 个配送员');
        
        // 获取产品数据
        $this->command->info('正在获取产品数据...');
        $products = Product::get();
        
        if ($products->isEmpty()) {
            $this->command->error('没有找到产品数据，无法创建订单');
            return;
        }
        
        $this->command->info('找到 ' . $products->count() . ' 个产品');
        
        // 如果产品数量太少，创建一些测试产品
        if ($products->count() < 5) {
            $this->command->info('产品数量不足，正在创建一些测试产品...');
            
            // 获取默认分类
            $categoryId = DB::table('categories')->min('id') ?? 1;
            
            try {
                for ($i = 1; $i <= 10; $i++) {
                    Product::create([
                        'name' => '测试商品' . $i,
                        'description' => '这是测试商品描述' . $i,
                        'price' => rand(10, 1000) / 10,
                        'stock' => rand(50, 200),
                        'category_id' => $categoryId,
                        'status' => 1,
                    ]);
                }
                
                // 重新获取产品
                $products = Product::get();
                $this->command->info('现在有 ' . $products->count() . ' 个产品');
            } catch (\Exception $e) {
                $this->command->error('创建测试产品时出错: ' . $e->getMessage());
                // 继续使用现有产品
            }
        }
        
        // 订单状态和对应比例
        $statusDistribution = [
            'pending' => 15,   // 15%
            'paid' => 20,      // 20%
            'shipped' => 25,   // 25%
            'delivered' => 30, // 30%
            'cancelled' => 10  // 10%
        ];
        
        // 支付方式
        $paymentMethods = ['wechat', 'alipay', 'bank', 'cash'];
        
        // 地址信息库
        $cities = ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '成都市', '重庆市', '武汉市', '西安市'];
        $districts = ['朝阳区', '浦东新区', '天河区', '福田区', '西湖区', '鼓楼区', '武侯区', '渝中区', '洪山区', '雁塔区'];
        $streets = ['人民路', '中山路', '解放大道', '东风路', '建设路', '和平街', '长安街', '南京路', '北京路', '天安门'];
        
        // 创建订单数量
        $orderAmount = 30;
        $this->command->info("开始创建 {$orderAmount} 个测试订单...");
        
        // 创建测试订单
        DB::beginTransaction();
        try {
            $createdCount = 0;
            
            for ($i = 1; $i <= $orderAmount; $i++) {
                try {
                    // 随机选择用户
                    $customer = $customers->random();
                    
                    // 随机状态（基于分布）
                    $status = $this->getRandomStatus($statusDistribution);
                    
                    // 生成地址
                    $city = $cities[array_rand($cities)];
                    $district = $districts[array_rand($districts)];
                    $street = $streets[array_rand($streets)];
                    $address = "{$city}{$district}{$street}" . rand(1, 100) . "号" . rand(1, 30) . "楼" . rand(101, 2505) . "室";
                    
                    // 生成订单号
                    $orderNo = date('YmdHis') . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
                    
                    // 生成随机日期（过去30天内）
                    $createdAt = Carbon::now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59));
                    
                    // 创建订单
                    $order = new Order([
                        'user_id' => $customer->id,
                        'order_no' => $orderNo,
                        'status' => $status,
                        'shipping_address' => $address,
                        'contact_name' => $customer->name ?? "联系人{$i}",
                        'contact_phone' => $customer->phone ?? "1388888" . str_pad($i, 4, '0', STR_PAD_LEFT),
                        'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                        'notes' => rand(0, 1) ? "这是订单#{$i}的备注信息，请于" . rand(8, 21) . "点前送达" : null,
                        'total' => 0, // 初始设为0，稍后更新
                    ]);
                    
                    // 设置创建时间
                    $order->created_at = $createdAt;
                    $order->updated_at = $createdAt;
                    
                    // 设置状态对应的时间
                    if ($status !== 'pending') {
                        $paidAt = (clone $createdAt)->addHours(rand(1, 24));
                        $order->paid_at = $paidAt;
                        $order->updated_at = $paidAt;
                        
                        if ($status === 'shipped' || $status === 'delivered') {
                            $shippedAt = (clone $paidAt)->addHours(rand(2, 48));
                            $order->shipped_at = $shippedAt;
                            $order->updated_at = $shippedAt;
                            
                            if ($status === 'delivered') {
                                $deliveredAt = (clone $shippedAt)->addHours(rand(1, 24));
                                $order->delivered_at = $deliveredAt;
                                $order->updated_at = $deliveredAt;
                            }
                        } else if ($status === 'cancelled') {
                            $cancelledAt = (clone $createdAt)->addHours(rand(1, 3));
                            $order->cancelled_at = $cancelledAt;
                            $order->updated_at = $cancelledAt;
                        }
                    }
                    
                    $order->save();
                    
                    // 随机生成1-5个订单项
                    $itemCount = rand(1, 5);
                    $orderTotal = 0;
                    
                    // 选择不重复的随机产品
                    $selectedProducts = $products->random(min($itemCount, $products->count()));
                    
                    foreach ($selectedProducts as $product) {
                        // 随机数量
                        $quantity = rand(1, 5);
                        $price = $product->price;
                        $itemTotal = $price * $quantity;
                        $orderTotal += $itemTotal;
                        
                        // 创建订单项
                        OrderItem::create([
                            'order_id' => $order->id,
                            'product_id' => $product->id,
                            'product_name' => $product->name,
                            'product_sku' => 'SKU-' . $product->id . '-' . rand(100, 999),
                            'quantity' => $quantity,
                            'price' => $price,
                            'total' => $itemTotal,
                            'created_at' => $order->created_at,
                            'updated_at' => $order->created_at,
                        ]);
                    }
                    
                    // 更新订单总金额
                    $order->total = $orderTotal;
                    $order->save();
                    
                    // 为已付款、已发货或已送达的订单创建配送记录
                    if (in_array($status, ['paid', 'shipped', 'delivered']) && !$deliverers->isEmpty()) {
                        // 确定配送状态
                        $deliveryStatus = 'pending'; // 默认为待配送
                        
                        if ($status === 'shipped') {
                            $deliveryStatus = 'in_progress'; // 已发货的订单配送中
                        } else if ($status === 'delivered') {
                            $deliveryStatus = 'completed'; // 已送达的订单配送完成
                        }
                        
                        // 随机分配配送员
                        $delivererId = null;
                        if (in_array($deliveryStatus, ['in_progress', 'completed'])) {
                            $delivererId = $deliverers->random()->id;
                        }
                        
                        // 创建配送记录
                        Delivery::create([
                            'order_id' => $order->id,
                            'status' => $deliveryStatus,
                            'employee_deliverer_id' => $delivererId,
                            'created_at' => $order->paid_at,
                            'updated_at' => $status === 'delivered' ? $order->delivered_at : ($status === 'shipped' ? $order->shipped_at : $order->paid_at),
                        ]);
                    }
                    
                    $createdCount++;
                    
                    if ($createdCount % 5 == 0) {
                        $this->command->info("已创建 {$createdCount} 个订单...");
                    }
                    
                } catch (\Exception $e) {
                    $this->command->warn("创建第 {$i} 个订单时遇到问题: " . $e->getMessage());
                    Log::warning("OrderSeeder: 创建第 {$i} 个订单失败", [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    // 继续创建下一个订单
                    continue;
                }
            }
            
            DB::commit();
            $this->command->info("成功创建了 {$createdCount} 个测试订单及相关数据");
        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error('创建订单失败: ' . $e->getMessage());
            Log::error('OrderSeeder失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 根据权重分布获取随机状态
     * 
     * @param array $distribution
     * @return string
     */
    private function getRandomStatus(array $distribution)
    {
        $rand = rand(1, 100);
        $cumulativeWeight = 0;
        
        foreach ($distribution as $status => $weight) {
            $cumulativeWeight += $weight;
            if ($rand <= $cumulativeWeight) {
                return $status;
            }
        }
        
        // 默认返回第一个状态
        return array_key_first($distribution);
    }
}
