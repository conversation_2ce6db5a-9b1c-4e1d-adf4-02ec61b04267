<?php

namespace App\Crm\Services;

use App\Crm\Models\UserAddress;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserAddressService
{
    /**
     * 获取用户地址列表
     *
     * @param int $userId
     * @param Request $request
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Pagination\LengthAwarePaginator
     */
    public function getUserAddresses($userId, Request $request)
    {
        $query = UserAddress::where('user_id', $userId);
        
        // 排序
        if ($request->has('sort') && $request->sort) {
            $direction = $request->has('direction') && $request->direction === 'desc' ? 'desc' : 'asc';
            $query->orderBy($request->sort, $direction);
        } else {
            $query->orderByDesc('is_default')->orderByDesc('created_at');
        }
        
        // 返回分页或全部数据
        if ($request->has('per_page')) {
            return $query->paginate($request->per_page);
        }
        
        return $query->get();
    }
    
    /**
     * 获取地址详情
     *
     * @param int $id
     * @return UserAddress
     */
    public function getAddress($id)
    {
        return UserAddress::findOrFail($id);
    }
    
    /**
     * 创建用户地址
     *
     * @param array $data
     * @return UserAddress
     */
    public function createAddress(array $data)
    {
        DB::beginTransaction();
        
        try {
            // 验证用户存在
            $user = User::findOrFail($data['user_id']);
            
            // 如果设置为默认地址，需要将该用户的其他地址设为非默认
            if (isset($data['is_default']) && $data['is_default']) {
                UserAddress::where('user_id', $user->id)
                    ->update(['is_default' => false]);
            }
            
            // 创建地址
            $address = new UserAddress();
            $address->user_id = $user->id;
            $address->contact_name = $data['contact_name'];
            $address->contact_phone = $data['contact_phone'];
            $address->province = $data['province'] ?? null;
            $address->city = $data['city'] ?? null;
            $address->district = $data['district'] ?? null;
            $address->address = $data['address'];
            $address->postal_code = $data['postal_code'] ?? null;
            $address->notes = $data['notes'] ?? null;
            $address->is_default = $data['is_default'] ?? false;
            $address->save();
            
            DB::commit();
            return $address;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建用户地址失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 更新用户地址
     *
     * @param int $id
     * @param array $data
     * @return UserAddress
     */
    public function updateAddress($id, array $data)
    {
        DB::beginTransaction();
        
        try {
            $address = UserAddress::findOrFail($id);
            
            // 如果设置为默认地址，需要将该用户的其他地址设为非默认
            if (isset($data['is_default']) && $data['is_default'] && !$address->is_default) {
                UserAddress::where('user_id', $address->user_id)
                    ->where('id', '!=', $address->id)
                    ->update(['is_default' => false]);
            }
            
            // 更新地址信息
            if (isset($data['contact_name'])) {
                $address->contact_name = $data['contact_name'];
            }
            
            if (isset($data['contact_phone'])) {
                $address->contact_phone = $data['contact_phone'];
            }
            
            if (isset($data['province'])) {
                $address->province = $data['province'];
            }
            
            if (isset($data['city'])) {
                $address->city = $data['city'];
            }
            
            if (isset($data['district'])) {
                $address->district = $data['district'];
            }
            
            if (isset($data['address'])) {
                $address->address = $data['address'];
            }
            
            if (isset($data['postal_code'])) {
                $address->postal_code = $data['postal_code'];
            }
            
            if (isset($data['notes'])) {
                $address->notes = $data['notes'];
            }
            
            if (isset($data['is_default'])) {
                $address->is_default = $data['is_default'];
            }
            
            $address->save();
            
            DB::commit();
            return $address;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('更新用户地址失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 删除用户地址
     *
     * @param int $id
     * @return bool
     */
    public function deleteAddress($id)
    {
        $address = UserAddress::findOrFail($id);
        
        // 如果是默认地址，删除后需要重新设置默认地址
        $wasDefault = $address->is_default;
        $userId = $address->user_id;
        
        $address->delete();
        
        // 如果删除的是默认地址，重新设置默认地址
        if ($wasDefault) {
            $newDefault = UserAddress::where('user_id', $userId)->orderByDesc('created_at')->first();
            if ($newDefault) {
                $newDefault->is_default = true;
                $newDefault->save();
            }
        }
        
        return true;
    }
    
    /**
     * 设置为默认地址
     *
     * @param int $id
     * @return UserAddress
     */
    public function setAsDefault($id)
    {
        DB::beginTransaction();
        
        try {
            $address = UserAddress::findOrFail($id);
            
            // 将该用户的所有地址设为非默认
            UserAddress::where('user_id', $address->user_id)
                ->update(['is_default' => false]);
            
            // 设置当前地址为默认
            $address->is_default = true;
            $address->save();
            
            DB::commit();
            return $address;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('设置默认地址失败: ' . $e->getMessage());
            throw $e;
        }
    }
} 