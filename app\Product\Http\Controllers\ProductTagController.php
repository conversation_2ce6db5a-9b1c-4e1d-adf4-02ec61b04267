<?php

namespace App\Product\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Product\Models\ProductTag;
use App\Product\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class ProductTagController extends Controller
{
    /**
     * 获取标签列表
     */
    public function index(Request $request)
    {
        try {
            $query = ProductTag::query();
            
            // 搜索
            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('slug', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }
            
            // 按状态筛选
            if ($request->filled('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }
            
            // 是否在筛选中显示
            if ($request->filled('show_in_filter')) {
                $query->where('show_in_filter', $request->boolean('show_in_filter'));
            }
            
            // 排序
            $sortBy = $request->input('sort_by', 'sort_order');
            $sortOrder = $request->input('sort_order', 'asc');
            $query->orderBy($sortBy, $sortOrder);
            
            // 是否包含商品数量统计
            if ($request->boolean('with_product_count')) {
                $query->withCount('products');
            }
            
            // 分页
            $perPage = $request->input('per_page', 15);
            if ($request->boolean('no_pagination')) {
                $tags = $query->get();
                return response()->json(ApiResponse::success($tags));
            } else {
                $tags = $query->paginate($perPage);
                return response()->json(ApiResponse::success($tags));
            }
        } catch (\Exception $e) {
            Log::error('获取标签列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(ApiResponse::error('获取标签列表失败'), 500);
        }
    }
    
    /**
     * 创建标签
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:product_tags,name',
                'slug' => 'nullable|string|max:255|unique:product_tags,slug',
                'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'background_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'icon' => 'nullable|string|max:50',
                'description' => 'nullable|string',
                'sort_order' => 'integer|min:0',
                'is_active' => 'boolean',
                'show_in_filter' => 'boolean',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $data = $request->all();
            
            // 设置默认值
            $data['color'] = $data['color'] ?? '#ffffff';
            $data['background_color'] = $data['background_color'] ?? '#666666';
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['is_active'] = $data['is_active'] ?? true;
            $data['show_in_filter'] = $data['show_in_filter'] ?? true;
            
            $tag = ProductTag::create($data);
            
            return response()->json(ApiResponse::success($tag, '标签创建成功'), 201);
        } catch (\Exception $e) {
            Log::error('创建标签失败', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            return response()->json(ApiResponse::error('创建标签失败'), 500);
        }
    }
    
    /**
     * 获取标签详情
     */
    public function show($id)
    {
        try {
            $tag = ProductTag::with(['products' => function ($query) {
                $query->select('id', 'name', 'price', 'status')
                      ->where('status', 1)
                      ->limit(10);
            }])->findOrFail($id);
            
            // 添加商品数量统计
            $tag->product_count = $tag->products()->count();
            
            return response()->json(ApiResponse::success($tag));
        } catch (\Exception $e) {
            Log::error('获取标签详情失败', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取标签详情失败'), 500);
        }
    }
    
    /**
     * 更新标签
     */
    public function update(Request $request, $id)
    {
        try {
            $tag = ProductTag::findOrFail($id);
            
            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|string|max:255|unique:product_tags,name,' . $id,
                'slug' => 'sometimes|string|max:255|unique:product_tags,slug,' . $id,
                'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'background_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'icon' => 'nullable|string|max:50',
                'description' => 'nullable|string',
                'sort_order' => 'integer|min:0',
                'is_active' => 'boolean',
                'show_in_filter' => 'boolean',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $tag->update($request->all());
            
            // 如果标签被禁用，需要更新相关商品的缓存
            if ($request->has('is_active') && !$request->boolean('is_active')) {
                $this->updateProductsTagsCache($tag->id);
            }
            
            return response()->json(ApiResponse::success($tag, '标签更新成功'));
        } catch (\Exception $e) {
            Log::error('更新标签失败', [
                'id' => $id,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            return response()->json(ApiResponse::error('更新标签失败'), 500);
        }
    }
    
    /**
     * 删除标签
     */
    public function destroy($id)
    {
        try {
            $tag = ProductTag::findOrFail($id);
            
            // 检查是否有商品使用了该标签
            $productCount = $tag->products()->count();
            if ($productCount > 0) {
                return response()->json(ApiResponse::error("该标签被 {$productCount} 个商品使用，无法删除"), 422);
            }
            
            $tag->delete();
            
            return response()->json(ApiResponse::success(null, '标签删除成功'));
        } catch (\Exception $e) {
            Log::error('删除标签失败', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('删除标签失败'), 500);
        }
    }
    
    /**
     * 批量更新标签排序
     */
    public function updateSort(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'tags' => 'required|array',
                'tags.*.id' => 'required|integer|exists:product_tags,id',
                'tags.*.sort_order' => 'required|integer|min:0',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            DB::transaction(function () use ($request) {
                foreach ($request->input('tags') as $tagData) {
                    ProductTag::where('id', $tagData['id'])
                             ->update(['sort_order' => $tagData['sort_order']]);
                }
            });
            
            return response()->json(ApiResponse::success(null, '排序更新成功'));
        } catch (\Exception $e) {
            Log::error('更新标签排序失败', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            return response()->json(ApiResponse::error('更新排序失败'), 500);
        }
    }
    
    /**
     * 获取标签统计信息
     */
    public function getStats()
    {
        try {
            $stats = [
                'total_tags' => ProductTag::count(),
                'active_tags' => ProductTag::where('is_active', true)->count(),
                'filter_tags' => ProductTag::where('show_in_filter', true)->count(),
                'most_used_tags' => ProductTag::withCount('products')
                                              ->orderBy('products_count', 'desc')
                                              ->limit(5)
                                              ->get(['id', 'name', 'products_count']),
            ];
            
            return response()->json(ApiResponse::success($stats));
        } catch (\Exception $e) {
            Log::error('获取标签统计失败', [
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取统计信息失败'), 500);
        }
    }
    
    /**
     * 获取商品的标签
     */
    public function getProductTags($productId)
    {
        try {
            $product = Product::findOrFail($productId);
            $tags = $product->tags()->ordered()->get();
            
            return response()->json(ApiResponse::success($tags));
        } catch (\Exception $e) {
            Log::error('获取商品标签失败', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取商品标签失败'), 500);
        }
    }
    
    /**
     * 设置商品标签
     */
    public function setProductTags(Request $request, $productId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'tag_ids' => 'required|array',
                'tag_ids.*' => 'integer|exists:product_tags,id',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $product = Product::findOrFail($productId);
            $tagIds = $request->input('tag_ids', []);
            
            // 同步标签
            $product->syncTags($tagIds);
            
            // 获取更新后的标签
            $tags = $product->tags()->ordered()->get();
            
            return response()->json(ApiResponse::success($tags, '商品标签更新成功'));
        } catch (\Exception $e) {
            Log::error('设置商品标签失败', [
                'product_id' => $productId,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            return response()->json(ApiResponse::error('设置商品标签失败'), 500);
        }
    }
    
    /**
     * 获取热门标签（用于前端筛选）
     */
    public function getPopularTags()
    {
        try {
            $tags = ProductTag::active()
                             ->showInFilter()
                             ->withCount('products')
                             ->having('products_count', '>', 0)
                             ->orderBy('products_count', 'desc')
                             ->orderBy('sort_order', 'asc')
                             ->limit(20)
                             ->get();
            
            return response()->json(ApiResponse::success($tags));
        } catch (\Exception $e) {
            Log::error('获取热门标签失败', [
                'error' => $e->getMessage()
            ]);
            return response()->json(ApiResponse::error('获取热门标签失败'), 500);
        }
    }
    
    /**
     * 更新商品标签缓存
     */
    protected function updateProductsTagsCache($tagId)
    {
        try {
            $products = Product::whereHas('tags', function ($query) use ($tagId) {
                $query->where('product_tags.id', $tagId);
            })->get();
            
            foreach ($products as $product) {
                $product->updateTagsCache();
            }
        } catch (\Exception $e) {
            Log::error('更新商品标签缓存失败', [
                'tag_id' => $tagId,
                'error' => $e->getMessage()
            ]);
        }
    }
} 