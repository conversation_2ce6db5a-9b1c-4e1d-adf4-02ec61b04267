<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 先更新products表，添加base_unit_id字段
        Schema::table('products', function (Blueprint $table) {
            // 只有在base_unit_id字段不存在时才添加
            if (!Schema::hasColumn('products', 'base_unit_id')) {
                $table->foreignId('base_unit_id')->nullable()->after('stock')->constrained('units')->onDelete('set null');
            }
            
            // 将原base_unit字段的值复制到base_unit_id (需要在数据库中已有数据的情况下执行)
            // 这一步可能需要在一个seeder或命令中完成，这里只是一个示例
            // DB::statement('UPDATE products p JOIN units u ON p.base_unit = u.name SET p.base_unit_id = u.id');
        });
        
        // 创建product_units表 (如果不存在)
        if (!Schema::hasTable('product_units')) {
            Schema::create('product_units', function (Blueprint $table) {
                $table->id();
                $table->foreignId('product_id')->constrained()->onDelete('cascade');
                $table->foreignId('unit_id')->constrained()->onDelete('cascade');
                $table->decimal('conversion_rate', 15, 6)->comment('与基本单位的换算比率');
                $table->boolean('is_sale_default')->default(false)->comment('是否为销售默认单位');
                $table->boolean('is_purchase_default')->default(false)->comment('是否为采购默认单位');
                $table->boolean('is_inventory_default')->default(false)->comment('是否为库存默认单位');
                $table->timestamps();
                
                // 每个商品不能有重复的辅助单位
                $table->unique(['product_id', 'unit_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 只删除product_units表，不删除base_unit_id列(因为它可能是由其他迁移创建的)
        Schema::dropIfExists('product_units');
    }
}; 