<?php

namespace App\Order\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class OrderMerge extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'merged_order_id',
        'original_order_ids',
        'merge_strategy',
        'merge_date',
        'original_total_amount',
        'merged_total_amount',
        'total_savings',
        'original_discount_info',
        'merged_discount_info',
        'discount_comparison',
        'merged_by',
        'merge_type',
        'merge_reason',
        'notes',
        'status',
        'reverted_at',
        'reverted_by',
    ];

    protected $casts = [
        'original_order_ids' => 'array',
        'merge_date' => 'date',
        'original_total_amount' => 'decimal:2',
        'merged_total_amount' => 'decimal:2',
        'total_savings' => 'decimal:2',
        'original_discount_info' => 'array',
        'merged_discount_info' => 'array',
        'discount_comparison' => 'array',
        'reverted_at' => 'datetime',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联合并后的订单
     */
    public function mergedOrder()
    {
        return $this->belongsTo(Order::class, 'merged_order_id');
    }

    /**
     * 关联原订单列表
     */
    public function originalOrders()
    {
        return $this->hasManyThrough(
            Order::class,
            'App\Order\Models\OrderMergeItem', // 这里需要一个中间表，但我们先用简化方式
            'merge_id',
            'id',
            'id',
            'order_id'
        );
    }

    /**
     * 获取原订单列表（访问器）
     */
    public function getOriginalOrdersAttribute()
    {
        return Order::whereIn('id', $this->original_order_ids)->get();
    }

    /**
     * 获取原订单查询构建器
     */
    public function originalOrdersQuery()
    {
        return Order::whereIn('id', $this->original_order_ids);
    }

    /**
     * 关联操作人
     */
    public function mergedBy()
    {
        return $this->belongsTo(User::class, 'merged_by');
    }

    /**
     * 关联撤销人
     */
    public function revertedBy()
    {
        return $this->belongsTo(User::class, 'reverted_by');
    }

    /**
     * 检查是否已撤销
     */
    public function isReverted(): bool
    {
        return $this->status === 'reverted';
    }

    /**
     * 获取节省百分比
     */
    public function getSavingsPercentage(): float
    {
        if ($this->original_total_amount <= 0) {
            return 0;
        }

        return round(($this->total_savings / $this->original_total_amount) * 100, 2);
    }

    /**
     * 获取合并策略文本
     */
    public function getStrategyText(): string
    {
        $strategies = [
            'intelligent' => '智能重算',
            'conservative' => '保持原价',
            'hybrid' => '混合优化',
        ];

        return $strategies[$this->merge_strategy] ?? $this->merge_strategy;
    }

    /**
     * 获取合并类型文本
     */
    public function getTypeText(): string
    {
        $types = [
            'manual' => '手动合并',
            'auto' => '自动合并',
        ];

        return $types[$this->merge_type] ?? $this->merge_type;
    }

    /**
     * 获取原订单数量
     */
    public function getOriginalOrderCount(): int
    {
        return count($this->original_order_ids);
    }

    /**
     * 获取优惠对比摘要
     */
    public function getDiscountSummary(): array
    {
        $comparison = $this->discount_comparison;
        
        return [
            'original_discount' => $comparison['original']['product_discount'] + $comparison['original']['payment_discount'],
            'merged_discount' => $comparison['merged']['product_discount'] + $comparison['merged']['payment_discount'],
            'savings' => $comparison['savings']['amount'],
            'savings_percentage' => $comparison['savings']['percentage'],
        ];
    }
} 