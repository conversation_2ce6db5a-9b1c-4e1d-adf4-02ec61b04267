<?php

namespace App\Crm\Services;

use App\Crm\Models\CustomerBehaviorAnalytics;
use App\Crm\Models\UserSession;
use App\Crm\Models\BehaviorStatistics;
use App\Models\User;
use App\Order\Models\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Illuminate\Support\Carbon;

class BehaviorAnalyticsService
{
    /**
     * 获取行为分析概览数据
     * 
     * @param int|null $crmAgentId CRM专员ID，用于权限控制
     * @param array $params 查询参数
     * @return array
     */
    public function getBehaviorOverview(?int $crmAgentId = null, array $params = []): array
    {
        $userIds = $this->getAuthorizedUserIds($crmAgentId);
        $startDate = $params['start_date'] ?? Carbon::now()->subDays(30)->format('Y-m-d');
        $endDate = $params['end_date'] ?? Carbon::now()->format('Y-m-d');

        // 活跃用户数（有订单的用户）
        $activeUsers = Order::whereIn('user_id', $userIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->distinct('user_id')
            ->count();

        // 平均客单价
        $avgOrderValue = Order::whereIn('user_id', $userIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->avg('total') ?? 0;

        // 复购率（有多次订单的用户比例）
        $totalUsers = User::whereIn('id', $userIds)->count();
        $repeatUsers = Order::whereIn('user_id', $userIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select('user_id')
            ->groupBy('user_id')
            ->havingRaw('COUNT(*) > 1')
            ->count();
        
        $repurchaseRate = $totalUsers > 0 ? ($repeatUsers / $totalUsers) * 100 : 0;

        // 流失预警（30天内无订单的用户）
        $churnWarnings = User::whereIn('id', $userIds)
            ->whereDoesntHave('orders', function($query) {
                $query->where('created_at', '>=', Carbon::now()->subDays(30));
            })
            ->whereHas('orders') // 但之前有过订单
            ->count();

        // 新增：基于行为数据的统计
        try {
            // 页面浏览总数
            $totalPageViews = CustomerBehaviorAnalytics::whereIn('user_id', $userIds)
                ->where('event_type', CustomerBehaviorAnalytics::EVENT_PAGE_VIEW)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            // 活跃会话数
            $activeSessions = UserSession::whereIn('user_id', $userIds)
                ->whereBetween('start_time', [$startDate, $endDate])
                ->count();

            // 平均会话时长
            $avgSessionDuration = UserSession::whereIn('user_id', $userIds)
                ->whereBetween('start_time', [$startDate, $endDate])
                ->avg('duration') ?? 0;

            // 商品浏览数
            $productViews = CustomerBehaviorAnalytics::whereIn('user_id', $userIds)
                ->where('event_type', CustomerBehaviorAnalytics::EVENT_PRODUCT_VIEW)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

        } catch (\Exception $e) {
            Log::error('获取行为统计数据失败', ['error' => $e->getMessage()]);
            $totalPageViews = 0;
            $activeSessions = 0;
            $avgSessionDuration = 0;
            $productViews = 0;
        }

        return [
            // 后端标准字段名
            'active_users' => $activeUsers,
            'avg_order_value' => round($avgOrderValue, 2),
            'repurchase_rate' => round($repurchaseRate, 1),
            'churn_warnings' => $churnWarnings,
            'last_updated' => now()->format('Y-m-d H:i:s'),
            
            // 新增行为数据统计
            'total_page_views' => $totalPageViews,
            'active_sessions' => $activeSessions,
            'avg_session_duration' => round($avgSessionDuration, 0),
            'product_views' => $productViews,
            
            // 前端期望的字段名（兼容性）
            'activeCustomers' => $activeUsers,
            'activeCustomersTrend' => 0, // 暂时设为0，后续可以计算趋势
            'avgOrderValue' => round($avgOrderValue, 2),
            'avgOrderValueTrend' => 0, // 暂时设为0，后续可以计算趋势
            'repeatPurchaseRate' => round($repurchaseRate, 1),
            'repeatPurchaseRateTrend' => 0, // 暂时设为0，后续可以计算趋势
            'churnRisk' => $churnWarnings,
            
            // 统计周期
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ];
    }

    /**
     * 获取客户行为详情
     */
    public function getClientBehavior(int $clientId, ?int $crmAgentId = null, array $params = []): array
    {
        // 权限检查
        if (!$this->canAccessClient($clientId, $crmAgentId)) {
            throw new \Exception('无权限访问该客户数据');
        }

        $userIds = $this->getAuthorizedUserIds($crmAgentId);
        
        if (!in_array($clientId, $userIds)) {
            throw new \Exception('无权限访问该客户数据');
        }

        $user = User::findOrFail($clientId);
        $startDate = $params['start_date'] ?? Carbon::now()->subDays(30)->format('Y-m-d');
        $endDate = $params['end_date'] ?? Carbon::now()->format('Y-m-d');

        // 订单统计
        $orders = Order::where('user_id', $clientId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $orderStats = [
            'total_orders' => $orders->count(),
            'total_amount' => $orders->sum('total'),
            'avg_order_value' => $orders->avg('total') ?? 0,
            'last_order_date' => $orders->max('created_at'),
        ];

        // 购买偏好（最常购买的商品类别）
        $categoryPreferences = $orders->flatMap(function($order) {
            return $order->items->pluck('product.category')->filter()->map(function($category) {
                return $category->name ?? '未分类';
            });
        })->countBy()->sortDesc()->take(5);

        return [
            'user_info' => [
                'id' => $user->id,
                'name' => $user->name,
                'phone' => $user->phone,
                'merchant_name' => $user->merchant_name,
            ],
            'order_stats' => $orderStats,
            'category_preferences' => $categoryPreferences->toArray(),
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ];
    }

    /**
     * 获取购买行为分析
     */
    public function getPurchaseAnalysis(?int $crmAgentId = null, array $params = []): array
    {
        $userIds = $this->getAuthorizedUserIds($crmAgentId);
        $startDate = $params['start_date'] ?? Carbon::now()->subDays(30)->format('Y-m-d');
        $endDate = $params['end_date'] ?? Carbon::now()->format('Y-m-d');

        try {
            // 基础统计数据
            $orders = Order::whereIn('user_id', $userIds)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('status', '!=', 'cancelled')
                ->get();

            $totalOrders = $orders->count();
            $totalAmount = $orders->sum('total');
            $avgOrderValue = $totalOrders > 0 ? round($totalAmount / $totalOrders, 2) : 0;
            $activeCustomers = $orders->unique('user_id')->count();

            // 购买概览
            $overview = [
                'totalOrders' => $totalOrders,
                'totalAmount' => round($totalAmount, 2),
                'avgOrderValue' => $avgOrderValue,
                'activeCustomers' => $activeCustomers
            ];

        // 购买频次分布
            $frequencyData = Order::whereIn('user_id', $userIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select('user_id')
            ->groupBy('user_id')
            ->selectRaw('user_id, COUNT(*) as order_count')
            ->get()
            ->groupBy(function($item) {
                if ($item->order_count == 1) return '1次';
                if ($item->order_count <= 3) return '2-3次';
                if ($item->order_count <= 5) return '4-5次';
                return '5次以上';
                });

            $totalCustomers = $frequencyData->sum(function($group) { return $group->count(); });
            $frequency = [];
            foreach (['1次', '2-3次', '4-5次', '5次以上'] as $label) {
                $count = $frequencyData->get($label, collect())->count();
                $percentage = $totalCustomers > 0 ? round(($count / $totalCustomers) * 100, 1) : 0;
                $frequency[] = [
                    'label' => $label,
                    'count' => $count,
                    'percentage' => $percentage
                ];
            }

        // 购买金额分布
            $amountGroups = $orders->groupBy(function($order) {
                $amount = $order->total;
                if ($amount < 100) return '100元以下';
                if ($amount < 300) return '100-300元';
                if ($amount < 500) return '300-500元';
                if ($amount < 800) return '500-800元';
                return '800元以上';
            });

            $amount_distribution = [];
            foreach (['100元以下', '100-300元', '300-500元', '500-800元', '800元以上'] as $range) {
                $count = $amountGroups->get($range, collect())->count();
                $percentage = $totalOrders > 0 ? round(($count / $totalOrders) * 100, 1) : 0;
                $amount_distribution[] = [
                    'range' => $range,
                    'count' => $count,
                    'percentage' => $percentage
                ];
            }

            // 复购分析
            $userOrderCounts = Order::whereIn('user_id', $userIds)
            ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('user_id')
                ->selectRaw('user_id, COUNT(*) as order_count')
            ->get();

            $firstTimeCustomers = $userOrderCounts->where('order_count', 1)->count();
            $repeatCustomers = $userOrderCounts->where('order_count', '>', 1)->count();
            $repurchaseRate = $activeCustomers > 0 ? round(($repeatCustomers / $activeCustomers) * 100, 1) : 0;

            // 计算平均复购间隔
            $avgInterval = 15; // 默认15天，可以根据实际数据计算

            $repurchase = [
                'rate' => $repurchaseRate,
                'firstTime' => $firstTimeCustomers,
                'repeat' => $repeatCustomers,
                'avgInterval' => $avgInterval
            ];

            // 客户价值分层
            $value_segments = [
                [
                    'level' => 'high',
                    'icon' => '💎',
                    'name' => '高价值客户',
                    'description' => '消费金额高，购买频次高',
                    'count' => round($activeCustomers * 0.2),
                    'totalAmount' => round($totalAmount * 0.6, 2)
                ],
                [
                    'level' => 'medium',
                    'icon' => '🥇',
                    'name' => '中价值客户',
                    'description' => '消费稳定，有提升潜力',
                    'count' => round($activeCustomers * 0.3),
                    'totalAmount' => round($totalAmount * 0.25, 2)
                ],
                [
                    'level' => 'low',
                    'icon' => '🥈',
                    'name' => '低价值客户',
                    'description' => '消费较少，需要激活',
                    'count' => round($activeCustomers * 0.5),
                    'totalAmount' => round($totalAmount * 0.15, 2)
                ]
            ];

        return [
                'overview' => $overview,
                'frequency' => $frequency,
                'amount_distribution' => $amount_distribution,
                'repurchase' => $repurchase,
                'value_segments' => $value_segments,
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取购买行为分析失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 返回空数据结构
            return [
                'overview' => [
                    'totalOrders' => 0,
                    'totalAmount' => 0,
                    'avgOrderValue' => 0,
                    'activeCustomers' => 0
                ],
                'frequency' => [],
                'amount_distribution' => [],
                'repurchase' => [
                    'rate' => 0,
                    'firstTime' => 0,
                    'repeat' => 0,
                    'avgInterval' => 0
                ],
                'value_segments' => [],
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ];
        }
    }

    /**
     * 获取浏览行为分析
     */
    public function getBrowseAnalysis(?int $crmAgentId = null, array $params = []): array
    {
        $userIds = $this->getAuthorizedUserIds($crmAgentId);
        $startDate = $params['start_date'] ?? Carbon::now()->subDays(30)->format('Y-m-d');
        $endDate = $params['end_date'] ?? Carbon::now()->format('Y-m-d');

        try {
            // 页面浏览统计 - 基于真实行为数据
            $pageViewsData = CustomerBehaviorAnalytics::whereIn('user_id', $userIds)
                ->where('event_type', CustomerBehaviorAnalytics::EVENT_PAGE_VIEW)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->get();

            $pageViews = [
                'home' => 0,
                'category' => 0,
                'product' => 0,
                'cart' => 0,
                'checkout' => 0
            ];

            foreach ($pageViewsData as $view) {
                $pageName = $view->event_data['page_name'] ?? 'unknown';
                if (isset($pageViews[$pageName])) {
                    $pageViews[$pageName]++;
                }
            }

            // 会话分析
            $sessions = UserSession::whereIn('user_id', $userIds)
                ->whereBetween('start_time', [$startDate, $endDate])
                ->get();

            $totalSessions = $sessions->count();
            $avgSessionDuration = $totalSessions > 0 ? $sessions->avg('duration') : 0;
            
            // 跳出率计算（只有1个页面浏览的会话比例）
            $singlePageSessions = $sessions->where('page_count', 1)->count();
            $bounceRate = $totalSessions > 0 ? ($singlePageSessions / $totalSessions) * 100 : 0;

            // 热门商品（基于商品浏览事件）
            $popularProducts = CustomerBehaviorAnalytics::whereIn('user_id', $userIds)
                ->where('event_type', CustomerBehaviorAnalytics::EVENT_PRODUCT_VIEW)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->get()
                ->groupBy(function($item) {
                    return $item->event_data['product_id'] ?? 0;
                })
                ->map(function($group, $productId) {
                    return [
                        'name' => '商品 #' . $productId,
                        'views' => $group->count()
                    ];
                })
                ->sortByDesc('views')
                ->take(10)
                ->values()
                ->toArray();

            return [
                'page_views' => $pageViews,
                'bounce_rate' => round($bounceRate, 1),
                'avg_session_duration' => round($avgSessionDuration, 0), // 秒
                'popular_products' => $popularProducts,
                'total_sessions' => $totalSessions,
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取浏览行为分析失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 如果查询失败，返回空数据
        return [
            'page_views' => [
                    'home' => 0,
                    'category' => 0,
                    'product' => 0,
                    'cart' => 0,
                    'checkout' => 0
                ],
                'bounce_rate' => 0,
                'avg_session_duration' => 0,
                'popular_products' => [],
                'total_sessions' => 0,
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ];
        }
    }

    /**
     * 获取时间行为分析
     */
    public function getTimeAnalysis(?int $crmAgentId = null, array $params = []): array
    {
        $userIds = $this->getAuthorizedUserIds($crmAgentId);
        $startDate = $params['start_date'] ?? Carbon::now()->subDays(30)->format('Y-m-d');
        $endDate = $params['end_date'] ?? Carbon::now()->format('Y-m-d');
        $granularity = $params['granularity'] ?? 'hour';

        try {
        // 按小时统计订单分布
        $hourlyDistribution = Order::whereIn('user_id', $userIds)
            ->whereBetween('created_at', [$startDate, $endDate])
                ->where('status', '!=', 'cancelled')
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->pluck('count', 'hour');

        // 按星期统计
        $weeklyDistribution = Order::whereIn('user_id', $userIds)
            ->whereBetween('created_at', [$startDate, $endDate])
                ->where('status', '!=', 'cancelled')
            ->selectRaw('DAYOFWEEK(created_at) as day, COUNT(*) as count')
            ->groupBy('day')
            ->orderBy('day')
            ->get()
            ->pluck('count', 'day');

        // 生成24小时热力图数据
        $heatmapData = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $heatmapData[$hour] = $hourlyDistribution[$hour] ?? 0;
        }

            // 生成星期数据
            $weekData = [];
            for ($day = 1; $day <= 7; $day++) {
                $weekData[$day] = $weeklyDistribution[$day] ?? 0;
            }

        return [
            'hourly_distribution' => $heatmapData,
                'weekly_distribution' => $weekData,
            'peak_hours' => [
                'morning' => '09:00-11:00',
                'afternoon' => '14:00-16:00',
                'evening' => '19:00-21:00'
                ],
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ],
                'granularity' => $granularity
            ];
        } catch (\Exception $e) {
            Log::error('获取时间行为分析失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 返回空数据结构
            return [
                'hourly_distribution' => array_fill(0, 24, 0),
                'weekly_distribution' => array_fill(1, 7, 0),
                'peak_hours' => [
                    'morning' => '09:00-11:00',
                    'afternoon' => '14:00-16:00',
                    'evening' => '19:00-21:00'
                ],
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ],
                'granularity' => $granularity
            ];
        }
    }

    /**
     * 获取地理行为分析
     */
    public function getGeoAnalysis(?int $crmAgentId = null, array $params = []): array
    {
        $userIds = $this->getAuthorizedUserIds($crmAgentId);

        // 按城市统计用户分布
        $cityDistribution = User::whereIn('id', $userIds)
            ->selectRaw('city, COUNT(*) as count')
            ->groupBy('city')
            ->orderByDesc('count')
            ->get();

        // 按区域统计订单
        $regionOrders = User::whereIn('id', $userIds)
            ->join('orders', 'users.id', '=', 'orders.user_id')
            ->selectRaw('users.district, COUNT(orders.id) as order_count, SUM(orders.total) as total_amount')
            ->groupBy('users.district')
            ->orderByDesc('order_count')
            ->get();

        return [
            'city_distribution' => $cityDistribution,
            'region_orders' => $regionOrders,
            'top_regions' => $regionOrders->take(10)
        ];
    }

    /**
     * 获取趋势分析
     */
    public function getTrendAnalysis(?int $crmAgentId = null, array $params = []): array
    {
        $userIds = $this->getAuthorizedUserIds($crmAgentId);
        $period = $params['period'] ?? 'last_30_days';

        // 根据周期计算日期范围
        switch ($period) {
            case 'last_7_days':
                $startDate = Carbon::now()->subDays(7);
                break;
            case 'last_30_days':
                $startDate = Carbon::now()->subDays(30);
                break;
            case 'last_90_days':
                $startDate = Carbon::now()->subDays(90);
                break;
            default:
                $startDate = Carbon::now()->subDays(30);
        }

        // 用户增长趋势
        $userGrowth = User::whereIn('id', $userIds)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as new_users')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // 订单趋势
        $orderTrend = Order::whereIn('user_id', $userIds)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as orders, SUM(total) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'user_growth' => $userGrowth,
            'order_trend' => $orderTrend,
            'period' => $period,
            'summary' => [
                'total_new_users' => $userGrowth->sum('new_users'),
                'total_orders' => $orderTrend->sum('orders'),
                'total_revenue' => $orderTrend->sum('revenue')
            ]
        ];
    }

    /**
     * 获取客户价值分析
     */
    public function getCustomerValueAnalysis(?int $crmAgentId = null, array $params = []): array
    {
        $userIds = $this->getAuthorizedUserIds($crmAgentId);

        // RFM分析
        $rfmAnalysis = User::whereIn('id', $userIds)
            ->with(['orders' => function($query) {
                $query->where('status', '!=', 'cancelled');
            }])
            ->get()
            ->map(function($user) {
                $orders = $user->orders;
                $recency = $orders->max('created_at') ? 
                    Carbon::parse($orders->max('created_at'))->diffInDays(now()) : 999;
                $frequency = $orders->count();
                $monetary = $orders->sum('total');

                return [
                    'user_id' => $user->id,
                    'name' => $user->name,
                    'merchant_name' => $user->merchant_name,
                    'recency' => $recency,
                    'frequency' => $frequency,
                    'monetary' => $monetary,
                    'segment' => $this->calculateCustomerSegment($recency, $frequency, $monetary)
                ];
            });

        // 价值分段统计
        $segmentStats = $rfmAnalysis->groupBy('segment')->map->count();

        return [
            'rfm_analysis' => $rfmAnalysis->take(100), // 限制返回数量
            'segment_stats' => $segmentStats,
            'high_value_customers' => $rfmAnalysis->where('segment', 'high_value')->take(20)
        ];
    }

    /**
     * 获取商品偏好分析
     */
    public function getProductPreferenceAnalysis(?int $crmAgentId = null, array $params = []): array
    {
        $userIds = $this->getAuthorizedUserIds($crmAgentId);
        $startDate = $params['start_date'] ?? Carbon::now()->subDays(30)->format('Y-m-d');
        $endDate = $params['end_date'] ?? Carbon::now()->format('Y-m-d');

        // 热门商品（基于订单项）- 简化查询，不依赖products表
        $popularProducts = DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereIn('orders.user_id', $userIds)
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->where('orders.status', '!=', 'cancelled')
            ->selectRaw('order_items.product_name as name, SUM(order_items.quantity) as total_quantity, SUM(order_items.quantity * order_items.price) as total_revenue')
            ->groupBy('order_items.product_name')
            ->orderByDesc('total_quantity')
            ->limit(20)
            ->get();

        // 品类偏好 - 基于商品名称的简单分类
        $categoryPreference = DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereIn('orders.user_id', $userIds)
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->where('orders.status', '!=', 'cancelled')
            ->selectRaw('
                CASE 
                    WHEN order_items.product_name LIKE "%蔬菜%" OR order_items.product_name LIKE "%青菜%" OR order_items.product_name LIKE "%白菜%" THEN "蔬菜类"
                    WHEN order_items.product_name LIKE "%水果%" OR order_items.product_name LIKE "%苹果%" OR order_items.product_name LIKE "%橙子%" THEN "水果类"
                    WHEN order_items.product_name LIKE "%肉%" OR order_items.product_name LIKE "%猪%" OR order_items.product_name LIKE "%牛%" OR order_items.product_name LIKE "%鸡%" THEN "肉类"
                    WHEN order_items.product_name LIKE "%海鲜%" OR order_items.product_name LIKE "%鱼%" OR order_items.product_name LIKE "%虾%" THEN "海鲜类"
                    ELSE "其他"
                END as category, 
                COUNT(*) as order_count, 
                SUM(order_items.quantity) as total_quantity
            ')
            ->groupBy('category')
            ->orderByDesc('order_count')
            ->get();

        return [
            'hotProducts' => $popularProducts->map(function($product) {
                return [
                    'id' => $product->id ?? rand(1, 1000),
                    'name' => $product->name,
                    'category' => '商品',
                    'sales' => (int)$product->total_quantity,
                    'revenue' => round($product->total_revenue, 2)
                ];
            })->toArray(),
            'popular_products' => $popularProducts,
            'category_preference' => $categoryPreference,
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ];
    }

    /**
     * 获取流失预警数据 - 融合跟进记录分析
     * 
     * @param int|null $crmAgentId
     * @param array $params
     * @return array
     */
    public function getChurnWarning(?int $crmAgentId = null, array $params = []): array
    {
        $warningDays = $params['warning_days'] ?? 30;
        $limit = $params['limit'] ?? 20;

        try {
            // 使用更高效的查询方式，避免大数组的 whereIn
            $churnUsersQuery = $this->buildChurnUsersQuery($crmAgentId, $warningDays, $limit);
            
            // 直接获取数据，不使用chunk（因为已经有limit限制）
            $users = $churnUsersQuery->get();
            
            $churnUsers = collect();
            foreach ($users as $user) {
                $churnUsers->push($this->formatChurnUserWithDetails($user));
            }

            // 融合跟进记录分析
            $churnUsers = $this->enrichWithFollowUpAnalysis($churnUsers);

        return [
            'customers' => $churnUsers->map(function($user) {
                return [
                    'id' => $user['user_id'],
                    'name' => $user['name'],
                        'phone' => $user['phone'],
                        'merchant_name' => $user['merchant_name'],
                        'churnReason' => $user['churn_reasons'],
                    'lastOrderTime' => $user['last_order_date'] ? $user['last_order_date'] . 'T00:00:00Z' : null,
                        'riskLevel' => $user['risk_level'],
                        'riskScore' => $user['risk_score'],
                        'totalSpend' => $user['total_spend'],
                        'totalOrders' => $user['total_orders'],
                        'avgOrderValue' => $user['avg_order_value'],
                        'daysSinceLastOrder' => $user['days_since_last_order'],
                        'suggestions' => $user['suggestions'],
                        // 新增跟进相关字段
                        'lastFollowUpDate' => $user['last_follow_up_date'] ?? null,
                        'followUpCount' => $user['follow_up_count'] ?? 0,
                        'daysSinceLastFollowUp' => $user['days_since_last_follow_up'] ?? null,
                        'followUpStatus' => $user['follow_up_status'] ?? 'none',
                        'nextFollowUpDate' => $user['next_follow_up_date'] ?? null,
                        'followUpPriority' => $user['follow_up_priority'] ?? 'normal'
                ];
            })->toArray(),
            'users' => $churnUsers->toArray(),
            'total_warnings' => $churnUsers->count(),
            'warning_threshold' => $warningDays,
                'risk_distribution' => $this->calculateRiskDistribution($churnUsers),
                'follow_up_stats' => $this->calculateFollowUpStats($churnUsers),
            'last_updated' => now()->format('Y-m-d H:i:s')
        ];
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('流失预警查询失败: ' . $e->getMessage());
            
            // 返回空结果而不是抛出异常
            return [
                'customers' => [],
                'users' => [],
                'total_warnings' => 0,
                'warning_threshold' => $warningDays,
                'risk_distribution' => ['high' => 0, 'medium' => 0, 'low' => 0],
                'follow_up_stats' => ['pending' => 0, 'overdue' => 0, 'completed' => 0],
                'last_updated' => now()->format('Y-m-d H:i:s'),
                'error' => '查询失败，请稍后重试'
            ];
        }
    }

    /**
     * 构建流失用户查询 - 生鲜配送业务优化版本
     */
    private function buildChurnUsersQuery(?int $crmAgentId, int $warningDays, int $limit)
    {
        // 生鲜配送业务调整预警天数：默认7天，最长不超过14天
        $adjustedWarningDays = min($warningDays, 14);
        if ($warningDays > 14) {
            $adjustedWarningDays = 7; // 生鲜配送超过7天未下单就需要关注
        }
        
        $cutoffDate = Carbon::now()->subDays($adjustedWarningDays);
        
        // 使用原生 SQL 构建更高效的查询
        $query = DB::table('users as u')
            ->select([
                'u.id',
                'u.name', 
                'u.phone',
                'u.merchant_name',
                'u.member_points',
                'u.joined_at',
                'last_orders.created_at as last_order_date',
                'order_stats.total_orders',
                'order_stats.avg_order_value',
                'order_stats.total_spend'
            ])
            ->joinSub(
                // 子查询获取每个用户的最后订单日期
                DB::table('orders')
                    ->select('user_id', DB::raw('MAX(created_at) as created_at'))
                    ->where('status', '!=', 'cancelled')
                    ->groupBy('user_id'),
                'last_orders',
                'u.id',
                '=',
                'last_orders.user_id'
            )
            ->leftJoinSub(
                // 子查询获取用户订单统计，包括总消费
                DB::table('orders')
                    ->select([
                        'user_id',
                        DB::raw('COUNT(*) as total_orders'),
                        DB::raw('AVG(total) as avg_order_value'),
                        DB::raw('SUM(total) as total_spend')
                    ])
                    ->where('status', '!=', 'cancelled')
                    ->groupBy('user_id'),
                'order_stats',
                'u.id',
                '=',
                'order_stats.user_id'
            )
            ->where('last_orders.created_at', '<', $cutoffDate)
            ->orderBy('last_orders.created_at', 'asc') // 按最后订单时间升序排列
            ->limit($limit);

        // 根据权限添加条件
        if (!is_null($crmAgentId)) {
            $query->where('u.crm_agent_id', $crmAgentId);
        }

        return $query;
    }

    /**
     * 格式化流失用户数据 - 增强版本
     */
    private function formatChurnUserWithDetails($user): array
    {
        $lastOrderDate = $user->last_order_date ? Carbon::parse($user->last_order_date) : null;
        $daysSinceLastOrder = $lastOrderDate ? $lastOrderDate->diffInDays(now()) : null;
        $joinedDate = $user->joined_at ? Carbon::parse($user->joined_at) : null;
        $totalOrders = $user->total_orders ?? 0;
        $avgOrderValue = $user->avg_order_value ?? 0;
        $totalSpend = $user->total_spend ?? 0; // 从子查询中获取正确的总消费

        // 计算风险等级和分数
        $riskAnalysis = $this->calculateDetailedRiskLevel($daysSinceLastOrder, $totalOrders, $avgOrderValue, $totalSpend, $joinedDate);
        
        // 生成预警原因
        $churnReasons = $this->generateChurnReasons($daysSinceLastOrder, $totalOrders, $avgOrderValue, $totalSpend, $joinedDate);
        
        // 生成建议操作
        $suggestions = $this->generateSuggestions($riskAnalysis['level'], $churnReasons, $totalSpend);

                return [
            'user_id' => $user->id,
            'name' => $user->merchant_name ?: $user->name,
            'phone' => $user->phone,
            'merchant_name' => $user->merchant_name,
            'total_spend' => round($totalSpend, 2), // 确保格式化为两位小数
            'member_points' => $user->member_points ?? 0,
            'total_orders' => $totalOrders,
            'avg_order_value' => round($avgOrderValue, 2),
            'last_order_date' => $lastOrderDate ? $lastOrderDate->format('Y-m-d') : null,
            'days_since_last_order' => $daysSinceLastOrder,
            'risk_level' => $riskAnalysis['level'],
            'risk_score' => $riskAnalysis['score'],
            'churn_reasons' => $churnReasons,
            'suggestions' => $suggestions,
            'joined_days' => $joinedDate ? $joinedDate->diffInDays(now()) : null
        ];
    }

    /**
     * 计算详细的风险等级 - 生鲜配送业务优化版本
     */
    private function calculateDetailedRiskLevel(?int $daysSinceLastOrder, int $totalOrders, float $avgOrderValue, float $totalSpend, ?Carbon $joinedDate): array
    {
        $score = 0;
        $factors = [];

        // 1. 最后下单时间因素 (权重: 50%) - 生鲜配送对时间更敏感
        if ($daysSinceLastOrder) {
            if ($daysSinceLastOrder > 14) { // 生鲜配送超过2周未下单就是高风险
                $score += 50;
                $factors[] = '超过2周未下单';
            } elseif ($daysSinceLastOrder > 7) { // 超过1周
                $score += 35;
                $factors[] = '超过1周未下单';
            } elseif ($daysSinceLastOrder > 3) { // 超过3天
                $score += 20;
                $factors[] = '超过3天未下单';
            } else {
                $score += 5;
            }
        } else {
            $score += 50; // 从未下单
            $factors[] = '从未下单';
        }

        // 2. 订单频次因素 (权重: 25%) - 生鲜配送期望更高频次
        if ($totalOrders == 0) {
            $score += 25;
            $factors[] = '零订单客户';
        } elseif ($totalOrders == 1) {
            $score += 20;
            $factors[] = '仅有一次购买';
        } elseif ($totalOrders <= 5) { // 生鲜配送5次以下算低频
            $score += 15;
            $factors[] = '购买频次较低';
        } elseif ($totalOrders <= 10) {
            $score += 8;
        } else {
            $score += 3; // 高频客户流失风险相对较低
        }

        // 3. 消费金额因素 (权重: 15%) - 生鲜配送单价相对较低
        if ($totalSpend == 0) {
            $score += 15;
            $factors[] = '零消费客户';
        } elseif ($totalSpend < 500) {
            $score += 12;
            $factors[] = '消费金额较低';
        } elseif ($totalSpend < 1500) {
            $score += 8;
        } elseif ($totalSpend < 3000) {
            $score += 4;
        } else {
            $score += 2; // 高价值客户
        }

        // 4. 平均订单价值因素 (权重: 5%) - 生鲜配送客单价相对固定
        if ($avgOrderValue == 0) {
            $score += 5;
        } elseif ($avgOrderValue < 400) { // 生鲜配送400元以下算低客单价
            $score += 4;
            $factors[] = '客单价较低';
        } elseif ($avgOrderValue < 600) {
            $score += 2;
        } else {
            $score += 1;
        }

        // 5. 客户生命周期因素 (权重: 5%) - 生鲜配送新客户转化期更短
        if ($joinedDate) {
            $daysSinceJoined = $joinedDate->diffInDays(now());
            if ($daysSinceJoined < 7 && $totalOrders <= 1) { // 一周内没有复购
                $score += 5;
                $factors[] = '新客户转化率低';
            } elseif ($daysSinceJoined > 180 && $totalOrders <= 5) { // 半年老客户但订单少
                $score += 3;
                $factors[] = '老客户活跃度低';
            }
        }

        // 确定风险等级 - 调整阈值适应生鲜配送
        if ($score >= 60) {
            $level = 'high';
        } elseif ($score >= 30) {
            $level = 'medium';
        } else {
            $level = 'low';
        }

        return [
            'level' => $level,
            'score' => $score,
            'factors' => $factors
        ];
    }

    /**
     * 生成流失原因 - 生鲜配送业务优化版本
     */
    private function generateChurnReasons(?int $daysSinceLastOrder, int $totalOrders, float $avgOrderValue, float $totalSpend, ?Carbon $joinedDate): array
    {
        $reasons = [];

        // 时间维度分析 - 生鲜配送时间敏感性更高
        if ($daysSinceLastOrder) {
            if ($daysSinceLastOrder > 14) {
                $reasons[] = "已超过2周未下单，可能改用其他平台";
            } elseif ($daysSinceLastOrder > 7) {
                $reasons[] = "超过1周未下单，购买习惯可能改变";
            } elseif ($daysSinceLastOrder > 3) {
                $reasons[] = "超过3天未下单，需要及时关注";
            }
        } else {
            $reasons[] = "注册后从未下单，可能对产品不满意";
        }

        // 购买行为分析 - 生鲜配送期望高频购买
        if ($totalOrders == 0) {
            $reasons[] = "零购买客户，可能价格或品质不符合预期";
        } elseif ($totalOrders == 1) {
            $reasons[] = "仅购买一次，首次体验可能不佳";
        } elseif ($totalOrders <= 5) {
            $reasons[] = "购买频次偏低，可能需求不匹配";
        }

        // 消费金额分析 - 生鲜配送特点
        if ($totalSpend < 500) {
            $reasons[] = "消费金额较低，可能只是尝试性购买";
        } elseif ($avgOrderValue < 400) {
            $reasons[] = "客单价偏低，可能对配送费或服务不满意";
        }

        // 客户生命周期分析 - 生鲜配送转化期短
        if ($joinedDate) {
            $daysSinceJoined = $joinedDate->diffInDays(now());
            if ($daysSinceJoined < 7 && $totalOrders <= 1) {
                $reasons[] = "新客户未形成购买习惯，需要引导";
            } elseif ($daysSinceJoined > 180 && $totalOrders <= 5) {
                $reasons[] = "老客户活跃度下降，可能生活习惯改变";
            }
        }

        return array_slice($reasons, 0, 3); // 最多返回3个主要原因
    }

    /**
     * 生成挽回建议 - 生鲜配送业务优化版本
     */
    private function generateSuggestions(string $riskLevel, array $reasons, float $totalSpend): array
    {
        $suggestions = [];

        switch ($riskLevel) {
            case 'high':
                $suggestions[] = "立即联系了解停购原因";
                $suggestions[] = "提供配送费减免或新鲜保证";
                if ($totalSpend > 1500) { // 调整高价值客户标准，适应700元均单价
                    $suggestions[] = "提供VIP客户专属优惠";
                } else {
                    $suggestions[] = "推荐当季热销生鲜商品";
                }
                $suggestions[] = "邀请参与限时秒杀活动";
                break;

            case 'medium':
                $suggestions[] = "发送每日新鲜到货提醒";
                $suggestions[] = "推荐个性化生鲜套餐";
                $suggestions[] = "提供小额优惠券刺激复购";
                $suggestions[] = "邀请参与会员积分活动";
                break;

            case 'low':
                $suggestions[] = "定期推送时令生鲜信息";
                $suggestions[] = "节假日特价商品提醒";
                $suggestions[] = "分享健康饮食搭配建议";
                break;
        }

        // 根据具体原因添加针对性建议
        foreach ($reasons as $reason) {
            if (strpos($reason, '从未下单') !== false) {
                $suggestions[] = "提供新用户首单免配送费";
            } elseif (strpos($reason, '客单价') !== false) {
                $suggestions[] = "推荐满减优惠套餐";
            } elseif (strpos($reason, '新客户') !== false) {
                $suggestions[] = "安排客服主动关怀指导";
            } elseif (strpos($reason, '配送费') !== false) {
                $suggestions[] = "推广包月配送服务";
            }
        }

        return array_unique(array_slice($suggestions, 0, 4)); // 去重并限制数量
    }

    /**
     * 计算风险分布
     */
    private function calculateRiskDistribution($churnUsers): array
    {
        $distribution = ['high' => 0, 'medium' => 0, 'low' => 0];
        
        foreach ($churnUsers as $user) {
            $riskLevel = $user['risk_level'];
            if (isset($distribution[$riskLevel])) {
                $distribution[$riskLevel]++;
            }
        }

        return $distribution;
    }

    /**
     * 权限控制：获取CRM专员可访问的用户ID列表 - 优化版本
     */
    private function getAuthorizedUserIds(?int $crmAgentId = null): array
    {
        try {
        if (is_null($crmAgentId)) {
                // 管理员情况：返回所有用户ID，但限制数量避免内存问题
                return User::limit(1000)->pluck('id')->toArray();
        }
        
        // CRM专员只能看到分配给自己的客户
            return User::where('crm_agent_id', $crmAgentId)
                ->limit(1000) // 限制最大数量
                ->pluck('id')
                ->toArray();
        } catch (\Exception $e) {
            Log::error('获取授权用户ID失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 检查是否为管理员权限
     */
    private function isAdminAccess(?int $crmAgentId): bool
    {
        return is_null($crmAgentId);
    }

    /**
     * 为查询添加权限条件
     */
    private function addPermissionConstraints($query, ?int $crmAgentId)
    {
        if (!$this->isAdminAccess($crmAgentId)) {
            $query->where('crm_agent_id', $crmAgentId);
        }
        return $query;
    }

    /**
     * 权限检查：是否可以访问指定客户
     */
    private function canAccessClient(int $clientId, ?int $crmAgentId = null): bool
    {
        if (is_null($crmAgentId)) {
            return true; // 管理员可以访问所有客户
        }
        
        return User::where('id', $clientId)
                  ->where('crm_agent_id', $crmAgentId)
                  ->exists();
    }

    /**
     * 计算客户细分
     * 
     * @param int $recency
     * @param int $frequency
     * @param float $monetary
     * @return string
     */
    private function calculateCustomerSegment(int $recency, int $frequency, float $monetary): string
    {
        if ($recency <= 30 && $frequency >= 5 && $monetary >= 1000) {
            return 'high_value';
        } elseif ($recency <= 60 && $frequency >= 3 && $monetary >= 500) {
            return 'medium_value';
        } else {
            return 'low_value';
        }
    }

    /**
     * 计算流失风险等级
     * 
     * @param int|null $daysSinceLastOrder
     * @param int $totalOrders
     * @return string
     */
    private function calculateRiskLevel(?int $daysSinceLastOrder, int $totalOrders): string
    {
        if (!$daysSinceLastOrder) {
            return 'high';
        }

        if ($daysSinceLastOrder > 60 || ($daysSinceLastOrder > 30 && $totalOrders <= 2)) {
            return 'high';
        } elseif ($daysSinceLastOrder > 30 || ($daysSinceLastOrder > 15 && $totalOrders <= 5)) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * 获取今日统计数据
     * 
     * @param int|null $crmAgentId
     * @return array
     */
    public function getTodayStats(?int $crmAgentId = null): array
    {
        $today = Carbon::today();
        
        try {
        // 今日订单数
            $todayOrdersQuery = Order::whereDate('created_at', $today);
            if (!is_null($crmAgentId)) {
                $todayOrdersQuery->whereHas('user', function($query) use ($crmAgentId) {
                    $query->where('crm_agent_id', $crmAgentId);
                });
            }
            $todayOrders = $todayOrdersQuery->count();
            
        // 今日销售额
            $todaySalesQuery = Order::whereDate('created_at', $today)
                ->where('status', '!=', 'cancelled');
            if (!is_null($crmAgentId)) {
                $todaySalesQuery->whereHas('user', function($query) use ($crmAgentId) {
                    $query->where('crm_agent_id', $crmAgentId);
                });
            }
            $todaySales = $todaySalesQuery->sum('total') ?? 0;
            
        // 今日新客户
            $todayNewCustomersQuery = User::whereDate('created_at', $today);
            if (!is_null($crmAgentId)) {
                $todayNewCustomersQuery->where('crm_agent_id', $crmAgentId);
            }
            $todayNewCustomers = $todayNewCustomersQuery->count();
            
        // 今日活跃客户（有订单的客户）
            $todayActiveCustomersQuery = Order::whereDate('created_at', $today);
            if (!is_null($crmAgentId)) {
                $todayActiveCustomersQuery->whereHas('user', function($query) use ($crmAgentId) {
                    $query->where('crm_agent_id', $crmAgentId);
                });
            }
            $todayActiveCustomers = $todayActiveCustomersQuery->distinct('user_id')->count();
        
        return [
            'today_orders' => $todayOrders,
            'today_sales' => round($todaySales, 2),
            'today_new_customers' => $todayNewCustomers,
            'today_active_customers' => $todayActiveCustomers,
            'date' => $today->format('Y-m-d')
        ];
        } catch (\Exception $e) {
            Log::error('获取今日统计失败: ' . $e->getMessage());
            
            return [
                'today_orders' => 0,
                'today_sales' => 0,
                'today_new_customers' => 0,
                'today_active_customers' => 0,
                'date' => $today->format('Y-m-d'),
                'error' => '查询失败，请稍后重试'
            ];
        }
    }

    /**
     * 获取对比分析数据
     * 
     * @param int|null $crmAgentId
     * @param array $params
     * @return array
     */
    public function getComparisonAnalysis(?int $crmAgentId = null, array $params = []): array
    {
        $userIds = $this->getAuthorizedUserIds($crmAgentId);
        $startDate = $params['start_date'] ?? Carbon::now()->subDays(30)->format('Y-m-d');
        $endDate = $params['end_date'] ?? Carbon::now()->format('Y-m-d');
        $comparePeriod = $params['compare_period'] ?? 'previous_period';
        
        // 计算对比期间
        $currentStart = Carbon::parse($startDate);
        $currentEnd = Carbon::parse($endDate);
        $daysDiff = $currentStart->diffInDays($currentEnd);
        
        $compareStart = $currentStart->copy()->subDays($daysDiff + 1);
        $compareEnd = $currentStart->copy()->subDay();
        
        // 当前期间数据
        $currentData = $this->getPeriodStats($userIds, $currentStart, $currentEnd);
        
        // 对比期间数据
        $compareData = $this->getPeriodStats($userIds, $compareStart, $compareEnd);
        
        // 计算变化率
        $comparison = [];
        foreach ($currentData as $key => $currentValue) {
            $compareValue = $compareData[$key] ?? 0;
            $changeRate = $compareValue > 0 ? (($currentValue - $compareValue) / $compareValue) * 100 : 0;
            
            $comparison[] = [
                'metric' => $key,
                'current_value' => $currentValue,
                'compare_value' => $compareValue,
                'change_rate' => round($changeRate, 1),
                'trend' => $changeRate > 0 ? 'up' : ($changeRate < 0 ? 'down' : 'stable')
            ];
        }
        
        return [
            'comparison' => $comparison,
            'current_period' => [
                'start' => $currentStart->format('Y-m-d'),
                'end' => $currentEnd->format('Y-m-d')
            ],
            'compare_period' => [
                'start' => $compareStart->format('Y-m-d'),
                'end' => $compareEnd->format('Y-m-d')
            ]
        ];
    }

    /**
     * 获取商品生命周期分析
     * 
     * @param int|null $crmAgentId
     * @param array $params
     * @return array
     */
    public function getProductLifecycleAnalysis(?int $crmAgentId = null, array $params = []): array
    {
        $userIds = $this->getAuthorizedUserIds($crmAgentId);
        $startDate = $params['start_date'] ?? Carbon::now()->subDays(90)->format('Y-m-d');
        $endDate = $params['end_date'] ?? Carbon::now()->format('Y-m-d');
        
        // 获取商品销售趋势
        $productTrends = DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereIn('orders.user_id', $userIds)
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->where('orders.status', '!=', 'cancelled')
            ->select(
                'order_items.product_name',
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('SUM(order_items.quantity) as daily_sales'),
                DB::raw('SUM(order_items.quantity * order_items.price) as daily_revenue')
            )
            ->groupBy('order_items.product_name', 'date')
            ->orderBy('date')
            ->get()
            ->groupBy('product_name');
        
        // 分析每个商品的生命周期阶段
        $lifecycleAnalysis = [];
        foreach ($productTrends as $productName => $trends) {
            $totalSales = $trends->sum('daily_sales');
            $avgDailySales = $trends->avg('daily_sales');
            $recentSales = $trends->take(-7)->avg('daily_sales'); // 最近7天平均
            $earlySales = $trends->take(7)->avg('daily_sales'); // 前7天平均
            
            // 判断生命周期阶段
            $stage = 'mature';
            if ($recentSales > $earlySales * 1.5) {
                $stage = 'growth';
            } elseif ($recentSales < $earlySales * 0.5) {
                $stage = 'decline';
            } elseif ($totalSales < 10) {
                $stage = 'introduction';
            }
            
            $lifecycleAnalysis[] = [
                'product_name' => $productName,
                'stage' => $stage,
                'total_sales' => (int)$totalSales,
                'avg_daily_sales' => round($avgDailySales, 1),
                'recent_trend' => $recentSales > $earlySales ? 'up' : 'down',
                'sales_data' => $trends->map(function($item) {
                    return [
                        'date' => $item->date,
                        'sales' => (int)$item->daily_sales,
                        'revenue' => round($item->daily_revenue, 2)
                    ];
                })->values()->toArray()
            ];
        }
        
        return [
            'products' => $lifecycleAnalysis,
            'summary' => [
                'introduction' => collect($lifecycleAnalysis)->where('stage', 'introduction')->count(),
                'growth' => collect($lifecycleAnalysis)->where('stage', 'growth')->count(),
                'mature' => collect($lifecycleAnalysis)->where('stage', 'mature')->count(),
                'decline' => collect($lifecycleAnalysis)->where('stage', 'decline')->count()
            ],
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ];
    }

    /**
     * 获取指定期间的统计数据
     * 
     * @param array $userIds
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    private function getPeriodStats(array $userIds, Carbon $startDate, Carbon $endDate): array
    {
        $orders = Order::whereIn('user_id', $userIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled');
            
        return [
            'total_orders' => $orders->count(),
            'total_sales' => round($orders->sum('total'), 2),
            'avg_order_value' => round($orders->avg('total'), 2),
            'active_customers' => $orders->distinct('user_id')->count()
        ];
    }

    /**
     * 丰富跟进记录分析数据
     */
    private function enrichWithFollowUpAnalysis($churnUsers)
    {
        $userIds = $churnUsers->pluck('user_id')->toArray();
        
        if (empty($userIds)) {
            return $churnUsers;
        }

        // 获取用户的跟进记录统计
        $followUpStats = DB::table('client_follow_ups')
            ->select([
                'user_id',
                DB::raw('COUNT(*) as follow_up_count'),
                DB::raw('MAX(follow_up_date) as last_follow_up_date'),
                DB::raw('MAX(next_follow_up) as next_follow_up_date'),
                DB::raw('MAX(CASE WHEN result = "successful" THEN follow_up_date END) as last_successful_follow_up')
            ])
            ->whereIn('user_id', $userIds)
            ->groupBy('user_id')
            ->get()
            ->keyBy('user_id');

        return $churnUsers->map(function($user) use ($followUpStats) {
            $userId = $user['user_id'];
            $followUpStat = $followUpStats->get($userId);
            
            if ($followUpStat) {
                $lastFollowUpDate = $followUpStat->last_follow_up_date ? 
                    Carbon::parse($followUpStat->last_follow_up_date) : null;
                $nextFollowUpDate = $followUpStat->next_follow_up_date ? 
                    Carbon::parse($followUpStat->next_follow_up_date) : null;
                
                $user['follow_up_count'] = $followUpStat->follow_up_count;
                $user['last_follow_up_date'] = $lastFollowUpDate ? $lastFollowUpDate->format('Y-m-d') : null;
                $user['days_since_last_follow_up'] = $lastFollowUpDate ? $lastFollowUpDate->diffInDays(now()) : null;
                $user['next_follow_up_date'] = $nextFollowUpDate ? $nextFollowUpDate->format('Y-m-d') : null;
                
                // 计算跟进状态
                $user['follow_up_status'] = $this->calculateFollowUpStatus($lastFollowUpDate, $nextFollowUpDate);
                
                // 计算跟进优先级
                $user['follow_up_priority'] = $this->calculateFollowUpPriority($user);
                
                // 更新流失原因（融合跟进信息）
                $user['churn_reasons'] = $this->enhanceChurnReasonsWithFollowUp($user['churn_reasons'], $user);
                
                // 更新建议操作（融合跟进建议）
                $user['suggestions'] = $this->enhanceSuggestionsWithFollowUp($user['suggestions'], $user);
            } else {
                $user['follow_up_count'] = 0;
                $user['last_follow_up_date'] = null;
                $user['days_since_last_follow_up'] = null;
                $user['next_follow_up_date'] = null;
                $user['follow_up_status'] = 'none';
                $user['follow_up_priority'] = 'high'; // 从未跟进的客户优先级高
                
                // 添加"从未跟进"的原因
                $user['churn_reasons'][] = '从未进行客户跟进，缺乏主动关怀';
                $user['suggestions'][] = '立即安排首次客户跟进';
            }
            
            return $user;
        });
    }

    /**
     * 计算跟进状态
     */
    private function calculateFollowUpStatus($lastFollowUpDate, $nextFollowUpDate)
    {
        $now = Carbon::now();
        
        if (!$lastFollowUpDate) {
            return 'none'; // 从未跟进
        }
        
        if ($nextFollowUpDate) {
            if ($nextFollowUpDate->isPast()) {
                return 'overdue'; // 逾期未跟进
            } elseif ($nextFollowUpDate->isToday() || $nextFollowUpDate->isTomorrow()) {
                return 'pending'; // 即将跟进
            } else {
                return 'scheduled'; // 已安排
            }
        }
        
        // 根据最后跟进时间判断
        $daysSinceLastFollowUp = $lastFollowUpDate->diffInDays($now);
        if ($daysSinceLastFollowUp > 14) {
            return 'stale'; // 跟进过时
        } elseif ($daysSinceLastFollowUp > 7) {
            return 'due'; // 需要跟进
        } else {
            return 'recent'; // 最近已跟进
        }
    }

    /**
     * 计算跟进优先级
     */
    private function calculateFollowUpPriority($user)
    {
        $priority = 'normal';
        
        // 高价值客户优先级高
        if ($user['total_spend'] > 10000) {
            $priority = 'high';
        }
        
        // 从未跟进的客户优先级高
        if ($user['follow_up_count'] == 0) {
            $priority = 'high';
        }
        
        // 逾期未跟进的客户优先级高
        if ($user['follow_up_status'] == 'overdue') {
            $priority = 'urgent';
        }
        
        // 高风险且长时间未跟进
        if ($user['risk_level'] == 'high' && $user['days_since_last_follow_up'] > 7) {
            $priority = 'urgent';
        }
        
        return $priority;
    }

    /**
     * 增强流失原因（融合跟进信息）
     */
    private function enhanceChurnReasonsWithFollowUp($reasons, $user)
    {
        if ($user['follow_up_count'] == 0) {
            $reasons[] = '从未进行客户跟进，缺乏主动关怀';
        } elseif ($user['days_since_last_follow_up'] > 30) {
            $reasons[] = '超过30天未跟进，客户关系疏远';
        } elseif ($user['follow_up_status'] == 'overdue') {
            $reasons[] = '计划跟进已逾期，客户可能感到被忽视';
        } elseif ($user['follow_up_count'] < 3 && $user['days_since_last_order'] > 14) {
            $reasons[] = '跟进频次不足，未能及时了解客户需求';
        }
        
        return array_unique($reasons);
    }

    /**
     * 增强建议操作（融合跟进建议）
     */
    private function enhanceSuggestionsWithFollowUp($suggestions, $user)
    {
        switch ($user['follow_up_priority']) {
            case 'urgent':
                array_unshift($suggestions, '紧急安排客户跟进');
                break;
            case 'high':
                array_unshift($suggestions, '优先安排客户跟进');
                break;
        }
        
        if ($user['follow_up_status'] == 'overdue') {
            $suggestions[] = '补充逾期跟进记录';
        }
        
        if ($user['follow_up_count'] == 0) {
            $suggestions[] = '建立客户跟进档案';
        }
        
        // 根据跟进历史提供个性化建议
        if ($user['follow_up_count'] > 0 && $user['days_since_last_follow_up'] > 14) {
            $suggestions[] = '回顾历史跟进记录，制定针对性方案';
        }
        
        return array_unique($suggestions);
    }

    /**
     * 计算跟进统计
     */
    private function calculateFollowUpStats($churnUsers)
    {
        $stats = [
            'pending' => 0,      // 待跟进
            'overdue' => 0,      // 逾期
            'completed' => 0,    // 已完成
            'none' => 0          // 从未跟进
        ];
        
        foreach ($churnUsers as $user) {
            $status = $user['follow_up_status'];
            if (isset($stats[$status])) {
                $stats[$status]++;
            }
        }
        
        return $stats;
    }

    /**
     * 获取客户活跃度分析
     * 
     * @param int|null $crmAgentId
     * @param array $params
     * @return array
     */
    public function getCustomerActivityAnalysis(?int $crmAgentId = null, array $params = []): array
    {
        $userIds = $this->getAuthorizedUserIds($crmAgentId);
        $startDate = $params['start_date'] ?? Carbon::now()->subDays(30)->format('Y-m-d');
        $endDate = $params['end_date'] ?? Carbon::now()->format('Y-m-d');

        try {
            // 基础活跃度统计
            $totalUsers = User::whereIn('id', $userIds)->count();
            
            // 活跃用户（在时间范围内有订单的用户）
            $activeUserIds = Order::whereIn('user_id', $userIds)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('status', '!=', 'cancelled')
                ->distinct('user_id')
                ->pluck('user_id')
                ->toArray();
            
            $activeUsers = count($activeUserIds);
            $activityRate = $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100, 1) : 0;
            
            // 平均订单频次
            $avgOrderFreq = $activeUsers > 0 ? 
                round(Order::whereIn('user_id', $activeUserIds)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->where('status', '!=', 'cancelled')
                    ->count() / $activeUsers, 1) : 0;

            // 活跃度概览
            $activityOverview = [
                'totalUsers' => $totalUsers,
                'activeUsers' => $activeUsers,
                'activityRate' => $activityRate,
                'avgOrderFreq' => $avgOrderFreq
            ];

            // 客户活跃度分层
            $activitySegments = $this->calculateActivitySegments($userIds, $startDate, $endDate);
            
            // 活跃度趋势（最近7天）
            $activityTrend = $this->calculateActivityTrend($userIds, $startDate, $endDate);
            
            // 行为指标
            $behaviorMetrics = $this->calculateBehaviorMetrics($userIds, $startDate, $endDate);
            
            // 高活跃客户列表
            $topActiveCustomers = $this->getTopActiveCustomers($userIds, $startDate, $endDate, 10);
            
            // 活跃度提升建议
            $activitySuggestions = $this->generateActivitySuggestions($activityOverview, $activitySegments);

            return [
                'activityOverview' => $activityOverview,
                'activitySegments' => $activitySegments,
                'activityTrend' => $activityTrend,
                'behaviorMetrics' => $behaviorMetrics,
                'topActiveCustomers' => $topActiveCustomers,
                'activitySuggestions' => $activitySuggestions,
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取客户活跃度分析失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 返回空数据结构
            return [
                'activityOverview' => [
                    'totalUsers' => 0,
                    'activeUsers' => 0,
                    'activityRate' => 0,
                    'avgOrderFreq' => 0
                ],
                'activitySegments' => [],
                'activityTrend' => [],
                'behaviorMetrics' => [
                    'avgPageViews' => 0,
                    'avgSessionDuration' => 0,
                    'avgProductViews' => 0,
                    'pageViewsTrend' => 'stable',
                    'sessionTrend' => 'stable',
                    'productViewsTrend' => 'stable'
                ],
                'topActiveCustomers' => [],
                'activitySuggestions' => [],
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ];
        }
    }

    /**
     * 计算客户活跃度分层
     */
    private function calculateActivitySegments(array $userIds, string $startDate, string $endDate): array
    {
        // 获取用户订单统计
        $userOrderStats = Order::whereIn('user_id', $userIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->select([
                'user_id',
                DB::raw('COUNT(*) as order_count'),
                DB::raw('SUM(total) as total_spend'),
                DB::raw('MAX(created_at) as last_order_date')
            ])
            ->groupBy('user_id')
            ->get()
            ->keyBy('user_id');

        $segments = [
            'high' => ['count' => 0, 'totalOrders' => 0, 'totalSpend' => 0, 'users' => []],
            'medium' => ['count' => 0, 'totalOrders' => 0, 'totalSpend' => 0, 'users' => []],
            'low' => ['count' => 0, 'totalOrders' => 0, 'totalSpend' => 0, 'users' => []],
            'inactive' => ['count' => 0, 'totalOrders' => 0, 'totalSpend' => 0, 'users' => []]
        ];

        foreach ($userIds as $userId) {
            $stats = $userOrderStats->get($userId);
            
            if (!$stats) {
                // 无订单用户
                $segments['inactive']['count']++;
                continue;
            }

            $orderCount = $stats->order_count;
            $totalSpend = $stats->total_spend;
            $daysSinceLastOrder = Carbon::parse($stats->last_order_date)->diffInDays(now());

            // 生鲜配送活跃度分层标准
            if ($orderCount >= 10 && $totalSpend >= 3000 && $daysSinceLastOrder <= 3) {
                $level = 'high';
            } elseif ($orderCount >= 5 && $totalSpend >= 1500 && $daysSinceLastOrder <= 7) {
                $level = 'medium';
            } elseif ($orderCount >= 2 && $daysSinceLastOrder <= 14) {
                $level = 'low';
            } else {
                $level = 'inactive';
            }

            $segments[$level]['count']++;
            $segments[$level]['totalOrders'] += $orderCount;
            $segments[$level]['totalSpend'] += $totalSpend;
        }

        $totalUsers = count($userIds);
        
        return [
            [
                'level' => 'high',
                'label' => '高活跃客户',
                'description' => '订单频次高，消费金额大，最近有购买',
                'count' => $segments['high']['count'],
                'percentage' => $totalUsers > 0 ? round(($segments['high']['count'] / $totalUsers) * 100, 1) : 0,
                'avgOrders' => $segments['high']['count'] > 0 ? round($segments['high']['totalOrders'] / $segments['high']['count'], 1) : 0,
                'avgSpend' => $segments['high']['count'] > 0 ? round($segments['high']['totalSpend'] / $segments['high']['count'], 2) : 0,
                'lastActiveDay' => '1-3'
            ],
            [
                'level' => 'medium',
                'label' => '中活跃客户',
                'description' => '有一定购买频次，消费稳定',
                'count' => $segments['medium']['count'],
                'percentage' => $totalUsers > 0 ? round(($segments['medium']['count'] / $totalUsers) * 100, 1) : 0,
                'avgOrders' => $segments['medium']['count'] > 0 ? round($segments['medium']['totalOrders'] / $segments['medium']['count'], 1) : 0,
                'avgSpend' => $segments['medium']['count'] > 0 ? round($segments['medium']['totalSpend'] / $segments['medium']['count'], 2) : 0,
                'lastActiveDay' => '4-7'
            ],
            [
                'level' => 'low',
                'label' => '低活跃客户',
                'description' => '偶尔购买，需要激活',
                'count' => $segments['low']['count'],
                'percentage' => $totalUsers > 0 ? round(($segments['low']['count'] / $totalUsers) * 100, 1) : 0,
                'avgOrders' => $segments['low']['count'] > 0 ? round($segments['low']['totalOrders'] / $segments['low']['count'], 1) : 0,
                'avgSpend' => $segments['low']['count'] > 0 ? round($segments['low']['totalSpend'] / $segments['low']['count'], 2) : 0,
                'lastActiveDay' => '8-14'
            ],
            [
                'level' => 'inactive',
                'label' => '不活跃客户',
                'description' => '长期未购买或从未购买',
                'count' => $segments['inactive']['count'],
                'percentage' => $totalUsers > 0 ? round(($segments['inactive']['count'] / $totalUsers) * 100, 1) : 0,
                'avgOrders' => 0,
                'avgSpend' => 0,
                'lastActiveDay' => '15+'
            ]
        ];
    }

    /**
     * 计算活跃度趋势
     */
    private function calculateActivityTrend(array $userIds, string $startDate, string $endDate): array
    {
        $trend = [];
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        
        // 按天统计活跃用户数
        for ($date = $start->copy(); $date <= $end; $date->addDay()) {
            $activeUsers = Order::whereIn('user_id', $userIds)
                ->whereDate('created_at', $date->format('Y-m-d'))
                ->where('status', '!=', 'cancelled')
                ->distinct('user_id')
                ->count();
            
            $trend[] = [
                'date' => $date->format('Y-m-d'),
                'activeUsers' => $activeUsers,
                'percentage' => $activeUsers > 0 ? min(($activeUsers / 20) * 100, 100) : 0 // 假设最大20个活跃用户为100%
            ];
        }

        return $trend;
    }

    /**
     * 计算行为指标
     */
    private function calculateBehaviorMetrics(array $userIds, string $startDate, string $endDate): array
    {
        try {
            // 页面浏览统计
            $pageViews = CustomerBehaviorAnalytics::whereIn('user_id', $userIds)
                ->where('event_type', CustomerBehaviorAnalytics::EVENT_PAGE_VIEW)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();
            
            $activeUsers = Order::whereIn('user_id', $userIds)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('status', '!=', 'cancelled')
                ->distinct('user_id')
                ->count();
            
            $avgPageViews = $activeUsers > 0 ? round($pageViews / $activeUsers, 1) : 0;
            
            // 会话时长统计
            $avgSessionDuration = UserSession::whereIn('user_id', $userIds)
                ->whereBetween('start_time', [$startDate, $endDate])
                ->avg('duration') ?? 0;
            
            // 商品浏览统计
            $productViews = CustomerBehaviorAnalytics::whereIn('user_id', $userIds)
                ->where('event_type', CustomerBehaviorAnalytics::EVENT_PRODUCT_VIEW)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();
            
            $avgProductViews = $activeUsers > 0 ? round($productViews / $activeUsers, 1) : 0;

            return [
                'avgPageViews' => $avgPageViews,
                'avgSessionDuration' => round($avgSessionDuration, 0),
                'avgProductViews' => $avgProductViews,
                'pageViewsTrend' => 'up', // 简化处理，实际应该计算趋势
                'sessionTrend' => 'stable',
                'productViewsTrend' => 'up'
            ];
        } catch (\Exception $e) {
            Log::error('计算行为指标失败: ' . $e->getMessage());
            
            return [
                'avgPageViews' => 0,
                'avgSessionDuration' => 0,
                'avgProductViews' => 0,
                'pageViewsTrend' => 'stable',
                'sessionTrend' => 'stable',
                'productViewsTrend' => 'stable'
            ];
        }
    }

    /**
     * 获取高活跃客户列表
     */
    private function getTopActiveCustomers(array $userIds, string $startDate, string $endDate, int $limit = 10): array
    {
        $customers = User::whereIn('id', $userIds)
            ->with(['orders' => function($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate])
                      ->where('status', '!=', 'cancelled');
            }])
            ->get()
            ->map(function($user) use ($startDate, $endDate) {
                $orders = $user->orders;
                $totalOrders = $orders->count();
                $totalSpend = $orders->sum('total');
                $lastOrder = $orders->max('created_at');
                $lastActiveDay = $lastOrder ? Carbon::parse($lastOrder)->diffInDays(now()) : 999;
                
                // 计算活跃天数（有订单的天数）
                $activeDays = $orders->groupBy(function($order) {
                    return Carbon::parse($order->created_at)->format('Y-m-d');
                })->count();
                
                // 活跃度等级
                $activityLevel = 'low';
                if ($totalOrders >= 10 && $totalSpend >= 3000 && $lastActiveDay <= 3) {
                    $activityLevel = 'high';
                } elseif ($totalOrders >= 5 && $totalSpend >= 1500 && $lastActiveDay <= 7) {
                    $activityLevel = 'medium';
                }
                
                return [
                    'id' => $user->id,
                    'name' => $user->merchant_name ?: $user->name,
                    'phone' => $user->phone,
                    'totalOrders' => $totalOrders,
                    'totalSpend' => round($totalSpend, 2),
                    'activeDays' => $activeDays,
                    'lastActiveDay' => $lastActiveDay,
                    'activityLevel' => $activityLevel,
                    'activityScore' => $totalOrders * 10 + ($totalSpend / 100) - $lastActiveDay
                ];
            })
            ->filter(function($customer) {
                return $customer['totalOrders'] > 0; // 只返回有订单的客户
            })
            ->sortByDesc('activityScore')
            ->take($limit)
            ->values()
            ->toArray();

        return $customers;
    }

    /**
     * 生成活跃度提升建议
     */
    private function generateActivitySuggestions(array $activityOverview, array $activitySegments): array
    {
        $suggestions = [];
        
        // 基于活跃率生成建议
        if ($activityOverview['activityRate'] < 30) {
            $suggestions[] = [
                'id' => 1,
                'icon' => '📢',
                'title' => '加强客户激活',
                'description' => '活跃率偏低，建议通过优惠活动、新品推荐等方式激活沉睡客户',
                'priority' => 'high',
                'actionText' => '制定激活方案'
            ];
        }
        
        // 基于不活跃客户比例生成建议
        $inactiveSegment = collect($activitySegments)->firstWhere('level', 'inactive');
        if ($inactiveSegment && $inactiveSegment['percentage'] > 40) {
            $suggestions[] = [
                'id' => 2,
                'icon' => '🎯',
                'title' => '重点关注流失客户',
                'description' => '不活跃客户占比较高，建议分析流失原因并制定挽回策略',
                'priority' => 'high',
                'actionText' => '查看流失分析'
            ];
        }
        
        // 基于订单频次生成建议
        if ($activityOverview['avgOrderFreq'] < 3) {
            $suggestions[] = [
                'id' => 3,
                'icon' => '🔄',
                'title' => '提升复购频次',
                'description' => '平均订单频次较低，建议通过会员制度、定期配送等提升复购',
                'priority' => 'medium',
                'actionText' => '设计复购策略'
            ];
        }
        
        // 生鲜配送特色建议
        $suggestions[] = [
            'id' => 4,
            'icon' => '🥬',
            'title' => '优化生鲜品类',
            'description' => '根据季节和客户偏好调整商品结构，提升客户满意度',
            'priority' => 'medium',
            'actionText' => '查看商品分析'
        ];
        
        $suggestions[] = [
            'id' => 5,
            'icon' => '⏰',
            'title' => '优化配送时段',
            'description' => '分析客户下单时间偏好，优化配送时段安排',
            'priority' => 'low',
            'actionText' => '查看时间分析'
        ];

        return $suggestions;
    }
} 