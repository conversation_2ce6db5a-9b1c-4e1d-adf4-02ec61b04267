<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_merges', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->unsignedBigInteger('merged_order_id')->nullable()->comment('合并后的订单ID');
            $table->json('original_order_ids')->comment('原订单ID列表');
            $table->enum('merge_strategy', ['intelligent', 'conservative', 'hybrid'])->default('intelligent')->comment('合并策略');
            $table->date('merge_date')->comment('合并日期');
            
            // 金额对比信息
            $table->decimal('original_total_amount', 10, 2)->comment('原订单总金额');
            $table->decimal('merged_total_amount', 10, 2)->comment('合并后总金额');
            $table->decimal('total_savings', 10, 2)->default(0)->comment('节省金额');
            
            // 优惠详情
            $table->json('original_discount_info')->comment('原订单优惠信息');
            $table->json('merged_discount_info')->comment('合并后优惠信息');
            $table->json('discount_comparison')->comment('优惠对比详情');
            
            // 操作信息
            $table->unsignedBigInteger('merged_by')->nullable()->comment('操作人ID');
            $table->enum('merge_type', ['manual', 'auto'])->default('manual')->comment('合并类型');
            $table->text('merge_reason')->nullable()->comment('合并原因');
            $table->text('notes')->nullable()->comment('备注');
            
            // 状态控制
            $table->enum('status', ['active', 'reverted'])->default('active')->comment('合并状态');
            $table->timestamp('reverted_at')->nullable()->comment('撤销时间');
            $table->unsignedBigInteger('reverted_by')->nullable()->comment('撤销人');
            
            $table->timestamps();
            
            // 索引
            $table->index(['user_id', 'merge_date']);
            $table->index(['merged_order_id']);
            $table->index(['merge_date', 'status']);
            $table->foreign('user_id')->references('id')->on('users');
            $table->foreign('merged_order_id')->references('id')->on('orders');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_merges');
    }
}; 