<?php

namespace App\shop\Http\Controllers;

use App\Http\Controllers\Controller;
use App\shop\Services\ConfigService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SmsConfigController extends Controller
{
    /**
     * @var ConfigService
     */
    protected $configService;

    /**
     * 构造函数
     *
     * @param ConfigService $configService
     */
    public function __construct(ConfigService $configService)
    {
        $this->configService = $configService;
    }

    /**
     * 获取短信配置
     *
     * @return JsonResponse
     */
    public function getConfig(): JsonResponse
    {
        try {
            // 清除相关缓存，确保获取最新值
            $this->configService->clearCache();
            
            // 从数据库获取最新配置，不使用缓存
            $configs = [
                // 基本配置
                'sms_verification_enabled' => $this->configService->get('sms_verification_enabled', false, false),
                'sms_cooldown' => $this->configService->get('sms_cooldown', 60, false),
                'sms_expire' => $this->configService->get('sms_expire', 300, false),
                'sms_code_length' => $this->configService->get('sms_code_length', 6, false),
                
                // 阿里云配置
                'aliyun_access_key_id' => $this->configService->get('aliyun_access_key_id', '', false),
                'aliyun_access_key_secret' => $this->configService->get('aliyun_access_key_secret', '', false),
                'aliyun_sign_name' => $this->configService->get('aliyun_sign_name', '', false),
                'aliyun_template_login' => $this->configService->get('aliyun_template_login', '', false),
                'aliyun_template_register' => $this->configService->get('aliyun_template_register', '', false),
                'aliyun_template_reset_password' => $this->configService->get('aliyun_template_reset_password', '', false),
            ];
            
            // 添加日志输出，记录获取的配置值
            \Illuminate\Support\Facades\Log::info('获取短信配置', [
                'configs' => array_merge($configs, [
                    'aliyun_access_key_secret' => '******' // 隐藏敏感信息
                ])
            ]);

            return response()->json([
                'code' => 200,
                'message' => '获取配置成功',
                'data' => $configs
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取短信配置失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '获取配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 保存短信配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function saveConfig(Request $request): JsonResponse
    {
        try {
            // 预处理：将sms_verification_enabled转为布尔值
            $input = $request->all();
            if (isset($input['sms_verification_enabled'])) {
                // 将各种表示"真"的值转换为布尔true
                $input['sms_verification_enabled'] = filter_var(
                    $input['sms_verification_enabled'], 
                    FILTER_VALIDATE_BOOLEAN
                );
            }

            $validator = Validator::make($input, [
                'sms_verification_enabled' => 'sometimes|boolean',
                'sms_cooldown' => 'sometimes|integer|min:30|max:300',
                'sms_expire' => 'sometimes|integer|min:60|max:600',
                'sms_code_length' => 'sometimes|integer|min:4|max:8',
                
                'aliyun_access_key_id' => 'required_if:sms_verification_enabled,true|string',
                'aliyun_access_key_secret' => 'required_if:sms_verification_enabled,true|string',
                'aliyun_sign_name' => 'required_if:sms_verification_enabled,true|string',
                'aliyun_template_login' => 'required_if:sms_verification_enabled,true|string',
                'aliyun_template_register' => 'required_if:sms_verification_enabled,true|string',
                'aliyun_template_reset_password' => 'required_if:sms_verification_enabled,true|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'message' => $validator->errors()->first(),
                ], 422);
            }

            // 保存配置
            foreach ($input as $key => $value) {
                $this->configService->set($key, $value);
            }
            
            // 清除缓存，确保下次获取配置时能够获取到最新的值
            // 方法1：清除所有配置缓存（彻底但代价大）
            $this->configService->clearCache();
            
            // 方法2：只清除短信相关的缓存（更精确）
            // foreach ($input as $key => $value) {
            //     $this->configService->clearCache($key);
            // }

            return response()->json([
                'code' => 200,
                'message' => '配置保存成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '保存配置失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 