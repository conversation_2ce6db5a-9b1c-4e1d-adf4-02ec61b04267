<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('membership_levels', function (Blueprint $table) {
            $table->decimal('discount_rate', 8, 2)->default(0.00)->comment('固定金额减免（元）')->change();
        });
        
        DB::table('membership_levels')
            ->where('discount_rate', 100.00)
            ->update(['discount_rate' => 0.00]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('membership_levels', function (Blueprint $table) {
            $table->decimal('discount_rate', 5, 2)->default(100.00)->comment('折扣率，百分比')->change();
        });
        
        DB::table('membership_levels')
            ->where('discount_rate', 0.00)
            ->update(['discount_rate' => 100.00]);
    }
};
