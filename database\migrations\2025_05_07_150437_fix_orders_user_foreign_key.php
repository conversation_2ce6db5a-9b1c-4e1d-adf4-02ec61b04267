<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 禁用外键约束检查
        Schema::disableForeignKeyConstraints();
        
        // 先删除现有的外键约束
        try {
            DB::statement('ALTER TABLE orders DROP FOREIGN KEY orders_user_id_foreign');
        } catch (\Exception $e) {
            // 如果约束不存在则忽略错误
            echo "移除旧外键约束时出错，可能约束不存在: " . $e->getMessage() . "\n";
        }
        
        // 添加新的外键约束，指向users表
        Schema::table('orders', function (Blueprint $table) {
            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onDelete('cascade');
        });
        
        // 重新启用外键约束检查
        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 禁用外键约束检查
        Schema::disableForeignKeyConstraints();
        
        // 删除添加的外键约束
        Schema::table('orders', function (Blueprint $table) {
            $table->dropForeign('orders_user_id_foreign');
        });
        
        // 重新启用外键约束检查
        Schema::enableForeignKeyConstraints();
    }
};
