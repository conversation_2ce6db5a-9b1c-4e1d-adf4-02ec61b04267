<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 只有当base_unit字段存在时才执行数据迁移
        if (Schema::hasColumn('products', 'base_unit')) {
            // 确保所有商品都已设置base_unit_id
            $this->ensureBaseUnitIdExists();
        }
        
        // 移除旧字段
        Schema::table('products', function (Blueprint $table) {
            if (Schema::hasColumn('products', 'base_unit')) {
                $table->dropColumn('base_unit');
            }
            
            if (Schema::hasColumn('products', 'unit_conversion_rate')) {
                $table->dropColumn('unit_conversion_rate');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复旧字段
        Schema::table('products', function (Blueprint $table) {
            if (!Schema::hasColumn('products', 'base_unit')) {
                $table->enum('base_unit', ['kg', 'pcs', 'g', 'lb'])->default('pcs')->after('stock')->comment('基本单位（如：公斤，件，克，磅）');
            }
            
            if (!Schema::hasColumn('products', 'unit_conversion_rate')) {
                $table->decimal('unit_conversion_rate', 10, 2)->default(1)->after('base_unit')->comment('单位转换率，转换为基础单位（如：1斤 = 0.5kg）');
            }
        });
    }
    
    /**
     * 确保所有商品都已设置base_unit_id
     */
    private function ensureBaseUnitIdExists(): void
    {
        // 检查字段是否存在
        if (!Schema::hasColumn('products', 'base_unit')) {
            return;
        }
        
        $productsWithoutBaseUnitId = DB::table('products')
            ->whereNull('base_unit_id')
            ->whereNotNull('base_unit')
            ->get();
            
        foreach ($productsWithoutBaseUnitId as $product) {
            // 查找base_unit对应的unit记录
            $unit = DB::table('units')
                ->where('name', $product->base_unit)
                ->first();
                
            if ($unit) {
                // 更新product的base_unit_id
                DB::table('products')
                    ->where('id', $product->id)
                    ->update(['base_unit_id' => $unit->id]);
            } else {
                // 创建新的unit记录
                $unitId = DB::table('units')->insertGetId([
                    'name' => $product->base_unit,
                    'display_name' => $product->base_unit,
                    'category' => $this->guessUnitCategory($product->base_unit),
                    'is_base' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                
                // 更新product的base_unit_id
                DB::table('products')
                    ->where('id', $product->id)
                    ->update(['base_unit_id' => $unitId]);
            }
        }
    }
    
    /**
     * 根据单位名称猜测其类别
     */
    private function guessUnitCategory($unitName): string
    {
        $weightUnits = ['kg', 'g', 't', 'lb', 'oz', '千克', '克', '吨', '公斤', '斤'];
        $volumeUnits = ['l', 'ml', 'kl', 'cc', 'm³', 'dm³', 'cm³', '升', '毫升', '立方米', '立方厘米'];
        $lengthUnits = ['m', 'cm', 'mm', 'km', 'in', 'ft', 'yd', '米', '厘米', '毫米', '公里'];
        $packageUnits = ['box', 'carton', 'case', 'pack', 'pcs', 'pallet', '件', '箱', '盒', '包', '袋', '瓶', '罐', '桶'];
        
        $unitName = strtolower($unitName);
        
        if (in_array($unitName, $weightUnits)) {
            return 'weight';
        } elseif (in_array($unitName, $volumeUnits)) {
            return 'volume';
        } elseif (in_array($unitName, $lengthUnits)) {
            return 'length';
        } elseif (in_array($unitName, $packageUnits)) {
            return 'package';
        }
        
        return 'quantity'; // 默认类别
    }
}; 