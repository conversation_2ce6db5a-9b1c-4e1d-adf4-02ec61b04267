<?php

namespace App\Order\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Order\Models\Order;
use App\Order\Models\OrderCorrection;
use App\Order\Models\PaymentLink;
use App\Order\Models\PaymentRecord;
use App\Order\Services\PaymentLinkService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PaymentLinkController extends Controller
{
    protected $paymentLinkService;

    public function __construct(PaymentLinkService $paymentLinkService)
    {
        $this->paymentLinkService = $paymentLinkService;
    }

    /**
     * 获取付款链接列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = PaymentLink::with(['order', 'correction', 'creator']);

        // 搜索条件
        if ($request->has('order_id')) {
            $query->where('order_id', $request->order_id);
        }

        if ($request->has('payment_type')) {
            $query->where('payment_type', $request->payment_type);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('created_at', [$request->start_date, $request->end_date]);
        }

        $paymentLinks = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $paymentLinks
        ]);
    }

    /**
     * 微信支付回调处理（核心功能）
     */
    public function paymentCallback(Request $request, string $linkId): JsonResponse
    {
        try {
            $paymentLink = PaymentLink::findOrFail($linkId);

            // 1. 检查当前状态
            if ($paymentLink->status === 'paid') {
                return response()->json([
                    'code' => 200,
                    'message' => '支付已完成'
                ]);
            }

            if (!in_array($paymentLink->status, ['processing', 'active'])) {
                return response()->json([
                    'code' => 400,
                    'message' => '付款链接状态异常'
                ], 400);
            }

            // 2. 验证回调数据
            $paymentData = [
                'payment_method' => $request->input('payment_method', 'wechat'),
                'transaction_id' => $request->input('transaction_id'),
                'amount' => $request->input('amount'),
                'trade_status' => $request->input('trade_status', 'TRADE_SUCCESS'),
            ];

            // 验证金额是否匹配
            if ($paymentData['amount'] && abs($paymentData['amount'] - $paymentLink->amount) > 0.01) {
                Log::warning('支付金额不匹配', [
                    'payment_link_id' => $linkId,
                    'expected_amount' => $paymentLink->amount,
                    'actual_amount' => $paymentData['amount'],
                ]);
                
                return response()->json([
                    'code' => 400,
                    'message' => '支付金额不匹配'
                ], 400);
            }

            // 3. 处理支付成功
            if ($paymentData['trade_status'] === 'TRADE_SUCCESS') {
                // 使用数据库事务确保原子性
                DB::transaction(function () use ($paymentLink, $paymentData, $linkId) {
                    // 再次检查状态（防止并发）
                    $currentLink = PaymentLink::lockForUpdate()->find($linkId);
                    
                    if ($currentLink->status === 'paid') {
                        return; // 已经处理过了
                    }

                    // 完成支付
                    $currentLink->completePayment($paymentData);

                    // 处理支付成功后的业务逻辑
                    $this->handlePaymentSuccess($currentLink);

                    Log::info('微信支付回调处理成功', [
                        'payment_link_id' => $linkId,
                        'transaction_id' => $paymentData['transaction_id'],
                        'amount' => $paymentData['amount'],
                        'payment_method' => $paymentData['payment_method'],
                    ]);
                });

                return response()->json([
                    'code' => 200,
                    'message' => '支付处理成功'
                ]);
            } else {
                // 支付失败
                $paymentLink->failPayment('第三方支付失败：' . $paymentData['trade_status']);

                Log::info('支付失败', [
                    'payment_link_id' => $linkId,
                    'trade_status' => $paymentData['trade_status'],
                    'transaction_id' => $paymentData['transaction_id'],
                ]);

                return response()->json([
                    'code' => 400,
                    'message' => '支付失败'
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('支付回调处理失败', [
                'payment_link_id' => $linkId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'code' => 500,
                'message' => '处理失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 处理支付成功后的业务逻辑（关键修复）
     */
    private function handlePaymentSuccess(PaymentLink $paymentLink): void
    {
        try {
            // 1. 更新相关的PaymentRecord状态
            if ($paymentLink->correction_id) {
                // 补款场景：更新对应的PaymentRecord
                $paymentRecord = PaymentRecord::where('correction_id', $paymentLink->correction_id)
                    ->where('payment_type', 'supplement')
                    ->where('status', 'pending')
                    ->first();

                if ($paymentRecord) {
                    $paymentRecord->update([
                        'status' => 'success',
                        'paid_at' => now(),
                        'transaction_id' => $paymentLink->transaction_id,
                        'notes' => ($paymentRecord->notes ?? '') . ' [微信支付成功]'
                    ]);

                    Log::info('补款PaymentRecord状态已更新', [
                        'payment_record_id' => $paymentRecord->id,
                        'payment_link_id' => $paymentLink->id,
                        'transaction_id' => $paymentLink->transaction_id
                    ]);
                }
            } else {
                // 货到付款场景：创建PaymentRecord
                PaymentRecord::create([
                    'order_id' => $paymentLink->order_id,
                    'payment_type' => 'cod',
                    'business_type' => 'cod_final',
                    'amount' => $paymentLink->payment_amount ?? $paymentLink->amount,
                    'payment_method' => $paymentLink->payment_method,
                    'transaction_id' => $paymentLink->transaction_id,
                    'status' => 'success',
                    'paid_at' => now(),
                    'notes' => '货到付款微信支付成功'
                ]);
            }

            // 2. 更新订单状态
            $order = $paymentLink->order;
            if ($paymentLink->payment_type === 'cod') {
                // 货到付款完成，更新订单为已付款
                $order->update([
                    'payment_status' => 'paid',
                    'paid_at' => now()
                ]);
            }

            // 3. 如果是补款，检查是否需要更新订单更正状态
            if ($paymentLink->correction) {
                $correction = $paymentLink->correction;
                
                // 检查是否所有补款都已完成
                $pendingSupplements = $correction->paymentRecords()
                    ->where('payment_type', 'supplement')
                    ->where('status', 'pending')
                    ->count();

                if ($pendingSupplements === 0) {
                    Log::info('订单更正的所有补款已完成', [
                        'correction_id' => $correction->id,
                        'order_id' => $correction->order_id
                    ]);
                }
            }

        } catch (\Exception $e) {
            Log::error('支付成功后处理失败', [
                'payment_link_id' => $paymentLink->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 查询支付状态（用于前端轮询）
     */
    public function checkPaymentStatus(string $linkId): JsonResponse
    {
        try {
            $paymentLink = PaymentLink::findOrFail($linkId);

            $response = [
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'status' => $paymentLink->status,
                    'paid_at' => $paymentLink->paid_at,
                    'payment_method' => $paymentLink->payment_method,
                    'transaction_id' => $paymentLink->transaction_id,
                    'is_expired' => $paymentLink->isExpired(),
                    'expires_at' => $paymentLink->expires_at,
                    'progress' => $this->getPaymentProgress($paymentLink),
                ]
            ];

            // 如果是补款，返回更正信息
            if ($paymentLink->correction) {
                $response['data']['correction'] = [
                    'id' => $paymentLink->correction->id,
                    'correction_no' => $paymentLink->correction->correction_no,
                    'status' => $paymentLink->correction->status,
                ];
            }

            return response()->json($response);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 404,
                'message' => '付款链接不存在'
            ], 404);
        }
    }

    /**
     * 获取支付进度信息
     */
    private function getPaymentProgress(PaymentLink $paymentLink): array
    {
        $progress = [
            'step' => 1,
            'total_steps' => 4,
            'current_step_name' => '等待支付',
            'steps' => [
                ['name' => '生成付款链接', 'status' => 'completed', 'time' => $paymentLink->created_at],
                ['name' => '等待支付', 'status' => 'current', 'time' => null],
                ['name' => '支付确认', 'status' => 'pending', 'time' => null],
                ['name' => '完成', 'status' => 'pending', 'time' => null],
            ]
        ];

        switch ($paymentLink->status) {
            case 'processing':
                $progress['step'] = 3;
                $progress['current_step_name'] = '支付确认中';
                $progress['steps'][1]['status'] = 'completed';
                $progress['steps'][2]['status'] = 'current';
                break;
            
            case 'paid':
                $progress['step'] = 4;
                $progress['current_step_name'] = '支付完成';
                $progress['steps'][1]['status'] = 'completed';
                $progress['steps'][2]['status'] = 'completed';
                $progress['steps'][2]['time'] = $paymentLink->paid_at;
                $progress['steps'][3]['status'] = 'completed';
                $progress['steps'][3]['time'] = $paymentLink->paid_at;
                break;
            
            case 'expired':
            case 'cancelled':
                $progress['step'] = 1;
                $progress['current_step_name'] = '支付已失效';
                $progress['steps'][1]['status'] = 'failed';
                break;
        }

        return $progress;
    }

    /**
     * 生成货到付款链接
     */
    public function generateCodLink(Request $request, int $orderId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $order = Order::findOrFail($orderId);
            
            // 检查订单是否为货到付款
            if (!$order->is_cod) {
                return response()->json([
                    'code' => 400,
                    'message' => '该订单不是货到付款订单'
                ], 400);
            }

            // 新的业务逻辑：订单在任何环节都可以生成付款链接
            // 只排除已取消和已退款的订单
            if (in_array($order->status, ['cancelled', 'refunded'])) {
                return response()->json([
                    'code' => 400,
                    'message' => '已取消或已退款的订单无法生成付款链接'
                ], 400);
            }

            $paymentLink = $this->paymentLinkService->generateCodPaymentLink($order);

            return response()->json([
                'code' => 200,
                'message' => '货到付款链接生成成功',
                'data' => [
                    'payment_link' => $paymentLink->fresh(),
                    'payment_url' => $paymentLink->full_url ?? config('app.url') . '/payment/' . $paymentLink->id,
                    'qr_code_url' => $paymentLink->qr_code_url,
                    'progress' => $this->getPaymentProgress($paymentLink)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('生成货到付款链接失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '生成失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 生成补款链接
     */
    public function generateSupplementLink(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'correction_id' => 'required|exists:order_corrections,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $correction = OrderCorrection::findOrFail($request->correction_id);
            $paymentLink = $this->paymentLinkService->generateSupplementLink($correction);

            return response()->json([
                'code' => 200,
                'message' => '补款链接生成成功',
                'data' => [
                    'payment_link' => $paymentLink->fresh(),
                    'payment_url' => $paymentLink->full_url,
                    'progress' => $this->getPaymentProgress($paymentLink)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '生成失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 手动标记已付款
     */
    public function markAsPaid(Request $request, string $linkId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|in:cash,wechat,alipay,bank_transfer',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $this->paymentLinkService->markAsPaid(
                $linkId,
                $request->payment_method,
                Auth::id()
            );

            return response()->json([
                'code' => 200,
                'message' => '标记成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '标记失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取付款页面（用户端）
     */
    public function paymentPage(string $linkId)
    {
        try {
            $paymentLink = PaymentLink::with([
                'order' => function($query) {
                    $query->with(['user', 'items.product']);
                }, 
                'correction.items'
            ])->findOrFail($linkId);

            // 检查链接状态
            if ($paymentLink->isExpired()) {
                return view('payment.expired', [
                    'payment_link' => $paymentLink,
                    'message' => '付款链接已过期，请联系客服获取新的付款链接'
                ]);
            }

            if ($paymentLink->status === 'paid') {
                return view('payment.success', [
                    'payment_link' => $paymentLink,
                    'message' => '支付已完成'
                ]);
            }

            if ($paymentLink->status === 'cancelled') {
                return view('payment.cancelled', [
                    'payment_link' => $paymentLink,
                    'message' => '付款链接已取消'
                ]);
            }

            // 返回H5付款页面视图
            return view('payment.index', [
                'payment_link' => $paymentLink,
                'progress' => $this->getPaymentProgress($paymentLink)
            ]);

        } catch (\Exception $e) {
            return view('payment.error', [
                'error' => '付款链接不存在或已失效',
                'message' => '请联系客服获取新的付款链接'
            ]);
        }
    }

    /**
     * 发起支付（H5页面用）
     */
    public function initiatePay(Request $request, string $linkId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|in:wechat,alipay',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 400,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $paymentLink = PaymentLink::findOrFail($linkId);

            // 检查付款链接状态
            if ($paymentLink->status === 'paid') {
                return response()->json([
                    'code' => 400,
                    'message' => '订单已完成付款，请勿重复支付',
                    'data' => ['status' => 'paid']
                ], 400);
            }

            if ($paymentLink->isExpired()) {
                return response()->json([
                    'code' => 400,
                    'message' => '付款链接已过期'
                ], 400);
            }

            // 更新状态为处理中
            $paymentLink->update(['status' => 'processing']);

            // 生成支付参数（这里需要集成实际的微信支付SDK）
            $payData = [
                'payment_link_id' => $linkId,
                'amount' => $paymentLink->amount,
                'order_no' => $paymentLink->order->order_no,
                'payment_method' => $request->payment_method,
                'callback_url' => route('payment.callback', $linkId),
                // 实际项目中需要调用微信支付API生成支付参数
                'pay_params' => [
                    'appId' => config('wechat.app_id'),
                    'timeStamp' => time(),
                                         'nonceStr' => Str::random(32),
                    'package' => 'prepay_id=wx123456789',
                    'signType' => 'MD5',
                    'paySign' => 'signature_here'
                ]
            ];

            Log::info('支付发起', [
                'payment_link_id' => $linkId,
                'payment_method' => $request->payment_method,
                'amount' => $paymentLink->amount,
            ]);

            return response()->json([
                'code' => 200,
                'message' => '支付参数生成成功',
                'data' => $payData
            ]);

        } catch (\Exception $e) {
            Log::error('支付发起失败', [
                'payment_link_id' => $linkId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'code' => 500,
                'message' => '发起支付失败：' . $e->getMessage()
            ], 500);
        }
    }
} 