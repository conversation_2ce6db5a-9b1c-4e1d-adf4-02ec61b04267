<template>
	<view class="order-detail-container">
		<!-- 订单状态卡片 -->
		<view class="status-card">
			<view class="status-header">
				<view class="status-icon" :class="getStatusIconClass(orderInfo.status)">
					<text class="icon-text">{{ getStatusIcon(orderInfo.status) }}</text>
				</view>
				<view class="status-info">
					<text class="status-text">{{ getStatusText(orderInfo.status) }}</text>
					<text class="order-no">订单号：{{ orderInfo.order_no }}</text>
					<text class="order-time">下单时间：{{ formatTime(orderInfo.created_at) }}</text>
				</view>
			</view>
			
			<!-- 订单进度 -->
			<view class="progress-section" v-if="orderInfo.status !== 'cancelled'">
				<view class="progress-steps">
					<view 
						class="progress-step" 
						:class="{ active: isStepActive('pending'), completed: isStepCompleted('pending') }"
					>
						<view class="step-dot"></view>
						<text class="step-text">待付款</text>
					</view>
					<view 
						class="progress-step" 
						:class="{ active: isStepActive('paid'), completed: isStepCompleted('paid') }"
					>
						<view class="step-dot"></view>
						<text class="step-text">已付款</text>
					</view>
					<view 
						class="progress-step" 
						:class="{ active: isStepActive('shipped'), completed: isStepCompleted('shipped') }"
					>
						<view class="step-dot"></view>
						<text class="step-text">已发货</text>
					</view>
					<view 
						class="progress-step" 
						:class="{ active: isStepActive('delivered'), completed: isStepCompleted('delivered') }"
					>
						<view class="step-dot"></view>
						<text class="step-text">已送达</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 客户信息卡片 -->
		<view class="info-card">
			<view class="card-title">客户信息</view>
			<view class="client-info">
				<view class="client-avatar">
					<text class="avatar-text">{{ getClientAvatarText() }}</text>
				</view>
				<view class="client-details">
					<text class="client-name">{{ (orderInfo.user && orderInfo.user.merchant_name) || (orderInfo.user && orderInfo.user.name) || '未知客户' }}</text>
					<text class="client-contact" v-if="orderInfo.user && orderInfo.user.merchant_name">联系人：{{ orderInfo.user && orderInfo.user.name }}</text>
					<text class="client-phone">{{ orderInfo.user && orderInfo.user.phone }}</text>
				</view>
				<button class="contact-btn" @tap="contactClient">联系</button>
			</view>
		</view>
		
		<!-- 收货信息卡片 -->
		<view class="info-card">
			<view class="card-title">收货信息</view>
			<view class="address-info">
				<view class="address-header">
					<text class="contact-name">{{ orderInfo.contact_name }}</text>
					<text class="contact-phone">{{ orderInfo.contact_phone }}</text>
				</view>
				<text class="address-detail">{{ orderInfo.shipping_address }}</text>
			</view>
		</view>
		
		<!-- 商品信息卡片 -->
		<view class="info-card">
			<view class="card-title">商品信息</view>
			<view class="product-list">
				<view class="product-item" v-for="item in orderInfo.items" :key="item.id">
					<image class="product-image" :src="(item.product && item.product.image) || '/static/default-product.png'" mode="aspectFill"></image>
					<view class="product-info">
						<text class="product-name">{{ item.product_name }}</text>
						<text class="product-spec" v-if="item.product_sku">规格：{{ item.product_sku }}</text>
						<view class="product-price-qty">
							<text class="product-price">¥{{ item.price }}/{{ getProductUnit(item) }}</text>
							<text class="product-qty">x{{ item.quantity }}{{ getProductUnit(item) }}</text>
						</view>
					</view>
					<text class="item-total">¥{{ item.total }}</text>
				</view>
			</view>
		</view>
		
		<!-- 费用明细卡片 -->
		<view class="info-card">
			<view class="card-title">费用明细</view>
			<view class="cost-details">
				<view class="cost-item">
					<text class="cost-label">商品小计</text>
					<text class="cost-value">¥{{ orderInfo.subtotal || orderInfo.total }}</text>
				</view>
				<view class="cost-item" v-if="orderInfo.discount && orderInfo.discount > 0">
					<text class="cost-label">优惠金额</text>
					<text class="cost-value discount">-¥{{ orderInfo.discount }}</text>
				</view>
				<view class="cost-item" v-if="orderInfo.delivery_fee && orderInfo.delivery_fee > 0">
					<text class="cost-label">配送费</text>
					<text class="cost-value">¥{{ orderInfo.delivery_fee }}</text>
				</view>
				<view class="cost-item total">
					<text class="cost-label">订单总额</text>
					<text class="cost-value">¥{{ orderInfo.total }}</text>
				</view>
			</view>
		</view>
		
		<!-- 订单信息卡片 -->
		<view class="info-card">
			<view class="card-title">订单信息</view>
			<view class="order-details">
				<view class="detail-item">
					<text class="detail-label">支付方式</text>
					<text class="detail-value">{{ getPaymentMethodText(orderInfo.payment_method) }}</text>
				</view>
				<view class="detail-item" v-if="orderInfo.delivery_method">
					<text class="detail-label">配送方式</text>
					<text class="detail-value">{{ getDeliveryMethodText(orderInfo.delivery_method) }}</text>
				</view>
				<view class="detail-item" v-if="orderInfo.notes">
					<text class="detail-label">订单备注</text>
					<text class="detail-value">{{ orderInfo.notes }}</text>
				</view>
				<view class="detail-item" v-if="orderInfo.deliverer_info">
					<text class="detail-label">配送员</text>
					<text class="detail-value">{{ orderInfo.deliverer_info.name }} {{ orderInfo.deliverer_info.phone }}</text>
				</view>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view class="action-section" v-if="canOperate">
			<button 
				class="action-btn primary" 
				v-if="orderInfo.status === 'pending'"
				@tap="markAsPaid"
			>
				标记已付款
			</button>
			<button 
				class="action-btn primary" 
				v-if="orderInfo.status === 'paid'"
				@tap="markAsShipped"
			>
				标记已发货
			</button>
			<button 
				class="action-btn primary" 
				v-if="orderInfo.status === 'shipped'"
				@tap="markAsDelivered"
			>
				标记已送达
			</button>
			<button 
				class="action-btn danger" 
				v-if="['pending', 'paid'].includes(orderInfo.status)"
				@tap="cancelOrder"
			>
				取消订单
			</button>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
import orderApi from '../../api/order.js'
import { formatDateTime, formatDate } from '../../utils/date-formatter.js'
import config from '../../utils/config.js'

export default {
	data() {
		return {
			orderId: null,
			orderInfo: {},
			loading: false
		}
	},
	
	computed: {
		// 是否可以操作订单
		canOperate() {
			const userInfo = uni.getStorageSync(config.storageKeys.employeeInfo)
			return userInfo && ['admin', 'manager', 'crm_agent'].includes(userInfo.role)
		}
	},
	
	onLoad(options) {
		if (options.id) {
			this.orderId = options.id
			this.loadOrderDetail()
		}
	},
	
	onPullDownRefresh() {
		this.loadOrderDetail()
	},
	
	methods: {
		// 加载订单详情
		async loadOrderDetail() {
			if (!this.orderId) return
			
			this.loading = true
			try {
				const response = await orderApi.getOrderDetail(this.orderId)
				this.orderInfo = response.data
				
				// 设置页面标题
				uni.setNavigationBarTitle({
					title: `订单详情 - ${this.orderInfo.order_no || ''}`
				})
				
			} catch (error) {
				console.error('加载订单详情失败:', error)
				uni.showToast({
					title: '加载订单详情失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
				uni.stopPullDownRefresh()
			}
		},
		
		// 获取状态图标
		getStatusIcon(status) {
			const iconMap = {
				'pending': '⏰',
				'paid': '💰',
				'shipped': '🚚',
				'delivered': '✅',
				'cancelled': '❌'
			}
			return iconMap[status] || '📋'
		},
		
		// 获取状态图标样式类
		getStatusIconClass(status) {
			return `status-${status}`
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'pending': '待付款',
				'paid': '已付款',
				'shipped': '已发货',
				'delivered': '已送达',
				'cancelled': '已取消'
			}
			return statusMap[status] || status
		},
		
		// 获取支付方式文本
		getPaymentMethodText(method) {
			const methodMap = {
				'cod': '货到付款',
				'wechat': '微信支付',
				'alipay': '支付宝',
				'cash': '现金支付',
				'bank': '银行转账'
			}
			return methodMap[method] || method
		},
		
		// 获取配送方式文本
		getDeliveryMethodText(method) {
			const methodMap = {
				'standard': '标准配送',
				'express': '快递配送',
				'same_day': '当日达',
				'self_pickup': '自行提货'
			}
			return methodMap[method] || method
		},
		
		// 判断步骤是否激活
		isStepActive(step) {
			return this.orderInfo.status === step
		},
		
		// 判断步骤是否完成
		isStepCompleted(step) {
			const steps = ['pending', 'paid', 'shipped', 'delivered']
			const currentIndex = steps.indexOf(this.orderInfo.status)
			const stepIndex = steps.indexOf(step)
			return currentIndex > stepIndex
		},
		
		// 获取客户头像文本
		getClientAvatarText() {
			if (this.orderInfo.user && this.orderInfo.user.merchant_name) {
				return this.orderInfo.user.merchant_name.charAt(0)
			}
			return (this.orderInfo.user && this.orderInfo.user.name || 'U').charAt(0)
		},
		
		// 格式化时间
		formatTime(time) {
			return formatDateTime(time)
		},
		
		// 联系客户
		contactClient() {
			const phone = this.orderInfo.user && this.orderInfo.user.phone
			if (!phone) {
				uni.showToast({
					title: '客户电话号码为空',
					icon: 'none',
					duration: 2000
				})
				return
			}
			
			console.log('拨打电话:', phone)
			uni.makePhoneCall({
				phoneNumber: phone,
				success: () => {
					console.log('拨打电话成功')
				},
				fail: (error) => {
					console.error('拨打电话失败:', error)
					uni.showToast({
						title: '拨打电话失败',
						icon: 'none',
						duration: 2000
					})
				}
			})
		},
		
		// 标记已付款
		async markAsPaid() {
			await this.updateOrderStatus('paid', '确认标记为已付款？')
		},
		
		// 标记已发货
		async markAsShipped() {
			await this.updateOrderStatus('shipped', '确认标记为已发货？')
		},
		
		// 标记已送达
		async markAsDelivered() {
			await this.updateOrderStatus('delivered', '确认标记为已送达？')
		},
		
		// 取消订单
		async cancelOrder() {
			uni.showModal({
				title: '确认取消',
				content: '确定要取消这个订单吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							await orderApi.cancelOrder(this.orderId)
							uni.showToast({
								title: '订单已取消',
								icon: 'success'
							})
							this.loadOrderDetail()
						} catch (error) {
							console.error('取消订单失败:', error)
							uni.showToast({
								title: '取消订单失败',
								icon: 'none'
							})
						}
					}
				}
			})
		},
		
		// 更新订单状态
		async updateOrderStatus(status, confirmText) {
			uni.showModal({
				title: '确认操作',
				content: confirmText,
				success: async (res) => {
					if (res.confirm) {
						try {
							await orderApi.updateOrderStatus(this.orderId, status)
							uni.showToast({
								title: '状态更新成功',
								icon: 'success'
							})
							this.loadOrderDetail()
						} catch (error) {
							console.error('更新订单状态失败:', error)
							uni.showToast({
								title: '更新状态失败',
								icon: 'none'
							})
						}
					}
				}
			})
		},
		
		// 获取产品单位
		getProductUnit(item) {
			// 优先使用商品的单位信息
			if (item.product && item.product.unit) {
				return item.product.unit
			}
			// 如果没有单位信息，使用默认单位
			if (item.unit) {
				return item.unit
			}
			// 默认单位
			return '件'
		}
	}
}
</script>

<style lang="scss" scoped>
.order-detail-container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.status-card, .info-card {
	background: white;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-header {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.status-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	
	&.status-pending {
		background: #fff7e6;
	}
	
	&.status-paid {
		background: #e6f7ff;
	}
	
	&.status-shipped {
		background: #f6ffed;
	}
	
	&.status-delivered {
		background: #e8f5e8;
	}
	
	&.status-cancelled {
		background: #fff2f0;
	}
}

.icon-text {
	font-size: 36rpx;
}

.status-info {
	flex: 1;
}

.status-text {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.order-no, .order-time {
	display: block;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 4rpx;
}

.progress-section {
	margin-top: 30rpx;
}

.progress-steps {
	display: flex;
	justify-content: space-between;
	position: relative;
	
	&::before {
		content: '';
		position: absolute;
		top: 20rpx;
		left: 40rpx;
		right: 40rpx;
		height: 2rpx;
		background: #e8e8e8;
		z-index: 1;
	}
}

.progress-step {
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	z-index: 2;
}

.step-dot {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: #e8e8e8;
	margin-bottom: 12rpx;
	
	.progress-step.active & {
		background: #1890ff;
	}
	
	.progress-step.completed & {
		background: #52c41a;
	}
}

.step-text {
	font-size: 24rpx;
	color: #999;
	
	.progress-step.active &,
	.progress-step.completed & {
		color: #333;
		font-weight: bold;
	}
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.client-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.client-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.avatar-text {
	color: white;
	font-size: 32rpx;
	font-weight: bold;
}

.client-details {
	flex: 1;
}

.client-name {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.client-contact, .client-phone {
	display: block;
	font-size: 26rpx;
	color: #666;
	margin-bottom: 4rpx;
}

.contact-btn {
	padding: 16rpx 24rpx;
	background: #1890ff;
	color: white;
	border: none;
	border-radius: 8rpx;
	font-size: 26rpx;
}

.address-info {
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.address-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 12rpx;
}

.contact-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.contact-phone {
	font-size: 26rpx;
	color: #666;
}

.address-detail {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.product-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.product-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.product-image {
	width: 100rpx;
	height: 100rpx;
	border-radius: 8rpx;
	flex-shrink: 0;
}

.product-info {
	flex: 1;
}

.product-name {
	display: block;
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.product-spec {
	display: block;
	font-size: 24rpx;
	color: #999;
	margin-bottom: 8rpx;
}

.product-price-qty {
	display: flex;
	justify-content: space-between;
}

.product-price, .product-qty {
	font-size: 26rpx;
	color: #666;
}

.item-total {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.cost-details {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.cost-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	
	&.total {
		padding-top: 16rpx;
		border-top: 1rpx solid #e8e8e8;
		
		.cost-label, .cost-value {
			font-size: 32rpx;
			font-weight: bold;
		}
	}
}

.cost-label {
	font-size: 28rpx;
	color: #666;
}

.cost-value {
	font-size: 28rpx;
	color: #333;
	
	&.discount {
		color: #ff4d4f;
	}
}

.order-details {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}

.detail-label {
	font-size: 28rpx;
	color: #666;
	flex-shrink: 0;
	width: 160rpx;
}

.detail-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
	text-align: right;
}

.action-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 20rpx;
	border-top: 1rpx solid #e8e8e8;
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	padding: 24rpx;
	border: none;
	border-radius: 12rpx;
	font-size: 28rpx;
	font-weight: bold;
	
	&.primary {
		background: #1890ff;
		color: white;
	}
	
	&.danger {
		background: #ff4d4f;
		color: white;
	}
}

.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-text {
	font-size: 28rpx;
	color: #666;
}
</style> 