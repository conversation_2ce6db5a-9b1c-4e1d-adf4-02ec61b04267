<?php

namespace App\Product\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;

class ProductServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     */
    public function register()
    {
        // 注册服务绑定
        $this->app->singleton('product.service', function ($app) {
            return new \App\Product\Services\ProductService(
                $app->make(\App\Unit\Services\UnitService::class),
                $app->make(\App\Unit\Services\ProductUnitService::class)
            );
        });
        
        $this->app->singleton('category.service', function ($app) {
            return new \App\Product\Services\CategoryService();
        });
        
        // 注册价格计算服务
        $this->app->singleton(\App\Product\Services\PriceCalculationService::class, function ($app) {
            return new \App\Product\Services\PriceCalculationService();
        });
    }

    /**
     * 引导服务
     */
    public function boot()
    {
        // 加载路由
        $this->loadRoutes();
    }

    /**
     * 加载模块路由
     * 
     * 使用loadRoutesFrom方法，避免重复api前缀问题
     */
    protected function loadRoutes()
    {
        // 确保使用正确的路径
        $productPath = app_path('Product');
        
        // 路由未缓存时才加载
        if (app()->routesAreCached() === false) {
            // 加载Web路由
            if (file_exists($productPath . '/routes/web.php')) {
                $this->loadRoutesFrom($productPath . '/routes/web.php');
            }
            
            // 加载API路由 - 使用loadRoutesFrom方法
            if (file_exists($productPath . '/routes/api.php')) {
                $this->loadRoutesFrom($productPath . '/routes/api.php');
            }
        }
    }
} 