<?php

namespace App\Core\Repositories;

interface RepositoryInterface
{
    /**
     * 获取所有记录
     *
     * @param array $columns 要获取的字段
     * @return mixed
     */
    public function all(array $columns = ['*']);
    
    /**
     * 分页获取记录
     *
     * @param int $perPage 每页记录数
     * @param array $columns 要获取的字段
     * @return mixed
     */
    public function paginate(int $perPage = 15, array $columns = ['*']);
    
    /**
     * 按条件查找记录
     *
     * @param array $conditions 查询条件
     * @param array $columns 要获取的字段
     * @return mixed
     */
    public function findBy(array $conditions, array $columns = ['*']);
    
    /**
     * 按ID查找记录
     *
     * @param int $id 记录ID
     * @param array $columns 要获取的字段
     * @return mixed
     */
    public function find(int $id, array $columns = ['*']);
    
    /**
     * 创建记录
     *
     * @param array $data 记录数据
     * @return mixed
     */
    public function create(array $data);
    
    /**
     * 更新记录
     *
     * @param int $id 记录ID
     * @param array $data 记录数据
     * @return mixed
     */
    public function update(int $id, array $data);
    
    /**
     * 删除记录
     *
     * @param int $id 记录ID
     * @return mixed
     */
    public function delete(int $id);
    
    /**
     * 按多个ID查找记录
     *
     * @param array $ids ID数组
     * @param array $columns 要获取的字段
     * @return mixed
     */
    public function findMany(array $ids, array $columns = ['*']);
    
    /**
     * 按条件计数
     *
     * @param array $conditions 查询条件
     * @return int
     */
    public function count(array $conditions = []): int;
} 