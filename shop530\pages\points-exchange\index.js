// pages/points-exchange/index.js - 积分兑换确认页
const PointsAPI = require('../../utils/pointsApi');
const app = getApp();

Page({
  data: {
    // 商品ID和数量
    productId: '',
    quantity: 1,
    
    // 商品详情
    productDetail: {},
    
    // 用户积分信息
    userPoints: {
      availablePoints: 0,
      memberLevel: 'normal'
    },
    
    // 地址信息（实物商品需要）
    addressInfo: null,
    
    // 兑换中状态
    exchanging: false,
    
    // 页面加载状态
    loading: true
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    const { productId, quantity = 1 } = options;
    
    if (!productId) {
      wx.showToast({
        title: '商品信息错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    this.setData({
      productId,
      quantity: parseInt(quantity)
    });
    
    this.initPage();
  },

  /**
   * 初始化页面
   */
  async initPage() {
    wx.showLoading({ title: '加载中...' });

    try {
      await Promise.all([
        this.loadProductDetail(),
        this.loadUserPoints()
      ]);
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
      wx.hideLoading();
    }
  },

  /**
   * 加载商品详情
   */
  async loadProductDetail() {
    try {
      const result = await PointsAPI.getPointsProductDetail(this.data.productId);
      const productDetail = result.data;
      
      this.setData({ productDetail });
      
      // 如果是实物商品，需要获取地址信息
      if (productDetail.type === 'physical') {
        this.loadDefaultAddress();
      }
      
    } catch (error) {
      console.error('加载商品详情失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
      throw error;
    }
  },

  /**
   * 加载用户积分
   */
  async loadUserPoints() {
    try {
      const result = await PointsAPI.getUserPointsBalance();
      const userPoints = {
        availablePoints: result.data.balance || 0,
        memberLevel: result.data.level || 'normal'
      };
      
      this.setData({ userPoints });
      
    } catch (error) {
      console.error('加载用户积分失败:', error);
      throw error;
    }
  },

  /**
   * 加载默认地址
   */
  async loadDefaultAddress() {
    try {
      // 调用地址API获取默认地址
      const result = await wx.request({
        url: `${app.globalData.apiBase}/user/addresses/default`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        }
      });
      
      if (result.data.code === 0) {
        this.setData({ addressInfo: result.data.data });
      }
      
    } catch (error) {
      console.error('加载地址失败:', error);
    }
  },

  /**
   * 选择地址
   */
  onSelectAddress() {
    wx.navigateTo({
      url: '/pages/address/index?from=exchange'
    });
  },

  /**
   * 数量变更
   */
  onQuantityChange(e) {
    const action = e.currentTarget.dataset.action;
    let quantity = this.data.quantity;
    
    if (action === 'minus' && quantity > 1) {
      quantity--;
    } else if (action === 'plus') {
      quantity++;
    }
    
    this.setData({ quantity });
  },

  /**
   * 确认兑换
   */
  async onConfirmExchange() {
    const { productDetail, quantity, userPoints, addressInfo } = this.data;
    
    // 检查积分是否足够
    const totalPoints = productDetail.points_price * quantity;
    if (userPoints.availablePoints < totalPoints) {
      wx.showToast({
        title: '积分不足',
        icon: 'none'
      });
      return;
    }
    
    // 如果是实物商品，检查地址
    if (productDetail.type === 'physical' && !addressInfo) {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none'
      });
      return;
    }
    
    // 显示确认弹窗
    const confirmResult = await this.showConfirmDialog();
    if (!confirmResult) return;
    
    try {
      this.setData({ exchanging: true });
      
      // 调用兑换API
      await this.performExchange();
      
      // 兑换成功
      wx.showToast({
        title: '兑换成功',
        icon: 'success'
      });
      
      setTimeout(() => {
        // 跳转到兑换记录页面
        wx.redirectTo({
          url: '/pages/points/orders/index'
        });
      }, 1500);
      
    } catch (error) {
      console.error('兑换失败:', error);
      wx.showToast({
        title: error.message || '兑换失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ exchanging: false });
    }
  },

  /**
   * 显示确认对话框
   */
  showConfirmDialog() {
    return new Promise((resolve) => {
      const { productDetail, quantity } = this.data;
      const totalPoints = productDetail.points_price * quantity;
      
      wx.showModal({
        title: '确认兑换',
        content: `确定要用${PointsAPI.formatPoints(totalPoints)}兑换${productDetail.name} x${quantity}吗？`,
        success: (res) => {
          resolve(res.confirm);
        }
      });
    });
  },

  /**
   * 执行兑换
   */
  async performExchange() {
    const { productId, quantity, addressInfo } = this.data;
    
    const orderData = {
      items: [{
        product_id: productId,
        quantity: quantity
      }]
    };
    
    // 如果有地址信息，添加到订单中
    if (addressInfo) {
      orderData.shipping_address = addressInfo;
    }
    
    const result = await PointsAPI.createPointsOrder(orderData);
    return result.data;
  },

  /**
   * 工具方法
   */
  formatPoints(points) {
    return PointsAPI.formatPoints(points);
  },

  calculateTotalPoints() {
    const { productDetail, quantity } = this.data;
    return (productDetail.points_price || 0) * quantity;
  },

  calculateTotalCash() {
    const { productDetail, quantity } = this.data;
    return (productDetail.cash_price || 0) * quantity;
  }
}); 