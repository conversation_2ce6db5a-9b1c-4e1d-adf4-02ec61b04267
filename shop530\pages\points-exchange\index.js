// pages/points-exchange/index.js - 积分兑换确认页
const PointsAPI = require('../../utils/pointsApi');

Page({
  data: {
    // 商品ID和数量
    productId: '',
    quantity: 1,
    
    // 商品详情
    productDetail: {},
    
    // 用户积分信息
    userPoints: {
      availablePoints: 0,
      memberLevel: 'normal',
      levelName: '普通会员'
    },
    
    // 兑换资格检查结果
    eligibilityCheck: {
      eligible: true,
      requiredPoints: 0,
      userPoints: 0
    },
    
    // 地址信息（实物商品需要）
    addressInfo: null,
    
    // 兑换中状态
    exchanging: false,
    
    // 页面加载状态
    loading: true
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    const { productId, quantity = 1 } = options;
    
    if (!productId) {
      wx.showToast({
        title: '商品信息错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    this.setData({
      productId,
      quantity: parseInt(quantity)
    });
    
    // 并行加载数据
    Promise.all([
      this.loadProductDetail(),
      this.loadUserPoints()
    ]).finally(() => {
      this.setData({ loading: false });
    });
  },

  /**
   * 页面显示时触发
   */
  onShow() {
    console.log('积分兑换页面显示');
    
    // 如果是从地址选择页面返回，重新检查地址信息
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    // 检查当前页面是否有selectedAddress数据
    if (currentPage.data && currentPage.data.selectedAddress) {
      console.log('检测到地址选择返回的数据:', currentPage.data.selectedAddress);
      
      this.setData({
        addressInfo: currentPage.data.selectedAddress
      });
      
      // 清除临时数据
      currentPage.setData({ selectedAddress: null });
    } else {
      console.log('未检测到地址选择返回数据');
      
      // 尝试从上一个页面获取地址数据
      if (pages.length > 1) {
        const prevPage = pages[pages.length - 2];
        if (prevPage && prevPage.data && prevPage.data.selectedAddress) {
          console.log('从上一页面获取到地址数据:', prevPage.data.selectedAddress);
          this.setData({
            addressInfo: prevPage.data.selectedAddress
          });
          // 清除上一页的临时数据
          prevPage.setData({ selectedAddress: null });
        }
      }
    }
    
    // 检查addressInfo是否已经设置
    if (this.data.addressInfo) {
      console.log('当前已选择的地址信息:', this.data.addressInfo);
    } else {
      console.log('当前未选择地址');
    }
  },

  /**
   * 页面隐藏时触发
   */
  onHide() {
    // 页面隐藏时可以做一些清理工作
  },

  /**
   * 页面卸载时触发
   */
  onUnload() {
    // 页面卸载时清理数据
    this.setData({
      productDetail: {},
      userPoints: { availablePoints: 0, memberLevel: 'normal', levelName: '普通会员' },
      addressInfo: null,
      exchanging: false,
      loading: false
    });
  },

  /**
   * 加载商品详情
   */
  async loadProductDetail() {
    try {
      // 获取商品详情
      const productDetail = await this.getProductDetail(this.data.productId);
      
      this.setData({ productDetail });
      
      // 如果是实物商品，需要获取地址信息
      if (productDetail.category === 'physical') {
        this.loadDefaultAddress();
      }
      
      // 检查兑换资格
      await this.checkExchangeEligibility();
      
    } catch (error) {
      console.error('加载商品详情失败:', error);
      wx.showToast({
        title: '商品信息加载失败',
        icon: 'none'
      });
      
      // 3秒后返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 3000);
    }
  },

  /**
   * 获取商品详情
   */
  async getProductDetail(productId) {
    try {
      console.log('🔍 获取商品详情，ID:', productId);
      const result = await PointsAPI.getPointsProduct(productId);
      console.log('✅ 商品详情API响应:', result);
      
      if (result && result.data) {
        return result.data;
      } else {
        throw new Error('商品详情数据格式错误');
      }
    } catch (error) {
      console.error('获取商品详情失败:', error);
      throw error;
    }
  },

  /**
   * 加载用户积分
   */
  async loadUserPoints() {
    try {
      console.log('🔍 获取用户积分余额...');
      const result = await PointsAPI.getUserBalance();
      console.log('✅ 用户积分API响应:', result);
      
      if (result && result.data) {
        const userPoints = {
          availablePoints: result.data.balance || 0,
          memberLevel: result.data.membership_level || 'normal',
          levelName: result.data.membership_level_name || '普通会员'
        };
        
        this.setData({ userPoints });
        console.log('✅ 用户积分信息设置完成:', userPoints);
      } else {
        throw new Error('积分余额数据格式错误');
      }
      
    } catch (error) {
      console.error('加载用户积分失败:', error);
      wx.showToast({
        title: '获取积分信息失败',
        icon: 'none'
      });
    }
  },

  /**
   * 检查兑换资格
   */
  async checkExchangeEligibility() {
    try {
      console.log('🔍 检查兑换资格...');
      const result = await PointsAPI.checkExchangeEligibility(this.data.productId, this.data.quantity);
      console.log('✅ 兑换资格检查结果:', result);
      
      if (result && result.data) {
        // 使用API返回的正确字段名：can_exchange而不是eligible
        const { can_exchange, points_sufficient, reasons, required_points, user_points } = result.data;
        
        if (!can_exchange) {
          // 如果不能兑换，显示原因
          const reason = reasons && reasons.length > 0 ? reasons[0] : '当前无法兑换此商品';
          wx.showModal({
            title: '无法兑换',
            content: reason,
            showCancel: false,
            confirmText: '我知道了',
            success: () => {
              wx.navigateBack();
            }
          });
          return;
        }
        
        // 更新页面数据
        this.setData({
          eligibilityCheck: {
            eligible: can_exchange,
            pointsSufficient: points_sufficient,
            requiredPoints: required_points,
            userPoints: user_points
          }
        });
      }
      
    } catch (error) {
      console.error('检查兑换资格失败:', error);
      // 资格检查失败不影响页面显示，只记录错误
    }
  },

  /**
   * 加载默认地址
   */
  async loadDefaultAddress() {
    try {
      // TODO: 调用地址API获取默认地址
      // 暂时设置空地址，用户必须手动选择
      this.setData({ 
        addressInfo: null  // 设置为null，强制用户选择
      });
      
    } catch (error) {
      console.error('加载地址失败:', error);
      this.setData({ addressInfo: null });
    }
  },

  /**
   * 选择地址
   */
  onSelectAddress() {
    console.log('跳转到地址选择页面');
    
    // 清除当前页面的临时地址数据，避免干扰
    this.setData({
      selectedAddress: null
    });
    
    // 跳转到地址选择页面，并传递来源参数
    wx.navigateTo({
      url: '/pages/address/index?from=exchange',
      success: () => {
        console.log('成功跳转到地址选择页面');
      },
      fail: (error) => {
        console.error('跳转到地址选择页面失败:', error);
        wx.showToast({
          title: '打开地址页面失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 减少数量
   */
  onDecreaseQuantity() {
    const { quantity } = this.data;
    if (quantity > 1) {
      this.setData({
        quantity: quantity - 1
      });
    }
  },

  /**
   * 增加数量
   */
  onIncreaseQuantity() {
    const { quantity, productDetail } = this.data;
    
    // 检查库存限制（如果商品有库存字段）
    const maxQuantity = productDetail.stock || productDetail.inventory || 99;
    
    if (quantity < maxQuantity) {
      this.setData({
        quantity: quantity + 1
      });
    } else {
      wx.showToast({
        title: `最多只能兑换${maxQuantity}件`,
        icon: 'none'
      });
    }
  },

  /**
   * 输入数量
   */
  onQuantityInput(e) {
    const value = parseInt(e.detail.value) || 1;
    const { productDetail } = this.data;
    
    // 检查数量范围
    if (value < 1) {
      this.setData({ quantity: 1 });
      return;
    }
    
    const maxQuantity = productDetail.stock || productDetail.inventory || 99;
    if (value > maxQuantity) {
      this.setData({ quantity: maxQuantity });
      wx.showToast({
        title: `最多只能兑换${maxQuantity}件`,
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      quantity: value
    });
  },

  /**
   * 确认兑换
   */
  async onConfirmExchange() {
    try {
      console.log('确认兑换按钮被点击');
      
      // 检查是否正在兑换中
      if (this.data.exchanging) {
        console.log('正在兑换中，忽略重复点击');
        return;
      }
      
      // 检查是否有商品详情
      if (!this.data.productDetail || !this.data.productDetail.id) {
        console.log('商品信息不完整');
        wx.showToast({
          title: '商品信息不完整',
          icon: 'none'
        });
        return;
      }
      
      // 检查兑换资格
      if (!this.data.eligibilityCheck.eligible) {
        console.log('不符合兑换条件');
        wx.showToast({
          title: '您不符合兑换条件',
          icon: 'none'
        });
        return;
      }
      
      // 检查积分是否足够
      if (!this.data.eligibilityCheck.pointsSufficient) {
        console.log('积分不足');
        wx.showToast({
          title: '积分不足',
          icon: 'none'
        });
        return;
      }
      
      // 如果是实物商品，检查地址
      if (this.data.productDetail.category === 'physical') {
        console.log('实物商品，检查地址信息:', this.data.addressInfo);
        
        if (!this.data.addressInfo) {
          console.log('地址信息为空');
          wx.showToast({
            title: '请选择收货地址',
            icon: 'none'
          });
          return;
        }
        
        // 检查地址信息是否完整
        if (!this.data.addressInfo.name || !this.data.addressInfo.phone) {
          console.log('地址信息不完整:', this.data.addressInfo);
          wx.showToast({
            title: '收货地址信息不完整',
            icon: 'none'
          });
          return;
        }
      }
      
      // 显示确认对话框
      const confirmed = await this.showConfirmDialog();
      console.log('确认对话框结果:', confirmed);
      
      if (!confirmed) {
        console.log('用户取消了兑换');
        return;
      }
      
      console.log('开始执行兑换操作');
      this.setData({ exchanging: true });
      
      // 执行兑换
      const exchangeResult = await this.performExchange();
      console.log('兑换结果:', exchangeResult);
      
      if (exchangeResult && exchangeResult.success) {
        // 兑换成功，显示详细信息
        wx.showModal({
          title: '🎉 兑换成功！',
          content: `订单号：${exchangeResult.orderNo}\n消耗积分：${exchangeResult.pointsUsed}\n\n请在订单记录中查看详情`,
          showCancel: false,
          confirmText: '查看订单',
          success: () => {
            // 跳转到积分订单详情页面
            wx.redirectTo({
              url: `/pages/points-order-detail/index?id=${exchangeResult.orderId}`
            });
          }
        });
      } else {
        throw new Error('兑换返回结果异常');
      }
      
    } catch (error) {
      this.handleError(error, '确认兑换');
      
      wx.showToast({
        title: error.message || '兑换失败，请重试',
        icon: 'none',
        duration: 3000
      });
    } finally {
      this.setData({ exchanging: false });
    }
  },

  /**
   * 显示确认对话框
   */
  showConfirmDialog() {
    return new Promise((resolve) => {
      const { productDetail, quantity } = this.data;
      const totalPoints = (productDetail.points_price || productDetail.pointsPrice) * quantity;
      
      wx.showModal({
        title: '确认兑换',
        content: `确定要用${totalPoints}积分兑换《${productDetail.name}》×${quantity}吗？`,
        confirmText: '确认兑换',
        cancelText: '再想想',
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  },

  /**
   * 执行兑换
   */
  async performExchange() {
    try {
      const { productDetail, quantity, addressInfo } = this.data;
      
      // 构建订单数据
      const orderData = {
        items: [{
          product_id: productDetail.id,
          quantity: quantity,
          points_price: productDetail.points_price || productDetail.pointsPrice,
          product_name: productDetail.name,
          product_image: productDetail.image || productDetail.images?.[0]
        }]
      };
      
      // 如果是实物商品，添加配送信息
      if (productDetail.category === 'physical' && addressInfo && addressInfo.name) {
        // 根据积分订单表结构设置地址信息
        // 直接设置联系人姓名和电话字段
        orderData.contact_name = addressInfo.name;
        orderData.contact_phone = addressInfo.phone;
        
        // 设置配送方式
        orderData.delivery_method = 'express';
        
        // 构建完整地址字符串
        const fullAddress = addressInfo.address || 
          `${addressInfo.province || ''}${addressInfo.city || ''}${addressInfo.district || ''}${addressInfo.detail || ''}`;
        
        // 将地址信息存入shipping_address JSON字段
        orderData.shipping_address = JSON.stringify({
          name: addressInfo.name,
          phone: addressInfo.phone,
          province: addressInfo.province || '',
          city: addressInfo.city || '',
          district: addressInfo.district || '',
          detail: addressInfo.detail || '',
          address: fullAddress,
          postal_code: addressInfo.postal_code || ''
        });
        
        // 添加备注信息(如果有)
        if (addressInfo.notes) {
          orderData.delivery_notes = addressInfo.notes;
        }
      }
      
      console.log('🔍 创建积分订单，数据:', orderData);
      
      // 调用创建订单API
      const result = await PointsAPI.createPointsOrder(orderData);
      console.log('✅ 创建订单API响应:', result);
      
      if (result && result.data) {
        // 兑换成功，返回订单信息
        return {
          success: true,
          orderId: result.data.id,
          orderNo: result.data.order_no,
          pointsUsed: result.data.total_points,
          message: '兑换成功！'
        };
      } else {
        throw new Error('订单创建失败');
      }
      
    } catch (error) {
      console.error('执行兑换失败:', error);
      
      // 处理不同的错误情况
      let errorMessage = '兑换失败，请重试';
      if (error.statusCode === 400) {
        if (error.data && error.data.message) {
          errorMessage = error.data.message;
        }
      } else if (error.statusCode === 422) {
        // 验证错误
        if (error.data && error.data.errors) {
          const errors = Object.values(error.data.errors).flat();
          errorMessage = errors[0] || '数据验证失败';
        }
      }
      
      throw new Error(errorMessage);
    }
  },

  /**
   * 全局错误处理
   */
  handleError(error, context = '') {
    console.error(`${context}错误:`, error);
    
    let errorMessage = '操作失败，请重试';
    
    if (error.statusCode) {
      switch (error.statusCode) {
        case 401:
          errorMessage = '请先登录';
          break;
        case 403:
          errorMessage = '没有操作权限';
          break;
        case 404:
          errorMessage = '商品不存在或已下架';
          break;
        case 422:
          errorMessage = error.data?.message || '数据验证失败';
          break;
        case 500:
          errorMessage = '服务器繁忙，请稍后重试';
          break;
        default:
          errorMessage = error.data?.message || error.message || errorMessage;
      }
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    wx.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    });
    
    return errorMessage;
  },

  /**
   * 页面下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新页面数据');
    
    this.setData({ loading: true });
    
    Promise.all([
      this.loadProductDetail(),
      this.loadUserPoints()
    ]).finally(() => {
      this.setData({ loading: false });
      wx.stopPullDownRefresh();
    });
  }
}); 