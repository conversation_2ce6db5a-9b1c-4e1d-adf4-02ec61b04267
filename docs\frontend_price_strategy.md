# 前端商场价格处理策略

## 🎯 核心策略

### 1. **用户状态识别**
- **游客状态**：显示基础价格和区域价格
- **登录状态**：显示个性化价格（包含会员优惠）
- **状态切换**：登录/退出时自动更新价格

### 2. **价格计算优先级**
```
1. 商品特定价格（最高优先级）
   ├── 商品区域价格
   └── 商品会员减免

2. 分类级别价格
   ├── 分类区域价格
   └── 分类会员减免

3. 全局价格（最低优先级）
   ├── 基础价格
   └── 全局会员减免
```

## 🚀 API接口设计

### 1. **获取单个商品价格**
```http
GET /shop/api/products/{productId}/price?region_id=1&quantity=2
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "product": {
      "id": 1,
      "name": "商品名称",
      "code": "P001",
      "category_id": 2
    },
    "pricing": {
      "base_price": 100.00,
      "final_price": 85.00,
      "total_discount": 15.00,
      "price_type": "product_specific",
      "discount_info": [
        {
          "type": "region_price",
          "name": "区域价格",
          "discount_amount": 10.00
        },
        {
          "type": "product_member_discount",
          "name": "商品会员减免",
          "discount_amount": 5.00
        }
      ],
      "user_type": "member",
      "region_id": 1
    },
    "context": {
      "is_logged_in": true,
      "user_id": 123,
      "membership_level": "银牌会员",
      "region_id": 1,
      "quantity": 2,
      "calculated_at": "2024-01-20T10:30:00Z"
    }
  }
}
```

### 2. **批量获取商品价格**
```http
POST /shop/api/products/batch-prices
Content-Type: application/json

{
  "product_ids": [1, 2, 3, 4, 5],
  "region_id": 1
}
```

### 3. **获取用户优惠信息**
```http
GET /shop/api/user/discount-info
Authorization: Bearer {token}
```

### 4. **价格变更通知**
```http
POST /shop/api/price/change-notification
Authorization: Bearer {token}
```

## 📱 前端实现策略

### 1. **页面加载时**
```javascript
// 商品列表页
async function loadProductList() {
  const productIds = getVisibleProductIds();
  const regionId = getCurrentRegionId();
  
  const prices = await fetchBatchPrices(productIds, regionId);
  updateProductPrices(prices);
}

// 商品详情页
async function loadProductDetail(productId) {
  const regionId = getCurrentRegionId();
  const quantity = getSelectedQuantity();
  
  const priceData = await fetchProductPrice(productId, regionId, quantity);
  updateProductPrice(priceData);
}
```

### 2. **用户登录/退出时**
```javascript
// 登录成功后
async function onUserLogin() {
  // 通知后端清除缓存
  await notifyPriceChange();
  
  // 重新获取当前页面的价格
  await refreshCurrentPagePrices();
  
  // 显示用户优惠信息
  const discountInfo = await fetchUserDiscountInfo();
  showUserDiscountInfo(discountInfo);
}

// 退出登录后
async function onUserLogout() {
  // 通知后端清除缓存
  await notifyPriceChange();
  
  // 重新获取当前页面的价格（游客价格）
  await refreshCurrentPagePrices();
  
  // 隐藏用户优惠信息
  hideUserDiscountInfo();
}
```

### 3. **价格显示组件**
```javascript
// 价格显示组件
function PriceDisplay({ priceData }) {
  const { base_price, final_price, total_discount, discount_info } = priceData.pricing;
  
  return (
    <div className="price-display">
      {/* 最终价格 */}
      <span className="final-price">¥{final_price}</span>
      
      {/* 原价（如果有优惠） */}
      {total_discount > 0 && (
        <span className="original-price">¥{base_price}</span>
      )}
      
      {/* 优惠标签 */}
      {discount_info.map(discount => (
        <span key={discount.type} className="discount-tag">
          {discount.name}
        </span>
      ))}
      
      {/* 会员专享标识 */}
      {priceData.context.is_logged_in && (
        <span className="member-exclusive">会员专享</span>
      )}
    </div>
  );
}
```

### 4. **缓存策略**
```javascript
// 前端价格缓存
class PriceCache {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟
  }
  
  getCacheKey(productId, userId, regionId, quantity) {
    return `${productId}_${userId || 'guest'}_${regionId || 'default'}_${quantity}`;
  }
  
  get(productId, userId, regionId, quantity) {
    const key = this.getCacheKey(productId, userId, regionId, quantity);
    const cached = this.cache.get(key);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    
    return null;
  }
  
  set(productId, userId, regionId, quantity, data) {
    const key = this.getCacheKey(productId, userId, regionId, quantity);
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }
  
  clearUserCache(userId) {
    for (const [key, value] of this.cache.entries()) {
      if (key.includes(`_${userId}_`)) {
        this.cache.delete(key);
      }
    }
  }
  
  clearAll() {
    this.cache.clear();
  }
}
```

## 🔄 状态管理

### 1. **用户状态变化处理**
```javascript
// 状态管理器
class UserStateManager {
  constructor() {
    this.currentUser = null;
    this.priceCache = new PriceCache();
    this.listeners = [];
  }
  
  // 用户登录
  async login(user) {
    const oldUserId = this.currentUser?.id;
    this.currentUser = user;
    
    // 清除旧用户的价格缓存
    if (oldUserId) {
      this.priceCache.clearUserCache(oldUserId);
    }
    
    // 通知所有监听器
    this.notifyListeners('login', user);
    
    // 通知后端
    await this.notifyBackendPriceChange();
  }
  
  // 用户退出
  async logout() {
    const oldUserId = this.currentUser?.id;
    this.currentUser = null;
    
    // 清除用户价格缓存
    if (oldUserId) {
      this.priceCache.clearUserCache(oldUserId);
    }
    
    // 通知所有监听器
    this.notifyListeners('logout', null);
    
    // 通知后端
    await this.notifyBackendPriceChange();
  }
  
  // 添加状态监听器
  addListener(callback) {
    this.listeners.push(callback);
  }
  
  // 通知监听器
  notifyListeners(event, user) {
    this.listeners.forEach(callback => {
      callback(event, user);
    });
  }
  
  // 通知后端价格变更
  async notifyBackendPriceChange() {
    try {
      await fetch('/shop/api/price/change-notification', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      console.error('通知后端价格变更失败:', error);
    }
  }
}
```

### 2. **页面组件监听**
```javascript
// 商品组件监听用户状态变化
function ProductComponent({ productId }) {
  const [priceData, setPriceData] = useState(null);
  const userStateManager = getUserStateManager();
  
  useEffect(() => {
    // 监听用户状态变化
    const handleUserStateChange = async (event, user) => {
      // 重新获取价格
      const newPriceData = await fetchProductPrice(productId);
      setPriceData(newPriceData);
    };
    
    userStateManager.addListener(handleUserStateChange);
    
    // 初始加载价格
    loadInitialPrice();
    
    return () => {
      // 清理监听器
      userStateManager.removeListener(handleUserStateChange);
    };
  }, [productId]);
  
  return (
    <div>
      {priceData && <PriceDisplay priceData={priceData} />}
    </div>
  );
}
```

## 🎨 用户体验优化

### 1. **价格变化动画**
```css
.price-display {
  transition: all 0.3s ease;
}

.price-updating {
  opacity: 0.6;
  transform: scale(0.98);
}

.price-updated {
  animation: priceHighlight 0.5s ease;
}

@keyframes priceHighlight {
  0% { background-color: #fff3cd; }
  100% { background-color: transparent; }
}
```

### 2. **加载状态**
```javascript
function PriceDisplay({ priceData, loading }) {
  if (loading) {
    return <div className="price-skeleton">加载中...</div>;
  }
  
  return (
    <div className="price-display">
      {/* 价格内容 */}
    </div>
  );
}
```

### 3. **错误处理**
```javascript
async function fetchProductPrice(productId, regionId, quantity) {
  try {
    const response = await fetch(`/shop/api/products/${productId}/price`, {
      method: 'GET',
      headers: {
        'Authorization': getAuthToken() ? `Bearer ${getAuthToken()}` : '',
        'Content-Type': 'application/json'
      },
      params: { region_id: regionId, quantity }
    });
    
    if (!response.ok) {
      throw new Error('获取价格失败');
    }
    
    return await response.json();
  } catch (error) {
    console.error('价格获取错误:', error);
    
    // 返回基础价格作为降级方案
    return {
      pricing: {
        base_price: getProductBasePrice(productId),
        final_price: getProductBasePrice(productId),
        total_discount: 0,
        price_type: 'base',
        discount_info: [],
        user_type: 'guest'
      }
    };
  }
}
```

## 📊 性能优化

### 1. **批量请求**
- 商品列表页使用批量价格接口
- 减少API调用次数
- 提高页面加载速度

### 2. **缓存策略**
- 前端缓存5分钟
- 后端缓存5分钟
- 用户状态变化时清除缓存

### 3. **懒加载**
- 只为可见商品获取价格
- 滚动时动态加载价格
- 减少初始加载时间

这个策略确保了：
✅ **游客和会员的无缝体验**
✅ **实时的价格更新**
✅ **优秀的性能表现**
✅ **清晰的优惠展示**
✅ **稳定的错误处理** 