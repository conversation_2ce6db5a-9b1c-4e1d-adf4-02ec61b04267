<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WechatSubMerchant extends Model
{
    use HasFactory;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'wechat_sub_merchants';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'provider_id',
        'merchant_id',
        'name',
        'sub_mch_id',
        'sub_appid',
        'rate',
        'contact_name',
        'contact_phone',
        'contact_email',
        'business_license',
        'remarks',
        'is_active',
    ];

    /**
     * 类型转换
     *
     * @var array
     */
    protected $casts = [
        'rate' => 'float',
        'is_active' => 'boolean',
    ];

    /**
     * 获取该子商户所属的服务商
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(WechatServiceProvider::class, 'provider_id');
    }

    /**
     * 获取该子商户的所有支付记录
     */
    public function payments(): HasMany
    {
        return $this->hasMany(WechatServicePayment::class, 'sub_merchant_id');
    }
} 