/* pages/search/search.wxss */

/* 搜索页面样式 */
page {
  background-color: #f5f5f5;
  height: 100%;
}

/* 搜索页面容器 */
.search-page {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  padding-top: 90rpx; /* 为搜索框预留空间，但不要太多 */
  box-sizing: border-box;
}

/* 搜索容器 */
.search-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 6rpx 24rpx;
  background-color: #4CAF50; /* 使用与首页一致的主色调 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  height: auto;
  max-height: 90rpx; /* 限制最大高度 */
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

/* 搜索内部容器，增加层次感 */
.search-inner {
  width: 100%;
  background-color: #45a049; /* 稍微深一点的绿色，增加层次感 */
  border-radius: 8rpx;
  padding: 2rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

/* 搜索内容区域 */
.search-content {
  flex: 1;
  padding: 20rpx;
}

/* 搜索区块 */
.search-section {
  margin-bottom: 30rpx;
}

/* 区块头部 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.section-action {
  font-size: 24rpx;
  color: #999;
  padding: 10rpx;
}

/* 搜索标签 */
.search-tags {
  display: flex;
  flex-wrap: wrap;
}

.search-tag {
  padding: 10rpx 20rpx;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.search-tag.highlight {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

/* 搜索建议 */
.search-suggestions {
  background-color: #fff;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1px solid #f5f5f5;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background-color: #f9f9f9;
}

.suggestion-text {
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 搜索结果 */
.search-results {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 排序栏 */
.sort-bar {
  display: flex;
  height: 80rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 10;
}

.sort-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  position: relative;
}

.sort-item.active {
  color: #4CAF50;
  font-weight: 500;
}

.sort-item:active {
  background-color: #f9f9f9;
}

.price-sort {
  display: flex;
  flex-direction: column;
  margin-left: 8rpx;
  height: 24rpx;
}

.arrow {
  width: 0;
  height: 0;
  border-left: 6rpx solid transparent;
  border-right: 6rpx solid transparent;
}

.arrow.up {
  border-bottom: 6rpx solid #ccc;
  margin-bottom: 2rpx;
}

.arrow.down {
  border-top: 6rpx solid #ccc;
}

.arrow.active {
  border-bottom-color: #4CAF50;
  border-top-color: #4CAF50;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.error-text {
  font-size: 28rpx;
  color: #666;
  margin-top: 30rpx;
}

.error-action {
  margin-top: 30rpx;
  padding: 16rpx 40rpx;
  background-color: #4CAF50;
  border-radius: 40rpx;
}

.error-action text {
  color: #fff;
  font-size: 28rpx;
}

/* 空结果 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-top: 30rpx;
}

.empty-tips {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 网格模式 - 使用product-card组件 */
.product-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx;
}

/* 每个product-card占据50%宽度，实现一行两个商品 */
.product-grid product-card {
  width: 50%;
  box-sizing: border-box;
  padding: 10rpx;
}

/* 列表模式 - 使用product-card组件 */
.product-list {
  padding: 10rpx 20rpx;
}

.product-list product-card {
  width: 100%;
  margin-bottom: 20rpx;
}

/* 加载更多 */
.load-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
}

.load-more-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 没有更多 */
.no-more {
  text-align: center;
  padding: 30rpx 0;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}

/* 回到顶部按钮 */
.back-to-top {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

/* 搜索头部包装器 */
.search-header-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 搜索结果筛选栏 */
.filter-bar {
  display: flex;
  align-items: center;
  background-color: #fff;
  height: 80rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.filter-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 26rpx;
  color: #666;
  position: relative;
}

.filter-item.active {
  color: #ff4d4f;
  font-weight: 500;
}

.filter-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff4d4f;
  border-radius: 2rpx;
}

/* 结果列表样式 */
.result-list {
  padding-bottom: 120rpx;
}

.result-list.grid-mode {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.result-list.list-mode {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 空结果样式 */
.empty-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-tips {
  font-size: 26rpx;
  color: #999;
}

/* 骨架屏样式 */
.skeleton-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.skeleton-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 16rpx;
  overflow: hidden;
}

.skeleton-image {
  width: 100%;
  height: 240rpx;
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

.skeleton-title {
  width: 80%;
  height: 32rpx;
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 4rpx;
  margin-bottom: 16rpx;
}

.skeleton-price {
  width: 40%;
  height: 32rpx;
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 4rpx;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

/* 适配iPhone底部安全区域 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .search-content {
    padding-bottom: calc(30rpx + constant(safe-area-inset-bottom));
  }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .search-content {
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  }
}

/* 底部工具栏 */
.search-toolbar {
  position: fixed;
  right: 30rpx;
  bottom: 160rpx;
  display: flex;
  flex-direction: column;
  z-index: 99;
}

.toolbar-item {
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
} 