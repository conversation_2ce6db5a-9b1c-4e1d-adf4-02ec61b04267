<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 将description字段从TEXT升级为MEDIUMTEXT
            // TEXT: 65,535 字节 (约65KB)
            // MEDIUMTEXT: 16,777,215 字节 (约16MB)
            $table->mediumText('description')->nullable()->change();
            
            // 可选：添加富文本类型字段
            $table->enum('description_type', ['text', 'html', 'markdown', 'json'])
                  ->default('html')
                  ->comment('描述内容类型')
                  ->after('description');
            
            // 可选：添加富文本版本字段（用于兼容性）
            $table->string('description_version', 10)
                  ->default('1.0')
                  ->comment('富文本版本')
                  ->after('description_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 恢复为TEXT类型
            $table->text('description')->nullable()->change();
            
            // 删除新增字段
            if (Schema::hasColumn('products', 'description_type')) {
                $table->dropColumn('description_type');
            }
            
            if (Schema::hasColumn('products', 'description_version')) {
                $table->dropColumn('description_version');
            }
        });
    }
}; 