/**
 * 购物车修复测试脚本
 * 用于验证横向商品卡片购物车更新问题的修复
 */

// 模拟小程序环境
const mockWx = {
  showToast: (options) => console.log('Toast:', options),
  showModal: (options) => console.log('Modal:', options),
  getStorageSync: (key) => {
    if (key === 'token') return 'test-token-123';
    return null;
  },
  removeStorageSync: (key) => console.log('Remove storage:', key)
};

// 设置全局wx对象
global.wx = mockWx;

// 模拟request模块
const mockRequest = {
  put: async (url, data) => {
    console.log('🔄 API调用:', url, data);
    
    // 模拟成功响应
    return {
      data: {
        cart_item: {
          id: 123,
          product_id: 760,
          quantity: data.quantity,
          name: '测试商品'
        },
        deleted: false
      }
    };
  },
  
  get: async (url) => {
    console.log('🔄 API调用:', url);
    
    // 模拟购物车列表响应
    if (url.includes('/cart')) {
      return {
        data: {
          items: [
            {
              id: 123,
              product_id: 760,
              quantity: 80,
              name: '测试商品',
              min_sale_quantity: 1,
              unit: '件'
            }
          ]
        }
      };
    }
    
    return { data: {} };
  },
  
  del: async (url) => {
    console.log('🔄 API调用:', url);
    return { data: { success: true } };
  }
};

// 模拟require函数
global.require = (modulePath) => {
  if (modulePath === './request') {
    return mockRequest;
  }
  if (modulePath === './request-config') {
    return {
      getCurrentConfig: () => ({
        baseUrl: 'https://test.com/api'
      })
    };
  }
  return {};
};

// 导入购物车模块
const cartModule = require('./utils/cart-unified.js');

/**
 * 测试购物车更新功能
 */
async function testCartUpdate() {
  console.log('\n🧪 开始测试购物车更新功能...\n');
  
  try {
    // 测试1: 正常更新数量
    console.log('📝 测试1: 正常更新商品数量');
    const result1 = await cartModule.updateCartItemByProductId(760, 85);
    console.log('✅ 测试1结果:', result1);
    
    if (result1.success) {
      console.log('✅ 测试1通过: 返回格式正确');
    } else {
      console.log('❌ 测试1失败: 返回格式错误');
    }
    
    // 测试2: 添加商品到购物车
    console.log('\n📝 测试2: 添加商品到购物车');
    const result2 = await cartModule.addToCart({ id: 760, name: '测试商品' }, 1);
    console.log('✅ 测试2结果:', result2);
    
    if (result2.success) {
      console.log('✅ 测试2通过: 返回格式正确');
    } else {
      console.log('❌ 测试2失败: 返回格式错误');
    }
    
    console.log('\n🎉 所有测试完成!');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

/**
 * 模拟横向商品卡片的更新逻辑
 */
async function simulateHorizontalCardUpdate() {
  console.log('\n🧪 模拟横向商品卡片更新逻辑...\n');
  
  const product = { id: 760, name: '测试商品' };
  const quantity = 85;
  
  try {
    console.log('🔄 横向卡片更新购物车:', {
      product_id: product.id,
      product_name: product.name,
      quantity
    });
    
    const result = await cartModule.updateCartItemByProductId(product.id, quantity);
    
    if (result.success) {
      console.log('✅ 横向卡片购物车更新成功');
      return true;
    } else {
      throw new Error(result.message || '更新失败');
    }
    
  } catch (error) {
    console.error('❌ 横向卡片购物车更新失败:', error);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  (async () => {
    await testCartUpdate();
    await simulateHorizontalCardUpdate();
  })();
}

module.exports = {
  testCartUpdate,
  simulateHorizontalCardUpdate
};
