<?php

namespace App\User\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    /**
     * 用户列表
     */
    public function index(Request $request)
    {
        // 这里可以直接调用CRM的UserController方法
        // 或者重新实现用户管理逻辑
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->index($request);
    }

    /**
     * 创建用户
     */
    public function store(Request $request)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->store($request);
    }

    /**
     * 显示用户详情
     */
    public function show($id)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->show($id);
    }

    /**
     * 更新用户
     */
    public function update(Request $request, $id)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->update($request, $id);
    }

    /**
     * 删除用户
     */
    public function destroy($id)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->destroy($id);
    }

    /**
     * 搜索用户
     */
    public function search(Request $request)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->search($request);
    }

    /**
     * 获取可用员工
     */
    public function availableEmployees()
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->availableEmployees();
    }

    /**
     * 获取可用配送员
     */
    public function availableDeliverers()
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->availableDeliverers();
    }

    /**
     * 获取可用CRM专员
     */
    public function availableCrmAgents()
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->availableCrmAgents();
    }

    /**
     * 导出用户
     */
    public function exportUsers(Request $request)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->exportUsers($request);
    }

    /**
     * 按手机号查找用户
     */
    public function findByPhone($phone)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->findByPhone($phone);
    }

    /**
     * 更新用户状态
     */
    public function updateStatus(Request $request, $id)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->updateStatus($request, $id);
    }

    /**
     * 更新用户会员信息
     */
    public function updateMembership(Request $request, $id)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->updateMembership($request, $id);
    }

    /**
     * 更新用户余额
     */
    public function updateBalance(Request $request, $id)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->updateBalance($request, $id);
    }

    /**
     * 更新用户积分
     */
    public function updatePoints(Request $request, $id)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->updatePoints($request, $id);
    }

    /**
     * 更新用户会员等级
     */
    public function updateMembershipLevel(Request $request, $id)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->updateMembershipLevel($request, $id);
    }

    /**
     * 刷新用户会员等级
     */
    public function refreshMembershipLevel(Request $request, $id)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->refreshMembershipLevel($request, $id);
    }

    /**
     * 获取用户订单
     */
    public function getUserOrders(Request $request, $id)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->getUserOrders($request, $id);
    }

    /**
     * 获取用户统计
     */
    public function getUserStatistics($id)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->getUserStatistics($id);
    }

    /**
     * 分配代理
     */
    public function assignAgent(Request $request, $id)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->assignAgent($request, $id);
    }

    /**
     * 同步微信用户
     */
    public function syncWechatUsers(Request $request)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->syncWechatUsers($request);
    }

    /**
     * 调试方法
     */
    public function debugIndex(Request $request)
    {
        $crmController = new \App\Crm\Http\Controllers\UserController();
        return $crmController->debugIndex($request);
    }
} 