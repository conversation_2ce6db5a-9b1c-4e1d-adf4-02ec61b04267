<template>
  <div class="stock-manage-dialog">
    <!-- 库存管理界面内容 -->
    <el-table 
      :data="stockList" 
      :loading="loading"
      style="width: 100%"
    >
      <!-- 商品信息列 -->
      <el-table-column prop="product_name" label="商品名称" width="200" />
      <el-table-column prop="product_code" label="商品编码" width="120" />
      
      <!-- 库存信息列 -->
      <el-table-column prop="stock_quantity" label="库存数量" width="100" />
      <el-table-column prop="stock_unit" label="单位" width="80" />
      
      <!-- 成本价信息列 -->
      <el-table-column label="成本价" width="120">
        <template #default="scope">
          <div class="cost-price-info">
            <div class="current-cost">
              ¥{{ formatCurrency(scope.row.base_cost_price || scope.row.cost_price || 0) }}
            </div>
            <div class="cost-method">
              <el-tag size="small" type="info">移动平均</el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <!-- 库存价值列 -->
      <el-table-column label="库存价值" width="120">
        <template #default="scope">
          ¥{{ formatCurrency(getTotalValue(scope.row)) }}
        </template>
      </el-table-column>
      
      <!-- 时间信息列 -->
      <el-table-column prop="last_in_time" label="最后入库" width="150" />
      <el-table-column prop="last_out_time" label="最后出库" width="150" />
      
      <!-- 操作列 -->
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="showCostPriceDetail(scope.row)">
            成本详情
          </el-button>
          <el-button size="small" @click="refreshCostPrice(scope.row.id)">
            刷新成本
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 成本价详情对话框 -->
    <el-dialog
      v-model="costDetailVisible"
      title="成本价详情"
      width="600px"
    >
      <div v-if="selectedItem" class="cost-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="商品名称">
            {{ selectedItem.product_name }}
          </el-descriptions-item>
          <el-descriptions-item label="当前库存">
            {{ selectedItem.stock_quantity }} {{ selectedItem.stock_unit }}
          </el-descriptions-item>
          <el-descriptions-item label="移动加权平均">
            <span class="cost-value">¥{{ formatCurrency(selectedItem.cost_methods?.moving_average || 0) }}</span>
            <el-tag v-if="selectedItem.cost_calculation_method === 'moving_average'" type="success" size="small">当前使用</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="标准成本">
            <span class="cost-value">¥{{ formatCurrency(selectedItem.cost_methods?.standard || 0) }}</span>
            <el-tag v-if="selectedItem.cost_calculation_method === 'standard'" type="success" size="small">当前使用</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="先进先出">
            <span class="cost-value">¥{{ formatCurrency(selectedItem.cost_methods?.fifo || 0) }}</span>
            <el-tag v-if="selectedItem.cost_calculation_method === 'fifo'" type="success" size="small">当前使用</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="计算时间">
            {{ formatDateTime(selectedItem.cost_calculation_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后入库时间">
            {{ selectedItem.last_in_time || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后出库时间">
            {{ selectedItem.last_out_time || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getCostPriceInfo, refreshCostPrice as refreshCostPriceAPI, getCostMethods } from '@/api/product'

// 响应式数据
const loading = ref(false)
const stockList = ref([])
const costDetailVisible = ref(false)
const selectedItem = ref(null)

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    // 这里应该调用获取库存列表的API
    // const response = await getStockList()
    // stockList.value = response.data.map(formatStockItem)
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 格式化库存项目
const formatStockItem = (item) => {
  // 获取成本价信息 - 使用新的移动加权平均方法
  const costPriceInfo = item.cost_price_info || {}
  const movingAvgCost = costPriceInfo.moving_average_cost || item.base_cost_price
  const standardCost = costPriceInfo.standard_cost || item.cost_price
  const fifoCost = costPriceInfo.fifo_cost
  
  // 默认使用移动加权平均成本
  const currentCostPrice = movingAvgCost || standardCost || fifoCost || 0
  
  return {
    ...item,
    // 成本价相关信息
    cost_price: currentCostPrice,
    base_cost_price: movingAvgCost,
    cost_methods: {
      moving_average: movingAvgCost,
      standard: standardCost,
      fifo: fifoCost
    },
    cost_calculation_method: 'moving_average', // 当前使用的计算方法
    cost_calculation_time: costPriceInfo.calculation_time || item.updated_at,
    
    // 时间信息
    last_in_time: costPriceInfo.last_in_time ? formatDateTime(costPriceInfo.last_in_time) : (item.last_in_time || '-'),
    last_out_time: costPriceInfo.last_out_time ? formatDateTime(costPriceInfo.last_out_time) : (item.last_out_time || '-'),
  }
}

// 获取成本价信息
const getCostPriceInfoDetail = async (inventoryId) => {
  try {
    const response = await getCostPriceInfo(inventoryId)
    const result = await response.json()
    if (result.success) {
      return result.data
    } else {
      throw new Error(result.message || '获取成本价信息失败')
    }
  } catch (error) {
    console.error('获取成本价信息失败:', error)
    ElMessage.error('获取成本价信息失败: ' + (error.response?.data?.message || error.message))
    return null
  }
}

// 刷新成本价
const refreshCostPrice = async (inventoryId) => {
  try {
    loading.value = true
    const response = await refreshCostPriceAPI(inventoryId)
    const result = await response.json()
    if (result.success) {
      ElMessage.success('成本价刷新成功')
      // 重新加载数据
      await loadData()
      return result.data
    } else {
      throw new Error(result.message || '刷新成本价失败')
    }
  } catch (error) {
    console.error('刷新成本价失败:', error)
    ElMessage.error('刷新成本价失败: ' + (error.response?.data?.message || error.message))
    return null
  } finally {
    loading.value = false
  }
}

// 显示成本价详情
const showCostPriceDetail = async (item) => {
  selectedItem.value = item
  
  // 获取详细的成本价信息
  if (item.id) {
    const costInfo = await getCostPriceInfoDetail(item.id)
    if (costInfo) {
      selectedItem.value = formatStockItem({
        ...item,
        cost_price_info: costInfo
      })
    }
  }
  
  costDetailVisible.value = true
}

// 计算库存总价值
const getTotalValue = (item) => {
  const costPrice = item.base_cost_price || item.cost_price || 0
  const quantity = item.stock_quantity || 0
  return costPrice * quantity
}

// 格式化货币
const formatCurrency = (value) => {
  if (value === null || value === undefined) return '0.00'
  return Number(value).toFixed(2)
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  try {
    return new Date(dateTime).toLocaleString('zh-CN')
  } catch (error) {
    return dateTime
  }
}

// 获取成本计算方法列表
const getCostMethodsList = async () => {
  try {
    const response = await getCostMethods()
    const result = await response.json()
    if (result.success) {
      return result.data
    }
  } catch (error) {
    console.error('获取成本计算方法失败:', error)
  }
  return []
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.stock-manage-dialog {
  padding: 20px;
}

.cost-price-info {
  text-align: center;
}

.current-cost {
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.cost-method {
  font-size: 12px;
}

.cost-detail {
  padding: 10px 0;
}

.cost-value {
  font-weight: bold;
  color: #409eff;
  margin-right: 8px;
}
</style> 