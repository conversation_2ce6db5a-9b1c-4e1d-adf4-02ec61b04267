<?php

namespace App\Search\Services;

use App\Search\Repositories\SearchRepository;
use Illuminate\Support\Facades\Log;

class SearchService
{
    /**
     * @var SearchRepository
     */
    protected $searchRepository;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->searchRepository = app('search.repository');
    }

    /**
     * 搜索商品
     *
     * @param string $keyword 搜索关键词
     * @param array $options 搜索选项
     * @return array
     */
    public function searchProducts($keyword, array $options = [])
    {
        try {
            // 记录搜索关键词
            $this->logSearchKeyword($keyword);
            
            // 调用仓库方法执行搜索
            $results = $this->searchRepository->searchProducts($keyword, $options);
            
            // 记录搜索日志，包含结果数量
            $this->searchRepository->logSearch($keyword, $results['total'] ?? 0, $options['platform'] ?? 'web');
            
            return $results;
        } catch (\Exception $e) {
            Log::error('搜索商品失败', [
                'keyword' => $keyword,
                'options' => $options,
                'error' => $e->getMessage()
            ]);
            
            // 返回空结果
            return [
                'list' => [],
                'total' => 0,
                'page' => $options['page'] ?? 1,
                'pageSize' => $options['pageSize'] ?? 10,
                'hasMore' => false
            ];
        }
    }

    /**
     * 获取热门搜索关键词
     *
     * @param int $limit 限制数量
     * @return array
     */
    public function getHotKeywords($limit = 10)
    {
        try {
            return $this->searchRepository->getHotKeywords($limit);
        } catch (\Exception $e) {
            Log::error('获取热门搜索词失败', [
                'error' => $e->getMessage()
            ]);
            
            // 返回空结果
            return [];
        }
    }

    /**
     * 获取搜索建议
     *
     * @param string $keyword 搜索关键词
     * @param int $limit 限制数量
     * @return array
     */
    public function getSuggestions($keyword, $limit = 10)
    {
        try {
            return $this->searchRepository->getSuggestions($keyword, $limit);
        } catch (\Exception $e) {
            Log::error('获取搜索建议失败', [
                'keyword' => $keyword,
                'error' => $e->getMessage()
            ]);
            
            // 返回空结果
            return [];
        }
    }

    /**
     * 获取搜索统计数据
     *
     * @param array $options 选项
     * @return array
     */
    public function getSearchStats(array $options = [])
    {
        try {
            return $this->searchRepository->getSearchStats($options);
        } catch (\Exception $e) {
            Log::error('获取搜索统计数据失败', [
                'options' => $options,
                'error' => $e->getMessage()
            ]);
            
            // 返回空结果
            return [
                'hot_keywords' => [],
                'daily_stats' => [],
                'platform_stats' => [],
                'zero_result_keywords' => [],
                'total_searches' => 0,
                'unique_keywords' => 0
            ];
        }
    }

    /**
     * 记录搜索关键词（简单日志记录）
     *
     * @param string $keyword 搜索关键词
     * @return void
     */
    protected function logSearchKeyword($keyword)
    {
        // 简单记录到日志文件
        Log::info('用户搜索', [
            'keyword' => $keyword,
            'time' => now()->toDateTimeString(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }
} 