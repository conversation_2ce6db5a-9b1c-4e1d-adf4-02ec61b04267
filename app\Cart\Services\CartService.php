<?php

namespace App\Cart\Services;

use App\Cart\Models\Cart;
use App\Cart\Models\CartItem;
use App\Product\Models\Product;
use App\Product\Services\PriceCalculationService;
use Illuminate\Support\Facades\Auth;

class CartService
{
    protected $priceCalculationService;
    
    public function __construct(PriceCalculationService $priceCalculationService)
    {
        $this->priceCalculationService = $priceCalculationService;
    }
    
    /**
     * 获取用户的购物车
     * 
     * @return Cart
     */
    public function getCart()
    {
        $userId = Auth::id();
        // 获取或创建购物车
        $cart = Cart::firstOrCreate(['user_id' => $userId]);
        
        return $cart;
    }
    
    /**
     * 获取购物车的商品列表
     * 
     * @return array
     */
    public function getCartItems()
    {
        $cart = $this->getCart();
        $user = Auth::user();
        
        // 预加载商品和SKU信息
        $cartItems = $cart->items()->with(['product', 'sku'])->get();
        
        // 批量计算价格（性能优化）
        $productIds = $cartItems->pluck('product_id')->unique()->toArray();
        $regionId = $user->region_id ?? null;
        $batchPrices = $this->priceCalculationService->calculateBatchPrices($productIds, $user, $regionId);
        
        // 处理购物车项并应用批量计算的价格
        $items = $cartItems->map(function ($item) use ($user, $batchPrices) {
                // 格式化数据
                $data = [
                    'id' => $item->id,
                    'product_id' => $item->product_id,
                    'sku_id' => $item->sku_id,
                    'quantity' => $item->quantity,
                    'is_selected' => $item->is_selected,
                ];
                
                // 添加商品信息
                if ($item->product) {
                    $data['name'] = $item->product->name;
                    $data['cover_url'] = $item->product->cover_url;
                    $data['unit'] = $item->product->getSaleDefaultUnit()?->name ?? '';
                    
                    // 使用批量计算的价格数据
                    $priceData = $batchPrices[$item->product_id] ?? null;
                    
                    if ($priceData) {
                        // 重新计算数量相关的价格
                        $unitPrice = $priceData['final_price'];
                        $data['price'] = $unitPrice;
                        $data['original_price'] = $priceData['base_price'];
                        $data['has_discount'] = $priceData['final_price'] < $priceData['base_price'];
                        $data['price_labels'] = $priceData['price_labels'] ?? [];
                        $data['discount_info'] = $priceData['discount_info'] ?? [];
                        $data['price_type'] = $priceData['price_type'] ?? 'base';
                        
                        // 计算该商品的总价（单价 × 数量）
                        $data['item_total'] = $unitPrice * $item->quantity;
                    } else {
                        // 如果批量计算失败，使用基础价格
                        $data['price'] = $item->product->price;
                        $data['original_price'] = $item->product->price;
                        $data['has_discount'] = false;
                        $data['price_labels'] = [];
                        $data['discount_info'] = [];
                        $data['price_type'] = 'base';
                        $data['item_total'] = $item->product->price * $item->quantity;
                    }
                    
                    // 如果有SKU，添加SKU信息但保持使用计算后的价格
                    if ($item->sku) {
                        $data['sku_name'] = $item->sku->name;
                        // 注意：这里不再覆盖price，保持使用上面计算的区域价格和会员折扣
                        // 如果将来需要SKU特定的区域价格，可以在这里扩展
                        $data['sku_price'] = $item->sku->price; // 保存SKU原价供参考
                    }
                }
                
                return $data;
            });
        
        // 计算统计信息 - 使用计算后的价格
        $totalPrice = $items->where('is_selected', true)
            ->sum(function ($item) {
                return $item['price'] * $item['quantity'];
            });
        
        $totalQuantity = $items->sum('quantity');
        $selectedQuantity = $items->where('is_selected', true)->sum('quantity');
        
        return [
            'items' => $items,
            'total_price' => $totalPrice,
            'total_quantity' => $totalQuantity,
            'selected_quantity' => $selectedQuantity,
        ];
    }
    
    /**
     * 获取购物车商品数量
     * 
     * @return int
     */
    public function getCartItemsCount()
    {
        $cart = $this->getCart();
        
        return $cart->items()->sum('quantity');
    }
    
    /**
     * 添加商品到购物车
     * 
     * @param int $productId
     * @param int $quantity
     * @param int|null $skuId
     * @return CartItem
     * @throws \Exception
     */
    public function addToCart($productId, $quantity = 1, $skuId = null)
    {
        // 获取商品信息
        $product = Product::find($productId);
        if (!$product) {
            throw new \Exception('商品不存在');
        }
        
        // 检查商品状态
        if ($product->status != 1) {
            throw new \Exception('商品已下架');
        }
        
        // 检查库存策略
        $stockCheck = $product->checkStockWithPolicy($quantity);
        if (!$stockCheck['allowed']) {
            throw new \Exception($stockCheck['message']);
        }
        
        $cart = $this->getCart();
        
        // 查找是否存在相同商品
        $cartItem = $cart->items()
            ->where('product_id', $productId)
            ->where('sku_id', $skuId)
            ->first();
        
        if ($cartItem) {
            // 已存在，检查增加后的总数量是否超过库存
            $newQuantity = $cartItem->quantity + $quantity;
            $stockCheckForTotal = $product->checkStockWithPolicy($newQuantity);
            if (!$stockCheckForTotal['allowed']) {
                throw new \Exception($stockCheckForTotal['message']);
            }
            
            // 增加数量
            $cartItem->quantity = $newQuantity;
            $cartItem->save();
        } else {
            // 新增购物车项
            $cartItem = new CartItem([
                'product_id' => $productId,
                'sku_id' => $skuId,
                'quantity' => $quantity,
                'is_selected' => true,
            ]);
            
            $cart->items()->save($cartItem);
        }
        
        return $cartItem;
    }
    
    /**
     * 更新购物车项数量
     * 
     * @param int $itemId
     * @param int $quantity
     * @return CartItem
     */
    public function updateItemQuantity($itemId, $quantity)
    {
        $userId = Auth::id();
        $cartItem = CartItem::whereHas('cart', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->findOrFail($itemId);
        
        $cartItem->quantity = $quantity;
        $cartItem->save();
        
        return $cartItem;
    }
    
    /**
     * 从购物车中移除商品
     * 
     * @param int $itemId
     * @return bool
     */
    public function removeItem($itemId)
    {
        $userId = Auth::id();
        $cartItem = CartItem::whereHas('cart', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->findOrFail($itemId);
        
        return $cartItem->delete();
    }
    
    /**
     * 切换购物车项选中状态
     * 
     * @param int $itemId
     * @param bool $isSelected
     * @return CartItem
     */
    public function toggleItemSelected($itemId, $isSelected)
    {
        $userId = Auth::id();
        $cartItem = CartItem::whereHas('cart', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->findOrFail($itemId);
        
        $cartItem->is_selected = $isSelected;
        $cartItem->save();
        
        return $cartItem;
    }
    
    /**
     * 全选/取消全选购物车
     * 
     * @param bool $isSelected
     * @return bool
     */
    public function toggleAllSelected($isSelected)
    {
        $cart = $this->getCart();
        
        $cart->items()->update(['is_selected' => $isSelected]);
        
        return true;
    }
    
    /**
     * 清空购物车
     * 
     * @return bool
     */
    public function clearCart()
    {
        $cart = $this->getCart();
        
        return $cart->items()->delete();
    }
    
    /**
     * 合并本地购物车到用户账户
     * 
     * @param array $localCartItems
     * @return bool
     */
    public function mergeLocalCart($localCartItems)
    {
        foreach ($localCartItems as $item) {
            $this->addToCart(
                $item['id'],
                $item['quantity'] ?? 1,
                $item['sku_id'] ?? null
            );
        }
        
        return true;
    }
} 