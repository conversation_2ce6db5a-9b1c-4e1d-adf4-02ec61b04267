<?php

namespace App\Cart\Services;

use App\Cart\Models\Cart;
use App\Cart\Models\CartItem;
use App\Product\Models\Product;
use App\Product\Services\PriceCalculationService;
use Illuminate\Support\Facades\Auth;

class CartService
{
    protected $priceCalculationService;
    
    public function __construct(PriceCalculationService $priceCalculationService)
    {
        $this->priceCalculationService = $priceCalculationService;
    }
    
    /**
     * 获取用户的购物车
     * 
     * @return Cart
     */
    public function getCart()
    {
        $userId = Auth::id();
        // 获取或创建购物车
        $cart = Cart::firstOrCreate(['user_id' => $userId]);
        
        return $cart;
    }
    
    /**
     * 获取购物车的商品列表
     * 
     * @return array
     */
    public function getCartItems()
    {
        $cart = $this->getCart();
        $user = Auth::user();
        
        // 预加载商品和SKU信息
        $cartItems = $cart->items()->with(['product', 'sku', 'unit'])->get();
        
        // 批量计算价格（性能优化）
        $productIds = $cartItems->pluck('product_id')->unique()->toArray();
        $regionId = $user->region_id ?? null;
        $batchPrices = $this->priceCalculationService->calculateBatchPrices($productIds, $user, $regionId);
        
        // 处理购物车项并应用批量计算的价格
        $items = $cartItems->map(function ($item) use ($user, $batchPrices) {
                // 格式化数据
                $data = [
                    'id' => $item->id,
                    'product_id' => $item->product_id,
                    'sku_id' => $item->sku_id,
                    'quantity' => $item->quantity,
                    'is_selected' => $item->is_selected,
                ];
                
                // 添加商品信息
                if ($item->product) {
                    $data['name'] = $item->product->name;
                    $data['cover_url'] = $item->product->cover_url;
                    
                    // 获取单位信息 - 优先使用购物车项的单位，否则使用销售默认单位
                    $unit = $item->unit ?? $item->product->getSaleDefaultUnit();
                    $data['unit'] = $unit?->name ?? '';
                    $data['unit_id'] = $item->unit_id ?? $unit?->id;
                    $data['unit_symbol'] = $unit?->symbol ?? '';
                    
                    // 使用批量计算的价格数据
                    $priceData = $batchPrices[$item->product_id] ?? null;
                    
                    if ($priceData) {
                        // 重新计算数量相关的价格
                        $unitPrice = $priceData['final_price'];
                        $data['price'] = $unitPrice;
                        $data['original_price'] = $priceData['base_price'];
                        $data['has_discount'] = $priceData['final_price'] < $priceData['base_price'];
                        $data['price_labels'] = $priceData['price_labels'] ?? [];
                        $data['discount_info'] = $priceData['discount_info'] ?? [];
                        $data['price_type'] = $priceData['price_type'] ?? 'base';
                        
                        // 计算该商品的总价（单价 × 数量）
                        $data['item_total'] = $unitPrice * $item->quantity;
                    } else {
                        // 如果批量计算失败，使用基础价格
                        $data['price'] = $item->product->price;
                        $data['original_price'] = $item->product->price;
                        $data['has_discount'] = false;
                        $data['price_labels'] = [];
                        $data['discount_info'] = [];
                        $data['price_type'] = 'base';
                        $data['item_total'] = $item->product->price * $item->quantity;
                    }
                    
                    // 如果有SKU，添加SKU信息但保持使用计算后的价格
                    if ($item->sku) {
                        $data['sku_name'] = $item->sku->name;
                        // 注意：这里不再覆盖price，保持使用上面计算的区域价格和会员折扣
                        // 如果将来需要SKU特定的区域价格，可以在这里扩展
                        $data['sku_price'] = $item->sku->price; // 保存SKU原价供参考
                    }
                }
                
                return $data;
            });
        
        // 计算统计信息 - 使用计算后的价格
        $totalPrice = $items->where('is_selected', true)
            ->sum(function ($item) {
                return $item['price'] * $item['quantity'];
            });
        
        $totalQuantity = $items->sum('quantity');
        $selectedQuantity = $items->where('is_selected', true)->sum('quantity');
        
        return [
            'items' => $items,
            'total_price' => $totalPrice,
            'total_quantity' => $totalQuantity,
            'selected_quantity' => $selectedQuantity,
        ];
    }
    
    /**
     * 获取购物车商品数量
     * 
     * @return int
     */
    public function getCartItemsCount()
    {
        $cart = $this->getCart();
        
        return $cart->items()->sum('quantity');
    }
    
    /**
     * 添加商品到购物车
     * 
     * @param int $productId
     * @param int $quantity
     * @param int|null $skuId
     * @param int|null $unitId
     * @return CartItem
     * @throws \Exception
     */
    public function addToCart($productId, $quantity = 1, $skuId = null, $unitId = null)
    {
        // 获取商品信息
        $product = Product::find($productId);
        if (!$product) {
            throw new \Exception('商品不存在');
        }
        
        // 检查商品状态
        if ($product->status != 1) {
            throw new \Exception('商品已下架');
        }
        
        // 确定使用的单位ID：优先使用传入的单位ID，否则使用销售默认单位
        if (!$unitId) {
            $saleUnit = $product->getSaleDefaultUnit();
            if (!$saleUnit) {
                throw new \Exception("商品 {$product->name} 未设置销售单位，无法添加到购物车");
            }
            $unitId = $saleUnit->id;
        }
        
        // 验证单位是否有效
        if (!$product->isValidUnit($unitId)) {
            throw new \Exception("商品 {$product->name} 不支持指定的单位");
        }
        
        $cart = $this->getCart();
        
        // 查找是否存在相同商品（同产品、同SKU、同单位）
        $cartItem = $cart->items()
            ->where('product_id', $productId)
            ->where('sku_id', $skuId)
            ->where('unit_id', $unitId)
            ->first();
        
        // 🔥 新增：检查最小起购数量
        $minSaleQuantity = $product->min_sale_quantity ?? 1;
        
        if ($cartItem) {
            // 已存在，检查增加后的总数量是否超过库存
            $newQuantity = $cartItem->quantity + $quantity;
            $stockCheckForTotal = $product->checkStockWithPolicy($newQuantity, $unitId);
            if (!$stockCheckForTotal['allowed']) {
                throw new \Exception($stockCheckForTotal['message']);
            }
            
            // 增加数量
            $cartItem->quantity = $newQuantity;
            $cartItem->save();
        } else {
            // 新增购物车项 - 检查最小起购数量
            if ($quantity < $minSaleQuantity) {
                // 自动调整到最小起购数量
                $quantity = $minSaleQuantity;
                
                // 记录日志
                \Illuminate\Support\Facades\Log::info('购物车添加商品 - 自动调整到最小起购数量', [
                    'product_id' => $productId,
                    'product_name' => $product->name,
                    'original_quantity' => $quantity,
                    'min_sale_quantity' => $minSaleQuantity,
                    'adjusted_quantity' => $quantity
                ]);
            }
            
            // 检查调整后的数量是否满足库存策略
            $stockCheck = $product->checkStockWithPolicy($quantity, $unitId);
            if (!$stockCheck['allowed']) {
                throw new \Exception($stockCheck['message']);
            }
            
            $cartItem = new CartItem([
                'product_id' => $productId,
                'sku_id' => $skuId,
                'quantity' => $quantity,
                'unit_id' => $unitId,
                'is_selected' => true,
            ]);
            
            $cart->items()->save($cartItem);
        }
        
        return $cartItem;
    }
    
    /**
     * 更新购物车项数量
     * 🔥 新增：当数量低于最小起购数量时，删除商品
     * 
     * @param int $itemId
     * @param int $quantity
     * @return CartItem|null 返回null表示商品被删除
     */
    public function updateItemQuantity($itemId, $quantity)
    {
        $userId = Auth::id();
        $cartItem = CartItem::whereHas('cart', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->with('product')->findOrFail($itemId);
        
        // 🔥 新增：检查最小起购数量
        $product = $cartItem->product;
        $minSaleQuantity = $product->min_sale_quantity ?? 1;
        
        if ($quantity < $minSaleQuantity) {
            // 数量低于最小起购数量，删除商品
            \Illuminate\Support\Facades\Log::info('购物车更新数量 - 数量低于最小起购数量，删除商品', [
                'cart_item_id' => $itemId,
                'product_id' => $product->id,
                'product_name' => $product->name,
                'quantity' => $quantity,
                'min_sale_quantity' => $minSaleQuantity
            ]);
            
            $cartItem->delete();
            return null; // 返回null表示商品被删除
        }
        
        // 正常更新数量
        $cartItem->quantity = $quantity;
        $cartItem->save();
        
        return $cartItem;
    }
    
    /**
     * 从购物车中移除商品
     * 
     * @param int $itemId
     * @return bool
     */
    public function removeItem($itemId)
    {
        $userId = Auth::id();
        $cartItem = CartItem::whereHas('cart', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->findOrFail($itemId);
        
        return $cartItem->delete();
    }
    
    /**
     * 切换购物车项选中状态
     * 
     * @param int $itemId
     * @param bool $isSelected
     * @return CartItem
     */
    public function toggleItemSelected($itemId, $isSelected)
    {
        $userId = Auth::id();
        $cartItem = CartItem::whereHas('cart', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->findOrFail($itemId);
        
        $cartItem->is_selected = $isSelected;
        $cartItem->save();
        
        return $cartItem;
    }
    
    /**
     * 全选/取消全选购物车
     * 
     * @param bool $isSelected
     * @return bool
     */
    public function toggleAllSelected($isSelected)
    {
        $cart = $this->getCart();
        
        $cart->items()->update(['is_selected' => $isSelected]);
        
        return true;
    }
    
    /**
     * 清空购物车
     * 
     * @return bool
     */
    public function clearCart()
    {
        $cart = $this->getCart();
        
        return $cart->items()->delete();
    }
    
    /**
     * 合并本地购物车到用户账户
     * 
     * @param array $localCartItems
     * @return bool
     */
    public function mergeLocalCart($localCartItems)
    {
        foreach ($localCartItems as $item) {
            $this->addToCart(
                $item['id'],
                $item['quantity'] ?? 1,
                $item['sku_id'] ?? null,
                $item['unit_id'] ?? null
            );
        }
        
        return true;
    }
} 