<?php

namespace App\Providers;

use App\Models\ShopConfig;
use App\shop\Services\ConfigService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;

class ShopConfigServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(ConfigService::class, function ($app) {
            return new ConfigService();
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // 在系统启动时加载配置
        try {
            $configService = $this->app->make(ConfigService::class);
            
            // 记录启动日志
            \Illuminate\Support\Facades\Log::info('ShopConfigServiceProvider启动');
            
            try {
                // 加载微信小程序配置
                $wechatConfig = $configService->getWechatMiniProgramConfig(false); // 强制不使用缓存
                
                \Illuminate\Support\Facades\Log::info('加载微信小程序配置', [
                    'config_count' => count($wechatConfig),
                    'config_keys' => array_keys($wechatConfig)
                ]);
                
                // 动态设置微信配置
                if (!empty($wechatConfig['wx_mini_program_app_id'])) {
                    Config::set('wechat.mini_program.app_id', $wechatConfig['wx_mini_program_app_id']);
                    \Illuminate\Support\Facades\Log::info('设置APP_ID: ' . $wechatConfig['wx_mini_program_app_id']);
                }
                
                if (!empty($wechatConfig['wx_mini_program_secret'])) {
                    Config::set('wechat.mini_program.secret', $wechatConfig['wx_mini_program_secret']);
                    \Illuminate\Support\Facades\Log::info('设置Secret: 长度为' . strlen($wechatConfig['wx_mini_program_secret']));
                }
                
                if (!empty($wechatConfig['wx_mini_program_token'])) {
                    Config::set('wechat.mini_program.token', $wechatConfig['wx_mini_program_token']);
                }
                
                if (!empty($wechatConfig['wx_mini_program_aes_key'])) {
                    Config::set('wechat.mini_program.aes_key', $wechatConfig['wx_mini_program_aes_key']);
                }
            } catch (\Exception $configError) {
                // 配置加载异常
                \Illuminate\Support\Facades\Log::error('微信配置加载异常', [
                    'error' => $configError->getMessage(),
                    'trace' => $configError->getTraceAsString()
                ]);
            }
        } catch (\Exception $e) {
            // 服务异常
            \Illuminate\Support\Facades\Log::error('ShopConfigServiceProvider异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
} 