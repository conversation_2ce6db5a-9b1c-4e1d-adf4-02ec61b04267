<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类跳转功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .section h2 {
            color: #2c5aa0;
            margin-top: 0;
            margin-bottom: 15px;
        }
        
        .test-case {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #4CAF50;
        }
        
        .test-case h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        
        .test-case p {
            margin: 5px 0;
            color: #666;
        }
        
        .code {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
        
        .button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .button:hover {
            background: #45a049;
        }
        
        .button.secondary {
            background: #2196F3;
        }
        
        .button.secondary:hover {
            background: #1976D2;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .flow-diagram {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
            margin: 15px 0;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .flow-step .step-number {
            background: #007bff;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .flow-step .step-content {
            flex: 1;
        }
        
        .arrow {
            text-align: center;
            color: #666;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 分类跳转功能测试</h1>
        
        <div class="section">
            <h2>📋 功能概述</h2>
            <p>测试从主页点击分类跳转到分类页面，并自动选中对应分类的功能。</p>
            
            <div class="status success">
                <strong>✅ 已实现功能：</strong>
                <ul>
                    <li>主页分类点击跳转（使用 wx.switchTab + 全局数据传递）</li>
                    <li>模块化分类点击跳转（使用 wx.navigateTo + URL参数）</li>
                    <li>分类页面自动选中目标分类（支持三级分类）</li>
                    <li>智能展开父分类和祖父分类</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>🔄 跳转流程图</h2>
            
            <div class="flow-diagram">
                <h3>方式1: 主页分类网格跳转</h3>
                <div class="flow-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <strong>用户点击主页分类</strong><br>
                        触发 onCategoryTap 事件
                    </div>
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <strong>保存分类信息到全局数据</strong><br>
                        app.globalData.selectedCategoryFromHome = { id, name, timestamp }
                    </div>
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <strong>跳转到分类页面</strong><br>
                        wx.switchTab({ url: '/pages/category/category' })
                    </div>
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <strong>分类页面 onShow 检查全局数据</strong><br>
                        checkAndSelectCategoryFromHome()
                    </div>
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <strong>自动选中目标分类</strong><br>
                        selectCategoryById() → 展开父分类 → 选中分类 → 加载商品
                    </div>
                </div>
            </div>
            
            <div class="flow-diagram">
                <h3>方式2: 模块化组件跳转</h3>
                <div class="flow-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <strong>组件触发分类点击</strong><br>
                        event-handlers.js onCategoryTap
                    </div>
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <strong>URL参数跳转</strong><br>
                        wx.navigateTo({ url: `/pages/category/category?id=${category.id}` })
                    </div>
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <strong>分类页面 onLoad 接收参数</strong><br>
                        options.id → this.data.targetCategoryId
                    </div>
                </div>
                <div class="arrow">↓</div>
                <div class="flow-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <strong>initData 完成后自动选中</strong><br>
                        selectCategoryById(targetCategoryId)
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>🧪 测试用例</h2>
            
            <div class="test-case">
                <h3>测试1: 一级分类跳转</h3>
                <p><strong>场景：</strong>从主页点击一级分类（如"新鲜蔬菜"）</p>
                <p><strong>期望：</strong>跳转到分类页面，自动选中该一级分类，展开子分类，加载所有子分类商品</p>
                <div class="code">
                    // 模拟代码
                    selectedCategory = { id: 1, name: "新鲜蔬菜", level: 1 }
                    → selectMainCategory() 
                    → 触发一级分类点击事件
                    → 展开子分类 + 加载所有子分类商品
                </div>
            </div>
            
            <div class="test-case">
                <h3>测试2: 二级分类跳转</h3>
                <p><strong>场景：</strong>从主页点击二级分类（如"叶菜类"）</p>
                <p><strong>期望：</strong>跳转到分类页面，展开父分类，选中该二级分类，显示三级分类（如果有）</p>
                <div class="code">
                    // 模拟代码
                    selectedCategory = { id: 11, name: "叶菜类", level: 2, parentId: 1 }
                    → expandCategory(parentId) 
                    → selectSubCategory()
                    → 显示三级分类 + 加载该二级分类商品
                </div>
            </div>
            
            <div class="test-case">
                <h3>测试3: 三级分类跳转</h3>
                <p><strong>场景：</strong>从主页点击三级分类（如"菠菜"）</p>
                <p><strong>期望：</strong>跳转到分类页面，展开祖父分类和父分类，选中该三级分类</p>
                <div class="code">
                    // 模拟代码
                    selectedCategory = { id: 111, name: "菠菜", level: 3, parentId: 11, grandParentId: 1 }
                    → expandCategory(grandParentId)
                    → selectSubCategory(parentId) 
                    → selectThirdCategory()
                    → 加载该三级分类商品
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>🔧 核心实现方法</h2>
            
            <div class="code">
<strong>1. 主页分类点击处理：</strong>
onCategoryTap(e) {
  const category = e.currentTarget.dataset.category;
  
  // 保存到全局数据
  app.globalData.selectedCategoryFromHome = {
    id: category.id,
    name: category.name,
    timestamp: Date.now()
  };
  
  // 跳转到分类页面
  wx.switchTab({ url: '/pages/category/category' });
}

<strong>2. 分类页面检查跳转来源：</strong>
checkAndSelectCategoryFromHome() {
  const selectedCategory = app.globalData?.selectedCategoryFromHome;
  if (selectedCategory && selectedCategory.timestamp) {
    // 检查时间戳（5秒内有效）
    if (Date.now() - selectedCategory.timestamp < 5000) {
      this.selectCategoryWhenReady(selectedCategory.id);
      app.globalData.selectedCategoryFromHome = null;
    }
  }
}

<strong>3. 智能分类选择：</strong>
selectCategoryById(categoryId) {
  const targetCategory = this.findCategoryById(categoryId);
  
  if (targetCategory.level === 1) {
    this.selectMainCategory(targetCategory);
  } else if (targetCategory.level === 2) {
    this.selectSubCategory(targetCategory);
  } else if (targetCategory.level === 3) {
    this.selectThirdCategory(targetCategory);
  }
}
            </div>
        </div>
        
        <div class="section">
            <h2>✅ 验证要点</h2>
            <div class="status info">
                <strong>测试时需要验证：</strong>
                <ol>
                    <li>从主页点击分类能正确跳转到分类页面</li>
                    <li>分类页面能自动选中对应的分类</li>
                    <li>一级分类点击后能展开子分类并加载所有子分类商品</li>
                    <li>二级分类点击后能展开父分类并显示三级分类</li>
                    <li>三级分类点击后能正确展开层级结构</li>
                    <li>URL参数跳转和全局数据跳转都能正常工作</li>
                    <li>时间戳机制能防止重复处理</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
