// 认证API服务
export interface LoginParams {
  username: string
  password: string
  captcha: string
}

export interface LoginResponse {
  token: string
  user: {
    id: number
    username: string
    name: string
    email: string
    avatar?: string
    roles: string[]
    permissions: string[]
  }
  expires_in: number
}

export interface UserInfo {
  id: number
  username: string
  name: string
  email: string
  avatar?: string
  roles: string[]
  permissions: string[]
}

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 登录API
export const login = async (params: LoginParams): Promise<LoginResponse> => {
  await delay(1000) // 模拟网络延迟
  
  // 验证验证码
  if (!verifyCaptcha(params.captcha)) {
    throw new Error('验证码错误')
  }
  
  // 模拟登录验证
  if (params.username === 'admin' && params.password === '123456') {
    return {
      token: 'mock-jwt-token-' + Date.now(),
      user: {
        id: 1,
        username: 'admin',
        name: '管理员',
        email: '<EMAIL>',
        avatar: '/api/placeholder/40/40',
        roles: ['admin'],
        permissions: ['*']
      },
      expires_in: 7200 // 2小时
    }
  } else {
    throw new Error('用户名或密码错误')
  }
}

// 获取用户信息
export const getUserInfo = async (): Promise<UserInfo> => {
  await delay(500)
  
  const token = localStorage.getItem('token')
  if (!token) {
    throw new Error('未登录')
  }
  
  return {
    id: 1,
    username: 'admin',
    name: '管理员',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    roles: ['admin'],
    permissions: ['*']
  }
}

// 退出登录
export const logout = async (): Promise<void> => {
  await delay(300)
  localStorage.removeItem('token')
  localStorage.removeItem('user')
}

// 刷新token
export const refreshToken = async (): Promise<string> => {
  await delay(500)
  const newToken = 'refreshed-jwt-token-' + Date.now()
  localStorage.setItem('token', newToken)
  return newToken
}

// 生成验证码
export const generateCaptcha = (): { code: string, image: string } => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let code = ''
  for (let i = 0; i < 4; i++) {
    code += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  
  // 模拟验证码图片（实际应该是后端生成的图片URL）
  const image = `data:image/svg+xml;base64,${btoa(`
    <svg width="100" height="40" xmlns="http://www.w3.org/2000/svg">
      <rect width="100" height="40" fill="#f5f7fa"/>
      <text x="50" y="25" text-anchor="middle" font-family="Arial" font-size="18" fill="#333">${code}</text>
    </svg>
  `)}`
  
  return { code, image }
}

// 当前验证码（实际应该存储在session中）
let currentCaptcha = generateCaptcha()

// 获取验证码
export const getCaptcha = (): string => {
  currentCaptcha = generateCaptcha()
  return currentCaptcha.image
}

// 验证验证码
export const verifyCaptcha = (inputCode: string): boolean => {
  return inputCode.toUpperCase() === currentCaptcha.code
} 