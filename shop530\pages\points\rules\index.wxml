<!--pages/points/rules/index.wxml - 积分规则页面模板-->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="header-title">积分规则说明</view>
    <view class="header-subtitle">了解积分获取和使用规则</view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 规则内容 -->
  <view wx:else class="rules-content">
    
    <!-- 积分获取规则 -->
    <view class="rule-section">
      <view class="section-title">
        <text class="title-icon">🎁</text>
        <text class="title-text">积分获取规则</text>
      </view>
      
      <view class="rule-item">
        <view class="rule-header" bindtap="onToggleExpand" data-key="register">
          <text class="rule-name">注册奖励</text>
          <text class="rule-points">+{{rulesData.register_points || 100}}积分</text>
          <text class="expand-icon {{expandedItems.register ? 'expanded' : ''}}">▼</text>
        </view>
        <view wx:if="{{expandedItems.register}}" class="rule-detail">
          新用户注册成功后即可获得{{rulesData.register_points || 100}}积分奖励，仅限首次注册。
        </view>
      </view>

      <view class="rule-item">
        <view class="rule-header" bindtap="onToggleExpand" data-key="signin">
          <text class="rule-name">每日签到</text>
          <text class="rule-points">+{{rulesData.signin_points || 10}}积分</text>
          <text class="expand-icon {{expandedItems.signin ? 'expanded' : ''}}">▼</text>
        </view>
        <view wx:if="{{expandedItems.signin}}" class="rule-detail">
          每日首次签到可获得{{rulesData.signin_points || 10}}积分，连续签到有额外奖励。
        </view>
      </view>

      <view class="rule-item">
        <view class="rule-header" bindtap="onToggleExpand" data-key="purchase">
          <text class="rule-name">购物奖励</text>
          <text class="rule-points">消费获积分</text>
          <text class="expand-icon {{expandedItems.purchase ? 'expanded' : ''}}">▼</text>
        </view>
        <view wx:if="{{expandedItems.purchase}}" class="rule-detail">
          购买商品可获得积分奖励，具体积分数量根据商品设置而定。部分商品支持固定积分奖励，部分商品按消费金额比例获得积分。
        </view>
      </view>

      <view class="rule-item">
        <view class="rule-header" bindtap="onToggleExpand" data-key="share">
          <text class="rule-name">分享奖励</text>
          <text class="rule-points">+{{rulesData.share_points || 5}}积分</text>
          <text class="expand-icon {{expandedItems.share ? 'expanded' : ''}}">▼</text>
        </view>
        <view wx:if="{{expandedItems.share}}" class="rule-detail">
          分享商品或页面给好友，每日最多可获得{{rulesData.max_share_points || 50}}积分。
        </view>
      </view>
    </view>

    <!-- 积分使用规则 -->
    <view class="rule-section">
      <view class="section-title">
        <text class="title-icon">💰</text>
        <text class="title-text">积分使用规则</text>
      </view>
      
      <view class="rule-item">
        <view class="rule-header" bindtap="onToggleExpand" data-key="exchange">
          <text class="rule-name">积分兑换</text>
          <text class="rule-points">兑换商品</text>
          <text class="expand-icon {{expandedItems.exchange ? 'expanded' : ''}}">▼</text>
        </view>
        <view wx:if="{{expandedItems.exchange}}" class="rule-detail">
          使用积分可以兑换积分商城中的商品，不同商品需要的积分数量不同。兑换成功后积分将被扣除。
        </view>
      </view>

      <view class="rule-item">
        <view class="rule-header" bindtap="onToggleExpand" data-key="deduction">
          <text class="rule-name">积分抵扣</text>
          <text class="rule-points">抵扣现金</text>
          <text class="expand-icon {{expandedItems.deduction ? 'expanded' : ''}}">▼</text>
        </view>
        <view wx:if="{{expandedItems.deduction}}" class="rule-detail">
          在购买普通商品时，可使用积分抵扣部分金额。抵扣比例为{{rulesData.deduction_ratio || '100:1'}}（积分:现金）。
        </view>
      </view>

      <view class="rule-item">
        <view class="rule-header" bindtap="onToggleExpand" data-key="expire">
          <text class="rule-name">积分有效期</text>
          <text class="rule-points">{{rulesData.expire_days || 365}}天</text>
          <text class="expand-icon {{expandedItems.expire ? 'expanded' : ''}}">▼</text>
        </view>
        <view wx:if="{{expandedItems.expire}}" class="rule-detail">
          积分自获得之日起{{rulesData.expire_days || 365}}天内有效，过期后将自动清零。请及时使用您的积分。
        </view>
      </view>
    </view>

    <!-- 特殊说明 -->
    <view class="rule-section">
      <view class="section-title">
        <text class="title-icon">⚠️</text>
        <text class="title-text">特殊说明</text>
      </view>
      
      <view class="notice-list">
        <view class="notice-item">• 积分不可转让，仅限本人使用</view>
        <view class="notice-item">• 恶意刷取积分的行为将被清零处理</view>
        <view class="notice-item">• 退货退款时，相应积分将被扣除</view>
        <view class="notice-item">• 积分规则可能会根据运营需要进行调整</view>
        <view class="notice-item">• 如对积分有疑问，请联系客服处理</view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn-primary" bindtap="goToPointsMall">
        前往积分商城
      </button>
      <button class="btn-secondary" bindtap="goToTransactions">
        查看积分明细
      </button>
      <button class="btn-outline" bindtap="onContactService">
        联系客服
      </button>
    </view>
  </view>
</view> 