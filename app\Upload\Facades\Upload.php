<?php

namespace App\Upload\Facades;

use App\Upload\Services\UploadService;
use Illuminate\Support\Facades\Facade;

/**
 * @method static \App\Upload\Services\UploadService setDriver(\App\Upload\Contracts\StorageDriverInterface $driver)
 * @method static \App\Upload\Contracts\StorageDriverInterface getDriver()
 * @method static array|null uploadImage(\Illuminate\Http\UploadedFile $file, string $type, array $options = [])
 * @method static array|null uploadCategoryImage(\Illuminate\Http\UploadedFile $file, array $options = [])
 * @method static array|null uploadProductImage(\Illuminate\Http\UploadedFile $file, array $options = [])
 * @method static array|null uploadBannerImage(\Illuminate\Http\UploadedFile $file, array $options = [])
 * @method static array|null uploadAvatarImage(\Illuminate\Http\UploadedFile $file, array $options = [])
 * 
 * @see \App\Upload\Services\UploadService
 */
class Upload extends Facade
{
    /**
     * 获取Facade注册名称
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return UploadService::class;
    }
} 