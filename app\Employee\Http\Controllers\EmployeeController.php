<?php

namespace App\Employee\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Employee\Models\Employee;
use App\Employee\Api\ApiResponse;
use App\Employee\Services\EmployeeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class EmployeeController extends Controller
{
    /**
     * @var EmployeeService
     */
    protected $employeeService;
    
    /**
     * 构造函数
     * 
     * @param EmployeeService $employeeService 员工服务
     */
    public function __construct(EmployeeService $employeeService)
    {
        $this->employeeService = $employeeService;
    }
    
    /**
     * 获取员工列表
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $filters = [
                'keyword' => $request->input('keyword'),
                'role' => $request->input('role'),
            ];
            
            $perPage = $request->input('per_page', 15);
            
            $employees = $this->employeeService->getEmployees($filters, $perPage);
            
            return response()->json(ApiResponse::paginate($employees, '获取员工列表成功'));
        } catch (\Exception $e) {
            Log::error('获取员工列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取员工列表失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 创建新员工
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            // 验证输入数据
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'username' => 'required|string|max:255|unique:employees',
                'password' => 'required|string|min:6',
                'phone' => 'nullable|string|max:20',
                'position' => 'required|string|max:100',
                'role' => [
                    'nullable',
                    'string',
                    Rule::in([
                        Employee::ROLE_ADMIN,
                        Employee::ROLE_MANAGER,
                        Employee::ROLE_STAFF,
                        Employee::ROLE_CRM_AGENT,
                        Employee::ROLE_DELIVERY,
                        Employee::ROLE_WAREHOUSE_MANAGER
                    ])
                ],
            ]);
            
            // 创建员工
            $employee = $this->employeeService->createEmployee($validated);
            
            return response()->json(ApiResponse::success($employee, '员工创建成功'), 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(ApiResponse::error('验证失败: ' . $e->getMessage(), 422, $e->errors()), 422);
        } catch (\Exception $e) {
            Log::error('创建员工失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('创建员工失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取指定员工
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $employee = $this->employeeService->getEmployeeById($id);
            
            if (!$employee) {
                return response()->json(ApiResponse::error('员工不存在', 404), 404);
            }
            
            return response()->json(ApiResponse::success($employee, '获取员工详情成功'));
        } catch (\Exception $e) {
            Log::error('获取员工详情失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'employee_id' => $id
            ]);
            
            return response()->json(ApiResponse::error('获取员工详情失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 更新指定员工
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            // 验证输入数据
            $validated = $request->validate([
                'name' => 'nullable|string|max:255',
                'username' => [
                    'nullable',
                    'string',
                    'max:255',
                    Rule::unique('employees')->ignore($id),
                ],
                'password' => 'nullable|string|min:6',
                'phone' => 'nullable|string|max:20',
                'position' => 'nullable|string|max:100',
                'role' => [
                    'nullable',
                    'string',
                    Rule::in([
                        Employee::ROLE_ADMIN,
                        Employee::ROLE_MANAGER,
                        Employee::ROLE_STAFF,
                        Employee::ROLE_CRM_AGENT,
                        Employee::ROLE_DELIVERY,
                        Employee::ROLE_WAREHOUSE_MANAGER
                    ])
                ],
            ]);
            
            // 查找员工
            $employee = $this->employeeService->getEmployeeById($id);
            
            if (!$employee) {
                return response()->json(ApiResponse::error('员工不存在', 404), 404);
            }
            
            // 禁止修改自己的角色权限
            $currentEmployee = $request->user('sanctum');
            if ($currentEmployee && $currentEmployee->id == $id && isset($validated['role']) && $validated['role'] != $currentEmployee->role) {
                return response()->json(ApiResponse::error('不能修改自己的角色权限', 403), 403);
            }
            
            // 更新员工
            $employee = $this->employeeService->updateEmployee($id, $validated);
            
            return response()->json(ApiResponse::success($employee, '员工信息更新成功'));
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(ApiResponse::error('验证失败: ' . $e->getMessage(), 422, $e->errors()), 422);
        } catch (\Exception $e) {
            Log::error('更新员工失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'employee_id' => $id
            ]);
            
            return response()->json(ApiResponse::error('更新员工失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 删除指定员工
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        try {
            // 查找员工
            $employee = $this->employeeService->getEmployeeById($id);
            
            if (!$employee) {
                return response()->json(ApiResponse::error('员工不存在', 404), 404);
            }
            
            // 禁止删除自己
            $currentEmployee = $request->user('sanctum');
            if ($currentEmployee && $currentEmployee->id == $id) {
                return response()->json(ApiResponse::error('不能删除当前登录的账号', 400), 400);
            }
            
            // 删除员工
            $result = $this->employeeService->deleteEmployee($id);
            
            if ($result) {
                return response()->json(ApiResponse::success(null, '员工删除成功'));
            } else {
                return response()->json(ApiResponse::error('员工删除失败', 500), 500);
            }
        } catch (\Exception $e) {
            Log::error('删除员工失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'employee_id' => $id
            ]);
            
            return response()->json(ApiResponse::error('删除员工失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 更新员工状态
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            // 验证输入数据
            $validated = $request->validate([
                'active' => 'required|boolean',
            ]);
            
            // 查找员工
            $employee = $this->employeeService->getEmployeeById($id);
            
            if (!$employee) {
                return response()->json(ApiResponse::error('员工不存在', 404), 404);
            }
            
            // 禁止修改自己的状态
            $currentEmployee = $request->user('sanctum');
            if ($currentEmployee && $currentEmployee->id == $id) {
                return response()->json(ApiResponse::error('不能修改自己的状态', 400), 400);
            }
            
            // 更新员工状态
            $employee = $this->employeeService->updateEmployeeStatus($id, $validated['active']);
            
            return response()->json(ApiResponse::success($employee, '员工状态更新成功'));
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(ApiResponse::error('验证失败: ' . $e->getMessage(), 422, $e->errors()), 422);
        } catch (\Exception $e) {
            Log::error('更新员工状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'employee_id' => $id
            ]);
            
            return response()->json(ApiResponse::error('更新员工状态失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 更新员工角色
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateRole(Request $request, $id)
    {
        try {
            // 验证输入数据
            $validated = $request->validate([
                'role' => [
                    'required',
                    'string',
                    Rule::in([
                        Employee::ROLE_ADMIN,
                        Employee::ROLE_MANAGER,
                        Employee::ROLE_STAFF,
                        Employee::ROLE_CRM_AGENT,
                        Employee::ROLE_DELIVERY,
                        Employee::ROLE_WAREHOUSE_MANAGER
                    ])
                ],
            ]);
            
            // 查找员工
            $employee = $this->employeeService->getEmployeeById($id);
            
            if (!$employee) {
                return response()->json(ApiResponse::error('员工不存在', 404), 404);
            }
            
            // 禁止修改自己的角色权限
            $currentEmployee = $request->user('sanctum');
            if ($currentEmployee && $currentEmployee->id == $id) {
                return response()->json(ApiResponse::error('不能修改自己的角色权限', 400), 400);
            }
            
            // 更新员工角色
            $employee = $this->employeeService->updateEmployeeRole($id, $validated['role']);
            
            return response()->json(ApiResponse::success($employee, '员工角色更新成功'));
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(ApiResponse::error('验证失败: ' . $e->getMessage(), 422, $e->errors()), 422);
        } catch (\Exception $e) {
            Log::error('更新员工角色失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'employee_id' => $id
            ]);
            
            return response()->json(ApiResponse::error('更新员工角色失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取所有可用角色
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRoles()
    {
        try {
            $roles = $this->employeeService->getAvailableRoles();
            
            return response()->json(ApiResponse::success($roles, '获取角色列表成功'));
        } catch (\Exception $e) {
            Log::error('获取角色列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取角色列表失败: ' . $e->getMessage(), 500), 500);
        }
    }
} 