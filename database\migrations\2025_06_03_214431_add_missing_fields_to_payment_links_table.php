<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_links', function (Blueprint $table) {
            // 添加付款链接编号
            $table->string('link_no', 32)->unique()->comment('付款链接编号')->after('id');
            
            // 添加实际支付金额
            $table->decimal('paid_amount', 10, 2)->default(0)->comment('实际支付金额')->after('amount');
            
            // 添加提醒相关字段
            $table->integer('reminder_count')->default(0)->comment('提醒次数')->after('transaction_id');
            $table->timestamp('last_reminder_at')->nullable()->comment('最后提醒时间')->after('reminder_count');
            
            // 添加备注字段
            $table->text('remark')->nullable()->comment('备注')->after('last_reminder_at');
            
            // 添加支付失败原因
            $table->string('failure_reason')->nullable()->comment('支付失败原因')->after('remark');
            
            // 添加幂等性键
            $table->string('idempotency_key', 100)->nullable()->comment('幂等性键')->after('failure_reason');
            
            // 添加索引
            $table->index('link_no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment_links', function (Blueprint $table) {
            $table->dropIndex(['link_no']);
            $table->dropColumn([
                'link_no',
                'paid_amount',
                'reminder_count',
                'last_reminder_at',
                'remark',
                'failure_reason',
                'idempotency_key'
            ]);
        });
    }
};
