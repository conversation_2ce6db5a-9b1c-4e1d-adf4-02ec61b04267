<?php

namespace App\Inventory\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryAnalysisMetric extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'analysis_date',
        'metric_type',
        'total_inventory_value',
        'total_inventory_quantity',
        'total_sku_count',
        'active_sku_count',
        'zero_stock_sku_count',
        'inventory_turnover_ratio',
        'average_inventory_days',
        'fast_moving_ratio',
        'slow_moving_ratio',
        'dead_stock_ratio',
        'stock_accuracy_rate',
        'stockout_count',
        'stockout_rate',
        'overstock_value',
        'carrying_cost',
        'ordering_cost',
        'shortage_cost',
        'total_inventory_cost',
        'category_analysis',
        'warehouse_analysis',
        'supplier_analysis',
        'trend_data',
        'forecast_data',
    ];

    /**
     * 应该被转换的属性
     */
    protected $casts = [
        'analysis_date' => 'date',
        'total_inventory_value' => 'decimal:2',
        'total_inventory_quantity' => 'decimal:2',
        'inventory_turnover_ratio' => 'decimal:4',
        'average_inventory_days' => 'decimal:2',
        'fast_moving_ratio' => 'decimal:4',
        'slow_moving_ratio' => 'decimal:4',
        'dead_stock_ratio' => 'decimal:4',
        'stock_accuracy_rate' => 'decimal:4',
        'stockout_rate' => 'decimal:4',
        'overstock_value' => 'decimal:2',
        'carrying_cost' => 'decimal:2',
        'ordering_cost' => 'decimal:2',
        'shortage_cost' => 'decimal:2',
        'total_inventory_cost' => 'decimal:2',
        'category_analysis' => 'array',
        'warehouse_analysis' => 'array',
        'supplier_analysis' => 'array',
        'trend_data' => 'array',
        'forecast_data' => 'array',
    ];

    /**
     * 获取指标类型文本
     * 
     * @return string
     */
    public function getMetricTypeTextAttribute()
    {
        return match($this->metric_type) {
            'daily' => '日报',
            'weekly' => '周报',
            'monthly' => '月报',
            'quarterly' => '季报',
            'yearly' => '年报',
            default => '未知类型'
        };
    }

    /**
     * 获取库存周转率等级
     * 
     * @return string
     */
    public function getTurnoverRatingAttribute()
    {
        if (!$this->inventory_turnover_ratio) {
            return '无数据';
        }

        $ratio = $this->inventory_turnover_ratio;
        
        if ($ratio >= 12) {
            return '优秀';
        } elseif ($ratio >= 6) {
            return '良好';
        } elseif ($ratio >= 3) {
            return '一般';
        } elseif ($ratio >= 1) {
            return '较差';
        } else {
            return '很差';
        }
    }

    /**
     * 获取库存健康度评分
     * 
     * @return array
     */
    public function getHealthScoreAttribute()
    {
        $scores = [];
        $totalScore = 0;
        $maxScore = 0;

        // 周转率评分 (30分)
        if ($this->inventory_turnover_ratio !== null) {
            $turnoverScore = min(30, $this->inventory_turnover_ratio * 2.5);
            $scores['turnover'] = round($turnoverScore, 1);
            $totalScore += $turnoverScore;
        }
        $maxScore += 30;

        // 准确率评分 (25分)
        if ($this->stock_accuracy_rate !== null) {
            $accuracyScore = $this->stock_accuracy_rate * 25;
            $scores['accuracy'] = round($accuracyScore, 1);
            $totalScore += $accuracyScore;
        }
        $maxScore += 25;

        // 缺货率评分 (20分) - 缺货率越低分数越高
        if ($this->stockout_rate !== null) {
            $stockoutScore = max(0, 20 - ($this->stockout_rate * 100));
            $scores['stockout'] = round($stockoutScore, 1);
            $totalScore += $stockoutScore;
        }
        $maxScore += 20;

        // 呆滞库存评分 (15分) - 呆滞比例越低分数越高
        if ($this->dead_stock_ratio !== null) {
            $deadStockScore = max(0, 15 - ($this->dead_stock_ratio * 75));
            $scores['dead_stock'] = round($deadStockScore, 1);
            $totalScore += $deadStockScore;
        }
        $maxScore += 15;

        // 活跃SKU比例评分 (10分)
        if ($this->total_sku_count > 0) {
            $activeRatio = $this->active_sku_count / $this->total_sku_count;
            $activeScore = $activeRatio * 10;
            $scores['active_ratio'] = round($activeScore, 1);
            $totalScore += $activeScore;
        }
        $maxScore += 10;

        $finalScore = $maxScore > 0 ? ($totalScore / $maxScore) * 100 : 0;

        return [
            'total_score' => round($finalScore, 1),
            'rating' => $this->getScoreRating($finalScore),
            'breakdown' => $scores,
        ];
    }

    /**
     * 根据分数获取等级
     * 
     * @param float $score
     * @return string
     */
    private function getScoreRating($score)
    {
        if ($score >= 90) {
            return '优秀';
        } elseif ($score >= 80) {
            return '良好';
        } elseif ($score >= 70) {
            return '中等';
        } elseif ($score >= 60) {
            return '及格';
        } else {
            return '需改进';
        }
    }

    /**
     * 作用域：按指标类型过滤
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('metric_type', $type);
    }

    /**
     * 作用域：按日期范围过滤
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('analysis_date', [$startDate, $endDate]);
    }

    /**
     * 作用域：最新的指标
     */
    public function scopeLatest($query, $type = null)
    {
        $query = $query->orderBy('analysis_date', 'desc');
        
        if ($type) {
            $query->where('metric_type', $type);
        }
        
        return $query;
    }

    /**
     * 获取趋势数据
     * 
     * @param string $metric 指标名称
     * @param int $periods 期数
     * @return array
     */
    public static function getTrendData($metric, $periods = 12, $type = 'monthly')
    {
        $metrics = static::ofType($type)
            ->orderBy('analysis_date', 'desc')
            ->limit($periods)
            ->get()
            ->reverse();

        return $metrics->map(function($item) use ($metric) {
            return [
                'date' => $item->analysis_date->format('Y-m-d'),
                'value' => $item->{$metric},
            ];
        })->toArray();
    }

    /**
     * 计算同比增长率
     * 
     * @param string $metric 指标名称
     * @param string $type 指标类型
     * @return float|null
     */
    public function getYearOverYearGrowth($metric, $type = 'monthly')
    {
        $lastYearDate = $this->analysis_date->subYear();
        
        $lastYearMetric = static::where('analysis_date', $lastYearDate)
            ->where('metric_type', $type)
            ->first();

        if (!$lastYearMetric || !$lastYearMetric->{$metric} || $lastYearMetric->{$metric} == 0) {
            return null;
        }

        $currentValue = $this->{$metric};
        $lastYearValue = $lastYearMetric->{$metric};

        return (($currentValue - $lastYearValue) / $lastYearValue) * 100;
    }

    /**
     * 计算环比增长率
     * 
     * @param string $metric 指标名称
     * @param string $type 指标类型
     * @return float|null
     */
    public function getMonthOverMonthGrowth($metric, $type = 'monthly')
    {
        $lastPeriodDate = $type === 'monthly' 
            ? $this->analysis_date->subMonth()
            : $this->analysis_date->subWeek();
        
        $lastPeriodMetric = static::where('analysis_date', $lastPeriodDate)
            ->where('metric_type', $type)
            ->first();

        if (!$lastPeriodMetric || !$lastPeriodMetric->{$metric} || $lastPeriodMetric->{$metric} == 0) {
            return null;
        }

        $currentValue = $this->{$metric};
        $lastPeriodValue = $lastPeriodMetric->{$metric};

        return (($currentValue - $lastPeriodValue) / $lastPeriodValue) * 100;
    }
} 