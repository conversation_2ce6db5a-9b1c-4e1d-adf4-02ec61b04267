<?php

namespace App\Region\Services;

use App\Region\Models\Region;
use App\Region\Models\RegionPrice;
use App\Product\Models\Product;
use Illuminate\Support\Collection;

class RegionPriceService
{
    /**
     * 获取商品在指定区域的价格
     *
     * @param int $productId 商品ID
     * @param int $regionId 区域ID
     * @return RegionPrice|null
     */
    public function getProductRegionPrice($productId, $regionId)
    {
        // 先直接查找指定区域的价格
        $price = RegionPrice::where('product_id', $productId)
            ->where('region_id', $regionId)
            ->where('status', true)
            ->first();
            
        if ($price) {
            return $price;
        }
        
        // 如果找不到，向上递归查找父级区域的价格
        $region = Region::find($regionId);
        if (!$region || $region->parent_id == 0) {
            return null;
        }
        
        return $this->getProductRegionPrice($productId, $region->parent_id);
    }
    
    /**
     * 批量设置商品区域价格
     *
     * @param int $productId 商品ID
     * @param array $prices 价格数据 [['region_id' => 1, 'price' => 100, ...], ...]
     * @return Collection
     */
    public function batchSetProductRegionPrices($productId, array $prices)
    {
        $results = collect();
        
        foreach ($prices as $priceData) {
            if (!isset($priceData['region_id']) || !isset($priceData['price'])) {
                continue;
            }
            
            $priceData['product_id'] = $productId;
            
            // 检查是否已存在
            $existingPrice = RegionPrice::where('product_id', $productId)
                ->where('region_id', $priceData['region_id'])
                ->first();
            
            if ($existingPrice) {
                $existingPrice->update($priceData);
                $results->push($existingPrice);
            } else {
                $newPrice = RegionPrice::create($priceData);
                $results->push($newPrice);
            }
        }
        
        return $results;
    }
    
    /**
     * 复制区域价格到其他区域
     *
     * @param int $sourceRegionId 源区域ID
     * @param int $targetRegionId 目标区域ID
     * @param array $filters 过滤条件
     * @return int 复制的价格数量
     */
    public function copyRegionPrices($sourceRegionId, $targetRegionId, array $filters = [])
    {
        $query = RegionPrice::where('region_id', $sourceRegionId);
        
        // 商品筛选
        if (!empty($filters['product_id'])) {
            $query->where('product_id', $filters['product_id']);
        }
        
        // 获取源区域的所有价格
        $sourcePrices = $query->get();
        $count = 0;
        
        foreach ($sourcePrices as $sourcePrice) {
            // 检查目标区域是否已存在价格
            $existingPrice = RegionPrice::where('product_id', $sourcePrice->product_id)
                ->where('region_id', $targetRegionId)
                ->first();
            
            $priceData = $sourcePrice->toArray();
            unset($priceData['id']);
            $priceData['region_id'] = $targetRegionId;
            
            if ($existingPrice) {
                $existingPrice->update($priceData);
            } else {
                RegionPrice::create($priceData);
            }
            
            $count++;
        }
        
        return $count;
    }
    
    /**
     * 批量调整区域价格
     *
     * @param int $regionId 区域ID
     * @param float $adjustmentValue 调整值（固定金额）
     * @param string $adjustmentType 调整类型：只支持'amount'金额
     * @param array $filters 过滤条件
     * @return int 调整的价格数量
     */
    public function adjustRegionPrices($regionId, $adjustmentValue, $adjustmentType = 'amount', array $filters = [])
    {
        // 只支持金额调整
        if ($adjustmentType !== 'amount') {
            throw new \InvalidArgumentException('只支持固定金额调整方式');
        }

        $query = RegionPrice::where('region_id', $regionId);
        
        // 商品筛选
        if (!empty($filters['product_id'])) {
            $query->where('product_id', $filters['product_id']);
        }
        
        // 价格范围筛选
        if (isset($filters['min_price'])) {
            $query->where('price', '>=', $filters['min_price']);
        }
        
        if (isset($filters['max_price'])) {
            $query->where('price', '<=', $filters['max_price']);
        }
        
        $prices = $query->get();
        $count = 0;
        
        foreach ($prices as $price) {
            // 只进行金额调整
            $newPrice = $price->price + $adjustmentValue;
            
            // 确保价格不为负
            $newPrice = max(0, $newPrice);
            
            $price->price = $newPrice;
            $price->save();
            
            $count++;
        }
        
        return $count;
    }
    
    /**
     * 获取商品的所有区域价格
     *
     * @param int $productId 商品ID
     * @return Collection
     */
    public function getProductAllRegionPrices($productId)
    {
        return RegionPrice::where('product_id', $productId)
            ->with('region')
            ->get();
    }
    
    /**
     * 删除商品的区域价格
     *
     * @param int $productId 商品ID
     * @param int|null $regionId 区域ID，为null时删除所有区域价格
     * @return int 删除的价格数量
     */
    public function deleteProductRegionPrices($productId, $regionId = null)
    {
        $query = RegionPrice::where('product_id', $productId);
        
        if ($regionId !== null) {
            $query->where('region_id', $regionId);
        }
        
        return $query->delete();
    }
    
    /**
     * 获取区域内的商品及价格
     *
     * @param int $regionId 区域ID
     * @param array $filters 过滤条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getRegionProducts($regionId, array $filters = [], $page = 1, $limit = 20)
    {
        // 使用Product模型查询
        $query = \App\Product\Models\Product::query()
            ->with(['category', 'mainImage'])
            ->where('status', 1); // 只查询启用的商品
        
        // 应用关键词搜索
        if (!empty($filters['keyword'])) {
            $keyword = $filters['keyword'];
            $query->where(function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('code', 'like', "%{$keyword}%");
            });
        }
        
        // 应用分类过滤
        if (!empty($filters['category'])) {
            $query->where('category_id', $filters['category']);
        }
        
        // 获取商品总数
        $total = $query->count();
        
        // 分页查询商品
        $products = $query->skip(($page - 1) * $limit)
            ->take($limit)
            ->get();
        
        // 获取这些商品在当前区域的价格信息
        $productIds = $products->pluck('id')->toArray();
        $regionPrices = RegionPrice::where('region_id', $regionId)
            ->whereIn('product_id', $productIds)
            ->get()
            ->keyBy('product_id');
        
        // 构建返回数据
        $data = $products->map(function ($product) use ($regionPrices, $regionId) {
            $regionPrice = $regionPrices->get($product->id);
            $hasDirectPrice = $regionPrice !== null;
            
            // 计算有效价格（直接价格或继承价格）
            $effectivePrice = $hasDirectPrice ? $regionPrice->price : $product->price;
            
            // 计算优惠信息
            $discount = 0;
            $discountRate = 0;
            if ($hasDirectPrice && $regionPrice->original_price) {
                $discount = $regionPrice->original_price - $regionPrice->price;
                $discountRate = ($discount / $regionPrice->original_price) * 100;
            }
            
            return [
                'id' => $regionPrice ? $regionPrice->id : 0,
                'product' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->code,
                    'base_price' => $product->price,
                    'category' => $product->category ? $product->category->name : '',
                    'cover' => $product->mainImage ? $product->mainImage->url : null,
                ],
                'price' => $hasDirectPrice ? $regionPrice->price : null,
                'original_price' => $hasDirectPrice ? $regionPrice->original_price : null,
                'stock' => $hasDirectPrice ? $regionPrice->stock : null,
                'status' => $hasDirectPrice ? $regionPrice->status : true,
                'has_direct_price' => $hasDirectPrice,
                'effective_price' => $effectivePrice,
                'start_date' => $hasDirectPrice ? $regionPrice->start_date : null,
                'end_date' => $hasDirectPrice ? $regionPrice->end_date : null,
                'discount' => $discount,
                'discount_rate' => $discountRate,
            ];
        });
        
        // 应用价格过滤
        if (!empty($filters['min_price']) || !empty($filters['max_price'])) {
            $data = $data->filter(function ($item) use ($filters) {
                $price = $item['effective_price'];
                if (!empty($filters['min_price']) && $price < $filters['min_price']) {
                    return false;
                }
                if (!empty($filters['max_price']) && $price > $filters['max_price']) {
                    return false;
                }
                return true;
            });
        }
        
        // 应用价格类型过滤
        if (!empty($filters['price_type'])) {
            if ($filters['price_type'] === 'direct') {
                $data = $data->filter(function ($item) {
                    return $item['has_direct_price'];
                });
            } elseif ($filters['price_type'] === 'inherited') {
                $data = $data->filter(function ($item) {
                    return !$item['has_direct_price'];
                });
            }
        }
        
        // 应用状态过滤
        if (isset($filters['status'])) {
            $data = $data->filter(function ($item) use ($filters) {
                return $item['status'] == $filters['status'];
            });
        }
        
        // 重新计算统计数据
        $filteredTotal = $data->count();
        $priceSetCount = $data->where('has_direct_price', true)->count();
        $averagePrice = $data->avg('effective_price');
        $totalValue = $data->sum('effective_price');
        
        return [
            'data' => $data->values()->toArray(),
            'total' => $filteredTotal,
            'current_page' => $page,
            'per_page' => $limit,
            'last_page' => ceil($filteredTotal / $limit),
            'stats' => [
                'total_products' => $filteredTotal,
                'price_set_count' => $priceSetCount,
                'average_price' => round($averagePrice, 2),
                'total_value' => round($totalValue, 2),
            ]
        ];
    }
    
    /**
     * 设置商品在区域的价格
     *
     * @param int $productId 商品ID
     * @param array $data 价格数据
     * @return RegionPrice
     */
    public function setProductRegionPrice($productId, array $data)
    {
        $data['product_id'] = $productId;
        
        // 检查是否已存在
        $existingPrice = RegionPrice::where('product_id', $productId)
            ->where('region_id', $data['region_id'])
            ->first();
        
        if ($existingPrice) {
            $existingPrice->update($data);
            return $existingPrice;
        } else {
            return RegionPrice::create($data);
        }
    }
    
    /**
     * 批量调整区域价格预览
     *
     * @param int $regionId 区域ID
     * @param float $adjustmentValue 调整值（固定金额）
     * @param string $adjustmentType 调整类型：只支持'amount'金额
     * @param array $productIds 商品ID数组
     * @return array
     */
    public function previewAdjustPrices($regionId, $adjustmentValue, $adjustmentType, array $productIds = [])
    {
        // 只支持金额调整
        if ($adjustmentType !== 'amount') {
            throw new \InvalidArgumentException('只支持固定金额调整方式');
        }

        $query = RegionPrice::where('region_id', $regionId);
        
        if (!empty($productIds)) {
            $query->whereIn('product_id', $productIds);
        }
        
        $prices = $query->get();
        $preview = [];
        
        foreach ($prices as $price) {
            $oldPrice = $price->price;
            // 只进行金额调整
            $newPrice = $oldPrice + $adjustmentValue;
            $newPrice = max(0, $newPrice);
            
            $preview[] = [
                'product_id' => $price->product_id,
                'old_price' => $oldPrice,
                'new_price' => $newPrice,
                'difference' => $newPrice - $oldPrice
            ];
        }
        
        return $preview;
    }
    
    /**
     * 批量调整区域价格
     *
     * @param int $regionId 区域ID
     * @param float $adjustmentValue 调整值（固定金额）
     * @param string $adjustmentType 调整类型：只支持'amount'金额
     * @param array $productIds 商品ID数组
     * @return int
     */
    public function adjustPrices($regionId, $adjustmentValue, $adjustmentType, array $productIds = [])
    {
        // 只支持金额调整
        if ($adjustmentType !== 'amount') {
            throw new \InvalidArgumentException('只支持固定金额调整方式');
        }

        $query = RegionPrice::where('region_id', $regionId);
        
        if (!empty($productIds)) {
            $query->whereIn('product_id', $productIds);
        }
        
        $prices = $query->get();
        $count = 0;
        
        foreach ($prices as $price) {
            // 只进行金额调整
            $newPrice = $price->price + $adjustmentValue;
            $newPrice = max(0, $newPrice);
            
            $price->price = $newPrice;
            $price->save();
            
            $count++;
        }
        
        return $count;
    }
    
    /**
     * 从其他区域复制价格
     *
     * @param int $targetRegionId 目标区域ID
     * @param int $sourceRegionId 源区域ID
     * @param array $productIds 商品ID数组
     * @param bool $overwrite 是否覆盖已存在的价格
     * @return int
     */
    public function copyPricesFromRegion($targetRegionId, $sourceRegionId, array $productIds = [], $overwrite = false)
    {
        $query = RegionPrice::where('region_id', $sourceRegionId);
        
        if (!empty($productIds)) {
            $query->whereIn('product_id', $productIds);
        }
        
        $sourcePrices = $query->get();
        $count = 0;
        
        foreach ($sourcePrices as $sourcePrice) {
            $existingPrice = RegionPrice::where('product_id', $sourcePrice->product_id)
                ->where('region_id', $targetRegionId)
                ->first();
            
            if ($existingPrice && !$overwrite) {
                continue;
            }
            
            $priceData = $sourcePrice->toArray();
            unset($priceData['id']);
            $priceData['region_id'] = $targetRegionId;
            
            if ($existingPrice) {
                $existingPrice->update($priceData);
            } else {
                RegionPrice::create($priceData);
            }
            
            $count++;
        }
        
        return $count;
    }

    /**
     * 批量添加商品到区域
     */
    public function batchAddProducts($regionId, $productIds, $options = [])
    {
        $results = [];
        $defaultPrice = $options['default_price'] ?? null;
        $defaultStock = $options['default_stock'] ?? null;
        $copyFromRegionId = $options['copy_from_region_id'] ?? null;
        $inheritParentPrice = $options['inherit_parent_price'] ?? false;

        foreach ($productIds as $productId) {
            try {
                // 检查商品是否已存在于该区域
                $existing = RegionPrice::where('region_id', $regionId)
                    ->where('product_id', $productId)
                    ->first();

                if ($existing) {
                    $results[] = [
                        'product_id' => $productId,
                        'status' => 'skipped',
                        'message' => '商品已存在于该区域'
                    ];
                    continue;
                }

                $priceData = [
                    'region_id' => $regionId,
                    'product_id' => $productId,
                    'status' => true
                ];

                // 设置价格
                if ($defaultPrice !== null) {
                    $priceData['price'] = $defaultPrice;
                } elseif ($copyFromRegionId) {
                    // 从其他区域复制价格
                    $sourcePrice = RegionPrice::where('region_id', $copyFromRegionId)
                        ->where('product_id', $productId)
                        ->first();
                    if ($sourcePrice) {
                        $priceData['price'] = $sourcePrice->price;
                        $priceData['stock'] = $sourcePrice->stock;
                        $priceData['start_date'] = $sourcePrice->start_date;
                        $priceData['end_date'] = $sourcePrice->end_date;
                    }
                } elseif ($inheritParentPrice) {
                    // 继承父区域价格
                    $parentPrice = $this->getInheritedPrice($regionId, $productId);
                    if ($parentPrice) {
                        $priceData['price'] = $parentPrice;
                    }
                }

                // 设置库存
                if ($defaultStock !== null) {
                    $priceData['stock'] = $defaultStock;
                }

                RegionPrice::create($priceData);

                $results[] = [
                    'product_id' => $productId,
                    'status' => 'success',
                    'message' => '添加成功'
                ];

            } catch (\Exception $e) {
                $results[] = [
                    'product_id' => $productId,
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 批量设置商品价格
     */
    public function batchSetPrices($regionId, $products)
    {
        $results = [];

        foreach ($products as $productData) {
            try {
                $productId = $productData['product_id'];
                
                $priceData = [
                    'region_id' => $regionId,
                    'product_id' => $productId,
                    'price' => $productData['price'],
                    'stock' => $productData['stock'] ?? null,
                    'status' => $productData['status'] ?? true,
                    'start_date' => $productData['start_date'] ?? null,
                    'end_date' => $productData['end_date'] ?? null
                ];

                RegionPrice::updateOrCreate(
                    ['region_id' => $regionId, 'product_id' => $productId],
                    $priceData
                );

                $results[] = [
                    'product_id' => $productId,
                    'status' => 'success',
                    'message' => '设置成功'
                ];

            } catch (\Exception $e) {
                $results[] = [
                    'product_id' => $productData['product_id'],
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 批量修改商品库存
     */
    public function batchUpdateStock($regionId, $products, $operation = 'set')
    {
        $results = [];

        foreach ($products as $productData) {
            try {
                $productId = $productData['product_id'];
                $stockValue = $productData['stock'];

                $regionPrice = RegionPrice::where('region_id', $regionId)
                    ->where('product_id', $productId)
                    ->first();

                if (!$regionPrice) {
                    $results[] = [
                        'product_id' => $productId,
                        'status' => 'error',
                        'message' => '商品在该区域不存在价格设置'
                    ];
                    continue;
                }

                switch ($operation) {
                    case 'set':
                        $regionPrice->stock = $stockValue;
                        break;
                    case 'add':
                        $regionPrice->stock = ($regionPrice->stock ?? 0) + $stockValue;
                        break;
                    case 'subtract':
                        $regionPrice->stock = max(0, ($regionPrice->stock ?? 0) - $stockValue);
                        break;
                }

                $regionPrice->save();

                $results[] = [
                    'product_id' => $productId,
                    'status' => 'success',
                    'message' => '库存修改成功',
                    'new_stock' => $regionPrice->stock
                ];

            } catch (\Exception $e) {
                $results[] = [
                    'product_id' => $productData['product_id'],
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 批量设置商品状态
     */
    public function batchUpdateStatus($regionId, $productIds, $status)
    {
        $results = [];

        foreach ($productIds as $productId) {
            try {
                $regionPrice = RegionPrice::where('region_id', $regionId)
                    ->where('product_id', $productId)
                    ->first();

                if (!$regionPrice) {
                    $results[] = [
                        'product_id' => $productId,
                        'status' => 'error',
                        'message' => '商品在该区域不存在价格设置'
                    ];
                    continue;
                }

                $regionPrice->status = $status;
                $regionPrice->save();

                $results[] = [
                    'product_id' => $productId,
                    'status' => 'success',
                    'message' => '状态修改成功'
                ];

            } catch (\Exception $e) {
                $results[] = [
                    'product_id' => $productId,
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 批量设置有效期
     */
    public function batchSetValidityPeriod($regionId, $productIds, $startDate = null, $endDate = null)
    {
        $results = [];

        foreach ($productIds as $productId) {
            try {
                $regionPrice = RegionPrice::where('region_id', $regionId)
                    ->where('product_id', $productId)
                    ->first();

                if (!$regionPrice) {
                    $results[] = [
                        'product_id' => $productId,
                        'status' => 'error',
                        'message' => '商品在该区域不存在价格设置'
                    ];
                    continue;
                }

                $regionPrice->start_date = $startDate;
                $regionPrice->end_date = $endDate;
                $regionPrice->save();

                $results[] = [
                    'product_id' => $productId,
                    'status' => 'success',
                    'message' => '有效期设置成功'
                ];

            } catch (\Exception $e) {
                $results[] = [
                    'product_id' => $productId,
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 获取可添加的商品列表
     */
    public function getAvailableProducts($regionId, $filters = [])
    {
        $page = $filters['page'] ?? 1;
        $limit = $filters['limit'] ?? 20;
        
        // 模拟商品数据 - 实际应该从商品表查询
        $allProducts = [
            ['id' => 1, 'name' => '苹果iPhone 15', 'price' => 5999.00, 'category' => '手机', 'sku' => 'IP15-001'],
            ['id' => 2, 'name' => '华为Mate 60', 'price' => 6999.00, 'category' => '手机', 'sku' => 'HW-M60-001'],
            ['id' => 3, 'name' => '小米14', 'price' => 3999.00, 'category' => '手机', 'sku' => 'MI14-001'],
            ['id' => 4, 'name' => 'MacBook Pro', 'price' => 12999.00, 'category' => '电脑', 'sku' => 'MBP-001'],
            ['id' => 5, 'name' => 'iPad Air', 'price' => 4599.00, 'category' => '平板', 'sku' => 'IPAD-AIR-001'],
        ];

        // 过滤已在该区域设置价格的商品
        $existingProductIds = RegionPrice::where('region_id', $regionId)
            ->pluck('product_id')
            ->toArray();

        $availableProducts = array_filter($allProducts, function($product) use ($existingProductIds) {
            return !in_array($product['id'], $existingProductIds);
        });

        // 应用搜索过滤
        if (!empty($filters['keyword'])) {
            $keyword = strtolower($filters['keyword']);
            $availableProducts = array_filter($availableProducts, function($product) use ($keyword) {
                return strpos(strtolower($product['name']), $keyword) !== false ||
                       strpos(strtolower($product['sku']), $keyword) !== false;
            });
        }

        // 应用价格过滤
        if (!empty($filters['price_min'])) {
            $availableProducts = array_filter($availableProducts, function($product) use ($filters) {
                return $product['price'] >= $filters['price_min'];
            });
        }

        if (!empty($filters['price_max'])) {
            $availableProducts = array_filter($availableProducts, function($product) use ($filters) {
                return $product['price'] <= $filters['price_max'];
            });
        }

        // 分页
        $total = count($availableProducts);
        $offset = ($page - 1) * $limit;
        $products = array_slice($availableProducts, $offset, $limit);

        return [
            'data' => array_values($products),
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }

    /**
     * 获取继承的价格
     */
    private function getInheritedPrice($regionId, $productId)
    {
        $region = Region::find($regionId);
        if (!$region || !$region->parent_id) {
            return null;
        }

        $parentPrice = RegionPrice::where('region_id', $region->parent_id)
            ->where('product_id', $productId)
            ->first();

        if ($parentPrice) {
            return $parentPrice->price;
        }

        // 递归查找更上级的价格
        return $this->getInheritedPrice($region->parent_id, $productId);
    }

    /**
     * 获取区域价格统计信息
     *
     * @param int $regionId 区域ID
     * @return array
     */
    public function getRegionPriceStats($regionId)
    {
        // 获取该区域的具体商品价格数量
        $specificPricesCount = RegionPrice::where('region_id', $regionId)->count();
        
        // 获取该区域的分类价格规则数量
        $categoryPricesCount = \App\Product\Models\CategoryRegionPrice::where('region_id', $regionId)->count();
        
        // 获取所有商品总数
        $totalProductsCount = Product::count();
        
        // 计算使用基础价格的商品数量（没有特殊价格设置的商品）
        $baseProductsCount = $totalProductsCount - $specificPricesCount;
        
        // 计算价格覆盖率
        $coverageRate = $totalProductsCount > 0 ? round(($specificPricesCount / $totalProductsCount) * 100, 2) : 0;
        
        // 获取区域信息
        $region = Region::find($regionId);
        
        return [
            'region' => [
                'id' => $regionId,
                'name' => $region ? $region->name : '未知区域',
                'level' => $region ? $region->level : 0
            ],
            'statistics' => [
                'specific_prices' => [
                    'count' => $specificPricesCount,
                    'label' => '具体区域价格',
                    'description' => '设置了具体区域价格的商品数量'
                ],
                'category_rules' => [
                    'count' => $categoryPricesCount,
                    'label' => '分类价格规则',
                    'description' => '设置了分类价格规则的分类数量'
                ],
                'base_prices' => [
                    'count' => $baseProductsCount,
                    'label' => '使用基础价格',
                    'description' => '没有特殊价格设置的商品数量'
                ],
                'coverage_rate' => [
                    'value' => $coverageRate,
                    'label' => '价格覆盖率',
                    'description' => '有特殊价格设置的商品占比',
                    'unit' => '%'
                ]
            ],
            'total_products' => $totalProductsCount,
            'updated_at' => now()->toISOString()
        ];
    }
} 