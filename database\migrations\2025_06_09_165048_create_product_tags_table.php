<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 创建商品标签表
        Schema::create('product_tags', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('标签名称');
            $table->string('slug')->unique()->comment('标签别名');
            $table->string('color', 7)->default('#ffffff')->comment('文字颜色');
            $table->string('background_color', 7)->default('#666666')->comment('背景颜色');
            $table->string('icon')->nullable()->comment('图标');
            $table->text('description')->nullable()->comment('标签描述');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->boolean('show_in_filter')->default(true)->comment('是否在筛选中显示');
            $table->timestamps();
            
            // 索引
            $table->index('is_active');
            $table->index('show_in_filter');
            $table->index('sort_order');
        });

        // 创建商品标签关联表
        Schema::create('product_tag_relations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade')->comment('商品ID');
            $table->foreignId('tag_id')->constrained('product_tags')->onDelete('cascade')->comment('标签ID');
            $table->timestamp('created_at')->nullable();
            
            // 联合唯一索引，防止重复关联
            $table->unique(['product_id', 'tag_id']);
            
            // 索引
            $table->index('product_id');
            $table->index('tag_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_tag_relations');
        Schema::dropIfExists('product_tags');
    }
};
