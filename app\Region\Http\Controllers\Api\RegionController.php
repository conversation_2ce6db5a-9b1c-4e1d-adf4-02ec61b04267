<?php

namespace App\Region\Http\Controllers\Api;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Region\Models\Region;
use App\Region\Services\RegionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class RegionController extends Controller
{
    /**
     * 区域服务
     *
     * @var RegionService
     */
    protected $regionService;
    
    /**
     * 构造函数
     *
     * @param RegionService $regionService
     */
    public function __construct(RegionService $regionService)
    {
        $this->regionService = $regionService;
    }
    
    /**
     * 获取区域列表（分页）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $filters = [
                'keyword' => $request->input('keyword'),
                'status' => $request->input('status'),
                'parent_id' => $request->input('parent_id'),
                'level' => $request->input('level'),
            ];
            
            $regions = $this->regionService->getRegions($filters, $request->input('limit', $request->input('per_page', 10)));
            
            return response()->json(ApiResponse::success($regions));
        } catch (\Exception $e) {
            Log::error('获取区域列表失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取区域列表失败', 500), 500);
        }
    }
    
    /**
     * 获取区域树结构
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function tree(Request $request)
    {
        try {
            $parentId = $request->input('parent_id', 0);
            $conditions = [];
            
            if ($request->has('status')) {
                $statusValue = $request->input('status');
                // 处理布尔值转换
                if (is_bool($statusValue)) {
                    $statusValue = $statusValue ? 1 : 0;
                } elseif (is_string($statusValue)) {
                    $statusValue = ($statusValue === 'true' || $statusValue === '1') ? 1 : 0;
                }
                $conditions['status'] = $statusValue;
            }
            
            Log::info('获取区域树请求', [
                'parent_id' => $parentId,
                'conditions' => $conditions,
                'raw_status' => $request->input('status')
            ]);
            
            $tree = $this->regionService->getTree($parentId, $conditions);
            
            Log::info('区域树查询结果', [
                'count' => $tree->count(),
                'first_few' => $tree->take(3)->toArray()
            ]);
            
            return response()->json(ApiResponse::success($tree));
        } catch (\Exception $e) {
            Log::error('获取区域树失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取区域树失败', 500), 500);
        }
    }
    
    /**
     * 获取所有区域数据（不分页）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function all(Request $request)
    {
        try {
            $filters = [
                'keyword' => $request->input('keyword'),
                'status' => $request->input('status'),
                'parent_id' => $request->input('parent_id'),
                'level' => $request->input('level'),
            ];
            
            // 添加调试日志
            Log::info('获取所有区域请求', [
                'filters' => $filters,
                'raw_status' => $request->input('status'),
                'status_type' => gettype($request->input('status'))
            ]);
            
            // 获取所有区域数据
            $regions = Region::query();
            
            // 应用过滤条件
            if (!empty($filters['keyword'])) {
                $regions->where(function ($q) use ($filters) {
                    $q->where('name', 'like', '%' . $filters['keyword'] . '%')
                      ->orWhere('code', 'like', '%' . $filters['keyword'] . '%');
                });
            }
            
            // 修复status过滤逻辑，处理布尔值和字符串
            if (isset($filters['status']) && $filters['status'] !== '' && $filters['status'] !== null) {
                // 将布尔值转换为整数
                $statusValue = $filters['status'];
                if (is_bool($statusValue)) {
                    $statusValue = $statusValue ? 1 : 0;
                } elseif (is_string($statusValue)) {
                    $statusValue = ($statusValue === 'true' || $statusValue === '1') ? 1 : 0;
                }
                
                Log::info('应用status过滤', [
                    'original' => $filters['status'],
                    'converted' => $statusValue
                ]);
                
                $regions->where('status', $statusValue);
            }
            
            if (isset($filters['parent_id']) && $filters['parent_id'] !== '') {
                $regions->where('parent_id', $filters['parent_id']);
            }
            
            if (isset($filters['level']) && $filters['level'] !== '') {
                $regions->where('level', $filters['level']);
            }
            
            // 排序并获取所有数据
            $allRegions = $regions->orderBy('sort', 'asc')->orderBy('id', 'asc')->get();
            
            Log::info('区域查询结果', [
                'total_count' => $allRegions->count(),
                'first_few' => $allRegions->take(3)->toArray()
            ]);
            
            return response()->json(ApiResponse::success($allRegions));
        } catch (\Exception $e) {
            Log::error('获取所有区域失败', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json(ApiResponse::error('获取所有区域失败', 500), 500);
        }
    }
    
    /**
     * 获取区域详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $region = Region::findOrFail($id);
            return response()->json(ApiResponse::success($region));
        } catch (\Exception $e) {
            Log::error('获取区域详情失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取区域详情失败', 404), 404);
        }
    }
    
    /**
     * 获取区域祖先
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function ancestors($id)
    {
        try {
            $ancestors = $this->regionService->getAncestors($id);
            return response()->json(ApiResponse::success($ancestors));
        } catch (\Exception $e) {
            Log::error('获取区域祖先失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取区域祖先失败', 500), 500);
        }
    }
    
    /**
     * 获取区域后代
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function descendants(Request $request, $id)
    {
        try {
            $activeOnly = $request->input('active_only', true);
            $descendants = $this->regionService->getDescendants($id, $activeOnly);
            return response()->json(ApiResponse::success($descendants));
        } catch (\Exception $e) {
            Log::error('获取区域后代失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取区域后代失败', 500), 500);
        }
    }
    
    /**
     * 获取区域面包屑
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function breadcrumb($id)
    {
        try {
            $breadcrumb = $this->regionService->getBreadcrumb($id);
            return response()->json(ApiResponse::success($breadcrumb));
        } catch (\Exception $e) {
            Log::error('获取区域面包屑失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取区域面包屑失败', 500), 500);
        }
    }
    
    /**
     * 获取子区域
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function children($id)
    {
        try {
            $region = Region::findOrFail($id);
            $children = $region->children()->orderBy('sort', 'asc')->get();
            return response()->json(ApiResponse::success($children));
        } catch (\Exception $e) {
            Log::error('获取子区域失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取子区域失败', 500), 500);
        }
    }
    
    /**
     * 创建区域
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            // 验证请求数据
            $validated = $request->validate([
                'name' => 'required|string|max:50',
                'code' => 'required|string|max:30|regex:/^[A-Za-z0-9_-]+$/|unique:regions,code',
                'parent_id' => 'nullable|integer|exists:regions,id',
                'sort' => 'nullable|integer|min:0|max:9999',
                'status' => 'nullable|boolean',
                'description' => 'nullable|string|max:255'
            ]);
            
            // 设置默认值
            $data = array_merge([
                'sort' => 0,
                'status' => true,
            ], $validated);
            
            // 创建区域
            $region = $this->regionService->createRegion($data);
            
            return response()->json(ApiResponse::success($region, '创建区域成功', 201), 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // 将验证错误数组转换为字符串
            $errorMessages = [];
            foreach ($e->errors() as $field => $messages) {
                $errorMessages[] = $field . ': ' . implode(', ', $messages);
            }
            $errorMessage = '验证失败: ' . implode('; ', $errorMessages);
            return response()->json(ApiResponse::error($errorMessage, 422), 422);
        } catch (\Exception $e) {
            Log::error('创建区域失败', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json(ApiResponse::error('创建区域失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 更新区域
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            // 验证请求数据
            $validated = $request->validate([
                'name' => 'nullable|string|max:50',
                'code' => "nullable|string|max:30|regex:/^[A-Za-z0-9_-]+$/|unique:regions,code,{$id}",
                'parent_id' => 'nullable|integer|exists:regions,id',
                'sort' => 'nullable|integer|min:0|max:9999',
                'status' => 'nullable|boolean',
                'description' => 'nullable|string|max:255'
            ]);
            
            // 过滤空值
            $data = array_filter($validated, function ($value) {
                return $value !== null;
            });
            
            // 更新区域
            $region = $this->regionService->updateRegion($id, $data);
            
            return response()->json(ApiResponse::success($region, '更新区域成功'));
        } catch (\Illuminate\Validation\ValidationException $e) {
            // 将验证错误数组转换为字符串
            $errorMessages = [];
            foreach ($e->errors() as $field => $messages) {
                $errorMessages[] = $field . ': ' . implode(', ', $messages);
            }
            $errorMessage = '验证失败: ' . implode('; ', $errorMessages);
            return response()->json(ApiResponse::error($errorMessage, 422), 422);
        } catch (\InvalidArgumentException $e) {
            return response()->json(ApiResponse::error($e->getMessage(), 400), 400);
        } catch (\Exception $e) {
            Log::error('更新区域失败', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json(ApiResponse::error('更新区域失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 删除区域
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $this->regionService->deleteRegion($id);
            return response()->json(ApiResponse::success(null, '删除区域成功'));
        } catch (\InvalidArgumentException $e) {
            return response()->json(ApiResponse::error($e->getMessage(), 400), 400);
        } catch (\Exception $e) {
            Log::error('删除区域失败', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json(ApiResponse::error('删除区域失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 查找最近的区域
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function nearest(Request $request)
    {
        try {
            // 验证请求数据
            $validated = $request->validate([
                'latitude' => 'required|numeric',
                'longitude' => 'required|numeric',
                'level' => 'nullable|integer|min:0|max:5',
                'limit' => 'nullable|integer|min:1|max:50',
                'only_active' => 'nullable|boolean'
            ]);
            
            $latitude = $validated['latitude'];
            $longitude = $validated['longitude'];
            $level = $validated['level'] ?? null;
            $limit = $validated['limit'] ?? 5;
            $onlyActive = $validated['only_active'] ?? true;
            
            $regions = $this->regionService->findNearestRegions(
                $latitude, 
                $longitude, 
                $level, 
                $onlyActive, 
                $limit
            );
            
            return response()->json(ApiResponse::success($regions));
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(ApiResponse::error('验证失败', 422, $e->errors()), 422);
        } catch (\Exception $e) {
            Log::error('查找最近区域失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('查找最近区域失败', 500), 500);
        }
    }
    
    /**
     * 查找范围内的区域
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function withinRadius(Request $request)
    {
        try {
            // 验证请求数据
            $validated = $request->validate([
                'latitude' => 'required|numeric',
                'longitude' => 'required|numeric',
                'radius' => 'required|numeric|min:0.1|max:500',
                'level' => 'nullable|integer|min:0|max:5',
                'only_active' => 'nullable|boolean'
            ]);
            
            $latitude = $validated['latitude'];
            $longitude = $validated['longitude'];
            $radius = $validated['radius'];
            $level = $validated['level'] ?? null;
            $onlyActive = $validated['only_active'] ?? true;
            
            $regions = $this->regionService->findRegionsWithinRadius(
                $latitude, 
                $longitude, 
                $radius, 
                $level, 
                $onlyActive
            );
            
            return response()->json(ApiResponse::success($regions));
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(ApiResponse::error('验证失败', 422, $e->errors()), 422);
        } catch (\Exception $e) {
            Log::error('查找范围内区域失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('查找范围内区域失败', 500), 500);
        }
    }
    
    /**
     * 检查点是否在区域内
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkPoint(Request $request, $id)
    {
        try {
            // 验证请求数据
            $validated = $request->validate([
                'latitude' => 'required|numeric',
                'longitude' => 'required|numeric'
            ]);
            
            $latitude = $validated['latitude'];
            $longitude = $validated['longitude'];
            
            $isInRegion = $this->regionService->isPointInRegion($latitude, $longitude, $id);
            
            return response()->json(ApiResponse::success([
                'is_in_region' => $isInRegion
            ]));
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(ApiResponse::error('验证失败', 422, $e->errors()), 422);
        } catch (\Exception $e) {
            Log::error('检查点是否在区域内失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('检查点是否在区域内失败', 500), 500);
        }
    }
} 