<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('points_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->integer('points')->comment('积分变动（正负数）');
            $table->unsignedInteger('balance_before')->default(0)->comment('变动前余额');
            $table->unsignedInteger('balance_after')->default(0)->comment('变动后余额');
            $table->enum('type', ['earn', 'spend', 'refund', 'expire', 'admin'])->comment('变动类型');
            $table->string('source', 50)->comment('积分来源');
            $table->unsignedBigInteger('source_id')->nullable()->comment('来源ID');
            $table->string('description')->nullable()->comment('描述');
            $table->timestamp('expired_at')->nullable()->comment('过期时间');
            $table->timestamp('processed_at')->nullable()->comment('处理时间（用于过期处理）');
            $table->timestamps();

            $table->index(['user_id', 'created_at']);
            $table->index(['type', 'source']);
            $table->index(['expired_at', 'processed_at']);
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('points_transactions');
    }
}; 