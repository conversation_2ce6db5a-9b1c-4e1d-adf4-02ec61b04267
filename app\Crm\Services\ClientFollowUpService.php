<?php

namespace App\Crm\Services;

use App\Crm\Models\ClientFollowUp;
use App\Models\User;
use App\Employee\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ClientFollowUpService
{
    /**
     * 获取跟进记录列表
     *
     * @param Request $request
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getFollowUps(Request $request)
    {
        $query = ClientFollowUp::with(['client', 'agent']);
        
        // 筛选条件
        if ($request->has('user_id') && $request->user_id) {
            $query->where('user_id', $request->user_id);
        }
        
        if ($request->has('employee_id') && $request->employee_id) {
            $query->where('employee_id', $request->employee_id);
        }
        
        if ($request->has('result') && $request->result) {
            $query->where('result', $request->result);
        }
        
        if ($request->has('contact_method') && $request->contact_method) {
            $query->where('contact_method', $request->contact_method);
        }
        
        if ($request->has('from_date') && $request->from_date) {
            $query->whereDate('follow_up_date', '>=', $request->from_date);
        }
        
        if ($request->has('to_date') && $request->to_date) {
            $query->whereDate('follow_up_date', '<=', $request->to_date);
        }
        
        // 排序
        if ($request->has('sort') && $request->sort) {
            $direction = $request->has('direction') && $request->direction === 'desc' ? 'desc' : 'asc';
            $query->orderBy($request->sort, $direction);
        } else {
            $query->latest('follow_up_date');
        }
        
        return $query->paginate($request->input('per_page', 15));
    }
    
    /**
     * 获取跟进记录详情
     *
     * @param int $id
     * @return ClientFollowUp
     */
    public function getFollowUp($id)
    {
        return ClientFollowUp::with(['client', 'agent'])->findOrFail($id);
    }
    
    /**
     * 创建跟进记录
     *
     * @param array $data
     * @return ClientFollowUp
     */
    public function createFollowUp(array $data)
    {
        // 验证用户和员工存在
        $user = User::findOrFail($data['user_id']);
        $employee = Employee::findOrFail($data['employee_id']);
        
        // 创建跟进记录
        $followUp = new ClientFollowUp();
        $followUp->user_id = $user->id;
        $followUp->employee_id = $employee->id;
        $followUp->follow_up_date = $data['follow_up_date'] ?? now();
        $followUp->contact_method = $data['contact_method'] ?? 'phone';
        $followUp->notes = $data['notes'] ?? null;
        $followUp->result = $data['result'] ?? 'follow_up';
        $followUp->next_follow_up = $data['next_follow_up'] ?? null;
        $followUp->save();
        
        return $followUp->load(['client', 'agent']);
    }
    
    /**
     * 更新跟进记录
     *
     * @param int $id
     * @param array $data
     * @return ClientFollowUp
     */
    public function updateFollowUp($id, array $data)
    {
        $followUp = ClientFollowUp::findOrFail($id);
        
        if (isset($data['follow_up_date'])) {
            $followUp->follow_up_date = $data['follow_up_date'];
        }
        
        if (isset($data['contact_method'])) {
            $followUp->contact_method = $data['contact_method'];
        }
        
        if (isset($data['notes'])) {
            $followUp->notes = $data['notes'];
        }
        
        if (isset($data['result'])) {
            $followUp->result = $data['result'];
        }
        
        if (isset($data['next_follow_up'])) {
            $followUp->next_follow_up = $data['next_follow_up'];
        }
        
        $followUp->save();
        
        return $followUp->load(['client', 'agent']);
    }
    
    /**
     * 删除跟进记录
     *
     * @param int $id
     * @return bool
     */
    public function deleteFollowUp($id)
    {
        $followUp = ClientFollowUp::findOrFail($id);
        return $followUp->delete();
    }
    
    /**
     * 创建后续跟进记录
     *
     * @param int $id
     * @param array $data
     * @return ClientFollowUp
     */
    public function createNextFollowUp($id, array $data)
    {
        $followUp = ClientFollowUp::findOrFail($id);
        return $followUp->createFollowUp($data);
    }
    
    /**
     * 获取用户的跟进记录
     *
     * @param int $userId
     * @param Request $request
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getUserFollowUps($userId, Request $request)
    {
        $query = ClientFollowUp::with(['agent'])
            ->where('user_id', $userId);
        
        // 排序
        if ($request->has('sort') && $request->sort) {
            $direction = $request->has('direction') && $request->direction === 'desc' ? 'desc' : 'asc';
            $query->orderBy($request->sort, $direction);
        } else {
            $query->latest('follow_up_date');
        }
        
        return $query->paginate($request->input('per_page', 15));
    }
    
    /**
     * 获取专员的跟进记录
     *
     * @param int $agentId
     * @param Request $request
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getAgentFollowUps($agentId, Request $request)
    {
        $query = ClientFollowUp::with(['client'])
            ->where('employee_id', $agentId);
        
        // 筛选条件
        if ($request->has('result') && $request->result) {
            $query->where('result', $request->result);
        }
        
        if ($request->has('contact_method') && $request->contact_method) {
            $query->where('contact_method', $request->contact_method);
        }
        
        // 排序
        if ($request->has('sort') && $request->sort) {
            $direction = $request->has('direction') && $request->direction === 'desc' ? 'desc' : 'asc';
            $query->orderBy($request->sort, $direction);
        } else {
            $query->latest('follow_up_date');
        }
        
        return $query->paginate($request->input('per_page', 15));
    }
} 