<?php

namespace App\Delivery\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Delivery\Services\DeliveryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class DeliveryController extends Controller
{
    /**
     * 配送服务
     *
     * @var DeliveryService
     */
    protected $deliveryService;

    /**
     * 构造函数
     *
     * @param DeliveryService $deliveryService
     */
    public function __construct(DeliveryService $deliveryService)
    {
        $this->deliveryService = $deliveryService;
    }

    /**
     * 获取配送列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 现在token绑定到Employee模型，直接获取Employee
            $employee = $request->user();
            $deliveries = $this->deliveryService->getDeliveries($request, $employee);
            
            return response()->json(ApiResponse::success($deliveries));
        } catch (\Exception $e) {
            Log::error('获取配送列表失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('获取配送列表失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 分配配送任务
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function assignDeliverer(Request $request, $orderId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'employee_deliverer_id' => 'required|exists:employees,id',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            // 检查用户权限
            $user = $request->user();
            $employee = $user ? $user->employee : null;
            $isDeliveryStaff = $employee && $employee->isDeliveryPerson();
            
            if (!$isDeliveryStaff) {
                return response()->json(ApiResponse::error('没有权限分配配送任务', 403), 403);
            }
            
            // 调用服务分配配送
            $delivery = $this->deliveryService->assignDelivery($orderId, $request->employee_deliverer_id);
            
            return response()->json(ApiResponse::success($delivery, '配送任务分配成功', 200), 201);
        } catch (\Exception $e) {
            Log::error('分配配送任务失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error($e->getMessage(), 400), 400);
        }
    }
    
    /**
     * 更新配送状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'required|in:pending,in_progress,completed',
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            // 检查用户权限
            $employee = $request->user(); // 现在token绑定到Employee模型
            $delivery = $this->deliveryService->getDelivery($id);
            
            // 检查是否是有权限的用户：配送员本人或管理员
            $isAuthorized = false;
            
            if ($employee) {
                // 管理员和经理可以更新所有配送状态
                if (in_array($employee->role, ['admin', 'manager'])) {
                    $isAuthorized = true;
                } 
                // 配送员只能更新分配给自己的配送任务
                elseif ($employee->role === 'delivery') {
                    $isAuthorized = $delivery->deliverer && $delivery->deliverer->employee_id === $employee->id;
                }
            }
            
            if (!$isAuthorized) {
                return response()->json(ApiResponse::error('没有权限更新配送状态', 403), 403);
            }
            
            // 调用服务更新状态
            $delivery = $this->deliveryService->updateDeliveryStatus($id, $request->status);
            
            return response()->json(ApiResponse::success($delivery, '配送状态更新成功'));
        } catch (\Exception $e) {
            Log::error('更新配送状态失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('更新配送状态失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取配送详情
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        try {
            $delivery = $this->deliveryService->getDelivery($id);
            
            // 检查权限
            $employee = $request->user(); // 现在token绑定到Employee模型
            
            // 配送员可以查看分配给自己的配送详情，管理员可以查看所有
            $isAuthorized = false;
            
            if ($employee) {
                // 管理员和经理可以查看所有配送任务
                if (in_array($employee->role, ['admin', 'manager'])) {
                    $isAuthorized = true;
                } 
                // 配送员只能查看分配给自己的配送任务
                elseif ($employee->role === 'delivery') {
                    $isAuthorized = $delivery->deliverer && $delivery->deliverer->employee_id === $employee->id;
                }
            }
            
            if (!$isAuthorized) {
                return response()->json(ApiResponse::error('无权查看此配送详情', 403), 403);
            }
            
            return response()->json(ApiResponse::success($delivery));
        } catch (\Exception $e) {
            Log::error('获取配送详情失败: ' . $e->getMessage());
            return response()->json(ApiResponse::error('获取配送详情失败', 404), 404);
        }
    }
} 