/* components/product-item/product-item.wxss */

.product-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.product-item:active {
  transform: scale(0.98);
}

/* 网格模式样式 */
.product-item.grid-mode {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.grid-mode .product-image-container {
  width: 100%;
  height: 320rpx;
  position: relative;
}

.grid-mode .product-info {
  padding: 16rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.grid-mode .product-title {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  height: 72rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 12rpx;
}

/* 列表模式样式 */
.product-item.list-mode {
  display: flex;
  width: 100%;
  height: 200rpx;
}

.list-mode .product-image-container {
  width: 200rpx;
  height: 200rpx;
  flex-shrink: 0;
  position: relative;
}

.list-mode .product-info {
  flex: 1;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.list-mode .product-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  height: 78rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 通用样式 */
.product-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.product-tags {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  display: flex;
  flex-wrap: wrap;
}

.product-tag {
  padding: 4rpx 12rpx;
  background-color: rgba(255, 77, 79, 0.8);
  color: #fff;
  font-size: 20rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
  margin-bottom: 8rpx;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.product-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 24rpx;
  color: #ff4d4f;
}

.price-value {
  font-size: 32rpx;
  font-weight: 500;
  color: #ff4d4f;
  margin-right: 8rpx;
}

.price-original {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
}

.product-sales {
  font-size: 22rpx;
  color: #999;
}

.product-bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12rpx;
}

.product-desc {
  font-size: 22rpx;
  color: #999;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-cart-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
} 