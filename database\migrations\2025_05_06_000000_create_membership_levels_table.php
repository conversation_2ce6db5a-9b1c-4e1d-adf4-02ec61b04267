<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('membership_levels', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('等级名称');
            $table->string('description')->nullable()->comment('等级描述');
            $table->integer('points_required')->default(0)->comment('达到此等级所需的积分');
            $table->decimal('discount_rate', 5, 2)->default(100.00)->comment('折扣率，百分比');
            $table->boolean('is_default')->default(false)->comment('是否为默认等级');
            $table->string('icon')->nullable()->comment('等级图标');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('status')->default(true)->comment('状态：启用/禁用');
            $table->json('privileges')->nullable()->comment('特权配置，JSON格式');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('membership_levels');
    }
}; 