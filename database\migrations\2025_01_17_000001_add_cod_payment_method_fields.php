<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. 为orders表添加货到付款实际收款方式字段
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('cod_payment_method', ['cash', 'wechat', 'alipay'])
                  ->nullable()
                  ->after('cod_status')
                  ->comment('货到付款实际收款方式');
        });
        
        // 2. 扩展orders表的cod_status枚举值
        DB::statement("ALTER TABLE `orders` MODIFY COLUMN `cod_status` ENUM(
            'unpaid',
            'paid',
            'paid_cash',
            'paid_online',
            'pending_online_payment',
            'link_generated',
            'partial_paid',
            'overpaid'
        ) DEFAULT 'unpaid' COMMENT '货到付款状态'");
        
        // 3. 扩展payment_records表的business_type枚举值
        DB::statement("ALTER TABLE `payment_records` MODIFY COLUMN `business_type` ENUM(
            'order_payment',
            'correction_supplement',
            'correction_refund', 
            'cod_final',
            'cod_cash_settlement',
            'cod_wechat_settlement',
            'cod_difference_supplement',
            'cod_difference_refund',
            'cod_correction_settlement'
        ) NOT NULL COMMENT '业务类型'");
        
        // 4. 扩展payment_records表的status枚举值
        DB::statement("ALTER TABLE `payment_records` MODIFY COLUMN `status` ENUM(
            'pending',
            'success',
            'failed',
            'refunded',
            'cancelled',
            'pending_settlement',
            'settled',
            'pending_payment_method'
        ) DEFAULT 'pending' COMMENT '状态'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复原始枚举值
        DB::statement("ALTER TABLE `orders` MODIFY COLUMN `cod_status` ENUM(
            'unpaid',
            'paid',
            'link_generated',
            'partial_paid',
            'overpaid'
        ) DEFAULT 'unpaid' COMMENT '货到付款状态'");
        
        DB::statement("ALTER TABLE `payment_records` MODIFY COLUMN `business_type` ENUM(
            'order_payment',
            'correction_supplement',
            'correction_refund', 
            'cod_final',
            'cod_cash_settlement',
            'cod_wechat_settlement',
            'cod_difference_supplement',
            'cod_difference_refund'
        ) NOT NULL COMMENT '业务类型'");
        
        DB::statement("ALTER TABLE `payment_records` MODIFY COLUMN `status` ENUM(
            'pending',
            'success',
            'failed',
            'refunded',
            'cancelled',
            'pending_settlement',
            'settled'
        ) DEFAULT 'pending' COMMENT '状态'");
        
        // 删除新增字段
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('cod_payment_method');
        });
    }
}; 