<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class ShopConfig extends Model
{
    use HasFactory;

    /**
     * 允许批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'key',
        'value',
        'group',
        'title',
        'description',
        'type',
        'options',
        'is_system',
        'sort_order',
    ];

    /**
     * 自动转换的属性
     *
     * @var array
     */
    protected $casts = [
        'options' => 'json',
        'is_system' => 'boolean',
        'sort_order' => 'integer',
    ];

    // 在模型被实例化时记录调试信息
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        
        // 记录模型信息到日志
        Log::info('ShopConfig模型被实例化', [
            'table' => $this->getTable(),
            'connection' => $this->getConnectionName() ?: config('database.default'),
            'exists' => $this->exists,
            'backtrace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
        ]);
    }

    /**
     * 获取配置
     *
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function getConfig(string $key, $default = null)
    {
        // 记录方法调用
        Log::info('调用ShopConfig::getConfig', [
            'key' => $key,
            'default' => $default
        ]);
        
        $config = static::where('key', $key)->first();
        
        // 记录查询结果
        Log::info('ShopConfig::getConfig结果', [
            'key' => $key,
            'found' => $config ? true : false,
            'value' => $config ? $config->value : null
        ]);
        
        return $config ? $config->value : $default;
    }

    /**
     * 设置配置
     *
     * @param string $key 配置键名
     * @param mixed $value 配置值
     * @param array $attributes 其他属性
     * @return \App\Models\ShopConfig
     */
    public static function setConfig(string $key, $value, array $attributes = [])
    {
        $config = static::firstOrNew(['key' => $key]);
        $config->value = $value;
        
        foreach ($attributes as $attr => $val) {
            if (in_array($attr, $config->getFillable())) {
                $config->{$attr} = $val;
            }
        }
        
        $config->save();
        return $config;
    }

    /**
     * 获取分组配置
     *
     * @param string $group 分组名称
     * @return \Illuminate\Support\Collection
     */
    public static function getGroupConfigs(string $group)
    {
        // 记录方法调用
        Log::info('调用ShopConfig::getGroupConfigs', [
            'group' => $group
        ]);
        
        $configs = static::where('group', $group)
            ->orderBy('sort_order')
            ->get();
            
        // 记录查询结果
        Log::info('ShopConfig::getGroupConfigs结果', [
            'group' => $group,
            'count' => $configs->count(),
            'items' => $configs->take(3)->toArray()
        ]);
        
        return $configs;
    }
} 