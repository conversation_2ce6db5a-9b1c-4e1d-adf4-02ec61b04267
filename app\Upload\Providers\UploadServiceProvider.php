<?php

namespace App\Upload\Providers;

use App\Models\UploadConfig;
use App\Upload\Contracts\StorageDriverInterface;
use App\Upload\Services\CosStorageDriver;
use App\Upload\Services\LocalStorageDriver;
use App\Upload\Services\UploadService;
use Illuminate\Support\ServiceProvider;

class UploadServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     *
     * @return void
     */
    public function register()
    {
        // 合并配置文件
        $this->mergeConfigFrom(
            __DIR__ . '/../config/upload.php', 'upload'
        );

        // 注册存储驱动实现
        $this->app->bind(StorageDriverInterface::class, function ($app) {
            // 优先从数据库获取驱动类型
            $driver = UploadConfig::getValue('upload_driver');
            
            // 如果数据库没有，从配置文件获取
            if (!$driver) {
                $driver = config('upload.default_driver', 'local');
            }
            
            return match($driver) {
                'cos' => new CosStorageDriver(),
                'local' => new LocalStorageDriver(),
                default => new LocalStorageDriver(),
            };
        });

        // 注册上传服务
        $this->app->singleton(UploadService::class, function ($app) {
            return new UploadService($app->make(StorageDriverInterface::class));
        });
    }

    /**
     * 引导服务
     *
     * @return void
     */
    public function boot()
    {
        // 发布配置文件
        $this->publishes([
            __DIR__ . '/../config/upload.php' => config_path('upload.php'),
        ], 'upload-config');

        // 加载路由
        $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
    }
} 