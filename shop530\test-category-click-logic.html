<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类点击逻辑测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-container { display: flex; gap: 20px; }
        .left-panel { width: 300px; border: 1px solid #ddd; padding: 10px; }
        .right-panel { flex: 1; border: 1px solid #ddd; padding: 10px; }
        
        .category-item { 
            padding: 10px; 
            border: 1px solid #eee; 
            margin: 5px 0; 
            cursor: pointer;
            background: #f8f8f8;
        }
        .category-item.active { background: #e8f4fd; }
        
        .subcategory-container { 
            overflow: hidden; 
            transition: height 0.3s ease;
            background: #fff;
        }
        .subcategory-item { 
            padding: 8px 20px; 
            border-bottom: 1px solid #f0f0f0; 
            cursor: pointer;
        }
        .subcategory-item.active { background: #e8f4fd; color: #07c160; }
        
        .third-category-container {
            background: #f8f8f8;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .third-category-item {
            display: inline-block;
            padding: 8px 20px;
            margin: 5px;
            background: #f8f8f8;
            border-radius: 28px;
            cursor: pointer;
            border: 1px solid #f0f0f0;
        }
        .third-category-item.active {
            background: #e8f4fd;
            border-color: #07c160;
            color: #07c160;
        }
        
        .api-call { 
            background: #f0f8ff; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px;
            font-family: monospace;
        }
        .current-state {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>分类点击逻辑测试</h1>
    
    <div class="test-container">
        <!-- 左侧分类导航 -->
        <div class="left-panel">
            <h3>左侧分类导航</h3>
            <div id="categoryList"></div>
        </div>
        
        <!-- 右侧内容区域 -->
        <div class="right-panel">
            <h3>右侧内容区域</h3>
            
            <!-- 三级分类 -->
            <div id="thirdCategories"></div>
            
            <!-- 当前状态 -->
            <div class="current-state">
                <h4>当前状态:</h4>
                <div id="currentState"></div>
            </div>
            
            <!-- API调用信息 -->
            <div class="api-call">
                <h4>API调用:</h4>
                <div id="apiCall">未选择分类</div>
            </div>
            
            <!-- 商品列表 -->
            <div id="productArea">
                <h4>商品列表:</h4>
                <div id="productList">请选择分类查看商品</div>
            </div>
        </div>
    </div>

    <script>
        // 模拟分类数据
        const categories = [
            {
                id: 1,
                name: "猪生鲜",
                hasChildren: true,
                isExpanded: false,
                children_data: [
                    { id: 4, name: "鲜货", children_data: [{ id: 108, name: "123" }] },
                    { id: 5, name: "半熟", children_data: [] },
                    { id: 21, name: "冻品", children_data: [] }
                ]
            },
            {
                id: 77,
                name: "鸡类",
                hasChildren: true,
                isExpanded: false,
                children_data: [
                    { id: 106, name: "鲜鸡", children_data: [] },
                    { id: 79, name: "鸡爪类", children_data: [] }
                ]
            }
        ];

        let state = {
            categories: [...categories],
            expandedCategoryId: null,
            activeCategoryId: null,
            activeSubCategoryId: null,
            activeThirdCategoryId: null,
            currentCategory: null,
            thirdCategories: []
        };

        function renderCategories() {
            const html = state.categories.map(category => `
                <div class="category-item ${state.activeCategoryId === category.id ? 'active' : ''}" 
                     onclick="handleCategoryClick(${category.id}, 'main')">
                    ${category.name}
                </div>
                <div class="subcategory-container" style="height: ${category.isExpanded && category.children_data.length ? (category.children_data.length * 40) + 'px' : '0'}">
                    ${category.children_data.map(subItem => `
                        <div class="subcategory-item ${state.activeSubCategoryId === subItem.id ? 'active' : ''}"
                             onclick="handleCategoryClick(${subItem.id}, 'sub', ${category.id})">
                            ${subItem.name}
                        </div>
                    `).join('')}
                </div>
            `).join('');
            
            document.getElementById('categoryList').innerHTML = html;
        }

        function renderThirdCategories() {
            if (state.thirdCategories.length > 0) {
                const html = `
                    <div class="third-category-container">
                        <h4>三级分类:</h4>
                        ${state.thirdCategories.map(item => `
                            <span class="third-category-item ${state.activeThirdCategoryId === item.id ? 'active' : ''}"
                                  onclick="handleThirdCategoryClick(${item.id})">
                                ${item.name}
                            </span>
                        `).join('')}
                    </div>
                `;
                document.getElementById('thirdCategories').innerHTML = html;
            } else {
                document.getElementById('thirdCategories').innerHTML = '';
            }
        }

        function updateCurrentState() {
            const stateHtml = `
                <p><strong>一级分类ID:</strong> ${state.activeCategoryId || '未选择'}</p>
                <p><strong>二级分类ID:</strong> ${state.activeSubCategoryId || '未选择'}</p>
                <p><strong>三级分类ID:</strong> ${state.activeThirdCategoryId || '未选择'}</p>
                <p><strong>当前分类:</strong> ${state.currentCategory ? state.currentCategory.name : '未选择'}</p>
                <p><strong>当前分类ID:</strong> ${state.currentCategory ? state.currentCategory.id : '未选择'}</p>
            `;
            document.getElementById('currentState').innerHTML = stateHtml;
        }

        function updateApiCall() {
            if (state.currentCategory) {
                // 判断是否是一级分类
                const isMainCategory = !state.currentCategory.parentId && !state.activeSubCategoryId && !state.activeThirdCategoryId;

                let apiParams = `{ category_id: ${state.currentCategory.id}`;
                if (isMainCategory) {
                    apiParams += `, include_children: true`;
                }
                apiParams += ` }`;

                const apiHtml = `
                    <p><strong>API调用:</strong> api.getProducts(${apiParams})</p>
                    <p><strong>说明:</strong> ${isMainCategory ?
                        `加载一级分类"${state.currentCategory.name}"及其所有子分类的商品` :
                        `只加载分类"${state.currentCategory.name}"的商品`}</p>
                    <p><strong>分类级别:</strong> ${isMainCategory ? '一级分类（包含子分类）' : '子分类（仅当前分类）'}</p>
                `;
                document.getElementById('apiCall').innerHTML = apiHtml;

                // 模拟商品列表
                const productHtml = isMainCategory ?
                    `正在加载一级分类"${state.currentCategory.name}"及其所有子分类的商品...` :
                    `正在加载分类"${state.currentCategory.name}"(ID: ${state.currentCategory.id})的商品...`;
                document.getElementById('productList').innerHTML = productHtml;
            } else {
                document.getElementById('apiCall').innerHTML = '未选择分类';
                document.getElementById('productList').innerHTML = '请选择分类查看商品';
            }
        }

        function handleCategoryClick(id, type, parentId = null) {
            console.log(`点击${type === 'main' ? '一级' : '二级'}分类: ID=${id}`);
            
            if (type === 'main') {
                const category = state.categories.find(c => c.id === id);
                
                // 更新选中状态
                state.activeCategoryId = id;
                state.activeSubCategoryId = null;
                state.activeThirdCategoryId = null;
                state.currentCategory = category;
                state.thirdCategories = [];
                
                // 切换展开状态
                if (category.hasChildren) {
                    toggleExpansion(id);
                }
                
                console.log(`✅ 一级分类点击: 将加载分类"${category.name}"(ID: ${id})下的所有商品`);
                
            } else if (type === 'sub') {
                const parentCategory = state.categories.find(c => c.id === parentId);
                const subCategory = parentCategory.children_data.find(c => c.id === id);
                
                // 更新选中状态
                state.activeCategoryId = parentId;
                state.activeSubCategoryId = id;
                state.activeThirdCategoryId = null;
                state.currentCategory = { ...subCategory, parentId };
                
                // 获取三级分类
                if (subCategory.children_data && subCategory.children_data.length > 0) {
                    state.thirdCategories = subCategory.children_data.map(thirdCat => ({
                        ...thirdCat,
                        parentName: subCategory.name
                    }));
                } else {
                    state.thirdCategories = [];
                }
                
                console.log(`✅ 二级分类点击: 将加载分类"${subCategory.name}"(ID: ${id})下的商品`);
                if (state.thirdCategories.length > 0) {
                    console.log(`📋 显示三级分类: ${state.thirdCategories.map(c => c.name).join(', ')}`);
                }
            }
            
            renderCategories();
            renderThirdCategories();
            updateCurrentState();
            updateApiCall();
        }

        function handleThirdCategoryClick(id) {
            const thirdCategory = state.thirdCategories.find(c => c.id === id);
            state.activeThirdCategoryId = id;
            state.currentCategory = {
                ...thirdCategory,
                parentId: state.activeSubCategoryId,
                grandParentId: state.activeCategoryId
            };
            
            console.log(`✅ 三级分类点击: 将加载分类"${thirdCategory.name}"(ID: ${id})下的商品`);
            
            renderThirdCategories();
            updateCurrentState();
            updateApiCall();
        }

        function toggleExpansion(categoryId) {
            if (state.expandedCategoryId === categoryId) {
                // 收起
                state.categories = state.categories.map(cat => ({
                    ...cat,
                    isExpanded: false
                }));
                state.expandedCategoryId = null;
            } else {
                // 展开
                state.categories = state.categories.map(cat => ({
                    ...cat,
                    isExpanded: cat.id === categoryId
                }));
                state.expandedCategoryId = categoryId;
            }
        }

        // 初始化
        renderCategories();
        updateCurrentState();
        updateApiCall();
        
        console.log('✅ 分类点击逻辑测试页面已加载');
        console.log('📋 测试说明:');
        console.log('1. 点击一级分类 → 加载该一级分类下的所有商品');
        console.log('2. 点击二级分类 → 加载该二级分类下的商品，显示三级分类（如果有）');
        console.log('3. 点击三级分类 → 加载该三级分类下的商品');
    </script>
</body>
</html>
