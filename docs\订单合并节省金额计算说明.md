# 订单合并节省金额计算说明

## 💰 什么是预计节省

"预计节省"是指通过合并多个订单，客户可以节省的总金额。这个节省主要来源于优惠规则的重新计算和门槛达成。

## 🔍 计算原理

### 1. 分别计算原订单总成本
```
原订单A：商品总价 - 各种优惠 = 实际支付A
原订单B：商品总价 - 各种优惠 = 实际支付B
原总成本 = 实际支付A + 实际支付B
```

### 2. 计算合并后的总成本
```
合并订单：商品总价 - 合并后的各种优惠 = 合并后实际支付
```

### 3. 计算节省金额
```
预计节省 = 原总成本 - 合并后实际支付
```

## 📊 具体节省来源

### 1. 满减优惠门槛达成
**场景**：店铺设置满200减30的优惠

**示例**：
- 订单A：150元（未达到满减门槛）
- 订单B：180元（未达到满减门槛）
- 分别支付：150 + 180 = 330元

**合并后**：
- 总金额：330元
- 满200减30：330 - 30 = 300元
- **节省：30元**

### 2. 支付方式优惠升级
**场景**：微信支付有分档优惠

**优惠规则**：
- 100元以下：无优惠
- 100-200元：2%优惠  
- 200元以上：5%优惠

**示例**：
- 订单A：120元，微信支付2%优惠 = 117.6元
- 订单B：90元，无优惠 = 90元
- 分别支付：207.6元

**合并后**：
- 总金额：210元
- 微信支付5%优惠：210 × 0.95 = 199.5元
- **节省：207.6 - 199.5 = 8.1元**

### 3. 会员折扣优化
**场景**：VIP客户大额订单享受额外折扣

**优惠规则**：
- 普通订单：会员9折
- 大额订单（≥300元）：会员8.5折

**示例**：
- 订单A：180元，会员9折 = 162元
- 订单B：160元，会员9折 = 144元
- 分别支付：306元

**合并后**：
- 总金额：340元
- 会员8.5折：340 × 0.85 = 289元
- **节省：306 - 289 = 17元**

### 4. 运费优化
**场景**：满额免运费

**优惠规则**：
- 单笔订单满99元免运费，否则收取8元运费

**示例**：
- 订单A：80元 + 8元运费 = 88元
- 订单B：70元 + 8元运费 = 78元
- 分别支付：166元

**合并后**：
- 总金额：150元（满99免运费）
- **节省：16元运费**

## 🎯 系统计算逻辑

### 当前简化版本
```php
if ($totalAmount >= 200) {
    return $totalAmount * 0.05; // 5%的额外优惠
} elseif ($totalAmount >= 100) {
    return $totalAmount * 0.03; // 3%的额外优惠
}
```

### 建议的精确版本
```php
// 1. 计算原订单总成本
$originalTotalCost = 0;
foreach ($orders as $order) {
    $originalTotalCost += $order->total;
}

// 2. 模拟合并后的优惠计算
$mergedSubtotal = $orders->sum('subtotal');
$mergedDiscount = 0;

// 满减优惠
if ($mergedSubtotal >= 200) {
    $mergedDiscount += 30; // 满200减30
}

// 支付方式优惠
$paymentMethod = $orders->first()->payment_method;
if ($paymentMethod === 'wechat') {
    if ($mergedSubtotal >= 200) {
        $mergedDiscount += $mergedSubtotal * 0.05; // 5%优惠
    } elseif ($mergedSubtotal >= 100) {
        $mergedDiscount += $mergedSubtotal * 0.02; // 2%优惠
    }
}

// 会员折扣
$user = $orders->first()->user;
if ($user->is_vip && $mergedSubtotal >= 300) {
    $mergedDiscount += $mergedSubtotal * 0.05; // VIP额外5%
}

// 3. 计算合并后成本
$mergedTotalCost = $mergedSubtotal - $mergedDiscount;

// 4. 计算节省金额
$savings = $originalTotalCost - $mergedTotalCost;
```

## 💡 实际业务价值

### 对客户的价值
- **直接节省成本**：享受更多优惠
- **简化支付流程**：一次支付代替多次
- **更好的购物体验**：智能优化订单

### 对商家的价值
- **提高客单价**：鼓励客户增加购买
- **减少支付手续费**：合并支付减少交易次数
- **提升客户满意度**：帮助客户省钱

## 📈 节省金额展示

### 前端展示格式
```
原订单总计：¥330.00
合并后总计：¥285.00
预计节省：¥45.00 (13.6%)
```

### 节省明细
```
满减优惠：¥30.00
支付优惠：¥10.50
会员折扣：¥4.50
```

这样客户就能清楚地看到合并订单的具体好处！ 