<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_member_discounts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id')->comment('商品ID');
            $table->unsignedBigInteger('membership_level_id')->comment('会员等级ID');
            $table->enum('discount_type', ['fixed_amount', 'percentage'])->default('fixed_amount')->comment('优惠类型');
            $table->decimal('discount_value', 10, 2)->comment('优惠值');
            $table->decimal('max_discount', 10, 2)->nullable()->comment('最大优惠金额');
            $table->boolean('status')->default(true)->comment('启用状态');
            $table->datetime('start_time')->nullable()->comment('开始时间');
            $table->datetime('end_time')->nullable()->comment('结束时间');
            $table->text('description')->nullable()->comment('说明');
            
            $table->timestamps();
            
            // 索引
            $table->unique(['product_id', 'membership_level_id']);
            $table->index(['product_id', 'status']);
            $table->index(['membership_level_id', 'status']);
            $table->index(['start_time', 'end_time']);
            
            // 外键约束
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->foreign('membership_level_id')->references('id')->on('membership_levels')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('product_member_discounts');
    }
}; 