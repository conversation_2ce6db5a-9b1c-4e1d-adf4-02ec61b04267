<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('print_records', function (Blueprint $table) {
            $table->id();
            $table->string('printable_type')->comment('可打印对象类型');
            $table->unsignedBigInteger('printable_id')->comment('可打印对象ID');
            $table->string('print_type')->default('normal')->comment('打印类型：normal普通,receipt小票,delivery配送单');
            $table->string('driver')->default('clodop')->comment('打印驱动：clodop,browser');
            $table->string('printer_name')->nullable()->comment('打印机名称');
            $table->string('status')->default('pending')->comment('打印状态：pending待打印,printing打印中,completed已完成,failed失败');
            $table->integer('copies')->default(1)->comment('打印份数');
            $table->json('print_options')->nullable()->comment('打印选项');
            $table->text('print_content')->nullable()->comment('打印内容');
            $table->timestamp('printed_at')->nullable()->comment('打印完成时间');
            $table->string('error_message')->nullable()->comment('错误信息');
            $table->unsignedBigInteger('printed_by')->nullable()->comment('打印操作人');
            $table->timestamps();
            
            // 创建索引
            $table->index(['printable_type', 'printable_id']);
            $table->index(['print_type', 'status']);
            $table->index(['driver', 'printer_name']);
            $table->index(['printed_at']);
            $table->index(['printed_by']);
            
            // 外键约束
            $table->foreign('printed_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('print_records');
    }
}; 