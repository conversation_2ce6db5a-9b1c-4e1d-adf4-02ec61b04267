<?php

namespace App\WechatPayment\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WechatSubMerchant extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'provider_id',
        'name',
        'sub_mch_id',
        'sub_appid',
        'is_active',
        'description',
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * 获取所属的服务商
     */
    public function provider()
    {
        return $this->belongsTo(WechatServiceProvider::class, 'provider_id');
    }

    /**
     * 获取该子商户的支付记录
     */
    public function payments()
    {
        return $this->hasMany(WechatServicePayment::class, 'sub_merchant_id');
    }
} 