<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Crm\Models\CrmAgent;
use App\Employee\Models\Employee;
use Carbon\Carbon;

class CrmAgentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 获取所有员工
        $employees = Employee::all();
        
        if ($employees->isEmpty()) {
            $this->command->error('没有找到员工数据，请先运行 EmployeeSeeder');
            return;
        }
        
        // 专长领域选项
        $specialties = [
            '客户关系管理',
            '销售咨询',
            '产品推荐',
            '售后服务',
            '技术支持',
            '市场分析',
            '客户培训',
            '商务谈判'
        ];
        
        // 服务区域选项
        $serviceAreas = [
            '北京市',
            '上海市',
            '广州市',
            '深圳市',
            '杭州市',
            '南京市',
            '苏州市',
            '成都市',
            '重庆市',
            '武汉市'
        ];
        
        // 状态选项
        $statuses = ['available', 'busy', 'offline'];
        
        // 为前5个员工创建CRM专员记录
        $employeesToMakeCrmAgents = $employees->take(5);
        
        foreach ($employeesToMakeCrmAgents as $index => $employee) {
            CrmAgent::create([
                'employee_id' => $employee->id,
                'service_area' => $serviceAreas[array_rand($serviceAreas)],
                'max_clients' => rand(20, 80),
                'performance_rating' => round(rand(300, 1000) / 100, 2), // 3.00 - 10.00
                'status' => $statuses[array_rand($statuses)],
                'specialty' => $specialties[array_rand($specialties)],
                'monthly_target' => rand(50000, 200000), // 5万到20万
                'clients_count' => rand(0, 30),
                'created_at' => Carbon::now()->subDays(rand(1, 365)),
                'updated_at' => Carbon::now(),
            ]);
            
            // 更新员工角色为CRM专员
            $employee->update(['role' => 'crm_agent']);
        }
        
        $this->command->info('CRM专员数据添加完成！共添加 ' . $employeesToMakeCrmAgents->count() . ' 条记录');
        $this->command->info('当前CRM专员总数：' . CrmAgent::count());
    }
} 