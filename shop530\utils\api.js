// utils/api.js - API工具类

const { createLogger } = require('./logger');
const apiLogger = createLogger('API');

// 安全获取App实例
function getAppInstance() {
  try {
    const app = getApp();
    if (app && app.globalData !== undefined) {
      return app;
    }
  } catch (error) {
    apiLogger.warn('获取App实例失败', error);
  }
  
  // 返回默认的globalData结构
  return {
    globalData: {
      token: null,
      userInfo: null
    }
  };
}

// 引入统一配置
const {
  getCurrentConfig,
  API_PATHS,
  CACHE_CONFIG
} = require('./request-config');

/**
 * API 接口配置和管理
 */

// API 基础配置
const CONFIG = getCurrentConfig();

// 根据环境选择API地址
function getBaseUrl() {
  return CONFIG.baseUrl;
}

// API 接口定义（使用统一配置）
const API = {
  // 用户相关
  LOGIN: '/api/auth/login',
  REGISTER: '/api/auth/register',
  USER_INFO: '/api/user/info',
  
  // 商品相关（公共接口）
  PRODUCTS: '/public/products',
  PRODUCT_DETAIL: '/public/products/{id}',
  PRODUCT_CATEGORIES: '/public/categories',
  CATEGORIES: '/public/categories',
  HOT_PRODUCTS: '/public/products/hot',
  SEARCH: '/public/products/search',
  
  // 购物车相关
  CART_LIST: '/api/cart',
  CART_COUNT: '/api/cart/count',
  CART_ADD: '/api/cart',
  CART_ITEM: '/api/cart/items',
  CART_SELECT_ALL: '/api/cart/select-all',
  CART_CLEAR: '/api/cart',
  
  // 订单相关
  ORDERS: '/api/orders',
  ORDER_DETAIL: '/api/orders/{id}',
  ORDER_PAY: '/api/orders/{id}/pay',
  ORDER_CANCEL: '/api/orders/{id}/cancel',
  
  // 地址相关
  ADDRESSES: '/api/addresses',
  ADDRESS_DETAIL: '/api/addresses/{id}',
  
  // 其他
  UPLOAD: '/api/upload',
  
  // 支付相关
  PAYMENT_CALCULATE: '/api/payment/offers/calculate',
  
  // 公共接口
  BANNERS: '/shop/public/banners',
  CONFIG: '/shop/public/config'
};

// API 请求类
class ApiService {
  constructor() {
    this.baseUrl = getBaseUrl();
    this.request = require('./request');
  }
  
  /**
   * 延迟方法 - 用于模拟API调用延迟
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  // ========== 首页相关 ==========
  
  /**
   * 获取轮播图数据（公共接口）
   */
  async getBanners() {
    apiLogger.time('getBanners');
    
    try {
      const result = await this.request.get(API.BANNERS, {}, {
        cacheTime: CACHE_CONFIG.BANNERS,
        requireAuth: false,
        showLoading: false,
        showError: true,
        timeout: 8000
      });
      
      const banners = result.data || [];
      
      // 处理字段映射，确保前端兼容性
      banners.forEach(banner => {
        if (banner.image_url && !banner.image) {
          banner.image = banner.image_url;
        }
        if (banner.link_url && !banner.link) {
          banner.link = banner.link_url;
        }
        if (!banner.title) {
          banner.title = '轮播图';
        }
        if (banner.image && !banner.image.startsWith('http')) {
          banner.image = this.baseUrl.replace('/api', '') + banner.image;
        }
      });
      
      apiLogger.info(`轮播图加载成功: ${banners.length}个`);
      apiLogger.timeEnd('getBanners');
      return banners;
      
    } catch (error) {
      apiLogger.warn('轮播图加载失败，使用空数组', { error: error.message });
      apiLogger.timeEnd('getBanners');
      return [];
    }
  }
  
  /**
   * 获取分类列表（公共接口）
   * @param {Object} params - 查询参数
   * @param {number} params.parent_id - 父分类ID，0表示获取一级分类
   * @param {boolean} params.tree - 是否返回树形结构
   * @param {number} params.level - 分类层级，1表示只获取一级分类
   */
  async getCategories(params = {}) {
    console.log('📡 API调用: 获取分类数据（公共接口）', params);
    const result = await this.request.get(API.CATEGORIES, params, {
      cacheTime: CACHE_CONFIG.CATEGORIES,
      requireAuth: false // 公共接口，不需要认证
    });
    return result.data || [];
  }

  /**
   * 获取首页一级分类（专门为首页优化）
   */
  async getHomeCategories() {
    apiLogger.time('getHomeCategories');
    
    try {
      // 优先使用分类树API
      const result = await this.request.get('/public/categories/tree', {
        parent_id: 0
      }, {
        cacheTime: CACHE_CONFIG.CATEGORIES * 2,
        requireAuth: false
      });
      
      const categories = result.data || [];
      const homeCategories = categories.slice(0, 10);
      
      // 处理图标字段兼容性
      homeCategories.forEach(category => {
        if (!category.icon && (category.image_url || category.custom_icon_url)) {
          category.icon = category.image_url || category.custom_icon_url;
        }
      });
      
      apiLogger.info(`首页分类加载成功: ${homeCategories.length}个`);
      apiLogger.timeEnd('getHomeCategories');
      return homeCategories;
      
    } catch (error) {
      apiLogger.debug('分类树API失败，尝试降级方案', error.message);
      
      try {
        // 降级方案：使用普通分类API
        const result = await this.request.get(API.CATEGORIES, {}, {
          cacheTime: CACHE_CONFIG.CATEGORIES,
          requireAuth: false,
          showError: false
        });
        
        const allCategories = result.data || [];
        const topLevelCategories = allCategories.filter(category => 
          !category.parent_id || category.parent_id === 0
        );
        const homeCategories = topLevelCategories.slice(0, 10);
        
        // 处理图标字段兼容性
        homeCategories.forEach(category => {
          if (!category.icon && (category.image_url || category.custom_icon_url)) {
            category.icon = category.image_url || category.custom_icon_url;
          }
          if (category.icon && !category.icon.startsWith('http')) {
            category.icon = this.baseUrl.replace('/api', '') + category.icon;
          }
        });
        
        apiLogger.info(`首页分类加载成功(降级): ${homeCategories.length}个`);
        apiLogger.timeEnd('getHomeCategories');
        return homeCategories;
        
      } catch (fallbackError) {
        apiLogger.warn('分类数据完全加载失败，使用空数组', fallbackError.message);
        apiLogger.timeEnd('getHomeCategories');
        return [];
      }
    }
   }

   /**
    * 获取分类树结构（用于分类页面等）
    */
   async getCategoryTree(parentId = 0) {
     console.log(`📡 API调用: 获取分类树结构 - parent_id: ${parentId}`);
     const result = await this.request.get('/public/categories/tree', {
       parent_id: parentId
     }, {
       cacheTime: CACHE_CONFIG.CATEGORIES,
       requireAuth: false
     });
     return result.data || [];
   }

   /**
    * 获取指定分类的子分类
    */
   async getCategoryChildren(categoryId) {
     console.log(`📡 API调用: 获取分类子分类 - category_id: ${categoryId}`);
     const result = await this.request.get(`/public/categories/${categoryId}/children`, {}, {
       cacheTime: CACHE_CONFIG.CATEGORIES,
       requireAuth: false
     });
     return result.data || [];
   }

   /**
    * 获取分类商品列表（分类页专用）
    */
   async getCategoryProducts(params = {}) {
     const {
       categoryId,
       page = 1,
       pageSize = 20,
       sortBy = 'created_at',
       order = 'desc',
       keyword = '',
       priceMin,
       priceMax
     } = params;

     console.log(`📡 API调用: 获取分类商品 - category_id: ${categoryId}`, params);

     // 构建查询参数
     const queryParams = {
       page,
       per_page: pageSize,
       sort: sortBy,
       order,
       keyword,
       // 包含关联数据
       with: 'images,category,tags' // 请求包含图片、分类、标签关联数据
     };

     // 如果指定了分类ID，添加分类筛选
     if (categoryId && categoryId > 0) {
       queryParams.category_id = categoryId;
       console.log(`🔍 筛选分类ID: ${categoryId}`);
     } else {
       console.log(`📦 获取全部商品（无分类筛选） - categoryId:`, categoryId);
     }
     
     // 🔍 调试：详细记录最终请求参数
     console.log(`📡 getCategoryProducts最终请求参数:`, queryParams);

     // 价格范围筛选
     if (priceMin !== undefined) {
       queryParams.price_min = priceMin;
     }
     if (priceMax !== undefined) {
       queryParams.price_max = priceMax;
     }

     const result = await this.request.get(API.PRODUCTS, queryParams, {
       cacheTime: CACHE_CONFIG.PRODUCTS,
       requireAuth: false
     });

     // 处理返回数据格式，适配分类页期望的格式
     // 后端返回的是分页数据结构：{ data: { data: [...], current_page: 1, ... } }
     const paginationData = result.data || {};
     const rawProducts = paginationData.data || [];
     
     // 确保rawProducts是数组
     if (!Array.isArray(rawProducts)) {
       console.error('❌ rawProducts不是数组:', rawProducts);
       throw new Error(`商品数据格式错误：期望数组，实际得到 ${typeof rawProducts}`);
     }
     
     const meta = {
       current_page: paginationData.current_page || page,
       last_page: paginationData.last_page || 1,
       total: paginationData.total || 0,
       per_page: paginationData.per_page || pageSize,
       from: paginationData.from || 0,
       to: paginationData.to || 0
     };

     console.log('📦 分页数据解析:', {
       总商品数: meta.total,
       当前页: meta.current_page,
       总页数: meta.last_page,
       本页商品数: rawProducts.length
     });

     // 增强商品数据处理
     const processedProducts = rawProducts.map((product, index) => {
       // 调试：打印前3个商品的原始数据结构
       if (index < 3) {
         console.log(`🔍 原始商品${index + 1} [${product.name}] 所有字段:`, Object.keys(product));
         console.log(`🔍 原始商品${index + 1} 图片相关字段:`, {
            images: product.images, // 关联的图片数据
            image: product.image,
            image_url: product.image_url,
            cover_image: product.cover_image
          });
          
          // 如果有关联图片，详细打印图片信息
          if (product.images && Array.isArray(product.images)) {
            console.log(`📸 商品${index + 1} 关联图片详情:`, product.images.map(img => ({
              id: img.id,
              url: img.url,
              path: img.path,
              is_main: img.is_main,
              sort: img.sort
            })));
          }
          
          // 打印完整的商品数据以便分析
          console.log(`🔍 原始商品${index + 1} 完整数据:`, product);
       }
       
       // 处理商品标签
       let tags = [];
       if (product.tags && Array.isArray(product.tags)) {
         tags = product.tags;
       } else if (product.tags && typeof product.tags === 'string') {
         // 如果标签是字符串，尝试解析
         try {
           tags = JSON.parse(product.tags);
         } catch (e) {
           // 如果解析失败，按逗号分割
           tags = product.tags.split(',').map(tag => ({ name: tag.trim() }));
         }
       }

       // 处理商品图片 - 优先使用关联的图片数据
       let image = '';
       
       if (product.images && Array.isArray(product.images) && product.images.length > 0) {
         // 优先使用主图
         const mainImage = product.images.find(img => img.is_main === 1 || img.is_main === true);
         if (mainImage) {
           image = mainImage.url || mainImage.path || '';
         } else {
           // 如果没有主图，使用第一张图片
           const firstImage = product.images[0];
           image = firstImage.url || firstImage.path || '';
         }
       }
       
       // 如果关联图片为空，尝试其他可能的字段
       if (!image) {
         image = product.image_url || 
                 product.image || 
                 product.cover_image || 
                 product.thumbnail || 
                 product.photo || 
                 product.picture || 
                 product.img || 
                 product.cover_url || '';
       }
       
       // 如果仍然没有图片，使用默认占位图
       if (!image) {
         // 不在这里设置占位图，让组件自己处理
         image = '';
       }

       // 处理价格
       const price = parseFloat(product.price || product.sale_price || 0).toFixed(2);

       // 处理描述
       const description = product.description || product.subtitle || product.summary || '新鲜直达';

       return {
         ...product,
         // 标准化字段
         image: image,
         price: price,
         description: description,
         tags: tags,
         // 保留原始字段以备用
         originalTags: product.tags,
         originalImage: product.image_url || product.image,
         // 添加供应商信息
         supplier: product.supplier || product.brand || '自营',
         // 添加单位信息
         unit: product.unit || product.measure_unit || '件',
         // 库存状态现在由后端API提供，不再在前端计算
         // outOfStock: product.stock <= 0 || product.status === 'out_of_stock',
         // 添加促销状态
         isPromotion: product.is_promotion || product.promotion_price > 0
       };
     });

     console.log(`📦 商品数据处理完成: ${processedProducts.length}个商品`);
     
     // 调试：打印前3个商品的标签信息
     processedProducts.slice(0, 3).forEach((product, index) => {
       console.log(`🏷️ 商品${index + 1} [${product.name}] 标签处理:`, {
         原始标签: product.originalTags,
         处理后标签: product.tags,
         图片: product.image,
         价格: product.price,
         描述: product.description
       });
     });

     return {
       list: processedProducts,
       hasMore: meta.current_page < meta.last_page,
       total: meta.total || 0,
       currentPage: meta.current_page || page,
       lastPage: meta.last_page || 1,
       meta: meta
     };
   }
   
   /**
   * 获取热门商品（公共接口）
   */
  async getHotProducts(limit = 10) {
    console.log('📡 API调用: 获取热门商品数据（公共接口）');
    const result = await this.request.get(API.HOT_PRODUCTS, {
      limit
    }, {
      cacheTime: CACHE_CONFIG.HOT_PRODUCTS,
      requireAuth: false // 公共接口，不需要认证
    });
    return {
      data: result.data || [],
      meta: result.meta || {}
    };
  }
  
  /**
   * 搜索商品（公共接口）
   */
  async searchProducts(keyword, page = 1, per_page = 20) {
    console.log(`📡 API调用: 搜索商品 - ${keyword}（公共接口）`);
    const result = await this.request.get(API.SEARCH, {
      keyword,
      page,
      per_page
    }, {
      requireAuth: false // 公共接口，不需要认证
    });
    return {
      data: result.data || [],
      meta: result.meta || {}
    };
  }
  
  /**
   * 获取商品列表（公共接口）
   */
  async getProducts(params = {}) {
    console.log('📡 API调用: 获取商品列表（公共接口）', params);
    const {
      category_id,
      page = 1,
      per_page = 20,
      pageSize = 20, // 兼容pageSize参数
      sort = 'created_at',
      order = 'desc',
      keyword = '',
      sales_count, // 销量筛选
      is_promotion, // 促销商品筛选
      promotion_price, // 促销价格排序
      include_children // 是否包含子分类商品
    } = params;
    
    // 处理分页参数兼容性
    const actualPageSize = pageSize || per_page;
    
    const requestParams = {
      category_id,
      page,
      per_page: actualPageSize,
      sort,
      order,
      keyword
    };
    
    // 添加可选参数
    if (sales_count !== undefined) {
      requestParams.sales_count = sales_count;
    }

    if (is_promotion !== undefined) {
      requestParams.is_promotion = is_promotion;
    }

    if (promotion_price !== undefined) {
      requestParams.promotion_price = promotion_price;
    }

    if (include_children !== undefined) {
      requestParams.include_children = include_children;
    }
    
    console.log('📡 最终请求参数:', requestParams);
    
    const result = await this.request.get(API.PRODUCTS, requestParams, {
      cacheTime: CACHE_CONFIG.PRODUCTS,
      requireAuth: false // 公共接口，不需要认证
    });
    
    // 统一返回格式，兼容分页信息
    const responseData = result.data || [];
    const responseMeta = result.meta || {};
    
    // 计算hasMore
    const hasMore = responseMeta.current_page ? 
      responseMeta.current_page < responseMeta.last_page : 
      responseData.length >= actualPageSize;
    
    return {
      list: responseData, // 使用list字段兼容前端
      data: responseData, // 保持data字段兼容性
      meta: responseMeta,
      hasMore: hasMore
    };
  }
  
  /**
   * 获取商品详情（公共接口）
   * @param {number} productId - 商品ID
   * @param {Object} params - 查询参数
   * @param {boolean} params.include_price - 是否包含价格信息
   * @param {number} params.region_id - 区域ID
   * @param {number} params.quantity - 数量
   */
  async getProductDetail(productId, params = {}) {
    console.log(`📡 API调用: 获取商品详情 - ${productId}`, params);
    // 修复路径重复问题：使用相对路径，不要带api前缀
    const url = API.PRODUCT_DETAIL.replace('{id}', productId);
    
    // 如果包含价格信息，需要认证以获取用户会员折扣
    const needAuth = params.include_price || false;
    
    const result = await this.request.get(url, params, {
      requireAuth: needAuth, // 包含价格时需要认证以计算会员折扣
      cacheTime: params.include_price ? 0 : CACHE_CONFIG.PRODUCT_DETAIL // 包含价格信息时不缓存
    });
    return result.data || {};
  }

  /**
   * 批量获取商品价格
   * @param {Array} productIds - 商品ID数组
   * @param {Object} params - 查询参数
   * @param {number} params.region_id - 区域ID
   * @param {number} params.quantity - 数量（默认1）
   */
  async getBatchProductPrices(productIds, params = {}) {
    console.log('📡 API调用: 批量获取商品价格', productIds, params);
    
    const requestData = {
      product_ids: productIds,
      quantity: params.quantity || 1,
      ...params
    };
    
      // 使用shop价格API路径（支持游客和登录用户）
  const result = await this.request.post('/shop/products/batch-prices', requestData, {
      cacheTime: 0, // 价格信息不缓存
      showLoading: false, // 批量获取时不显示loading
    requireAuth: true // 需要认证以计算会员折扣，获取用户特定价格
    });
    
    return result;
  }

  /**
   * 获取用户常购商品（个性化推荐）
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.per_page - 每页数量
   */
  async getUserFrequentProducts(params = {}) {
    console.log('📡 API调用: 获取用户常购商品', params);
    try {
      const result = await this.request.get('/wechat/mp/user/frequent-products', params, {
        requireAuth: true, // 需要用户登录
        cacheTime: 0, // 个性化数据不缓存
        showLoading: false,
        showError: false // 静默失败，由上层处理
      });
      
      // 验证返回数据格式
      if (result && result.data && Array.isArray(result.data)) {
        console.log('✅ 用户常购商品API调用成功:', result.data.length, '个商品');
        
        // 处理商品数据，确保字段完整性
        const processedProducts = result.data.map(product => {
          return {
            ...product,
            // 确保必要字段存在
            id: product.id,
            name: product.name || '未知商品',
            price: parseFloat(product.price) || 0,
            sale_price: product.sale_price ? parseFloat(product.sale_price) : null,
            stock: parseInt(product.stock) || 0,
            sales_count: parseInt(product.sales_count) || 0,
            rating: parseFloat(product.rating) || 0,
            image_url: product.image_url || '',
            description: product.description || '',
            category_name: product.category_name || '',
            // 保留购买统计信息
            frequent_info: product.frequent_info || {}
          };
        });
        
        return {
          ...result,
          data: processedProducts
        };
      } else {
        console.warn('⚠️ 用户常购商品API返回数据格式异常:', result);
        return null;
      }
      
    } catch (error) {
      console.warn('⚠️ 获取用户常购商品失败:', error);
      
      // 根据错误类型进行不同处理
      if (error.statusCode === 401) {
        console.log('🔄 用户认证失败，需要重新登录');
        // 可以在这里触发重新登录逻辑
      } else if (error.statusCode === 403) {
        console.log('🔄 用户权限不足');
      } else if (error.statusCode >= 500) {
        console.log('🔄 服务器错误，稍后重试');
      }
      
      return null;
    }
  }
  
  /**
   * 获取首页商品分区数据
   * 直接获取普通商品，不使用复杂分区逻辑
   */
  async getProductSections() {
    console.log('📡 获取首页商品数据（简化版）');
    
    try {
      // 直接获取普通商品作为默认分区
      const defaultProducts = await this.getProducts({ limit: 12 }).catch(() => ({ data: [] }));
      
      if (defaultProducts.data && defaultProducts.data.length > 0) {
        const sections = [{
          type: 'all',
          title: '精选商品',
          subtitle: '优质好货',
          products: this.processProductsForSection(defaultProducts.data)
        }];
        
        console.log(`📦 首页商品数据加载完成: ${sections[0].products.length}个商品`);
        return sections;
      }
      
      return [];
      
    } catch (error) {
      console.error('❌ 获取首页商品数据失败:', error);
      return [];
    }
  }

  /**
   * 获取商品标签配置
   */
  async getProductTabs() {
    console.log('📡 API调用: 获取商品标签配置');
    
    try {
      const result = await this.request.get(API_PATHS.PUBLIC.PRODUCT_TABS, {}, {
        cacheTime: CACHE_CONFIG.CATEGORIES,
        requireAuth: false
      });
      
      if (result.data && result.data.length > 0) {
        console.log('🔍 后端返回的原始标签数据:', result.data);
        
        // 确保有"全部"标签
        const tabs = [{ type: 'all', name: '全部' }];
        
        result.data.forEach((tab, index) => {
          console.log(`🏷️ 处理标签 ${index}:`, tab);
          
          // 容错处理：确保tab是对象且有必要字段
          if (tab && typeof tab === 'object' && tab.type && tab.type !== 'all') {
            const processedTab = {
              type: String(tab.type || ''),
              name: String(tab.name || tab.title || '未命名'),
              icon: tab.icon ? String(tab.icon) : '',
              sort: Number(tab.sort || 0)
            };
            
            console.log(`✅ 处理后的标签:`, processedTab);
            tabs.push(processedTab);
          } else {
            console.warn('⚠️ 跳过无效标签:', tab);
          }
        });
        
        // 按排序字段排序
        tabs.sort((a, b) => (a.sort || 0) - (b.sort || 0));
        
        console.log(`📋 最终标签数据: ${tabs.length}个标签`, tabs);
        return tabs;
      }
      
      // 如果API没有数据，返回默认标签
      return [{ type: 'all', name: '全部' }];
      
    } catch (error) {
      console.log('📡 商品标签API不可用，将从商品分区生成标签');
      return null; // 返回null表示需要从分区生成
    }
  }

  /**
   * 根据标签获取商品
   */
  async getProductsByTag(tagSlug, params = {}) {
    console.log(`📡 API调用: 根据标签获取商品 - ${tagSlug}`, params);
    
    try {
      const {
        page = 1,
        per_page = 20,
        category_id,
        keyword
      } = params;
      
      const queryParams = {
        page,
        per_page,
        with: 'images,category,tags'
      };
      
      // 如果是特定标签，添加标签筛选
      if (tagSlug && tagSlug !== 'all') {
        queryParams.tag_slug = tagSlug;
        console.log(`🏷️ 添加标签筛选: tag_slug = ${tagSlug}`);
      } else {
        console.log(`📋 获取全部商品（无标签筛选）`);
      }
      
      // 添加其他筛选条件
      if (category_id) {
        queryParams.category_id = category_id;
      }
      
      if (keyword) {
        queryParams.keyword = keyword;
      }
      
      console.log(`📡 最终请求参数:`, queryParams);
      
      const result = await this.request.get(API_PATHS.PUBLIC.PRODUCTS, queryParams, {
        requireAuth: false
      });
      
      console.log(`📡 API响应:`, result);
      
      // 处理商品数据
      if (result.data && result.data.data) {
        const products = this.processProductsForSection(result.data.data);
        
        console.log(`📋 根据标签${tagSlug}获取到商品: ${products.length}个`);
        console.log(`📊 分页信息:`, {
          current_page: result.data.current_page,
          total: result.data.total,
          per_page: result.data.per_page,
          last_page: result.data.last_page
        });
        
        return {
          data: products,
          meta: result.data
        };
      }
      
      console.warn(`⚠️ API返回数据格式异常:`, result);
      return {
        data: [],
        meta: {}
      };
      
    } catch (error) {
      console.error(`❌ 根据标签获取商品失败 - ${tagSlug}:`, error);
      
      // 如果标签筛选失败，降级获取全部商品
      if (tagSlug && tagSlug !== 'all') {
        console.log(`🔄 标签筛选失败，降级获取全部商品`);
        try {
          const fallbackResult = await this.getProducts({ 
            page: params.page || 1, 
            per_page: params.per_page || 20 
          });
          return {
            data: fallbackResult.data || [],
            meta: fallbackResult.meta || {}
          };
        } catch (fallbackError) {
          console.error(`❌ 降级获取商品也失败:`, fallbackError);
        }
      }
      
      return {
        data: [],
        meta: {}
      };
    }
  }
  
  /**
   * 处理商品数据用于分区显示
   */
  processProductsForSection(products) {
    if (!Array.isArray(products)) {
      return [];
    }
    
    return products.map(product => {
      // 处理图片 - 优先使用关联的图片数据
      let image = '';
      
      if (product.images && Array.isArray(product.images) && product.images.length > 0) {
        // 优先使用主图
        const mainImage = product.images.find(img => img.is_main === 1 || img.is_main === true);
        if (mainImage) {
          image = mainImage.url || mainImage.path || '';
        } else {
          // 如果没有主图，使用第一张图片
          const firstImage = product.images[0];
          image = firstImage.url || firstImage.path || '';
        }
      }
      
      // 如果关联图片为空，尝试其他可能的字段
      if (!image) {
        image = product.image_url || 
                product.image || 
                product.cover_image || 
                product.thumbnail || 
                product.photo || 
                product.picture || 
                product.img || 
                product.cover || '';
      }
      
      // 如果仍然没有图片，使用默认占位图
      if (!image) {
        // 不在这里设置占位图，让组件自己处理
        image = '';
      }
      
      return {
        ...product,
        // 标准化字段
        image: image,
        price: parseFloat(product.price || 0).toFixed(2),
        description: product.description || product.subtitle || product.summary || '',
        // 处理标签
        tags: Array.isArray(product.tags) ? product.tags : [],
        // 添加供应商信息
        supplier: product.supplier || product.brand || '自营',
        // 添加单位信息
        unit: product.unit || product.measure_unit || '件',
        // 库存状态现在由后端API提供，不再在前端计算
        // outOfStock: product.stock <= 0 || product.status === 'out_of_stock',
        // 添加促销状态
        isPromotion: product.is_promotion || product.promotion_price > 0
      };
    });
  }
  
  // ========== 用户相关 ==========
  
  /**
   * 微信登录
   */
  async login(code, userInfo) {
    console.log('📡 API调用: 微信登录');
    const result = await this.request.post(API.USER_LOGIN, {
      code,
      userInfo
    });
    return result.data || {};
  }
  
  /**
   * 获取用户信息
   */
  async getUserInfo() {
    console.log('📡 API调用: 获取用户信息');
    const result = await this.request.get(API.USER_INFO, {}, {
      cacheTime: CACHE_CONFIG.USER_PROFILE
    });
    return result.data || {};
  }
  
  /**
   * 更新用户信息
   */
  async updateUserInfo(userInfo) {
    console.log('📡 API调用: 更新用户信息');
    const result = await this.request.put(API.USER_INFO, userInfo);
    return result.data || {};
  }
  
  // ========== 购物车相关 ==========
  
  /**
   * 添加到购物车
   */
  async addToCart(product_id, quantity = 1, specifications = {}, unit_id = null) {
    console.log(`📡 API调用: 添加购物车 - 商品${product_id}`, { quantity, unit_id });
    const requestData = {
      product_id,
      quantity,
      specifications
    };
    
    // 如果有单位ID，添加到请求数据中
    if (unit_id) {
      requestData.unit_id = unit_id;
    }
    
    const result = await this.request.post(API.CART_ADD, requestData);
    return result.data || {};
  }
  
  /**
   * 获取购物车列表
   */
  async getCartList() {
    console.log('📡 API调用: 获取购物车列表');
    try {
    const result = await this.request.get(API.CART_LIST);
      console.log('📡 购物车API原始响应:', result);
      
      // 获取基础URL，用于处理图片路径
      const baseUrl = this.baseUrl.replace('/api', '');
      console.log('📡 基础URL:', baseUrl);
      
      // 标准化响应数据
      let cartData = [];
      
      // 处理不同的响应格式
      if (result && result.data) {
        // 情况1: data是数组
        if (Array.isArray(result.data)) {
          cartData = result.data;
        }
        // 情况2: data.items是数组
        else if (result.data.items && Array.isArray(result.data.items)) {
          cartData = result.data.items;
        }
        // 情况3: data.data是数组
        else if (result.data.data && Array.isArray(result.data.data)) {
          cartData = result.data.data;
        }
        // 情况4: data是对象，尝试转换为数组
        else if (typeof result.data === 'object') {
          // 尝试将对象转换为数组
          const possibleItems = Object.values(result.data).filter(item => 
            item && typeof item === 'object' && (item.id || item.product_id)
          );
          
          if (possibleItems.length > 0) {
            cartData = possibleItems;
          }
        }
      }
      
      // 确保每个购物车项都有必要的属性，特别是处理图片URL
      cartData = cartData.map(item => {
        // 处理图片URL - 优先使用cover_url（后端字段），然后是image_url，最后是image
        let imageUrl = item.cover_url || item.image_url || item.image || '';
        
        // 如果图片URL是相对路径，添加基础URL
        if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('/images/')) {
          // 如果以/开头，直接拼接基础URL
          if (imageUrl.startsWith('/')) {
            imageUrl = `${baseUrl}${imageUrl}`;
          } else {
            // 否则添加/前缀再拼接
            imageUrl = `${baseUrl}/${imageUrl}`;
          }
        }
        
        console.log(`📡 商品 ${item.id || item.product_id} 图片处理: ${item.cover_url || item.image_url || item.image} -> ${imageUrl}`);
        
    return {
          ...item,
          id: item.id || item.cart_id || item.product_id,
          product_id: item.product_id || item.id,
          name: item.name || item.product_name || '商品名称',
          image: imageUrl,
          price: parseFloat(item.price) || 0,
          quantity: parseInt(item.quantity) || 1,
          selected: typeof item.is_selected === 'boolean' ? item.is_selected : true
        };
      });
      
      console.log('📡 处理后的购物车数据:', {
        count: cartData.length,
        sample: cartData.length > 0 ? cartData[0] : null
      });
      
      return {
        data: cartData,
      meta: result.meta || {}
    };
    } catch (error) {
      console.error('📡 获取购物车列表失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取购物车数量
   */
  async getCartCount() {
    console.log('📡 API调用: 获取购物车数量');
    const result = await this.request.get(API.CART_COUNT, {}, {
      cacheTime: CACHE_CONFIG.CART_COUNT,
      showLoading: false
    });
    return result.data || 0;
  }
  
  /**
   * 更新购物车商品数量
   */
  async updateCartQuantity(cartId, quantity) {
    console.log(`📡 API调用: 更新购物车数量 - 购物车项${cartId}`);
    const url = API.CART_UPDATE.replace('{id}', cartId);
    const result = await this.request.put(url, {
      quantity
    });
    return result.data || {};
  }
  
  /**
   * 删除购物车商品
   */
  async removeFromCart(cartId) {
    console.log(`📡 API调用: 删除购物车商品 - 购物车项${cartId}`);
    const url = API.CART_REMOVE.replace('{id}', cartId);
    const result = await this.request.delete(url);
    return result.data || {};
  }
  
  /**
   * 清空购物车
   */
  async clearCart() {
    console.log('📡 API调用: 清空购物车');
    const result = await this.request.post(API_PATHS.CART.CLEAR);
    return result.data || {};
  }

  // ========== 订单相关 ==========
  
  /**
   * 创建订单
   */
  async createOrder(orderData) {
    console.log('📡 API调用: 创建订单');
    const result = await this.request.post(API.ORDER_CREATE, orderData);
    return result.data || {};
  }
  
  /**
   * 获取订单列表
   */
  async getOrderList(params = {}) {
    console.log('📡 API调用: 获取订单列表');
    const {
      status,
      page = 1,
      per_page = 20
    } = params;
    
    const result = await this.request.get(API.ORDER_LIST, {
      status,
      page,
      per_page
    });
    
    return {
      data: result.data || [],
      meta: result.meta || {}
    };
  }
  
  /**
   * 获取订单详情
   */
  async getOrderDetail(orderId) {
    console.log('📡 API调用: 获取订单详情', orderId);
    const result = await this.request.get(`${API.ORDERS}/${orderId}`);
    return result.data || {};
  }
  
  /**
   * 获取订单统计信息
   */
  async getOrderStats() {
    console.log('📡 API调用: 获取订单统计');
    try {
      const result = await this.request.get(API.ORDER_STATS, {}, {
        requireAuth: true,
        showLoading: false
      });
      return result.data || { total: 0, status_stats: {} };
    } catch (error) {
      console.error('❌ 获取订单统计失败:', error);
      return { total: 0, status_stats: {} };
    }
  }
  
  /**
   * 取消订单
   */
  async cancelOrder(orderId, reason = '') {
    console.log(`📡 API调用: 取消订单 - ${orderId}`);
    const url = API_PATHS.ORDER.CANCEL.replace('{id}', orderId);
    const result = await this.request.post(url, { reason });
    return result.data || {};
  }
  
  /**
   * 确认收货
   */
  async confirmOrder(orderId) {
    console.log(`📡 API调用: 确认收货 - ${orderId}`);
    const url = API_PATHS.ORDER.CONFIRM.replace('{id}', orderId);
    const result = await this.request.post(url);
    return result.data || {};
  }
  
  // ========== 地址相关 ==========
  
  /**
   * 获取地址列表
   */
  async getAddressList() {
    console.log('📡 API调用: 获取地址列表');
    const result = await this.request.get(API.ADDRESSES);
    return {
      data: result.data || [],
      meta: result.meta || {}
    };
  }
  
  /**
   * 添加地址
   */
  async addAddress(addressData) {
    console.log('📡 API调用: 添加地址');
    const result = await this.request.post(API.ADDRESSES, addressData);
    return result.data || {};
  }
  
  /**
   * 更新地址
   */
  async updateAddress(addressId, addressData) {
    console.log(`📡 API调用: 更新地址 - ${addressId}`);
    const result = await this.request.put(`${API.ADDRESSES}/${addressId}`, addressData);
    return result.data || {};
  }
  
  /**
   * 删除地址
   */
  async deleteAddress(addressId) {
    console.log(`📡 API调用: 删除地址 - ${addressId}`);
    const result = await this.request.delete(`${API.ADDRESSES}/${addressId}`);
    return result.data || {};
  }
  
  /**
   * 设置默认地址
   */
  async setDefaultAddress(addressId) {
    console.log(`📡 API调用: 设置默认地址 - ${addressId}`);
    const result = await this.request.post(`${API.ADDRESSES}/${addressId}/default`);
    return result.data || {};
  }
  
  // ========== 支付相关 ==========
  
  /**
   * 创建支付订单
   */
  async createPayment(paymentData) {
    console.log('📡 API调用: 创建支付订单');
    const result = await this.request.post(API.PAYMENT_CREATE, paymentData);
    return result.data || {};
  }
  
  /**
   * 查询支付状态
   */
  async queryPayment(paymentId) {
    console.log(`📡 API调用: 查询支付状态 - ${paymentId}`);
    const url = API_PATHS.PAYMENT.QUERY.replace('{id}', paymentId);
    const result = await this.request.get(url);
    return result.data || {};
  }
  
  // ========== 公共接口 ==========
  
  /**
   * 获取地区数据
   */
  async getRegions(parentId = 0) {
    console.log(`📡 API调用: 获取地区数据 - ${parentId}`);
    const result = await this.request.get(API_PATHS.PUBLIC.REGIONS, {
      parent_id: parentId
    }, {
      cacheTime: CACHE_CONFIG.CATEGORIES // 地区数据缓存时间较长
    });
    return {
      data: result.data || [],
      meta: result.meta || {}
    };
  }
  
  /**
   * 获取系统配置
   */
  async getConfig() {
    console.log('📡 API调用: 获取系统配置');
    const result = await this.request.get(API_PATHS.PUBLIC.CONFIG, {}, {
      cacheTime: CACHE_CONFIG.CATEGORIES
    });
    return result.data || {};
  }

  /**
   * 获取支付方式配置（从后端获取）
   */
  async getPaymentMethods() {
    console.log('📡 API调用: 获取支付方式配置');
    try {
      // 并行获取支付方式和支付优惠
      const [methodsResult, offersResult] = await Promise.all([
        this.request.get(API.PAYMENT_METHODS, {}, {
          cacheTime: CACHE_CONFIG.CATEGORIES,
          requireAuth: false,
          showLoading: false,
          showError: false
        }),
        this.request.get(API.PAYMENT_OFFERS, {}, {
          cacheTime: CACHE_CONFIG.CATEGORIES,
          requireAuth: false,
          showLoading: false,
          showError: false
        })
      ]);
      
      // 处理支付方式数据
      const paymentMethods = methodsResult?.data || {};
      const paymentOffers = offersResult?.data?.data || [];
      
      console.log('📡 获取到支付方式:', Object.keys(paymentMethods).length, '种');
      console.log('📡 获取到支付优惠:', paymentOffers.length, '个');
      
      // 构建支付方式配置
      const methods = [];
      
      // 处理在线支付
      if (paymentMethods.wechat) {
        const wechatOffer = paymentOffers.find(offer => 
          offer.payment_method === 'wechat' && offer.status && offer.is_valid
        );
        
        methods.push({
          value: 'online',
          name: '在线支付',
          icon: 'gold-coin-o',
          discount: wechatOffer ? this.calculateOfferAmount(wechatOffer, 100) : 5,
          discount_type: wechatOffer ? wechatOffer.offer_type : 'fixed',
          description: wechatOffer ? 
            `${wechatOffer.description}` : 
            '立减5元，更优惠',
          enabled: true,
          sort_order: 1,
          offer_info: wechatOffer
        });
      }
      
      // 处理货到付款
      if (paymentMethods.cod) {
        const codOffer = paymentOffers.find(offer => 
          offer.payment_method === 'cod' && offer.status && offer.is_valid
        );
        
        methods.push({
          value: 'cod',
          name: '货到付款',
          icon: 'cash-o',
          discount: codOffer ? this.calculateOfferAmount(codOffer, 100) : 0,
          discount_type: codOffer ? codOffer.offer_type : 'fixed',
          description: codOffer ? 
            `${codOffer.description}` : 
            '送货上门再付款',
          enabled: true,
          sort_order: 2,
          offer_info: codOffer
        });
      }
      
      if (methods.length > 0) {
        console.log('✅ 获取支付方式配置成功:', methods.length, '种支付方式');
        return methods.sort((a, b) => a.sort_order - b.sort_order);
      }
      
      console.log('⚠️ 后端未配置支付方式，使用默认配置');
      return this.getDefaultPaymentMethods();
      
    } catch (error) {
      console.error('❌ 获取支付方式配置失败:', error);
      console.log('⚠️ 使用默认支付方式配置');
      return this.getDefaultPaymentMethods();
    }
  }

  /**
   * 计算优惠金额（用于显示）
   */
  calculateOfferAmount(offer, testAmount = 100) {
    if (!offer || !offer.is_valid) return 0;
    
    if (testAmount < offer.min_amount) return 0;
    
    switch (offer.offer_type) {
      case 'fixed_amount':
        return Math.min(offer.offer_value, testAmount);
      case 'percentage':
        const percentOffer = testAmount * (offer.offer_value / 100);
        return offer.max_offer ? Math.min(percentOffer, offer.max_offer) : percentOffer;
      default:
        return 0;
    }
  }

  /**
   * 计算支付优惠（调用后端API）
   */
  async calculatePaymentOffer(paymentMethod, amount, enableStacking = true) {
    console.log('📡 API调用: 计算支付优惠', { paymentMethod, amount, enableStacking });
    try {
      const result = await this.request.post(API.PAYMENT_CALCULATE, {
        payment_method: paymentMethod === 'online' ? 'wechat' : paymentMethod,
        amount: amount,
        enable_stacking: enableStacking
      }, {
        requireAuth: false,
        showLoading: false,
        showError: false
      });
      
      if (result && result.data) {
        console.log('✅ 计算支付优惠成功:', result.data);
        return {
          offerAmount: result.data.offer_amount || 0,
          finalAmount: result.data.final_amount || amount,
          applicable: result.data.applicable || false,
          description: result.data.description || '',
          offers: result.data.offers || []
        };
      }
      
      console.log('⚠️ 支付优惠计算API返回格式异常');
      return { offerAmount: 0, finalAmount: amount, applicable: false };
      
    } catch (error) {
      console.error('❌ 计算支付优惠失败:', error);
      return { offerAmount: 0, finalAmount: amount, applicable: false };
    }
  }

  /**
   * 获取默认支付方式配置（兜底方案）
   */
  getDefaultPaymentMethods() {
    return [
      {
        value: 'online',
        name: '在线支付',
        icon: 'gold-coin-o',
        discount: 5,
        discount_type: 'fixed',
        description: '立减5元，更优惠',
        enabled: true,
        sort_order: 1
      },
      {
        value: 'cod',
        name: '货到付款',
        icon: 'cash-o',
        discount: 0,
        discount_type: 'fixed',
        description: '送货上门再付款',
        enabled: true,
        sort_order: 2
      }
    ];
  }
}

// 创建API服务实例
const apiService = new ApiService();

// 导出API服务和配置
module.exports = {
  api: apiService,
  CONFIG,
  API,
  getBaseUrl
}; 