<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('unit_properties')) {
            Schema::create('unit_properties', function (Blueprint $table) {
                $table->id();
                $table->foreignId('unit_id')->constrained()->onDelete('cascade');
                $table->string('key');
                $table->text('value')->nullable();
                $table->string('value_type')->default('string');
                $table->timestamps();
                
                // 确保单位-属性键的组合是唯一的
                $table->unique(['unit_id', 'key']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unit_properties');
    }
}; 