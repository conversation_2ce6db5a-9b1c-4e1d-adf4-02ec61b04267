<?php

namespace App\Printing\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class PrintRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'printable_type',
        'printable_id',
        'print_type',
        'driver',
        'printer_name',
        'status',
        'copies',
        'print_options',
        'print_content',
        'printed_at',
        'error_message',
        'printed_by'
    ];

    protected $casts = [
        'print_options' => 'array',
        'printed_at' => 'datetime',
        'copies' => 'integer'
    ];

    /**
     * 打印状态常量
     */
    const STATUS_PENDING = 'pending';      // 待打印
    const STATUS_PRINTING = 'printing';    // 打印中
    const STATUS_COMPLETED = 'completed';  // 已完成
    const STATUS_FAILED = 'failed';        // 失败


    /**
     * 打印类型常量
     */
    const TYPE_NORMAL = 'normal';          // 普通打印
    const TYPE_RECEIPT = 'receipt';        // 小票打印
    const TYPE_DELIVERY = 'delivery';      // 配送单打印

    /**
     * 打印驱动常量
     */
    const DRIVER_CLODOP = 'clodop';        // CLodop驱动
    const DRIVER_BROWSER = 'browser';      // 浏览器驱动

    /**
     * 获取所有打印状态
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_PENDING => '待打印',
            self::STATUS_PRINTING => '打印中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_FAILED => '失败'
        ];
    }

    /**
     * 获取所有打印类型
     */
    public static function getPrintTypes(): array
    {
        return [
            self::TYPE_NORMAL => '普通打印',
            self::TYPE_RECEIPT => '小票打印',
            self::TYPE_DELIVERY => '配送单打印'
        ];
    }

    /**
     * 获取所有打印驱动
     */
    public static function getDrivers(): array
    {
        return [
            self::DRIVER_CLODOP => 'CLodop打印',
            self::DRIVER_BROWSER => '浏览器打印'
        ];
    }

    /**
     * 多态关联 - 可打印对象
     */
    public function printable()
    {
        return $this->morphTo();
    }

    /**
     * 关联打印操作人
     */
    public function printedBy()
    {
        return $this->belongsTo(\App\Employee\Models\Employee::class, 'printed_by');
    }

    /**
     * 按状态筛选
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 按打印类型筛选
     */
    public function scopeByPrintType($query, string $printType)
    {
        return $query->where('print_type', $printType);
    }

    /**
     * 按驱动筛选
     */
    public function scopeByDriver($query, string $driver)
    {
        return $query->where('driver', $driver);
    }

    /**
     * 按可打印对象筛选
     */
    public function scopeByPrintable($query, string $type, int $id)
    {
        return $query->where('printable_type', $type)->where('printable_id', $id);
    }

    /**
     * 已完成的打印记录
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * 待打印的记录
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 失败的打印记录
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * 创建打印记录
     */
    public static function createRecord($printable, array $options = []): self
    {
        return static::create([
            'printable_type' => get_class($printable),
            'printable_id' => $printable->id,
            'print_type' => $options['print_type'] ?? self::TYPE_NORMAL,
            'driver' => $options['driver'] ?? self::DRIVER_CLODOP,
            'printer_name' => $options['printer_name'] ?? null,
            'status' => self::STATUS_PENDING,
            'copies' => $options['copies'] ?? 1,
            'print_options' => $options,
            'print_content' => $options['print_content'] ?? null,
            'printed_by' => $options['printed_by'] ?? auth()->id()
        ]);
    }

    /**
     * 标记为打印中
     */
    public function markAsPrinting(): bool
    {
        return $this->update([
            'status' => self::STATUS_PRINTING,
            'printed_at' => null
        ]);
    }

    /**
     * 标记为已完成
     */
    public function markAsCompleted(): bool
    {
        return $this->update([
            'status' => self::STATUS_COMPLETED,
            'printed_at' => now(),
            'printed_by' => auth()->id(),
            'error_message' => null
        ]);
    }

    /**
     * 标记为失败
     */
    public function markAsFailed(string $errorMessage = ''): bool
    {
        return $this->update([
            'status' => self::STATUS_FAILED,
            'error_message' => $errorMessage,
            'printed_at' => null
        ]);
    }

    /**
     * 检查对象是否已经打印过
     */
    public static function isPrinted($printable, string $printType = null): bool
    {
        $query = static::byPrintable(get_class($printable), $printable->id)
            ->completed();

        if ($printType) {
            $query->byPrintType($printType);
        }

        return $query->exists();
    }

    /**
     * 获取对象的打印历史
     */
    public static function getPrintHistory($printable, string $printType = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = static::byPrintable(get_class($printable), $printable->id)
            ->with('printedBy')
            ->orderBy('created_at', 'desc');

        if ($printType) {
            $query->byPrintType($printType);
        }

        return $query->get();
    }

    /**
     * 获取最近的打印记录
     */
    public static function getLastPrintRecord($printable, string $printType = null): ?self
    {
        $query = static::byPrintable(get_class($printable), $printable->id)
            ->orderBy('created_at', 'desc');

        if ($printType) {
            $query->byPrintType($printType);
        }

        return $query->first();
    }

    /**
     * 统计打印次数
     */
    public static function countPrints($printable, string $printType = null): int
    {
        $query = static::byPrintable(get_class($printable), $printable->id)
            ->completed();

        if ($printType) {
            $query->byPrintType($printType);
        }

        return $query->count();
    }
} 