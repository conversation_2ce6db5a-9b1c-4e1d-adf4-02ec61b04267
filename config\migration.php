<?php

return [
    
    /*
    |--------------------------------------------------------------------------
    | 老系统数据库连接配置
    |--------------------------------------------------------------------------
    */
    'old_system' => [
        'connection' => 'old_system_db',
        'host' => env('OLD_DB_HOST', 'localhost'),
        'port' => env('OLD_DB_PORT', '3306'),
        'database' => env('OLD_DB_DATABASE', 'old_system'),
        'username' => env('OLD_DB_USERNAME', 'root'),
        'password' => env('OLD_DB_PASSWORD', ''),
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
    ],

    /*
    |--------------------------------------------------------------------------
    | 商品数据迁移映射配置
    |--------------------------------------------------------------------------
    */
    'product_mapping' => [
        // 基础信息映射
        'basic_fields' => [
            'code' => 'product_code',           // 新系统字段 => 老系统字段
            'name' => 'product_name',
            'description' => 'product_desc',
            'price' => 'sell_price',
            'sale_price' => 'discount_price',
            'cost_price' => 'cost_price',
            'status' => [
                'field' => 'is_active',
                'mapping' => [1 => 1, 0 => 0, 'Y' => 1, 'N' => 0], // 值映射
            ],
            'sort' => 'display_order',
            'sales_count' => 'total_sales',
            'views_count' => 'view_count',
        ],

        // 分类映射（如果老系统有分类）
        'category_mapping' => [
            'table' => 'old_categories',
            'id_field' => 'cat_id',
            'name_field' => 'cat_name',
            'parent_field' => 'parent_id',
            'product_category_field' => 'category_id',
        ],

        // 库存映射
        'inventory_mapping' => [
            'table' => 'old_inventory', // 老系统库存表
            'product_field' => 'product_id',
            'stock_field' => 'quantity',
            'warehouse_field' => 'warehouse_id', // 如果有多仓库
            'warehouse_default' => 1, // 默认仓库ID
            
            // 商品匹配策略
            'match_strategy' => 'code', // code|custom_field
            'match_field' => 'product_code', // 老系统中的匹配字段
            'new_match_field' => 'code', // 新系统中的匹配字段
        ],

        // 单位映射
        'unit_mapping' => [
            'table' => 'old_units',
            'id_field' => 'unit_id',
            'name_field' => 'unit_name',
            'symbol_field' => 'unit_symbol',
            'product_unit_field' => 'unit_id',
            'default_unit_id' => 13, // 默认单位"件"的ID
        ],

        // 图片映射
        'image_mapping' => [
            'table' => 'old_product_images',
            'product_field' => 'product_id',
            'image_field' => 'image_url',
            'sort_field' => 'sort_order',
            'is_main_field' => 'is_main',
        ],

        // 需要跳过的老系统字段
        'skip_fields' => [
            'created_time', 'updated_time', 'deleted_at', 'temp_data'
        ],

        // 默认值设置
        'default_values' => [
            'track_inventory' => true,
            'inventory_policy' => 'strict',
            'min_sale_quantity' => 1,
            'inventory_type' => 'product',
            'auto_reorder' => false,
            'is_featured' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 迁移执行配置
    |--------------------------------------------------------------------------
    */
    'migration_settings' => [
        'batch_size' => 100,           // 每批次处理的记录数
        'max_execution_time' => 300,   // 最大执行时间（秒）
        'backup_before_migrate' => true, // 迁移前是否备份
        'validate_data' => true,       // 是否验证数据完整性
        'log_level' => 'info',         // 日志级别：debug, info, warning, error
        'continue_on_error' => false,  // 遇到错误是否继续
        'dry_run' => false,           // 是否为试运行模式
    ],

    /*
    |--------------------------------------------------------------------------
    | 数据验证规则
    |--------------------------------------------------------------------------
    */
    'validation_rules' => [
        'product' => [
            'code' => 'required|string|max:50',
            'name' => 'required|string|max:200',
            'price' => 'required|numeric|min:0',
            'status' => 'required|in:0,1',
        ],
        'inventory' => [
            'stock' => 'numeric|min:0',
            'warehouse_id' => 'exists:warehouses,id',
        ],
        'category' => [
            'name' => 'required|string|max:100',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 数据清理规则
    |--------------------------------------------------------------------------
    */
    'data_cleaning' => [
        'trim_strings' => true,        // 自动去除字符串首尾空格
        'empty_to_null' => true,       // 空字符串转为null
        'fix_encoding' => true,        // 修复编码问题
        'normalize_prices' => true,    // 标准化价格格式
        'generate_missing_codes' => true, // 为缺失的商品代码生成新代码
    ],
]; 