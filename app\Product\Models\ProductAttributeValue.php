<?php

namespace App\Product\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductAttributeValue extends Model
{
    use HasFactory;

    /**
     * 表名
     */
    protected $table = 'product_attribute_values';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'product_id',
        'attribute_id',
        'value',
        'value_json',
    ];

    /**
     * 类型转换
     */
    protected $casts = [
        'value_json' => 'array',
    ];

    /**
     * 获取所属商品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 获取所属属性
     */
    public function attribute()
    {
        return $this->belongsTo(ProductAttribute::class, 'attribute_id');
    }

    /**
     * 获取格式化后的属性值
     */
    public function getFormattedValueAttribute()
    {
        if (!$this->attribute) {
            return $this->value;
        }

        // 根据属性类型返回相应的值
        switch ($this->attribute->type) {
            case ProductAttribute::TYPE_MULTI_SELECT:
                return $this->value_json ?? $this->value;
            
            default:
                return $this->value;
        }
    }

    /**
     * 获取显示值
     */
    public function getDisplayValueAttribute()
    {
        if (!$this->attribute) {
            return $this->value;
        }

        return $this->attribute->formatValue($this->formatted_value);
    }

    /**
     * 设置属性值
     */
    public function setValue($value)
    {
        if (!$this->attribute) {
            $this->value = $value;
            return;
        }

        // 根据属性类型设置值
        switch ($this->attribute->type) {
            case ProductAttribute::TYPE_MULTI_SELECT:
                $this->value_json = is_array($value) ? $value : [$value];
                $this->value = is_array($value) ? implode(',', $value) : $value;
                break;
            
            case ProductAttribute::TYPE_BOOLEAN:
                $this->value = $value ? '1' : '0';
                break;
            
            default:
                $this->value = (string) $value;
                break;
        }
    }

    /**
     * 验证属性值
     */
    public function validateValue()
    {
        if (!$this->attribute) {
            return true;
        }

        return $this->attribute->validateValue($this->formatted_value);
    }

    /**
     * 作用域：根据属性ID筛选
     */
    public function scopeByAttribute($query, $attributeId)
    {
        return $query->where('attribute_id', $attributeId);
    }

    /**
     * 作用域：根据商品ID筛选
     */
    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * 作用域：根据属性值筛选
     */
    public function scopeByValue($query, $value)
    {
        return $query->where('value', $value);
    }
} 