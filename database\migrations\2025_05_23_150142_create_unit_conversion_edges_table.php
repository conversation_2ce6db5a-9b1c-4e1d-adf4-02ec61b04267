<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('unit_conversion_edges', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('graph_id')->comment('转换图ID');
            $table->unsignedBigInteger('from_unit_id')->comment('源单位ID');
            $table->unsignedBigInteger('to_unit_id')->comment('目标单位ID');
            $table->decimal('conversion_factor', 20, 10)->comment('转换系数');
            $table->boolean('is_bidirectional')->default(false)->comment('是否双向');
            $table->timestamps();
            
            $table->unique(['graph_id', 'from_unit_id', 'to_unit_id']);
            $table->index('from_unit_id');
            $table->index('to_unit_id');
            
            $table->foreign('graph_id')->references('id')->on('unit_conversion_graphs')->onDelete('cascade');
            $table->foreign('from_unit_id')->references('id')->on('units')->onDelete('cascade');
            $table->foreign('to_unit_id')->references('id')->on('units')->onDelete('cascade');
        });
        
        // 迁移现有的单位转换关系到新表
        $this->migrateExistingConversions();
    }

    /**
     * 迁移现有的单位转换关系
     */
    private function migrateExistingConversions()
    {
        // 检查unit_conversions表是否存在
        if (Schema::hasTable('unit_conversions')) {
            // 获取默认转换图
            $defaultGraphs = DB::table('unit_conversion_graphs')
                               ->where('is_default', true)
                               ->get()
                               ->keyBy('type');
                               
            // 获取所有单位的类型映射
            $unitTypes = DB::table('units')
                           ->select('id', 'type')
                           ->get()
                           ->keyBy('id');
            
            // 迁移数据
            $conversions = DB::table('unit_conversions')->get();
            
            foreach ($conversions as $conversion) {
                $fromUnitId = $conversion->from_unit_id;
                $toUnitId = $conversion->to_unit_id;
                
                // 获取单位类型
                $fromType = $unitTypes[$fromUnitId]->type ?? null;
                
                // 如果找不到类型，跳过
                if (!$fromType || !isset($defaultGraphs[$fromType])) {
                    continue;
                }
                
                $graphId = $defaultGraphs[$fromType]->id;
                
                // 插入新记录
                DB::table('unit_conversion_edges')->insert([
                    'graph_id' => $graphId,
                    'from_unit_id' => $fromUnitId,
                    'to_unit_id' => $toUnitId,
                    'conversion_factor' => $conversion->conversion_factor,
                    'is_bidirectional' => false, // 默认为单向
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unit_conversion_edges');
    }
};
