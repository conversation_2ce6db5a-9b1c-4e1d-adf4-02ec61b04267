<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 将旧角色字段数据合并到新的roles字段
        $this->migrateOldRolesToJson();
        
        Schema::table('product_units', function (Blueprint $table) {
            // 新增默认字段
            if (!Schema::hasColumn('product_units', 'is_default')) {
                $table->boolean('is_default')->default(false)->comment('是否为默认单位');
            }
            
            // 尝试删除重复的索引
            try {
                if (Schema::hasIndex('product_units', 'idx_product_unit_unique')) {
                    $table->dropIndex('idx_product_unit_unique');
                }
            } catch (\Exception $e) {
                // 忽略错误
            }
            
            // 删除旧的角色字段
            $table->dropColumn([
                'is_purchase_unit',
                'is_sale_unit',
                'is_inventory_unit'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_units', function (Blueprint $table) {
            // 恢复旧的角色字段
            $table->boolean('is_purchase_unit')->default(false)->comment('是否为采购单位');
            $table->boolean('is_sale_unit')->default(false)->comment('是否为销售单位');
            $table->boolean('is_inventory_unit')->default(false)->comment('是否为库存单位');
            
            // 移除新增字段
            if (Schema::hasColumn('product_units', 'is_default')) {
                $table->dropColumn('is_default');
            }
        });
    }
    
    /**
     * 将旧角色字段数据迁移到roles字段
     */
    private function migrateOldRolesToJson()
    {
        $productUnits = DB::table('product_units')->get();
        
        foreach ($productUnits as $unit) {
            $roles = [];
            $rolePriority = [];
            
            // 检查并添加销售角色
            if ($unit->is_sale_unit) {
                $roles[] = 'sale';
                $rolePriority['sale'] = 10;
            }
            
            // 检查并添加采购角色
            if ($unit->is_purchase_unit) {
                $roles[] = 'purchase';
                $rolePriority['purchase'] = 20;
            }
            
            // 检查并添加库存角色
            if ($unit->is_inventory_unit) {
                $roles[] = 'inventory';
                $rolePriority['inventory'] = 30;
            }
            
            // 只更新尚未设置roles的记录
            if (!empty($roles) && (!$unit->roles || $unit->roles === 'null')) {
                DB::table('product_units')
                    ->where('id', $unit->id)
                    ->update([
                        'roles' => json_encode($roles),
                        'role_priority' => json_encode($rolePriority)
                    ]);
            }
        }
    }
};
