<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';

// 启动应用
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// 确保数据库连接正常
try {
    \Illuminate\Support\Facades\DB::connection()->getPdo();
    echo "✅ 数据库连接正常\n";
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 模拟请求
$request = Illuminate\Http\Request::create('/api/inventory/products/1?for_edit=true', 'GET');
$request->headers->set('Accept', 'application/json');

try {
    // 直接调用控制器方法
    $controller = new App\Inventory\Http\Controllers\InventoryProductController();
    $mockRequest = new Illuminate\Http\Request();
    $mockRequest->query->set('for_edit', 'true');
    
    echo "🧪 测试库存商品API (for_edit=true)\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    // 先检查商品是否存在
    $product = App\Product\Models\Product::first();
    if (!$product) {
        echo "❌ 没有找到商品数据\n";
        exit(1);
    }
    
    echo "✅ 找到商品: ID={$product->id}, Name={$product->name}\n";
    echo "📦 基本单位ID: {$product->base_unit_id}\n";
    
    // 测试销售单位获取
    $saleUnit = $product->getSaleDefaultUnit();
    echo "💰 销售单位: " . ($saleUnit ? "{$saleUnit->name} (ID: {$saleUnit->id})" : "无") . "\n";
    
    // 测试采购单位获取  
    $purchaseUnit = $product->getPurchaseDefaultUnit();
    echo "🛒 采购单位: " . ($purchaseUnit ? "{$purchaseUnit->name} (ID: {$purchaseUnit->id})" : "无") . "\n";
    
    // 测试API调用
    echo "\n🔌 调用API...\n";
    $response = $controller->show($mockRequest, $product->id);
    
    echo "📊 响应状态码: " . $response->getStatusCode() . "\n";
    
    $responseData = json_decode($response->getContent(), true);
    
    if ($response->getStatusCode() === 200) {
        echo "✅ API调用成功!\n";
        echo "📋 响应键: " . implode(', ', array_keys($responseData)) . "\n";
        
        if (isset($responseData['data'])) {
            $data = $responseData['data'];
            echo "🎯 商品数据键: " . implode(', ', array_keys($data)) . "\n";
            echo "📝 商品名称: " . ($data['name'] ?? 'N/A') . "\n";
            echo "🆔 商品ID: " . ($data['id'] ?? 'N/A') . "\n";
            echo "🔢 销售单位ID: " . ($data['sale_unit_id'] ?? 'null') . "\n";
            echo "🔢 采购单位ID: " . ($data['purchase_unit_id'] ?? 'null') . "\n";
            echo "⚖️ 销售转换系数: " . ($data['sale_conversion_factor'] ?? 'N/A') . "\n";
            echo "⚖️ 采购转换系数: " . ($data['purchase_conversion_factor'] ?? 'N/A') . "\n";
        }
    } else {
        echo "❌ API调用失败!\n";
        echo "🚨 错误信息: " . ($responseData['message'] ?? '未知错误') . "\n";
        
        if (isset($responseData['data'])) {
            echo "🔍 错误详情: " . print_r($responseData['data'], true) . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "💥 异常: " . $e->getMessage() . "\n";
    echo "📍 文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "🔍 堆栈:\n" . $e->getTraceAsString() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "测试完成\n"; 