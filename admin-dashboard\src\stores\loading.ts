import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useLoadingStore = defineStore('loading', () => {
  // 全局加载状态
  const globalLoading = ref(false)
  
  // 页面级加载状态
  const pageLoading = ref(false)
  
  // 组件级加载状态
  const componentLoading = ref<Record<string, boolean>>({})
  
  // 设置全局加载
  const setGlobalLoading = (loading: boolean) => {
    globalLoading.value = loading
  }
  
  // 设置页面加载
  const setPageLoading = (loading: boolean) => {
    pageLoading.value = loading
  }
  
  // 设置组件加载
  const setComponentLoading = (key: string, loading: boolean) => {
    componentLoading.value[key] = loading
  }
  
  // 获取组件加载状态
  const getComponentLoading = (key: string) => {
    return componentLoading.value[key] || false
  }
  
  // 显示加载（带自动隐藏）
  const showLoading = async (fn: () => Promise<any>, type: 'global' | 'page' | string = 'page') => {
    try {
      if (type === 'global') {
        setGlobalLoading(true)
      } else if (type === 'page') {
        setPageLoading(true)
      } else {
        setComponentLoading(type, true)
      }
      
      const result = await fn()
      return result
    } finally {
      if (type === 'global') {
        setGlobalLoading(false)
      } else if (type === 'page') {
        setPageLoading(false)
      } else {
        setComponentLoading(type, false)
      }
    }
  }
  
  return {
    globalLoading,
    pageLoading,
    componentLoading,
    setGlobalLoading,
    setPageLoading,
    setComponentLoading,
    getComponentLoading,
    showLoading
  }
}) 