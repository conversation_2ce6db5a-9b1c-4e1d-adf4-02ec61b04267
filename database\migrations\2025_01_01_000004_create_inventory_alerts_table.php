<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_alerts', function (Blueprint $table) {
            $table->id();
            
            // 关联信息
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('warehouse_id')->nullable()->constrained()->onDelete('cascade');
            
            // 预警类型
            $table->enum('alert_type', [
                'low_stock',        // 低库存预警
                'negative_stock',   // 负库存预警
                'out_of_stock',     // 缺货预警
                'overstock',        // 超库存预警
                'reorder_point',    // 补货点预警
                'expiry_warning',   // 过期预警
                'custom'            // 自定义预警
            ])->comment('预警类型');
            
            // 预警级别
            $table->enum('severity', ['low', 'medium', 'high', 'critical'])->default('medium')->comment('预警级别');
            
            // 预警状态
            $table->enum('status', ['active', 'acknowledged', 'resolved', 'ignored'])->default('active')->comment('预警状态');
            
            // 预警内容
            $table->string('title')->comment('预警标题');
            $table->text('message')->comment('预警消息');
            $table->json('alert_data')->nullable()->comment('预警相关数据');
            
            // 库存信息
            $table->decimal('current_stock', 10, 2)->comment('当前库存');
            $table->decimal('threshold_value', 10, 2)->nullable()->comment('阈值');
            $table->decimal('previous_stock', 10, 2)->nullable()->comment('之前库存');
            
            // 处理信息
            $table->timestamp('acknowledged_at')->nullable()->comment('确认时间');
            $table->foreignId('acknowledged_by')->nullable()->constrained('employees')->comment('确认人');
            $table->timestamp('resolved_at')->nullable()->comment('解决时间');
            $table->foreignId('resolved_by')->nullable()->constrained('employees')->comment('解决人');
            $table->text('resolution_notes')->nullable()->comment('解决备注');
            
            // 通知设置
            $table->json('notification_channels')->nullable()->comment('通知渠道：email,sms,system');
            $table->json('notification_recipients')->nullable()->comment('通知接收人');
            $table->timestamp('last_notified_at')->nullable()->comment('最后通知时间');
            $table->integer('notification_count')->default(0)->comment('通知次数');
            
            // 自动处理
            $table->boolean('auto_resolve')->default(false)->comment('是否自动解决');
            $table->timestamp('expires_at')->nullable()->comment('预警过期时间');
            
            $table->timestamps();
            
            // 索引
            $table->index(['product_id', 'warehouse_id']);
            $table->index(['alert_type', 'status']);
            $table->index(['severity', 'status']);
            $table->index(['status', 'created_at']);
            $table->index('expires_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_alerts');
    }
}; 