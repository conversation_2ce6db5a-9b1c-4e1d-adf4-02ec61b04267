# API修复验证指南

## 问题描述
之前遇到的错误：`TypeError: request is not a function`

## 问题原因
在 `api/client.js` 中，错误地使用了 `request()` 函数调用方式，而实际上 `request` 是一个对象，应该使用 `request.get()`、`request.post()` 等方法。

## 修复内容

### 修复前（错误的调用方式）
```javascript
// ❌ 错误
return request({
    url: '/users',
    method: 'GET',
    data: params
})
```

### 修复后（正确的调用方式）
```javascript
// ✅ 正确
return request.get('/users', params)
```

## 验证步骤

### 1. 检查客户列表页面
1. 启动UniApp项目
2. 登录系统
3. 进入客户管理页面（底部导航栏"客户"）
4. 观察控制台日志，应该看到：
   ```
   发起请求: {url: "http://xxx/api/users", method: "GET", ...}
   ```
5. 不应该再看到 `TypeError: request is not a function` 错误

### 2. 检查代客下单页面
1. 进入代客下单页面
2. 点击"选择客户"
3. 观察客户列表是否正常加载
4. 控制台应该显示正常的API请求日志

### 3. 验证API响应
根据后端返回的数据格式，应该看到类似的响应：
```json
{
  "data": [...],
  "total": 100,
  "current_page": 1,
  "per_page": 20
}
```

## 可能遇到的新问题

### 1. 404错误 - 路由不存在
如果看到404错误，说明后端路由可能不存在，需要检查：
```bash
php artisan route:list --path=api/users
```

### 2. 401错误 - 未授权
如果看到401错误，说明Token问题：
- 检查是否已登录
- 检查Token是否正确存储
- 尝试重新登录

### 3. 403错误 - 权限不足
如果看到403错误，说明权限问题：
- 检查当前员工的角色
- 确认该角色是否有访问客户列表的权限

### 4. 空数据
如果API调用成功但返回空数据：
- 检查数据库中是否有客户数据
- 检查当前员工是否有分配的客户（如果是CRM专员）
- 查看后端日志确认权限过滤逻辑

## 调试技巧

### 1. 查看详细请求日志
在 `utils/request.js` 中已经添加了详细日志：
```javascript
console.log('发起请求:', {
    url: options.url,
    method: options.method,
    data: options.data,
    header: options.header
})
```

### 2. 检查网络请求
在浏览器开发者工具的Network标签中查看实际的HTTP请求。

### 3. 验证Token
检查请求头中是否包含正确的Authorization：
```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
```

## 成功标志

修复成功后，您应该看到：
1. ✅ 不再有 `request is not a function` 错误
2. ✅ 控制台显示正常的API请求日志
3. ✅ 客户列表页面能正常加载数据（或显示权限相关的错误信息）
4. ✅ 代客下单页面的客户选择功能正常

## 下一步

如果API调用正常但遇到权限或数据问题，请参考：
- `CRM权限边界说明.md` - 了解权限机制
- `API接口测试指南.md` - 详细的测试步骤

## 联系支持

如果仍有问题，请提供：
1. 控制台完整的错误日志
2. 网络请求的详细信息
3. 当前登录员工的角色信息 