<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * 阶段3：优化单位字段冗余
     * - 迁移display_unit_id数据到product_units表
     * - 删除冗余的单位相关字段
     */
    public function up(): void
    {
        echo "=== 阶段3：优化单位字段 ===\n";
        
        // 1. 迁移display_unit_id数据
        $this->migrateDisplayUnitData();
        
        // 2. 删除冗余的单位字段
        $this->removeRedundantUnitFields();
        
        // 3. 确保base_unit_id不为空
        $this->enforceBaseUnitConstraint();
        
        echo "=== 阶段3完成 ===\n";
    }
    
    /**
     * 迁移display_unit_id数据到product_units表
     */
    private function migrateDisplayUnitData(): void
    {
        echo "迁移display_unit_id数据...\n";
        
        // 先清理无效的display_unit_id（指向不存在的单位）
        $this->cleanupInvalidDisplayUnits();
        
        // 检查display_unit_id字段是否存在
        if (!Schema::hasColumn('products', 'display_unit_id')) {
            echo "✅ display_unit_id字段不存在，跳过迁移\n";
            return;
        }
        
        // 获取有display_unit_id但与base_unit_id不同的商品
        $productsWithDisplayUnit = DB::table('products')
            ->whereNotNull('display_unit_id')
            ->whereColumn('display_unit_id', '!=', 'base_unit_id')
            ->get(['id', 'display_unit_id', 'base_unit_id']);
            
        if ($productsWithDisplayUnit->count() > 0) {
            echo "发现 {$productsWithDisplayUnit->count()} 个商品有独立的显示单位\n";
            
            foreach ($productsWithDisplayUnit as $product) {
                // 检查是否已存在相应的product_units记录
                $existingRecord = DB::table('product_units')
                    ->where('product_id', $product->id)
                    ->where('unit_id', $product->display_unit_id)
                    ->first();
                    
                if (!$existingRecord) {
                    // 获取显示单位与基础单位的转换关系
                    $conversionFactor = $this->calculateConversionFactor(
                        $product->base_unit_id, 
                        $product->display_unit_id
                    );
                    
                    // 创建显示单位的product_units记录
                    DB::table('product_units')->insert([
                        'product_id' => $product->id,
                        'unit_id' => $product->display_unit_id,
                        'conversion_factor' => $conversionFactor,
                        'roles' => json_encode(['sales']), // 显示单位通常用于销售
                        'role_priority' => json_encode(['sales' => 1]),
                        'is_default' => false,
                        'is_active' => true,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                    
                    echo "  ✅ 商品ID {$product->id}: 创建显示单位关系\n";
                } else {
                    // 更新现有记录，确保包含sales角色
                    $roles = json_decode($existingRecord->roles, true) ?: [];
                    if (!in_array('sales', $roles)) {
                        $roles[] = 'sales';
                        $rolePriority = json_decode($existingRecord->role_priority, true) ?: [];
                        $rolePriority['sales'] = 1;
                        
                        DB::table('product_units')
                            ->where('id', $existingRecord->id)
                            ->update([
                                'roles' => json_encode($roles),
                                'role_priority' => json_encode($rolePriority)
                            ]);
                    }
                    echo "  ✅ 商品ID {$product->id}: 更新现有单位关系\n";
                }
            }
        } else {
            echo "✅ 没有需要迁移的display_unit_id数据\n";
        }
    }
    
    /**
     * 计算单位转换系数
     */
    private function calculateConversionFactor($baseUnitId, $displayUnitId): float
    {
        // 这里应该根据实际的单位转换逻辑来计算
        // 暂时返回1.0，实际项目中应该从单位转换表或配置中获取
        
        // 简单的类型判断
        $baseUnit = DB::table('units')->find($baseUnitId);
        $displayUnit = DB::table('units')->find($displayUnitId);
        
        if ($baseUnit && $displayUnit) {
            // 如果是相同类别的单位，尝试根据名称推断转换关系
            if ($baseUnit->category === $displayUnit->category) {
                // 这里可以添加具体的转换逻辑
                // 例如：kg to g = 1000, m to cm = 100 等
                return 1.0; // 默认返回1:1
            }
        }
        
        return 1.0; // 默认转换系数
    }
    
    /**
     * 删除冗余的单位字段
     */
    private function removeRedundantUnitFields(): void
    {
        echo "删除冗余的单位字段...\n";
        
        Schema::table('products', function (Blueprint $table) {
            // 删除display_unit_id字段
            if (Schema::hasColumn('products', 'display_unit_id')) {
                $table->dropForeign(['display_unit_id']);
                $table->dropColumn('display_unit_id');
                echo "✅ 删除display_unit_id字段\n";
            }
            
            // 删除unit_conversion_graph_id字段
            if (Schema::hasColumn('products', 'unit_conversion_graph_id')) {
                $table->dropForeign(['unit_conversion_graph_id']);
                $table->dropColumn('unit_conversion_graph_id');
                echo "✅ 删除unit_conversion_graph_id字段\n";
            }
            
            // 删除unit_settings JSON字段
            if (Schema::hasColumn('products', 'unit_settings')) {
                $table->dropColumn('unit_settings');
                echo "✅ 删除unit_settings字段\n";
            }
            
            // 删除multi_unit_enabled字段
            if (Schema::hasColumn('products', 'multi_unit_enabled')) {
                $table->dropColumn('multi_unit_enabled');
                echo "✅ 删除multi_unit_enabled字段\n";
            }
        });
    }
    
    /**
     * 确保base_unit_id不为空
     */
    private function enforceBaseUnitConstraint(): void
    {
        echo "强化base_unit_id约束...\n";
        
        // 检查是否还有空的base_unit_id
        $nullCount = DB::table('products')->whereNull('base_unit_id')->count();
        
        if ($nullCount > 0) {
            echo "❌ 警告：仍有 {$nullCount} 个商品的base_unit_id为空\n";
            echo "请先运行阶段2迁移修复数据完整性\n";
            return;
        }
        
        // 先删除现有的外键约束，然后重新创建
        try {
            Schema::table('products', function (Blueprint $table) {
                $table->dropForeign(['base_unit_id']);
            });
            echo "✅ 删除现有外键约束\n";
        } catch (Exception $e) {
            echo "⚠️ 外键约束不存在，跳过删除\n";
        }
        
        // 将base_unit_id设为NOT NULL
        Schema::table('products', function (Blueprint $table) {
            $table->bigInteger('base_unit_id')->unsigned()->nullable(false)->change();
        });
        
        // 重新创建外键约束，使用RESTRICT而不是SET NULL
        Schema::table('products', function (Blueprint $table) {
            $table->foreign('base_unit_id')->references('id')->on('units')->onDelete('restrict');
        });
        
        echo "✅ base_unit_id字段设为必填，并重新创建外键约束\n";
    }
    
    /**
     * 清理无效的display_unit_id
     */
    private function cleanupInvalidDisplayUnits(): void
    {
        echo "清理无效的display_unit_id...\n";
        
        // 检查display_unit_id字段是否存在
        if (!Schema::hasColumn('products', 'display_unit_id')) {
            echo "✅ display_unit_id字段不存在，跳过清理\n";
            return;
        }
        
        // 查找指向不存在单位的display_unit_id
        $invalidDisplayUnits = DB::table('products as p')
            ->leftJoin('units as u', 'p.display_unit_id', '=', 'u.id')
            ->whereNotNull('p.display_unit_id')
            ->whereNull('u.id')
            ->pluck('p.id');
            
        if ($invalidDisplayUnits->count() > 0) {
            echo "发现 {$invalidDisplayUnits->count()} 个商品有无效的display_unit_id\n";
            
            // 将无效的display_unit_id设为null
            $updated = DB::table('products')
                ->whereIn('id', $invalidDisplayUnits)
                ->update(['display_unit_id' => null]);
                
            echo "✅ 清理了 {$updated} 个无效的display_unit_id\n";
        } else {
            echo "✅ 没有发现无效的display_unit_id\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复字段
        Schema::table('products', function (Blueprint $table) {
            // 恢复display_unit_id
            $table->bigInteger('display_unit_id')->unsigned()->nullable()->after('base_unit_id');
            $table->foreign('display_unit_id')->references('id')->on('units')->onDelete('set null');
            
            // 恢复unit_conversion_graph_id
            $table->bigInteger('unit_conversion_graph_id')->unsigned()->nullable()->after('display_unit_id');
            $table->foreign('unit_conversion_graph_id')->references('id')->on('unit_conversion_graphs')->onDelete('set null');
            
            // 恢复unit_settings
            $table->json('unit_settings')->nullable()->after('unit_conversion_graph_id');
            
            // 恢复multi_unit_enabled
            $table->boolean('multi_unit_enabled')->default(true)->after('unit_settings');
            
            // base_unit_id改回可空
            $table->bigInteger('base_unit_id')->unsigned()->nullable()->change();
        });
    }
}; 