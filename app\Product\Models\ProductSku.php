<?php

namespace App\Product\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductSku extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'product_id',
        'name',
        'price',
        'stock',
        'code',
    ];
    
    /**
     * SKU所属的商品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
} 