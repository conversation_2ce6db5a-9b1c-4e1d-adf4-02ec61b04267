<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory', function (Blueprint $table) {
            $table->id();
            $table->foreignId('warehouse_id')->constrained()->onDelete('cascade')->comment('仓库ID');
            $table->foreignId('product_id')->constrained()->onDelete('cascade')->comment('商品ID');
            $table->enum('unit', ['kg', 'pcs', 'g', 'lb'])->default('pcs')->comment('存储的单位（如：公斤，件，克，磅）');
            $table->decimal('stock', 10, 2)->default(0)->comment('商品在此仓库的库存量');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory');
    }
}; 