// 存储工具类
export class Storage {
  private static readonly TOKEN_KEY = 'tianxin_token'
  private static readonly USER_KEY = 'tianxin_user'
  private static readonly REFRESH_TOKEN_KEY = 'tianxin_refresh_token'
  private static readonly TOKEN_EXPIRE_KEY = 'tianxin_token_expire'

  // 设置token
  static setToken(token: string, expiresIn: number = 7200) {
    const expireTime = Date.now() + expiresIn * 1000
    localStorage.setItem(this.TOKEN_KEY, token)
    localStorage.setItem(this.TOKEN_EXPIRE_KEY, expireTime.toString())
  }

  // 获取token
  static getToken(): string | null {
    const token = localStorage.getItem(this.TOKEN_KEY)
    const expireTime = localStorage.getItem(this.TOKEN_EXPIRE_KEY)
    
    if (!token || !expireTime) {
      return null
    }
    
    // 检查token是否过期
    if (Date.now() > parseInt(expireTime)) {
      this.clearToken()
      return null
    }
    
    return token
  }

  // 检查token是否即将过期（30分钟内）
  static isTokenExpiringSoon(): boolean {
    const expireTime = localStorage.getItem(this.TOKEN_EXPIRE_KEY)
    if (!expireTime) return false
    
    const thirtyMinutes = 30 * 60 * 1000
    return Date.now() > (parseInt(expireTime) - thirtyMinutes)
  }

  // 设置刷新token
  static setRefreshToken(refreshToken: string) {
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken)
  }

  // 获取刷新token
  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY)
  }

  // 设置用户信息
  static setUser(user: any) {
    localStorage.setItem(this.USER_KEY, JSON.stringify(user))
  }

  // 获取用户信息
  static getUser(): any | null {
    const user = localStorage.getItem(this.USER_KEY)
    return user ? JSON.parse(user) : null
  }

  // 清除token
  static clearToken() {
    localStorage.removeItem(this.TOKEN_KEY)
    localStorage.removeItem(this.TOKEN_EXPIRE_KEY)
    localStorage.removeItem(this.REFRESH_TOKEN_KEY)
  }

  // 清除用户信息
  static clearUser() {
    localStorage.removeItem(this.USER_KEY)
  }

  // 清除所有认证信息
  static clearAuth() {
    this.clearToken()
    this.clearUser()
  }

  // 获取token剩余时间（秒）
  static getTokenRemainingTime(): number {
    const expireTime = localStorage.getItem(this.TOKEN_EXPIRE_KEY)
    if (!expireTime) return 0
    
    const remaining = parseInt(expireTime) - Date.now()
    return Math.max(0, Math.floor(remaining / 1000))
  }
} 