<?php

use Illuminate\Support\Facades\Route;
use App\Delivery\Http\Controllers\DeliveryController;
use App\Delivery\Http\Controllers\DelivererController;
use App\Delivery\Http\Controllers\DeliveryRouteController;
use App\Delivery\Http\Controllers\DeliveryStatisticsController;

/*
|--------------------------------------------------------------------------
| Delivery Module API Routes
|--------------------------------------------------------------------------
|
| 这里是所有配送模块的API路由定义
|
*/

// 配送员相关路由
Route::prefix('deliverers')->group(function () {
    // 配送员专用登录接口（不需要认证）
    Route::post('/login', [DelivererController::class, 'login']);
    
    // 需要认证的配送员路由
    Route::middleware(['auth:sanctum'])->group(function () {
        // 配送员仪表板
        Route::get('/dashboard', [DelivererController::class, 'dashboard']);
        
        // 配送员统计数据
        Route::get('/statistics', [DelivererController::class, 'statistics']);
        
        // 更新配送员位置
        Route::post('/update-location', [DelivererController::class, 'updateLocation']);
    });
    
    // 管理员权限的配送员管理路由
    Route::middleware(['auth:sanctum', 'employee.role:admin,manager'])->group(function () {
        // 获取配送员列表
        Route::get('/', [DelivererController::class, 'index']);
        
        // 创建配送员
        Route::post('/', [DelivererController::class, 'store']);
        
        // 获取配送员详情
        Route::get('/{id}', [DelivererController::class, 'show']);
        
        // 更新配送员
        Route::put('/{id}', [DelivererController::class, 'update']);
        
        // 删除配送员
        Route::delete('/{id}', [DelivererController::class, 'destroy']);
    });
});

// 兼容旧的配送员登录路由（单数形式deliverer）
Route::prefix('deliverer')->group(function () {
    Route::post('/login', [DelivererController::class, 'login'])->withoutMiddleware(['auth:sanctum']);
});

// 配送相关路由
Route::prefix('deliveries')->group(function () {
    // 获取配送列表
    Route::get('/', [DeliveryController::class, 'index']);
    
    // 获取配送详情
    Route::get('/{id}', [DeliveryController::class, 'show']);
    
    // 订单分配配送员 - 统一为一个路由
    Route::post('/order/{order_id}/assign', [DeliveryController::class, 'assignDeliverer']);
    
    // 更新配送状态
    Route::put('/{id}/status', [DeliveryController::class, 'updateStatus']);
});

// 配送路线管理路由
Route::prefix('routes')->group(function () {
    // 获取配送路线列表
    Route::get('/', [DeliveryRouteController::class, 'index']);
    
    // 创建配送路线
    Route::post('/', [DeliveryRouteController::class, 'store']);
    
    // 获取配送路线详情
    Route::get('/{id}', [DeliveryRouteController::class, 'show']);
    
    // 更新配送路线
    Route::put('/{id}', [DeliveryRouteController::class, 'update']);
    
    // 删除配送路线
    Route::delete('/{id}', [DeliveryRouteController::class, 'destroy']);
    
    // 分配配送员到路线
    Route::post('/{id}/assign-deliverers', [DeliveryRouteController::class, 'assignDeliverers']);
});

// 统计数据API路由
Route::prefix('statistics')->group(function () {
    // 获取配送概览统计
    Route::get('/overview', [DeliveryStatisticsController::class, 'overview']);
    
    // 获取订单趋势数据
    Route::get('/order-trend', [DeliveryStatisticsController::class, 'orderTrend']);
});

// 需要认证的配送路由
Route::middleware('auth:sanctum')->group(function () {
    // 配送管理路由
    Route::prefix('deliveries')->middleware(['employee.role:admin,manager,delivery'])->group(function () {
        Route::get('/', [DeliveryController::class, 'index']);
        Route::get('/{id}', [DeliveryController::class, 'show']);
        Route::post('/orders/{orderId}/assign', [DeliveryController::class, 'assignDeliverer']);
        Route::put('/{id}/status', [DeliveryController::class, 'updateStatus']);
        
        // 为现有配送记录分配配送员
        Route::put('/{id}/assign-deliverer', [DeliveryController::class, 'assignDelivererToDelivery']);
    });
});

// 公共配送信息（不需要认证）
Route::prefix('public')->group(function () {
    Route::get('/delivery/methods', [DeliveryController::class, 'publicMethods']);
    Route::post('/delivery/fee', [DeliveryController::class, 'calculateFee']);
}); 