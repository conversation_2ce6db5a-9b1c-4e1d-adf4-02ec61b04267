<?php

namespace App\Unit\DTO;

class ProductUnitData
{
    /**
     * 关联ID
     *
     * @var int|null
     */
    public ?int $id;
    
    /**
     * 商品ID
     *
     * @var int
     */
    public int $productId;
    
    /**
     * 单位ID
     *
     * @var int
     */
    public int $unitId;
    
    /**
     * 转换系数
     *
     * @var float
     */
    public float $conversionFactor;
    
    /**
     * 是否基本单位
     *
     * @var bool
     */
    public bool $isBase;
    
    /**
     * 是否默认单位
     *
     * @var bool
     */
    public bool $isDefault;
    
    /**
     * 单位角色列表
     *
     * @var array
     */
    public array $roles;
    
    /**
     * 角色优先级
     *
     * @var array
     */
    public array $rolePriority;
    
    /**
     * 关联的单位数据
     *
     * @var UnitData|null
     */
    public ?UnitData $unit;
    
    /**
     * 创建实例
     */
    public function __construct()
    {
        $this->id = null;
        $this->productId = 0;
        $this->unitId = 0;
        $this->conversionFactor = 1.0;
        $this->isBase = false;
        $this->isDefault = false;
        $this->roles = [];
        $this->rolePriority = [];
        $this->unit = null;
    }
    
    /**
     * 从模型数据创建DTO
     *
     * @param array $data 模型数据
     * @return self
     */
    public static function fromArray(array $data): self
    {
        $dto = new self();
        
        if (isset($data['id'])) {
            $dto->id = (int)$data['id'];
        }
        
        if (isset($data['product_id'])) {
            $dto->productId = (int)$data['product_id'];
        }
        
        if (isset($data['unit_id'])) {
            $dto->unitId = (int)$data['unit_id'];
        }
        
        if (isset($data['conversion_factor']) || isset($data['conversion_rate'])) {
            $dto->conversionFactor = (float)($data['conversion_factor'] ?? $data['conversion_rate'] ?? 1);
        }
        
        if (isset($data['is_base'])) {
            $dto->isBase = (bool)$data['is_base'];
        }
        
        if (isset($data['is_default'])) {
            $dto->isDefault = (bool)$data['is_default'];
        }
        
        // 处理角色
        if (isset($data['roles'])) {
            $dto->roles = is_array($data['roles']) ? $data['roles'] : self::parseRoles($data['roles']);
        } else {
            $dto->roles = self::getLegacyRoles($data);
        }
        
        // 处理角色优先级
        if (isset($data['role_priority'])) {
            $dto->rolePriority = is_array($data['role_priority']) ? 
                $data['role_priority'] : 
                self::parseRolePriority($data['role_priority']);
        } else {
            $dto->rolePriority = self::generateDefaultPriority($dto->roles);
        }
        
        // 处理关联的单位
        if (isset($data['unit']) && is_array($data['unit'])) {
            $dto->unit = UnitData::fromModel((object)$data['unit']);
        }
        
        return $dto;
    }
    
    /**
     * 转换为数组
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [
            'product_id' => $this->productId,
            'unit_id' => $this->unitId,
            'conversion_factor' => $this->conversionFactor,
            'conversion_rate' => $this->conversionFactor, // 兼容字段
            'is_base' => $this->isBase,
            'is_default' => $this->isDefault,
            'roles' => $this->roles,
            'role_priority' => $this->rolePriority,
        ];
        
        if ($this->id) {
            $data['id'] = $this->id;
        }
        
        if ($this->unit) {
            $data['unit'] = $this->unit->toArray();
        }
        
        // 添加兼容字段
        $data['is_inventory_unit'] = in_array('inventory', $this->roles) ? 1 : 0;
        $data['is_purchase_unit'] = in_array('purchase', $this->roles) ? 1 : 0;
        $data['is_sale_unit'] = in_array('sales', $this->roles) ? 1 : 0;
        
        return $data;
    }
    
    /**
     * 解析角色字符串
     *
     * @param string|null $roles
     * @return array
     */
    private static function parseRoles($roles): array
    {
        if (empty($roles)) {
            return [];
        }
        
        if (is_string($roles)) {
            try {
                $parsed = json_decode($roles, true);
                if (is_array($parsed)) {
                    return $parsed;
                }
            } catch (\Exception $e) {
                // 解析失败，尝试其他方式
            }
            
            // 尝试按逗号分割
            return array_map('trim', explode(',', $roles));
        }
        
        return [];
    }
    
    /**
     * 解析角色优先级
     *
     * @param string|null $priority
     * @return array
     */
    private static function parseRolePriority($priority): array
    {
        if (empty($priority)) {
            return [];
        }
        
        if (is_string($priority)) {
            try {
                $parsed = json_decode($priority, true);
                if (is_array($parsed)) {
                    return $parsed;
                }
            } catch (\Exception $e) {
                // 解析失败，返回空数组
            }
        }
        
        return [];
    }
    
    /**
     * 生成默认角色优先级
     *
     * @param array $roles
     * @return array
     */
    private static function generateDefaultPriority(array $roles): array
    {
        $priority = [];
        $index = 1;
        
        foreach ($roles as $role) {
            $priority[$role] = $index * 10;
            $index++;
        }
        
        return $priority;
    }
    
    /**
     * 从旧系统字段获取角色
     *
     * @param array $data
     * @return array
     */
    private static function getLegacyRoles(array $data): array
    {
        $roles = [];
        
        if (isset($data['is_inventory_unit']) && $data['is_inventory_unit']) {
            $roles[] = 'inventory';
        }
        
        if (isset($data['is_purchase_unit']) && $data['is_purchase_unit']) {
            $roles[] = 'purchase';
        }
        
        if (isset($data['is_sale_unit']) && $data['is_sale_unit']) {
            $roles[] = 'sales';
        }
        
        return $roles;
    }
} 