<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 积分奖励类型：fixed(固定积分), rate(按金额比例), none(无奖励)
            $table->enum('points_reward_type', ['fixed', 'rate', 'none'])->default('rate')->comment('积分奖励类型');
            
            // 固定积分数量（当type为fixed时使用）
            $table->unsignedInteger('points_reward_fixed')->default(0)->comment('固定积分奖励数量');
            
            // 积分奖励比例（当type为rate时使用，如0.01表示1%）
            $table->decimal('points_reward_rate', 5, 4)->default(0.0100)->comment('积分奖励比例');
            
            // 最大积分奖励（防止奖励过多）
            $table->unsignedInteger('points_reward_max')->nullable()->comment('最大积分奖励');
            
            // 最小订单金额要求（达到此金额才给积分）
            $table->decimal('points_min_amount', 10, 2)->default(0)->comment('积分奖励最小金额要求');
            
            // 是否启用积分奖励
            $table->boolean('points_reward_enabled')->default(true)->comment('是否启用积分奖励');
            
            // 积分奖励说明
            $table->string('points_reward_desc', 200)->nullable()->comment('积分奖励说明');

            $table->index(['points_reward_enabled', 'points_reward_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['points_reward_enabled', 'points_reward_type']);
            $table->dropColumn([
                'points_reward_type',
                'points_reward_fixed',
                'points_reward_rate',
                'points_reward_max',
                'points_min_amount',
                'points_reward_enabled',
                'points_reward_desc'
            ]);
        });
    }
}; 