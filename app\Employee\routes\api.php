<?php

use App\Employee\Http\Controllers\EmployeeController;
use App\Employee\Http\Controllers\AuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Employee API 路由
|--------------------------------------------------------------------------
|
| 这里定义与员工管理相关的API路由，将替代主路由文件中的对应路由
|
*/

// 不需要认证的端点
Route::prefix('employee/auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);    // 登录不需要认证
});

// 需要认证的端点
Route::middleware('auth:sanctum')->group(function () {
    // 员工认证相关
    Route::prefix('employee/auth')->group(function () {
        Route::get('/me', [AuthController::class, 'me']);    // 获取当前员工信息
        Route::post('/logout', [AuthController::class, 'logout']);
    });
    
    // 员工管理
    Route::prefix('employees')->middleware(['employee.role:admin,manager'])->group(function () {
        Route::get('/', [EmployeeController::class, 'index']);
        Route::post('/', [EmployeeController::class, 'store']);
        Route::get('/{id}', [EmployeeController::class, 'show']);
        Route::put('/{id}', [EmployeeController::class, 'update']);
        Route::delete('/{id}', [EmployeeController::class, 'destroy']);
        Route::put('/{id}/status', [EmployeeController::class, 'updateStatus']);
        Route::put('/{id}/role', [EmployeeController::class, 'updateRole']);
        Route::get('/roles', [EmployeeController::class, 'getRoles']);
    });
}); 