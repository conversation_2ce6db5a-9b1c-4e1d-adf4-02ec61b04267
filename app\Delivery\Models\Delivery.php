<?php

namespace App\Delivery\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Order\Models\Order;
use App\Employee\Models\Employee;
use App\Delivery\Models\DeliveryRoute;
use App\Delivery\Models\Deliverer;

class Delivery extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'status',
        'deliverer_id',
        'route_id',
    ];

    /**
     * The relationships that should be eager loaded.
     *
     * @var array
     */
    protected $with = ['order'];

    /**
     * Get the order that owns the delivery.
     */
    public function order()
    {
        return $this->belongsTo(Order::class)->withDefault([
            'order_no' => '未知订单',
            'status' => 'unknown',
            'shipping_address' => '',
            'contact_name' => '',
            'contact_phone' => ''
        ]);
    }
    
    /**
     * 获取配送员
     */
    public function deliverer()
    {
        return $this->belongsTo(Deliverer::class, 'deliverer_id');
    }
    
    /**
     * 获取作为配送员的员工（通过deliverer关联）
     * 保持向后兼容性
     */
    public function employeeDeliverer()
    {
        return $this->deliverer ? $this->deliverer->employee() : null;
    }

    /**
     * 获取配送员关联的用户账号
     * 
     * @return \App\Models\User|null
     */
    public function delivererUser()
    {
        if ($this->deliverer && $this->deliverer->employee) {
            return $this->deliverer->employee->user;
        }
        
        return null;
    }

    /**
     * 获取当前负责配送的员工
     * 
     * @return Employee|null
     */
    public function getCurrentDeliverer()
    {
        if ($this->deliverer && $this->deliverer->employee) {
            return $this->deliverer->employee;
        }
        
        return null;
    }
    
    /**
     * 获取配送路线
     */
    public function route()
    {
        return $this->belongsTo(DeliveryRoute::class, 'route_id');
    }
} 