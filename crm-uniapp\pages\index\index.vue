<template>
	<view class="index-container">
		<!-- 今日关键指标 -->
		<view class="stats-section">
			<view class="stats-title">
				<text>今日关键指标</text>
				<view class="title-actions">
					<text class="refresh-btn" @tap="refreshData">🔄</text>
					<text class="debug-btn" @tap="showAnalyticsMenu">📊</text>
				</view>
			</view>
			<view class="stats-grid">
				<view class="stat-item" @tap="goToOrders">
					<text class="stat-number">{{ todayStats.today_orders || 0 }}</text>
					<text class="stat-label">今日订单</text>
					<text class="stat-trend" :class="getTrendClass(todayStats.orders_trend)">
						{{ getTrendText(todayStats.orders_trend) }}
					</text>
				</view>
				<view class="stat-item" @tap="goToOrders">
					<text class="stat-number">¥{{ formatNumber(todayStats.today_sales) || 0 }}</text>
					<text class="stat-label">今日销售</text>
					<text class="stat-trend" :class="getTrendClass(todayStats.sales_trend)">
						{{ getTrendText(todayStats.sales_trend) }}
					</text>
				</view>
				<view class="stat-item" @tap="goToClients">
					<text class="stat-number">{{ todayStats.today_active_customers || 0 }}</text>
					<text class="stat-label">活跃客户</text>
					<text class="stat-trend" :class="getTrendClass(todayStats.customers_trend)">
						{{ getTrendText(todayStats.customers_trend) }}
					</text>
				</view>
				<view class="stat-item" @tap="goToAnalytics">
					<text class="stat-number">{{ behaviorOverview.avg_session_duration || 0 }}s</text>
					<text class="stat-label">平均停留</text>
					<text class="stat-trend positive">
						{{ behaviorOverview.total_sessions || 0 }}会话
					</text>
				</view>
			</view>
		</view>

		<!-- 智能洞察卡片 -->
		<view class="insights-section" v-if="insights.length > 0">
			<view class="section-title">
				<text>💡 智能洞察</text>
			</view>
			<view class="insights-list">
				<view class="insight-item" v-for="(insight, index) in insights" :key="index" @tap="handleInsightAction(insight)">
					<view class="insight-icon">{{ insight.icon }}</view>
					<view class="insight-content">
						<text class="insight-title">{{ insight.title }}</text>
						<text class="insight-desc">{{ insight.description }}</text>
					</view>
					<view class="insight-action">
						<text class="action-text">{{ insight.actionText }}</text>
						<text class="arrow-icon">></text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 快捷操作 -->
		<view class="quick-actions">
			<view class="section-title">快捷操作</view>
			<view class="action-grid">
				<view class="action-item" @tap="goToProxyOrder">
					<view class="action-icon">📝</view>
					<text class="action-text">代客下单</text>
				</view>
				<view class="action-item" @tap="goToClients">
					<view class="action-icon">👥</view>
					<text class="action-text">客户管理</text>
				</view>
				<view class="action-item" @tap="goToAnalytics">
					<view class="action-icon">📊</view>
					<text class="action-text">行为分析</text>
				</view>
				<view class="action-item" @tap="goToChurnWarning">
					<view class="action-icon">⚠️</view>
					<text class="action-text">流失预警</text>
					<view class="badge" v-if="churnWarningCount > 0">{{ churnWarningCount }}</view>
				</view>
			</view>
		</view>
		
		<!-- 客户行为概览 -->
		<view class="behavior-overview" v-if="behaviorOverview.total_page_views > 0">
			<view class="section-title">
				<text>客户行为概览</text>
				<text class="more-link" @tap="goToAnalytics">详细分析</text>
			</view>
			<view class="behavior-stats">
				<view class="behavior-item">
					<text class="behavior-number">{{ formatNumber(behaviorOverview.total_page_views) }}</text>
					<text class="behavior-label">页面浏览</text>
				</view>
				<view class="behavior-item">
					<text class="behavior-number">{{ formatNumber(behaviorOverview.product_views) }}</text>
					<text class="behavior-label">商品浏览</text>
				</view>
				<view class="behavior-item">
					<text class="behavior-number">{{ behaviorOverview.active_sessions }}</text>
					<text class="behavior-label">活跃会话</text>
					</view>
				<view class="behavior-item">
					<text class="behavior-number">{{ behaviorOverview.repurchase_rate }}%</text>
					<text class="behavior-label">复购率</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import config from '../../utils/config.js'
import analyticsApi from '../../api/analytics.js'

export default {
	data() {
		return {
			todayStats: {},
			behaviorOverview: {},
			insights: [],
			churnWarningCount: 0
		}
	},
	
	onLoad() {
		this.checkAuth()
		this.loadTodayStats()
		this.loadData()
	},
	
	onShow() {
		// 页面显示时刷新数据
		this.loadData()
	},
	
	onPullDownRefresh() {
		this.refreshData()
	},
	
	methods: {
		// 检查登录状态
		checkAuth() {
			const token = uni.getStorageSync(config.storageKeys.token)
			if (!token) {
				uni.reLaunch({
					url: '/pages/login/login'
				})
			}
		},
		
		// 加载今日统计
		async loadTodayStats() {
			try {
				const response = await analyticsApi.getTodayStats()
				this.todayStats = response.data || {}
			} catch (error) {
				console.error('加载统计数据失败:', error)
				this.todayStats = {}
			}
		},
		
		// 加载行为分析概览
		async loadBehaviorOverview() {
			try {
				const response = await analyticsApi.getBehaviorOverview()
				this.behaviorOverview = response.data || {}
			} catch (error) {
				console.error('加载行为分析概览失败:', error)
				this.behaviorOverview = {}
			}
		},
		
		// 加载智能洞察
		async loadInsights() {
			try {
				// 确保行为概览数据已加载
				if (!this.behaviorOverview || Object.keys(this.behaviorOverview).length === 0) {
					await this.loadBehaviorOverview()
				}
				
				// 确保流失预警数据已加载
				if (this.churnWarningCount === 0) {
					await this.loadChurnWarningCount()
				}
				
				// 基于数据生成智能洞察
				this.insights = this.generateSmartInsights()
				
				// 如果没有生成洞察，等待一下再重试
				if (this.insights.length === 0) {
					setTimeout(() => {
						this.insights = this.generateSmartInsights()
					}, 1000)
				}
			} catch (error) {
				console.error('加载智能洞察失败:', error)
				// 即使出错也尝试生成基础洞察
				this.insights = this.generateSmartInsights()
			}
		},
		
		// 加载流失预警数量
		async loadChurnWarningCount() {
			try {
				const response = await analyticsApi.getChurnWarning({ limit: 1 })
				this.churnWarningCount = response.data?.total_warnings || 0
			} catch (error) {
				console.error('加载流失预警数量失败:', error)
				this.churnWarningCount = 0
			}
		},
		
		// 生成智能洞察
		generateSmartInsights() {
			const insights = []
			
			// 基于流失预警生成洞察
			if (this.behaviorOverview.churn_warnings !== undefined && this.behaviorOverview.churn_warnings > 0) {
				insights.push({
					icon: '⚠️',
					title: '客户流失预警',
					description: `发现 ${this.behaviorOverview.churn_warnings} 位客户有流失风险，需要及时关注`,
					actionText: '立即查看',
					action: 'churn_warning'
				})
			}
			
			// 基于复购率生成洞察 - 生鲜配送期望70%以上复购率
			if (this.behaviorOverview.repurchase_rate !== undefined && this.behaviorOverview.repurchase_rate >= 0) {
				if (this.behaviorOverview.repurchase_rate < 50) {
					insights.push({
						icon: '🔄',
						title: '复购率需要提升',
						description: `当前复购率 ${this.behaviorOverview.repurchase_rate}%，建议优化商品质量和服务`,
						actionText: '查看分析',
						action: 'purchase_analysis'
					})
				} else if (this.behaviorOverview.repurchase_rate >= 70) {
					insights.push({
						icon: '🎯',
						title: '复购率表现优秀',
						description: `复购率达到 ${this.behaviorOverview.repurchase_rate}%，客户忠诚度高`,
						actionText: '查看详情',
						action: 'purchase_analysis'
					})
				}
			}
			
			// 基于客户活跃度生成洞察
			if (this.behaviorOverview.active_users !== undefined && this.behaviorOverview.active_users >= 0) {
				if (this.behaviorOverview.active_users < 30) {
					insights.push({
						icon: '📱',
						title: '客户活跃度偏低',
						description: `活跃客户 ${this.behaviorOverview.active_users} 人，建议加强推广和客户关怀`,
						actionText: '查看分析',
						action: 'customer_activity'
					})
				} else if (this.behaviorOverview.active_users >= 30) {
					insights.push({
						icon: '🔥',
						title: '客户活跃度良好',
						description: `活跃客户 ${this.behaviorOverview.active_users} 人，继续保持运营效果`,
						actionText: '查看详情',
						action: 'customer_activity'
					})
				}
			} else {
				// 如果没有活跃用户数据，也生成一个默认的客户活跃度洞察
				insights.push({
					icon: '📊',
					title: '客户活跃度分析',
					description: '深度分析客户活跃度表现与趋势，制定精准运营策略',
					actionText: '立即查看',
					action: 'customer_activity'
				})
			}
			
			// 基于平均客单价生成洞察 - 生鲜配送关注客单价优化
			if (this.behaviorOverview.avg_order_value !== undefined && this.behaviorOverview.avg_order_value > 0) {
				if (this.behaviorOverview.avg_order_value < 500) { // 调整为500元以下
					insights.push({
						icon: '💰',
						title: '客单价有提升空间',
						description: `平均客单价 ¥${this.behaviorOverview.avg_order_value}，可推荐套餐提升`,
						actionText: '查看建议',
						action: 'order_value_analysis'
					})
				} else if (this.behaviorOverview.avg_order_value >= 800) { // 调整为800元以上
					insights.push({
						icon: '💎',
						title: '客单价表现良好',
						description: `平均客单价 ¥${this.behaviorOverview.avg_order_value}，客户价值较高`,
						actionText: '查看详情',
						action: 'order_value_analysis'
					})
				}
			}
			
			// 基于会话时长生成洞察 - 生鲜配送购买决策快
			if (this.behaviorOverview.avg_session_duration !== undefined && this.behaviorOverview.avg_session_duration > 0) {
				if (this.behaviorOverview.avg_session_duration < 30) {
					insights.push({
						icon: '⏱️',
						title: '用户停留时间短',
						description: `平均停留 ${this.behaviorOverview.avg_session_duration} 秒，建议优化页面体验`,
						actionText: '查看行为分析',
						action: 'behavior_analysis'
					})
				} else if (this.behaviorOverview.avg_session_duration >= 120) {
					insights.push({
						icon: '👀',
						title: '用户参与度高',
						description: `平均停留 ${this.behaviorOverview.avg_session_duration} 秒，用户粘性良好`,
						actionText: '查看行为分析',
						action: 'behavior_analysis'
					})
				}
			}
			
			// 基于页面浏览量生成洞察
			if (this.behaviorOverview.total_page_views !== undefined && this.behaviorOverview.total_page_views > 0) {
				if (this.behaviorOverview.total_page_views > 5000) {
					insights.push({
						icon: '📊',
						title: '用户参与度高',
						description: `页面浏览量达到 ${this.behaviorOverview.total_page_views} 次，用户活跃`,
						actionText: '查看趋势分析',
						action: 'trend_analysis'
					})
				}
			}
			
			// 基于今日统计生成洞察
			if (this.todayStats.today_orders !== undefined) {
				if (this.todayStats.today_orders === 0) {
					insights.push({
						icon: '📈',
						title: '今日订单关注',
						description: '今日暂无新订单，建议主动联系重点客户',
						actionText: '查看客户分层',
						action: 'customer_segment'
					})
				} else if (this.todayStats.today_orders >= 10) {
					insights.push({
						icon: '🎯',
						title: '今日订单活跃',
						description: `今日已有 ${this.todayStats.today_orders} 个订单，表现良好`,
						actionText: '查看趋势分析',
						action: 'trend_analysis'
					})
				}
			}
			
			// 如果没有生成任何洞察，添加生鲜配送相关的默认洞察
			if (insights.length === 0) {
				insights.push({
					icon: '📊',
					title: '客户活跃度分析',
					description: '深度分析客户活跃度表现与趋势，制定精准运营策略',
					actionText: '立即查看',
					action: 'customer_activity'
				})
				
				insights.push({
					icon: '📈',
					title: '购买行为分析',
					description: '分析客户购买频次、偏好和消费模式',
					actionText: '查看分析',
					action: 'purchase_analysis'
				})
				
				insights.push({
					icon: '🥬',
					title: '生鲜配送分析',
					description: '查看客户购买习惯和配送时段偏好分析',
					actionText: '立即查看',
					action: 'analytics_overview'
				})
			}
			
			return insights.slice(0, 3) // 最多显示3个洞察
		},
		
		// 刷新数据
		async refreshData() {
			await Promise.all([
				this.loadTodayStats(),
				this.loadData()
			])
			uni.stopPullDownRefresh()
		},
		
		// 跳转到代客下单
		goToProxyOrder() {
			// 先跳转到客户选择页面
			uni.navigateTo({
				url: '/pages/proxy-order/select-client'
			})
		},
		
		// 跳转到客户管理
		goToClients() {
			uni.switchTab({
				url: '/pages/clients/clients'
			})
		},
		
		// 跳转到订单管理
		goToOrders() {
			uni.switchTab({
				url: '/pages/orders/orders'
			})
		},
		
		// 跳转到行为分析
		goToAnalytics() {
			uni.navigateTo({
				url: '/pages/analytics/analytics'
			})
		},
		
		// 跳转到流失预警
		goToChurnWarning() {
			uni.navigateTo({
				url: '/pages/analytics/churn-warning'
			})
		},
		
		// 测试客户活跃度跳转
		testCustomerActivityJump() {
			console.log('测试客户活跃度跳转')
			uni.navigateTo({
				url: '/pages/analytics/customer-activity'
			})
		},
		
		// 添加调试功能：显示所有可用的分析页面
		showAnalyticsMenu() {
			const analyticsPages = [
				{ name: '客户活跃度分析', url: '/pages/analytics/customer-activity' },
				{ name: '行为分析概览', url: '/pages/analytics/analytics' },
				{ name: '流失预警', url: '/pages/analytics/churn-warning' },
				{ name: '购买分析', url: '/pages/analytics/purchase-analysis' },
				{ name: '客单价表现分析', url: '/pages/analytics/order-value-analysis' },
				{ name: '浏览分析', url: '/pages/analytics/browse-analysis' },
				{ name: '时间分析', url: '/pages/analytics/time-analysis' },
				{ name: '商品分析', url: '/pages/analytics/product-analysis' },
				{ name: '客户分层', url: '/pages/analytics/customer-segment' },
				{ name: '趋势分析', url: '/pages/analytics/trend-analysis' }
			]
			
			const itemList = analyticsPages.map(page => page.name)
			
			uni.showActionSheet({
				itemList: itemList,
				success: (res) => {
					const selectedPage = analyticsPages[res.tapIndex]
					uni.navigateTo({
						url: selectedPage.url
					})
				}
			})
		},
		
		// 格式化数字
		formatNumber(num) {
			if (!num) return '0'
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + 'w'
			}
			if (num >= 1000) {
				return (num / 1000).toFixed(1) + 'k'
			}
			return num.toString()
		},
		
		// 获取趋势类
		getTrendClass(trend) {
			return {
				positive: trend === 'up',
				negative: trend === 'down'
			}
		},
		
		// 获取趋势文本
		getTrendText(trend) {
			return trend === 'up' ? '↑' : trend === 'down' ? '↓' : ''
		},
		
		// 处理智能洞察操作
		handleInsightAction(insight) {
			console.log('处理洞察操作:', insight.action, insight.title)
			
			switch (insight.action) {
				case 'churn_warning':
					this.goToChurnWarning()
					break
				case 'purchase_analysis':
					uni.navigateTo({
						url: '/pages/analytics/purchase-analysis'
					})
					break
				case 'customer_activity':
					console.log('跳转到客户活跃度分析页面')
					uni.navigateTo({
						url: '/pages/analytics/customer-activity'
					})
					break
				case 'order_value_analysis':
					uni.navigateTo({
						url: '/pages/analytics/order-value-analysis'
					})
					break
				case 'time_analysis':
					uni.navigateTo({
						url: '/pages/analytics/time-analysis'
					})
					break
				case 'behavior_analysis':
					uni.navigateTo({
						url: '/pages/analytics/browse-analysis'
					})
					break
				case 'product_analysis':
					uni.navigateTo({
						url: '/pages/analytics/product-analysis'
					})
					break
				case 'customer_segment':
					uni.navigateTo({
						url: '/pages/analytics/customer-segment'
					})
					break
				case 'trend_analysis':
					uni.navigateTo({
						url: '/pages/analytics/trend-analysis'
					})
					break
				case 'analytics_overview':
					this.goToAnalytics()
					break
				default:
					console.log('未知的洞察操作:', insight.action)
					// 默认跳转到分析概览页面
					this.goToAnalytics()
					break
					}
		},
		
		// 统一加载数据
		async loadData() {
			try {
				// 并行加载基础数据
				await Promise.all([
					this.loadBehaviorOverview(),
					this.loadChurnWarningCount()
				])
				
				// 基础数据加载完成后再加载智能洞察
				await this.loadInsights()
			} catch (error) {
				console.error('加载数据失败:', error)
				// 即使出错也尝试加载洞察
				this.loadInsights()
			}
		}
	}
}
</script>

<style scoped>
.index-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 今日关键指标 */
.stats-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.stats-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.title-actions {
	display: flex;
	align-items: center;
}

.refresh-btn {
	font-size: 28rpx;
	color: #007AFF;
	padding: 8rpx;
}

.debug-btn {
	font-size: 28rpx;
	color: #007AFF;
	padding: 8rpx;
	margin-left: 16rpx;
}

.stats-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 24rpx;
}

.stat-item {
	text-align: center;
	padding: 32rpx 16rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	position: relative;
	transition: all 0.3s ease;
}

.stat-item:active {
	transform: scale(0.98);
	background: #e9ecef;
}

.stat-number {
	display: block;
	font-size: 40rpx;
	font-weight: 700;
	color: #007AFF;
	margin-bottom: 8rpx;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 8rpx;
}

.stat-trend {
	font-size: 24rpx;
	color: #666666;
}

.stat-trend.positive {
	color: #28a745;
}

.stat-trend.negative {
	color: #dc3545;
}

/* 智能洞察卡片 */
.insights-section {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 24rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.more-link {
	font-size: 28rpx;
	color: #007AFF;
}

.insights-list {
	margin-top: 16rpx;
}

.insight-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
	transition: all 0.3s ease;
}

.insight-item:active {
	transform: scale(0.98);
	background: #e9ecef;
}

.insight-item:last-child {
	margin-bottom: 0;
}

.insight-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	font-size: 40rpx;
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.insight-content {
	flex: 1;
}

.insight-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.insight-desc {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.4;
}

.insight-action {
	display: flex;
	align-items: center;
}

.action-text {
	font-size: 28rpx;
	color: #007AFF;
	margin-right: 8rpx;
}

.arrow-icon {
	font-size: 32rpx;
	color: #cccccc;
}

/* 快捷操作 */
.quick-actions {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.action-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 24rpx;
	margin-top: 16rpx;
}

.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 32rpx 16rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	position: relative;
	transition: all 0.3s ease;
}

.action-item:active {
	transform: scale(0.98);
	background: #e9ecef;
}

.action-icon {
	font-size: 48rpx;
	margin-bottom: 16rpx;
}

.action-text {
	font-size: 28rpx;
	color: #333333;
}

.badge {
	position: absolute;
	top: 16rpx;
	right: 16rpx;
	background: #ff4757;
	color: #ffffff;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 50rpx;
	min-width: 32rpx;
	text-align: center;
}

/* 客户行为概览 */
.behavior-overview {
	background: #ffffff;
	margin: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.behavior-stats {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
	margin-top: 16rpx;
}

.behavior-item {
	text-align: center;
	padding: 24rpx 16rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.behavior-number {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #007AFF;
	margin-bottom: 8rpx;
}

.behavior-label {
	font-size: 24rpx;
	color: #666666;
}
</style>
