<?php

namespace App\Upload\Services;

use App\Models\UploadConfig;
use App\Upload\Contracts\StorageDriverInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class CosStorageDriver implements StorageDriverInterface
{
    /**
     * 存储磁盘
     *
     * @var string
     */
    protected $disk = 'cos';
    
    /**
     * COS配置
     *
     * @var array
     */
    protected $config;
    
    /**
     * 构造函数
     *
     * @param array $config 配置数组
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;
        $this->disk = 'cos';
        
        // 将数据库配置同步到Laravel配置
        $this->updateCosConfig();
    }
    
    /**
     * 从数据库加载COS配置
     *
     * @return void
     */
    protected function loadConfig()
    {
        $this->config = [
            'cos_app_id' => UploadConfig::getValue('cos_app_id', config('filesystems.disks.cos.app_id', '')),
            'cos_secret_id' => UploadConfig::getValue('cos_secret_id', config('filesystems.disks.cos.secret_id', '')),
            'cos_secret_key' => UploadConfig::getValue('cos_secret_key', config('filesystems.disks.cos.secret_key', '')),
            'cos_region' => UploadConfig::getValue('cos_region', config('filesystems.disks.cos.region', '')),
            'cos_bucket' => UploadConfig::getValue('cos_bucket', config('filesystems.disks.cos.bucket', '')),
            'cos_cdn' => UploadConfig::getValue('cos_cdn', config('filesystems.disks.cos.cdn', '')),
        ];
    }
    
    /**
     * 存储文件
     *
     * @param UploadedFile $file 上传的文件
     * @param string $directory 目标目录
     * @param string $filename 文件名
     * @param array $options 附加选项
     * @return array|null 上传结果
     */
    public function store(UploadedFile $file, string $directory, string $filename, array $options = []): ?array
    {
        try {
            // 记录上传信息
            Log::info('准备上传文件到腾讯云COS', [
                'original_name' => $file->getClientOriginalName(),
                'directory' => $directory,
                'filename' => $filename,
                'disk' => $this->disk,
                'bucket' => config('filesystems.disks.cos.bucket'),
                'app_id' => config('filesystems.disks.cos.app_id')
            ]);
            
            // 修正COS配置
            $this->updateCosConfig();
            
            // 提取并提示底层SDK不要自动添加APP ID (在这里处理可能无效，已在updateCosConfig中处理)
            $appId = config('filesystems.disks.cos.app_id');
            $bucket = config('filesystems.disks.cos.bucket');
            $fullBucket = $bucket . '-' . $appId;
            
            // 存储文件前先记录配置
            Log::info('最终的COS配置', [
                'config' => array_filter(config('filesystems.disks.cos')),
                'full_bucket' => $fullBucket,
                'append_app_id' => config('filesystems.disks.cos.append_app_id', false)
            ]);
            
            // 构建完整路径
            $fullPath = trim($directory, '/') . '/' . $filename;
            
            // 使用底层存储方法
            $disk = Storage::disk($this->disk);
            $contents = file_get_contents($file->getRealPath());
            $result = $disk->put($fullPath, $contents);
            
            if (!$result) {
                Log::error('COS文件上传失败', [
                    'original_name' => $file->getClientOriginalName(),
                    'full_path' => $fullPath,
                    'disk' => $this->disk
                ]);
                return null;
            }
            
            // 成功后在日志记录完整路径和URL
            $url = $this->getUrl($fullPath);
            
            Log::info('COS文件上传成功', [
                'path' => $fullPath,
                'url' => $url,
                'disk' => $this->disk,
                'full_bucket' => $fullBucket
            ]);
            
            // 返回上传结果
            return [
                'path' => $fullPath,
                'url' => $url,
                'disk' => $this->disk,
                'directory' => $directory,
                'filename' => $filename,
                'driver' => $this->getDriverName()
            ];
        } catch (\Exception $e) {
            Log::error('COS存储上传失败', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'original_name' => $file->getClientOriginalName(),
                'directory' => $directory,
                'filename' => $filename
            ]);
            
            return null;
        }
    }
    
    /**
     * 修正COS配置
     */
    protected function updateCosConfig()
    {
        // 从配置中读取值
        $appId = $this->config['cos_app_id'] ?? '';
        $bucket = $this->config['cos_bucket'] ?? '';
        $secretId = $this->config['cos_secret_id'] ?? '';
        $secretKey = $this->config['cos_secret_key'] ?? '';
        $region = $this->config['cos_region'] ?? '';
        
        // 检查必要的配置
        if (empty($appId) || empty($bucket) || empty($secretId) || empty($secretKey) || empty($region)) {
            Log::error('COS配置不完整', [
                'app_id' => $appId,
                'bucket' => $bucket,
                'region' => $region,
                'has_secret_id' => !empty($secretId),
                'has_secret_key' => !empty($secretKey),
            ]);
        }
        
        // 检查原始存储桶名称是否已包含APP ID
        $hasSuffix = !empty($appId) && !empty($bucket) && 
                     strpos($bucket, '-' . $appId) !== false;
        
        // 如果已经包含APP ID后缀，则将存储桶名称拆分
        if ($hasSuffix) {
            // 提取不带APP ID的部分
            $cleanBucket = preg_replace('/-' . $appId . '$/', '', $bucket);
            
            // 更新配置
            config(['filesystems.disks.cos.bucket' => $cleanBucket]);
            config(['filesystems.disks.cos.app_id' => $appId]);
            
            // 记录信息
            Log::info('分离APP ID和存储桶名称', [
                'original_bucket' => $bucket,
                'clean_bucket' => $cleanBucket,
                'app_id' => $appId
            ]);
        } else {
            // 直接使用原始值
            config(['filesystems.disks.cos.bucket' => $bucket]);
            config(['filesystems.disks.cos.app_id' => $appId]);
        }
        
        // 确保其他配置也更新
        if (!empty($secretId)) {
            config(['filesystems.disks.cos.secret_id' => $secretId]);
        }
        
        if (!empty($secretKey)) {
            config(['filesystems.disks.cos.secret_key' => $secretKey]);
        }
        
        if (!empty($region)) {
            config(['filesystems.disks.cos.region' => $region]);
        }
        
        // 确保禁用自动添加APP ID
        config(['filesystems.disks.cos.append_app_id' => false]);
        
        // 关键修复：更新credentials数组，这是COS SDK要求的
        config(['filesystems.disks.cos.credentials' => [
            'appId' => $appId,
            'secretId' => $secretId,
            'secretKey' => $secretKey,
        ]]);
        
        // 记录最终配置
        Log::info('COS配置已更新', [
            'app_id' => config('filesystems.disks.cos.app_id'),
            'bucket' => config('filesystems.disks.cos.bucket'),
            'region' => config('filesystems.disks.cos.region'),
            'driver' => $this->disk,
            'append_app_id' => config('filesystems.disks.cos.append_app_id'),
            'has_secret_id' => !empty(config('filesystems.disks.cos.secret_id')),
            'has_secret_key' => !empty(config('filesystems.disks.cos.secret_key')),
            'credentials_updated' => true
        ]);
    }
    
    /**
     * 获取文件URL
     *
     * @param string $path 文件路径
     * @return string
     */
    public function getUrl(string $path): string
    {
        try {
            // 优先使用CDN
            $cdn = $this->config['cos_cdn'] ?? '';
            if (!empty($cdn)) {
                return rtrim($cdn, '/') . '/' . ltrim($path, '/');
            }
            
            // 获取配置
            $appId = config('filesystems.disks.cos.app_id');
            $bucket = config('filesystems.disks.cos.bucket');
            $region = config('filesystems.disks.cos.region');
            
            // 确保存储桶包含APP ID
            $fullBucket = strpos($bucket, '-' . $appId) === false ?
                          $bucket . '-' . $appId :
                          $bucket;
            
            // 生成URL
            $url = sprintf(
                'https://%s.cos.%s.myqcloud.com/%s',
                $fullBucket,
                $region,
                ltrim($path, '/')
            );
            
            Log::info('生成的COS文件URL', [
                'path' => $path,
                'url' => $url,
                'full_bucket' => $fullBucket
            ]);
            
            return $url;
        } catch (\Exception $e) {
            Log::error('获取COS文件URL失败', [
                'error' => $e->getMessage(),
                'path' => $path
            ]);
            return '';
        }
    }
    
    /**
     * 删除文件
     *
     * @param string $path 文件路径
     * @return bool
     */
    public function delete(string $path): bool
    {
        try {
            return Storage::disk($this->disk)->delete($path);
        } catch (\Exception $e) {
            Log::error('删除COS文件失败', [
                'path' => $path,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * 获取存储驱动名称
     *
     * @return string
     */
    public function getDriverName(): string
    {
        return 'cos';
    }
}