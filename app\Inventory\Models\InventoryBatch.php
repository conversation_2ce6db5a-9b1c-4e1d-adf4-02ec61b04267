<?php

namespace App\Inventory\Models;

use App\Models\User;
use App\Purchase\Models\PurchaseItem;
use App\Unit\Models\Unit;
use App\Warehouse\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class InventoryBatch extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'inventory_id',
        'purchase_item_id',
        'batch_code',
        'supplier_batch_no',
        'manufacturer',
        'origin_country',
        'production_date',
        'expiry_date',
        'purchase_price',
        'total_cost',
        'unit_cost',
        'cost_breakdown',
        'quantity',
        'initial_quantity',
        'locked_quantity',
        'available_quantity',
        'unit_id',
        'status',
        'quality_status',
        'quality_data',
        'quality_checked_at',
        'quality_checked_by',
        'storage_conditions',
        'storage_temperature_min',
        'storage_temperature_max',
        'storage_humidity_min',
        'storage_humidity_max',
        'last_moved_at',
        'last_moved_by',
        'move_reason',
        'expiry_warning_days',
        'auto_dispose_expired',
        'notes',
        'created_by'
    ];

    /**
     * 应该被转换的属性
     */
    protected $casts = [
        'production_date' => 'date',
        'expiry_date' => 'date',
        'purchase_price' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'unit_cost' => 'decimal:4',
        'quantity' => 'decimal:2',
        'initial_quantity' => 'decimal:2',
        'locked_quantity' => 'decimal:2',
        'available_quantity' => 'decimal:2',
        'storage_temperature_min' => 'decimal:2',
        'storage_temperature_max' => 'decimal:2',
        'storage_humidity_min' => 'decimal:2',
        'storage_humidity_max' => 'decimal:2',
        'auto_dispose_expired' => 'boolean',
        'quality_data' => 'array',
        'cost_breakdown' => 'array',
        'storage_conditions' => 'array',
        'quality_checked_at' => 'datetime',
        'last_moved_at' => 'datetime',
    ];

    /**
     * 获取关联的库存记录
     */
    public function inventory()
    {
        return $this->belongsTo(Inventory::class);
    }

    /**
     * 获取关联的采购明细
     */
    public function purchaseItem()
    {
        return $this->belongsTo(PurchaseItem::class);
    }

    /**
     * 获取关联的单位
     */
    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * 获取创建者
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取质量检验人
     */
    public function qualityChecker()
    {
        return $this->belongsTo(User::class, 'quality_checked_by');
    }

    /**
     * 获取最后移动人
     */
    public function lastMover()
    {
        return $this->belongsTo(User::class, 'last_moved_by');
    }

    /**
     * 获取批次操作记录
     */
    public function operations()
    {
        return $this->hasMany(BatchOperation::class, 'batch_id');
    }

    /**
     * 检查批次是否已过期
     * 
     * @return bool
     */
    public function isExpired()
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    /**
     * 获取距离过期的天数
     * 
     * @return int|null
     */
    public function daysUntilExpiry()
    {
        if (!$this->expiry_date) {
            return null;
        }
        
        return now()->startOfDay()->diffInDays($this->expiry_date, false);
    }

    /**
     * 检查批次是否快过期（少于给定天数）
     * 
     * @param int $days
     * @return bool
     */
    public function isNearExpiry($days = null)
    {
        $warningDays = $days ?? $this->expiry_warning_days ?? 7;
        $daysLeft = $this->daysUntilExpiry();
        return $daysLeft !== null && $daysLeft >= 0 && $daysLeft <= $warningDays;
    }

    /**
     * 获取批次剩余百分比
     * 
     * @return float
     */
    public function getRemainingPercentage()
    {
        if ($this->initial_quantity <= 0) {
            return 0;
        }
        
        return ($this->quantity / $this->initial_quantity) * 100;
    }

    /**
     * 获取可用数量（总数量 - 锁定数量）
     * 
     * @return float
     */
    public function getAvailableQuantityAttribute()
    {
        return max(0, $this->quantity - $this->locked_quantity);
    }

    /**
     * 自动生成批次编码
     * 
     * @return string
     */
    public static function generateBatchCode()
    {
        $prefix = 'B';
        $date = date('ymd');
        
        do {
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $code = $prefix . $date . $random;
        } while (self::where('batch_code', $code)->exists());
        
        return $code;
    }

    /**
     * 更新批次状态
     * 
     * @param string $newStatus 新状态
     * @param string|null $reason 原因
     * @param int|null $operatedBy 操作人ID
     * @return bool
     */
    public function updateStatus($newStatus, $reason = null, $operatedBy = null)
    {
        $oldStatus = $this->status;
        
        if ($oldStatus === $newStatus) {
            return true;
        }

        DB::beginTransaction();
        try {
            $this->status = $newStatus;
            $this->save();

            // 记录操作历史
            $this->recordOperation('status_change', [
                'status_before' => $oldStatus,
                'status_after' => $newStatus,
                'reason' => $reason,
                'operated_by' => $operatedBy ?? auth()->id(),
            ]);

            // 自动处理状态变更的后续操作
            $this->handleStatusChange($oldStatus, $newStatus);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('批次状态更新失败', [
                'batch_id' => $this->id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 锁定批次数量
     * 
     * @param float $quantity 锁定数量
     * @param string $reason 锁定原因
     * @param array $lockData 锁定相关数据
     * @return bool
     */
    public function lockQuantity($quantity, $reason = null, $lockData = [])
    {
        if ($quantity <= 0) {
            return false;
        }

        if ($this->available_quantity < $quantity) {
            return false;
        }

        DB::beginTransaction();
        try {
            $this->locked_quantity += $quantity;
            $this->save();

            // 记录操作历史
            $this->recordOperation('locked', [
                'quantity_change' => $quantity,
                'locked_quantity_after' => $this->locked_quantity,
                'reason' => $reason,
                'lock_data' => $lockData,
            ]);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('批次数量锁定失败', [
                'batch_id' => $this->id,
                'quantity' => $quantity,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 解锁批次数量
     * 
     * @param float $quantity 解锁数量
     * @param string $reason 解锁原因
     * @return bool
     */
    public function unlockQuantity($quantity, $reason = null)
    {
        if ($quantity <= 0 || $quantity > $this->locked_quantity) {
            return false;
        }

        DB::beginTransaction();
        try {
            $this->locked_quantity -= $quantity;
            $this->save();

            // 记录操作历史
            $this->recordOperation('unlocked', [
                'quantity_change' => -$quantity,
                'locked_quantity_after' => $this->locked_quantity,
                'reason' => $reason,
            ]);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('批次数量解锁失败', [
                'batch_id' => $this->id,
                'quantity' => $quantity,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 出库操作
     * 
     * @param float $quantity 出库数量
     * @param array $outboundData 出库相关数据
     * @return bool
     */
    public function outbound($quantity, $outboundData = [])
    {
        if ($quantity <= 0 || $quantity > $this->available_quantity) {
            return false;
        }

        DB::beginTransaction();
        try {
            $quantityBefore = $this->quantity;
            $this->quantity -= $quantity;
            $this->save();

            // 记录操作历史
            $this->recordOperation('shipped', [
                'quantity_before' => $quantityBefore,
                'quantity_after' => $this->quantity,
                'quantity_change' => -$quantity,
                'outbound_data' => $outboundData,
            ]);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('批次出库失败', [
                'batch_id' => $this->id,
                'quantity' => $quantity,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 质量检验
     * 
     * @param string $status 检验状态
     * @param array $qualityData 检验数据
     * @param int|null $checkedBy 检验人ID
     * @return bool
     */
    public function qualityCheck($status, $qualityData = [], $checkedBy = null)
    {
        DB::beginTransaction();
        try {
            $this->quality_status = $status;
            $this->quality_data = array_merge($this->quality_data ?? [], $qualityData);
            $this->quality_checked_at = now();
            $this->quality_checked_by = $checkedBy ?? auth()->id();
            $this->save();

            // 记录操作历史
            $this->recordOperation('quality_check', [
                'quality_status' => $status,
                'quality_data' => $qualityData,
                'checked_by' => $this->quality_checked_by,
            ]);

            // 根据检验结果自动更新批次状态
            if ($status === 'failed') {
                $this->updateStatus('quarantine', '质量检验不合格');
            } elseif ($status === 'passed' && $this->status === 'quarantine') {
                $this->updateStatus('normal', '质量检验合格');
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('批次质量检验失败', [
                'batch_id' => $this->id,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 记录操作历史
     * 
     * @param string $operationType 操作类型
     * @param array $operationData 操作数据
     * @return void
     */
    public function recordOperation($operationType, $operationData = [])
    {
        try {
            BatchOperation::create([
                'batch_id' => $this->id,
                'operation_type' => $operationType,
                'quantity_before' => $operationData['quantity_before'] ?? null,
                'quantity_after' => $operationData['quantity_after'] ?? null,
                'quantity_change' => $operationData['quantity_change'] ?? null,
                'status_before' => $operationData['status_before'] ?? null,
                'status_after' => $operationData['status_after'] ?? null,
                'operation_data' => $operationData,
                'reason' => $operationData['reason'] ?? null,
                'notes' => $operationData['notes'] ?? null,
                'reference_type' => $operationData['reference_type'] ?? null,
                'reference_id' => $operationData['reference_id'] ?? null,
                'reference_no' => $operationData['reference_no'] ?? null,
                'warehouse_id' => $operationData['warehouse_id'] ?? $this->inventory->warehouse_id ?? null,
                'operated_by' => $operationData['operated_by'] ?? auth()->id(),
                'operated_at' => $operationData['operated_at'] ?? now(),
            ]);
        } catch (\Exception $e) {
            Log::error('记录批次操作历史失败', [
                'batch_id' => $this->id,
                'operation_type' => $operationType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 处理状态变更的后续操作
     * 
     * @param string $oldStatus 旧状态
     * @param string $newStatus 新状态
     * @return void
     */
    protected function handleStatusChange($oldStatus, $newStatus)
    {
        // 过期处理
        if ($newStatus === 'expired' && $this->auto_dispose_expired) {
            // 自动处置过期商品的逻辑
            $this->updateStatus('disposed', '自动处置过期商品');
        }

        // 状态变更通知
        $this->notifyStatusChange($oldStatus, $newStatus);
    }

    /**
     * 发送状态变更通知
     * 
     * @param string $oldStatus 旧状态
     * @param string $newStatus 新状态
     * @return void
     */
    protected function notifyStatusChange($oldStatus, $newStatus)
    {
        // TODO: 实现状态变更通知逻辑
        Log::info('批次状态变更', [
            'batch_id' => $this->id,
            'batch_code' => $this->batch_code,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
        ]);
    }

    /**
     * 自动更新批次状态（基于过期时间等）
     * 
     * @return void
     */
    public function autoUpdateStatus()
    {
        if ($this->isExpired() && $this->status === 'normal') {
            $this->updateStatus('expired', '自动检测到批次已过期');
        } elseif ($this->isNearExpiry() && $this->status === 'normal') {
            $this->updateStatus('near_expiry', '自动检测到批次临近过期');
        }
    }

    /**
     * 获取批次优先级分数（用于出库排序）
     * 
     * @param string $strategy 出库策略
     * @return float
     */
    public function getPriorityScore($strategy = 'fefo')
    {
        $score = 0;

        switch ($strategy) {
            case 'fifo': // 先进先出
                $score = $this->production_date ? $this->production_date->timestamp : 0;
                break;

            case 'lifo': // 后进先出
                $score = $this->production_date ? -$this->production_date->timestamp : 0;
                break;

            case 'fefo': // 先过期先出
                if ($this->expiry_date) {
                    $score = -$this->expiry_date->timestamp; // 越早过期分数越高
                } else {
                    $score = -999999999; // 无过期日期的排在最后
                }
                break;

            case 'quality_first': // 质量优先
                $qualityScore = match($this->quality_status) {
                    'passed' => 100,
                    'partial' => 50,
                    'pending' => 10,
                    'failed' => 0,
                    default => 0
                };
                $expiryScore = $this->expiry_date ? -$this->expiry_date->timestamp / 1000000 : 0;
                $score = $qualityScore + $expiryScore;
                break;

            case 'cost_optimized': // 成本优化
                $score = -($this->unit_cost ?? $this->purchase_price ?? 0); // 成本低的优先
                break;
        }

        return $score;
    }

    /**
     * 获取单位名称
     *
     * @return string
     */
    public function getUnitNameAttribute()
    {
        return $this->unit ? $this->unit->name : '';
    }

    /**
     * 获取单位符号
     *
     * @return string
     */
    public function getUnitSymbolAttribute()
    {
        return $this->unit ? $this->unit->symbol : '';
    }

    /**
     * 获取状态文本
     * 
     * @return string
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'normal' => '正常',
            'near_expiry' => '临期',
            'expired' => '过期',
            'damaged' => '损坏',
            'recalled' => '召回',
            'quarantine' => '隔离',
            'disposed' => '已处置',
            default => '未知'
        };
    }

    /**
     * 获取质量状态文本
     * 
     * @return string
     */
    public function getQualityStatusTextAttribute()
    {
        return match($this->quality_status) {
            'pending' => '待检验',
            'passed' => '合格',
            'failed' => '不合格',
            'partial' => '部分合格',
            default => '未知'
        };
    }

    /**
     * 作用域：正常状态的批次
     */
    public function scopeNormal($query)
    {
        return $query->where('status', 'normal');
    }

    /**
     * 作用域：可用的批次（正常状态且有可用数量）
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'normal')
                    ->where('quality_status', 'passed')
                    ->whereRaw('quantity > locked_quantity');
    }

    /**
     * 作用域：临期批次
     */
    public function scopeNearExpiry($query, $days = 7)
    {
        return $query->whereNotNull('expiry_date')
                    ->where('expiry_date', '>', now())
                    ->where('expiry_date', '<=', now()->addDays($days));
    }

    /**
     * 作用域：过期批次
     */
    public function scopeExpired($query)
    {
        return $query->whereNotNull('expiry_date')
                    ->where('expiry_date', '<', now());
    }

    /**
     * 作用域：按出库策略排序
     */
    public function scopeOrderByStrategy($query, $strategy = 'fefo')
    {
        switch ($strategy) {
            case 'fifo':
                return $query->orderBy('production_date', 'asc')
                           ->orderBy('created_at', 'asc');

            case 'lifo':
                return $query->orderBy('production_date', 'desc')
                           ->orderBy('created_at', 'desc');

            case 'fefo':
                return $query->orderByRaw('CASE WHEN expiry_date IS NULL THEN 1 ELSE 0 END')
                           ->orderBy('expiry_date', 'asc')
                           ->orderBy('production_date', 'asc');

            case 'quality_first':
                return $query->orderByRaw("
                    CASE quality_status 
                        WHEN 'passed' THEN 1 
                        WHEN 'partial' THEN 2 
                        WHEN 'pending' THEN 3 
                        WHEN 'failed' THEN 4 
                        ELSE 5 
                    END
                ")->orderBy('expiry_date', 'asc');

            default:
                return $query->orderBy('created_at', 'asc');
        }
    }
} 