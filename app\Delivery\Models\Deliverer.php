<?php

namespace App\Delivery\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
use App\Employee\Models\Employee;
use App\Delivery\Models\Delivery;

class Deliverer extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 配送员状态常量
     */
    const STATUS_AVAILABLE = 'available';
    const STATUS_BUSY = 'busy';
    const STATUS_OFFLINE = 'offline';
    
    /**
     * 配送员类型常量
     */
    const TYPE_EMPLOYEE = 'employee';

    /**
     * 批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'employee_id',
        'type',
        'delivery_area',
        'max_orders',
        'rating',
        'status',
        'working_hours',
        'transportation',
        'last_location_lat',
        'last_location_lng',
        'last_active_at',
    ];

    /**
     * 应该转换的属性
     *
     * @var array
     */
    protected $casts = [
        'max_orders' => 'integer',
        'rating' => 'float',
        'last_location_lat' => 'float',
        'last_location_lng' => 'float',
        'last_active_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 附加到模型数组形式的属性
     *
     * @var array
     */
    protected $appends = [
        'name', 
        'phone'
    ];

    /**
     * 获取关联的用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    /**
     * 获取关联的员工
     */
    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * 获取关联的配送记录
     */
    public function deliveries()
    {
        return $this->hasMany(Delivery::class);
    }

    /**
     * 获取正在进行的配送
     */
    public function activeDeliveries()
    {
        return $this->deliveries()->whereNotIn('status', ['completed', 'cancelled']);
    }
    
    /**
     * 获取配送员姓名（从关联的员工获取）
     * 
     * @return string
     */
    public function getNameAttribute()
    {
        if ($this->employee) {
            return $this->employee->name;
        }
        
        return '未知配送员';
    }
    
    /**
     * 获取配送员电话（从关联的员工获取）
     * 
     * @return string|null
     */
    public function getPhoneAttribute()
    {
        if ($this->employee) {
            return $this->employee->phone;
        }
        
        return null;
    }

    /**
     * 查询可用的配送员
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', self::STATUS_AVAILABLE);
    }

    /**
     * 查询忙碌的配送员
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBusy($query)
    {
        return $query->where('status', self::STATUS_BUSY);
    }
} 