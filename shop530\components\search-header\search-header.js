Component({
  properties: {
    value: {
      type: String,
      value: ''
    },
    placeholder: {
      type: String,
      value: '搜索商品'
    },
    showSearchButton: {
      type: Boolean,
      value: false
    },
    buttonPosition: {
      type: String,
      value: 'right'
    },
    searchIcon: {
      type: String,
      value: 'search'
    },
    showAction: {
      type: Boolean,
      value: true
    },
    actionText: {
      type: String,
      value: '取消'
    },
    show: {
      type: Boolean,
      value: true
    },
    autoFocus: {
      type: Boolean,
      value: false
    }
  },

  data: {
    focused: false
  },

  lifetimes: {
    attached() {
      // 如果设置了自动获取焦点
      if (this.data.autoFocus) {
        this.setData({
          focused: true
        });
      }
    }
  },

  methods: {
    // 搜索输入变化
    onSearchChange(e) {
      const value = e.detail.value || '';
      this.triggerEvent('change', { value });
    },

    // 搜索提交
    onSearch(e) {
      const value = e.detail.value || '';
      this.triggerEvent('search', { value });
    },

    // 获取焦点
    onSearchFocus() {
      this.setData({
        focused: true
      });
      this.triggerEvent('focus');
    },

    // 失去焦点
    onSearchBlur() {
      // 延迟设置失去焦点状态，以便可以正确处理点击取消按钮
      setTimeout(() => {
        this.setData({
          focused: false
        });
      }, 100);
      this.triggerEvent('blur');
    },

    // 点击输入框
    onClickInput() {
      this.triggerEvent('click-input');
    },

    // 点击取消按钮
    onSearchCancel() {
      this.setData({
        focused: false
      });
      this.triggerEvent('cancel');
    },

    // 点击搜索按钮
    onSearchButtonTap() {
      // 触发搜索事件，使用当前输入值
      const value = this.properties.value || '';
      this.triggerEvent('search', { value });
    },

    // 清除搜索内容
    clearSearch() {
      this.triggerEvent('clear');
      this.triggerEvent('change', { value: '' });
    }
  }
})