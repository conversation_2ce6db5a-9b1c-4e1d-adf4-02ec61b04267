<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 价格计算配置
    |--------------------------------------------------------------------------
    |
    | 这里定义了价格计算相关的配置选项
    |
    */

    // 是否允许区域价格和会员折扣叠加
    'allow_stack_region_member_discount' => env('ALLOW_STACK_REGION_MEMBER_DISCOUNT', false),

    // 价格优先级设置
    'price_priority' => [
        'region',  // 区域价格优先级最高
        'member',  // 会员价格次之
        'base'     // 基础价格最低
    ],

    // 折扣叠加规则
    'discount_stack_rules' => [
        'region_member' => env('ALLOW_REGION_MEMBER_STACK', false),
        'member_coupon' => env('ALLOW_MEMBER_COUPON_STACK', true),
        'region_coupon' => env('ALLOW_REGION_COUPON_STACK', true),
    ],

    // 价格精度设置
    'price_precision' => 2,

    // 最小订单金额
    'minimum_order_amount' => env('MINIMUM_ORDER_AMOUNT', 0),

    // 会员价格设置
    'member_pricing' => [
        // 是否启用会员价格
        'enabled' => env('MEMBER_PRICING_ENABLED', true),
        
        // 会员价格计算方式：只支持 'fixed' 固定价格
        'calculation_method' => 'fixed',
        
        // 是否允许会员价格低于成本价
        'allow_below_cost' => false,
    ],

    // 区域价格设置
    'region_pricing' => [
        // 是否启用区域价格
        'enabled' => env('REGION_PRICING_ENABLED', true),
        
        // 是否继承父区域价格
        'inherit_parent_price' => true,
        
        // 区域价格有效期检查
        'check_validity_period' => true,
    ],

    // 价格显示设置
    'display' => [
        // 是否显示原价
        'show_original_price' => true,
        
        // 是否显示折扣金额
        'show_discount_amount' => true,
        
        // 是否显示价格类型标签
        'show_price_labels' => true,
        
        // 价格格式化
        'currency_symbol' => '¥',
        'decimal_places' => 2,
    ],

    // 价格缓存设置
    'cache' => [
        // 是否启用价格缓存
        'enabled' => env('PRICE_CACHE_ENABLED', true),
        
        // 缓存时间（分钟）
        'ttl' => env('PRICE_CACHE_TTL', 60),
        
        // 缓存键前缀
        'prefix' => 'price_calc',
    ],

    // 价格验证规则
    'validation' => [
        // 最大折扣率（百分比）
        'max_discount_rate' => 90,
        
        // 最小价格
        'min_price' => 0.01,
        
        // 最大价格
        'max_price' => 999999.99,
    ],
]; 