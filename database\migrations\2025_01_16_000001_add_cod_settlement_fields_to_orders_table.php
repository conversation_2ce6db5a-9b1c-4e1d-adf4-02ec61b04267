<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // 货到付款结算相关字段 - 放在payment相关字段后面
            $table->decimal('actual_payment_amount', 10, 2)->nullable()->after('payment_link_expires_at')->comment('实际收款金额（货到付款）');
            $table->decimal('amount_difference', 10, 2)->nullable()->after('actual_payment_amount')->comment('金额差异（避免重复计算）');
            $table->enum('settlement_status', ['none', 'pending_settlement', 'cash_settled', 'wechat_settled', 'completed'])->default('none')->after('amount_difference')->comment('结算状态');
            $table->enum('settlement_method', ['cash', 'wechat'])->nullable()->after('settlement_status')->comment('结算方式');
            $table->timestamp('settlement_completed_at')->nullable()->after('settlement_method')->comment('结算完成时间');
            $table->foreignId('settlement_operator_id')->nullable()->after('settlement_completed_at')->constrained('employees')->onDelete('set null')->comment('结算操作员ID');
            
            // 性能优化索引
            $table->index(['settlement_status', 'settlement_method'], 'idx_settlement_status_method');
            $table->index('settlement_completed_at', 'idx_settlement_completed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // 删除索引
            $table->dropIndex('idx_settlement_status_method');
            $table->dropIndex('idx_settlement_completed_at');
            
            // 删除外键和字段
            $table->dropForeign(['settlement_operator_id']);
            $table->dropColumn([
                'actual_payment_amount',
                'amount_difference',
                'settlement_status',
                'settlement_method',
                'settlement_completed_at',
                'settlement_operator_id'
            ]);
        });
    }
}; 