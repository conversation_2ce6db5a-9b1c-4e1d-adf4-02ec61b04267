<?php

namespace App\Product\Http\Controllers;use App\Product\Models\Category;
use App\Api\Models\ApiResponse;
use App\Product\Models\Product;
use App\Product\Services\CategoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class CategoryController extends Controller
{
    /**
     * 分类服务
     */
    protected $categoryService;
    
    /**
     * 构造函数
     */
    public function __construct(CategoryService $categoryService = null)
    {
        $this->categoryService = $categoryService ?: app(CategoryService::class);
    }
    
    /**
     * 获取分类列表（分页）
     */
    public function index(Request $request)
    {
        try {
            $filters = [
                'keyword' => $request->input('keyword'),
                'status' => $request->input('status'),
                'parent_id' => $request->input('parent_id'),
            ];
            
            // 如果请求不分页（用于树形视图），则获取所有数据
            $noPagination = $request->input('no_pagination', false);
            
            if ($noPagination) {
                $categories = $this->categoryService->getAllCategories($filters);
            } else {
                $categories = $this->categoryService->getCategories($filters, $request->input('limit', 10));
            }
            
            return response()->json(ApiResponse::success($categories));
        } catch (\Exception $e) {
            Log::error('获取分类列表失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取分类列表失败: ' . $e->getMessage()), 500);
        }
    }

    /**
     * 创建分类
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'description' => 'nullable|string|max:255',
            'parent_id' => 'nullable|integer|min:0',
            'sort' => 'nullable|integer|min:0',
            'status' => 'nullable|in:0,1',
            'image_url' => 'nullable|string|max:255',
            'icon' => 'nullable|string|max:50',
            'custom_icon_url' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            $category = Category::create([
                'name' => $request->name,
                'description' => $request->description ?? '',
                'parent_id' => $request->parent_id ?? 0,
                'sort' => $request->sort ?? 0,
                'status' => $request->has('status') ? (int)$request->status : 1,
                'image_url' => $request->image_url ?? '',
                'icon' => $request->icon ?? '',
                'custom_icon_url' => $request->custom_icon_url ?? '',
            ]);

            return response()->json(ApiResponse::success($category, '创建成功'));
        } catch (\Exception $e) {
            Log::error('分类创建失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('创建失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取分类详情
     */
    public function show($id)
    {
        $category = Category::find($id);
        
        if (!$category) {
            return response()->json(ApiResponse::error('分类不存在', 404), 404);
        }
        
        return response()->json(ApiResponse::success($category, '获取成功'));
    }

    /**
     * 更新分类
     */
    public function update(Request $request, $id)
    {
        $category = Category::find($id);
        
        if (!$category) {
            return response()->json(ApiResponse::error('分类不存在', 404), 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:50',
            'description' => 'nullable|string|max:255',
            'parent_id' => 'nullable|integer|min:0',
            'sort' => 'nullable|integer|min:0',
            'status' => 'nullable|in:0,1',
            'image_url' => 'nullable|string|max:255',
            'icon' => 'nullable|string|max:50',
            'custom_icon_url' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            // 防止设置自己为自己的父类
            if ($request->has('parent_id') && $request->parent_id == $id) {
                return response()->json(ApiResponse::error('不能将分类设为自己的父类', 400), 400);
            }
            
            // 防止设置自己的后代为自己的父类（形成循环）
            if ($request->has('parent_id') && $request->parent_id > 0) {
                $potentialParent = Category::find($request->parent_id);
                if (!$potentialParent) {
                    return response()->json(ApiResponse::error('父分类不存在', 404), 404);
                }
                
                if ($potentialParent->isDescendantOf($id)) {
                    return response()->json(ApiResponse::error('不能将自己的后代设为自己的父类', 400), 400);
                }
            }

            $category->fill($request->only([
                'name', 'description', 'parent_id', 'sort', 'status', 'image_url', 'icon', 'custom_icon_url'
            ]));
            $category->save();

            return response()->json(ApiResponse::success($category, '更新成功'));
        } catch (\Exception $e) {
            Log::error('分类更新失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('更新失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 删除分类
     */
    public function destroy($id)
    {
        $category = Category::find($id);
        
        if (!$category) {
            return response()->json(ApiResponse::error('分类不存在', 404), 404);
        }

        // 检查是否有子分类
        if ($category->hasChildren()) {
            return response()->json(ApiResponse::error('该分类下有子分类，不能删除', 400), 400);
        }
        
        // 检查是否有关联的商品
        $hasProducts = Product::where('category_id', $id)->exists();
        if ($hasProducts) {
            return response()->json(ApiResponse::error('该分类下有商品，不能删除', 400), 400);
        }

        try {
            // 如果有图片，也删除图片文件
            if (!empty($category->image_url) && Storage::exists(str_replace('/storage/', 'public/', $category->image_url))) {
                Storage::delete(str_replace('/storage/', 'public/', $category->image_url));
            }
            
            $category->delete();
            return response()->json(ApiResponse::success(null, '删除成功'));
        } catch (\Exception $e) {
            Log::error('分类删除失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('删除失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取分类树结构
     */
    public function tree(Request $request)
    {
        try {
            $status = $request->has('status') ? (int)$request->status : 1;
            $parentId = $request->input('parent_id', 0);
            
            // 如果指定了parent_id，使用原来的方法
            if ($parentId > 0) {
                $tree = Category::getTree($parentId, ['status' => $status]);
            } else {
                // 使用优化后的树形结构方法（减少数据库查询）
                $tree = Category::getOptimizedTree(['status' => $status]);
            }
            
            return response()->json(ApiResponse::success($tree, '获取成功'));
        } catch (\Exception $e) {
            Log::error('获取分类树失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取分类树失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取扁平化的分类列表（包含层级信息）
     */
    public function flat(Request $request)
    {
        try {
            $status = $request->has('status') ? (int)$request->status : 1;
            
            // 获取扁平化的分类列表
            $flatCategories = Category::getFlatList(['status' => $status]);
            
            return response()->json(ApiResponse::success($flatCategories, '获取成功'));
        } catch (\Exception $e) {
            Log::error('获取扁平分类列表失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取扁平分类列表失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取分类的所有祖先
     */
    public function ancestors($id)
    {
        try {
            $category = Category::find($id);
            
            if (!$category) {
                return response()->json(ApiResponse::error('分类不存在', 404), 404);
            }
            
            $ancestors = $category->getAncestors();
            
            return response()->json(ApiResponse::success($ancestors, '获取成功'));
        } catch (\Exception $e) {
            Log::error('获取分类祖先失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取分类祖先失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取分类的所有后代
     */
    public function descendants($id, Request $request)
    {
        try {
            $category = Category::find($id);
            
            if (!$category) {
                return response()->json(ApiResponse::error('分类不存在', 404), 404);
            }
            
            $activeOnly = $request->input('active_only', false);
            $descendants = $category->getAllDescendants($activeOnly);
            
            return response()->json(ApiResponse::success($descendants, '获取成功'));
        } catch (\Exception $e) {
            Log::error('获取分类后代失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取分类后代失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取分类路径（面包屑）
     */
    public function breadcrumb($id)
    {
        try {
            $category = Category::find($id);
            
            if (!$category) {
                return response()->json(ApiResponse::error('分类不存在', 404), 404);
            }
            
            $breadcrumb = $category->getBreadcrumb();
            
            return response()->json(ApiResponse::success($breadcrumb, '获取成功'));
        } catch (\Exception $e) {
            Log::error('获取分类路径失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('获取分类路径失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 上传分类图片
     */
    public function uploadImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|image|max:2048', // 最大2MB
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        try {
            $image = $request->file('image');
            $path = $image->store('public/categories');
            $url = Storage::url($path);

            return response()->json(ApiResponse::success([
                'path' => $path,
                'url' => $url
            ], '上传成功'));
        } catch (\Exception $e) {
            Log::error('分类图片上传失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('上传失败: ' . $e->getMessage(), 500), 500);
        }
    }
} 