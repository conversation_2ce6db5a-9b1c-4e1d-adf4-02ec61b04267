<?php

namespace App\Inventory\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Product\Models\Product;
use App\Warehouse\Models\Warehouse;
use App\Supplier\Models\Supplier;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AutoReorderRule extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'warehouse_id',
        'supplier_id',
        'name',
        'description',
        'is_active',
        'trigger_type',
        'reorder_point',
        'reorder_quantity',
        'max_stock_level',
        'trigger_conditions',
        'lead_time_days',
        'safety_stock',
        'reorder_strategy',
        'strategy_params',
        'unit_cost',
        'min_order_quantity',
        'order_multiple',
        'schedule_config',
        'last_triggered_at',
        'next_check_at',
        'trigger_count',
        'success_count',
        'last_success_at',
        'require_approval',
        'approval_threshold',
        'approval_workflow',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'trigger_conditions' => 'array',
        'strategy_params' => 'array',
        'schedule_config' => 'array',
        'approval_workflow' => 'array',
        'last_triggered_at' => 'datetime',
        'next_check_at' => 'datetime',
        'last_success_at' => 'datetime',
        'require_approval' => 'boolean',
        'reorder_point' => 'decimal:2',
        'reorder_quantity' => 'decimal:2',
        'max_stock_level' => 'decimal:2',
        'safety_stock' => 'decimal:2',
        'unit_cost' => 'decimal:2',
        'min_order_quantity' => 'decimal:2',
        'order_multiple' => 'decimal:2',
        'approval_threshold' => 'decimal:2',
        'lead_time_days' => 'integer',
        'trigger_count' => 'integer',
        'success_count' => 'integer',
    ];

    /**
     * 关联商品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 关联仓库
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * 关联供应商
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * 关联补货记录
     */
    public function reorderRecords()
    {
        return $this->hasMany(ReorderRecord::class, 'reorder_rule_id');
    }

    /**
     * 检查是否应该触发补货
     * 
     * @return array ['should_trigger' => bool, 'reason' => string, 'data' => array]
     */
    public function shouldTrigger()
    {
        if (!$this->is_active) {
            return ['should_trigger' => false, 'reason' => '规则未启用', 'data' => []];
        }

        $product = $this->product;
        if (!$product) {
            return ['should_trigger' => false, 'reason' => '商品不存在', 'data' => []];
        }

        $currentStock = $product->stock;
        $data = [
            'current_stock' => $currentStock,
            'reorder_point' => $this->reorder_point,
            'safety_stock' => $this->safety_stock,
            'trigger_type' => $this->trigger_type,
        ];

        switch ($this->trigger_type) {
            case 'stock_level':
                return $this->checkStockLevelTrigger($currentStock, $data);
            
            case 'sales_velocity':
                return $this->checkSalesVelocityTrigger($currentStock, $data);
            
            case 'time_based':
                return $this->checkTimeBasedTrigger($currentStock, $data);
            
            case 'combined':
                return $this->checkCombinedTrigger($currentStock, $data);
            
            default:
                return ['should_trigger' => false, 'reason' => '未知触发类型', 'data' => $data];
        }
    }

    /**
     * 检查库存水平触发条件
     */
    protected function checkStockLevelTrigger($currentStock, $data)
    {
        if ($currentStock <= $this->reorder_point) {
            return [
                'should_trigger' => true,
                'reason' => "库存({$currentStock})低于补货点({$this->reorder_point})",
                'data' => $data
            ];
        }

        return ['should_trigger' => false, 'reason' => '库存充足', 'data' => $data];
    }

    /**
     * 检查销售速度触发条件
     */
    protected function checkSalesVelocityTrigger($currentStock, $data)
    {
        // 获取销售速度配置
        $velocityConfig = $this->trigger_conditions['sales_velocity'] ?? [];
        $days = $velocityConfig['days'] ?? 30;
        $threshold = $velocityConfig['threshold'] ?? 0;

        // 计算销售速度（这里需要根据实际的订单系统来实现）
        // 暂时使用模拟数据
        $salesVelocity = $this->calculateSalesVelocity($days);
        $data['sales_velocity'] = $salesVelocity;
        $data['velocity_threshold'] = $threshold;

        // 预测库存耗尽时间
        $daysToStockOut = $salesVelocity > 0 ? $currentStock / $salesVelocity : 999;
        $data['days_to_stock_out'] = $daysToStockOut;

        if ($daysToStockOut <= $this->lead_time_days) {
            return [
                'should_trigger' => true,
                'reason' => "预计{$daysToStockOut}天后缺货，小于采购周期({$this->lead_time_days}天)",
                'data' => $data
            ];
        }

        return ['should_trigger' => false, 'reason' => '销售速度正常', 'data' => $data];
    }

    /**
     * 检查时间触发条件
     */
    protected function checkTimeBasedTrigger($currentStock, $data)
    {
        $scheduleConfig = $this->schedule_config ?? [];
        $frequency = $scheduleConfig['frequency'] ?? 'weekly';
        
        if (!$this->next_check_at || $this->next_check_at <= now()) {
            // 更新下次检查时间
            $this->updateNextCheckTime();
            
            return [
                'should_trigger' => true,
                'reason' => "定时触发({$frequency})",
                'data' => $data
            ];
        }

        return ['should_trigger' => false, 'reason' => '未到定时触发时间', 'data' => $data];
    }

    /**
     * 检查组合触发条件
     */
    protected function checkCombinedTrigger($currentStock, $data)
    {
        $conditions = $this->trigger_conditions['combined'] ?? [];
        $requiredConditions = $conditions['required'] ?? 1;
        $metConditions = 0;

        // 检查库存条件
        if (isset($conditions['stock_level']) && $currentStock <= $this->reorder_point) {
            $metConditions++;
        }

        // 检查销售速度条件
        if (isset($conditions['sales_velocity'])) {
            $velocityResult = $this->checkSalesVelocityTrigger($currentStock, $data);
            if ($velocityResult['should_trigger']) {
                $metConditions++;
            }
        }

        // 检查时间条件
        if (isset($conditions['time_based'])) {
            $timeResult = $this->checkTimeBasedTrigger($currentStock, $data);
            if ($timeResult['should_trigger']) {
                $metConditions++;
            }
        }

        $data['met_conditions'] = $metConditions;
        $data['required_conditions'] = $requiredConditions;

        if ($metConditions >= $requiredConditions) {
            return [
                'should_trigger' => true,
                'reason' => "满足{$metConditions}/{$requiredConditions}个触发条件",
                'data' => $data
            ];
        }

        return ['should_trigger' => false, 'reason' => '未满足足够的触发条件', 'data' => $data];
    }

    /**
     * 计算补货数量
     * 
     * @param float $currentStock 当前库存
     * @return float
     */
    public function calculateReorderQuantity($currentStock)
    {
        switch ($this->reorder_strategy) {
            case 'fixed_quantity':
                return $this->reorder_quantity;
            
            case 'target_level':
                $targetLevel = $this->strategy_params['target_level'] ?? $this->max_stock_level ?? ($this->reorder_point * 2);
                return max(0, $targetLevel - $currentStock);
            
            case 'economic_order':
                return $this->calculateEconomicOrderQuantity();
            
            case 'dynamic':
                return $this->calculateDynamicQuantity($currentStock);
            
            default:
                return $this->reorder_quantity;
        }
    }

    /**
     * 计算经济订货量
     */
    protected function calculateEconomicOrderQuantity()
    {
        $params = $this->strategy_params['economic_order'] ?? [];
        $annualDemand = $params['annual_demand'] ?? 1000;
        $orderingCost = $params['ordering_cost'] ?? 50;
        $holdingCost = $params['holding_cost'] ?? 10;

        if ($holdingCost <= 0) {
            return $this->reorder_quantity;
        }

        $eoq = sqrt((2 * $annualDemand * $orderingCost) / $holdingCost);
        
        // 考虑最小订货量和订货倍数
        if ($this->min_order_quantity && $eoq < $this->min_order_quantity) {
            $eoq = $this->min_order_quantity;
        }

        if ($this->order_multiple && $this->order_multiple > 0) {
            $eoq = ceil($eoq / $this->order_multiple) * $this->order_multiple;
        }

        return $eoq;
    }

    /**
     * 计算动态补货数量
     */
    protected function calculateDynamicQuantity($currentStock)
    {
        // 基于销售速度和采购周期的动态计算
        $salesVelocity = $this->calculateSalesVelocity(30);
        $demandDuringLeadTime = $salesVelocity * $this->lead_time_days;
        $quantity = $demandDuringLeadTime + $this->safety_stock - $currentStock;

        return max($this->min_order_quantity ?? 0, $quantity);
    }

    /**
     * 计算销售速度
     */
    protected function calculateSalesVelocity($days)
    {
        // TODO: 实现基于实际订单数据的销售速度计算
        // 这里返回模拟数据
        return 1.5; // 每天销售1.5件
    }

    /**
     * 触发补货
     * 
     * @param int|null $triggeredBy 触发人员ID
     * @return ReorderRecord|null
     */
    public function trigger($triggeredBy = null)
    {
        $triggerResult = $this->shouldTrigger();
        
        if (!$triggerResult['should_trigger']) {
            Log::info('补货规则检查：不需要触发', [
                'rule_id' => $this->id,
                'reason' => $triggerResult['reason']
            ]);
            return null;
        }

        $currentStock = $this->product->stock;
        $reorderQuantity = $this->calculateReorderQuantity($currentStock);

        // 创建补货记录
        $reorderRecord = ReorderRecord::create([
            'reorder_rule_id' => $this->id,
            'product_id' => $this->product_id,
            'warehouse_id' => $this->warehouse_id,
            'supplier_id' => $this->supplier_id,
            'reorder_no' => ReorderRecord::generateReorderNo(),
            'trigger_reason' => $this->getTriggerReasonFromType(),
            'trigger_data' => $triggerResult['data'],
            'stock_before' => $currentStock,
            'reorder_point' => $this->reorder_point,
            'requested_quantity' => $reorderQuantity,
            'unit_cost' => $this->unit_cost,
            'estimated_cost' => $reorderQuantity * ($this->unit_cost ?? 0),
            'triggered_at' => now(),
            'triggered_by' => $triggeredBy,
            'expected_delivery_at' => now()->addDays($this->lead_time_days),
        ]);

        // 更新规则统计
        $this->update([
            'last_triggered_at' => now(),
            'trigger_count' => $this->trigger_count + 1,
        ]);

        Log::info('自动补货触发成功', [
            'rule_id' => $this->id,
            'record_id' => $reorderRecord->id,
            'reason' => $triggerResult['reason'],
            'quantity' => $reorderQuantity
        ]);

        return $reorderRecord;
    }

    /**
     * 更新下次检查时间
     */
    protected function updateNextCheckTime()
    {
        $scheduleConfig = $this->schedule_config ?? [];
        $frequency = $scheduleConfig['frequency'] ?? 'daily';

        $nextCheck = match($frequency) {
            'hourly' => now()->addHour(),
            'daily' => now()->addDay(),
            'weekly' => now()->addWeek(),
            'monthly' => now()->addMonth(),
            default => now()->addDay()
        };

        $this->update(['next_check_at' => $nextCheck]);
    }

    /**
     * 获取触发原因
     */
    protected function getTriggerReasonFromType()
    {
        return match($this->trigger_type) {
            'stock_level' => 'low_stock',
            'sales_velocity' => 'sales_velocity',
            'time_based' => 'scheduled',
            'combined' => 'low_stock',
            default => 'manual'
        };
    }

    /**
     * 作用域：活跃规则
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：需要检查的规则
     */
    public function scopeNeedsCheck($query)
    {
        return $query->active()
            ->where(function($q) {
                $q->whereNull('next_check_at')
                  ->orWhere('next_check_at', '<=', now());
            });
    }

    /**
     * 获取规则状态文本
     */
    public function getStatusTextAttribute()
    {
        return $this->is_active ? '启用' : '禁用';
    }

    /**
     * 获取触发类型文本
     */
    public function getTriggerTypeTextAttribute()
    {
        return match($this->trigger_type) {
            'stock_level' => '库存水平',
            'sales_velocity' => '销售速度',
            'time_based' => '定时触发',
            'manual' => '手动触发',
            'combined' => '组合条件',
            default => '未知'
        };
    }

    /**
     * 获取补货策略文本
     */
    public function getReorderStrategyTextAttribute()
    {
        return match($this->reorder_strategy) {
            'fixed_quantity' => '固定数量',
            'economic_order' => '经济订货量',
            'target_level' => '目标库存',
            'dynamic' => '动态计算',
            default => '未知'
        };
    }
} 