# 打印模块说明文档

## 1. 功能介绍

打印模块是一个基于Laravel的灵活的打印解决方案，支持小票打印、票据打印以及自定义打印模板。主要特点包括：

- 模块化设计，易于扩展
- 支持多种打印驱动（当前实现了C-Lodop驱动）
- 基于模板的打印系统，支持使用Blade模板
- 自定义格式化器，支持将业务数据转换为打印格式
- 完整的打印历史记录
- 多打印机管理
- 前端预览功能

## 2. 架构设计

### 2.1 核心组件

- **PrintDriverInterface**: 打印驱动接口，定义所有打印驱动必须实现的方法
- **TemplateEngineInterface**: 模板引擎接口，定义模板渲染功能
- **FormatterInterface**: 格式化器接口，将业务数据转换为模板数据
- **PrintingService**: 打印服务，协调打印驱动、模板引擎和格式化器
- **PrintTemplate**: 打印模板模型，管理模板内容和版本
- **Printer**: 打印机模型，管理打印机配置和状态
- **PrintJob**: 打印任务模型，记录打印历史

### 2.2 目录结构

```
app/Printing/
├── Config/                   # 配置文件
├── Contracts/                # 接口定义
├── database/                 # 数据库迁移
│   └── migrations/
├── Formatters/               # 格式化器
├── Http/                     # HTTP相关
│   ├── Controllers/          # 控制器
│   ├── Requests/             # 请求验证
│   └── Resources/            # API资源
├── Jobs/                     # 队列任务
├── Models/                   # 数据模型
├── Providers/                # 服务提供者
├── resources/                # 资源文件
│   ├── js/                   # JavaScript文件
│   └── views/                # 视图文件
│       └── templates/        # 打印模板
├── routes/                   # 路由定义
├── Services/                 # 服务类
│   └── Drivers/              # 打印驱动
└── Templates/                # 模板引擎
    ├── Engine/               # 模板引擎实现
    └── Repository/           # 模板仓库
```

## 3. 安装与配置

### 3.1 安装步骤

1. 在`config/app.php`中注册服务提供者：
```php
'providers' => [
    // ...
    App\Printing\Providers\PrintingServiceProvider::class,
],
```

2. 运行数据库迁移：
```bash
php artisan migrate
```

3. 发布配置和资源文件：
```bash
php artisan vendor:publish --tag=printing-config
php artisan vendor:publish --tag=printing-views
php artisan vendor:publish --tag=printing-assets
```

### 3.2 配置C-Lodop

要使用C-Lodop打印功能，需要进行以下配置：

1. 安装C-Lodop打印服务
2. 在`.env`文件中配置C-Lodop服务：
```
CLODOP_SERVICE_URL=http://localhost:8000/CLodopfuncs.js
CLODOP_LICENSE=
CLODOP_DEFAULT_PRINTER=
```

## 4. 使用说明

### 4.1 创建打印模板

通过管理界面或API创建打印模板，模板使用Blade语法编写。

示例小票模板：
```html
<div class="receipt">
    <div class="header">
        <div class="store-name">{{ $store['name'] }}</div>
    </div>
    
    <div class="info">
        <div class="info-item">单号: {{ $order['number'] }}</div>
        <div class="info-item">日期: {{ $order['date'] }}</div>
    </div>
    
    <table class="table">
        <thead>
            <tr>
                <th>商品</th>
                <th>单价</th>
                <th>数量</th>
                <th>小计</th>
            </tr>
        </thead>
        <tbody>
            @foreach($items as $item)
            <tr>
                <td>{{ $item['name'] }}</td>
                <td>{{ $item['price'] }}</td>
                <td>{{ $item['quantity'] }}</td>
                <td>{{ $item['subtotal'] }}</td>
            </tr>
            @endforeach
        </tbody>
        <tfoot>
            <tr>
                <td colspan="3">合计:</td>
                <td>{{ $order['total_amount'] }}</td>
            </tr>
        </tfoot>
    </table>
</div>
```

### 4.2 打印API使用

#### 打印小票
```php
// 在控制器中
$printingService = app('printing');
$order = Order::find($orderId);
$options = [
    'printer_name' => 'POS-58',
    'copies' => 2
];
$printingService->printObject($order, 'receipt', $options);
```

#### 打印文本
```php
$printingService = app('printing');
$content = "这是一段测试文本\n这是第二行";
$options = [
    'printer_name' => 'POS-58',
    'font_size' => 12
];
$printingService->printText($content, $options);
```

#### 打印HTML
```php
$printingService = app('printing');
$html = "<h1>测试标题</h1><p>这是一段测试内容</p>";
$options = [
    'printer_name' => 'HP LaserJet'
];
$printingService->printHtml($html, $options);
```

#### 打印自定义模板
```php
$printingService = app('printing');
$template = PrintTemplate::find($templateId);
$data = [
    'title' => '测试标题',
    'content' => '测试内容',
    'items' => [
        ['name' => '商品1', 'price' => 10.00],
        ['name' => '商品2', 'price' => 20.00]
    ]
];
$printingService->printTemplate($template, $data);
```

### 4.3 创建自定义格式化器

要为特定业务数据创建格式化器，需要实现`FormatterInterface`接口：

```php
namespace App\Printing\Formatters;

use App\Printing\Contracts\FormatterInterface;
use App\Invoice\Models\Invoice;

class InvoiceFormatter implements FormatterInterface
{
    public function format($data, array $options = []): array
    {
        if (!$data instanceof Invoice) {
            throw new \InvalidArgumentException('数据必须是Invoice实例');
        }
        
        // 格式化发票数据
        return [
            'invoice_number' => $data->number,
            'date' => $data->created_at->format('Y-m-d'),
            'customer' => [
                'name' => $data->customer->name,
                'address' => $data->customer->address
            ],
            'items' => $data->items->map(function($item) {
                return [
                    'name' => $item->product->name,
                    'quantity' => $item->quantity,
                    'price' => $item->price,
                    'subtotal' => $item->quantity * $item->price
                ];
            })->toArray(),
            'total' => $data->total_amount
        ];
    }
    
    public function getSupportedTemplates(): array
    {
        return ['invoice', 'invoice_detailed'];
    }
    
    public function canHandle($data): bool
    {
        return $data instanceof Invoice;
    }
    
    public function getName(): string
    {
        return 'invoice';
    }
    
    public function getSupportedTypes(): array
    {
        return [Invoice::class];
    }
}
```

然后在服务提供者中注册这个格式化器：

```php
// 在PrintingServiceProvider的register方法中
$this->app->singleton('printing', function ($app) {
    $driver = $app->make(PrintDriverInterface::class);
    $templateEngine = $app->make(TemplateEngineInterface::class);
    
    $service = new PrintingService($driver, $templateEngine);
    
    // 注册格式化器
    $service->registerFormatter(new ReceiptFormatter());
    $service->registerFormatter(new InvoiceFormatter());  // 新增
    
    return $service;
});
```

### 4.4 前端集成

#### 引入C-Lodop辅助脚本
```html
<script src="{{ asset('vendor/printing/clodop-helper.js') }}"></script>
```

#### 初始化和使用
```javascript
// 初始化CLodopHelper
const clodopHelper = new CLodopHelper({
    serviceUrl: 'http://localhost:8000/CLodopfuncs.js',
    defaultPrinter: 'POS-58'
});

// 打印HTML
async function printHtml() {
    try {
        const html = document.getElementById('preview').innerHTML;
        await clodopHelper.printHtml(html, {
            printer_name: 'POS-58',
            copies: 2
        });
        alert('打印成功');
    } catch (error) {
        alert('打印失败: ' + error.message);
    }
}
```

## 5. 扩展开发

### 5.1 添加新的打印驱动

创建新的打印驱动类，实现`PrintDriverInterface`接口：

```php
namespace App\Printing\Services\Drivers;

use App\Printing\Contracts\PrintDriverInterface;

class NewPrinterDriver implements PrintDriverInterface
{
    // 实现接口中定义的所有方法
}
```

在服务提供者中注册新的驱动：

```php
// 在PrintingServiceProvider的register方法中
$this->app->bind(PrintDriverInterface::class, function ($app) {
    $driver = config('printing.default');
    
    switch ($driver) {
        case 'new_driver':
            return new NewPrinterDriver();
        case 'clodop':
        default:
            $config = config('printing.drivers.clodop');
            $driver = new CLodopDriver();
            $driver->initialize($config);
            return $driver;
    }
});
```

### 5.2 添加新的模板引擎

创建新的模板引擎类，实现`TemplateEngineInterface`接口：

```php
namespace App\Printing\Templates\Engine;

use App\Printing\Contracts\TemplateEngineInterface;

class NewTemplateEngine implements TemplateEngineInterface
{
    // 实现接口中定义的所有方法
}
```

在服务提供者中注册新的模板引擎：

```php
// 在PrintingServiceProvider的register方法中
$this->app->bind(TemplateEngineInterface::class, function ($app) {
    $engine = config('printing.template_engine', 'blade');
    
    switch ($engine) {
        case 'new_engine':
            return new NewTemplateEngine();
        case 'blade':
        default:
            return new BladeTemplateEngine();
    }
});
```

## 6. 故障排除

### 6.1 常见问题

1. **C-Lodop未安装或未启动**
   - 确保已安装C-Lodop并且服务已启动
   - 检查防火墙设置，确保允许C-Lodop服务通信

2. **打印预览正常但无法打印**
   - 检查打印机是否在线
   - 检查打印机权限
   - 检查C-Lodop服务日志

3. **模板渲染错误**
   - 检查模板语法
   - 检查传递的数据结构是否与模板匹配
   - 使用预览功能验证模板渲染结果

### 6.2 日志与调试

打印模块会记录详细的日志，可以在Laravel日志文件中查看：

```bash
tail -f storage/logs/laravel.log | grep "Printing"
```

可以开启调试模式获取更多日志信息：

```php
// 在config/printing.php中
'debug' => env('PRINTING_DEBUG', false),
```

## 7. 更新日志

### v1.0.0 (2023-06-01)
- 初始版本发布
- 支持C-Lodop打印驱动
- 支持小票打印和文本打印
- 基于Blade的模板系统
- 打印历史记录
- 前端打印预览

## 8. 许可证

本模块采用MIT许可证。 