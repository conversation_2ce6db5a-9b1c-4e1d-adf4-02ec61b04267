<?php

namespace App\shop\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\shop\Models\Banner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PublicController extends Controller
{
    /**
     * 获取轮播图 - 无需认证
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function banners()
    {
        Log::info('公开API - 获取轮播图');
        
        $banners = Banner::active()->get();
        
        return response()->json(ApiResponse::success($banners));
    }

    /**
     * 获取商城基本信息
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getShopInfo()
    {
        Log::info('公开API - 获取商城基本信息');
        
        $shopInfo = [
            'name' => config('app.name', '商城'),
            'description' => '优质商品，优质服务',
            'logo' => '',
            'contact_phone' => '************',
            'service_time' => '9:00-18:00',
        ];
        
        return response()->json(ApiResponse::success($shopInfo));
    }

    /**
     * 获取商城配置
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getShopConfig()
    {
        Log::info('公开API - 获取商城配置');
        
        $config = [
            'delivery_fee' => 0, // 配送费
            'free_delivery_amount' => 0, // 免配送费金额
            'min_order_amount' => 0, // 最小订单金额
            'max_order_amount' => 10000, // 最大订单金额
            'delivery_time_slots' => [
                '09:00-10:00',
                '10:00-11:00',
                '11:00-12:00',
                '14:00-15:00',
                '15:00-16:00',
                '16:00-17:00'
            ],
            'payment_timeout' => 30, // 支付超时时间（分钟）
        ];
        
        return response()->json(ApiResponse::success($config));
    }

    /**
     * 获取支付方式配置
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentMethods()
    {
        Log::info('公开API - 获取支付方式配置');
        
        $paymentMethods = [
            [
                'value' => 'online',
                'name' => '在线支付',
                'icon' => 'gold-coin-o',
                'discount' => 5, // 立减金额
                'discount_type' => 'fixed', // fixed: 固定金额, percent: 百分比
                'description' => '立减5元，更优惠',
                'enabled' => true,
                'sort_order' => 1
            ],
            [
                'value' => 'cod',
                'name' => '货到付款',
                'icon' => 'cash-o',
                'discount' => 0,
                'discount_type' => 'fixed',
                'description' => '送货上门再付款',
                'enabled' => true,
                'sort_order' => 2
            ]
        ];
        
        // 只返回启用的支付方式，按排序顺序
        $enabledMethods = collect($paymentMethods)
            ->where('enabled', true)
            ->sortBy('sort_order')
            ->values()
            ->toArray();
        
        return response()->json(ApiResponse::success($enabledMethods));
    }
} 