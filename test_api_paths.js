// 测试API路径拼接是否正确
const { getCurrentConfig } = require('./shop530/utils/request-config');
const PointsAPI = require('./shop530/utils/pointsApi');

console.log('=== API路径测试 ===');

// 获取当前配置
const config = getCurrentConfig();
console.log('当前配置baseUrl:', config.baseUrl);

// 测试PointsAPI的BASE_PATH
console.log('PointsAPI.BASE_PATH:', PointsAPI.BASE_PATH);

// 模拟API请求路径拼接
function testApiPath(endpoint) {
  const fullUrl = `${config.baseUrl}${PointsAPI.BASE_PATH}${endpoint}`;
  console.log(`端点: ${endpoint} -> 完整URL: ${fullUrl}`);
  return fullUrl;
}

console.log('\n=== 测试各个API端点 ===');
testApiPath('/balance');
testApiPath('/stats');
testApiPath('/products');
testApiPath('/products/categories');
testApiPath('/ranking');

console.log('\n=== 期望的正确路径 ===');
console.log('应该是: http://localhost/api/points/balance');
console.log('应该是: http://localhost/api/points/stats');
console.log('应该是: http://localhost/api/points/products'); 