<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WechatServicePayment extends Model
{
    use HasFactory;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'wechat_service_payments';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'provider_id',
        'sub_merchant_id',
        'order_id',
        'out_trade_no',
        'transaction_id',
        'total_fee',
        'service_fee',
        'settlement_fee',
        'trade_type',
        'trade_state',
        'pay_time',
        'attach',
        'notify_data',
        'prepay_data',
        'error_message',
    ];

    /**
     * 类型转换
     *
     * @var array
     */
    protected $casts = [
        'total_fee' => 'float',
        'service_fee' => 'float',
        'settlement_fee' => 'float',
        'pay_time' => 'datetime',
        'notify_data' => 'array',
        'prepay_data' => 'array',
    ];

    /**
     * 获取该支付记录所属的服务商
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(WechatServiceProvider::class, 'provider_id');
    }

    /**
     * 获取该支付记录所属的子商户
     */
    public function subMerchant(): BelongsTo
    {
        return $this->belongsTo(WechatSubMerchant::class, 'sub_merchant_id');
    }

    /**
     * 获取该支付记录关联的订单
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * 获取该支付记录的所有退款记录
     */
    public function refunds(): HasMany
    {
        return $this->hasMany(WechatServiceRefund::class, 'payment_id');
    }
} 