<?php

namespace App\shop\Http\Controllers;

use App\Http\Controllers\Controller;
use App\shop\Models\Banner;
use Illuminate\Http\Request;

class BannerController extends Controller
{
    /**
     * 获取轮播图列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $banners = Banner::active()->get();
        
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $banners
        ]);
    }
    
    /**
     * 管理员：获取所有轮播图（包括未激活的）
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function all()
    {
        $banners = Banner::orderBy('sort_order', 'asc')->get();
        
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $banners
        ]);
    }
    
    /**
     * 创建轮播图
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'title' => 'required|string|max:255',
            'image_url' => 'required|string|max:255',
            'link_url' => 'nullable|string|max:255',
            'sort_order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
            'start_time' => 'nullable|date',
            'end_time' => 'nullable|date|after_or_equal:start_time',
        ]);
        
        $banner = Banner::create($validatedData);
        
        return response()->json([
            'code' => 200,
            'message' => '创建成功',
            'data' => $banner
        ]);
    }
    
    /**
     * 获取轮播图详情
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $banner = Banner::findOrFail($id);
        
        return response()->json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $banner
        ]);
    }
    
    /**
     * 更新轮播图
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $banner = Banner::findOrFail($id);
        
        $validatedData = $request->validate([
            'title' => 'string|max:255',
            'image_url' => 'string|max:255',
            'link_url' => 'nullable|string|max:255',
            'sort_order' => 'integer',
            'is_active' => 'boolean',
            'start_time' => 'nullable|date',
            'end_time' => 'nullable|date|after_or_equal:start_time',
        ]);
        
        $banner->update($validatedData);
        
        return response()->json([
            'code' => 200,
            'message' => '更新成功',
            'data' => $banner
        ]);
    }
    
    /**
     * 删除轮播图
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $banner = Banner::findOrFail($id);
        $banner->delete();
        
        return response()->json([
            'code' => 200,
            'message' => '删除成功'
        ]);
    }
    
    /**
     * 更新轮播图状态
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $banner = Banner::findOrFail($id);
        
        $validatedData = $request->validate([
            'is_active' => 'required|boolean',
        ]);
        
        $banner->update($validatedData);
        
        return response()->json([
            'code' => 200,
            'message' => '状态更新成功',
            'data' => $banner
        ]);
    }
} 