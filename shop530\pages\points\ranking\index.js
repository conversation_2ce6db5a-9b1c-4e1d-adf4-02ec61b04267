// pages/points/ranking/index.js - 积分排行榜页
const PointsAPI = require('../../../utils/pointsApi');

Page({
  data: {
    // 排行榜数据
    rankingList: [],
    
    // 我的排名信息
    myRanking: {},
    
    // 页面加载状态
    loading: true,
    
    // 当前选中的排行榜类型
    currentType: 'total', // total: 总积分, month: 月度积分, week: 周积分
    
    // 排行榜类型选项
    rankingTypes: [
      { key: 'total', name: '总积分榜', icon: '🏆' },
      { key: 'month', name: '月度榜', icon: '📅' },
      { key: 'week', name: '周榜', icon: '⏰' }
    ],
    
    // 分页信息
    page: 1,
    hasMore: true
  },

  onLoad() {
    this.loadRankingData();
  },

  onPullDownRefresh() {
    this.loadRankingData(true);
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreData();
    }
  },

  // ==================== 数据加载 ====================

  async loadRankingData(refresh = false) {
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        rankingList: []
      });
    }

    wx.showLoading({ title: '加载中...' });

    try {
      const [rankingResult, myRankingResult] = await Promise.all([
        PointsAPI.getPointsRanking({
          type: this.data.currentType,
          page: this.data.page,
          limit: 20
        }),
        PointsAPI.getMyPointsRanking(this.data.currentType)
      ]);

      const newList = refresh ? rankingResult.data.list : [...this.data.rankingList, ...rankingResult.data.list];

      this.setData({
        rankingList: newList,
        myRanking: myRankingResult.data || {},
        hasMore: rankingResult.data.has_more,
        loading: false
      });

    } catch (error) {
      console.error('加载排行榜失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
      wx.stopPullDownRefresh();
    }
  },

  async loadMoreData() {
    this.setData({
      page: this.data.page + 1,
      loading: true
    });

    try {
      const result = await PointsAPI.getPointsRanking({
        type: this.data.currentType,
        page: this.data.page,
        limit: 20
      });

      this.setData({
        rankingList: [...this.data.rankingList, ...result.data.list],
        hasMore: result.data.has_more,
        loading: false
      });

    } catch (error) {
      console.error('加载更多失败:', error);
      this.setData({ 
        loading: false,
        page: this.data.page - 1 // 回退页码
      });
    }
  },

  // ==================== 用户交互 ====================

  onTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    if (type === this.data.currentType) return;

    this.setData({
      currentType: type,
      page: 1,
      rankingList: [],
      hasMore: true
    });

    this.loadRankingData();
  },

  onUserTap(e) {
    const userId = e.currentTarget.dataset.userId;
    if (!userId) return;

    // 跳转到用户详情页（如果有的话）
    wx.showToast({
      title: '用户详情页面开发中',
      icon: 'none'
    });
  },

  onShareRanking() {
    const myRank = this.data.myRanking.rank || '未上榜';
    const points = this.data.myRanking.points || 0;
    
    return {
      title: `我在积分排行榜排名第${myRank}，积分${points}分！`,
      path: '/pages/points/ranking/index',
      imageUrl: '' // 可以设置分享图片
    };
  },

  // ==================== 页面跳转 ====================

  goToPointsMall() {
    wx.switchTab({
      url: '/pages/points/index'
    });
  },

  goToPointsDetail() {
    wx.navigateTo({
      url: '/pages/points/transactions/index'
    });
  },

  // ==================== 工具方法 ====================

  formatPoints(points) {
    return PointsAPI.formatPoints(points);
  },

  getRankIcon(rank) {
    switch (rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return rank;
    }
  },

  getTypeIcon(type) {
    const typeObj = this.data.rankingTypes.find(t => t.key === type);
    return typeObj ? typeObj.icon : '🏆';
  }
}); 