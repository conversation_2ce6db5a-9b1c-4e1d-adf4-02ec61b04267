<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Crm\Models\MembershipLevel;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 获取默认会员等级
        $defaultLevel = MembershipLevel::where('is_default', true)->first();
        if (!$defaultLevel) {
            $defaultLevel = MembershipLevel::first();
        }
        
        if (!$defaultLevel) {
            $this->command->error('没有找到会员等级，请先运行 MembershipLevelSeeder');
            return;
        }
        
        // 创建测试用户
        for ($i = 3; $i <= 20; $i++) {
            User::create([
                'name' => "测试用户{$i}",
                'password' => Hash::make('password'),
                'phone' => "1380000000{$i}",
                'nickname' => "用户昵称{$i}",
                'merchant_name' => "测试商户{$i}号店",
                'avatar' => "https://randomuser.me/api/portraits/" . ($i % 2 == 0 ? 'women' : 'men') . "/" . ($i + 10) . ".jpg",
                'balance' => rand(100, 10000) / 100,
                'member_points' => rand(0, 5000),
                'total_spend' => rand(500, 50000) / 100,
                'largest_order' => rand(100, 5000) / 100,
                'gender' => rand(0, 2),
                'province' => $this->getRandomProvince(),
                'city' => $this->getRandomCity(),
                'country' => '中国',
                'district' => $this->getRandomDistrict(),
                'membership_level_id' => $defaultLevel->id,
                'joined_at' => Carbon::now()->subDays(rand(1, 365)),
                'level_upgraded_at' => Carbon::now()->subDays(rand(1, 180)),
                'created_at' => Carbon::now()->subDays(rand(1, 365)),
                'updated_at' => Carbon::now(),
            ]);
        }
        
        // 创建微信关联用户
        for ($i = 21; $i <= 30; $i++) {
            User::create([
                'name' => "微信用户{$i}",
                'password' => Hash::make('password'),
                'phone' => rand(0, 1) ? "135{$i}000000" : null,
                'nickname' => "微信昵称{$i}",
                'avatar' => "https://randomuser.me/api/portraits/" . ($i % 2 == 0 ? 'women' : 'men') . "/" . ($i + 20) . ".jpg",
                'openid' => 'wx_' . md5("user_{$i}"),
                'unionid' => 'union_' . md5("user_{$i}"),
                'balance' => rand(0, 3000) / 100,
                'member_points' => rand(0, 800),
                'total_spend' => rand(100, 10000) / 100,
                'largest_order' => rand(50, 2000) / 100,
                'gender' => rand(0, 2),
                'province' => $this->getRandomProvince(),
                'city' => $this->getRandomCity(),
                'country' => '中国',
                'district' => $this->getRandomDistrict(),
                'membership_level_id' => $defaultLevel->id,
                'joined_at' => Carbon::now()->subDays(rand(1, 90)),
                'level_upgraded_at' => Carbon::now()->subDays(rand(1, 45)),
                'created_at' => Carbon::now()->subDays(rand(1, 90)),
                'updated_at' => Carbon::now(),
            ]);
        }
        
        $this->command->info('用户数据添加完成！共添加 ' . (18 + 10) . ' 条记录');
        $this->command->info('当前用户总数：' . User::count());
    }
    
    /**
     * 获取随机省份
     */
    private function getRandomProvince()
    {
        $provinces = ['北京市', '上海市', '广东省', '浙江省', '江苏省', '福建省', '湖南省', '湖北省', '四川省', '重庆市'];
        return $provinces[array_rand($provinces)];
    }
    
    /**
     * 获取随机城市
     */
    private function getRandomCity()
    {
        $cities = ['北京', '上海', '广州', '深圳', '杭州', '南京', '苏州', '福州', '厦门', '长沙', '武汉', '成都', '重庆'];
        return $cities[array_rand($cities)];
    }
    
    /**
     * 获取随机区县
     */
    private function getRandomDistrict()
    {
        $districts = ['朝阳区', '海淀区', '西城区', '东城区', '浦东新区', '黄浦区', '徐汇区', '静安区', '天河区', '越秀区'];
        return $districts[array_rand($districts)];
    }
}
