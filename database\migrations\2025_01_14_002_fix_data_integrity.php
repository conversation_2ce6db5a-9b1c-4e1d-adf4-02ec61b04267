<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * 阶段2：修复数据完整性问题
     * - 修复base_unit_id为空的商品
     * - 统一价格字段逻辑
     */
    public function up(): void
    {
        echo "=== 阶段2：修复数据完整性 ===\n";
        
        // 1. 修复base_unit_id为空的商品
        $this->fixMissingBaseUnits();
        
        // 2. 统一价格字段逻辑
        $this->unifyPriceFields();
        
        echo "=== 阶段2完成 ===\n";
    }
    
    /**
     * 修复缺失的base_unit_id
     */
    private function fixMissingBaseUnits(): void
    {
        echo "修复缺失的base_unit_id...\n";
        
        // 获取默认单位（通常是"个"、"件"等）
        $defaultUnit = DB::table('units')
            ->where('name', 'LIKE', '%个%')
            ->orWhere('name', 'LIKE', '%件%')
            ->orWhere('name', 'LIKE', '%piece%')
            ->first();
            
        if (!$defaultUnit) {
            // 如果没有找到合适的默认单位，创建一个
            $defaultUnitId = DB::table('units')->insertGetId([
                'name' => '件',
                'symbol' => 'pcs',
                'category' => 'quantity',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            echo "✅ 创建默认单位 '件' (ID: {$defaultUnitId})\n";
        } else {
            $defaultUnitId = $defaultUnit->id;
            echo "✅ 使用现有单位 '{$defaultUnit->name}' (ID: {$defaultUnitId})\n";
        }
        
        // 更新缺失base_unit_id的商品
        $affectedRows = DB::table('products')
            ->whereNull('base_unit_id')
            ->update(['base_unit_id' => $defaultUnitId]);
            
        echo "✅ 修复了 {$affectedRows} 个商品的base_unit_id\n";
        
        // 为这些商品创建默认的product_units记录
        $productsWithoutUnits = DB::table('products')
            ->where('base_unit_id', $defaultUnitId)
            ->whereNotExists(function($query) {
                $query->select(DB::raw(1))
                      ->from('product_units')
                      ->whereRaw('product_units.product_id = products.id');
            })
            ->get(['id']);
            
        if ($productsWithoutUnits->count() > 0) {
            foreach ($productsWithoutUnits as $product) {
                DB::table('product_units')->insert([
                    'product_id' => $product->id,
                    'unit_id' => $defaultUnitId,
                    'conversion_factor' => 1.00,
                    'roles' => json_encode(['sales', 'purchase', 'inventory']),
                    'role_priority' => json_encode(['sales' => 1, 'purchase' => 1, 'inventory' => 1]),
                    'is_default' => true,
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
            echo "✅ 为 {$productsWithoutUnits->count()} 个商品创建了默认单位关系\n";
        }
    }
    
    /**
     * 统一价格字段逻辑
     */
    private function unifyPriceFields(): void
    {
        echo "统一价格字段逻辑...\n";
        
        // 检查sale_price vs price的情况
        $priceAnalysis = DB::table('products')
            ->selectRaw('
                COUNT(*) as total,
                COUNT(CASE WHEN sale_price IS NOT NULL THEN 1 END) as has_sale_price,
                COUNT(CASE WHEN sale_price IS NOT NULL AND sale_price < price THEN 1 END) as sale_cheaper,
                COUNT(CASE WHEN sale_price IS NOT NULL AND sale_price >= price THEN 1 END) as sale_same_or_higher
            ')
            ->first();
            
        echo "价格分析:\n";
        echo "  - 总商品数: {$priceAnalysis->total}\n";
        echo "  - 有促销价: {$priceAnalysis->has_sale_price}\n";
        echo "  - 促销价更便宜: {$priceAnalysis->sale_cheaper}\n";
        echo "  - 促销价相同或更高: {$priceAnalysis->sale_same_or_higher}\n";
        
        // 清理无效的促销价（促销价≥原价的情况）
        if ($priceAnalysis->sale_same_or_higher > 0) {
            $cleanedRows = DB::table('products')
                ->whereColumn('sale_price', '>=', 'price')
                ->update(['sale_price' => null]);
            echo "✅ 清理了 {$cleanedRows} 个无效促销价记录\n";
        }
        
        // 标记哪些商品正在促销
        $activePromotions = DB::table('products')
            ->whereNotNull('sale_price')
            ->where('sale_price', '<', DB::raw('price'))
            ->count();
            
        echo "✅ 当前有 {$activePromotions} 个商品正在促销\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        echo "注意：数据完整性修复通常不应该回滚\n";
        echo "如需回滚，请手动处理数据\n";
    }
}; 