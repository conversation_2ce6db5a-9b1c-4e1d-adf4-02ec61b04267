<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Admin\Http\Middleware\CheckBackendAccess as AdminCheckBackendAccess;

/**
 * 后台访问控制中间件（代理类）
 * 
 * 该类作为App\Admin\Http\Middleware\CheckBackendAccess的代理
 * 用于保持向后兼容性
 */
class CheckBackendAccess
{
    /**
     * 代理的中间件实例
     *
     * @var \App\Admin\Http\Middleware\CheckBackendAccess
     */
    protected $middleware;

    /**
     * 创建一个新的中间件实例
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware = new AdminCheckBackendAccess();
    }

    /**
     * 处理传入的请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $backend
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, string $backend): Response
    {
        return $this->middleware->handle($request, $next, $backend);
    }
} 