const app = getApp();
const { debounce } = require('../../utils/util');
const { searchProducts, getHotKeywords, getSuggestions } = require('../../api/search');
const { addToCart } = require('../../utils/cart-unified');
const { isLoggedIn } = require('../../utils/login-state-manager');

Page({
  data: {
    statusBarHeight: 44, // 默认值
    searchKeyword: '', // 搜索关键词
    searchHistory: [],
    hotKeywords: [],
    suggestions: [],
    results: [],
    showResults: false,
    loading: false,
    initialLoading: true, // 初次加载标志
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    currentSort: 'default',
    priceSortDirection: 'asc',
    listMode: 'grid', // grid 或 list
    scrollTop: 0,
    isScrolling: false,
    historyVisible: true,
    // 筛选条件
    categoryId: null,
    minPrice: null,
    maxPrice: null,
    tags: [],
    // 错误状态
    error: false,
    errorMsg: '',
    // 请求控制
    requestController: {
      lastRequestId: 0,
      searchDebounceTimer: null,
      reachBottomTimer: null
    }
  },

  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
    
    // 加载搜索历史
    this.loadSearchHistory();
    
    // 加载热门搜索词
    this.loadHotKeywords();
    
    // 如果有搜索参数，直接搜索
    if (options && options.keyword) {
      this.setData({ searchKeyword: options.keyword });
      this.doSearch(options.keyword);
    }
    
    // 初始化防抖搜索函数
    this.debounceSearch = debounce((value) => {
      this.loadSuggestions(value);
    }, 300);
  },
  
  onShow() {
    // 检查是否从登录页回来，如果是，重置加载状态
    if (this.data.loading || this.data.initialLoading) {
      wx.hideLoading();
      this.setData({
        loading: false,
        initialLoading: false
      });
    }
    
    // 如果有搜索关键词且显示结果，刷新结果
    if (this.data.searchKeyword && this.data.showResults) {
      this.refreshResults();
    }
  },
  
  // 加载搜索历史
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync('searchHistory') || [];
      this.setData({ searchHistory: history.slice(0, 10) });
    } catch (e) {
      console.error('加载搜索历史失败', e);
    }
  },
  
  // 保存搜索历史
  saveSearchHistory(keyword) {
    if (!keyword || !keyword.trim()) return;
    
    try {
      let history = wx.getStorageSync('searchHistory') || [];
      
      // 如果已存在，先移除
      const index = history.findIndex(item => item === keyword);
      if (index > -1) {
        history.splice(index, 1);
      }
      
      // 添加到头部
      history.unshift(keyword);
      
      // 限制长度
      if (history.length > 20) {
        history = history.slice(0, 20);
      }
      
      wx.setStorageSync('searchHistory', history);
      this.loadSearchHistory();
    } catch (e) {
      console.error('保存搜索历史失败', e);
    }
  },
  
  // 清除搜索历史
  clearHistory() {
    try {
      wx.removeStorageSync('searchHistory');
      this.setData({ searchHistory: [] });
      wx.showToast({
        title: '已清除搜索历史',
        icon: 'none'
      });
    } catch (e) {
      console.error('清除搜索历史失败', e);
      wx.showToast({
        title: '清除失败，请重试',
        icon: 'none'
      });
    }
  },
  
  // 加载热门搜索词
  loadHotKeywords() {
    this.setData({ loading: true });
    
    getHotKeywords()
      .then(res => {
        if (res && res.data) {
          this.setData({
            hotKeywords: res.data,
            loading: false
          });
        }
      })
      .catch(err => {
        console.error('获取热门搜索词失败', err);
        this.setData({ loading: false });
      });
  },

  // 搜索输入变化
  handleSearchChange(e) {
    console.log('搜索输入变化事件触发', e);
    
    // 确保value是字符串类型
    let value;
    
    if (typeof e === 'string') {
      value = e;
    } else if (e && e.detail) {
      // 处理组件传递的事件对象
      value = e.detail.value !== undefined ? e.detail.value : '';
    } else {
      value = '';
    }
    
    const searchKeyword = String(value || '').trim();
    console.log('处理后的输入关键词:', searchKeyword);
    
    this.setData({ searchKeyword });
    
    // 实时搜索
    if (searchKeyword) {
      this.debounceSearch(searchKeyword);
    } else {
      this.setData({
        suggestions: [],
        historyVisible: true
      });
    }
  },

  // 搜索提交
  handleSearch(e) {
    // 确保value是字符串类型
    let value = '';
    
    if (typeof e === 'string') {
      value = e;
    } else if (e && e.detail && e.detail.value) {
      value = e.detail.value;
    } else if (e && e.detail) {
      value = e.detail.value || '';
    } else {
      value = this.data.searchKeyword || '';
    }
    
    const searchKeyword = value.trim();
    
    if (searchKeyword) {
      this.saveSearchHistory(searchKeyword);
      this.doSearch(searchKeyword);
    } else {
      wx.showToast({
        title: '请输入搜索内容',
        icon: 'none'
      });
    }
  },

  // 取消搜索
  handleSearchCancel() {
    wx.navigateBack();
  },

  // 清除搜索
  handleSearchClear() {
    this.setData({
      searchKeyword: '',
      suggestions: [],
      historyVisible: true,
      showResults: false
    });
  },
  
  // 执行搜索
  doSearch(keyword) {
    if (!keyword) return;
    
    // 重置状态
    this.setData({
      searchKeyword: keyword,
      showResults: true,
      loading: true,
      initialLoading: true,
      results: [],
      currentPage: 1,
      hasMore: true,
      suggestions: [],
      error: false,
      errorMsg: ''
    });
    
    // 执行搜索请求
    wx.showLoading({
      title: '搜索中...',
      mask: true
    });
    
    // 构建搜索参数
    const params = {
      keyword,
      page: 1,
      pageSize: this.data.pageSize || 20, // 增大页面大小，减少请求次数
      sort: this.data.currentSort,
      order: this.data.currentSort === 'price' ? this.data.priceSortDirection : 'desc',
      platform: 'miniprogram',
      useCache: true // 使用缓存提高速度
    };
    
    // 确保categoryId是整数
    if (this.data.categoryId) {
      params.categoryId = parseInt(this.data.categoryId, 10) || null;
    }
    
    // 添加其他筛选参数
    if (this.data.minPrice) params.minPrice = parseFloat(this.data.minPrice);
    if (this.data.maxPrice) params.maxPrice = parseFloat(this.data.maxPrice);
    if (this.data.tags && this.data.tags.length) params.tags = this.data.tags;
    
    // 使用防抖，避免频繁请求
    if (this._searchTimer) clearTimeout(this._searchTimer);
    
    this._searchTimer = setTimeout(() => {
      searchProducts(params)
        .then(res => {
          wx.hideLoading();
          
          if (res && res.data) {
            const newResults = res.data.list || [];
            const formattedResults = this.formatSearchResults(newResults);
            
            this.setData({
              results: formattedResults,
              hasMore: res.data.hasMore || (res.data.total > formattedResults.length),
              loading: false,
              initialLoading: false,
              currentPage: 1,
              error: false,
              errorMsg: ''
            });
            
            // 预加载下一页数据
            if (formattedResults.length > 0 && res.data.hasMore) {
              this._preloadNextPage(keyword, 2);
            }
          } else {
            this.setData({
              results: [],
              hasMore: false,
              loading: false,
              initialLoading: false
            });
          }
        })
        .catch(err => {
          wx.hideLoading();
          console.error('搜索失败', err);
          
          this.setData({
            loading: false,
            initialLoading: false,
            error: true,
            errorMsg: err.message || '搜索失败，请重试'
          });
          
          wx.showToast({
            title: err.message || '搜索失败，请重试',
            icon: 'none'
          });
        });
    }, 100); // 短暂延迟，合并多次调用
  },
  
  // 预加载下一页数据
  _preloadNextPage(keyword, page) {
    if (!keyword || page < 2) return;
    
    const params = {
      keyword,
      page,
      pageSize: this.data.pageSize || 20,
      sort: this.data.currentSort,
      order: this.data.currentSort === 'price' ? this.data.priceSortDirection : 'desc',
      platform: 'miniprogram',
      useCache: true
    };
    
    // 确保categoryId是整数
    if (this.data.categoryId) {
      params.categoryId = parseInt(this.data.categoryId, 10) || null;
    }
    
    // 添加其他筛选参数
    if (this.data.minPrice) params.minPrice = parseFloat(this.data.minPrice);
    if (this.data.maxPrice) params.maxPrice = parseFloat(this.data.maxPrice);
    if (this.data.tags && this.data.tags.length) params.tags = this.data.tags;
    
    // 静默加载，不显示加载状态
    searchProducts(params).catch(() => {
      // 忽略错误，这只是预加载
    });
  },
  
  // 加载搜索建议
  loadSuggestions(keyword) {
    if (!keyword || !keyword.trim()) {
      this.setData({ suggestions: [] });
      return;
    }
    
    getSuggestions(keyword)
      .then(res => {
        if (res && res.data) {
          this.setData({ 
            suggestions: res.data,
            historyVisible: false
          });
        }
      })
      .catch(err => {
        console.error('获取搜索建议失败', err);
      });
  },
  
  // 获取搜索结果
  fetchSearchResults(keyword, page = 1, isLoadMore = false) {
    // 如果已经在加载中，防止重复请求
    if (this.data.loading && !isLoadMore) return;
    
    this.setData({ loading: true });
    
    // 显示加载提示，但加载更多时不显示全屏loading
    if (!isLoadMore && this.data.initialLoading) {
      wx.showLoading({
        title: '搜索中...',
        mask: true
      });
    }
    
    console.log(`开始请求第${page}页数据，关键词: ${keyword}，加载更多: ${isLoadMore}`);
    
    // 构建搜索参数
    const params = {
      keyword,
      page,
      pageSize: this.data.pageSize,
      sort: this.data.currentSort,
      order: this.data.currentSort === 'price' ? this.data.priceSortDirection : 'desc',
      categoryId: this.data.categoryId,
      minPrice: this.data.minPrice,
      maxPrice: this.data.maxPrice,
      tags: this.data.tags,
      platform: 'miniprogram'
    };
    
    // 添加请求标识，用于取消旧请求
    const requestId = Date.now();
    this.data.requestController.lastRequestId = requestId;
    
    // 设置超时处理
    const timeoutPromise = new Promise((resolve, reject) => {
      setTimeout(() => {
        reject(new Error('搜索请求超时'));
      }, 10000); // 10秒超时
    });
    
    console.log('发送搜索请求:', params);
    
    // 使用Promise.race确保请求不会永久挂起
    Promise.race([
      searchProducts(params),
      timeoutPromise
    ])
      .then(res => {
        console.log('搜索请求成功，处理响应:', res);
        
        // 如果不是最新请求，忽略结果
        if (this.data.requestController.lastRequestId !== requestId) {
          console.log('忽略过期的请求结果');
          return;
        }
        
        // 隐藏加载提示
        if (!isLoadMore && this.data.initialLoading) {
          wx.hideLoading();
        }
        
        // 确保res和res.data存在
        if (!res || !res.data) {
          console.error('响应数据格式错误:', res);
          throw new Error('响应数据格式错误');
        }
        
        // 获取列表数据
        const newResults = Array.isArray(res.data.list) ? res.data.list : [];
        console.log('获取到搜索结果数量:', newResults.length);
        
        // 格式化结果
        const formattedResults = this.formatSearchResults(newResults);
        
        // 确保hasMore状态正确
        const hasMore = res.data.hasMore !== undefined ? 
          res.data.hasMore : 
          (res.data.total > (page * this.data.pageSize));
        
        console.log('搜索结果状态:', {
          total: res.data.total || 0,
          page: page,
          pageSize: this.data.pageSize,
          hasMore: hasMore,
          resultCount: formattedResults.length
        });
        
        // 更新数据，区分首次加载和加载更多
        if (page === 1) {
          this.setData({
            results: formattedResults,
            hasMore: hasMore,
            loading: false,
            initialLoading: false,
            currentPage: page,
            error: false,
            errorMsg: '',
            showResults: true
          });
        } else if (isLoadMore) {
          // 加载更多时，合并结果
          this.setData({
            results: [...this.data.results, ...formattedResults],
            hasMore: hasMore,
            loading: false,
            currentPage: page
          });
        }
      })
      .catch(err => {
        console.error('搜索商品失败', err);
        
        // 如果不是最新请求，忽略错误
        if (this.data.requestController.lastRequestId !== requestId) {
          return;
        }
        
        // 确保错误时也隐藏加载提示
        if (!isLoadMore && this.data.initialLoading) {
          wx.hideLoading();
        }
        
        this.setData({
          loading: false,
          initialLoading: false,
          hasMore: false,
          error: true,
          errorMsg: err.message || '搜索失败，请重试'
        });
        
        wx.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      })
      .finally(() => {
        // 无论成功或失败，都确保重置加载状态
        console.log('搜索请求完成，重置加载状态');
        if (this.data.loading) {
          this.setData({ loading: false });
        }
        if (this.data.initialLoading) {
          this.setData({ initialLoading: false });
          wx.hideLoading();
        }
      });
  },
  
  // 格式化搜索结果
  formatSearchResults(results) {
    if (!Array.isArray(results)) {
      return [];
    }
    
    // 过滤并格式化商品数据 - 简化处理以提高速度
    return results
      .filter(item => item && item.id && item.status !== 'deleted')
      .map(item => ({
        id: item.id,
        name: item.title || item.name || '',
        price: item.price || 0,
        originalPrice: item.originalPrice || item.original_price || item.price || 0,
        image: item.image || item.image_url || '',
        sales: item.sales || item.sales_count || 0,
        tags: Array.isArray(item.tags) ? item.tags : [],
        unit: item.unit || '件',
        status: item.status || 'active',
        stock: item.stock !== undefined ? item.stock : 999,
        out_of_stock: item.out_of_stock || (item.stock !== undefined && item.stock <= 0) || false
      }));
  },
  
  // 加载更多结果
  loadMoreResults() {
    if (this.data.loading || !this.data.hasMore) {
      return;
    }
    
    const nextPage = this.data.currentPage + 1;
    this.setData({ loading: true });
    
    // 构建搜索参数
    const params = {
      keyword: this.data.searchKeyword,
      page: nextPage,
      pageSize: this.data.pageSize || 20,
      sort: this.data.currentSort,
      order: this.data.currentSort === 'price' ? this.data.priceSortDirection : 'desc',
      platform: 'miniprogram',
      useCache: true // 使用缓存提高速度
    };
    
    // 确保categoryId是整数
    if (this.data.categoryId) {
      params.categoryId = parseInt(this.data.categoryId, 10) || null;
    }
    
    // 添加其他筛选参数
    if (this.data.minPrice) params.minPrice = parseFloat(this.data.minPrice);
    if (this.data.maxPrice) params.maxPrice = parseFloat(this.data.maxPrice);
    if (this.data.tags && this.data.tags.length) params.tags = this.data.tags;
    
    searchProducts(params)
      .then(res => {
        if (res && res.data) {
          const newResults = res.data.list || [];
          const formattedResults = this.formatSearchResults(newResults);
          
          // 使用setData的局部更新功能，只更新变化的部分
          this.setData({
            ['results[' + this.data.results.length + ']']: formattedResults,
            hasMore: res.data.hasMore || (res.data.total > (this.data.results.length + formattedResults.length)),
            loading: false,
            currentPage: nextPage
          });
          
          // 预加载下一页
          if (formattedResults.length > 0 && res.data.hasMore) {
            this._preloadNextPage(this.data.searchKeyword, nextPage + 1);
          }
        } else {
          this.setData({
            hasMore: false,
            loading: false
          });
        }
      })
      .catch(err => {
        console.error('加载更多失败', err);
        this.setData({
          loading: false,
          hasMore: false
        });
      });
  },
  
  // 刷新搜索结果
  refreshResults() {
    // 如果没有关键词，不刷新
    if (!this.data.searchKeyword) return;
    
    // 构建搜索参数
    const params = {
      keyword: this.data.searchKeyword,
      page: 1,
      pageSize: this.data.pageSize || 20,
      sort: this.data.currentSort,
      order: this.data.currentSort === 'price' ? this.data.priceSortDirection : 'desc',
      platform: 'miniprogram'
    };
    
    // 确保categoryId是整数
    if (this.data.categoryId) {
      params.categoryId = parseInt(this.data.categoryId, 10) || null;
    }
    
    // 添加其他筛选参数
    if (this.data.minPrice) params.minPrice = parseFloat(this.data.minPrice);
    if (this.data.maxPrice) params.maxPrice = parseFloat(this.data.maxPrice);
    if (this.data.tags && this.data.tags.length) params.tags = this.data.tags;
    
    // 静默刷新，不显示加载状态
    searchProducts(params)
      .then(res => {
        if (res && res.data) {
          const newResults = res.data.list || [];
          const formattedResults = this.formatSearchResults(newResults);
          
          this.setData({
            results: formattedResults,
            hasMore: res.data.hasMore || (res.data.total > formattedResults.length),
            currentPage: 1
          });
        }
      })
      .catch(() => {
        // 忽略错误，保持当前结果
      });
  },
  
  // 使用历史搜索词
  useHistoryItem(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.doSearch(keyword);
  },
  
  // 使用热门搜索词
  useHotKeyword(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.doSearch(keyword);
  },
  
  // 使用搜索建议
  useSuggestion(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.doSearch(keyword);
  },
  
  // 改变排序方式
  changeSort(e) {
    const sort = e.currentTarget.dataset.sort;
    
    let priceSortDirection = this.data.priceSortDirection;
    
    if (sort === 'price') {
      // 价格排序，切换升序/降序
      if (this.data.currentSort === 'price') {
        priceSortDirection = priceSortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        priceSortDirection = 'asc';
      }
    }
    
    this.setData({
      currentSort: sort,
      priceSortDirection,
      currentPage: 1,
      loading: true
    });
    
    this.fetchSearchResults(this.data.searchKeyword, 1, false);
  },
  
  // 切换列表模式（网格/列表）
  toggleListMode() {
    const mode = this.data.listMode === 'grid' ? 'list' : 'grid';
    this.setData({ listMode: mode });
  },
  
  // 回到顶部
  scrollToTop() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  },
  
  // 页面滚动事件
  onPageScroll(e) {
    this.setData({
      scrollTop: e.scrollTop,
      isScrolling: true
    });
  },
  
  // 商品点击事件
  onProductTap(e) {
    const product = e.detail.product;
    if (product && product.id) {
      wx.navigateTo({
        url: `/pages/product-detail/product-detail?id=${product.id}`
      });
    }
  },
  
  // 添加到购物车事件
  async onAddToCart(e) {
    const { product, quantity = 1 } = e.detail;

    if (!product || !product.id) {
      console.error('添加购物车失败：商品信息无效');
      return;
    }

    // 判断商品是否缺货
    if (product.out_of_stock) {
      wx.showToast({
        title: product.purchase_message || '商品已售罄',
        icon: 'none'
      });
      return;
    }

    // 检查登录状态
    if (!isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });

      setTimeout(() => {
        wx.navigateTo({ url: '/pages/login/index' });
      }, 1000);
      return;
    }

    try {
      // 使用统一购物车管理器
      wx.showLoading({
        title: '添加中...',
        mask: true
      });

      const params = {
        product_id: product.id,
        quantity: quantity
      };

      // 如果有销售单位信息，也一并传入
      if (product.sale_unit && product.sale_unit.id) {
        params.unit_id = product.sale_unit.id;
      }

      const success = await addToCart(params);

      wx.hideLoading();

      if (success) {
        console.log('✅ 搜索页添加到购物车成功:', product.name);
        // Toast提示由统一管理器的监听器处理
        // 购物车数量由统一管理器自动更新
      } else {
        wx.showToast({
          title: '添加失败，请重试',
          icon: 'error'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('❌ 搜索页添加到购物车异常:', error);

      let errorMessage = '添加失败';
      if (error.message) {
        if (error.message.includes('登录')) {
          errorMessage = '登录已过期，请重新登录';
        } else if (error.message.includes('网络')) {
          errorMessage = '网络异常，请重试';
        } else {
          errorMessage = error.message;
        }
      }

      wx.showToast({
        title: errorMessage,
        icon: 'error'
      });
    }
  },
  
  // 触底加载更多
  onReachBottom() {
    // 防止频繁触发，添加节流
    if (this.data.requestController.reachBottomTimer) {
      clearTimeout(this.data.requestController.reachBottomTimer);
    }
    
    this.data.requestController.reachBottomTimer = setTimeout(() => {
      console.log('触发触底加载更多', {
        showResults: this.data.showResults,
        hasMore: this.data.hasMore,
        loading: this.data.loading
      });
      
      if (this.data.showResults && this.data.hasMore && !this.data.loading) {
        this.loadMoreResults();
      }
    }, 200); // 200ms内的多次触底只执行一次
  },
  
  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.showResults) {
      this.refreshResults();
    }
    wx.stopPullDownRefresh();
  },
  
  // 设置筛选条件
  setFilter(filter) {
    this.setData({
      ...filter,
      currentPage: 1,
      loading: true
    });
    
    this.fetchSearchResults(this.data.searchKeyword, 1, false);
  },
  
  // 重试加载
  retrySearch() {
    this.setData({
      error: false,
      errorMsg: '',
      loading: true
    });
    this.fetchSearchResults(this.data.searchKeyword, this.data.currentPage, false);
  }
});