<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('unit_properties', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('unit_id')->comment('单位ID');
            $table->string('key', 100)->comment('属性键');
            $table->text('value')->comment('属性值');
            $table->timestamps();
            
            $table->unique(['unit_id', 'key']);
            $table->index('unit_id');
            
            $table->foreign('unit_id')->references('id')->on('units')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unit_properties');
    }
};
