<template>
  <div class="order-list">
    <div class="page-header">
      <h1 class="page-title">订单列表</h1>
      <div class="page-actions">
        <el-button type="primary" class="add-btn">
          <el-icon><Plus /></el-icon>
          添加订单
        </el-button>
      </div>
    </div>
    
    <el-card>
      <p>订单列表页面内容...</p>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
</script>

<style scoped>
.order-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin: 0;
}

.add-btn {
  background-color: #28a745;
  border-color: #28a745;
}

.add-btn:hover {
  background-color: #218838;
  border-color: #1e7e34;
}
</style> 