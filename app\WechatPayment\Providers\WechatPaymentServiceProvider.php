<?php

namespace App\WechatPayment\Providers;

use Illuminate\Support\ServiceProvider;

class WechatPaymentServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     *
     * @return void
     */
    public function register()
    {
        // 注册服务（如果有）
    }

    /**
     * 启动服务
     *
     * @return void
     */
    public function boot()
    {
        // 加载路由
        $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
    }
} 