<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 创建商品系列表（用于商品系列分类）
        if (!Schema::hasTable('product_series')) {
            Schema::create('product_series', function (Blueprint $table) {
                $table->id();
                $table->string('name')->comment('系列名称，如：iPhone系列、有机系列、春夏系列等');
                $table->string('description')->nullable()->comment('系列描述');
                $table->string('image_url')->nullable()->comment('系列图片');
                $table->integer('sort')->default(0)->comment('排序');
                $table->boolean('status')->default(true)->comment('状态：1启用，0禁用');
                $table->timestamps();
                
                $table->index(['status', 'sort']);
            });
        }

        // 创建属性定义表（管理员预定义的属性模板）
        if (!Schema::hasTable('product_attributes')) {
            Schema::create('product_attributes', function (Blueprint $table) {
                $table->id();
                $table->string('name')->comment('属性名称，如：品牌、产地、重量、颜色等');
                $table->string('key')->unique()->comment('属性键名，如：brand、origin、weight、color');
                $table->enum('type', ['text', 'number', 'select', 'multi_select', 'boolean', 'date', 'textarea'])->default('text')->comment('属性类型');
                $table->json('options')->nullable()->comment('选项值（用于select和multi_select类型）');
                $table->string('unit')->nullable()->comment('单位，如：克、厘米、天等');
                $table->string('placeholder')->nullable()->comment('输入框占位符');
                $table->text('description')->nullable()->comment('属性描述');
                $table->boolean('is_required')->default(false)->comment('是否必填');
                $table->boolean('is_searchable')->default(false)->comment('是否可搜索');
                $table->boolean('show_in_list')->default(false)->comment('是否在商品列表显示');
                $table->integer('sort')->default(0)->comment('排序');
                $table->boolean('status')->default(true)->comment('状态：1启用，0禁用');
                $table->timestamps();
                
                $table->index(['status', 'sort']);
                $table->index(['type', 'status']);
                $table->index(['is_searchable', 'status']);
            });
        }

        // 创建商品属性值表（存储具体商品的属性值）
        if (!Schema::hasTable('product_attribute_values')) {
            Schema::create('product_attribute_values', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('product_id')->comment('商品ID');
                $table->unsignedBigInteger('attribute_id')->comment('属性ID');
                $table->text('value')->nullable()->comment('属性值');
                $table->json('value_json')->nullable()->comment('JSON格式属性值（用于复杂类型）');
                $table->timestamps();
                
                $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
                $table->foreign('attribute_id')->references('id')->on('product_attributes')->onDelete('cascade');
                $table->unique(['product_id', 'attribute_id']);
                // 为TEXT字段创建索引时需要指定长度
                $table->index(['attribute_id', DB::raw('value(255)')], 'product_attribute_values_attribute_id_value_index');
            });
        }

        // 为products表添加series_id字段
        if (!Schema::hasColumn('products', 'series_id')) {
            Schema::table('products', function (Blueprint $table) {
                $table->unsignedBigInteger('series_id')->nullable()->comment('商品系列ID')->after('category_id');
                $table->foreign('series_id')->references('id')->on('product_series')->onDelete('set null');
                $table->index(['series_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('products', 'series_id')) {
            Schema::table('products', function (Blueprint $table) {
                $table->dropForeign(['series_id']);
                $table->dropColumn('series_id');
            });
        }
        
        Schema::dropIfExists('product_attribute_values');
        Schema::dropIfExists('product_attributes');
        Schema::dropIfExists('product_series');
    }
}; 