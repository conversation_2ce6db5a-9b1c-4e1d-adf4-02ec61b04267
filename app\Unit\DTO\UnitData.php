<?php

namespace App\Unit\DTO;

use App\Unit\Models\Unit;

class UnitData
{
    /**
     * 单位ID
     *
     * @var int|null
     */
    public ?int $id;
    
    /**
     * 单位名称
     *
     * @var string
     */
    public string $name;
    
    /**
     * 单位显示名称
     *
     * @var string
     */
    public string $displayName;
    
    /**
     * 单位符号
     *
     * @var string|null
     */
    public ?string $symbol;
    
    /**
     * 是否基本单位
     *
     * @var bool
     */
    public bool $isBase;
    
    /**
     * 单位分类
     *
     * @var string
     */
    public string $category;
    
    /**
     * 单位描述
     *
     * @var string|null
     */
    public ?string $description;
    
    /**
     * 创建时间
     *
     * @var string|null
     */
    public ?string $createdAt;
    
    /**
     * 更新时间
     *
     * @var string|null
     */
    public ?string $updatedAt;
    
    /**
     * 从模型创建DTO
     *
     * @param Unit $unit
     * @return self
     */
    public static function fromModel(Unit $unit): self
    {
        $dto = new self();
        $dto->id = $unit->id;
        $dto->name = $unit->name;
        $dto->displayName = $unit->display_name ?? $unit->name;
        $dto->symbol = $unit->symbol;
        $dto->isBase = (bool)$unit->is_base;
        $dto->category = $unit->category;
        $dto->description = $unit->description;
        $dto->createdAt = $unit->created_at ? $unit->created_at->toDateTimeString() : null;
        $dto->updatedAt = $unit->updated_at ? $unit->updated_at->toDateTimeString() : null;
        
        return $dto;
    }
    
    /**
     * 转换为数组
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'display_name' => $this->displayName,
            'symbol' => $this->symbol,
            'is_base' => $this->isBase,
            'category' => $this->category,
            'description' => $this->description,
            'created_at' => $this->createdAt,
            'updated_at' => $this->updatedAt,
        ];
    }
    
    /**
     * 转换为模型数据
     *
     * @return array
     */
    public function toModelData(): array
    {
        return [
            'name' => $this->name,
            'display_name' => $this->displayName,
            'symbol' => $this->symbol,
            'is_base' => $this->isBase,
            'category' => $this->category,
            'description' => $this->description,
        ];
    }
} 