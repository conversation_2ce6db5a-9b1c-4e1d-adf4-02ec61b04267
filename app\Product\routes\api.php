<?php

use App\Product\Http\Controllers\ProductController;
use App\Product\Http\Controllers\CategoryController;
use App\Product\Http\Controllers\ProductAttributeController;
use App\Product\Http\Controllers\ProductTagController;
use App\Product\Http\Controllers\MemberDiscountController;
use App\Product\Http\Controllers\PublicController;
use App\Product\Http\Controllers\PublicCategoryController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Product API 路由
|--------------------------------------------------------------------------
|
| Product模块的API路由定义
|
*/

// 管理后台API - 商品路由
Route::group(['prefix' => 'api/products', 'middleware' => ['api', 'auth:sanctum']], function () {
    Route::get('/', [ProductController::class, 'index']);
    Route::post('/', [ProductController::class, 'store']);
    Route::get('/stats', [ProductController::class, 'stats']);
    Route::post('/generate-code', [ProductController::class, 'generateCode']);
    Route::get('/{id}', [ProductController::class, 'show']);
    Route::put('/{id}', [ProductController::class, 'update']);
    Route::delete('/{id}', [ProductController::class, 'destroy']);
    Route::put('/{id}/status', [ProductController::class, 'updateStatus']);
    Route::get('/search/all', [ProductController::class, 'search']);
    
    // 价格相关路由
    Route::get('/{id}/price', [ProductController::class, 'getPrice']);
    Route::get('/{id}/price-display', [ProductController::class, 'getPriceDisplay']);
    Route::get('/{id}/full-info', [ProductController::class, 'getFullInfo']);
    Route::post('/batch-prices', [ProductController::class, 'batchGetPrices']);
    
    // 删除相关路由
    Route::get('/{id}/can-delete', [ProductController::class, 'canDelete']);
    Route::post('/batch-delete', [ProductController::class, 'batchDestroy']);
    
    // 商品属性相关路由
    Route::get('/{id}/attributes', [ProductAttributeController::class, 'getProductAttributes']);
    Route::post('/{id}/attributes', [ProductAttributeController::class, 'setProductAttributes']);
    
    // 商品标签相关路由
    Route::get('/{id}/tags', [ProductTagController::class, 'getProductTags']);
    Route::post('/{id}/tags', [ProductTagController::class, 'setProductTags']);
});

// 商品分类路由
Route::group(['prefix' => 'api/categories', 'middleware' => ['api', 'auth:sanctum']], function () {
    Route::get('/', [CategoryController::class, 'index']);
    Route::post('/', [CategoryController::class, 'store']);
    Route::get('/tree', [CategoryController::class, 'tree']);
    Route::get('/flat', [CategoryController::class, 'flat']);
    Route::get('/{id}', [CategoryController::class, 'show']);
    Route::put('/{id}', [CategoryController::class, 'update']);
    Route::delete('/{id}', [CategoryController::class, 'destroy']);
    Route::post('/upload/image', [CategoryController::class, 'uploadImage']);
    
    // 新增树形结构相关路由
    Route::get('/{id}/ancestors', [CategoryController::class, 'ancestors']);
    Route::get('/{id}/descendants', [CategoryController::class, 'descendants']);
    Route::get('/{id}/breadcrumb', [CategoryController::class, 'breadcrumb']);
});

// 商品属性管理路由
Route::group(['prefix' => 'api/product-attributes', 'middleware' => ['api', 'auth:sanctum']], function () {
    Route::get('/', [ProductAttributeController::class, 'index']);
    Route::post('/', [ProductAttributeController::class, 'store']);
    Route::get('/types', [ProductAttributeController::class, 'getTypes']);
    Route::get('/stats', [ProductAttributeController::class, 'getStats']);
    Route::post('/sort', [ProductAttributeController::class, 'updateSort']);
    Route::get('/{id}', [ProductAttributeController::class, 'show']);
    Route::put('/{id}', [ProductAttributeController::class, 'update']);
    Route::delete('/{id}', [ProductAttributeController::class, 'destroy']);
    Route::post('/{id}/copy', [ProductAttributeController::class, 'copy']);
});

// 商品标签管理路由
Route::group(['prefix' => 'api/product-tags', 'middleware' => ['api', 'auth:sanctum']], function () {
    Route::get('/', [ProductTagController::class, 'index']);
    Route::post('/', [ProductTagController::class, 'store']);
    Route::get('/stats', [ProductTagController::class, 'getStats']);
    Route::get('/popular', [ProductTagController::class, 'getPopularTags']);
    Route::post('/sort', [ProductTagController::class, 'updateSort']);
    Route::get('/{id}', [ProductTagController::class, 'show']);
    Route::put('/{id}', [ProductTagController::class, 'update']);
    Route::delete('/{id}', [ProductTagController::class, 'destroy']);
});

// 会员折扣管理路由
Route::group(['prefix' => 'api/member-discounts', 'middleware' => ['api', 'auth:sanctum']], function () {
    // 商品会员折扣
    Route::get('/products', [MemberDiscountController::class, 'getProductMemberDiscounts']);
    Route::post('/products', [MemberDiscountController::class, 'createProductMemberDiscount']);
    Route::put('/products/{id}', [MemberDiscountController::class, 'updateProductMemberDiscount']);
    Route::delete('/products/{id}', [MemberDiscountController::class, 'deleteProductMemberDiscount']);
    Route::post('/products/batch', [MemberDiscountController::class, 'batchSetProductMemberDiscounts']);
    
    // 分类会员折扣
    Route::get('/categories', [MemberDiscountController::class, 'getCategoryMemberDiscounts']);
    Route::post('/categories', [MemberDiscountController::class, 'createCategoryMemberDiscount']);
    Route::put('/categories/{id}', [MemberDiscountController::class, 'updateCategoryMemberDiscount']);
    Route::delete('/categories/{id}', [MemberDiscountController::class, 'deleteCategoryMemberDiscount']);
    Route::post('/categories/batch', [MemberDiscountController::class, 'batchSetCategoryMemberDiscounts']);
});

// 公共API - 客户端访问的公开接口
Route::prefix('api/public')->group(function () {
    // 商品相关
    Route::get('/products', [PublicController::class, 'products']);
    Route::get('/products/search', [PublicController::class, 'searchProducts']);
    Route::get('/products/{id}', [PublicController::class, 'productDetail']);
    
    // 商品价格相关（公开接口，用于用户端商城）
    Route::get('/products/{id}/price', [ProductController::class, 'getPrice']);
    Route::get('/products/{id}/price-display', [ProductController::class, 'getPriceDisplay']);
    Route::get('/products/{id}/full-info', [ProductController::class, 'getFullInfo']);
    Route::post('/products/batch-prices', [ProductController::class, 'batchGetPrices']);
    
    // 分类相关
    Route::get('/categories', [PublicCategoryController::class, 'categories']);
    Route::get('/categories/tree', [PublicCategoryController::class, 'categoryTree']);
    Route::get('/categories/{id}/breadcrumb', [PublicCategoryController::class, 'categoryBreadcrumb']);
    Route::get('/categories/{id}/children', [PublicCategoryController::class, 'categoryChildren']);
    
    // 标签相关
    Route::get('/tags', [PublicController::class, 'tags']);
    Route::get('/tags/popular', [PublicController::class, 'popularTags']);
    
    // 商品标签页配置接口
    Route::get('/product-tabs', [PublicController::class, 'productTabs']);
});

// 微信小程序公共API（不需要认证）
Route::prefix('public')->group(function () {
    // 商品公共接口
    Route::get('/products', [ProductController::class, 'index']);
    Route::get('/products/hot', [ProductController::class, 'hotProducts']);
    Route::get('/products/new', [ProductController::class, 'newProducts']);
    Route::get('/products/{id}', [ProductController::class, 'show']);
    Route::get('/products/search', [ProductController::class, 'search']);
    
    // 分类公共接口 - 使用已有的PublicCategoryController
    Route::get('/categories', [PublicCategoryController::class, 'categories']);
    Route::get('/categories/tree', [PublicCategoryController::class, 'categoryTree']);
}); 