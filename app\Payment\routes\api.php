<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PaymentOfferController;

/*
|--------------------------------------------------------------------------
| Payment API Routes
|--------------------------------------------------------------------------
|
| 支付管理相关的API路由
| 统一在此文件管理所有支付相关路由
|
*/

// 公共支付路由（不需要认证）
Route::prefix('payment')->group(function () {
    
    // 公共统计路由（用于前端展示）
    Route::get('/statistics', [PaymentOfferController::class, 'getStatistics']);

    // 公共配置路由（不需要认证）
    Route::prefix('config')->group(function () {
        // 获取支付方式列表
        Route::get('/payment-methods', [PaymentOfferController::class, 'getPaymentMethods']);
        
        // 获取优惠类型列表
        Route::get('/offer-types', [PaymentOfferController::class, 'getOfferTypes']);
    });

    // 公共优惠计算（不需要认证）
    Route::post('/offers/calculate', [PaymentOfferController::class, 'calculateOffer']);
    
    // 公共支付优惠列表（不需要认证，只返回启用的优惠）
    Route::get('/offers', [PaymentOfferController::class, 'publicOfferList']);
});

// 需要员工权限的支付管理路由
Route::middleware(['auth:sanctum', 'employee.role:admin,manager,staff'])
    ->prefix('admin/payment')
    ->group(function () {
        
        // 支付优惠管理路由
        Route::prefix('offers')->group(function () {
            // 获取支付优惠列表
            Route::get('/', [PaymentOfferController::class, 'index']);
            
            // 创建支付优惠
            Route::post('/', [PaymentOfferController::class, 'store']);
            
            // 获取单个支付优惠
            Route::get('/{paymentOffer}', [PaymentOfferController::class, 'show']);
            
            // 更新支付优惠
            Route::put('/{paymentOffer}', [PaymentOfferController::class, 'update']);
            
            // 删除支付优惠
            Route::delete('/{paymentOffer}', [PaymentOfferController::class, 'destroy']);
            
            // 批量更新状态
            Route::put('/batch-status', [PaymentOfferController::class, 'batchUpdateStatus']);
            
            // 获取统计数据
            Route::get('/statistics/overview', [PaymentOfferController::class, 'statistics']);
        });
        
        // 管理员配置路由
        Route::prefix('config')->group(function () {
            // 管理支付方式配置
            Route::get('/payment-methods', [PaymentOfferController::class, 'getPaymentMethods']);
            
            // 管理优惠类型配置
            Route::get('/offer-types', [PaymentOfferController::class, 'getOfferTypes']);
        });
    }); 