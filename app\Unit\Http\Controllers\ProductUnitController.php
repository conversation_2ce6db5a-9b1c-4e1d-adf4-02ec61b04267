<?php

namespace App\Unit\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Product\Models\Product;
use App\Unit\Models\Unit;
use App\Product\Models\ProductUnit;
use App\Unit\Services\ProductUnitService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;

class ProductUnitController extends Controller
{
    /**
     * 产品单位服务
     *
     * @var ProductUnitService
     */
    protected $productUnitService;

    /**
     * 构造函数
     *
     * @param ProductUnitService $productUnitService
     */
    public function __construct(ProductUnitService $productUnitService)
    {
        $this->productUnitService = $productUnitService;
    }

    /**
     * 获取产品的单位列表
     *
     * @param int $productId
     * @return JsonResponse
     */
    public function index($productId): JsonResponse
    {
        try {
            $product = Product::findOrFail($productId);
            $units = $product->getAllUnits();
            
            return response()->json(ApiResponse::success($units));
        } catch (\Exception $e) {
            Log::error('获取产品单位列表失败', [
                'product_id' => $productId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ApiResponse::error('获取产品单位失败: ' . $e->getMessage(), 500),
                500
            );
        }
    }

    /**
     * 为产品添加单位
     *
     * @param int $productId
     * @param Request $request
     * @return JsonResponse
     */
    public function store($productId, Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'unit_id' => 'required|exists:units,id',
                'conversion_factor' => 'required|numeric|gt:0',
                'roles' => 'nullable|array',
                'role_priority' => 'nullable|array',
                'is_default' => 'nullable|boolean',
                'is_active' => 'nullable|boolean'
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $product = Product::findOrFail($productId);
            $unit = Unit::findOrFail($request->input('unit_id'));
            
            // 收集单位数据
            $unitData = [
                'unit_id' => $unit->id,
                'conversion_factor' => $request->input('conversion_factor'),
                'roles' => $request->input('roles', []),
                'role_priority' => $request->input('role_priority', []),
                'is_default' => $request->input('is_default', false),
                'is_active' => $request->input('is_active', true)
            ];
            
            // 检查是否已存在
            $existingUnit = ProductUnit::where('product_id', $product->id)
                ->where('unit_id', $unit->id)
                ->first();
                
            if ($existingUnit) {
                return response()->json(
                    ApiResponse::error('该单位已经添加到商品中', 422),
                    422
                );
            }
            
            $result = $this->productUnitService->addProductUnit(
                $product,
                $unit,
                $unitData['conversion_factor'],
                $unitData['roles'],
                $unitData['role_priority'],
                $unitData['is_default'],
                $unitData['is_active']
            );
            
            return response()->json(ApiResponse::success($result, '单位添加成功'));
        } catch (\Exception $e) {
            Log::error('添加产品单位失败', [
                'product_id' => $productId,
                'data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ApiResponse::error('添加单位失败: ' . $e->getMessage(), 500),
                500
            );
        }
    }

    /**
     * 更新产品单位信息
     *
     * @param int $productId
     * @param int $unitId
     * @param Request $request
     * @return JsonResponse
     */
    public function update($productId, $unitId, Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'conversion_factor' => 'sometimes|required|numeric|gt:0',
                'roles' => 'nullable|array',
                'role_priority' => 'nullable|array',
                'is_default' => 'nullable|boolean',
                'is_active' => 'nullable|boolean'
            ]);
            
            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }
            
            $product = Product::findOrFail($productId);
            $unit = Unit::findOrFail($unitId);
            
            // 检查单位关联是否存在
            $productUnit = ProductUnit::where('product_id', $product->id)
                ->where('unit_id', $unit->id)
                ->first();
                
            if (!$productUnit) {
                return response()->json(
                    ApiResponse::error('单位关联不存在', 404),
                    404
                );
            }
            
            // 收集更新数据
            $updateData = [];
            
            if ($request->has('conversion_factor')) {
                $updateData['conversion_factor'] = $request->input('conversion_factor');
            }
            
            if ($request->has('roles')) {
                $updateData['roles'] = $request->input('roles');
            }
            
            if ($request->has('role_priority')) {
                $updateData['role_priority'] = $request->input('role_priority');
            }
            
            if ($request->has('is_default')) {
                $updateData['is_default'] = $request->input('is_default');
            }
            
            if ($request->has('is_active')) {
                $updateData['is_active'] = $request->input('is_active');
            }
            
            $productUnit->update($updateData);
            
            return response()->json(ApiResponse::success($productUnit, '单位信息更新成功'));
        } catch (\Exception $e) {
            Log::error('更新产品单位信息失败', [
                'product_id' => $productId,
                'unit_id' => $unitId,
                'data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ApiResponse::error('更新单位信息失败: ' . $e->getMessage(), 500),
                500
            );
        }
    }

    /**
     * 删除产品单位关联
     *
     * @param int $productId
     * @param int $unitId
     * @return JsonResponse
     */
    public function destroy($productId, $unitId): JsonResponse
    {
        try {
            $product = Product::findOrFail($productId);
            
            // 检查是否是基本单位
            if ($product->base_unit_id == $unitId) {
                return response()->json(
                    ApiResponse::error('不能删除产品的基本单位，请先更改产品的基本单位', 422),
                    422
                );
            }
            
            $deleted = ProductUnit::where('product_id', $productId)
                ->where('unit_id', $unitId)
                ->delete();
                
            if (!$deleted) {
                return response()->json(
                    ApiResponse::error('单位关联不存在', 404),
                    404
                );
            }
            
            return response()->json(ApiResponse::success(null, '单位关联已删除'));
        } catch (\Exception $e) {
            Log::error('删除产品单位关联失败', [
                'product_id' => $productId,
                'unit_id' => $unitId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(
                ApiResponse::error('删除单位关联失败: ' . $e->getMessage(), 500),
                500
            );
        }
    }
} 