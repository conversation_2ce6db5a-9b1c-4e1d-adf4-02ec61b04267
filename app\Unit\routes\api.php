<?php

use Illuminate\Support\Facades\Route;
use App\Unit\Http\Controllers\UnitController;
use App\Unit\Http\Controllers\UnitExtendController;
use App\Unit\Http\Controllers\UnitConversionController;
use App\Unit\Http\Controllers\UnitConversionExtendController;
use App\Unit\Http\Controllers\ProductUnitController;
use App\Unit\Http\Controllers\UnitConversionGraphController;

/*
|--------------------------------------------------------------------------
| Unit Module API Routes
|--------------------------------------------------------------------------
|
| 这里是所有单位模块的API路由定义
|
*/

// 单位管理路由
Route::prefix('api/units')->middleware(['api'])->group(function () {
    // 基本CRUD
    Route::get('/', [UnitController::class, 'index']);
    Route::post('/', [UnitController::class, 'store']);
    Route::get('/{id}', [UnitController::class, 'show']);
    Route::put('/{id}', [UnitController::class, 'update']);
    Route::delete('/{id}', [UnitController::class, 'destroy']);
    
    // 获取统计数据
    Route::get('/stats/overview', [UnitController::class, 'getStats']);
    
    // 获取单位类型
    Route::get('/types/list', [UnitController::class, 'getTypes']);
    
    // 按类型获取单位
    Route::get('/type/{type}', [UnitController::class, 'getByType']);
    
    // 切换单位状态
    Route::put('/{id}/toggle', [UnitController::class, 'toggleStatus']);
    
    // 获取单位使用统计
    Route::get('/{id}/usage', [UnitController::class, 'getUsageStats']);
    
    // 获取单位转换规则
    Route::get('/{id}/conversions', [UnitController::class, 'getConversions']);
    
    // 批量操作
    Route::post('/batch', [UnitController::class, 'batchOperation']);
});

// 产品单位关联路由
Route::prefix('api/product-units')->middleware(['api'])->group(function () {
    // 获取产品单位
    Route::get('/product/{productId}', [ProductUnitController::class, 'getByProduct']);
    
    // 设置产品单位
    Route::post('/product/{productId}', [ProductUnitController::class, 'setProductUnits']);
    
    // 添加产品单位
    Route::post('/', [ProductUnitController::class, 'store']);
    
    // 更新产品单位
    Route::put('/{id}', [ProductUnitController::class, 'update']);
    
    // 删除产品单位
    Route::delete('/{id}', [ProductUnitController::class, 'destroy']);
    
    // 设置默认单位
    Route::put('/product/{productId}/default/{unitId}', [ProductUnitController::class, 'setDefaultUnit']);
    
    // 转换产品数量
    Route::post('/convert', [ProductUnitController::class, 'convertQuantity']);
});

// 单位与产品关联路由，统一使用api前缀模式
Route::group(['prefix' => 'api/units/products', 'middleware' => ['api']], function () {
    Route::get('/{productId}', [UnitController::class, 'getProductUnits']);
    Route::post('/{productId}', [UnitController::class, 'setProductAllUnits']);
});

// 单位转换路由 - 使用api前缀
Route::group(['prefix' => 'api/unit-conversions', 'middleware' => ['api']], function () {
    // 基本CRUD
    Route::get('/', [UnitConversionController::class, 'index']);
    Route::post('/', [UnitConversionController::class, 'store']);
    Route::get('/{id}', [UnitConversionController::class, 'show']);
    Route::put('/{id}', [UnitConversionController::class, 'update']);
    Route::delete('/{id}', [UnitConversionController::class, 'destroy']);
    
    // 扩展功能
    Route::post('/convert', [UnitConversionExtendController::class, 'convertValue']);
    Route::post('/batch', [UnitConversionController::class, 'batchStore']);
    
    // 其他转换功能
    Route::post('/batch-convert', [UnitConversionController::class, 'batchConvert']);
    Route::get('/path', [UnitConversionController::class, 'findPath']);
});

// 单位转换图管理路由
Route::group(['prefix' => 'api/unit-conversion-graphs', 'middleware' => ['api']], function () {
    // 基本CRUD
    Route::get('/', [UnitConversionGraphController::class, 'index']);
    Route::post('/', [UnitConversionGraphController::class, 'store']);
    Route::get('/{id}', [UnitConversionGraphController::class, 'show']);
    Route::put('/{id}', [UnitConversionGraphController::class, 'update']);
    Route::delete('/{id}', [UnitConversionGraphController::class, 'destroy']);
    
    // 设置默认转换图
    Route::put('/{id}/default', [UnitConversionGraphController::class, 'setAsDefault']);
    
    // 获取转换图的所有边
    Route::get('/{id}/edges', [UnitConversionGraphController::class, 'getEdges']);
    
    // 添加转换边
    Route::post('/{id}/edges', [UnitConversionGraphController::class, 'addEdge']);
    
    // 删除转换边
    Route::delete('/edges/{edgeId}', [UnitConversionGraphController::class, 'deleteEdge']);
    
    // 获取默认转换图
    Route::get('/default/{type}', [UnitConversionGraphController::class, 'getDefaultGraph']);
}); 