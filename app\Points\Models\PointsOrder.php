<?php

namespace App\Points\Models;

use App\Models\User;
use App\Crm\Models\UserAddress;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PointsOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'order_no',
        'total_points',
        'cash_amount',
        'status',
        'payment_method',
        'payment_no',
        'shipping_address',
        'contact_name',
        'contact_phone',
        'delivery_method',
        'delivery_date',
        'delivery_time',
        'delivery_notes',
        'notes',
        'paid_at',
        'shipped_at',
        'delivered_at',
        'cancelled_at',
    ];

    protected $casts = [
        'total_points' => 'integer',
        'cash_amount' => 'decimal:2',
        'shipping_address' => 'array',
        'delivery_date' => 'date',
        'paid_at' => 'datetime',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
        'cancelled_at' => 'datetime',
    ];

    /**
     * 订单状态常量
     */
    const STATUS_PENDING = 'pending';       // 待付款
    const STATUS_PAID = 'paid';             // 已付款
    const STATUS_SHIPPED = 'shipped';       // 已发货
    const STATUS_DELIVERED = 'delivered';   // 已送达
    const STATUS_CANCELLED = 'cancelled';   // 已取消
    const STATUS_COMPLETED = 'completed';   // 已完成

    /**
     * 配送方式常量
     */
    const DELIVERY_METHOD_EXPRESS = 'express';   // 快递配送
    const DELIVERY_METHOD_PICKUP = 'pickup';     // 自提
    const DELIVERY_METHOD_VIRTUAL = 'virtual';   // 虚拟商品

    /**
     * 生成唯一积分订单号
     */
    public static function generateOrderNo(): string
    {
        return 'P' . date('YmdHis') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联订单项
     */
    public function items()
    {
        return $this->hasMany(PointsOrderItem::class);
    }

    /**
     * 获取订单状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => '待付款',
            self::STATUS_PAID => '已付款',
            self::STATUS_SHIPPED => '已发货',
            self::STATUS_DELIVERED => '已送达',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_COMPLETED => '已完成',
            default => '未知状态'
        };
    }

    /**
     * 获取支付方式文本
     */
    public function getPaymentMethodTextAttribute(): string
    {
        if (!$this->payment_method) {
            return '纯积分兑换';
        }

        return match($this->payment_method) {
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'cash' => '现金支付',
            default => '未知支付方式'
        };
    }

    /**
     * 获取配送方式文本
     */
    public function getDeliveryMethodTextAttribute(): string
    {
        return match($this->delivery_method) {
            self::DELIVERY_METHOD_EXPRESS => '快递配送',
            self::DELIVERY_METHOD_PICKUP => '自提',
            self::DELIVERY_METHOD_VIRTUAL => '虚拟商品',
            default => '未知配送方式'
        };
    }

    /**
     * 检查订单是否可以支付
     */
    public function canBePaid(): bool
    {
        return $this->status === self::STATUS_PENDING && $this->cash_amount > 0;
    }

    /**
     * 检查订单是否可以发货
     */
    public function canBeShipped(): bool
    {
        return in_array($this->status, [self::STATUS_PAID]) && 
               $this->delivery_method !== self::DELIVERY_METHOD_VIRTUAL;
    }

    /**
     * 检查订单是否可以完成
     */
    public function canBeCompleted(): bool
    {
        return in_array($this->status, [self::STATUS_DELIVERED, self::STATUS_PAID]) &&
               $this->delivery_method === self::DELIVERY_METHOD_VIRTUAL;
    }

    /**
     * 检查订单是否可以取消
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_PAID]);
    }

    /**
     * 是否为纯积分订单
     */
    public function isPurePointsOrder(): bool
    {
        return $this->cash_amount == 0;
    }

    /**
     * 是否为混合支付订单
     */
    public function isMixedPaymentOrder(): bool
    {
        return $this->cash_amount > 0;
    }

    /**
     * 是否需要配送
     */
    public function needsDelivery(): bool
    {
        return $this->delivery_method !== self::DELIVERY_METHOD_VIRTUAL;
    }

    /**
     * 标记为已付款
     */
    public function markAsPaid(string $paymentNo = null): void
    {
        $this->update([
            'status' => self::STATUS_PAID,
            'payment_no' => $paymentNo,
            'paid_at' => now(),
        ]);
    }

    /**
     * 标记为已发货
     */
    public function markAsShipped(): void
    {
        $this->update([
            'status' => self::STATUS_SHIPPED,
            'shipped_at' => now(),
        ]);
    }

    /**
     * 标记为已送达
     */
    public function markAsDelivered(): void
    {
        $this->update([
            'status' => self::STATUS_DELIVERED,
            'delivered_at' => now(),
        ]);
    }

    /**
     * 标记为已完成
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
        ]);
    }

    /**
     * 取消订单
     */
    public function cancel(string $reason = null): void
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
            'cancelled_at' => now(),
            'notes' => $this->notes . ($reason ? "\n取消原因：{$reason}" : ''),
        ]);
    }

    /**
     * 计算订单总计
     */
    public function calculateTotals(): array
    {
        $totalPoints = $this->items()->sum('points_price');
        $totalCash = $this->items()->sum('cash_price');

        return [
            'total_points' => $totalPoints,
            'cash_amount' => $totalCash,
        ];
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：需要配送的订单
     */
    public function scopeNeedsDelivery($query)
    {
        return $query->where('delivery_method', '!=', self::DELIVERY_METHOD_VIRTUAL);
    }

    /**
     * 作用域：纯积分订单
     */
    public function scopePurePoints($query)
    {
        return $query->where('cash_amount', 0);
    }

    /**
     * 作用域：混合支付订单
     */
    public function scopeMixedPayment($query)
    {
        return $query->where('cash_amount', '>', 0);
    }
} 