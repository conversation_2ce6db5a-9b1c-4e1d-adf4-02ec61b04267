<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wechat_sub_merchants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('provider_id')->comment('关联的服务商ID')
                  ->constrained('wechat_service_provider')
                  ->onDelete('cascade');
            $table->unsignedBigInteger('merchant_id')->nullable()->comment('商户ID');
            $table->string('name')->comment('子商户名称');
            $table->string('sub_mch_id')->comment('子商户号');
            $table->string('sub_appid')->nullable()->comment('子商户AppID');
            $table->decimal('rate', 5, 4)->default(0)->comment('服务费率');
            $table->string('contact_name')->nullable()->comment('联系人');
            $table->string('contact_phone')->nullable()->comment('联系电话');
            $table->string('contact_email')->nullable()->comment('联系邮箱');
            $table->text('business_license')->nullable()->comment('营业执照信息');
            $table->text('remarks')->nullable()->comment('备注信息');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();
            
            // 子商户号在同一服务商下唯一
            $table->unique(['provider_id', 'sub_mch_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wechat_sub_merchants');
    }
};
