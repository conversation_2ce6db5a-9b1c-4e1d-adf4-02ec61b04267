<?php

namespace App\Inventory\Services;

use App\Inventory\Models\InventoryBatch;
use App\Inventory\Models\BatchOperation;
use App\Inventory\Models\BatchOutboundStrategy;
use App\Product\Models\Product;
use App\Warehouse\Models\Warehouse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BatchManagementService
{
    /**
     * 智能选择出库批次
     * 
     * @param int $productId 商品ID
     * @param int $warehouseId 仓库ID
     * @param float $requiredQuantity 需要的数量
     * @param array $options 选项
     * @return array
     */
    public function selectOutboundBatches($productId, $warehouseId, $requiredQuantity, $options = [])
    {
        try {
            // 获取适用的出库策略
            $strategy = $this->getOutboundStrategy($productId, $warehouseId, $options);
            
            // 获取可用的批次
            $availableBatches = $this->getAvailableBatches($productId, $warehouseId, $options);
            
            if ($availableBatches->isEmpty()) {
                return [
                    'success' => false,
                    'message' => '没有可用的批次',
                    'selected_batches' => [],
                    'total_quantity' => 0,
                ];
            }
            
            // 应用出库策略排序
            $sortedBatches = $strategy ? 
                $strategy->applyStrategy($availableBatches, $options) : 
                $availableBatches->sortBy('created_at');
            
            // 选择批次直到满足数量需求
            $selectedBatches = [];
            $totalQuantity = 0;
            $remainingQuantity = $requiredQuantity;
            
            foreach ($sortedBatches as $batch) {
                if ($remainingQuantity <= 0) {
                    break;
                }
                
                $availableQty = $batch->available_quantity;
                $useQuantity = min($remainingQuantity, $availableQty);
                
                if ($useQuantity > 0) {
                    $selectedBatches[] = [
                        'batch' => $batch,
                        'quantity' => $useQuantity,
                        'batch_id' => $batch->id,
                        'batch_code' => $batch->batch_code,
                        'available_quantity' => $availableQty,
                        'expiry_date' => $batch->expiry_date,
                        'production_date' => $batch->production_date,
                        'quality_status' => $batch->quality_status,
                        'unit_cost' => $batch->unit_cost ?? $batch->purchase_price,
                    ];
                    
                    $totalQuantity += $useQuantity;
                    $remainingQuantity -= $useQuantity;
                }
            }
            
            return [
                'success' => true,
                'message' => $remainingQuantity > 0 ? '部分满足需求' : '完全满足需求',
                'selected_batches' => $selectedBatches,
                'total_quantity' => $totalQuantity,
                'remaining_quantity' => $remainingQuantity,
                'strategy_used' => $strategy ? $strategy->name : '默认策略',
                'strategy_type' => $strategy ? $strategy->strategy_type : 'default',
            ];
            
        } catch (\Exception $e) {
            Log::error('批次选择失败', [
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'required_quantity' => $requiredQuantity,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '批次选择失败：' . $e->getMessage(),
                'selected_batches' => [],
                'total_quantity' => 0,
            ];
        }
    }

    /**
     * 执行批次出库
     * 
     * @param array $batchSelections 批次选择结果
     * @param array $outboundData 出库数据
     * @return array
     */
    public function executeBatchOutbound($batchSelections, $outboundData = [])
    {
        DB::beginTransaction();
        try {
            $results = [];
            $totalOutboundQuantity = 0;
            
            foreach ($batchSelections as $selection) {
                $batch = $selection['batch'];
                $quantity = $selection['quantity'];
                
                // 执行批次出库
                $success = $batch->outbound($quantity, array_merge($outboundData, [
                    'selection_data' => $selection
                ]));
                
                if ($success) {
                    $results[] = [
                        'batch_id' => $batch->id,
                        'batch_code' => $batch->batch_code,
                        'outbound_quantity' => $quantity,
                        'remaining_quantity' => $batch->fresh()->quantity,
                        'success' => true,
                    ];
                    $totalOutboundQuantity += $quantity;
                } else {
                    $results[] = [
                        'batch_id' => $batch->id,
                        'batch_code' => $batch->batch_code,
                        'outbound_quantity' => 0,
                        'success' => false,
                        'error' => '出库失败',
                    ];
                }
            }
            
            DB::commit();
            
            return [
                'success' => true,
                'message' => "成功出库 {$totalOutboundQuantity} 件商品",
                'total_outbound_quantity' => $totalOutboundQuantity,
                'batch_results' => $results,
            ];
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('批次出库执行失败', [
                'batch_selections' => $batchSelections,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '批次出库失败：' . $e->getMessage(),
                'total_outbound_quantity' => 0,
                'batch_results' => [],
            ];
        }
    }

    /**
     * 批次质量检验
     * 
     * @param int $batchId 批次ID
     * @param string $status 检验状态
     * @param array $qualityData 检验数据
     * @param int|null $checkedBy 检验人ID
     * @return array
     */
    public function performQualityCheck($batchId, $status, $qualityData = [], $checkedBy = null)
    {
        try {
            $batch = InventoryBatch::findOrFail($batchId);
            
            $success = $batch->qualityCheck($status, $qualityData, $checkedBy);
            
            if ($success) {
                return [
                    'success' => true,
                    'message' => '质量检验完成',
                    'batch_id' => $batchId,
                    'quality_status' => $status,
                    'batch_status' => $batch->fresh()->status,
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '质量检验失败',
                    'batch_id' => $batchId,
                ];
            }
            
        } catch (\Exception $e) {
            Log::error('批次质量检验失败', [
                'batch_id' => $batchId,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '质量检验失败：' . $e->getMessage(),
                'batch_id' => $batchId,
            ];
        }
    }

    /**
     * 批量更新过期状态
     * 
     * @return array
     */
    public function updateExpiredBatches()
    {
        try {
            $expiredCount = 0;
            $nearExpiryCount = 0;
            
            // 更新过期批次
            $expiredBatches = InventoryBatch::where('status', 'normal')
                ->whereNotNull('expiry_date')
                ->where('expiry_date', '<', now())
                ->get();
            
            foreach ($expiredBatches as $batch) {
                $batch->updateStatus('expired', '系统自动检测过期');
                $expiredCount++;
            }
            
            // 更新临期批次
            $nearExpiryBatches = InventoryBatch::where('status', 'normal')
                ->whereNotNull('expiry_date')
                ->where('expiry_date', '>', now())
                ->where('expiry_date', '<=', now()->addDays(7))
                ->get();
            
            foreach ($nearExpiryBatches as $batch) {
                if ($batch->isNearExpiry()) {
                    $batch->updateStatus('near_expiry', '系统自动检测临期');
                    $nearExpiryCount++;
                }
            }
            
            return [
                'success' => true,
                'message' => "更新完成：{$expiredCount} 个过期批次，{$nearExpiryCount} 个临期批次",
                'expired_count' => $expiredCount,
                'near_expiry_count' => $nearExpiryCount,
            ];
            
        } catch (\Exception $e) {
            Log::error('批量更新过期状态失败', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '更新失败：' . $e->getMessage(),
                'expired_count' => 0,
                'near_expiry_count' => 0,
            ];
        }
    }

    /**
     * 获取批次追溯信息
     * 
     * @param int $batchId 批次ID
     * @return array
     */
    public function getBatchTraceability($batchId)
    {
        try {
            $batch = InventoryBatch::with([
                'inventory.product',
                'inventory.warehouse',
                'purchaseItem.purchaseOrder.supplier',
                'operations.operator',
                'operations.warehouse',
                'creator',
                'qualityChecker',
                'lastMover'
            ])->findOrFail($batchId);
            
            // 基本信息
            $traceInfo = [
                'batch_info' => [
                    'id' => $batch->id,
                    'batch_code' => $batch->batch_code,
                    'supplier_batch_no' => $batch->supplier_batch_no,
                    'manufacturer' => $batch->manufacturer,
                    'origin_country' => $batch->origin_country,
                    'production_date' => $batch->production_date,
                    'expiry_date' => $batch->expiry_date,
                    'status' => $batch->status,
                    'status_text' => $batch->status_text,
                    'quality_status' => $batch->quality_status,
                    'quality_status_text' => $batch->quality_status_text,
                ],
                'product_info' => [
                    'id' => $batch->inventory->product->id,
                    'name' => $batch->inventory->product->name,
                    'code' => $batch->inventory->product->code,
                ],
                'warehouse_info' => [
                    'id' => $batch->inventory->warehouse->id,
                    'name' => $batch->inventory->warehouse->name,
                    'location' => $batch->inventory->warehouse->location,
                ],
                'supplier_info' => null,
                'quantity_info' => [
                    'initial_quantity' => $batch->initial_quantity,
                    'current_quantity' => $batch->quantity,
                    'locked_quantity' => $batch->locked_quantity,
                    'available_quantity' => $batch->available_quantity,
                    'remaining_percentage' => $batch->getRemainingPercentage(),
                ],
                'cost_info' => [
                    'purchase_price' => $batch->purchase_price,
                    'total_cost' => $batch->total_cost,
                    'unit_cost' => $batch->unit_cost,
                    'cost_breakdown' => $batch->cost_breakdown,
                ],
                'quality_info' => [
                    'quality_status' => $batch->quality_status,
                    'quality_data' => $batch->quality_data,
                    'quality_checked_at' => $batch->quality_checked_at,
                    'quality_checker' => $batch->qualityChecker ? [
                        'id' => $batch->qualityChecker->id,
                        'name' => $batch->qualityChecker->name,
                    ] : null,
                ],
                'storage_info' => [
                    'storage_conditions' => $batch->storage_conditions,
                    'temperature_range' => [
                        'min' => $batch->storage_temperature_min,
                        'max' => $batch->storage_temperature_max,
                    ],
                    'humidity_range' => [
                        'min' => $batch->storage_humidity_min,
                        'max' => $batch->storage_humidity_max,
                    ],
                ],
                'operations' => [],
            ];
            
            // 供应商信息
            if ($batch->purchaseItem && $batch->purchaseItem->purchaseOrder) {
                $supplier = $batch->purchaseItem->purchaseOrder->supplier;
                $traceInfo['supplier_info'] = [
                    'id' => $supplier->id,
                    'name' => $supplier->name,
                    'code' => $supplier->code,
                    'contact' => $supplier->contact,
                ];
            }
            
            // 操作历史
            foreach ($batch->operations as $operation) {
                $traceInfo['operations'][] = [
                    'id' => $operation->id,
                    'operation_type' => $operation->operation_type,
                    'operation_type_text' => $operation->operation_type_text,
                    'quantity_before' => $operation->quantity_before,
                    'quantity_after' => $operation->quantity_after,
                    'quantity_change' => $operation->quantity_change,
                    'status_before' => $operation->status_before,
                    'status_after' => $operation->status_after,
                    'reason' => $operation->reason,
                    'notes' => $operation->notes,
                    'reference_type' => $operation->reference_type,
                    'reference_id' => $operation->reference_id,
                    'reference_no' => $operation->reference_no,
                    'warehouse' => $operation->warehouse ? [
                        'id' => $operation->warehouse->id,
                        'name' => $operation->warehouse->name,
                    ] : null,
                    'operator' => [
                        'id' => $operation->operator->id,
                        'name' => $operation->operator->name,
                    ],
                    'operated_at' => $operation->operated_at,
                ];
            }
            
            return [
                'success' => true,
                'data' => $traceInfo,
            ];
            
        } catch (\Exception $e) {
            Log::error('获取批次追溯信息失败', [
                'batch_id' => $batchId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '获取追溯信息失败：' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * 获取批次报表数据
     * 
     * @param array $filters 过滤条件
     * @return array
     */
    public function getBatchReportData($filters = [])
    {
        try {
            $query = InventoryBatch::with(['inventory.product', 'inventory.warehouse']);
            
            // 应用过滤条件
            if (isset($filters['warehouse_id'])) {
                $query->whereHas('inventory', function($q) use ($filters) {
                    $q->where('warehouse_id', $filters['warehouse_id']);
                });
            }
            
            if (isset($filters['product_id'])) {
                $query->whereHas('inventory', function($q) use ($filters) {
                    $q->where('product_id', $filters['product_id']);
                });
            }
            
            if (isset($filters['status'])) {
                $query->where('status', $filters['status']);
            }
            
            if (isset($filters['quality_status'])) {
                $query->where('quality_status', $filters['quality_status']);
            }
            
            if (isset($filters['date_range'])) {
                $query->whereBetween('created_at', [
                    $filters['date_range']['start'],
                    $filters['date_range']['end']
                ]);
            }
            
            $batches = $query->get();
            
            // 统计数据
            $statistics = [
                'total_batches' => $batches->count(),
                'total_quantity' => $batches->sum('quantity'),
                'total_value' => $batches->sum(function($batch) {
                    return $batch->quantity * ($batch->unit_cost ?? $batch->purchase_price ?? 0);
                }),
                'status_distribution' => $batches->groupBy('status')->map->count(),
                'quality_distribution' => $batches->groupBy('quality_status')->map->count(),
                'expiry_analysis' => [
                    'expired' => $batches->filter->isExpired()->count(),
                    'near_expiry' => $batches->filter->isNearExpiry()->count(),
                    'normal' => $batches->filter(function($batch) {
                        return !$batch->isExpired() && !$batch->isNearExpiry();
                    })->count(),
                ],
                'warehouse_distribution' => $batches->groupBy('inventory.warehouse.name')->map->count(),
            ];
            
            return [
                'success' => true,
                'data' => [
                    'batches' => $batches->toArray(),
                    'statistics' => $statistics,
                ],
            ];
            
        } catch (\Exception $e) {
            Log::error('获取批次报表数据失败', [
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '获取报表数据失败：' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * 获取出库策略
     * 
     * @param int $productId 商品ID
     * @param int $warehouseId 仓库ID
     * @param array $context 上下文信息
     * @return BatchOutboundStrategy|null
     */
    protected function getOutboundStrategy($productId, $warehouseId, $context = [])
    {
        $product = Product::find($productId);
        $contextData = array_merge($context, [
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'category_id' => $product ? $product->category_id : null,
        ]);
        
        return BatchOutboundStrategy::getApplicableStrategy($contextData);
    }

    /**
     * 获取可用批次
     * 
     * @param int $productId 商品ID
     * @param int $warehouseId 仓库ID
     * @param array $options 选项
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getAvailableBatches($productId, $warehouseId, $options = [])
    {
        $query = InventoryBatch::whereHas('inventory', function($q) use ($productId, $warehouseId) {
            $q->where('product_id', $productId)
              ->where('warehouse_id', $warehouseId);
        })->available();
        
        // 排除特定状态的批次
        $excludeStatuses = $options['exclude_statuses'] ?? ['expired', 'damaged', 'recalled', 'disposed'];
        $query->whereNotIn('status', $excludeStatuses);
        
        // 质量要求
        if (isset($options['require_quality_passed']) && $options['require_quality_passed']) {
            $query->where('quality_status', 'passed');
        }
        
        // 过期时间要求
        if (isset($options['min_expiry_days'])) {
            $minExpiryDate = now()->addDays($options['min_expiry_days']);
            $query->where(function($q) use ($minExpiryDate) {
                $q->whereNull('expiry_date')
                  ->orWhere('expiry_date', '>', $minExpiryDate);
            });
        }
        
        return $query->get();
    }
} 