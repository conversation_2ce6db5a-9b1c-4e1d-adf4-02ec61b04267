<?php

namespace App\Points\Services;

use App\Models\User;
use App\Points\Models\PointsTransaction;
use App\Points\Models\PointsRule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PointsService
{
    /**
     * 给用户增加积分
     */
    public function addPoints(
        int $userId,
        int $points,
        string $source,
        ?int $sourceId = null,
        ?string $description = null,
        ?\DateTime $expiredAt = null
    ): bool {
        if ($points <= 0) {
            return false;
        }

        try {
            DB::beginTransaction();

            $user = User::findOrFail($userId);
            
            // 记录积分流水
            PointsTransaction::createTransaction(
                $userId,
                $points,
                PointsTransaction::TYPE_EARN,
                $source,
                $sourceId,
                $description,
                $expiredAt
            );

            // 更新用户积分
            $user->addPoints($points, $description);

            DB::commit();

            Log::info('用户积分增加成功', [
                'user_id' => $userId,
                'points' => $points,
                'source' => $source,
                'description' => $description
            ]);

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('用户积分增加失败', [
                'user_id' => $userId,
                'points' => $points,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 扣减用户积分
     */
    public function deductPoints(
        int $userId,
        int $points,
        string $source,
        ?int $sourceId = null,
        ?string $description = null
    ): bool {
        if ($points <= 0) {
            return false;
        }

        try {
            DB::beginTransaction();

            $user = User::findOrFail($userId);
            
            // 检查积分是否充足
            if ($user->member_points < $points) {
                throw new \Exception('积分不足');
            }

            // 记录积分流水
            PointsTransaction::createTransaction(
                $userId,
                -$points,
                PointsTransaction::TYPE_SPEND,
                $source,
                $sourceId,
                $description
            );

            // 扣减用户积分
            $user->deductPoints($points, $description);

            DB::commit();

            Log::info('用户积分扣减成功', [
                'user_id' => $userId,
                'points' => $points,
                'source' => $source,
                'description' => $description
            ]);

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('用户积分扣减失败', [
                'user_id' => $userId,
                'points' => $points,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 退还用户积分
     */
    public function refundPoints(
        int $userId,
        int $points,
        string $source,
        ?int $sourceId = null,
        ?string $description = null
    ): bool {
        return $this->addPoints(
            $userId,
            $points,
            $source,
            $sourceId,
            $description ?: '积分退还'
        );
    }

    /**
     * 根据规则给用户奖励积分
     */
    public function awardPointsByRule(int $userId, string $ruleType, array $data = []): bool
    {
        // 获取有效的积分规则
        $rules = PointsRule::valid()->byType($ruleType)->get();
        
        if ($rules->isEmpty()) {
            return false;
        }

        $totalAwarded = 0;

        foreach ($rules as $rule) {
            // 检查用户是否满足条件
            if (!$rule->checkConditions($data)) {
                continue;
            }

            // 检查每日限制
            if (!$rule->checkDailyLimit($userId)) {
                continue;
            }

            // 检查总次数限制
            if (!$rule->checkTotalLimit($userId)) {
                continue;
            }

            // 计算积分奖励
            $points = $rule->calculatePoints($data);
            
            if ($points > 0) {
                $description = $rule->name ?: $rule->getRuleTypeTextAttribute();
                $sourceId = data_get($data, 'source_id');
                
                if ($this->addPoints($userId, $points, $rule->getTransactionSource(), $sourceId, $description)) {
                    $totalAwarded += $points;
                }
            }
        }

        return $totalAwarded > 0;
    }

    /**
     * 获取用户积分余额
     */
    public function getUserPointsBalance(int $userId): int
    {
        $user = User::find($userId);
        return $user ? $user->member_points : 0;
    }

    /**
     * 获取用户积分流水
     */
    public function getUserPointsTransactions(int $userId, int $page = 1, int $perPage = 20): array
    {
        $transactions = PointsTransaction::byUser($userId)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        return [
            'data' => $transactions->items(),
            'current_page' => $transactions->currentPage(),
            'last_page' => $transactions->lastPage(),
            'per_page' => $transactions->perPage(),
            'total' => $transactions->total(),
        ];
    }

    /**
     * 获取用户积分统计
     */
    public function getUserPointsStats(int $userId): array
    {
        $user = User::find($userId);
        if (!$user) {
            return [];
        }

        // 总积分获得
        $totalEarned = PointsTransaction::byUser($userId)
            ->earning()
            ->sum('points');

        // 总积分消费
        $totalSpent = abs(PointsTransaction::byUser($userId)
            ->spending()
            ->sum('points'));

        // 本月获得积分
        $monthlyEarned = PointsTransaction::byUser($userId)
            ->earning()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('points');

        // 本月消费积分
        $monthlySpent = abs(PointsTransaction::byUser($userId)
            ->spending()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('points'));

        return [
            'current_balance' => $user->member_points,
            'total_earned' => $totalEarned,
            'total_spent' => $totalSpent,
            'monthly_earned' => $monthlyEarned,
            'monthly_spent' => $monthlySpent,
        ];
    }

    /**
     * 检查用户积分是否充足
     */
    public function checkPointsSufficient(int $userId, int $requiredPoints): bool
    {
        return $this->getUserPointsBalance($userId) >= $requiredPoints;
    }

    /**
     * 批量处理积分过期
     */
    public function processExpiredPoints(): int
    {
        $expiredTransactions = PointsTransaction::earning()
            ->expired()
            ->whereNull('processed_at')
            ->get();

        $processedCount = 0;

        foreach ($expiredTransactions as $transaction) {
            try {
                DB::beginTransaction();

                // 创建积分过期记录
                PointsTransaction::createTransaction(
                    $transaction->user_id,
                    -$transaction->points,
                    PointsTransaction::TYPE_EXPIRE,
                    PointsTransaction::SOURCE_EXPIRE,
                    $transaction->id,
                    "积分过期：{$transaction->description}"
                );

                // 扣减用户积分
                $user = User::find($transaction->user_id);
                if ($user) {
                    $user->deductPoints($transaction->points, '积分过期');
                }

                // 标记为已处理
                $transaction->update(['processed_at' => now()]);

                DB::commit();
                $processedCount++;

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('处理积分过期失败', [
                    'transaction_id' => $transaction->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $processedCount;
    }

    /**
     * 管理员调整用户积分
     */
    public function adminAdjustPoints(
        int $userId,
        int $points,
        string $reason,
        int $adminId
    ): bool {
        $description = "管理员调整：{$reason}（操作员ID：{$adminId}）";
        
        if ($points > 0) {
            return $this->addPoints(
                $userId,
                $points,
                PointsTransaction::SOURCE_ADMIN,
                $adminId,
                $description
            );
        } else {
            return $this->deductPoints(
                $userId,
                abs($points),
                PointsTransaction::SOURCE_ADMIN,
                $adminId,
                $description
            );
        }
    }

    /**
     * 获取积分排行榜
     */
    public function getPointsRanking(int $limit = 10): array
    {
        return User::whereNotNull('member_points')
            ->where('member_points', '>', 0)
            ->orderBy('member_points', 'desc')
            ->limit($limit)
            ->select('id', 'name', 'nickname', 'avatar', 'member_points')
            ->get()
            ->toArray();
    }

    /**
     * 订单完成后给予积分奖励
     *
     * @param int $userId 用户ID
     * @param int $orderId 订单ID
     * @param array $orderItems 订单项数据
     * @return int 总积分奖励数量
     */
    public function awardPointsForOrder($userId, $orderId, $orderItems)
    {
        $totalPoints = 0;

        foreach ($orderItems as $item) {
            $product = \App\Product\Models\Product::find($item['product_id']);
            if (!$product || !$product->hasPointsReward()) {
                continue;
            }

            // 计算该商品的积分奖励
            $itemAmount = $item['price'] * $item['quantity'];
            $points = $product->calculatePointsReward($itemAmount, $item['quantity']);

            if ($points > 0) {
                $description = "购买商品 {$product->name} 获得积分奖励";
                $this->addPoints($userId, $points, 'order', $orderId, $description);
                $totalPoints += $points;
            }
        }

        return $totalPoints;
    }

    /**
     * 批量处理订单积分奖励
     *
     * @param array $orders 订单数据数组
     * @return array 处理结果统计
     */
    public function batchAwardPointsForOrders($orders)
    {
        $results = [
            'total_orders' => count($orders),
            'success_count' => 0,
            'total_points' => 0,
            'errors' => []
        ];

        foreach ($orders as $order) {
            try {
                $points = $this->awardPointsForOrder(
                    $order['user_id'],
                    $order['id'],
                    $order['items']
                );

                if ($points > 0) {
                    $results['success_count']++;
                    $results['total_points'] += $points;
                }
            } catch (\Exception $e) {
                $results['errors'][] = [
                    'order_id' => $order['id'],
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * 获取商品积分奖励预览
     *
     * @param int $productId 商品ID
     * @param float $amount 金额
     * @param int $quantity 数量
     * @return array 积分奖励信息
     */
    public function getProductPointsRewardPreview($productId, $amount, $quantity = 1)
    {
        $product = \App\Product\Models\Product::find($productId);
        if (!$product) {
            return [
                'has_reward' => false,
                'points' => 0,
                'description' => '商品不存在'
            ];
        }

        $points = $product->calculatePointsReward($amount, $quantity);
        
        return [
            'has_reward' => $product->hasPointsReward(),
            'points' => $points,
            'description' => $product->getPointsRewardDescription(),
            'config' => $product->getPointsRewardConfig()
        ];
    }

    /**
     * 计算购物车总积分奖励预览
     *
     * @param array $cartItems 购物车项目
     * @return array 积分奖励预览
     */
    public function getCartPointsRewardPreview($cartItems)
    {
        $totalPoints = 0;
        $itemRewards = [];

        foreach ($cartItems as $item) {
            $productId = $item['product_id'];
            $quantity = $item['quantity'];
            $price = $item['price'];
            $amount = $price * $quantity;

            $reward = $this->getProductPointsRewardPreview($productId, $amount, $quantity);
            
            if ($reward['has_reward']) {
                $totalPoints += $reward['points'];
                $itemRewards[] = [
                    'product_id' => $productId,
                    'product_name' => $item['product_name'] ?? '',
                    'quantity' => $quantity,
                    'amount' => $amount,
                    'points' => $reward['points'],
                    'description' => $reward['description']
                ];
            }
        }

        return [
            'total_points' => $totalPoints,
            'item_rewards' => $itemRewards,
            'has_rewards' => $totalPoints > 0
        ];
    }
} 