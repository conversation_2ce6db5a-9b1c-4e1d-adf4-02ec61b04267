<?php

namespace App\Crm\Services;

use App\Crm\Models\CrmAgent;
use App\Employee\Models\Employee;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CrmAgentService
{
    /**
     * 获取CRM专员列表
     *
     * @param Request $request
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Pagination\LengthAwarePaginator
     */
    public function getAgents(Request $request)
    {
        $query = CrmAgent::with(['employee']);
        
        // 筛选条件
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }
        
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->whereHas('employee', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }
        
        // 排序
        if ($request->has('sort') && $request->sort) {
            $direction = $request->has('direction') && $request->direction === 'desc' ? 'desc' : 'asc';
            $query->orderBy($request->sort, $direction);
        } else {
            $query->latest();
        }
        
        // 返回分页或全部数据
        if ($request->has('per_page')) {
            return $query->paginate($request->per_page);
        }
        
        return $query->get();
    }
    
    /**
     * 获取CRM专员详情
     *
     * @param int $id
     * @return CrmAgent
     */
    public function getAgent($id)
    {
        return CrmAgent::with(['employee'])->findOrFail($id);
    }
    
    /**
     * 创建CRM专员
     *
     * @param array $data
     * @return CrmAgent
     */
    public function createAgent(array $data)
    {
        DB::beginTransaction();
        
        try {
            // 查找员工
            $employee = Employee::findOrFail($data['employee_id']);
            
            // 检查员工是否已经是CRM专员
            $existingAgent = CrmAgent::where('employee_id', $employee->id)->first();
            if ($existingAgent) {
                throw new \Exception('该员工已经是CRM专员');
            }
            
            // 创建CRM专员
            $agent = new CrmAgent();
            $agent->employee_id = $employee->id;
            $agent->service_area = $data['service_area'] ?? '';
            $agent->max_clients = $data['max_clients'] ?? 10;
            $agent->performance_rating = $data['performance_rating'] ?? 5.0;
            $agent->status = $data['status'] ?? 'available';
            $agent->specialty = $data['specialty'] ?? '';
            $agent->monthly_target = $data['monthly_target'] ?? 0;
            $agent->clients_count = 0;
            $agent->save();
            
            // 将员工角色设置为CRM专员
            if ($employee->role !== 'crm_agent') {
                $employee->role = 'crm_agent';
                $employee->save();
            }
            
            DB::commit();
            return $agent->load('employee');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建CRM专员失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 更新CRM专员信息
     *
     * @param int $id
     * @param array $data
     * @return CrmAgent
     */
    public function updateAgent($id, array $data)
    {
        $agent = CrmAgent::findOrFail($id);
        
        DB::beginTransaction();
        
        try {
            // 更新允许的字段
            if (isset($data['service_area'])) {
                $agent->service_area = $data['service_area'];
            }
            
            if (isset($data['max_clients'])) {
                $agent->max_clients = $data['max_clients'];
            }
            
            if (isset($data['performance_rating'])) {
                $agent->performance_rating = $data['performance_rating'];
            }
            
            if (isset($data['status'])) {
                $agent->status = $data['status'];
            }
            
            if (isset($data['specialty'])) {
                $agent->specialty = $data['specialty'];
            }
            
            if (isset($data['monthly_target'])) {
                $agent->monthly_target = $data['monthly_target'];
            }
            
            $agent->save();
            
            DB::commit();
            return $agent->load('employee');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('更新CRM专员失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 删除CRM专员
     *
     * @param int $id
     * @return bool
     */
    public function deleteAgent($id)
    {
        $agent = CrmAgent::findOrFail($id);
        
        DB::beginTransaction();
        
        try {
            // 检查是否有客户关联到该专员
            $clientsCount = User::where('crm_agent_id', $agent->employee_id)->count();
            if ($clientsCount > 0) {
                throw new \Exception('该CRM专员有关联的客户，无法删除');
            }
            
            // 删除专员
            $agent->delete();
            
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('删除CRM专员失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 更新CRM专员状态
     *
     * @param int $id
     * @param string $status
     * @return CrmAgent
     */
    public function updateStatus($id, $status)
    {
        $agent = CrmAgent::findOrFail($id);
        $agent->status = $status;
        $agent->save();
        
        return $agent;
    }
    
    /**
     * 获取CRM专员的客户列表
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getClients($id, Request $request)
    {
        $agent = CrmAgent::findOrFail($id);
        
        $query = User::where('crm_agent_id', $agent->employee_id);
        
        // 筛选和排序
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }
        
        // 排序
        if ($request->has('sort') && $request->sort) {
            $direction = $request->has('direction') && $request->direction === 'desc' ? 'desc' : 'asc';
            $query->orderBy($request->sort, $direction);
        } else {
            $query->latest();
        }
        
        return $query->paginate($request->input('per_page', 15));
    }
} 