<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 添加会员等级表的消费金额条件字段
        Schema::table('membership_levels', function (Blueprint $table) {
            $table->decimal('total_consumption_required', 10, 2)->default(0)->after('points_required')
                  ->comment('升级所需的累计消费金额');
            $table->decimal('single_order_required', 10, 2)->default(0)->after('total_consumption_required')
                  ->comment('单笔订单金额达到此值可直接升级');
        });
        
        // 添加用户表的累计消费金额
        Schema::table('users', function (Blueprint $table) {
            $table->decimal('total_consumption', 10, 2)->default(0)->after('points')
                  ->comment('用户累计消费金额');
            $table->decimal('max_single_order', 10, 2)->default(0)->after('total_consumption')
                  ->comment('用户历史最大单笔订单金额');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('membership_levels', function (Blueprint $table) {
            $table->dropColumn(['total_consumption_required', 'single_order_required']);
        });
        
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['total_consumption', 'max_single_order']);
        });
    }
}; 