<template>
  <div class="token-monitor" v-if="userStore.isLoggedIn">
    <el-tooltip :content="tooltipContent" placement="bottom">
      <div class="token-status" :class="statusClass">
        <el-icon><Clock /></el-icon>
        <span class="time-text">{{ timeText }}</span>
      </div>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Clock } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const remainingTime = ref(0)
let timer: NodeJS.Timeout | null = null

// 格式化时间显示
const timeText = computed(() => {
  const hours = Math.floor(remainingTime.value / 3600)
  const minutes = Math.floor((remainingTime.value % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}h${minutes}m`
  } else if (minutes > 0) {
    return `${minutes}m`
  } else {
    return `${remainingTime.value}s`
  }
})

// 状态样式
const statusClass = computed(() => {
  if (remainingTime.value < 300) { // 5分钟
    return 'danger'
  } else if (remainingTime.value < 1800) { // 30分钟
    return 'warning'
  } else {
    return 'normal'
  }
})

// 提示内容
const tooltipContent = computed(() => {
  const hours = Math.floor(remainingTime.value / 3600)
  const minutes = Math.floor((remainingTime.value % 3600) / 60)
  const seconds = remainingTime.value % 60
  
  return `登录状态剩余时间: ${hours}小时${minutes}分钟${seconds}秒`
})

// 更新剩余时间
const updateRemainingTime = () => {
  remainingTime.value = userStore.getTokenRemainingTime()
  
  // 如果时间小于5分钟，提醒用户
  if (remainingTime.value < 300 && remainingTime.value > 0) {
    console.warn('登录即将过期，请及时保存工作')
  }
  
  // 如果时间为0，自动退出登录
  if (remainingTime.value <= 0) {
    userStore.logoutUser()
  }
}

onMounted(() => {
  updateRemainingTime()
  // 每秒更新一次
  timer = setInterval(updateRemainingTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.token-monitor {
  display: inline-block;
}

.token-status {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.token-status.normal {
  color: #67c23a;
  background-color: #f0f9ff;
}

.token-status.warning {
  color: #e6a23c;
  background-color: #fdf6ec;
}

.token-status.danger {
  color: #f56c6c;
  background-color: #fef0f0;
  animation: blink 1s infinite;
}

.time-text {
  font-weight: 500;
  min-width: 40px;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}
</style> 