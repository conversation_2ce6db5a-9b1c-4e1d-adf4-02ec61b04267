<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 由于MySQL不支持直接修改列顺序，我们需要创建一个新表，然后复制数据
        // 这种方法只在开发环境实用，生产环境可能需要更复杂的方案
        
        // 临时禁用外键约束
        Schema::disableForeignKeyConstraints();
        
        // 1. 先修改现有的users表，确保有balance、points和joined_at字段
        if (!Schema::hasColumn('users', 'balance')) {
            Schema::table('users', function (Blueprint $table) {
                $table->decimal('balance', 10, 2)->default(0)->after('role');
            });
        }
        
        if (!Schema::hasColumn('users', 'points')) {
            Schema::table('users', function (Blueprint $table) {
                $table->integer('points')->default(0)->after('balance');
            });
        }
        
        if (!Schema::hasColumn('users', 'joined_at')) {
            Schema::table('users', function (Blueprint $table) {
                $table->timestamp('joined_at')->nullable()->after('points');
            });
        }
        
        if (!Schema::hasColumn('users', 'merchant_name')) {
            Schema::table('users', function (Blueprint $table) {
                $table->string('merchant_name')->nullable()->after('role');
            });
        }
        
        // 2. 创建一张新表，字段顺序按照我们想要的方式排列
        Schema::create('users_new', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('password');
            $table->string('phone')->nullable()->unique();
            $table->string('role')->default('customer');
            $table->string('merchant_name')->nullable()->comment('商户名称');
            $table->decimal('balance', 10, 2)->default(0)->comment('用户余额');
            $table->integer('points')->default(0)->comment('用户积分');
            $table->timestamp('joined_at')->nullable()->comment('加入时间');
            $table->string('nickname')->nullable();
            $table->string('avatar')->nullable();
            $table->unsignedInteger('gender')->nullable()->default(0);
            $table->string('province')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->nullable();
            $table->string('openid')->nullable()->index();
            $table->string('unionid')->nullable()->index();
            $table->unsignedBigInteger('default_deliverer_id')->nullable();
            $table->rememberToken();
            $table->timestamps();
            
            // 添加外键约束
            $table->foreign('default_deliverer_id')->references('id')->on('users')->nullOnDelete();
        });
        
        // 3. 显式指定列名复制数据
        $users = DB::table('users')->get();
        foreach ($users as $user) {
            DB::table('users_new')->insert([
                'id' => $user->id,
                'name' => $user->name,
                'password' => $user->password,
                'phone' => $user->phone ?? null,
                'role' => $user->role,
                'merchant_name' => $user->merchant_name ?? null,
                'balance' => $user->balance ?? 0,
                'points' => $user->points ?? 0,
                'joined_at' => $user->joined_at ?? now(),
                'nickname' => $user->nickname ?? null,
                'avatar' => $user->avatar ?? null,
                'gender' => $user->gender ?? 0,
                'province' => $user->province ?? null,
                'city' => $user->city ?? null,
                'country' => $user->country ?? null,
                'openid' => $user->openid ?? null,
                'unionid' => $user->unionid ?? null,
                'default_deliverer_id' => $user->default_deliverer_id ?? null,
                'remember_token' => $user->remember_token ?? null,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at
            ]);
        }
        
        // 4. 重命名表
        Schema::rename('users', 'users_old');
        Schema::rename('users_new', 'users');
        Schema::drop('users_old');
        
        // 重新启用外键约束
        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 因为这是字段顺序调整，down 方法不需要做特殊处理
        // 如果需要回滚，已经存在的表会被保留
    }
};
