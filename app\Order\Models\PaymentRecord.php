<?php

namespace App\Order\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Employee\Models\Employee;
use App\Models\WechatServicePayment;
use App\Models\WechatServiceRefund;

class PaymentRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'correction_id',
        'wechat_payment_id',
        'wechat_refund_id',
        'payment_type',
        'business_type',
        'amount',
        'payment_method',
        'transaction_id',
        'out_trade_no',
        'status',
        'paid_at',
        'refunded_at',
        'operated_by',
        'notes',
        'extra_data',
        // 货到付款结算相关字段
        'settlement_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'paid_at' => 'datetime',
        'refunded_at' => 'datetime',
        'extra_data' => 'array',
        // 货到付款结算相关字段
        'settlement_at' => 'datetime',
    ];

    /**
     * 关联订单
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * 关联订单更正
     */
    public function correction(): BelongsTo
    {
        return $this->belongsTo(OrderCorrection::class, 'correction_id');
    }

    /**
     * 关联微信支付记录
     */
    public function wechatPayment(): BelongsTo
    {
        return $this->belongsTo(WechatServicePayment::class, 'wechat_payment_id');
    }

    /**
     * 关联微信退款记录
     */
    public function wechatRefund(): BelongsTo
    {
        return $this->belongsTo(WechatServiceRefund::class, 'wechat_refund_id');
    }

    /**
     * 关联操作员
     */
    public function operator(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'operated_by');
    }

    /**
     * 获取支付方式中文名称
     */
    public function getPaymentMethodNameAttribute(): string
    {
        $methods = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'cash' => '现金',
            'bank_transfer' => '银行转账',
        ];

        return $methods[$this->payment_method] ?? $this->payment_method;
    }

    /**
     * 获取业务类型中文名称
     */
    public function getBusinessTypeNameAttribute(): string
    {
        $types = [
            'order_payment' => '订单付款',
            'correction_supplement' => '更正补款',
            'correction_refund' => '更正退款',
            'cod_final' => '货到付款最终收款',
        ];

        return $types[$this->business_type] ?? $this->business_type;
    }

    /**
     * 获取状态中文名称
     */
    public function getStatusNameAttribute(): string
    {
        $statuses = [
            'pending' => '待处理',
            'success' => '成功',
            'failed' => '失败',
            'refunded' => '已退款',
            'cancelled' => '已取消',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * 是否为微信支付
     */
    public function isWechatPayment(): bool
    {
        return $this->payment_method === 'wechat';
    }

    /**
     * 是否为补款
     */
    public function isSupplement(): bool
    {
        return $this->payment_type === 'supplement';
    }

    /**
     * 是否为退款
     */
    public function isRefund(): bool
    {
        return $this->payment_type === 'refund';
    }

    /**
     * 是否为货到付款
     */
    public function isCod(): bool
    {
        return $this->payment_type === 'cod';
    }
} 