import request from '../utils/request.js'

// 地址管理相关API
export default {
	// 获取用户地址列表
	getUserAddresses(userId) {
		return request.get(`/crm/users/${userId}/addresses`)
	},
	
	// 创建新地址 - 使用CRM专用的为指定用户创建地址API
	createAddress(userId, data) {
		return request.post(`/crm/users/${userId}/addresses`, data)
	},
	
	// 更新地址
	updateAddress(addressId, data) {
		return request.put(`/crm/user-addresses/${addressId}`, data)
	},
	
	// 删除地址
	deleteAddress(addressId) {
		return request.delete(`/crm/user-addresses/${addressId}`)
	},
	
	// 设置默认地址
	setDefaultAddress(addressId) {
		return request.put(`/crm/user-addresses/${addressId}/default`)
	},
	
	// 获取地址详情
	getAddressDetail(addressId) {
		return request.get(`/crm/user-addresses/${addressId}`)
	}
} 