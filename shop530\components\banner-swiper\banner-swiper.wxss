/* components/banner-swiper/banner-swiper.wxss */

.banner-swiper-container {
  width: 100%;
  height: 600rpx; /* 强制设置固定高度 */
  position: relative;
  overflow: hidden;
  border-radius: 0 0 32rpx 32rpx;
  
  z-index: 1; /* 设置较低的层级，让分类网格能覆盖在上方 */
}

/* 三层切割轮播效果 */
.banner-section-layered {
  position: relative;
  width: 100%;
  height: 600rpx; /* 强制设置固定高度 */
  overflow: hidden;
  background: transparent;
}

.banner-layer {
  position: absolute;
  width: 100%;
  height: 600rpx; /* 强制设置固定高度 */
  left: 0;
  top: 0;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 上层 - 显示图片上部分，模糊效果 */
.banner-layer-top {
  z-index: 1;
  clip-path: polygon(0 0, 100% 0, 100% 35%, 0 25%);
}

/* 中层 - 显示图片中部分，主要内容 */
.banner-layer-middle {
  z-index: 2;
  clip-path: polygon(0 20%, 100% 30%, 100% 80%, 0 75%);
}

/* 下层 - 显示图片下部分，装饰效果 */
.banner-layer-bottom {
  z-index: 0;
  clip-path: polygon(0 70%, 100% 75%, 100% 100%, 0 100%);
}

.banner-item-top,
.banner-item-middle,
.banner-item-bottom {
  width: 100%;
  height: 600rpx; /* 强制设置固定高度 */
  position: relative;
  overflow: hidden;
}

.banner-swiper-middle {
  width: 100%;
  height: 600rpx; /* 强制设置固定高度 */
}

/* 普通轮播效果 */
.banner-section-normal {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.banner-swiper-normal {
  width: 100%;
  height: 100%;
}

.banner-item-normal {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* 轮播图图片样式 */
.banner-image-top {
  width: 100%;
  height: 600rpx; /* 强制设置固定高度 */
  object-fit: cover;
  transition: all 0.4s ease-in-out;
}

.banner-image-middle {
  width: 100%;
  height: 600rpx; /* 强制设置固定高度 */
  object-fit: cover;
  transition: transform 0.3s ease;
}

.banner-image-bottom {
  width: 100%;
  height: 600rpx; /* 强制设置固定高度 */
  object-fit: cover;
  transition: all 0.5s ease-in-out;
}

.banner-image-normal {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

/* 点击效果 */
.banner-item-middle:active .banner-image-middle,
.banner-item-normal:active .banner-image-normal {
  transform: scale(0.98);
}

/* 空状态占位 */
.banner-placeholder {
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: 16rpx;
}

/* 性能优化 */
.banner-swiper-container {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

.banner-layer {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* 响应式设计 */
@media (max-width: 375px) {
  /* 小屏幕下调整clip-path的剪切区域 */
  .banner-layer-top {
    clip-path: polygon(0 0, 100% 0, 100% 40%, 0 30%);
  }
  
  .banner-layer-middle {
    clip-path: polygon(0 25%, 100% 35%, 100% 75%, 0 70%);
  }
  
  .banner-layer-bottom {
    clip-path: polygon(0 65%, 100% 70%, 100% 100%, 0 100%);
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .banner-placeholder {
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
  }
  
  .placeholder-text {
    color: #666;
  }
}

/* 动画效果 */
@keyframes bannerFadeIn {
  from {
    opacity: 0;
    transform: scale(1.1);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.banner-section-layered,
.banner-section-normal {
  animation: bannerFadeIn 0.6s ease-out;
} 