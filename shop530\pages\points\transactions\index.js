// pages/points/transactions/index.js - 积分流水记录页
const PointsAPI = require('../../../utils/pointsApi');
const app = getApp();

Page({
  data: {
    // 流水记录
    transactions: [],
    
    // 用户积分信息
    userBalance: 0,
    userStats: {},
    
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 分页
    page: 1,
    hasMore: true,
    
    // 筛选条件
    currentType: 'all',
    typeFilters: [
      { key: 'all', name: '全部' },
      { key: 'earn', name: '获得积分' },
      { key: 'spend', name: '消费积分' },
      { key: 'refund', name: '退还积分' },
      { key: 'expire', name: '积分过期' },
      { key: 'admin', name: '管理员操作' }
    ],
    
    // 时间筛选
    dateRange: '',
    showDatePicker: false,
    
    // 统计信息
    monthStats: {
      earned: 0,
      spent: 0,
      net: 0
    }
  },

  onLoad() {
    this.initPage();
  },

  onShow() {
    this.loadUserInfo();
  },

  onPullDownRefresh() {
    this.refreshTransactions();
  },

  onReachBottom() {
    this.loadMoreTransactions();
  },

  // ==================== 页面初始化 ====================

  async initPage() {
    wx.showLoading({ title: '加载中...' });

    try {
      await Promise.all([
        this.loadUserInfo(),
        this.loadTransactions(true),
        this.loadMonthStats()
      ]);
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  async refreshTransactions() {
    this.setData({
      refreshing: true,
      page: 1,
      transactions: [],
      hasMore: true
    });

    try {
      await Promise.all([
        this.loadUserInfo(),
        this.loadTransactions(true),
        this.loadMonthStats()
      ]);
    } catch (error) {
      console.error('刷新失败:', error);
    } finally {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  // ==================== 数据加载 ====================

  async loadUserInfo() {
    try {
      const [balance, stats] = await Promise.all([
        PointsAPI.getUserBalance(),
        PointsAPI.getUserStats()
      ]);

      this.setData({
        userBalance: balance.data.balance || 0,
        userStats: stats.data || {}
      });
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  async loadTransactions(reset = false) {
    if (this.data.loading) return;
    if (!reset && !this.data.hasMore) return;

    this.setData({ loading: true });

    try {
      const params = {
        page: reset ? 1 : this.data.page,
        per_page: 20
      };

      // 添加类型筛选
      if (this.data.currentType !== 'all') {
        params.type = this.data.currentType;
      }

      // 添加日期筛选
      if (this.data.dateRange) {
        params.date_range = this.data.dateRange;
      }

      const result = await PointsAPI.getUserTransactions(params);
      const newTransactions = result.data.data || [];

      this.setData({
        transactions: reset ? newTransactions : [...this.data.transactions, ...newTransactions],
        page: reset ? 2 : this.data.page + 1,
        hasMore: newTransactions.length >= params.per_page,
        loading: false
      });
    } catch (error) {
      console.error('加载流水记录失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  async loadMoreTransactions() {
    await this.loadTransactions();
  },

  async loadMonthStats() {
    try {
      const result = await PointsAPI.getEarnHistory();
      const stats = result.data.current_month || {};

      this.setData({
        monthStats: {
          earned: stats.earned || 0,
          spent: stats.spent || 0,
          net: (stats.earned || 0) - (stats.spent || 0)
        }
      });
    } catch (error) {
      console.error('加载月度统计失败:', error);
    }
  },

  // ==================== 用户操作 ====================

  onTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    
    if (type === this.data.currentType) return;

    this.setData({
      currentType: type,
      page: 1,
      transactions: [],
      hasMore: true
    });

    this.loadTransactions(true);
  },

  onDateRangeChange(e) {
    const range = e.detail.value;
    let dateRange = '';
    
    if (range === 'week') {
      dateRange = 'last_7_days';
    } else if (range === 'month') {
      dateRange = 'last_30_days';
    } else if (range === 'quarter') {
      dateRange = 'last_90_days';
    }

    this.setData({
      dateRange,
      page: 1,
      transactions: [],
      hasMore: true
    });

    this.loadTransactions(true);
  },

  toggleDatePicker() {
    this.setData({
      showDatePicker: !this.data.showDatePicker
    });
  },

  hideDatePicker() {
    this.setData({
      showDatePicker: false
    });
  },

  onDateChange(e) {
    const date = e.detail.value;
    this.setData({
      selectedDate: date,
      showDatePicker: false,
      page: 1,
      transactions: [],
      hasMore: true
    });
    this.loadTransactions(true);
  },

  // ==================== 页面跳转 ====================

  goToPointsRules() {
    wx.navigateTo({
      url: '/pages/points/rules/index'
    });
  },

  goToPointsMall() {
    wx.navigateTo({
      url: '/pages/points/index'
    });
  },

  // ==================== 工具方法 ====================

  formatPoints(points) {
    return PointsAPI.formatPoints(points);
  },

  formatPointsSource(source) {
    return PointsAPI.formatPointsSource(source);
  },

  formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      const hours = Math.floor(diff / (1000 * 60 * 60));
      if (hours === 0) {
        const minutes = Math.floor(diff / (1000 * 60));
        return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
      }
      return `${hours}小时前`;
    } else if (days === 1) {
      return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    }
  },

  getTransactionTypeColor(type) {
    const colorMap = {
      earn: '#4CAF50',
      spend: '#FF5722',
      refund: '#2196F3',
      expire: '#FF9800',
      admin: '#9C27B0'
    };
    return colorMap[type] || '#757575';
  },

  getTransactionIcon(source) {
    const iconMap = {
      order: 'shopping-cart',
      signin: 'calendar',
      invite: 'user-plus',
      review: 'star',
      share: 'share',
      birthday: 'gift',
      upgrade: 'trending-up',
      admin: 'settings',
      points_mall: 'shopping-bag',
      refund: 'rotate-ccw'
    };
    return iconMap[source] || 'circle';
  },

  isPositiveTransaction(type) {
    return ['earn', 'refund'].includes(type);
  },

  getTransactionPrefix(type) {
    return this.isPositiveTransaction(type) ? '+' : '-';
  },

  getMonthStatsColor(value) {
    if (value > 0) return '#4CAF50';
    if (value < 0) return '#FF5722';
    return '#757575';
  },

  formatMonthStatsText(key, value) {
    const labels = {
      earned: '本月获得',
      spent: '本月消费',
      net: '净变化'
    };
    
    const prefix = key === 'net' ? (value >= 0 ? '+' : '') : '';
    return `${labels[key]} ${prefix}${this.formatPoints(Math.abs(value))}`;
  }
}); 