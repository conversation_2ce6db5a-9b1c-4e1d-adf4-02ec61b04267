<?php

namespace App\Crm\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

class CustomerBehaviorAnalytics extends Model
{
    protected $table = 'customer_behavior_analytics';
    
    // 禁用updated_at字段，只保留created_at
    public $timestamps = false;
    
    protected $fillable = [
        'user_id',
        'session_id',
        'event_type',
        'event_data',
        'device_info',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'event_data' => 'array',
        'device_info' => 'array',
        'created_at' => 'datetime',
    ];

    // 事件类型常量
    const EVENT_PAGE_VIEW = 'page_view';
    const EVENT_PRODUCT_VIEW = 'product_view';
    const EVENT_CART_OPERATION = 'cart_operation';
    const EVENT_SEARCH = 'search';
    const EVENT_ORDER_BEHAVIOR = 'order_behavior';
    const EVENT_USER_ACTION = 'user_action';

    // 页面类型常量
    const PAGE_HOME = 'home';
    const PAGE_CATEGORY = 'category';
    const PAGE_PRODUCT_LIST = 'product_list';
    const PAGE_PRODUCT_DETAIL = 'product_detail';
    const PAGE_CART = 'cart';
    const PAGE_ORDER = 'order';
    const PAGE_PROFILE = 'profile';
    const PAGE_SEARCH = 'search';

    // 购物车操作类型常量
    const CART_ADD = 'add';
    const CART_REMOVE = 'remove';
    const CART_UPDATE = 'update';
    const CART_CLEAR = 'clear';

    // 订单行为类型常量
    const ORDER_CREATE = 'create';
    const ORDER_PAY = 'pay';
    const ORDER_CANCEL = 'cancel';
    const ORDER_CONFIRM = 'confirm_receipt';

    /**
     * 获取关联的用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取关联的会话
     */
    public function session(): BelongsTo
    {
        return $this->belongsTo(UserSession::class, 'session_id', 'session_id');
    }

    /**
     * 获取所有事件类型
     */
    public static function getEventTypes(): array
    {
        return [
            self::EVENT_PAGE_VIEW => '页面浏览',
            self::EVENT_PRODUCT_VIEW => '商品查看',
            self::EVENT_CART_OPERATION => '购物车操作',
            self::EVENT_SEARCH => '搜索行为',
            self::EVENT_ORDER_BEHAVIOR => '订单行为',
            self::EVENT_USER_ACTION => '用户操作',
        ];
    }

    /**
     * 获取所有页面类型
     */
    public static function getPageTypes(): array
    {
        return [
            self::PAGE_HOME => '首页',
            self::PAGE_CATEGORY => '分类页',
            self::PAGE_PRODUCT_LIST => '商品列表',
            self::PAGE_PRODUCT_DETAIL => '商品详情',
            self::PAGE_CART => '购物车',
            self::PAGE_ORDER => '订单页面',
            self::PAGE_PROFILE => '个人中心',
            self::PAGE_SEARCH => '搜索页面',
        ];
    }

    /**
     * 创建页面浏览事件
     */
    public static function createPageViewEvent(
        ?int $userId,
        string $sessionId,
        string $pageName,
        array $pageData = [],
        array $deviceInfo = [],
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): self {
        return self::create([
            'user_id' => $userId,
            'session_id' => $sessionId,
            'event_type' => self::EVENT_PAGE_VIEW,
            'event_data' => array_merge([
                'page_name' => $pageName,
                'enter_time' => now()->timestamp * 1000, // 毫秒时间戳
            ], $pageData),
            'device_info' => $deviceInfo,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
        ]);
    }

    /**
     * 创建商品浏览事件
     */
    public static function createProductViewEvent(
        ?int $userId,
        string $sessionId,
        int $productId,
        ?int $categoryId = null,
        string $source = 'list',
        array $additionalData = [],
        array $deviceInfo = [],
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): self {
        return self::create([
            'user_id' => $userId,
            'session_id' => $sessionId,
            'event_type' => self::EVENT_PRODUCT_VIEW,
            'event_data' => array_merge([
                'product_id' => $productId,
                'category_id' => $categoryId,
                'source' => $source,
                'view_time' => now()->timestamp * 1000,
            ], $additionalData),
            'device_info' => $deviceInfo,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
        ]);
    }

    /**
     * 创建购物车操作事件
     */
    public static function createCartOperationEvent(
        ?int $userId,
        string $sessionId,
        string $operation,
        ?int $productId = null,
        int $quantity = 0,
        float $price = 0,
        float $totalCartValue = 0,
        array $deviceInfo = [],
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): self {
        return self::create([
            'user_id' => $userId,
            'session_id' => $sessionId,
            'event_type' => self::EVENT_CART_OPERATION,
            'event_data' => [
                'operation' => $operation,
                'product_id' => $productId,
                'quantity' => $quantity,
                'price' => $price,
                'total_cart_value' => $totalCartValue,
                'operation_time' => now()->timestamp * 1000,
            ],
            'device_info' => $deviceInfo,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
        ]);
    }

    /**
     * 创建搜索事件
     */
    public static function createSearchEvent(
        ?int $userId,
        string $sessionId,
        string $keyword,
        array $filters = [],
        int $resultCount = 0,
        ?int $clickPosition = null,
        ?int $clickedProductId = null,
        array $deviceInfo = [],
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): self {
        return self::create([
            'user_id' => $userId,
            'session_id' => $sessionId,
            'event_type' => self::EVENT_SEARCH,
            'event_data' => [
                'keyword' => $keyword,
                'filters' => $filters,
                'result_count' => $resultCount,
                'click_position' => $clickPosition,
                'clicked_product_id' => $clickedProductId,
                'search_time' => now()->timestamp * 1000,
            ],
            'device_info' => $deviceInfo,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
        ]);
    }

    /**
     * 创建订单行为事件
     */
    public static function createOrderBehaviorEvent(
        ?int $userId,
        string $sessionId,
        string $action,
        ?int $orderId = null,
        float $orderAmount = 0,
        ?string $paymentMethod = null,
        array $additionalData = [],
        array $deviceInfo = [],
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): self {
        return self::create([
            'user_id' => $userId,
            'session_id' => $sessionId,
            'event_type' => self::EVENT_ORDER_BEHAVIOR,
            'event_data' => array_merge([
                'action' => $action,
                'order_id' => $orderId,
                'order_amount' => $orderAmount,
                'payment_method' => $paymentMethod,
                'action_time' => now()->timestamp * 1000,
            ], $additionalData),
            'device_info' => $deviceInfo,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
        ]);
    }

    /**
     * 按时间范围查询行为数据
     */
    public function scopeInTimeRange($query, $startTime, $endTime)
    {
        return $query->whereBetween('created_at', [$startTime, $endTime]);
    }

    /**
     * 按用户查询
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 按事件类型查询
     */
    public function scopeOfEventType($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * 按会话查询
     */
    public function scopeInSession($query, string $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }
} 