<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 默认存储驱动
    |--------------------------------------------------------------------------
    |
    | 此选项控制上传文件时使用的默认存储驱动
    | 支持: "local", "cos"
    |
    */
    'default_driver' => env('UPLOAD_DRIVER', 'local'),
    
    /*
    |--------------------------------------------------------------------------
    | 文件类型目录映射
    |--------------------------------------------------------------------------
    |
    | 这里定义了不同类型文件的存储目录结构
    | 支持的占位符: {date}, {type}
    |
    */
    'directories' => [
        'banner' => 'banners/{date}',
        'product' => 'products/{date}',
        'category' => 'categories/{date}',
        'category_icon' => 'categories/icons/{date}',
        'avatar' => 'avatars/{date}',
        'article' => 'articles/{date}',
        'attachment' => 'attachments/{date}/{type}',
    ],
    
    /*
    |--------------------------------------------------------------------------
    | 文件上传验证规则
    |--------------------------------------------------------------------------
    |
    | 这里定义了不同类型文件的上传验证规则
    |
    */
    'validation' => [
        'banner' => [
            'image' => 'required|file|image|max:5120', // 5MB
            'image_max_size' => 5120,
        ],
        'product' => [
            'image' => 'required|file|image|max:5120', // 5MB
            'image_max_size' => 5120,
        ],
        'category' => [
            'image' => 'required|file|image|max:2048', // 2MB
            'image_max_size' => 2048,
        ],
        'avatar' => [
            'image' => 'required|file|image|max:2048', // 2MB
            'image_max_size' => 2048,
        ],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | 允许的文件类型
    |--------------------------------------------------------------------------
    |
    | 这里定义了允许上传的文件类型
    |
    */
    'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
]; 