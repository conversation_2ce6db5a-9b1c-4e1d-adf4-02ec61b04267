<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('points_order_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('points_order_id')->comment('积分订单ID');
            $table->unsignedBigInteger('points_product_id')->comment('积分商品ID');
            $table->string('product_name')->comment('商品名称');
            $table->string('product_image')->nullable()->comment('商品图片');
            $table->unsignedInteger('points_price')->default(0)->comment('积分单价');
            $table->decimal('cash_price', 10, 2)->default(0)->comment('现金单价');
            $table->unsignedInteger('quantity')->default(1)->comment('数量');
            $table->unsignedInteger('total_points')->default(0)->comment('总积分');
            $table->decimal('total_cash', 10, 2)->default(0)->comment('总现金');
            $table->timestamps();

            $table->index('points_order_id');
            $table->index('points_product_id');
            $table->foreign('points_order_id')->references('id')->on('points_orders')->onDelete('cascade');
            $table->foreign('points_product_id')->references('id')->on('points_products')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('points_order_items');
    }
}; 