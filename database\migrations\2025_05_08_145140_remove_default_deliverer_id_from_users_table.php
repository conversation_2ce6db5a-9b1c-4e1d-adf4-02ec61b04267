<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * 从用户表中删除旧的配送员字段，仅保留员工配送员相关字段
     */
    public function up(): void
    {
        // 首先删除可能存在的外键约束
        Schema::table('users', function (Blueprint $table) {
            // 获取关于default_deliverer_id字段的所有外键约束
            $constraints = DB::select("
                SELECT CONSTRAINT_NAME 
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_NAME = 'users' 
                AND COLUMN_NAME = 'default_deliverer_id'
                AND CONSTRAINT_NAME != 'PRIMARY'
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            
            // 删除找到的所有约束
            foreach ($constraints as $constraint) {
                if (isset($constraint->CONSTRAINT_NAME)) {
                    try {
                        DB::statement('ALTER TABLE users DROP FOREIGN KEY ' . $constraint->CONSTRAINT_NAME);
                    } catch (\Exception $e) {
                        // 忽略错误，继续执行
                        continue;
                    }
                }
            }
            
            // 删除default_deliverer_id字段
            if (Schema::hasColumn('users', 'default_deliverer_id')) {
                $table->dropColumn('default_deliverer_id');
            }
        });
        
        // 确保default_employee_deliverer_id字段存在并有正确的外键约束
        Schema::table('users', function (Blueprint $table) {
            // 如果default_employee_deliverer_id字段不存在，则添加
            if (!Schema::hasColumn('users', 'default_employee_deliverer_id')) {
                $table->unsignedBigInteger('default_employee_deliverer_id')->nullable()->after('membership_level_id');
            }
            
            // 删除可能已存在的外键约束
            $constraints = DB::select("
                SELECT CONSTRAINT_NAME 
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_NAME = 'users' 
                AND COLUMN_NAME = 'default_employee_deliverer_id'
                AND CONSTRAINT_NAME != 'PRIMARY'
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            
            foreach ($constraints as $constraint) {
                if (isset($constraint->CONSTRAINT_NAME)) {
                    try {
                        DB::statement('ALTER TABLE users DROP FOREIGN KEY ' . $constraint->CONSTRAINT_NAME);
                    } catch (\Exception $e) {
                        // 忽略错误，继续执行
                        continue;
                    }
                }
            }
            
            // 添加新的外键约束
            $table->foreign('default_employee_deliverer_id')
                ->references('id')
                ->on('employees')
                ->onDelete('set null');
        });
    }

    /**
     * 恢复迁移，重新添加default_deliverer_id字段
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // 删除default_employee_deliverer_id的外键约束
            $constraints = DB::select("
                SELECT CONSTRAINT_NAME 
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_NAME = 'users' 
                AND COLUMN_NAME = 'default_employee_deliverer_id'
                AND CONSTRAINT_NAME != 'PRIMARY'
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            
            foreach ($constraints as $constraint) {
                if (isset($constraint->CONSTRAINT_NAME)) {
                    try {
                        DB::statement('ALTER TABLE users DROP FOREIGN KEY ' . $constraint->CONSTRAINT_NAME);
                    } catch (\Exception $e) {
                        // 忽略错误，继续执行
                        continue;
                    }
                }
            }
            
            // 添加default_deliverer_id字段
            if (!Schema::hasColumn('users', 'default_deliverer_id')) {
                $table->unsignedBigInteger('default_deliverer_id')->nullable()->after('membership_level_id');
                
                // 添加外键约束指向deliverers表
                $table->foreign('default_deliverer_id')
                    ->references('id')
                    ->on('deliverers')
                    ->onDelete('set null');
            }
        });
    }
};
