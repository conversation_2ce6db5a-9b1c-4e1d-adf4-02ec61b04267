# 订单更正与付款链接 API 文档

## 概述

本文档描述了生鲜订单更正和货到付款付款链接系统的API接口。

## 认证

所有管理端API都需要使用 `Bearer Token` 进行认证：

```
Authorization: Bearer {your-token}
```

## 订单更正 API

### 1. 获取订单更正列表

**GET** `/api/order-corrections`

**查询参数：**
- `order_id` (可选): 订单ID
- `status` (可选): 更正状态 (pending, confirmed, cancelled)
- `correction_type` (可选): 更正类型 (increase, decrease, no_change)
- `start_date` (可选): 开始日期
- `end_date` (可选): 结束日期
- `page` (可选): 页码，默认1
- `per_page` (可选): 每页数量，默认15

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "correction_no": "CR202506031841293800",
                "order_id": 123,
                "status": "pending",
                "correction_type": "increase",
                "original_total": 100.00,
                "corrected_total": 120.00,
                "difference_amount": 20.00,
                "correction_reason": "商品重量增加",
                "corrected_at": "2025-06-03 18:41:29",
                "order": {
                    "id": 123,
                    "order_no": "ORD20250603001",
                    "status": "corrected"
                }
            }
        ],
        "total": 50,
        "per_page": 15
    }
}
```

### 2. 创建订单更正

**POST** `/api/order-corrections`

**请求体：**
```json
{
    "order_id": 123,
    "correction_reason": "商品重量与实际不符",
    "items": [
        {
            "order_item_id": 456,
            "corrected_quantity": 2,
            "corrected_weight": 1.5,
            "corrected_price": 25.00,
            "corrected_total": 50.00,
            "reason": "实际重量增加"
        }
    ]
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "订单更正创建成功",
    "data": {
        "id": 1,
        "correction_no": "CR202506031841293800",
        "status": "pending",
        "items": [
            {
                "id": 1,
                "order_item_id": 456,
                "corrected_quantity": 2,
                "corrected_total": 50.00
            }
        ]
    }
}
```

### 3. 确认订单更正

**POST** `/api/order-corrections/{id}/confirm`

**响应示例：**
```json
{
    "code": 200,
    "message": "订单更正确认成功",
    "data": {
        "id": 1,
        "status": "confirmed",
        "confirmed_at": "2025-06-03 18:45:00"
    }
}
```

### 4. 取消订单更正

**POST** `/api/order-corrections/{id}/cancel`

**响应示例：**
```json
{
    "code": 200,
    "message": "订单更正已取消"
}
```

### 5. 获取订单更正详情

**GET** `/api/order-corrections/{id}`

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "correction_no": "CR202506031841293800",
        "order": {
            "id": 123,
            "order_no": "ORD20250603001"
        },
        "items": [
            {
                "id": 1,
                "order_item_id": 456,
                "original_quantity": 1,
                "corrected_quantity": 2,
                "difference_amount": 25.00
            }
        ],
        "payment_records": []
    }
}
```

## 付款链接 API

### 1. 生成货到付款付款链接

**POST** `/api/payment-links/cod`

**请求体：**
```json
{
    "order_id": 123
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "付款链接生成成功",
    "data": {
        "payment_link": {
            "id": "PL20250603184129ByhnBZD7",
            "order_id": 123,
            "amount": 100.00,
            "payment_type": "cod",
            "status": "active",
            "expires_at": "2025-07-03 18:41:29"
        },
        "qr_code_url": "https://example.com/qr/PL20250603184129ByhnBZD7.png",
        "payment_url": "https://example.com/payment/PL20250603184129ByhnBZD7"
    }
}
```

### 2. 生成补款链接

**POST** `/api/payment-links/supplement`

**请求体：**
```json
{
    "correction_id": 1
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "补款链接生成成功",
    "data": {
        "payment_link": {
            "id": "PL20250603184129ByhnBZD7",
            "correction_id": 1,
            "amount": 20.00,
            "payment_type": "supplement",
            "status": "active"
        },
        "qr_code_url": "https://example.com/qr/PL20250603184129ByhnBZD7.png",
        "payment_url": "https://example.com/payment/PL20250603184129ByhnBZD7"
    }
}
```

### 3. 手动标记已付款

**POST** `/api/payment-links/{linkId}/mark-paid`

**请求体：**
```json
{
    "payment_method": "cash",
    "notes": "客户现金支付"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "标记付款成功"
}
```

### 4. 取消付款链接

**POST** `/api/payment-links/{linkId}/cancel`

**响应示例：**
```json
{
    "code": 200,
    "message": "付款链接已取消"
}
```

## 用户端付款页面 API

### 1. 获取付款页面信息

**GET** `/payment/{linkId}`

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "payment_link": {
            "id": "PL20250603184129ByhnBZD7",
            "amount": 100.00,
            "payment_type": "cod",
            "status": "active"
        },
        "order_info": {
            "order_no": "ORD20250603001",
            "contact_name": "张三",
            "contact_phone": "13800138000"
        },
        "payment_amount": 100.00,
        "payment_type_name": "货到付款",
        "expires_at": "2025-07-03 18:41:29",
        "is_expired": false
    }
}
```

### 2. 付款回调处理

**POST** `/payment/{linkId}/callback`

**请求体：**
```json
{
    "payment_method": "wechat",
    "transaction_id": "wx123456789",
    "amount": 100.00
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "付款处理成功"
}
```

## 统计 API

### 1. 订单更正统计

**GET** `/api/order-corrections/statistics/overview`

**查询参数：**
- `start_date` (可选): 开始日期
- `end_date` (可选): 结束日期

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "total_corrections": 100,
        "pending_corrections": 10,
        "confirmed_corrections": 85,
        "cancelled_corrections": 5,
        "increase_corrections": 60,
        "decrease_corrections": 30,
        "no_change_corrections": 10,
        "total_difference_amount": 1500.00
    }
}
```

### 2. 付款链接统计

**GET** `/api/payment-links/statistics/overview`

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "total_links": 200,
        "active_links": 20,
        "paid_links": 150,
        "expired_links": 25,
        "cancelled_links": 5,
        "cod_links": 180,
        "supplement_links": 20,
        "total_amount": 15000.00
    }
}
```

## 错误码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 业务流程说明

### 订单更正流程

1. 订单送达后，发现商品与订单不符
2. 调用创建订单更正API，提交更正信息
3. 系统计算差额，确定更正类型
4. 调用确认订单更正API
5. 系统根据差额类型处理：
   - 无差额：直接确认收货
   - 订单减少：微信原路退款或货到付款扣除
   - 订单增加：微信补款或货到付款增加

### 货到付款付款流程

1. 货到付款订单送达后
2. 生成付款链接，发送给客户
3. 客户通过链接完成在线支付
4. 系统处理付款回调，更新订单状态

### 补款流程

1. 订单更正确认后，如需补款
2. 生成补款链接
3. 客户通过链接完成补款
4. 系统更新订单和更正状态 