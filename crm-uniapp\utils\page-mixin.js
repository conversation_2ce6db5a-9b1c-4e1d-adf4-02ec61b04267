import loadingManager from './loading-manager.js'
import cacheManager from './cache-manager.js'

/**
 * 页面加载优化混入
 * 提供统一的页面加载、缓存、错误处理逻辑
 */
export default {
	data() {
		return {
			// 页面状态
			pageLoading: false,
			pageError: null,
			pageEmpty: false,
			
			// 列表状态
			listData: [],
			listLoading: false,
			listError: null,
			hasMore: true,
			currentPage: 1,
			pageSize: 20,
			
			// 搜索状态
			searchKeyword: '',
			searchLoading: false,
			searchTimer: null,
			searchDebounceTime: 800, // 搜索防抖时间
			
			// 刷新状态
			refreshing: false,
			lastRefreshTime: 0,
			refreshThrottle: 2000, // 刷新节流时间
			
			// 缓存配置
			cacheEnabled: true,
			cacheType: 'default',
			cacheKey: 'list',
			
			// 页面标识
			pageId: '',
			loadingKey: ''
		}
	},
	
	onLoad(options) {
		// 生成页面唯一标识
		this.pageId = this.$mp.page.route || 'unknown'
		this.loadingKey = `${this.pageId}_${Date.now()}`
		
		console.log(`页面加载: ${this.pageId}`)
		
		// 调用页面初始化
		if (this.onPageLoad) {
			this.onPageLoad(options)
		}
	},
	
	onShow() {
		console.log(`页面显示: ${this.pageId}`)
		
		// 检查是否需要刷新数据
		if (this.shouldRefreshOnShow()) {
			this.refreshData()
		}
		
		// 调用页面显示回调
		if (this.onPageShow) {
			this.onPageShow()
		}
	},
	
	onHide() {
		console.log(`页面隐藏: ${this.pageId}`)
		
		// 清理定时器
		this.clearSearchTimer()
		
		// 调用页面隐藏回调
		if (this.onPageHide) {
			this.onPageHide()
		}
	},
	
	onUnload() {
		console.log(`页面卸载: ${this.pageId}`)
		
		// 清理加载状态
		loadingManager.hideLoading(this.loadingKey)
		
		// 清理定时器
		this.clearSearchTimer()
		
		// 调用页面卸载回调
		if (this.onPageUnload) {
			this.onPageUnload()
		}
	},
	
	onPullDownRefresh() {
		this.handlePullRefresh()
	},
	
	onReachBottom() {
		this.handleReachBottom()
	},
	
	methods: {
		/**
		 * 显示页面加载
		 */
		showPageLoading(title = '加载中...') {
			this.pageLoading = true
			loadingManager.showLoading(this.loadingKey, { title })
		},
		
		/**
		 * 隐藏页面加载
		 */
		hidePageLoading() {
			this.pageLoading = false
			loadingManager.hideLoading(this.loadingKey)
		},
		
		/**
		 * 显示列表加载
		 */
		showListLoading() {
			this.listLoading = true
		},
		
		/**
		 * 隐藏列表加载
		 */
		hideListLoading() {
			this.listLoading = false
		},
		
		/**
		 * 显示搜索加载
		 */
		showSearchLoading() {
			this.searchLoading = true
		},
		
		/**
		 * 隐藏搜索加载
		 */
		hideSearchLoading() {
			this.searchLoading = false
		},
		
		/**
		 * 显示Toast消息
		 */
		showToast(title, icon = 'none', duration = 2000) {
			loadingManager.showToast(title, icon, duration)
		},
		
		/**
		 * 处理页面错误
		 */
		handlePageError(error, context = '页面加载') {
			console.error(`${context}失败:`, error)
			
			this.pageError = error
			this.hidePageLoading()
			
			let message = `${context}失败`
			
			if (error.statusCode === 401) {
				message = '登录已过期，请重新登录'
				setTimeout(() => {
					uni.reLaunch({ url: '/pages/login/login' })
				}, 2000)
			} else if (error.statusCode === 403) {
				message = '权限不足，请联系管理员'
			} else if (error.statusCode >= 500) {
				message = '服务器错误，请稍后重试'
			} else if (!error.statusCode) {
				message = '网络连接失败，请检查网络'
			} else if (error.message) {
				message = error.message
			}
			
			this.showToast(message)
		},
		
		/**
		 * 处理列表错误
		 */
		handleListError(error, context = '加载数据') {
			console.error(`${context}失败:`, error)
			
			this.listError = error
			this.hideListLoading()
			this.hidePageLoading()
			
			// 如果是刷新操作，停止下拉刷新
			if (this.refreshing) {
				uni.stopPullDownRefresh()
				this.refreshing = false
			}
			
			this.handlePageError(error, context)
		},
		
		/**
		 * 检查是否应该在onShow时刷新
		 */
		shouldRefreshOnShow() {
			// 如果页面为空或有错误，需要刷新
			if (this.pageEmpty || this.pageError) {
				return true
			}
			
			// 如果列表为空，需要刷新
			if (Array.isArray(this.listData) && this.listData.length === 0) {
				return true
			}
			
			// 如果距离上次刷新时间超过阈值，需要刷新
			const now = Date.now()
			if (now - this.lastRefreshTime > 5 * 60 * 1000) { // 5分钟
				return true
			}
			
			return false
		},
		
		/**
		 * 刷新数据
		 */
		async refreshData() {
			// 节流控制
			const now = Date.now()
			if (now - this.lastRefreshTime < this.refreshThrottle) {
				console.log('刷新过于频繁，跳过')
				return
			}
			
			this.lastRefreshTime = now
			this.refreshing = true
			
			try {
				// 清除相关缓存
				if (this.cacheEnabled) {
					cacheManager.clearType(this.cacheType)
				}
				
				// 重置状态
				this.pageError = null
				this.listError = null
				this.pageEmpty = false
				this.currentPage = 1
				this.hasMore = true
				
				// 调用数据加载方法
				if (this.loadData) {
					await this.loadData(true)
				}
				
			} catch (error) {
				this.handleListError(error, '刷新数据')
			} finally {
				this.refreshing = false
				uni.stopPullDownRefresh()
			}
		},
		
		/**
		 * 加载更多数据
		 */
		async loadMoreData() {
			if (this.listLoading || !this.hasMore) {
				return
			}
			
			try {
				this.showListLoading()
				this.currentPage++
				
				if (this.loadData) {
					await this.loadData(false)
				}
				
			} catch (error) {
				this.currentPage-- // 回滚页码
				this.handleListError(error, '加载更多')
			} finally {
				this.hideListLoading()
			}
		},
		
		/**
		 * 处理下拉刷新
		 */
		handlePullRefresh() {
			console.log('下拉刷新触发')
			this.refreshData()
		},
		
		/**
		 * 处理触底加载
		 */
		handleReachBottom() {
			console.log('触底加载触发')
			this.loadMoreData()
		},
		
		/**
		 * 处理搜索输入
		 */
		handleSearchInput(value) {
			this.searchKeyword = value
			
			// 清除之前的定时器
			this.clearSearchTimer()
			
			// 如果搜索词为空，立即执行
			if (!value || !value.trim()) {
				this.performSearch()
				return
			}
			
			// 设置防抖定时器
			this.searchTimer = setTimeout(() => {
				this.performSearch()
			}, this.searchDebounceTime)
		},
		
		/**
		 * 执行搜索
		 */
		async performSearch() {
			try {
				this.showSearchLoading()
				
				// 重置列表状态
				this.currentPage = 1
				this.hasMore = true
				this.listError = null
				
				// 调用搜索方法
				if (this.searchData) {
					await this.searchData(this.searchKeyword)
				}
				
			} catch (error) {
				this.handleListError(error, '搜索')
			} finally {
				this.hideSearchLoading()
			}
		},
		
		/**
		 * 清除搜索定时器
		 */
		clearSearchTimer() {
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
				this.searchTimer = null
			}
		},
		
		/**
		 * 使用缓存加载数据
		 */
		async loadWithCache(fetchFn, params = {}, customTtl = null) {
			if (!this.cacheEnabled) {
				return await fetchFn()
			}
			
			return await cacheManager.getOrSet(
				this.cacheType,
				this.cacheKey,
				fetchFn,
				params,
				customTtl
			)
		},
		
		/**
		 * 清除页面缓存
		 */
		clearPageCache() {
			if (this.cacheEnabled) {
				cacheManager.delete(this.cacheType, this.cacheKey)
			}
		},
		
		/**
		 * 设置列表数据
		 */
		setListData(data, isRefresh = false) {
			if (isRefresh) {
				this.listData = data
			} else {
				// 加载更多时需要去重，避免重复数据
				const existingIds = new Set(this.listData.map(item => item.id))
				const newData = data.filter(item => !existingIds.has(item.id))
				this.listData = [...this.listData, ...newData]
				
				console.log(`去重处理: 新数据${data.length}条，去重后${newData.length}条，总计${this.listData.length}条`)
			}
			
			// 检查是否为空
			this.pageEmpty = this.listData.length === 0
		},
		
		/**
		 * 更新分页状态
		 */
		updatePagination(response) {
			// 根据响应格式更新分页状态
			if (response.data && response.data.last_page) {
				this.hasMore = response.data.current_page < response.data.last_page
			} else if (Array.isArray(response.data)) {
				this.hasMore = response.data.length === this.pageSize
			} else {
				this.hasMore = false
			}
		}
	}
} 