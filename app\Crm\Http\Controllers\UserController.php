<?php

namespace App\Crm\Http\Controllers;

use App\Api\Models\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Employee\Models\Employee;
use App\Models\Deliverer;
use App\Crm\Models\MembershipLevel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;

class UserController extends Controller
{
    /**
     * 获取用户列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 修改认证方式，使用sanctum
            $currentEmployee = auth('sanctum')->user();

            if (!$currentEmployee) {
                return response()->json(ApiResponse::error('未授权访问，请先登录', 401), 401);
            }

            // 检查是否有users或users_view权限
            if (!in_array($currentEmployee->role, ['admin', 'manager', 'staff', 'crm_agent', 'delivery', 'warehouse_manager'])) {
                return response()->json(ApiResponse::error('没有权限查看用户列表', 403), 403);
            }
            
            // 最简单的查询，不加载任何关联
            $users = User::query();

            if ($request->has('status') && $request->status) {
                $users->where('status', $request->status);
            }

            // CRM专员只能看到自己的客户
            if ($currentEmployee->role === 'crm_agent') {
                $users->where('crm_agent_id', $currentEmployee->id);
            }

            // 分页
            $perPage = $request->input('per_page', 20);
            
            $users = $users->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'data' => $users->items(),
                'total' => $users->total(),
                'current_page' => $users->currentPage(),
                'per_page' => $users->perPage(),
                'debug' => [
                    'employee_role' => $currentEmployee->role,
                    'employee_id' => $currentEmployee->id,
                    'query_executed' => true,
                    'total_users' => $users->total(),
                    'current_page_count' => count($users->items())
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取用户列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            
            return response()->json([
                'error' => '获取用户列表失败: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ], 500);
        }
    }

    /**
     * 创建新用户
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // 修改认证方式，使用sanctum
        $currentEmployee = auth('sanctum')->user();

        if (!$currentEmployee || $currentEmployee->role !== 'admin') {
            return response()->json(ApiResponse::error('没有权限创建用户', 403), 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20|unique:users',
            'password' => 'required|string|min:8',
            'employee_id' => 'nullable|exists:employees,id',
            'default_deliverer_id' => 'nullable|exists:deliverers,id',
            'default_employee_deliverer_id' => 'nullable|exists:employees,id',
            'crm_agent_id' => 'nullable|exists:employees,id',
            'membership_level_id' => 'nullable|exists:membership_levels,id',
            'nickname' => 'nullable|string|max:50',
            'avatar' => 'nullable|string',
            'merchant_name' => 'nullable|string|max:100',
            'balance' => 'nullable|numeric|min:0',
            'member_points' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        DB::beginTransaction();
        try {
            // 创建用户
            $userData = [
                'name' => $request->name,
                'password' => Hash::make($request->password),
                'joined_at' => now(),
            ];
            
            // 添加可选字段
            if ($request->has('phone')) {
                $userData['phone'] = $request->phone;
            }
            
            if ($request->has('nickname')) {
                $userData['nickname'] = $request->nickname;
            }
            
            if ($request->has('avatar')) {
                $userData['avatar'] = $request->avatar;
            }
            
            // 处理商户名称
            if ($request->has('merchant_name')) {
                $userData['merchant_name'] = $request->merchant_name;
            }
            
            // 处理余额和积分
            if ($request->has('balance')) {
                $userData['balance'] = $request->balance;
            }
            
            // 处理积分和会员等级
            if ($request->has('member_points')) {
                $userData['member_points'] = $request->member_points;
            }
            
            // 处理会员等级
            if ($request->has('membership_level_id')) {
                $userData['membership_level_id'] = $request->membership_level_id;
                $userData['level_upgraded_at'] = now();
            } else {
                // 如果没有指定会员等级，但提供了积分，根据积分自动匹配会员等级
                if ($request->has('member_points') && $request->member_points > 0) {
                    $level = MembershipLevel::getLevelByPoints($request->member_points);
                    if ($level) {
                        $userData['membership_level_id'] = $level->id;
                        $userData['level_upgraded_at'] = now();
                    }
                } else {
                    // 如果没有积分，使用默认会员等级
                    $defaultLevel = MembershipLevel::getDefault();
                    if ($defaultLevel) {
                        $userData['membership_level_id'] = $defaultLevel->id;
                        $userData['level_upgraded_at'] = now();
                    }
                }
            }
            
            // 处理配送员和CRM专员关联
            if ($request->has('default_deliverer_id')) {
                $userData['default_deliverer_id'] = $request->default_deliverer_id;
            }
            
            if ($request->has('default_employee_deliverer_id')) {
                $userData['default_employee_deliverer_id'] = $request->default_employee_deliverer_id;
            }
            
            if ($request->has('crm_agent_id')) {
                $userData['crm_agent_id'] = $request->crm_agent_id;
            }
            
            $user = User::create($userData);

            // 如果提供了员工ID，绑定关系
            if ($request->has('employee_id') && $request->employee_id) {
                $employee = Employee::findOrFail($request->employee_id);
                $employee->user_id = $user->id;
                $employee->save();
            }
            
            DB::commit();
            return response()->json(ApiResponse::success($user->load(['employee', 'defaultEmployeeDeliverer', 'crmAgent', 'membershipLevel']), '用户创建成功'), 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('创建用户失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取用户详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $currentEmployee = auth('sanctum')->user();
        
        // 修改权限检查，基于员工角色
        if (!$currentEmployee || ($currentEmployee->role !== 'admin' && $currentEmployee->user_id != $id)) {
            return response()->json(ApiResponse::error('没有权限查看此用户', 403), 403);
        }

        $user = User::with(['employee', 'defaultEmployeeDeliverer', 'crmAgent', 'membershipLevel'])->findOrFail($id);
        return response()->json(ApiResponse::success($user));
    }

    /**
     * 更新用户信息
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $currentEmployee = auth('sanctum')->user();
        
        // 修改权限检查，基于员工角色
        if (!$currentEmployee || ($currentEmployee->role !== 'admin' && $currentEmployee->user_id != $id)) {
            return response()->json(ApiResponse::error('没有权限更新此用户', 403), 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'phone' => [
                'sometimes',
                'nullable',
                'string',
                'max:20',
                Rule::unique('users')->ignore($id),
            ],
            'password' => 'sometimes|string|min:8',
            'employee_id' => 'nullable|exists:employees,id',
            'default_deliverer_id' => 'nullable|exists:deliverers,id',
            'default_employee_deliverer_id' => 'nullable|exists:employees,id',
            'crm_agent_id' => 'nullable|exists:employees,id',
            'nickname' => 'sometimes|nullable|string|max:50',
            'avatar' => 'sometimes|nullable|string',
            'gender' => 'sometimes|nullable|integer|in:0,1,2',
            'province' => 'sometimes|nullable|string|max:50',
            'city' => 'sometimes|nullable|string|max:50',
            'country' => 'sometimes|nullable|string|max:50',
            'merchant_name' => 'sometimes|nullable|string|max:100',
            'balance' => 'sometimes|nullable|numeric|min:0',
            'member_points' => 'sometimes|nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        DB::beginTransaction();
        try {
            $user = User::findOrFail($id);
            
            // 基本信息更新
            $updateFields = [
                'name', 'phone', 'nickname', 'avatar', 
                'gender', 'province', 'city', 'country',
                'merchant_name', 'balance', 'member_points',
                'default_deliverer_id', 'default_employee_deliverer_id', 'crm_agent_id'
            ];
            
            foreach ($updateFields as $field) {
                if ($request->has($field)) {
                    $user->$field = $request->input($field);
                }
            }
            
            // 密码更新
            if ($request->has('password')) {
                $user->password = Hash::make($request->password);
            }
            
            $user->save();
            
            // 处理员工关联
            if ($request->has('employee_id')) {
                // 先解除可能的旧关联
                Employee::where('user_id', $user->id)->update(['user_id' => null]);
                
                // 如果提供了新的员工ID，建立新关联
                if ($request->employee_id) {
                    $employee = Employee::findOrFail($request->employee_id);
                    $employee->user_id = $user->id;
                    $employee->save();
                }
            }
            
            DB::commit();
            return response()->json(ApiResponse::success($user->load(['employee', 'defaultEmployeeDeliverer', 'crmAgent']), '用户信息更新成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('更新用户失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 删除用户
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $currentEmployee = auth('sanctum')->user();
        
        // 修改权限检查，基于员工角色
        if (!$currentEmployee || $currentEmployee->role !== 'admin') {
            return response()->json(ApiResponse::error('没有权限删除用户', 403), 403);
        }
        
        // 不能删除自己关联的用户
        if ($currentEmployee->user_id == $id) {
            return response()->json(ApiResponse::error('不能删除当前登录的账号', 400), 400);
        }

        DB::beginTransaction();
        try {
            $user = User::findOrFail($id);
            
            // 解除员工关联
            Employee::where('user_id', $user->id)->update(['user_id' => null]);
            
            $user->delete();
            
            DB::commit();
            return response()->json(ApiResponse::success(null, '用户删除成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('删除用户失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 获取用户可绑定的员工列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function availableEmployees()
    {
        // 修改权限检查，基于员工角色
        $currentEmployee = auth('sanctum')->user();
        
        if (!$currentEmployee || $currentEmployee->role !== 'admin') {
            return response()->json(ApiResponse::error('没有权限查看可绑定的员工列表', 403), 403);
        }
        
        // 获取未绑定用户的员工列表
        $employees = Employee::whereNull('user_id')->get(['id', 'name', 'username', 'position']);
        
        return response()->json(ApiResponse::success($employees));
    }
    
    /**
     * 微信用户同步
     * 统计微信用户信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncWechatUsers(Request $request)
    {
        // 修改权限检查，基于员工角色
        $currentEmployee = auth('sanctum')->user();
        
        if (!$currentEmployee || $currentEmployee->role !== 'admin') {
            return response()->json(ApiResponse::error('没有权限同步微信用户', 403), 403);
        }
        
        try {
            // 查找所有有openid的微信用户
            $wechatUsers = User::whereNotNull('openid')->count();
            $phoneVerified = User::whereNotNull('openid')->whereNotNull('phone')->count();
            
            return response()->json(ApiResponse::success([
                'total' => $wechatUsers,
                'verified' => $phoneVerified,
                'unverified' => $wechatUsers - $phoneVerified
            ], '微信用户统计完成'));
            
        } catch (\Exception $e) {
            Log::error('同步微信用户失败', ['error' => $e->getMessage()]);
            return response()->json(ApiResponse::error('微信用户统计失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 更新用户余额
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateBalance(Request $request, $id)
    {
        // 修改权限检查，基于员工角色
        $currentEmployee = auth('sanctum')->user();
        
        if (!$currentEmployee || $currentEmployee->role !== 'admin') {
            return response()->json(ApiResponse::error('没有权限更新用户余额', 403), 403);
        }

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric',
            'type' => 'required|in:add,subtract,set',
            'description' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        DB::beginTransaction();
        try {
            $user = User::findOrFail($id);
            $oldBalance = $user->balance;
            $amount = $request->amount;
            $description = $request->description ?? '';
            
            switch ($request->type) {
                case 'add':
                    $user->balance += $amount;
                    $description = "增加余额: +{$amount} " . $description;
                    break;
                case 'subtract':
                    if ($user->balance < $amount) {
                        return response()->json(ApiResponse::error('用户余额不足', 422), 422);
                    }
                    $user->balance -= $amount;
                    $description = "减少余额: -{$amount} " . $description;
                    break;
                case 'set':
                    $user->balance = $amount;
                    $description = "设置余额: {$amount} " . $description;
                    break;
            }
            
            $user->save();
            
            // 记录余额变动日志（假设有BalanceLog模型）
            // BalanceLog::create([
            //     'user_id' => $user->id,
            //     'amount' => $request->type === 'subtract' ? -$amount : $amount,
            //     'balance_before' => $oldBalance,
            //     'balance_after' => $user->balance,
            //     'description' => $description,
            //     'operator_id' => $currentEmployee->id,
            // ]);
            
            DB::commit();
            return response()->json(ApiResponse::success([
                'user_id' => $user->id,
                'old_balance' => $oldBalance,
                'new_balance' => $user->balance,
                'amount' => $request->type === 'subtract' ? -$amount : $amount,
            ], '余额更新成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('更新余额失败: ' . $e->getMessage(), 500), 500);
        }
    }
    
    /**
     * 更新用户积分
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePoints(Request $request, $id)
    {
        // 修改权限检查，基于员工角色
        $currentEmployee = auth('sanctum')->user();
        
        if (!$currentEmployee || $currentEmployee->role !== 'admin') {
            return response()->json(ApiResponse::error('没有权限更新用户积分', 403), 403);
        }

        $validator = Validator::make($request->all(), [
            'amount' => 'required|integer',
            'type' => 'required|in:add,subtract,set',
            'description' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }

        DB::beginTransaction();
        try {
            $user = User::findOrFail($id);
            $oldPoints = $user->member_points;
            $amount = $request->amount;
            $description = $request->description ?? '';
            
            switch ($request->type) {
                case 'add':
                    $user->member_points += $amount;
                    $description = "增加积分: +{$amount} " . $description;
                    break;
                case 'subtract':
                    if ($user->member_points < $amount) {
                        return response()->json(ApiResponse::error('用户积分不足', 422), 422);
                    }
                    $user->member_points -= $amount;
                    $description = "减少积分: -{$amount} " . $description;
                    break;
                case 'set':
                    $user->member_points = $amount;
                    $description = "设置积分: {$amount} " . $description;
                    break;
            }
            
            $user->save();
            
            // 记录积分变动日志（假设有PointsLog模型）
            // PointsLog::create([
            //     'user_id' => $user->id,
            //     'amount' => $request->type === 'subtract' ? -$amount : $amount,
            //     'points_before' => $oldPoints,
            //     'points_after' => $user->member_points,
            //     'description' => $description,
            //     'operator_id' => $currentEmployee->id,
            // ]);
            
            DB::commit();
            return response()->json(ApiResponse::success([
                'user_id' => $user->id,
                'old_points' => $oldPoints,
                'new_points' => $user->member_points,
                'amount' => $request->type === 'subtract' ? -$amount : $amount,
            ], '积分更新成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(ApiResponse::error('更新积分失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取可绑定的配送员列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function availableDeliverers()
    {
        // 修改认证方式，使用sanctum
        $currentEmployee = auth('sanctum')->user();
        
        if (!$currentEmployee) {
            return response()->json(ApiResponse::error('未授权访问，请先登录', 401), 401);
        }
        
        // 允许更多角色访问配送员列表
        if (!in_array($currentEmployee->role, ['admin', 'manager', 'staff', 'delivery', 'warehouse_manager'])) {
            return response()->json(ApiResponse::error('没有权限查看可绑定的配送员列表', 403), 403);
        }
        
        // 简化查询，避免依赖关联关系
        $deliverers = Employee::where('role', 'delivery')
            ->select(['id', 'user_id', 'name', 'username', 'phone', 'role'])
            ->get()
            ->map(function ($employee) {
                return [
                    'id' => $employee->id,
                    'name' => $employee->name,
                    'phone' => $employee->phone,
                    'username' => $employee->username,
                    'role' => $employee->role
                ];
            });
            
        return response()->json(ApiResponse::success($deliverers));
    }
    
    /**
     * 获取可绑定的CRM专员列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function availableCrmAgents()
    {
        // 修改认证方式，使用sanctum
        $currentEmployee = auth('sanctum')->user();
        
        if (!$currentEmployee) {
            return response()->json(ApiResponse::error('未授权访问，请先登录', 401), 401);
        }
        
        // 允许更多角色访问CRM专员列表
        if (!in_array($currentEmployee->role, ['admin', 'manager', 'staff', 'crm_agent'])) {
            return response()->json(ApiResponse::error('没有权限查看可绑定的CRM专员列表', 403), 403);
        }
        
        // 获取所有CRM专员角色的员工，不加载user关联
        $agents = Employee::where('role', 'crm_agent')
                  ->select(['id', 'user_id', 'name', 'username', 'position', 'phone'])
                  ->get()
                  ->map(function ($agent) {
                      // 返回需要的字段，避免依赖关联
                      return [
                          'id' => $agent->id,
                          'name' => $agent->name,
                          'username' => $agent->username,
                          'position' => $agent->position,
                          'phone' => $agent->phone,
                          'user_id' => $agent->user_id
                      ];
                  });
        
        return response()->json(ApiResponse::success($agents));
    }

    /**
     * 更新用户会员等级
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateMembershipLevel(Request $request, $id)
    {
        // 修改权限检查，基于员工角色
        $currentEmployee = auth('sanctum')->user();
        
        if (!$currentEmployee || $currentEmployee->role !== 'admin') {
            return response()->json(ApiResponse::error('没有权限更新会员等级', 403), 403);
        }
        
        $validator = Validator::make($request->all(), [
            'membership_level_id' => 'required|exists:membership_levels,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
        }
        
        try {
            $user = User::findOrFail($id);
            $level = MembershipLevel::findOrFail($request->membership_level_id);
            
            $user->membership_level_id = $level->id;
            $user->level_upgraded_at = now();
            $user->save();
            
            // 返回更新后的用户信息，包含会员等级关系
            return response()->json(ApiResponse::success(
                $user->load(['membershipLevel']), 
                '会员等级更新成功'
            ));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('更新会员等级失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 手动刷新用户会员等级（根据积分）
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function refreshMembershipLevel($id)
    {
        // 修改权限检查，基于员工角色
        $currentEmployee = auth('sanctum')->user();
        
        if (!$currentEmployee || $currentEmployee->role !== 'admin') {
            return response()->json(ApiResponse::error('没有权限刷新会员等级', 403), 403);
        }
        
        try {
            $user = User::findOrFail($id);
            
            // 调用更新会员等级方法
            $isUpgraded = $user->updateMembershipLevel();
            
            // 根据是否升级返回不同消息
            $message = $isUpgraded ? '会员等级已更新' : '会员等级无需更新';
            
            // 返回更新后的用户信息
            return response()->json(ApiResponse::success(
                $user->load(['membershipLevel']), 
                $message
            ));
        } catch (\Exception $e) {
            return response()->json(ApiResponse::error('刷新会员等级失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取用户统计信息
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserStatistics($id)
    {
        try {
            // 修改认证方式，使用sanctum
            $currentEmployee = auth('sanctum')->user();

            if (!$currentEmployee) {
                return response()->json(ApiResponse::error('未授权访问，请先登录', 401), 401);
            }

            // 检查权限
            if (!in_array($currentEmployee->role, ['admin', 'manager', 'staff', 'crm_agent'])) {
                return response()->json(ApiResponse::error('没有权限查看用户统计', 403), 403);
            }

            // 查找用户
            $user = User::with(['orders', 'membershipLevel'])->find($id);
            if (!$user) {
                return response()->json(ApiResponse::error('用户不存在', 404), 404);
            }

            // CRM专员只能查看自己的客户
            if ($currentEmployee->role === 'crm_agent' && $user->crm_agent_id !== $currentEmployee->id) {
                return response()->json(ApiResponse::error('没有权限查看该用户统计', 403), 403);
            }

            // 计算统计数据
            $statistics = [
                // 基本信息
                'user_id' => $user->id,
                'user_name' => $user->name,
                'user_phone' => $user->phone,
                'membership_level' => $user->membershipLevel ? $user->membershipLevel->name : '普通会员',
                'balance' => $user->balance ?? 0,
                'member_points' => $user->member_points ?? 0,
                'joined_at' => $user->joined_at,
                
                // 订单统计
                'total_orders' => $user->orders->count(),
                'completed_orders' => $user->orders->where('status', 'completed')->count(),
                'pending_orders' => $user->orders->where('status', 'pending')->count(),
                'cancelled_orders' => $user->orders->where('status', 'cancelled')->count(),
                
                // 消费统计
                'total_amount' => $user->orders->where('status', 'completed')->sum('total'),
                'average_order_amount' => $user->orders->where('status', 'completed')->count() > 0 
                    ? $user->orders->where('status', 'completed')->avg('total') 
                    : 0,
                
                // 时间统计
                'last_order_at' => $user->orders->max('created_at'),
                'first_order_at' => $user->orders->min('created_at'),
                
                // 最近30天统计
                'recent_30_days' => [
                    'orders_count' => $user->orders->where('created_at', '>=', now()->subDays(30))->count(),
                    'total_amount' => $user->orders
                        ->where('created_at', '>=', now()->subDays(30))
                        ->where('status', 'completed')
                        ->sum('total'),
                ],
                
                // 最近7天统计
                'recent_7_days' => [
                    'orders_count' => $user->orders->where('created_at', '>=', now()->subDays(7))->count(),
                    'total_amount' => $user->orders
                        ->where('created_at', '>=', now()->subDays(7))
                        ->where('status', 'completed')
                        ->sum('total'),
                ],
            ];

            return response()->json(ApiResponse::success($statistics, '获取用户统计成功'));

        } catch (\Exception $e) {
            Log::error('获取用户统计失败', [
                'user_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取用户统计失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 根据手机号查找用户
     *
     * @param string $phone
     * @return \Illuminate\Http\JsonResponse
     */
    public function findByPhone($phone)
    {
        try {
            // 修改认证方式，使用sanctum
            $currentEmployee = auth('sanctum')->user();

            if (!$currentEmployee) {
                return response()->json(ApiResponse::error('未授权访问，请先登录', 401), 401);
            }

            // 检查权限
            if (!in_array($currentEmployee->role, ['admin', 'manager', 'staff', 'crm_agent'])) {
                return response()->json(ApiResponse::error('没有权限查找用户', 403), 403);
            }

            $user = User::with(['membershipLevel', 'crmAgent'])->where('phone', $phone)->first();
            
            if (!$user) {
                return response()->json(ApiResponse::error('未找到该手机号对应的用户', 404), 404);
            }

            return response()->json(ApiResponse::success($user, '查找用户成功'));

        } catch (\Exception $e) {
            Log::error('根据手机号查找用户失败', [
                'phone' => $phone,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('查找用户失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 更新用户状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            // 修改认证方式，使用sanctum
            $currentEmployee = auth('sanctum')->user();

            if (!$currentEmployee) {
                return response()->json(ApiResponse::error('未授权访问，请先登录', 401), 401);
            }

            // 检查权限
            if (!in_array($currentEmployee->role, ['admin', 'manager'])) {
                return response()->json(ApiResponse::error('没有权限更新用户状态', 403), 403);
            }

            $validator = Validator::make($request->all(), [
                'status' => 'required|in:active,inactive,banned'
            ]);

            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }

            $user = User::find($id);
            if (!$user) {
                return response()->json(ApiResponse::error('用户不存在', 404), 404);
            }

            $user->status = $request->status;
            $user->save();

            return response()->json(ApiResponse::success($user, '用户状态更新成功'));

        } catch (\Exception $e) {
            Log::error('更新用户状态失败', [
                'user_id' => $id,
                'status' => $request->status ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('更新用户状态失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 更新用户会员信息
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateMembership(Request $request, $id)
    {
        try {
            // 修改认证方式，使用sanctum
            $currentEmployee = auth('sanctum')->user();

            if (!$currentEmployee) {
                return response()->json(ApiResponse::error('未授权访问，请先登录', 401), 401);
            }

            // 检查权限
            if (!in_array($currentEmployee->role, ['admin', 'manager', 'crm_agent'])) {
                return response()->json(ApiResponse::error('没有权限更新用户会员信息', 403), 403);
            }

            $validator = Validator::make($request->all(), [
                'membership_level_id' => 'required|exists:membership_levels,id'
            ]);

            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }

            $user = User::find($id);
            if (!$user) {
                return response()->json(ApiResponse::error('用户不存在', 404), 404);
            }

            // CRM专员只能更新自己的客户
            if ($currentEmployee->role === 'crm_agent' && $user->crm_agent_id !== $currentEmployee->id) {
                return response()->json(ApiResponse::error('没有权限更新该用户会员信息', 403), 403);
            }

            $user->membership_level_id = $request->membership_level_id;
            $user->level_upgraded_at = now();
            $user->save();

            $user->load('membershipLevel');

            return response()->json(ApiResponse::success($user, '用户会员信息更新成功'));

        } catch (\Exception $e) {
            Log::error('更新用户会员信息失败', [
                'user_id' => $id,
                'membership_level_id' => $request->membership_level_id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('更新用户会员信息失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 获取用户订单列表
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserOrders(Request $request, $id)
    {
        try {
            // 修改认证方式，使用sanctum
            $currentEmployee = auth('sanctum')->user();

            if (!$currentEmployee) {
                return response()->json(ApiResponse::error('未授权访问，请先登录', 401), 401);
            }

            // 检查权限
            if (!in_array($currentEmployee->role, ['admin', 'manager', 'staff', 'crm_agent'])) {
                return response()->json(ApiResponse::error('没有权限查看用户订单', 403), 403);
            }

            $user = User::find($id);
            if (!$user) {
                return response()->json(ApiResponse::error('用户不存在', 404), 404);
            }

            // CRM专员只能查看自己的客户订单
            if ($currentEmployee->role === 'crm_agent' && $user->crm_agent_id !== $currentEmployee->id) {
                return response()->json(ApiResponse::error('没有权限查看该用户订单', 403), 403);
            }

            $orders = $user->orders()->with(['items.product', 'delivery']);

            // 状态筛选
            if ($request->has('status') && $request->status) {
                $orders->where('status', $request->status);
            }

            // 时间筛选
            if ($request->has('start_date') && $request->start_date) {
                $orders->where('created_at', '>=', $request->start_date);
            }

            if ($request->has('end_date') && $request->end_date) {
                $orders->where('created_at', '<=', $request->end_date);
            }

            // 分页
            $perPage = $request->input('per_page', 20);
            $orders = $orders->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'data' => $orders->items(),
                'total' => $orders->total(),
                'current_page' => $orders->currentPage(),
                'per_page' => $orders->perPage(),
            ]);

        } catch (\Exception $e) {
            Log::error('获取用户订单失败', [
                'user_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('获取用户订单失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 分配CRM专员
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function assignAgent(Request $request, $id)
    {
        try {
            // 修改认证方式，使用sanctum
            $currentEmployee = auth('sanctum')->user();

            if (!$currentEmployee) {
                return response()->json(ApiResponse::error('未授权访问，请先登录', 401), 401);
            }

            // 检查权限
            if (!in_array($currentEmployee->role, ['admin', 'manager'])) {
                return response()->json(ApiResponse::error('没有权限分配CRM专员', 403), 403);
            }

            $validator = Validator::make($request->all(), [
                'crm_agent_id' => 'required|exists:employees,id'
            ]);

            if ($validator->fails()) {
                return response()->json(ApiResponse::error($validator->errors()->first(), 422), 422);
            }

            $user = User::find($id);
            if (!$user) {
                return response()->json(ApiResponse::error('用户不存在', 404), 404);
            }

            // 验证CRM专员是否存在且角色正确
            $crmAgent = Employee::where('id', $request->crm_agent_id)
                ->where('role', 'crm_agent')
                ->where('status', 'active')
                ->first();

            if (!$crmAgent) {
                return response()->json(ApiResponse::error('指定的CRM专员不存在或状态异常', 404), 404);
            }

            $user->crm_agent_id = $request->crm_agent_id;
            $user->save();

            $user->load('crmAgent');

            return response()->json(ApiResponse::success($user, 'CRM专员分配成功'));

        } catch (\Exception $e) {
            Log::error('分配CRM专员失败', [
                'user_id' => $id,
                'crm_agent_id' => $request->crm_agent_id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(ApiResponse::error('分配CRM专员失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 搜索用户
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        try {
            // 添加调试日志
            Log::info('CRM搜索请求', [
                'all_params' => $request->all(),
                'query_params' => $request->query(),
                'has_keyword' => $request->has('keyword'),
                'keyword_value' => $request->input('keyword'),
                'method' => $request->method(),
                'url' => $request->fullUrl()
            ]);

            // 修改认证方式，使用sanctum
            $currentEmployee = auth('sanctum')->user();

            if (!$currentEmployee) {
                return response()->json(ApiResponse::error('未授权访问，请先登录', 401), 401);
            }

            // 检查是否有users或users_view权限
            if (!in_array($currentEmployee->role, ['admin', 'manager', 'staff', 'crm_agent', 'delivery', 'warehouse_manager'])) {
                return response()->json(ApiResponse::error('没有权限搜索用户', 403), 403);
            }

            // 验证请求参数
            $request->validate([
                'keyword' => 'required|string|min:1',
                'per_page' => 'nullable|integer|min:1|max:100',
            ]);

            $keyword = $request->input('keyword');
            $perPage = $request->input('per_page', 15);

            // 使用传统查询而不是Scout搜索，因为需要复杂的关联过滤
            $users = User::with(['addresses', 'membershipLevel', 'crmAgent'])
                        // 只查询客户用户，排除有员工记录的用户
                        ->whereDoesntHave('employee')
                        // 关键词搜索
                        ->where(function($query) use ($keyword) {
                            $query->where('name', 'like', "%{$keyword}%")
                                ->orWhere('nickname', 'like', "%{$keyword}%")
                                ->orWhere('phone', 'like', "%{$keyword}%")
                                ->orWhere('merchant_name', 'like', "%{$keyword}%")
                                ->orWhere('province', 'like', "%{$keyword}%")
                                ->orWhere('city', 'like', "%{$keyword}%");
                        });

            // CRM专员只能搜索自己的客户
            if ($currentEmployee->role === 'crm_agent') {
                $users->where('crm_agent_id', $currentEmployee->id);
            }

            // 配送员只能搜索分配给自己的用户
            if ($currentEmployee->role === 'delivery') {
                $users->whereHas('orders', function ($q) use ($currentEmployee) {
                    $q->whereHas('delivery', function ($dq) use ($currentEmployee) {
                        $dq->whereHas('deliverer', function ($delivererQuery) use ($currentEmployee) {
                            $delivererQuery->where('employee_id', $currentEmployee->id);
                        });
                    });
                });
            }

            $users = $users->orderBy('created_at', 'desc')->paginate($perPage);
            
            // 格式化返回数据
            $formattedUsers = $users->getCollection()->map(function($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name ?? '',
                    'phone' => $user->phone ?? '',
                    'nickname' => $user->nickname ?? '',
                    'merchant_name' => $user->merchant_name ?? '',
                    'addresses' => $user->addresses ? $user->addresses : [],
                    'city' => $user->city ?? '',
                    'province' => $user->province ?? '',
                    'role' => 'customer', // 固定返回customer，因为已经过滤了员工用户
                    'membership_level' => $user->membershipLevel,
                    'crm_agent' => $user->crmAgent,
                ];
            });

            return response()->json([
                'data' => $formattedUsers,
                'total' => $users->total(),
                'current_page' => $users->currentPage(),
                'per_page' => $users->perPage(),
                'last_page' => $users->lastPage(),
            ]);
        } catch (\Exception $e) {
            Log::error('用户搜索失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            
            return response()->json([
                'message' => '搜索用户失败: ' . $e->getMessage(),
                'status' => 'error'
            ], 500);
        }
    }

    /**
     * 导出用户数据
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function exportUsers(Request $request)
    {
        try {
            // 修改认证方式，使用sanctum
            $currentEmployee = auth('sanctum')->user();

            if (!$currentEmployee) {
                return response()->json(ApiResponse::error('未授权访问，请先登录', 401), 401);
            }

            // 检查权限
            if (!in_array($currentEmployee->role, ['admin', 'manager'])) {
                return response()->json(ApiResponse::error('没有权限导出用户数据', 403), 403);
            }

            $users = User::with(['membershipLevel', 'crmAgent'])
                        ->whereDoesntHave('employee'); // 只导出客户用户

            // 应用筛选条件
            if ($request->has('status') && $request->status) {
                $users->where('status', $request->status);
            }

            if ($request->has('keyword') && $request->keyword) {
                $keyword = $request->keyword;
                $users->where(function($query) use ($keyword) {
                    $query->where('name', 'like', "%{$keyword}%")
                        ->orWhere('phone', 'like', "%{$keyword}%")
                        ->orWhere('nickname', 'like', "%{$keyword}%")
                        ->orWhere('merchant_name', 'like', "%{$keyword}%");
                });
            }

            $users = $users->orderBy('created_at', 'desc')->get();

            // 生成CSV内容
            $csvData = [];
            $csvData[] = ['ID', '姓名', '手机号', '昵称', '商户名称', '会员等级', 'CRM专员', '余额', '积分', '注册时间', '状态'];

            foreach ($users as $user) {
                $csvData[] = [
                    $user->id,
                    $user->name ?? '',
                    $user->phone ?? '',
                    $user->nickname ?? '',
                    $user->merchant_name ?? '',
                    $user->membershipLevel ? $user->membershipLevel->name : '普通会员',
                    $user->crmAgent ? $user->crmAgent->name : '未分配',
                    $user->balance ?? 0,
                    $user->member_points ?? 0,
                    $user->created_at ? $user->created_at->format('Y-m-d H:i:s') : '',
                    $user->status == 1 ? '正常' : '禁用'
                ];
            }

            // 生成CSV文件内容
            $output = fopen('php://temp', 'r+');
            foreach ($csvData as $row) {
                fputcsv($output, $row);
            }
            rewind($output);
            $csvContent = stream_get_contents($output);
            fclose($output);

            // 添加BOM以支持中文
            $csvContent = "\xEF\xBB\xBF" . $csvContent;

            $filename = '用户数据导出_' . date('Y-m-d_H-i-s') . '.csv';

            return response($csvContent)
                ->header('Content-Type', 'text/csv; charset=utf-8')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            Log::error('导出用户数据失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            
            return response()->json(ApiResponse::error('导出用户数据失败: ' . $e->getMessage(), 500), 500);
        }
    }

    /**
     * 临时调试方法 - 排查用户列表问题
     */
    public function debugIndex(Request $request)
    {
        try {
            $currentEmployee = auth('sanctum')->user();
            
            $debug = [
                'step' => 1,
                'auth_user' => $currentEmployee ? [
                    'id' => $currentEmployee->id,
                    'role' => $currentEmployee->role,
                    'name' => $currentEmployee->name
                ] : null
            ];

            if (!$currentEmployee) {
                return response()->json(['debug' => $debug, 'error' => '未授权访问']);
            }

            $debug['step'] = 2;
            $debug['has_permission'] = in_array($currentEmployee->role, ['admin', 'manager', 'staff', 'crm_agent', 'delivery', 'warehouse_manager']);

            // 测试基础用户查询
            $debug['step'] = 3;
            $totalUsers = User::count();
            $debug['total_users'] = $totalUsers;

            // 测试排除员工的查询
            $debug['step'] = 4;
            $customerUsers = User::whereDoesntHave('employee')->count();
            $debug['customer_users'] = $customerUsers;

            // 测试会员等级关联
            $debug['step'] = 5;
            try {
                $usersWithMembership = User::with('membershipLevel')->whereDoesntHave('employee')->limit(5)->get();
                $debug['membership_test'] = 'success';
                $debug['sample_users'] = $usersWithMembership->map(function($user) {
                    return [
                        'id' => $user->id,
                        'name' => $user->name,
                        'membership_level_id' => $user->membership_level_id,
                        'membership_level_name' => $user->membershipLevel ? $user->membershipLevel->name : null
                    ];
                });
            } catch (\Exception $e) {
                $debug['membership_test'] = 'failed';
                $debug['membership_error'] = $e->getMessage();
            }

            // 测试其他关联
            $debug['step'] = 6;
            try {
                $usersWithAllRelations = User::with(['employee', 'defaultEmployeeDeliverer', 'crmAgent', 'membershipLevel'])
                    ->whereDoesntHave('employee')
                    ->limit(3)
                    ->get();
                $debug['all_relations_test'] = 'success';
            } catch (\Exception $e) {
                $debug['all_relations_test'] = 'failed';
                $debug['all_relations_error'] = $e->getMessage();
            }

            return response()->json(['debug' => $debug]);

        } catch (\Exception $e) {
            return response()->json([
                'debug' => $debug ?? ['step' => 'unknown'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
} 