<?php

namespace App\Upload\Services;

use App\Models\UploadConfig;
use App\Upload\Contracts\StorageDriverInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
// 注意：此功能需要安装Intervention/Image包
// 请运行: composer require intervention/image
// use Intervention\Image\ImageManagerStatic as Image;

class UploadService
{
    /**
     * 存储驱动
     *
     * @var StorageDriverInterface
     */
    protected $driver;

    /**
     * 目录结构
     *
     * @var array
     */
    protected $directories;

    /**
     * 创建服务实例
     *
     * @param StorageDriverInterface $driver 存储驱动
     */
    public function __construct(StorageDriverInterface $driver)
    {
        $this->driver = $driver;
        // 从配置文件或数据库读取目录结构
        $this->directories = config('upload.directories');
    }

    /**
     * 创建腾讯云COS上传实例
     *
     * @return self
     */
    public static function cos(): self
    {
        // 从数据库获取配置
        $config = UploadConfig::getGroupValues('upload_cos');
        
        // 确保存储桶名称不会被重复添加APP ID
        if (empty($config['cos_bucket']) || !isset($config['cos_app_id'])) {
            throw new \Exception('COS配置不完整，缺少存储桶或APP ID配置');
        }
        
        // 记录即将使用的配置
        Log::info('即将使用的COS配置', [
            'bucket' => $config['cos_bucket'],
            'app_id' => $config['cos_app_id'],
            'region' => $config['cos_region'] ?? 'ap-chengdu'
        ]);

        return new self(new CosStorageDriver($config), 'cos');
    }

    /**
     * 创建本地上传实例
     *
     * @return self
     */
    public static function local()
    {
        $driver = new LocalStorageDriver();
        return new static($driver);
    }
    
    /**
     * 根据配置创建上传实例
     *
     * @return self
     */
    public static function createFromConfig()
    {
        try {
            // 优先从数据库获取驱动类型
            $driver = UploadConfig::getValue('upload_driver', 'local');
            
            // 如果数据库没有，从配置文件获取
            if (!$driver) {
                $driver = config('upload.default_driver', 'local');
            }
            
            // 根据类型返回适当的驱动实例
            return match($driver) {
                'cos' => self::cos(),
                default => self::local(),
            };
        } catch (\Exception $e) {
            // 异常时使用本地存储
            Log::error('上传配置异常，使用本地存储', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return self::local();
        }
    }

    /**
     * 设置存储驱动
     *
     * @param StorageDriverInterface $driver
     * @return $this
     */
    public function setDriver(StorageDriverInterface $driver)
    {
        $this->driver = $driver;
        return $this;
    }
    
    /**
     * 获取当前存储驱动
     *
     * @return StorageDriverInterface
     */
    public function getDriver(): StorageDriverInterface
    {
        return $this->driver;
    }

    /**
     * 上传图片文件
     *
     * @param UploadedFile $file 上传的文件
     * @param string $type 文件类型（banner, product, category等）
     * @param array $options 附加选项
     * @return array|null 上传结果
     */
    public function uploadImage(UploadedFile $file, string $type, array $options = [])
    {
        try {
            // 获取目录
            $directory = $this->getDirectory($type, $options);
            
            // 生成文件名（带随机串防止重名）
            $extension = $file->getClientOriginalExtension();
            $filename = $this->generateFilename($extension, $options);
            
            // 使用驱动存储文件
            $result = $this->driver->store($file, $directory, $filename, $options);
            
            if (!$result) {
                Log::error('文件上传失败', [
                    'original_name' => $file->getClientOriginalName(),
                    'type' => $type,
                    'driver' => $this->driver->getDriverName()
                ]);
                return null;
            }
            
            // 记录日志
            Log::info("{$type}上传成功", [
                'original_name' => $file->getClientOriginalName(),
                'path' => $result['path'] ?? '',
                'url' => $result['url'] ?? '',
                'driver' => $this->driver->getDriverName(),
                'options' => $options
            ]);
            
            // 添加版本信息
            $result['versions'] = [
                'default' => [
                    'path' => $result['path'] ?? '',
                    'url' => $result['url'] ?? ''
                ]
            ];
            
            return $result;
        } catch (\Exception $e) {
            Log::error("{$type}上传失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'original_name' => $file->getClientOriginalName(),
                'type' => $type,
                'driver' => $this->driver->getDriverName()
            ]);
            
            return null;
        }
    }

    /**
     * 获取存储目录
     *
     * @param string $type 文件类型
     * @param array $options 附加选项
     * @return string
     */
    protected function getDirectory(string $type, array $options = [])
    {
        if (!isset($this->directories[$type])) {
            throw new \InvalidArgumentException("未知的文件类型: {$type}");
        }
        
        $directory = $this->directories[$type];
        
        // 替换{date}占位符
        $date = date('Ymd');
        $directory = str_replace('{date}', $date, $directory);
        
        // 替换{type}占位符
        if (isset($options['subtype'])) {
            $directory = str_replace('{type}', $options['subtype'], $directory);
        }
        
        // 应用自定义前缀
        if (isset($options['prefix']) && !empty($options['prefix'])) {
            $directory = rtrim($options['prefix'], '/') . '/' . $directory;
        }
        
        return $directory;
    }

    /**
     * 生成文件名
     *
     * @param string $extension 文件扩展名
     * @param array $options 附加选项
     * @return string
     */
    protected function generateFilename(string $extension, array $options = [])
    {
        // 如果提供了自定义文件名
        if (isset($options['filename'])) {
            return $options['filename'] . '.' . $extension;
        }
        
        // 使用UUID生成唯一文件名
        $uuid = (string) Str::uuid();
        
        // 添加可选前缀
        $prefix = isset($options['name_prefix']) ? $options['name_prefix'] . '_' : '';
        
        return $prefix . $uuid . '.' . $extension;
    }
    
    /**
     * 上传分类图片
     *
     * @param UploadedFile $file 上传的文件
     * @param array $options 附加选项
     * @return array|null
     */
    public function uploadCategoryImage(UploadedFile $file, array $options = [])
    {
        try {
            $type = isset($options['is_icon']) && $options['is_icon'] ? 'category_icon' : 'category';
            
            // 添加额外的日志信息
            Log::info('正在处理分类图片上传', [
                'file_name' => $file->getClientOriginalName(),
                'file_size' => $file->getSize(),
                'file_mime' => $file->getMimeType(),
                'type' => $type,
                'options' => $options,
                'disk' => $this->driver->getDriverName()
            ]);

            // 获取目录路径
            $directory = $this->getDirectory($type, $options);
            Log::info('分类图片存储目录', ['directory' => $directory]);
            
            // 确保存储目录存在
            if ($this->driver->getDriverName() === 'local') {
                $storagePath = storage_path('app/public/' . $directory);
                Log::info('检查本地存储目录是否存在', ['storagePath' => $storagePath]);
                
                if (!file_exists($storagePath)) {
                    Log::info('尝试创建目录', ['directory' => $storagePath]);
                    if (!mkdir($storagePath, 0755, true)) {
                        Log::error('无法创建存储目录', ['path' => $storagePath]);
                        throw new \Exception('无法创建存储目录: ' . $storagePath);
                    }
                    Log::info('成功创建目录', ['directory' => $storagePath]);
                }
            }
            
            // 上传图片
            $result = $this->uploadImage($file, $type, $options);
            if (!$result) {
                Log::error('上传图片失败', [
                    'file_name' => $file->getClientOriginalName(),
                    'type' => $type
                ]);
                throw new \Exception('上传图片处理失败');
            }
            
            Log::info('分类图片上传完成', [
                'result' => $result,
                'file_name' => $file->getClientOriginalName()
            ]);
            
            return $result;
        } catch (\Exception $e) {
            Log::error('分类图片上传异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file_name' => $file->getClientOriginalName()
            ]);
            return null;
        }
    }
    
    /**
     * 上传商品图片
     *
     * @param UploadedFile $file 上传的文件
     * @param array $options 附加选项
     * @return array|null
     */
    public function uploadProductImage(UploadedFile $file, array $options = [])
    {
        return $this->uploadImage($file, 'product', $options);
    }
    
    /**
     * 上传轮播图
     *
     * @param UploadedFile $file 上传的文件
     * @param array $options 附加选项
     * @return array|null
     */
    public function uploadBannerImage(UploadedFile $file, array $options = [])
    {
        return $this->uploadImage($file, 'banner', $options);
    }
    
    /**
     * 上传用户头像
     *
     * @param UploadedFile $file 上传的文件
     * @param array $options 附加选项
     * @return array|null
     */
    public function uploadAvatarImage(UploadedFile $file, array $options = [])
    {
        return $this->uploadImage($file, 'avatar', $options);
    }

    /**
     * 生成缩略图
     * 
     * @param \Illuminate\Http\UploadedFile $file 上传的文件
     * @param array $dimensions 需要生成的尺寸数组，例如 ['80x80', '150x150']
     * @return array 缩略图路径数组
     */
    protected function generateThumbnails($file, array $dimensions = ['80x80'])
    {
        try {
            // 注意：此功能需要安装 Intervention/Image 包才能正常工作
            // 请运行: composer require intervention/image
            Log::warning('缩略图功能需要安装Intervention/Image包', [
                'suggestion' => '请运行: composer require intervention/image'
            ]);
            
            // 暂时返回空数组，表示没有生成缩略图
            return [];
            
            /* 原始实现（需要Intervention/Image包）
            $thumbnails = [];
            $originalImage = Image::make($file->getRealPath());
            $extension = $file->getClientOriginalExtension();
            $filename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $tempDir = storage_path('app/temp');
            
            // 确保临时目录存在
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }
            
            foreach ($dimensions as $dimension) {
                list($width, $height) = explode('x', $dimension);
                
                // 创建一个新的缩略图实例
                $thumbnail = clone $originalImage;
                $thumbnail->fit((int)$width, (int)$height);
                
                // 保存缩略图到临时目录
                $thumbnailFilename = $filename . '_' . $dimension . '.' . $extension;
                $thumbnailPath = $tempDir . '/' . $thumbnailFilename;
                $thumbnail->save($thumbnailPath);
                
                // 添加到结果数组
                $thumbnails[$dimension] = [
                    'path' => $thumbnailPath,
                    'filename' => $thumbnailFilename
                ];
            }
            
            return $thumbnails;
            */
        } catch (\Exception $e) {
            Log::error('生成缩略图失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [];
        }
    }

    /**
     * 上传图片及其缩略图
     *
     * @param \Illuminate\Http\UploadedFile $file 要上传的文件
     * @param string $type 图片类型
     * @param array $options 附加选项
     * @return array|false 上传结果，包含主图和缩略图信息，失败返回false
     */
    public function uploadImageWithThumbnails($file, string $type, array $options = [])
    {
        // 生成缩略图
        $thumbnails = $this->generateThumbnails($file, $options['thumbnail_sizes'] ?? ['80x80']);
        
        // 上传原图
        $mainResult = $this->uploadImage($file, $type, $options);
        if (!$mainResult) {
            return false;
        }
        
        // 上传所有缩略图
        $thumbnailResults = [];
        if (!empty($thumbnails)) {
            foreach ($thumbnails as $dimension => $thumbnail) {
                $thumbnailFile = new \Illuminate\Http\UploadedFile(
                    $thumbnail['path'],
                    $thumbnail['filename'],
                    mime_content_type($thumbnail['path']),
                    null,
                    true
                );
                
                // 设置缩略图特定选项
                $thumbnailOptions = array_merge($options, [
                    'subtype' => 'thumb_' . $dimension,
                    'name_prefix' => ($options['name_prefix'] ?? '') . 'thumb_' . $dimension . '_'
                ]);
                
                $result = $this->uploadImage($thumbnailFile, $type, $thumbnailOptions);
                if ($result) {
                    $thumbnailResults[$dimension] = $result;
                }
                
                // 删除临时文件
                @unlink($thumbnail['path']);
            }
        } else {
            Log::info('没有生成缩略图，可能是因为未安装Intervention/Image包');
        }
        
        // 返回包含主图和缩略图的结果
        return [
            'main' => $mainResult,
            'thumbnails' => $thumbnailResults
        ];
    }

    /**
     * 上传商品图片及其缩略图
     *
     * @param \Illuminate\Http\UploadedFile $file 要上传的文件
     * @param array $options 附加选项
     * @return array|false 上传结果
     */
    public function uploadProductImageWithThumbnails($file, array $options = [])
    {
        return $this->uploadImageWithThumbnails($file, 'product', $options);
    }
}