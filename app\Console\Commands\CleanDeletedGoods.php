<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class CleanDeletedGoods extends Command
{
    protected $signature = 'clean:deleted-goods 
                           {--connection=mysql_old : 数据库连接名}
                           {--table=zjhj_bd_goods_warehouse : 商品表名}
                           {--dry-run : 仅预览，不执行删除}
                           {--backup : 删除前备份已删除的数据}';

    protected $description = '清理商品表中已删除的数据';

    public function handle()
    {
        $connection = $this->option('connection');
        $tableName = $this->option('table');
        $dryRun = $this->option('dry-run');
        $backup = $this->option('backup');

        try {
            $this->info("正在处理表: {$tableName}");
            
            // 检查表是否存在
            $this->checkTableExists($connection, $tableName);
            
            // 统计已删除的数据
            $deletedCount = $this->getDeletedCount($connection, $tableName);
            $totalCount = $this->getTotalCount($connection, $tableName);
            
            $this->info("表统计信息:");
            $this->info("  总记录数: {$totalCount}");
            $this->info("  已删除记录数: {$deletedCount}");
            $this->info("  有效记录数: " . ($totalCount - $deletedCount));
            
            if ($deletedCount == 0) {
                $this->info("✅ 没有需要清理的数据");
                return 0;
            }
            
            // 显示一些已删除的记录示例
            $this->showDeletedSamples($connection, $tableName);
            
            if ($dryRun) {
                $this->info("🔍 预览模式 - 将删除 {$deletedCount} 条记录");
                return 0;
            }
            
            if (!$this->confirm("确定要删除这 {$deletedCount} 条已删除的记录吗？")) {
                $this->info("操作已取消");
                return 0;
            }
            
            // 备份已删除的数据
            if ($backup) {
                $this->backupDeletedData($connection, $tableName);
            }
            
            // 执行清理
            $this->cleanDeletedData($connection, $tableName);
            
            // 优化表
            $this->optimizeTable($connection, $tableName);
            
            $this->info("✅ 清理完成！");
            
        } catch (Exception $e) {
            $this->error("❌ 清理失败: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 检查表是否存在
     */
    private function checkTableExists($connection, $tableName)
    {
        $exists = DB::connection($connection)
            ->select("SHOW TABLES LIKE '{$tableName}'");
            
        if (empty($exists)) {
            throw new Exception("表 {$tableName} 不存在");
        }
    }
    
    /**
     * 获取已删除记录数量
     */
    private function getDeletedCount($connection, $tableName)
    {
        return DB::connection($connection)
            ->table($tableName)
            ->where('is_delete', 1)
            ->count();
    }
    
    /**
     * 获取总记录数量
     */
    private function getTotalCount($connection, $tableName)
    {
        return DB::connection($connection)
            ->table($tableName)
            ->count();
    }
    
    /**
     * 显示已删除记录的示例
     */
    private function showDeletedSamples($connection, $tableName)
    {
        $samples = DB::connection($connection)
            ->table($tableName)
            ->where('is_delete', 1)
            ->select('id', 'name', 'deleted_at')
            ->limit(5)
            ->get();
            
        if ($samples->count() > 0) {
            $this->info("已删除记录示例:");
            foreach ($samples as $sample) {
                $this->line("  ID: {$sample->id}, 名称: {$sample->name}, 删除时间: {$sample->deleted_at}");
            }
        }
    }
    
    /**
     * 备份已删除的数据
     */
    private function backupDeletedData($connection, $tableName)
    {
        $this->info("正在备份已删除的数据...");
        
        $backupTableName = $tableName . '_deleted_backup_' . date('Y_m_d_H_i_s');
        
        // 创建备份表
        DB::connection($connection)->statement("
            CREATE TABLE `{$backupTableName}` LIKE `{$tableName}`
        ");
        
        // 复制已删除的数据到备份表
        DB::connection($connection)->statement("
            INSERT INTO `{$backupTableName}` 
            SELECT * FROM `{$tableName}` 
            WHERE is_delete = 1
        ");
        
        $this->info("✅ 已删除数据已备份到表: {$backupTableName}");
    }
    
    /**
     * 清理已删除的数据
     */
    private function cleanDeletedData($connection, $tableName)
    {
        $this->info("正在删除已删除的记录...");
        
        $deletedCount = DB::connection($connection)
            ->table($tableName)
            ->where('is_delete', 1)
            ->delete();
            
        $this->info("✅ 已删除 {$deletedCount} 条记录");
    }
    
    /**
     * 优化表
     */
    private function optimizeTable($connection, $tableName)
    {
        $this->info("正在优化表结构...");
        
        try {
            DB::connection($connection)->statement("OPTIMIZE TABLE `{$tableName}`");
            $this->info("✅ 表优化完成");
        } catch (Exception $e) {
            $this->warn("表优化失败: " . $e->getMessage());
        }
    }
} 