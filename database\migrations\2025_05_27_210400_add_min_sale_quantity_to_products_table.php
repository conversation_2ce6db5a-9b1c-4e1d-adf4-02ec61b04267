<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // 添加最小销售量字段（按销售单位计算）
            $table->decimal('min_sale_quantity', 10, 2)->default(1)->after('is_featured')
                ->comment('最小销售量（按销售单位计算）');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('min_sale_quantity');
        });
    }
}; 