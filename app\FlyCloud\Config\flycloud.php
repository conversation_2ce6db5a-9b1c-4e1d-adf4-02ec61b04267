<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 飞蛾云打印配置
    |--------------------------------------------------------------------------
    |
    | 飞蛾云云端小票打印服务配置
    |
    */

    // API配置
    'api_url' => env('FLYCLOUD_API_URL', 'http://api.feieyun.cn/Api/Open/'),
    'user' => env('FLYCLOUD_USER', ''), // 飞蛾云后台注册账号
    'ukey' => env('FLYCLOUD_UKEY', ''), // 飞蛾云后台生成的UKEY
    'timeout' => env('FLYCLOUD_TIMEOUT', 30), // API请求超时时间（秒）

    // 打印机配置
    'default_printer' => env('FLYCLOUD_DEFAULT_PRINTER', ''), // 默认打印机编号
    'max_copies' => env('FLYCLOUD_MAX_COPIES', 10), // 最大打印份数

    // 调试配置
    'debug' => env('FLYCLOUD_DEBUG', false), // 是否开启调试模式

    /*
    |--------------------------------------------------------------------------
    | 小票格式配置
    |--------------------------------------------------------------------------
    |
    | 配置小票打印的格式参数
    |
    */
    'receipt' => [
        'width' => 58, // 小票宽度(mm)，支持58mm和80mm
        'font_size' => 12, // 字体大小
        'line_height' => 1.2, // 行高
        'auto_cut' => true, // 是否自动切纸
        'store_name' => env('FLYCLOUD_STORE_NAME', '万家生鲜'), // 店铺名称
        'service_phone' => env('FLYCLOUD_SERVICE_PHONE', '************'), // 客服电话
    ],

    /*
    |--------------------------------------------------------------------------
    | 支持的格式控制标签
    |--------------------------------------------------------------------------
    |
    | 飞蛾云支持的小票格式控制标签说明
    |
    */
    'format_tags' => [
        '<BR>' => '换行符',
        '<CUT>' => '切刀指令(主动切纸)',
        '<LOGO>' => '打印LOGO指令',
        '<C>文本</C>' => '居中对齐',
        '<L>文本</L>' => '左对齐',
        '<R>文本</R>' => '右对齐',
        '<B>文本</B>' => '粗体',
        '<QR>二维码内容</QR>' => '二维码',
    ],

    /*
    |--------------------------------------------------------------------------
    | 错误码说明
    |--------------------------------------------------------------------------
    |
    | 飞蛾云API返回的错误码说明
    |
    */
    'error_codes' => [
        0 => '正确',
        1 => '参数错误',
        2 => '用户名或密码错误',
        3 => '缺少参数',
        4 => '验证失败',
        5 => '打印机不在线',
        6 => '打印机缺纸',
        7 => '打印机开盖',
        8 => '打印机其他异常',
        9 => '验证码错误',
        10 => '该账号未注册开发者',
        11 => '该账号未通过实名认证',
        12 => '缺少ukey参数',
        13 => 'IP白名单验证失败',
        14 => '接口调用次数超限',
        15 => '打印内容过长',
        16 => '打印机型号不支持',
        17 => '打印机已离线超过10分钟',
        18 => '系统维护中',
    ],
]; 