<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_transaction_types', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique()->comment('类型代码');
            $table->string('name')->comment('类型名称');
            $table->boolean('affects_inventory')->default(true)->comment('是否影响库存量');
            $table->tinyInteger('effect_direction')->default(1)->comment('影响方向：1增加，-1减少，0无影响');
            $table->text('description')->nullable()->comment('描述');
            $table->timestamps();
        });

        // 插入预定义事务类型
        DB::table('inventory_transaction_types')->insert([
            ['code' => 'purchase_in', 'name' => '采购入库', 'affects_inventory' => true, 'effect_direction' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['code' => 'sales_out', 'name' => '销售出库', 'affects_inventory' => true, 'effect_direction' => -1, 'created_at' => now(), 'updated_at' => now()],
            ['code' => 'return_in', 'name' => '销售退货入库', 'affects_inventory' => true, 'effect_direction' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['code' => 'return_out', 'name' => '采购退货出库', 'affects_inventory' => true, 'effect_direction' => -1, 'created_at' => now(), 'updated_at' => now()],
            ['code' => 'inventory_loss', 'name' => '库存损耗', 'affects_inventory' => true, 'effect_direction' => -1, 'created_at' => now(), 'updated_at' => now()],
            ['code' => 'inventory_adjustment', 'name' => '库存调整', 'affects_inventory' => true, 'effect_direction' => 0, 'created_at' => now(), 'updated_at' => now()],
            ['code' => 'transfer_out', 'name' => '调拨出库', 'affects_inventory' => true, 'effect_direction' => -1, 'created_at' => now(), 'updated_at' => now()],
            ['code' => 'transfer_in', 'name' => '调拨入库', 'affects_inventory' => true, 'effect_direction' => 1, 'created_at' => now(), 'updated_at' => now()],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_transaction_types');
    }
}; 