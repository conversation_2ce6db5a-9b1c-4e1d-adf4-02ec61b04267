<?php

namespace App\Delivery\Services;

use App\Delivery\Models\Delivery;
use App\Order\Models\Order;
use App\Models\User;
use App\Employee\Models\Employee;
use App\Delivery\Models\Deliverer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DeliveryService
{
    /**
     * 获取配送列表
     *
     * @param Request $request
     * @param Employee|null $employee
     * @return \Illuminate\Pagination\LengthAwarePaginator|\Illuminate\Database\Eloquent\Collection
     */
    public function getDeliveries(Request $request, ?Employee $employee = null)
    {
        try {
            $perPage = $request->input('per_page', 20);
            $status = $request->input('status', 'pending');
            
            // 基础查询
            $query = Delivery::query();
            
            if (!$employee) {
                // 未登录用户返回空结果
                return $this->getEmptyPaginatedResult($perPage);
            }
            
            // 使用新的过滤方法
            $query = $this->filterDeliveriesByEmployee($query, $employee);
            
            // 根据状态参数应用时间和状态筛选
            $query = $this->applyTimeBasedFilter($query, $status);
            
            return $query->with(['order.user', 'order.items', 'deliverer.employee'])
                ->latest()
                ->paginate($perPage);
        } catch (\Exception $e) {
            // 记录错误但返回空结果，避免前端崩溃
            Log::error('Delivery index error: ' . $e->getMessage());
            return $this->getEmptyPaginatedResult($request->input('per_page', 20));
        }
    }
    
    /**
     * 获取配送详情
     *
     * @param int $id
     * @return Delivery
     */
    public function getDelivery($id)
    {
        return Delivery::with(['order.user', 'order.items', 'deliverer.employee'])->findOrFail($id);
    }
    
    /**
     * 根据员工角色过滤配送记录
     */
    private function filterDeliveriesByEmployee($query, Employee $employee)
    {
        if (in_array($employee->role, ['admin', 'manager'])) {
            // 管理员和经理可以查看所有配送任务（包括未分配的）
            return $query;
        } elseif ($employee->role === 'delivery') {
            // 配送员只能查看分配给自己的配送任务
            return $query->whereHas('deliverer', function($q) use ($employee) {
                $q->where('employee_id', $employee->id);
            });
        } else {
            // 其他员工只能查看自己负责的配送任务（如果有的话）
            return $query->whereHas('deliverer', function($q) use ($employee) {
                $q->where('employee_id', $employee->id);
            });
        }
    }
    
    /**
     * 为订单分配配送员并创建配送记录
     */
    public function assignDelivery($orderId, $employeeId)
    {
        $order = Order::findOrFail($orderId);
        $employee = Employee::findOrFail($employeeId);
        
        // 检查员工是否有配送权限
        if ($employee->role !== 'delivery') {
            throw new \Exception('该员工不是配送员，无法分配配送任务');
        }
        
        // 查找或创建对应的deliverer记录
        $deliverer = Deliverer::where('employee_id', $employee->id)->first();
        if (!$deliverer) {
            $deliverer = Deliverer::create([
                'employee_id' => $employee->id,
                'user_id' => $employee->user_id,
                'type' => 'employee',
                'status' => 'available',
                'max_orders' => 10,
            ]);
        }
        
        // 创建配送记录
        $delivery = Delivery::create([
            'order_id' => $orderId,
            'deliverer_id' => $deliverer->id,
            'status' => 'pending',
        ]);
        
        return $delivery;
    }
    
    /**
     * 为现有配送记录分配配送员
     */
    public function assignDelivererToExistingDelivery($deliveryId, $employeeId)
    {
        $delivery = Delivery::findOrFail($deliveryId);
        $employee = Employee::findOrFail($employeeId);
        
        // 检查员工是否有配送权限
        if ($employee->role !== 'delivery') {
            throw new \Exception('该员工不是配送员，无法分配配送任务');
        }
        
        // 查找或创建对应的deliverer记录
        $deliverer = Deliverer::where('employee_id', $employee->id)->first();
        if (!$deliverer) {
            $deliverer = Deliverer::create([
                'employee_id' => $employee->id,
                'user_id' => $employee->user_id,
                'type' => 'employee',
                'status' => 'available',
                'max_orders' => 10,
            ]);
        }
        
        // 更新配送记录
        $delivery->deliverer_id = $deliverer->id;
        $delivery->save();
        
        return $delivery;
    }
    
    /**
     * 更新配送状态
     *
     * @param int $id
     * @param string $status
     * @return Delivery
     */
    public function updateDeliveryStatus($id, $status)
    {
        $delivery = Delivery::findOrFail($id);
        $delivery->status = $status;
        $delivery->save();
        
        // 如果配送完成，更新订单状态
        if ($status === 'completed') {
            try {
                $order = $delivery->order;
                if ($order) {
                    $order->status = 'delivered';
                    $order->delivered_at = now();
                    $order->save();
                }
            } catch (\Exception $e) {
                Log::error('Failed to update order status when completing delivery: ' . $e->getMessage());
                // 继续执行，不因为订单更新失败而中断操作
            }
        }
        
        return $delivery;
    }
    
    /**
     * 生成空的分页结果
     *
     * @param int $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    private function getEmptyPaginatedResult($perPage)
    {
        return new \Illuminate\Pagination\LengthAwarePaginator(
            [], // 空数据
            0,  // 总数
            $perPage, // 每页数量
            1   // 当前页
        );
    }
    
    /**
     * 根据配送时效逻辑应用时间筛选
     */
    private function applyTimeBasedFilter($query, $status)
    {
        $today = now()->startOfDay();
        $yesterday = now()->subDay()->startOfDay();
        $tomorrow = now()->addDay()->startOfDay();
        
        switch ($status) {
            case 'pending':
                // 待配送：显示当日订单（今天下单，明天配送）
                $query->whereHas('order', function($q) use ($today, $tomorrow) {
                    $q->whereBetween('created_at', [$today, $tomorrow]);
                })
                ->where('status', 'pending');
                break;
                
            case 'in_progress':
                // 配送中：显示昨天的订单（昨天下单，今天配送）
                $query->whereHas('order', function($q) use ($yesterday, $today) {
                    $q->whereBetween('created_at', [$yesterday, $today]);
                })
                ->where('status', 'in_progress');
                break;
                
            case 'completed':
                // 已完成：显示已完成的配送（不限制订单时间）
                $query->where('status', 'completed');
                break;
                
            default:
                // 默认显示待配送
                $query->whereHas('order', function($q) use ($today, $tomorrow) {
                    $q->whereBetween('created_at', [$today, $tomorrow]);
                })
                ->where('status', 'pending');
                break;
        }
        
        return $query;
    }
} 