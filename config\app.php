<?php return array (
  'name' => 'Laravel',
  'env' => 'production',
  'debug' => false,
  'url' => 'http://localhost',
  'asset_url' => NULL,
  'timezone' => 'Asia/Shanghai',
  'locale' => 'zh_CN',
  'fallback_locale' => 'en',
  'faker_locale' => 'en_US',
  'key' => env('APP_KEY'),
  'cipher' => 'AES-256-CBC',
  'maintenance' => 
  array (
    'driver' => 'file',
  ),
  'providers' => 
  array (
    0 => 'Illuminate\\Auth\\AuthServiceProvider',
    1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    2 => 'Illuminate\\Bus\\BusServiceProvider',
    3 => 'Illuminate\\Cache\\CacheServiceProvider',
    4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    5 => 'Illuminate\\Cookie\\CookieServiceProvider',
    6 => 'Illuminate\\Database\\DatabaseServiceProvider',
    7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
    8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
    9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
    10 => 'Illuminate\\Hashing\\HashServiceProvider',
    11 => 'Illuminate\\Mail\\MailServiceProvider',
    12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
    13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
    14 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    15 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    16 => 'Illuminate\\Queue\\QueueServiceProvider',
    17 => 'Illuminate\\Redis\\RedisServiceProvider',
    18 => 'Illuminate\\Session\\SessionServiceProvider',
    19 => 'Illuminate\\Translation\\TranslationServiceProvider',
    20 => 'Illuminate\\Validation\\ValidationServiceProvider',
    21 => 'Illuminate\\View\\ViewServiceProvider',
    22 => 'App\\Providers\\AppServiceProvider',
    23 => 'App\\Providers\\AuthServiceProvider',
    24 => 'App\\Providers\\EventServiceProvider',
    25 => 'App\\Providers\\RouteServiceProvider',
    26 => 'App\\Product\\Providers\\ProductServiceProvider',
    27 => 'App\\Order\\Providers\\OrderServiceProvider',
    28 => 'App\\Supplier\\Providers\\SupplierServiceProvider',
    29 => 'App\\Purchase\\Providers\\PurchaseServiceProvider',
    30 => 'App\\Inventory\\Providers\\InventoryServiceProvider',
    31 => 'App\\Unit\\Providers\\UnitServiceProvider',
    32 => 'App\\Delivery\\Providers\\DeliveryServiceProvider',
    33 => 'App\\Crm\\Providers\\CrmServiceProvider',
    34 => 'App\\Cart\\Providers\\CartServiceProvider',
    35 => 'App\\Warehouse\\Providers\\WarehouseServiceProvider',
    36 => 'App\\WechatMp\\Providers\\WechatMpServiceProvider',
    37 => 'App\\Admin\\Providers\\AdminServiceProvider',
    38 => 'App\\shop\\Providers\\ShopServiceProvider',
    39 => 'App\\Upload\\Providers\\UploadServiceProvider',
    40 => 'App\\WechatPayment\\Providers\\WechatPaymentServiceProvider',
    41 => 'App\\Employee\\EmployeeServiceProvider',
    42 => 'App\\Region\\Providers\\RegionServiceProvider',
    43 => 'App\\Printing\\Providers\\PrintingServiceProvider',
    44 => 'App\\Payment\\PaymentServiceProvider',
    45 => 'App\\Points\\Providers\\PointsServiceProvider',
    46 => 'App\\Billing\\Providers\\BillingServiceProvider',
    47 => 'App\\Search\\Providers\\SearchServiceProvider',
  ),
  'aliases' => 
  array (
    'App' => 'Illuminate\\Support\\Facades\\App',
    'Arr' => 'Illuminate\\Support\\Arr',
    'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
    'Auth' => 'Illuminate\\Support\\Facades\\Auth',
    'Blade' => 'Illuminate\\Support\\Facades\\Blade',
    'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
    'Bus' => 'Illuminate\\Support\\Facades\\Bus',
    'Cache' => 'Illuminate\\Support\\Facades\\Cache',
    'Config' => 'Illuminate\\Support\\Facades\\Config',
    'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
    'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
    'Date' => 'Illuminate\\Support\\Facades\\Date',
    'DB' => 'Illuminate\\Support\\Facades\\DB',
    'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
    'Event' => 'Illuminate\\Support\\Facades\\Event',
    'File' => 'Illuminate\\Support\\Facades\\File',
    'Gate' => 'Illuminate\\Support\\Facades\\Gate',
    'Hash' => 'Illuminate\\Support\\Facades\\Hash',
    'Http' => 'Illuminate\\Support\\Facades\\Http',
    'Js' => 'Illuminate\\Support\\Js',
    'Lang' => 'Illuminate\\Support\\Facades\\Lang',
    'Log' => 'Illuminate\\Support\\Facades\\Log',
    'Mail' => 'Illuminate\\Support\\Facades\\Mail',
    'Notification' => 'Illuminate\\Support\\Facades\\Notification',
    'Number' => 'Illuminate\\Support\\Number',
    'Password' => 'Illuminate\\Support\\Facades\\Password',
    'Process' => 'Illuminate\\Support\\Facades\\Process',
    'Queue' => 'Illuminate\\Support\\Facades\\Queue',
    'RateLimiter' => 'Illuminate\\Support\\Facades\\RateLimiter',
    'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
    'Request' => 'Illuminate\\Support\\Facades\\Request',
    'Response' => 'Illuminate\\Support\\Facades\\Response',
    'Route' => 'Illuminate\\Support\\Facades\\Route',
    'Schema' => 'Illuminate\\Support\\Facades\\Schema',
    'Session' => 'Illuminate\\Support\\Facades\\Session',
    'Storage' => 'Illuminate\\Support\\Facades\\Storage',
    'Str' => 'Illuminate\\Support\\Str',
    'Upload' => 'App\\Upload\\Facades\\Upload',
    'URL' => 'Illuminate\\Support\\Facades\\URL',
    'Validator' => 'Illuminate\\Support\\Facades\\Validator',
    'View' => 'Illuminate\\Support\\Facades\\View',
    'Vite' => 'Illuminate\\Support\\Facades\\Vite',
  ),
);