<?php

namespace App\shop\Services\Sms;

use AlibabaCloud\SDK\Dysmsapi\V20170525\Dysmsapi;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\SDK\Dysmsapi\V20170525\Models\SendSmsRequest;
use AlibabaCloud\SDK\Dysmsapi\V20170525\Models\ValidatePhoneCodeRequest;
use App\shop\Models\SmsVerification;
use App\shop\Services\ConfigService;
use Darabonba\OpenApi\Models\Config;
use Exception;
use Illuminate\Support\Facades\Log;

class AliyunSmsService
{
    /**
     * @var Dysmsapi
     */
    protected $client;

    /**
     * @var ConfigService
     */
    protected $configService;

    /**
     * 缓存的配置
     */
    protected $config = [];

    /**
     * 构造函数
     *
     * @param ConfigService $configService
     */
    public function __construct(ConfigService $configService)
    {
        $this->configService = $configService;
        // 暂时注释掉自动初始化，只在需要时初始化
        // $this->initClient();
    }

    /**
     * 初始化阿里云短信客户端
     *
     * @return void
     */
    protected function initClient(): void
    {
        // 如果客户端已初始化，则跳过
        if ($this->client) {
            return;
        }

        // 读取配置
        $accessKeyId = $this->getConfig('aliyun_access_key_id');
        $accessKeySecret = $this->getConfig('aliyun_access_key_secret');

        if (empty($accessKeyId) || empty($accessKeySecret)) {
            Log::warning('阿里云短信配置不完整，无法初始化客户端');
            return;
        }

        // 如果缺少依赖，则跳过初始化
        if (!class_exists('Darabonba\OpenApi\Models\Config')) {
            Log::warning('缺少阿里云短信SDK依赖，无法初始化客户端');
            return;
        }

        try {
            $config = new Config([
                'accessKeyId' => $accessKeyId,
                'accessKeySecret' => $accessKeySecret,
                'endpoint' => 'dysmsapi.aliyuncs.com'
            ]);

            $this->client = new Dysmsapi($config);
        } catch (Exception $e) {
            Log::error('阿里云短信客户端初始化失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取配置
     *
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    protected function getConfig(string $key, $default = null)
    {
        if (!isset($this->config[$key])) {
            $this->config[$key] = $this->configService->get($key, $default, false);
        }
        return $this->config[$key];
    }

    /**
     * 检查短信验证功能是否启用
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return (bool) $this->getConfig('sms_verification_enabled', false);
    }

    /**
     * 获取短信冷却时间
     *
     * @return int 秒数
     */
    public function getCooldownTime(): int
    {
        return (int) $this->getConfig('sms_cooldown', 60);
    }

    /**
     * 获取短信验证码有效期
     *
     * @return int 秒数
     */
    public function getExpireTime(): int
    {
        return (int) $this->getConfig('sms_expire', 300);
    }

    /**
     * 获取短信验证码长度
     *
     * @return int
     */
    public function getCodeLength(): int
    {
        return (int) $this->getConfig('sms_code_length', 6);
    }

    /**
     * 根据类型获取模板代码
     *
     * @param string $type
     * @return string|null
     */
    protected function getTemplateCode(string $type): ?string
    {
        $templates = [
            'login' => $this->getConfig('aliyun_template_login'),
            'register' => $this->getConfig('aliyun_template_register'),
            'reset_password' => $this->getConfig('aliyun_template_reset_password'),
            'bind' => $this->getConfig('aliyun_template_register'), // bind类型使用register模板
        ];

        return $templates[$type] ?? null;
    }

    /**
     * 生成随机验证码
     *
     * @return string
     */
    protected function generateCode(): string
    {
        $length = $this->getCodeLength();
        $min = pow(10, $length - 1);
        $max = pow(10, $length) - 1;
        return (string) mt_rand($min, $max);
    }

    /**
     * 检查用户是否可以发送短信（冷却时间检查）
     *
     * @param string $phone
     * @return bool 是否可以发送
     */
    public function canSendSms(string $phone): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        $latestSms = SmsVerification::where('phone', $phone)
            ->latest()
            ->first();

        if ($latestSms && $latestSms->created_at->diffInSeconds(now()) < $this->getCooldownTime()) {
            return false;
        }

        return true;
    }

    /**
     * 发送短信验证码
     *
     * @param string $phone 手机号
     * @param string $type 验证码类型(login, register, reset_password)
     * @param array $requestInfo 请求信息
     * @return array ['success' => bool, 'message' => string, 'code' => string|null]
     */
    public function sendVerificationCode(string $phone, string $type, array $requestInfo = []): array
    {
        // 初始化客户端
        $this->initClient();
        
        // 检查服务是否启用
        if (!$this->isEnabled()) {
            return ['success' => false, 'message' => '短信验证服务未启用'];
        }

        // 检查冷却时间
        if (!$this->canSendSms($phone)) {
            $cooldown = $this->getCooldownTime();
            return ['success' => false, 'message' => "发送过于频繁，请{$cooldown}秒后重试"];
        }

        // 获取模板代码
        $templateCode = $this->getTemplateCode($type);
        if (empty($templateCode)) {
            return ['success' => false, 'message' => '短信模板未配置'];
        }

        // 生成验证码
        $code = $this->generateCode();
        
        // 计算过期时间
        $expiresAt = now()->addSeconds($this->getExpireTime());

        // 记录验证码
        $verification = new SmsVerification([
            'phone' => $phone,
            'code' => $code,
            'type' => $type,
            'expires_at' => $expiresAt,
            'used' => false,
            'ip_address' => $requestInfo['ip'] ?? null,
            'user_agent' => $requestInfo['user_agent'] ?? null,
        ]);
        $verification->save();

        // 如果是本地开发环境，直接返回验证码
        if (app()->environment('local', 'testing')) {
            return [
                'success' => true,
                'message' => '验证码已生成（测试环境）',
                'code' => $code
            ];
        }

        // 发送短信
        $result = $this->sendSms($phone, $templateCode, ['code' => $code]);
        if (!$result['success']) {
            return $result;
        }

        return [
            'success' => true,
            'message' => '验证码已发送',
            'code' => app()->environment('local', 'testing') ? $code : null
        ];
    }

    /**
     * 发送短信
     *
     * @param string $phone 手机号
     * @param string $templateCode 模板代码
     * @param array $templateParam 模板参数
     * @return array ['success' => bool, 'message' => string]
     */
    protected function sendSms(string $phone, string $templateCode, array $templateParam = []): array
    {
        if (!$this->client) {
            return ['success' => false, 'message' => '短信客户端未初始化'];
        }

        try {
            $request = new SendSmsRequest([
                'phoneNumbers' => $phone,
                'signName' => $this->getConfig('aliyun_sign_name'),
                'templateCode' => $templateCode,
                'templateParam' => json_encode($templateParam)
            ]);

            $runtime = new RuntimeOptions([]);
            $response = $this->client->sendSmsWithOptions($request, $runtime);

            if ($response->body->code == 'OK') {
                return [
                    'success' => true,
                    'message' => '短信发送成功'
                ];
            }

            Log::warning('阿里云短信发送失败', [
                'phone' => $phone,
                'response' => json_decode(json_encode($response), true)
            ]);

            return [
                'success' => false,
                'message' => $response->body->message ?? '短信发送失败'
            ];
        } catch (Exception $error) {
            Log::error('阿里云短信发送异常', [
                'phone' => $phone,
                'error' => $error->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '短信发送异常: ' . $error->getMessage()
            ];
        }
    }

    /**
     * 验证短信验证码
     *
     * @param string $phone 手机号
     * @param string $code 验证码
     * @param string $type 验证码类型
     * @param bool $useOnce 是否标记为已使用
     * @return bool
     */
    public function verifyCode(string $phone, string $code, string $type, bool $useOnce = true): bool
    {
        if (!$this->isEnabled()) {
            // 如果服务未启用，在开发环境中让任何验证码通过
            return app()->environment('local', 'testing');
        }

        $verification = SmsVerification::findLatestValidCode($phone, $type);

        if (!$verification) {
            return false;
        }

        $isValid = $verification->code === $code && $verification->isValid();

        if ($isValid && $useOnce) {
            $verification->markAsUsed();
        }

        return $isValid;
    }

    /**
     * 使用阿里云ValidatePhoneCode接口验证验证码
     * 
     * @param string $phone 手机号
     * @param string $code 验证码
     * @param string $bizId 发送回执ID
     * @return bool
     */
    public function validatePhoneCodeWithAliyun(string $phone, string $code, string $bizId): bool
    {
        // 暂时不使用阿里云SDK自带的验证方法，直接返回false
        // 我们使用自己的数据库验证逻辑
        Log::warning('validatePhoneCodeWithAliyun方法未实现，使用本地验证逻辑');
        return false;
    }
} 