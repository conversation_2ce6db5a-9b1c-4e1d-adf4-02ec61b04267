<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('unit_conversion_graphs', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('转换图名称');
            $table->string('type', 50)->comment('单位类型');
            $table->boolean('is_default')->default(false)->comment('是否默认转换图');
            $table->text('description')->nullable()->comment('描述');
            $table->timestamps();
            
            $table->index('type');
        });
        
        // 为每种单位类型创建默认的转换图
        $types = DB::table('units')->select('type')->distinct()->pluck('type')->toArray();
        foreach ($types as $type) {
            DB::table('unit_conversion_graphs')->insert([
                'name' => $type . ' 默认转换',
                'type' => $type,
                'is_default' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unit_conversion_graphs');
    }
};
