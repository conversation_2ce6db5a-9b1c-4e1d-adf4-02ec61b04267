# TabBar 图标说明

## 🎯 快速解决方案

### 方法1：使用图标生成器（推荐）
1. 用浏览器打开 `图标生成器.html` 文件
2. 点击"下载图标"按钮，自动下载所需的PNG文件
3. 将下载的图标文件放到此目录下
4. 重新运行项目即可看到底部导航栏

### 方法2：使用Node.js脚本
```bash
# 在此目录下运行
npm install canvas
node 生成图标.js
```

## 📋 需要的图标文件

### 普通状态图标：
- `home.png` - 首页图标
- `order.png` - 代客下单图标  
- `client.png` - 客户图标
- `orders.png` - 订单图标
- `profile.png` - 个人中心图标

### 选中状态图标：
- `home-active.png` - 首页选中图标
- `order-active.png` - 代客下单选中图标
- `client-active.png` - 客户选中图标
- `orders-active.png` - 订单选中图标
- `profile-active.png` - 个人中心选中图标

## 🎨 图标要求
- **格式**：PNG
- **尺寸**：81x81px（推荐）
- **背景**：透明或纯色
- **颜色**：普通状态建议使用灰色(#999)，选中状态使用主题色(#007AFF)

## 🔧 自定义图标

### 在线图标资源：
1. **Iconfont** - https://www.iconfont.cn/
2. **Feather Icons** - https://feathericons.com/
3. **Material Icons** - https://material.io/icons/
4. **Font Awesome** - https://fontawesome.com/

### 设计工具：
1. **Figma** - 免费在线设计工具
2. **Sketch** - Mac专业设计工具
3. **Adobe Illustrator** - 专业矢量设计
4. **Canva** - 简单易用的在线工具

## 📱 使用步骤

### 1. 生成或下载图标
- 使用上述方法生成图标文件

### 2. 放置图标文件
```
static/tabbar/
├── home.png
├── home-active.png
├── order.png
├── order-active.png
├── client.png
├── client-active.png
├── orders.png
├── orders-active.png
├── profile.png
└── profile-active.png
```

### 3. 重新运行项目
- 在HBuilderX中重新运行项目
- 底部导航栏将显示图标

## ⚠️ 注意事项

### 文件命名
- 图标文件名必须与`pages.json`中配置的路径完全一致
- 区分大小写，注意文件扩展名

### 图标质量
- 建议使用矢量图标转换为PNG格式
- 确保图标在不同背景下都清晰可见
- 避免过于复杂的图标设计

### 性能优化
- 控制图标文件大小，建议每个文件小于10KB
- 使用PNG-8格式可以减小文件大小
- 考虑使用图标字体替代图片（高级用法）

## 🚀 高级技巧

### 使用图标字体
```css
/* 在CSS中使用图标字体 */
.icon-home::before {
    content: '\e001';
    font-family: 'iconfont';
}
```

### 动态图标
```javascript
// 在代码中动态切换图标
uni.setTabBarItem({
    index: 0,
    iconPath: 'static/tabbar/home-new.png'
})
```

## 🎉 完成后效果

生成图标后，您的应用将拥有：
- ✅ 美观的底部导航栏
- ✅ 清晰的功能图标
- ✅ 统一的视觉风格
- ✅ 良好的用户体验

现在就开始生成您的图标吧！🎨 