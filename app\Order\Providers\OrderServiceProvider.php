<?php

namespace App\Order\Providers;

use Illuminate\Support\ServiceProvider;

class OrderServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     */
    public function register()
    {
        // 注册服务绑定
    }

    /**
     * 引导服务
     */
    public function boot()
    {
        // 加载路由
        $this->loadRoutes();
    }

    /**
     * 加载模块路由
     */
    protected function loadRoutes()
    {
        // 如果routes目录不存在，需要先创建
        if (file_exists(app_path('Order/routes/web.php'))) {
            $this->loadRoutesFrom(app_path('Order/routes/web.php'));
        }
        
        if (file_exists(app_path('Order/routes/api.php'))) {
            $this->loadRoutesFrom(app_path('Order/routes/api.php'));
        }
    }
} 