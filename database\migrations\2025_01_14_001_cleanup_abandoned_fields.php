<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * 阶段1：清理明确废弃的字段
     * - temp_base_unit_id (完全为空，可安全删除)
     * - 迁移stock数据到inventories表，然后删除stock字段
     */
    public function up(): void
    {
        echo "=== 阶段1：清理废弃字段 ===\n";
        
        // 1. 检查库存数据一致性
        $this->checkStockDataConsistency();
        
        // 2. 删除temp_base_unit_id字段 (数据为空，安全删除)
        if (Schema::hasColumn('products', 'temp_base_unit_id')) {
            // 检查外键是否存在
            $this->dropForeignKeyIfExists('products', 'temp_base_unit_id');
            
            Schema::table('products', function (Blueprint $table) {
                $table->dropColumn('temp_base_unit_id');
            });
            echo "✅ 删除temp_base_unit_id字段\n";
        }
        
        // 3. 删除stock字段 (数据已迁移到inventories表)
        if (Schema::hasColumn('products', 'stock')) {
            Schema::table('products', function (Blueprint $table) {
                $table->dropColumn('stock');
            });
            echo "✅ 删除stock字段\n";
        }
        
        echo "=== 阶段1完成 ===\n";
    }
    
    /**
     * 检查库存数据一致性
     */
    private function checkStockDataConsistency(): void
    {
        echo "检查库存数据一致性...\n";
        
        // 统计products.stock vs inventory表的差异
        $stockAnalysis = DB::select("
            SELECT 
                COUNT(p.id) as total_products,
                COUNT(CASE WHEN p.stock > 0 THEN 1 END) as products_with_stock,
                COUNT(DISTINCT i.product_id) as products_in_inventory,
                COUNT(CASE WHEN p.stock > 0 AND i.product_id IS NULL THEN 1 END) as missing_inventory
            FROM products p 
            LEFT JOIN inventory i ON p.id = i.product_id
        ")[0];
        
        echo "库存数据分析:\n";
        echo "  - 总商品数: {$stockAnalysis->total_products}\n";
        echo "  - 有stock字段的商品: {$stockAnalysis->products_with_stock}\n";
        echo "  - 在inventory表中的商品: {$stockAnalysis->products_in_inventory}\n";
        echo "  - 缺少inventory记录的商品: {$stockAnalysis->missing_inventory}\n";
        
        if ($stockAnalysis->missing_inventory > 0) {
            echo "⚠️ 发现数据不一致，但由于inventory表已经是正式库存管理表，\n";
            echo "   我们将直接删除products.stock字段，保持inventory表数据不变\n";
        }
        
        echo "✅ 库存数据检查完成，准备删除冗余的products.stock字段\n";
    }
    
    /**
     * 安全删除外键约束（如果存在）
     */
    private function dropForeignKeyIfExists(string $table, string $column): void
    {
        // 查询外键约束名称
        $foreignKeys = DB::select("
            SELECT CONSTRAINT_NAME 
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = ? 
            AND COLUMN_NAME = ?
            AND CONSTRAINT_NAME != 'PRIMARY'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ", [$table, $column]);
        
        if (!empty($foreignKeys)) {
            foreach ($foreignKeys as $fk) {
                try {
                    DB::statement("ALTER TABLE `{$table}` DROP FOREIGN KEY `{$fk->CONSTRAINT_NAME}`");
                    echo "✅ 删除外键约束: {$fk->CONSTRAINT_NAME}\n";
                } catch (Exception $e) {
                    echo "⚠️ 删除外键失败: {$fk->CONSTRAINT_NAME} - {$e->getMessage()}\n";
                }
            }
        } else {
            echo "⚠️ 字段 {$column} 没有外键约束\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复字段（谨慎操作）
        Schema::table('products', function (Blueprint $table) {
            // 恢复temp_base_unit_id
            $table->bigInteger('temp_base_unit_id')->unsigned()->nullable()->after('base_unit_id');
            
            // 恢复stock字段
            $table->integer('stock')->default(0)->after('description');
        });
        
        // 从inventory表恢复stock数据
        $inventories = DB::table('inventory')
            ->select('product_id', DB::raw('SUM(stock) as total_stock'))
            ->groupBy('product_id')
            ->get();
            
        foreach ($inventories as $inventory) {
            DB::table('products')
                ->where('id', $inventory->product_id)
                ->update(['stock' => $inventory->total_stock]);
        }
    }
}; 