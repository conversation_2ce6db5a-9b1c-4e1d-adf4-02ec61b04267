<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('unit_conversion_edges')) {
            Schema::create('unit_conversion_edges', function (Blueprint $table) {
                $table->id();
                $table->foreignId('graph_id')->constrained('unit_conversion_graphs')->onDelete('cascade');
                $table->foreignId('from_unit_id')->constrained('units')->onDelete('cascade');
                $table->foreignId('to_unit_id')->constrained('units')->onDelete('cascade');
                $table->decimal('conversion_factor', 20, 10);
                $table->boolean('is_bidirectional')->default(true);
                $table->text('description')->nullable();
                $table->boolean('is_active')->default(true);
                $table->json('meta_data')->nullable();
                $table->timestamps();
                
                // 创建联合唯一索引确保每个图中的每对单位只有一条边
                $table->unique(['graph_id', 'from_unit_id', 'to_unit_id'], 'unique_edge_per_graph');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unit_conversion_edges');
    }
}; 