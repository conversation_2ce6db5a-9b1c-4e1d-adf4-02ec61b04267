<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * 修复users表中default_deliverer_id外键关系
     * 将外键引用从users表自身改为deliverers表
     */
    public function up(): void
    {
        // 获取所有与default_deliverer_id相关的外键约束
        $constraints = DB::select("
            SELECT CONSTRAINT_NAME 
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_NAME = 'users' 
            AND COLUMN_NAME = 'default_deliverer_id'
            AND CONSTRAINT_NAME != 'PRIMARY'
        ");
        
        // 删除所有找到的外键约束
        foreach ($constraints as $constraint) {
            $constraintName = $constraint->CONSTRAINT_NAME;
            try {
                DB::statement("ALTER TABLE `users` DROP FOREIGN KEY `{$constraintName}`");
            } catch (\Exception $e) {
                // 忽略错误，继续尝试其他约束
            }
        }
        
        // 使用原始SQL语句直接删除外键约束，避免点号导致的语法问题
        try {
            DB::statement('ALTER TABLE `users` DROP FOREIGN KEY `FK_users_tianxin_db.users`');
        } catch (\Exception $e) {
            // 如果删除失败，可能是外键名称不同或已经不存在
            // 可以继续执行后续代码
        }
        
        // 使用原始SQL添加新的外键约束，使用不同的约束名
        try {
            DB::statement('ALTER TABLE `users` ADD CONSTRAINT `users_default_deliverer_id_foreign_new` FOREIGN KEY (`default_deliverer_id`) REFERENCES `deliverers` (`id`) ON DELETE SET NULL');
        } catch (\Exception $e) {
            // 如果添加失败，记录错误
            \Illuminate\Support\Facades\Log::error('Failed to add foreign key: ' . $e->getMessage());
        }
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        // 删除我们添加的外键约束
        try {
            DB::statement('ALTER TABLE `users` DROP FOREIGN KEY `users_default_deliverer_id_foreign_new`');
        } catch (\Exception $e) {
            // 忽略错误
        }
    }
};
