<?php

namespace App\Search\Models;

use Illuminate\Database\Eloquent\Model;

class SearchLog extends Model
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'search_logs';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'keyword',
        'user_id',
        'session_id',
        'ip_address',
        'user_agent',
        'result_count',
        'platform',
        'source'
    ];

    /**
     * 模型的默认属性值
     *
     * @var array
     */
    protected $attributes = [
        'platform' => 'web',
        'source' => 'search'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
} 