<?php

namespace App\Unit\Repositories;

use App\Core\Repositories\RepositoryInterface;

interface UnitRepositoryInterface extends RepositoryInterface
{
    /**
     * 按类型获取单位
     *
     * @param string $type 单位类型
     * @param array $columns 要获取的字段
     * @return mixed
     */
    public function getByType(string $type, array $columns = ['*']);
    
    /**
     * 获取单位使用统计
     *
     * @param int $unitId 单位ID
     * @return array
     */
    public function getUsageStats(int $unitId): array;
    
    /**
     * 检查单位是否可以删除
     *
     * @param int $unitId 单位ID
     * @return bool
     */
    public function canDelete(int $unitId): bool;
    
    /**
     * 获取单位类型列表
     *
     * @return array
     */
    public function getTypes(): array;
    
    /**
     * 获取单位关联的产品
     *
     * @param int $unitId 单位ID
     * @return mixed
     */
    public function getRelatedProducts(int $unitId);
    
    /**
     * 搜索单位
     *
     * @param string $keyword 关键词
     * @param array $filters 过滤条件
     * @param int $perPage 每页记录数
     * @return mixed
     */
    public function search(string $keyword, array $filters = [], int $perPage = 15);
} 