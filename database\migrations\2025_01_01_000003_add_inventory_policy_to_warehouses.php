<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('warehouses', function (Blueprint $table) {
            // 仓库级别库存策略
            $table->enum('inventory_policy', ['inherit', 'strict', 'allow_negative', 'unlimited'])
                  ->default('inherit')
                  ->comment('库存策略：inherit-继承商品策略，strict-严格库存，allow_negative-允许负库存，unlimited-无限库存');
            
            // 仓库级别库存阈值
            $table->decimal('min_stock_threshold', 10, 2)->nullable()->comment('最小库存预警阈值');
            $table->decimal('max_negative_stock', 10, 2)->nullable()->comment('最大负库存限制');
            
            // 仓库级别自动补货设置
            $table->boolean('auto_reorder')->default(false)->comment('是否启用自动补货');
            $table->decimal('reorder_point', 10, 2)->nullable()->comment('补货点');
            $table->decimal('reorder_quantity', 10, 2)->nullable()->comment('补货数量');
            
            // 仓库库存管理设置
            $table->boolean('track_inventory')->default(true)->comment('是否追踪库存');
            $table->boolean('allow_oversell')->default(false)->comment('是否允许超卖');
            $table->json('inventory_alerts')->nullable()->comment('库存预警设置');
            
            // 仓库优先级（用于多仓库出库顺序）
            $table->integer('priority')->default(0)->comment('仓库优先级，数字越小优先级越高');
            
            // 索引
            $table->index(['inventory_policy', 'track_inventory']);
            $table->index('priority');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('warehouses', function (Blueprint $table) {
            $table->dropIndex(['inventory_policy', 'track_inventory']);
            $table->dropIndex(['priority']);
            
            $table->dropColumn([
                'inventory_policy',
                'min_stock_threshold',
                'max_negative_stock',
                'auto_reorder',
                'reorder_point',
                'reorder_quantity',
                'track_inventory',
                'allow_oversell',
                'inventory_alerts',
                'priority'
            ]);
        });
    }
}; 