# ERP系统成本价计算方案

## 概述

基于ERP系统的最佳实践，我们重新设计了成本价计算方案，采用多种标准的成本核算方法，确保成本计算的准确性和合理性。

## 支持的成本计算方法

### 1. 移动加权平均法（Moving Weighted Average）- 推荐

**原理：** 每次入库时重新计算平均成本价
**公式：** 新成本价 = (原库存金额 + 本次入库金额) / (原库存数量 + 本次入库数量)

**优点：**
- 成本计算相对平滑
- 避免价格波动对成本的剧烈影响
- 适用于大多数企业
- 计算相对简单

**适用场景：** 大部分企业的日常库存管理

### 2. 先进先出法（FIFO - First In First Out）

**原理：** 按照入库时间顺序，先入库的商品先出库
**特点：** 出库成本使用最早入库批次的成本价

**优点：**
- 符合商品流转的实际情况
- 在价格上涨时，成本相对较低
- 财务报表更保守

**适用场景：** 有保质期的商品、价格波动较大的商品

### 3. 标准成本法（Standard Cost）

**原理：** 使用预设的标准成本价，定期与实际成本比较
**特点：** 成本相对固定，便于预算和控制

**优点：**
- 便于成本控制和预算管理
- 简化日常核算工作
- 有利于绩效评估

**适用场景：** 生产制造企业、成本相对稳定的商品

## 实现架构

### 核心服务类

```php
class CostPriceService
{
    // 移动加权平均成本计算
    public function calculateMovingAverageCostPrice(int $inventoryId): ?float
    
    // FIFO成本计算
    public function calculateFIFOCostPrice(int $inventoryId): ?float
    
    // 标准成本获取
    public function getStandardCostPrice(int $inventoryId): ?float
    
    // 获取综合成本信息
    public function getCostPriceInfo(int $inventoryId): array
}
```

### 数据库设计

#### 成本价缓存表
```sql
CREATE TABLE inventory_cost_cache (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    inventory_id BIGINT NOT NULL,
    method ENUM('moving_average', 'fifo', 'standard') NOT NULL,
    cost_price DECIMAL(10,2) NOT NULL,
    calculation_time TIMESTAMP NOT NULL,
    last_transaction_id BIGINT,
    transaction_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_inventory_method (inventory_id, method),
    INDEX idx_calculation_time (calculation_time)
);
```

#### 成本计算批次表（用于FIFO）
```sql
CREATE TABLE inventory_cost_batches (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    inventory_id BIGINT NOT NULL,
    transaction_id BIGINT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    remaining_quantity DECIMAL(10,3) NOT NULL,
    unit_cost DECIMAL(10,2) NOT NULL,
    batch_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_inventory_date (inventory_id, batch_date),
    INDEX idx_remaining (inventory_id, remaining_quantity)
);
```

## API接口

### 获取成本价信息
```
GET /api/inventory/{inventoryId}/cost-price-info

Response:
{
    "success": true,
    "data": {
        "inventory_id": 123,
        "moving_average_cost": 25.50,
        "fifo_cost": 26.00,
        "standard_cost": 25.00,
        "recommended_method": "moving_average",
        "calculation_time": "2025-01-22 14:30:00",
        "last_in_time": "2025-01-20 10:15:00",
        "last_out_time": "2025-01-21 16:45:00",
        "transaction_count": 15,
        "cost_batches_count": 3
    }
}
```

### 刷新成本价缓存
```
POST /api/inventory/{inventoryId}/refresh-cost-price

Response:
{
    "success": true,
    "message": "成本价缓存刷新成功",
    "data": {
        "updated_methods": ["moving_average", "fifo"],
        "calculation_time": "2025-01-22 14:35:00"
    }
}
```

### 获取成本计算方法
```
GET /api/inventory/cost-methods

Response:
{
    "success": true,
    "data": [
        {
            "code": "moving_average",
            "name": "移动加权平均",
            "description": "每次入库时重新计算平均成本",
            "recommended": true
        },
        {
            "code": "fifo",
            "name": "先进先出",
            "description": "按入库时间顺序计算成本"
        },
        {
            "code": "standard",
            "name": "标准成本",
            "description": "使用预设的标准成本价"
        }
    ]
}
```

## 缓存策略

### 缓存键命名
- `inventory_cost_ma_{inventory_id}` - 移动加权平均成本
- `inventory_cost_fifo_{inventory_id}` - FIFO成本
- `inventory_cost_std_{inventory_id}` - 标准成本

### 缓存更新触发
1. 库存调整时
2. 采购入库时
3. 销售出库时
4. 库存转移时
5. 手动刷新时

### 缓存过期策略
- 默认缓存时间：1小时
- 有新库存事务时立即清除相关缓存
- 支持手动刷新缓存

## 前端集成

### 组件更新
- 库存管理页面显示多种成本价
- 支持切换成本计算方法
- 显示成本计算时间和相关统计

### 数据格式
```javascript
const stockItem = {
    // ... 其他字段
    cost_price: 25.50,  // 当前使用的成本价
    cost_price_methods: {
        moving_average: 25.50,
        fifo: 26.00,
        standard: 25.00
    },
    cost_calculation_method: 'moving_average',
    cost_calculation_time: '2025-01-22 14:30:00'
}
```

## 配置选项

### 系统配置
```php
// config/inventory.php
return [
    'cost_calculation' => [
        'default_method' => 'moving_average',
        'cache_ttl' => 3600, // 1小时
        'enable_fifo' => true,
        'enable_standard_cost' => true,
        'auto_refresh_threshold' => 100, // 事务数量阈值
    ]
];
```

## 迁移和升级

### 数据迁移步骤
1. 创建新的成本价相关数据表
2. 迁移现有的成本价数据
3. 重新计算所有库存的成本价
4. 更新前端代码
5. 测试验证

### 兼容性
- 保留旧的成本价接口作为兼容
- 新接口逐步替代旧接口
- 支持平滑升级

## 优势总结

1. **标准化**：采用ERP行业标准的成本计算方法
2. **灵活性**：支持多种成本计算方法，适应不同业务需求
3. **准确性**：实时计算，确保成本数据的准确性
4. **性能**：合理的缓存策略，保证系统性能
5. **可扩展**：易于添加新的成本计算方法
6. **可维护**：清晰的代码结构和文档

## 注意事项

1. **数据一致性**：确保库存事务和成本计算的一致性
2. **性能监控**：监控成本计算的性能，必要时优化
3. **定期校验**：定期校验成本计算结果的准确性
4. **备份策略**：重要的成本数据需要定期备份
5. **权限控制**：成本价信息的查看和修改需要适当的权限控制 