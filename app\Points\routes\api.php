<?php

use Illuminate\Support\Facades\Route;
use App\Points\Http\Controllers\PointsProductController;
use App\Points\Http\Controllers\PointsOrderController;
use App\Points\Http\Controllers\PointsController;
use App\Points\Http\Controllers\PointsRewardController;

/*
|--------------------------------------------------------------------------
| Points API Routes
|--------------------------------------------------------------------------
*/

// 积分商品相关路由
Route::prefix('points/products')->group(function () {
    Route::get('/', [PointsProductController::class, 'index'])->name('points.products.index');
    Route::get('/categories', [PointsProductController::class, 'categories'])->name('points.products.categories');
    Route::get('/popular', [PointsProductController::class, 'popular'])->name('points.products.popular');
    Route::get('/recommended', [PointsProductController::class, 'recommended'])->name('points.products.recommended');
    Route::get('/{id}', [PointsProductController::class, 'show'])->name('points.products.show');
    Route::get('/{id}/check-eligibility', [PointsProductController::class, 'checkExchangeEligibility'])
        ->middleware('auth:sanctum')
        ->name('points.products.check-eligibility');
});

// 积分订单相关路由（需要登录）
Route::middleware('auth:sanctum')->prefix('points/orders')->group(function () {
    Route::get('/', [PointsOrderController::class, 'index'])->name('points.orders.index');
    Route::post('/', [PointsOrderController::class, 'store'])->name('points.orders.store');
    Route::post('/preview', [PointsOrderController::class, 'preview'])->name('points.orders.preview');
    Route::get('/status-stats', [PointsOrderController::class, 'statusStats'])->name('points.orders.status-stats');
    Route::get('/{id}', [PointsOrderController::class, 'show'])->name('points.orders.show');
    Route::post('/{id}/pay', [PointsOrderController::class, 'pay'])->name('points.orders.pay');
    Route::post('/{id}/cancel', [PointsOrderController::class, 'cancel'])->name('points.orders.cancel');
    Route::post('/{id}/confirm-delivery', [PointsOrderController::class, 'confirmDelivery'])->name('points.orders.confirm-delivery');
});

// 积分管理相关路由（需要登录）
Route::middleware('auth:sanctum')->prefix('points')->group(function () {
    Route::get('/balance', [PointsController::class, 'balance'])->name('points.balance');
    Route::get('/transactions', [PointsController::class, 'transactions'])->name('points.transactions');
    Route::get('/stats', [PointsController::class, 'stats'])->name('points.stats');
    Route::get('/ranking', [PointsController::class, 'ranking'])->name('points.ranking');
    Route::post('/signin', [PointsController::class, 'signin'])->name('points.signin');
    Route::get('/rules', [PointsController::class, 'rules'])->name('points.rules');
    Route::get('/signin-status', [PointsController::class, 'signinStatus'])->name('points.signin-status');
    Route::get('/earn-history', [PointsController::class, 'earnHistory'])->name('points.earn-history');
});

// 积分奖励相关路由
Route::prefix('points/rewards')->group(function () {
    Route::get('/products/{productId}/preview', [PointsRewardController::class, 'productRewardPreview'])->name('points.rewards.product-preview');
    Route::post('/cart/preview', [PointsRewardController::class, 'cartRewardPreview'])->name('points.rewards.cart-preview');
    Route::get('/products', [PointsRewardController::class, 'productsWithRewards'])->name('points.rewards.products');
    Route::get('/stats', [PointsRewardController::class, 'rewardStats'])->name('points.rewards.stats');
    Route::get('/options', [PointsRewardController::class, 'rewardOptions'])->name('points.rewards.options');
}); 